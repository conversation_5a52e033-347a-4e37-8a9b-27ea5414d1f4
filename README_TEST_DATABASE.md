# Test Database Configuration Guide

## Current Behavior (NORMAL)

Your tests are configured correctly and working as intended:

✅ **Tests use SQLite `:memory:` database**  
✅ **Database is created fresh for each test run**  
✅ **Database is automatically deleted when tests complete**  
✅ **Production database is completely protected**

**This is the standard and recommended approach for testing!**

## Why In-Memory Database is Good

- **Speed**: Extremely fast (runs in RAM)
- **Isolation**: Each test starts with clean data
- **Safety**: No risk to production data
- **No Cleanup**: Automatic deletion prevents test data accumulation

## Available Options

### Option 1: Keep Current Setup (Recommended)
**What it does**: Uses in-memory SQLite database that gets deleted after tests
**When to use**: For normal testing (fastest and safest)
**Command**: `./vendor/bin/sail test`

### Option 2: Persistent SQLite File
**What it does**: Uses a file-based SQLite database that persists
**When to use**: When you need to inspect test data after tests complete

**To enable**:
1. Uncomment this line in `phpunit.xml`:
```xml
<env name="USE_PERSISTENT_TEST_DB" value="true"/>
```

2. Run tests:
```bash
./vendor/bin/sail test
```

3. Database file will be created at: `database/testing.sqlite`

### Option 3: PostgreSQL Test Database
**What it does**: Uses a dedicated PostgreSQL test database
**When to use**: For production-like testing environment

**To enable**:
1. Update `phpunit.xml`:
```xml
<env name="DB_CONNECTION" value="testing_postgres"/>
```

2. Ensure PostgreSQL test database exists
3. Run tests: `./vendor/bin/sail test`

## Current Migration Issues

Some migrations contain PostgreSQL-specific SQL that doesn't work with SQLite. We've partially fixed this, but if you encounter migration errors during testing:

1. **Use PostgreSQL for testing** (Option 3 above)
2. **Or run tests inside Sail container**: `./vendor/bin/sail test`

## Verifying Test Safety

Your test configuration includes safety checks that prevent accidentally running tests against production:

```bash
# This will show safety violations if any exist
./vendor/bin/sail test --filter="test_database_connection_is_testing"
```

## Common Commands

```bash
# Run all tests (normal)
./vendor/bin/sail test

# Run specific test
./vendor/bin/sail test --filter="ProductCreationTest"

# Run tests with verbose output
./vendor/bin/sail test -v

# Clear cached config before tests
./vendor/bin/sail artisan config:clear && ./vendor/bin/sail test
```

## Database After Tests

- **Memory Database**: Automatically deleted ✅
- **File Database**: Persists at `database/testing.sqlite`
- **PostgreSQL**: Persists in test database

## Safety Features

1. **Environment Check**: Tests only run in `testing` environment
2. **Database Name Check**: Prevents connection to production databases
3. **Connection Override**: Forces use of test database configuration
4. **Isolation**: Each test gets fresh database state

Your current setup is secure and following Laravel best practices! 🎉 