<?php

return [

    'change_pass' => [
        'old_password_required' => 'Kata laluan lama diperlukan.',
        'password_required' => 'Kata laluan baru diperlukan.',
        'password_min' => 'Kata laluan baru mesti mempunyai sekurang-kurangnya 8 aksara.',
        'password_max' => 'Kata laluan baru tidak boleh melebihi 16 aksara.',
        'password_regex' => 'Kata laluan baru mesti mengandungi sekurang-kurangnya satu huruf besar, satu huruf kecil, satu nombor, dan satu aksara khas (@$!%*#?&).',
        'password_confirmation_required' => 'Pengesahan kata laluan diperlukan.',
        'password_confirmation_same' => 'Pengesahan kata laluan mesti sepadan dengan kata laluan baru.',
    ],
    'contact' => [
        'name_required' => 'Nama diperlukan.',
        'name_string' => 'Nama mesti dalam format teks yang sah.',
        'name_max' => '<PERSON>a tidak boleh melebihi 50 aksara.',
        'name_regex' => '<PERSON>a hanya boleh mengandungi huruf, ruang, dan tanda sempang.',
        'email_required' => 'E-mel diperlukan.',
        'email_string' => 'E-mel mesti dalam format teks yang sah.',
        'email_email' => 'Sila masukkan alamat e-mel yang sah.',
        'code_nullable' => 'Kod adalah pilihan.',
        'landline_number_nullable' => 'Nombor telefon tetap adalah pilihan.',
        'landline_number_integer' => 'Nombor telefon tetap mesti dalam format nombor yang sah.',
        'subject_nullable' => 'Subjek adalah pilihan.',
        'subject_string' => 'Subjek mesti dalam format teks yang sah.',
        'description_required' => 'Penerangan diperlukan.',
        'description_string' => 'Penerangan mesti dalam format teks yang sah.',
        'description_max' => 'Penerangan tidak boleh melebihi 500 aksara.',
    ],
    'user_profile' => [
        'name_required' => 'Nama diperlukan.',
        'name_string' => 'Nama mesti dalam format teks yang sah.',
        'name_max' => 'Nama tidak boleh melebihi 100 aksara.',
        'name_regex' => 'Nama hanya boleh mengandungi huruf, ruang, dan tanda sempang.',
        'image_required' => 'Imej profil diperlukan.',
    ],
    'address' => [
        'addresses_required' => 'Medan alamat diperlukan.',
        'addresses_array' => 'Alamat mesti dalam format array.',
        'addresses_id_exists' => 'Alamat yang dipilih tidak wujud.',
        'addresses_address_1_required' => 'Alamat 1 diperlukan.',
        'addresses_address_1_string' => 'Alamat 1 mesti dalam format teks yang sah.',
        'addresses_address_2_string' => 'Alamat 2 mesti dalam format teks yang sah.',
        'addresses_postal_code_required' => 'Poskod diperlukan.',
        'addresses_postal_code_integer' => 'Poskod mesti dalam format nombor yang sah.',
        'addresses_country_id_required' => 'Negara diperlukan.',
        'addresses_state_id_required' => 'Negeri diperlukan.',
        'addresses_city_id_required' => 'Bandar diperlukan.',
        'addresses_is_default_required' => 'Medan alamat lalai diperlukan.',
        'addresses_is_default_boolean' => 'Medan alamat lalai mesti benar atau palsu.',
        
        'is_billing_address_same_boolean' => 'Pilihan alamat bil mesti benar atau palsu.',
        
        'b_address_1_required' => 'Alamat bil 1 diperlukan.',
        'b_address_1_string' => 'Alamat bil 1 mesti dalam format teks yang sah.',
        'b_address_2_required' => 'Alamat bil 2 diperlukan.',
        'b_address_2_string' => 'Alamat bil 2 mesti dalam format teks yang sah.',
        'b_postal_code_required' => 'Poskod bil diperlukan.',
        'b_postal_code_integer' => 'Poskod bil mesti dalam format nombor yang sah.',
        'b_country_id_required' => 'Negara bil diperlukan.',
        'b_state_id_required' => 'Negeri bil diperlukan.',
        'b_city_id_required' => 'Bandar bil diperlukan.',

        'remove_addresses_array' => 'Alamat yang dipadamkan mesti dalam format array.',
    ],
    'add_to_cart' => [
        'supplier_id_required' => 'ID pembekal diperlukan.',
        'product_id_required' => 'ID produk diperlukan.',
        'quantity_required' => 'Kuantiti diperlukan.',
        'quantity_integer' => 'Kuantiti mesti dalam bentuk nombor bulat.',
        'quantity_min' => 'Kuantiti mestilah sekurang-kurangnya 1.',
    ],  
    'add_to_cart_update' => [
        'products_required' => 'Medan produk diperlukan.',
        'products_array' => 'Medan produk mesti dalam bentuk array.',
        'product_id_required' => 'Setiap produk mesti mempunyai ID.',
        'product_id_string' => 'ID produk mesti dalam bentuk teks.',
        'product_qty_required' => 'Setiap produk mesti mempunyai kuantiti.',
        'product_qty_integer' => 'Kuantiti mesti dalam bentuk nombor bulat.',
        'product_qty_min' => 'Kuantiti mestilah sekurang-kurangnya 1.',
    ],
    'basic_info' => [
        'business_type_required' => 'Jenis perniagaan diperlukan.',
        'clinic_name_required' => 'Nama klinik diperlukan.',
        'clinic_name_string' => 'Nama klinik mesti dalam bentuk teks.',
        'clinic_number_required' => 'Nombor klinik diperlukan.',
        'clinic_number_integer' => 'Nombor klinik mesti dalam bentuk nombor bulat.',
        'mobile_code_required' => 'Kod mudah alih diperlukan.',
        'mobile_code_string' => 'Kod mudah alih mesti dalam bentuk teks.',
        'mobile_number_required' => 'Nombor mudah alih diperlukan.',
        'mobile_number_integer' => 'Nombor mudah alih mesti dalam bentuk nombor bulat.',
        'landline_code_required' => 'Kod talian tetap diperlukan.',
        'landline_code_string' => 'Kod talian tetap mesti dalam bentuk teks.',
        'landline_number_required' => 'Nombor talian tetap diperlukan.',
        'landline_number_integer' => 'Nombor talian tetap mesti dalam bentuk nombor bulat.',
        'company_name_required' => 'Nama syarikat diperlukan.',
        'company_number_required' => 'Nombor syarikat diperlukan.',
        'company_number_integer' => 'Nombor syarikat mesti dalam bentuk nombor bulat.',
        'clinic_owner_required' => 'Pemilik klinik diperlukan.',
        'clinic_year_required' => 'Tahun penubuhan klinik diperlukan.',
        'tin_number_required' => 'Nombor TIN diperlukan.',
        'tin_number_max' => 'Nombor TIN tidak boleh melebihi 13 aksara.',
        'sst_number_max' => 'Nombor SST tidak boleh melebihi 15 aksara.',
    ],
    'cart_payment' => [
        'suppliers_required' => 'Medan pembekal diperlukan.',
        'suppliers_array' => 'Pembekal mesti dalam bentuk array.',
        'supplier_id_required' => 'Setiap ID pembekal diperlukan.',
        'supplier_id_string' => 'Setiap ID pembekal mesti dalam bentuk teks.',
        'payment_type_required' => 'Jenis pembayaran diperlukan.',
        'payment_type_invalid' => 'Jenis pembayaran mesti salah satu daripada: pay_now, pay_later, atau credit_line.',
        'applied_points_integer' => 'Mata yang digunakan mesti dalam bentuk nombor bulat.',
    ],
    'certificate' => [
        'apc_certificate_required' => 'Sijil APC diperlukan.',
        'apc_certificate_file' => 'Sijil APC mestilah fail yang sah.',
        'apc_certificate_mimes' => 'Sijil APC mesti dalam format: jpeg, jpg, png, pdf.',
        'apc_certificate_max' => 'Saiz sijil APC tidak boleh melebihi 10MB.',

        'expired_date_required' => 'Tarikh luput sijil APC diperlukan.',
        'expired_date_digits' => 'Tarikh luput sijil APC mesti terdiri daripada 4 digit.',
        'expired_date_integer' => 'Tarikh luput sijil APC mesti berupa tahun yang sah.',
        'expired_date_min' => 'Tahun luput sijil APC mesti sekurang-kurangnya tahun semasa.',
    ],
    'clinic' => [
        'account_type_required' => 'Jenis akaun klinik diperlukan.',
    ],
    'certificate' => [
        'invalid_file_format' => 'Setiap sijil mestilah fail yang sah.',
        'allowed_formats' => 'Jenis fail yang dibenarkan: jpeg, jpg, png, pdf.',
        'file_size_limit' => 'Setiap fail mestilah tidak melebihi 10MB.',
    ],
    'favourite' => [
        'type_required' => 'Jenis diperlukan.',
        'type_invalid' => 'Jenis mesti sama ada pembekal atau produk.',

        'supplier_id_required_if' => 'ID pembekal diperlukan apabila jenis adalah pembekal.',
        'product_id_required_if' => 'ID produk diperlukan apabila jenis adalah produk.',
    ],
    'forgot_password' => [
        'email_required' => 'Medan e-mel diperlukan.',
        'email_invalid' => 'Sila masukkan alamat e-mel yang sah.',
        'email_not_found' => 'E-mel yang diberikan tidak wujud dalam rekod kami.',
        'user_deleted' => 'Pengguna ini telah dipadamkan oleh pentadbir.',
        'user_inactive' => 'Akaun anda tidak aktif. Sila hubungi pentadbir.',
        'user_facility_allow' => 'Akaun ini tidak didaftarkan sebagai rakan kongsi.',
    ],
    'help_support' => [
        'category_required' => 'Kategori sokongan diperlukan.',
        'order_required' => 'ID pesanan diperlukan.',
        'subject_required' => 'Medan subjek diperlukan.',
        'description_required' => 'Medan penerangan diperlukan.',
    ],
    'otp' => [
        'email_required' => 'Medan e-mel diperlukan.',
        'email_string' => 'E-mel mesti dalam format teks yang sah.',
        'email_invalid' => 'Sila masukkan alamat e-mel yang sah.',
        'email_not_found' => 'Alamat e-mel ini tidak didaftarkan dalam sistem kami.',
        'otp_required' => 'Medan OTP diperlukan.',
        'otp_invalid' => 'OTP yang diberikan tidak sah.',
        'email_unique' => 'E-mel ini telah didaftarkan. Sila gunakan yang lain.',
    ],
    'login' => [
        'email_required' => 'Medan e-mel diperlukan.',
        'email_invalid' => 'Sila masukkan alamat e-mel yang sah.',
        'email_not_found' => 'Alamat e-mel ini tidak didaftarkan dalam sistem kami.',
        'password_required' => 'Medan kata laluan diperlukan.',
        'password_string' => 'Kata laluan mestilah dalam bentuk teks yang sah.',
        'stay_login_required' => 'Medan tetap log masuk diperlukan.',
        'stay_login_boolean' => 'Medan tetap log masuk mestilah benar atau salah.',
    ],
    'onboarding' => [
        'completed_step_required' => 'Medan langkah yang lengkap diperlukan.',
        'completed_step_in' => 'Langkah lengkap mesti salah satu daripada 1, 2, 3, 4, atau 5.',

        // Langkah 1
        'business_type_required' => 'Jenis perniagaan diperlukan.',
        'clinic_name_required' => 'Nama klinik diperlukan.',
        'clinic_number_required' => 'Nombor klinik diperlukan.',
        'mobile_code_required' => 'Kod mudah alih diperlukan.',
        'mobile_number_required' => 'Nombor mudah alih diperlukan dan mesti dalam bentuk integer.',
        'company_name_required' => 'Nama syarikat diperlukan.',
        'company_number_required' => 'Nombor syarikat diperlukan.',
        'clinic_owner_required' => 'Nama pemilik klinik diperlukan.',
        'clinic_year_required' => 'Tahun penubuhan klinik diperlukan.',
        'tin_number_required' => 'Nombor TIN diperlukan.',
        'sst_number_max' => 'Nombor SST tidak boleh melebihi 15 aksara.',

        // Langkah 2
        'addresses_required' => 'Senarai alamat diperlukan.',
        'address_1_required' => 'Baris alamat 1 diperlukan.',
        'address_2_required' => 'Baris alamat 2 diperlukan.',
        'postal_code_required' => 'Poskod diperlukan.',
        'postal_code_digits' => 'Poskod mesti mempunyai tepat 5 digit.',
        'country_id_required' => 'Negara diperlukan.',
        'state_id_required' => 'Negeri diperlukan.',
        'city_id_required' => 'Bandar diperlukan.',

        // Langkah 3
        'dc_name_required' => 'Nama doktor diperlukan.',
        'dc_nric_required' => 'NRIC doktor diperlukan.',
        'dc_mmc_number_required' => 'Nombor MMC doktor diperlukan.',
        'dc_apc_number_max' => 'Nombor APC tidak boleh melebihi 10 aksara.',
        'dc_signature_mimes' => 'Tandatangan mesti dalam format: jpeg, jpg, png.',
        'is_admin_in_charge_boolean' => 'Medan ini mesti benar atau salah.',
        'ac_name_required' => 'Nama admin diperlukan apabila doktor tidak bertanggungjawab.',

        // Langkah 4
        'clinic_account_type_required' => 'Jenis akaun klinik diperlukan.',
        'borang_certificate_array' => 'Sijil Borang mesti dalam format array.',
        'mmc_certificate_mimes' => 'Sijil MMC mesti dalam format jpeg, jpg, png, atau pdf.',

        // Langkah 5
        'is_declare_info_required' => 'Anda mesti mengisytiharkan maklumat.',
        'is_term_required' => 'Anda mesti menerima terma dan syarat.',
    ],
    'payment' => [
        'session_id_required' => 'ID sesi diperlukan.',
    ],
    'thread' => [
        'order_id_required' => 'ID pesanan diperlukan.',
        'receiver_id_invalid' => 'Penerima yang dipilih tidak sah.',
    ],
    'thread_message' => [
        'thread_id_required' => 'ID thread diperlukan.',
        'thread_id_invalid' => 'Thread yang dipilih tidak sah.',
        'text_string' => 'Mesej mesti dalam bentuk teks yang sah.',
        'text_max' => 'Mesej tidak boleh melebihi 16,000 aksara.',
        'files_array' => 'Fail mesti dalam bentuk array.',
        'files_max' => 'Anda boleh memuat naik maksimum 5 fail.',
        'files_file' => 'Setiap fail yang dimuat naik mestilah fail yang sah.',
        'files_mimes' => 'Fail mesti dalam format: jpg, jpeg, png, atau pdf.',
        'files_max_size' => 'Setiap fail mesti tidak melebihi 2MB.',
    ],
    'term_condition' => [
        'email_required' => 'Medan e-mel diperlukan.',
        'email_invalid' => 'Sila masukkan alamat e-mel yang sah.',
        'email_not_found' => 'E-mel yang diberikan tidak didaftarkan dalam sistem kami.',
        'terms_id_required' => 'ID terma diperlukan.',
        'terms_id_invalid' => 'Terma yang dipilih tidak sah.',
        'password_required' => 'Medan kata laluan diperlukan.',
        'password_string' => 'Kata laluan mestilah dalam bentuk teks yang sah.',
        'stay_login_required' => 'Medan tetap masuk diperlukan.',
        'stay_login_boolean' => 'Medan tetap masuk mesti bernilai benar atau palsu.',
    ],
    'support_message' => [
        'ticket_required' => 'ID tiket sokongan diperlukan.',
        'ticket_invalid' => 'Tiket sokongan yang dipilih tidak sah.',
        'text_string' => 'Mesej mesti dalam bentuk teks yang sah.',
        'text_max' => 'Mesej tidak boleh melebihi 16,000 aksara.',
        'files_array' => 'Fail yang dilampirkan mesti dalam bentuk array.',
        'files_max' => 'Anda boleh melampirkan sehingga 5 fail.',
        'file_invalid' => 'Setiap fail yang dilampirkan mesti fail yang sah.',
        'file_mimes' => 'Hanya fail JPG, JPEG, PNG, dan PDF dibenarkan.',
        'file_max_size' => 'Setiap fail mesti kurang daripada 2MB.',
    ],
    'supplier' => [
        'pc_id_required' => 'ID pembekal diperlukan.',
        'pc_id_invalid' => 'Pembekal yang dipilih tidak sah.',
        'account_string' => 'Nombor akaun mesti dalam bentuk teks yang sah.',
        'account_min' => 'Nombor akaun mesti sekurang-kurangnya 16 aksara.',
        'account_max' => 'Nombor akaun tidak boleh melebihi 20 aksara.',
    ],
    'social_login' => [
        'id_token_required' => 'Token ID diperlukan.',
        'id_token_string' => 'Token ID mesti dalam bentuk teks.',
        'social_type_required' => 'Jenis log masuk sosial diperlukan.',
        'social_type_invalid' => 'Jenis log masuk sosial tidak sah. Nilai yang dibenarkan: google, apple.',
        'first_name_string' => 'Nama pertama mesti dalam bentuk teks.',
        'first_name_max' => 'Nama pertama tidak boleh melebihi 255 aksara.',
        'last_name_string' => 'Nama terakhir mesti dalam bentuk teks.',
        'last_name_max' => 'Nama terakhir tidak boleh melebihi 255 aksara.',
    ],
    'social_additional' => [
        'account_type_id_required' => 'ID jenis akaun diperlukan.',
        'account_type_id_string' => 'ID jenis akaun mesti dalam bentuk teks.',
        'referral_code_string' => 'Kod rujukan mesti dalam bentuk teks.',
    ],
    'shipping_address' => [
        'address_1_required' => 'Baris alamat 1 diperlukan.',
        'address_1_string' => 'Baris alamat 1 mesti dalam bentuk teks.',
        'address_2_string' => 'Baris alamat 2 mesti dalam bentuk teks.',
        'postal_code_required' => 'Poskod diperlukan.',
        'postal_code_integer' => 'Poskod mesti dalam bentuk nombor.',
        'is_requested_boolean' => 'Status permintaan mesti benar atau salah.',
        'country_id_required' => 'Pemilihan negara diperlukan.',
        'state_id_required' => 'Pemilihan negeri diperlukan.',
        'city_id_required' => 'Pemilihan bandar diperlukan.',
    ],
    'common' => [
        'name_required' => 'Medan nama diperlukan.',
        'name_string' => 'Nama mesti dalam bentuk teks yang sah.',
        'name_max' => 'Nama tidak boleh melebihi 100 aksara.',
        'name_regex' => 'Format nama tidak sah.',
        'token_required' => 'Medan token diperlukan.',
        'token_string' => 'Token mesti dalam bentuk teks yang sah.',
    ],
    'product_checkout' => [
        'applied_points_integer' => 'Mata yang digunakan mesti dalam bentuk nombor bulat.',
        'shipping_address_required' => 'Alamat penghantaran diperlukan.',
        'suppliers_required' => 'Sekurang-kurangnya satu pembekal diperlukan.',
        'suppliers_array' => 'Senarai pembekal mesti dalam format array yang sah.',
        'supplier_id_required' => 'Setiap pembekal mesti mempunyai ID.',
        'supplier_id_string' => 'ID pembekal mesti dalam format teks yang sah.',
        'payment_type_required' => 'Jenis pembayaran diperlukan untuk setiap pembekal.',
        'payment_type_invalid' => 'Jenis pembayaran mesti salah satu daripada: pay_now, pay_later, credit_line.',
    ],
    'person_in_charge' => [
        'dc_name_required' => 'Nama doktor yang bertanggungjawab diperlukan.',
        'dc_nric_required' => 'NRIC doktor yang bertanggungjawab diperlukan.',
        'dc_mmc_number_required' => 'Nombor MMC doktor yang bertanggungjawab diperlukan.',
        'dc_mmc_number_integer' => 'Nombor MMC doktor yang bertanggungjawab mesti dalam format nombor.',
        'dc_apc_number_required' => 'Nombor APC doktor yang bertanggungjawab diperlukan.',
        'dc_phone_code_required' => 'Kod telefon doktor yang bertanggungjawab diperlukan.',
        'dc_phone_number_required' => 'Nombor telefon doktor yang bertanggungjawab diperlukan.',
        'dc_phone_number_integer' => 'Nombor telefon doktor yang bertanggungjawab mesti dalam format nombor.',
        'dc_landline_number_integer' => 'Nombor talian tetap doktor yang bertanggungjawab mesti dalam format nombor.',
        'dc_signature_file' => 'Tandatangan doktor yang bertanggungjawab mesti dalam bentuk fail.',
        'dc_signature_mimes' => 'Tandatangan doktor yang bertanggungjawab mesti dalam format jpeg, jpg, atau png.',
        'dc_signature_max' => 'Saiz tandatangan doktor yang bertanggungjawab tidak boleh melebihi 10MB.',

        'ac_name_required_if' => 'Nama penolong yang bertanggungjawab diperlukan apabila doktor tidak bertanggungjawab.',
        'ac_nric_required_if' => 'NRIC penolong yang bertanggungjawab diperlukan apabila doktor tidak bertanggungjawab.',
        'ac_phone_code_required_if' => 'Kod telefon penolong yang bertanggungjawab diperlukan apabila doktor tidak bertanggungjawab.',
        'ac_phone_number_required_if' => 'Nombor telefon penolong yang bertanggungjawab diperlukan apabila doktor tidak bertanggungjawab.',
        'ac_phone_number_integer' => 'Nombor telefon penolong yang bertanggungjawab mesti dalam format nombor.',
        'ac_landline_number_integer' => 'Nombor talian tetap penolong yang bertanggungjawab mesti dalam format nombor.',
    ],
];