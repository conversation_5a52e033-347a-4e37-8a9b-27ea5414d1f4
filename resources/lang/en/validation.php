<?php

return [

    'change_pass' => [
        'old_password_required' => 'The old password is required.',
        'password_required' => 'The new password is required.',
        'password_min' => 'The new password must be at least 8 characters.',
        'password_max' => 'The new password must not exceed 16 characters.',
        'password_regex' => 'The new password must include at least one uppercase letter, one lowercase letter, one number, and one special character (@$!%*#?&).',
        'password_confirmation_required' => 'The password confirmation is required.',
        'password_confirmation_same' => 'The password confirmation must match the new password.',
    ],
    'contact' => [
        'name_required' => 'The name is required.',
        'name_string' => 'The name must be a valid string.',
        'name_max' => 'The name must not exceed 50 characters.',
        'name_regex' => 'The name may only contain letters, spaces, and hyphens.',
        'email_required' => 'The email is required.',
        'email_string' => 'The email must be a valid string.',
        'email_email' => 'Please enter a valid email address.',
        'code_nullable' => 'The code is optional.',
        'landline_number_nullable' => 'The landline number is optional.',
        'landline_number_integer' => 'The landline number must be a valid integer.',
        'subject_nullable' => 'The subject is optional.',
        'subject_string' => 'The subject must be a valid string.',
        'description_required' => 'The description is required.',
        'description_string' => 'The description must be a valid string.',
        'description_max' => 'The description must not exceed 500 characters.',
    ],
    'user_profile' => [
        'name_required' => 'The name is required.',
        'name_string' => 'The name must be a valid string.',
        'name_max' => 'The name must not exceed 100 characters.',
        'name_regex' => 'The name may only contain letters, spaces, and hyphens.',
        'image_required' => 'Profile image is required.',
    ],
    'address' => [
        'addresses_required' => 'The addresses field is required.',
        'addresses_array' => 'The addresses must be an array.',
        'addresses_id_exists' => 'The selected address does not exist.',
        'addresses_address_1_required' => 'Address 1 is required.',
        'addresses_address_1_string' => 'Address 1 must be a valid string.',
        'addresses_address_2_string' => 'Address 2 must be a valid string.',
        'addresses_postal_code_required' => 'The postal code is required.',
        'addresses_postal_code_integer' => 'The postal code must be a valid number.',
        'addresses_country_id_required' => 'The country is required.',
        'addresses_state_id_required' => 'The state is required.',
        'addresses_city_id_required' => 'The city is required.',
        'addresses_is_default_required' => 'The default address field is required.',
        'addresses_is_default_boolean' => 'The default address field must be true or false.',
        'is_billing_address_same_boolean' => 'The billing address flag must be true or false.',
        'b_address_1_required' => 'The billing address 1 is required.',
        'b_address_1_string' => 'The billing address 1 must be a valid string.',
        'b_address_2_required' => 'The billing address 2 is required.',
        'b_address_2_string' => 'The billing address 2 must be a valid string.',
        'b_postal_code_required' => 'The billing postal code is required.',
        'b_postal_code_integer' => 'The billing postal code must be a valid number.',
        'b_country_id_required' => 'The billing country is required.',
        'b_state_id_required' => 'The billing state is required.',
        'b_city_id_required' => 'The billing city is required.',
        'remove_addresses_array' => 'The removed addresses must be an array.',
    ],
    'add_to_cart' => [
        'supplier_id_required' => 'The supplier ID is required.',
        'product_id_required' => 'The product ID is required.',
        'quantity_required' => 'The quantity is required.',
        'quantity_integer' => 'The quantity must be an integer.',
        'quantity_min' => 'The quantity must be at least 1.',
    ],
    'add_to_cart_update' => [
        'products_required' => 'The products field is required.',
        'products_array' => 'The products field must be an array.',
        'product_id_required' => 'Each product must have an ID.',
        'product_id_string' => 'Each product ID must be a string.',
        'product_qty_required' => 'Each product must have a quantity.',
        'product_qty_integer' => 'The quantity must be an integer.',
        'product_qty_min' => 'The quantity must be at least 1.',
    ],
    'basic_info' => [
        'business_type_required' => 'The business type is required.',
        'clinic_name_required' => 'The facility name is required.',
        'clinic_name_string' => 'The facility name must be a string.',
        'clinic_number_required' => 'The facility number is required.',
        'clinic_number_integer' => 'The facility number must be a valid integer.',
        'mobile_code_required' => 'The mobile code is required.',
        'mobile_code_string' => 'The mobile code must be a string.',
        'mobile_number_required' => 'The mobile number is required.',
        'mobile_number_integer' => 'The mobile number must be a valid integer.',
        'landline_code_required' => 'The landline code is required.',
        'landline_code_string' => 'The landline code must be a string.',
        'landline_number_required' => 'The landline number is required.',
        'landline_number_integer' => 'The landline number must be a valid integer.',
        'company_name_required' => 'The company name is required.',
        'company_number_required' => 'The company number is required.',
        'company_number_integer' => 'The company number must be a valid integer.',
        'clinic_owner_required' => 'The facility owner is required.',
        'clinic_year_required' => 'The facility establishment year is required.',
        'tin_number_required' => 'The TIN number is required.',
        'tin_number_max' => 'The TIN number cannot exceed 13 characters.',
        'sst_number_max' => 'The SST number cannot exceed 15 characters.',
    ],
    'cart_payment' => [
        'suppliers_required' => 'The suppliers field is required.',
        'suppliers_array' => 'The suppliers must be an array.',
        'supplier_id_required' => 'Each supplier ID is required.',
        'supplier_id_string' => 'Each supplier ID must be a valid string.',
        'payment_type_required' => 'Payment type is required.',
        'payment_type_invalid' => 'The payment type must be one of: pay_now, pay_later, or credit_line.',
        'applied_points_integer' => 'Applied points must be a valid integer.',
    ],
    'certificate' => [
        'apc_certificate_required' => 'The APC certificate is required.',
        'apc_certificate_file' => 'The APC certificate must be a valid file.',
        'apc_certificate_mimes' => 'The APC certificate must be a file of type: jpeg, jpg, png, pdf.',
        'apc_certificate_max' => 'The APC certificate must not exceed 10MB.',

        'expired_date_required' => 'The APC certificate expiration date is required.',
        'expired_date_digits' => 'The APC certificate expiration date must be a 4-digit year.',
        'expired_date_integer' => 'The APC certificate expiration date must be a valid year.',
        'expired_date_min' => 'The APC certificate expiration year must be at least the current year.',
    ],
    'clinic' => [
        'account_type_required' => 'The facility account type is required.',
    ],
    'certificate' => [
        'invalid_file_format' => 'Each certificate must be a valid file.',
        'allowed_formats' => 'Allowed file types: jpeg, jpg, png, pdf.',
        'file_size_limit' => 'Each file must not exceed 10MB.',
    ],
    'favourite' => [
        'type_required' => 'The type field is required.',
        'type_invalid' => 'The type must be either supplier or product.',

        'supplier_id_required_if' => 'The supplier ID is required when the type is supplier.',
        'product_id_required_if' => 'The product ID is required when the type is product.',
    ],
    'forgot_password' => [
        'email_required' => 'The email field is required.',
        'email_invalid' => 'Please enter a valid email address.',
        'email_not_found' => 'The provided email does not exist in our records.',
        'user_deleted' => 'This user has been deleted by the administrator.',
        'user_inactive' => 'Your account is inactive. Please contact administrator.',
        'user_facility_allow' => 'This account is not registered as a partner.',
    ],
    'help_support' => [
        'category_required' => 'The support category is required.',
        'order_required' => 'The order ID is required.',
        'subject_required' => 'The subject field is required.',
        'description_required' => 'The description field is required.',
    ],
    'otp' => [
        'email_required' => 'The email field is required.',
        'email_invalid' => 'Please enter a valid email address.',
        'email_string' => 'The email must be a valid string.',
        'email_not_found' => 'This email address is not registered in our system.',
        'otp_required' => 'The OTP field is required.',
        'otp_invalid' => 'The OTP provided is invalid.',
        'email_unique' => 'This email is already registered. Please use a different one.',
        'facility_name_unique' => 'This facility name is already registered. Please use a different one.',
    ],
    'login' => [
        'email_required' => 'The email field is required.',
        'email_invalid' => 'Please enter a valid email address.',
        'email_not_found' => 'This email address is not registered in our system.',
        'password_required' => 'The password field is required.',
        'password_string' => 'The password must be a valid string.',
        'stay_login_required' => 'The stay login field is required.',
        'stay_login_boolean' => 'The stay login field must be true or false.',
    ],
    'onboarding' => [
        'completed_step_required' => 'The completed step field is required.',
        'completed_step_in' => 'The completed step must be one of 1, 2, 3, 4, or 5.',

        // Step 1
        'business_type_required' => 'The business type is required.',
        'clinic_name_required' => 'The facility name is required.',
        'clinic_number_required' => 'The facility number is required.',
        'mobile_code_required' => 'The mobile code is required.',
        'mobile_number_required' => 'The mobile number is required and must be an integer.',
        'company_name_required' => 'The company name is required.',
        'company_number_required' => 'The company number is required.',
        'clinic_owner_required' => 'The facility owner name is required.',
        'clinic_year_required' => 'The facility establishment year is required.',
        'tin_number_required' => 'The TIN number is required.',
        'sst_number_max' => 'The SST number may not be greater than 15 characters.',

        // Step 2
        'addresses_required' => 'The address list is required.',
        'address_1_required' => 'Address line 1 is required.',
        'address_2_required' => 'Address line 2 is required.',
        'postal_code_required' => 'The postal code is required.',
        'postal_code_digits' => 'The postal code must be exactly 5 digits.',
        'country_id_required' => 'The country is required.',
        'state_id_required' => 'The state is required.',
        'city_id_required' => 'The city is required.',
        'nick_name_required' => 'The nickname is required.',

        // Step 3
        'dc_name_required' => 'Doctor’s name is required.',
        'dc_nric_required' => 'Doctor’s NRIC is required.',
        'dc_mmc_number_required' => 'Doctor’s MMC number is required.',
        'dc_apc_number_max' => 'The APC number may not be greater than 10 characters.',
        'dc_signature_mimes' => 'The signature must be a file of type: jpeg, jpg, png.',
        'is_admin_in_charge_boolean' => 'The is admin in charge field must be true or false.',
        'ac_name_required' => 'Admin name is required when the doctor is not in charge.',

        // Step 4
        'clinic_account_type_required' => 'The facility account type is required.',
        'borang_certificate_array' => 'Borang certificate must be an array.',
        'mmc_certificate_mimes' => 'The MMC certificate must be a jpeg, jpg, png, or pdf file.',

        // Step 5
        'is_declare_info_required' => 'You must declare the information.',
        'is_term_required' => 'You must accept the terms and conditions.',
    ],
    'payment' => [
        'session_id_required' => 'The session ID is required.',
    ],
    'thread' => [
        'order_id_required' => 'The order ID is required.',
        'receiver_id_invalid' => 'The selected receiver is invalid.',
    ],
    'thread_message' => [
        'thread_id_required' => 'The thread ID is required.',
        'thread_id_invalid' => 'The selected thread is invalid.',
        'text_string' => 'The message must be a valid text.',
        'text_max' => 'The message must not exceed 16,000 characters.',
        'files_array' => 'The files must be an array.',
        'files_max' => 'You can upload a maximum of 5 files.',
        'files_file' => 'Each uploaded file must be a valid file.',
        'files_mimes' => 'Files must be of type: jpg, jpeg, png, or pdf.',
        'files_max_size' => 'Each file must not exceed 2MB in size.',
    ],
    'term_condition' => [
        'email_required' => 'The email field is required.',
        'email_invalid' => 'Please provide a valid email address.',
        'email_not_found' => 'The provided email is not registered in our system.',
        'terms_id_required' => 'The terms ID is required.',
        'terms_id_invalid' => 'The selected terms are invalid.',
        'password_required' => 'The password field is required.',
        'password_string' => 'The password must be a valid string.',
        'stay_login_required' => 'The stay login field is required.',
        'stay_login_boolean' => 'The stay login field must be true or false.',
    ],
    'support_message' => [
        'ticket_required' => 'The support ticket ID is required.',
        'ticket_invalid' => 'The selected support ticket is invalid.',
        'text_string' => 'The message must be a valid text string.',
        'text_max' => 'The message cannot exceed 16,000 characters.',
        'files_array' => 'The attached files must be an array.',
        'files_max' => 'You can attach up to 5 files.',
        'file_invalid' => 'Each attached file must be a valid file.',
        'file_mimes' => 'Only JPG, JPEG, PNG, and PDF files are allowed.',
        'file_max_size' => 'Each file must be smaller than 2MB.',
    ],
    'supplier' => [
        'pc_id_required' => 'The supplier ID is required.',
        'pc_id_invalid' => 'The selected supplier is invalid.',
        'account_string' => 'The account number must be a valid string.',
        'account_min' => 'The account number must be at least 10 characters.',
        'account_max' => 'The account number cannot exceed 15 characters.',
    ],
    'social_login' => [
        'id_token_required' => 'The ID token is required.',
        'id_token_string' => 'The ID token must be a valid string.',
        'social_type_required' => 'The social login type is required.',
        'social_type_invalid' => 'Invalid social login type. Allowed values: google, apple.',
        'first_name_string' => 'The first name must be a string.',
        'first_name_max' => 'The first name may not exceed 255 characters.',
        'last_name_string' => 'The last name must be a string.',
        'last_name_max' => 'The last name may not exceed 255 characters.',
    ],
    'social_additional' => [
        'account_type_id_required' => 'The account type ID is required.',
        'account_type_id_string' => 'The account type ID must be a string.',
        'referral_code_string' => 'The referral code must be a string.',
        'code_not_found' => 'The referral code is invalid.',
    ],
    'shipping_address' => [
        'address_1_required' => 'Address Line 1 is required.',
        'address_1_string' => 'Address Line 1 must be a string.',
        'address_2_string' => 'Address Line 2 must be a string.',
        'postal_code_required' => 'Postal code is required.',
        'postal_code_integer' => 'Postal code must be an integer.',
        'is_requested_boolean' => 'The request status must be true or false.',
        'country_id_required' => 'Country selection is required.',
        'state_id_required' => 'State selection is required.',
        'city_id_required' => 'City selection is required.',
    ],
    'common' => [
        'name_required' => 'The name field is required.',
        'name_string' => 'The name must be a valid string.',
        'name_max' => 'The name may not be greater than 100 characters.',
        'name_regex' => 'The name format is invalid.',
        'token_required' => 'The token field is required.',
        'token_string' => 'The token must be a valid string.',
    ],
    'product_checkout' => [
        'applied_points_integer' => 'Applied points must be an integer.',
        'shipping_address_required' => 'The shipping address is required.',
        'suppliers_required' => 'At least one supplier is required.',
        'suppliers_array' => 'Suppliers must be a valid array.',
        'supplier_id_required' => 'Each supplier must have an ID.',
        'supplier_id_string' => 'The supplier ID must be a valid string.',
        'payment_type_required' => 'Payment type is required for each supplier.',
        'payment_type_invalid' => 'Payment type must be one of: pay_now, pay_later, credit_line.',
    ],
    'person_in_charge' => [
        'dc_name_required' => 'Doctor in charge name is required.',
        'dc_nric_required' => 'Doctor in charge NRIC is required.',
        'dc_mmc_number_required' => 'Doctor in charge MMC number is required.',
        'dc_mmc_number_integer' => 'Doctor in charge MMC number must be an integer.',
        'dc_apc_number_required' => 'Doctor in charge APC number is required.',
        'dc_phone_code_required' => 'Doctor in charge phone code is required.',
        'dc_phone_number_required' => 'Doctor in charge phone number is required.',
        'dc_phone_number_integer' => 'Doctor in charge phone number must be a valid number.',
        'dc_landline_number_integer' => 'Doctor in charge landline number must be a valid number.',
        'dc_signature_file' => 'Doctor in charge signature must be a file.',
        'dc_signature_mimes' => 'Doctor in charge signature must be a jpeg, jpg, or png file.',
        'dc_signature_max' => 'Doctor in charge signature size cannot exceed 10MB.',

        'ac_name_required_if' => 'Assistant in charge name is required when the doctor is not in charge.',
        'ac_nric_required_if' => 'Assistant in charge NRIC is required when the doctor is not in charge.',
        'ac_phone_code_required_if' => 'Assistant in charge phone code is required when the doctor is not in charge.',
        'ac_phone_number_required_if' => 'Assistant in charge phone number is required when the doctor is not in charge.',
        'ac_phone_number_integer' => 'Assistant in charge phone number must be a valid number.',
        'ac_landline_number_integer' => 'Assistant in charge landline number must be a valid number.',
    ],
];