<?php

return [
    'generic_name' => [
        'required' => 'The generic name field is required.',
        'max' => 'The generic name must not exceed 100 characters.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'case_sensitive_unique' => "This generic name has already been taken.",
        'delete_warning' => 'This generic name is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Generic name has been deleted successfully.',
        'status_warning' => 'This generic name is used in a product and cannot change status. Please remove the product first.',
        'bulk_delete_success' => 'Successfully deleted :count generic name(s).',
        'bulk_delete_failed' => 'The following generic names could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected generic names have been activated successfully.',
        'bulk_inactivate_success' => ':count generic names have been inactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following generic name(s) as they are used in products: :names',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Generic name has been updated successfully.',
        'create_success' => 'Generic name has been created successfully.',


        'title' => [
            'updated' => 'Generic Name Updated',
            'created' => 'Generic Name Created',
            'warning' => 'Warning',
            'deleted' => 'Generic Name Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Generic Names Activated',
            'deactivated' => 'Generic Names Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'category' => [

        'title' => [
            'updated' => 'Category Updated',
            'created' => 'Category Created',
            'warning' => 'Warning',
            'deleted' => 'Category Deleted',
            'deletion_completed' => 'Deletion Completed',
            'subcategories_found' => 'Subcategories Found',
            'products_found' => 'Products Found',
            'activated' => 'Categories Activated',
            'deactivated' => 'Categories Deactivated',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],

        'update_success' => 'Category has been updated successfully.',
        'create_success' => 'Category has been created successfully.',
        'required' => 'The category name field is required.',
        'order_required' => 'The order number is required.',
        'max' => 'The category name must not exceed 100 characters.',
        'min_order' => 'Order must be one or a positive number.',
        'case_sensitive_unique' => 'This category name has already been taken.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'status_updated' => 'Status has been updated successfully.',
        'status_warning' => 'This category is used in a product and cannot change status. Please remove the product first.',
        'delete_subcategory_warning' => 'This category has subcategories and cannot be deleted. Please remove the subcategories first.',
        'delete_product_warning' => 'This category is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Category has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count category(s).',
        'bulk_delete_failed_subcategories' => 'The following categories have subcategories and cannot be deleted: :names',
        'bulk_delete_failed_products' => 'The following categories are linked to products and cannot be deleted: :names',
        'bulk_activate_success' => 'Selected categories have been activated successfully.',
        'bulk_inactivate_success' => ':count category(s) have been inactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following category(s) as they are used in products: :names',
    ],
    'sub-category' => [
        'parent_required' => 'The parent category field is required.',
        'required' => 'The sub category name field is required.',
        'max' => 'The sub category name must not exceed 100 characters.',
        'case_sensitive_unique' => 'This sub category has already been taken.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'order_required' => 'The order number is required.',


        'title' => [
            'updated' => 'Sub Category Updated',
            'created' => 'Sub Category Created',
            'warning' => 'Warning',
            'deleted' => 'Sub Category Deleted',
            'deletion_completed' => 'Deletion Completed',
            'subcategories_found' => 'Sub Categories Found',
            'partial_deleted' => 'Some Items Not Deleted',
            'products_found' => 'Products Found',
            'activated' => 'Sub Categories Activated',
            'deactivated' => 'Sub Categories Deactivated',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
        'min_order' => 'Order must be one or a positive number.',
        'update_success' => 'Sub category has been updated successfully.',
        'create_success' => 'Sub category has been created successfully.',
        'status_updated' => 'Status has been updated successfully.',
        'delete_warning' => 'This sub category is used in a product and cannot be deleted. Please remove the product first.',
        'deleted' => 'Sub Category Deleted',
        'delete_success' => 'Sub category has been deleted successfully.',
        'bulk_deleted' => 'Deletion Completed',
        'bulk_delete_success' => 'Successfully deleted :count sub category(s).',
        'bulk_delete_failed' => 'The following sub categories could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected sub categories have been activated successfully.',
        'bulk_inactivate_success' => ':count sub category(s) have been inactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following sub category(s) as they are used in products: :names',
    ],

    'dosage_foam' => [
        'required' => 'The dosage form name field is required.',
        'max' => 'The dosage form name must not exceed 100 characters.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'case_sensitive_unique' => "This dosage form name has already been taken.",
        'delete_warning' => 'This dosage form is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Dosage form has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count dosage form(s).',
        'bulk_delete_failed' => 'The following dosage forms could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected dosage forms have been activated successfully.',
        'bulk_inactivate_success' => 'Selected dosage forms have been deactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following dosage forms as they are used in products: :names',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Dosage form has beenupdated successfully.',
        'create_success' => 'Dosage form has been created successfully.',


        'title' => [
            'updated' => 'Dosage Form Updated',
            'created' => 'Dosage Form Created',
            'warning' => 'Warning',
            'deleted' => 'Dosage Form Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Dosage Forms Activated',
            'deactivated' => 'Dosage Forms Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'brand' => [
        'required' => 'The brand name field is required.',
        'max' => 'The brand name must not exceed 100 characters.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'case_sensitive_unique' => "This brand name has already been taken.",
        'delete_warning' => 'This brand is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Brand has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count brand(s).',
        'bulk_delete_failed' => 'The following brands could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected brands have been activated successfully.',
        'bulk_inactivate_success' => 'Selected brands have been deactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following brands as they are used in products: :names',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Brand has been updated successfully.',
        'create_success' => 'Brand has been created successfully.',


        'title' => [
            'updated' => 'Brand Updated',
            'created' => 'Brand Created',
            'warning' => 'Warning',
            'deleted' => 'Brand Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Brands Activated',
            'deactivated' => 'Brands Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'unit' => [
        'name_required' => 'The volume unit name field is required.',
        'name_max' => 'The volume unit name must not exceed 100 characters.',
        'name_regex' => "Emojis, special symbols, and non-English characters are not allowed in volume unit name.",
        'name_case_sensitive_unique' => "This volume unit name has already been taken.",
        'short_form_required' => 'The volume unit short form field is required.',
        'short_form_max' => 'The volume unit short form must not exceed 20 characters.',
        'short_form_case_sensitive_unique' => "This volume unit short form has already been taken.",
        'delete_warning' => 'This volume unit is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'volume unit has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count volume unit(s).',
        'bulk_delete_failed' => 'The following volume units could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected volume units have been activated successfully.',
        'bulk_inactivate_success' => 'Selected volume units have been deactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following volume units as they are used in products: :names',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Volume unit has been updated successfully.',
        'create_success' => 'Volume unit has been created successfully.',


        'title' => [
            'updated' => 'Volume Unit Updated',
            'created' => 'Volume Unit Created',
            'warning' => 'Warning',
            'deleted' => 'Volume Unit Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Volume Units Activated',
            'deactivated' => 'Volume Units Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'commission' => [
        'required' => 'The commission field is required.',
        'numeric' => 'The commission must be a number.',
        'min' => 'The commission must be at least :min.',
        'max' => 'The commission must not exceed :max.',
        'commission_flat_max' => 'The commission must not exceed :max characters.',
        'percentage_placeholder' => 'Enter percentage value',
        'flat_value_placeholder' => 'Enter flat value',
        'content' => 'Note: The minimum commission percentage indicates that the setup commission percentage for any product, pharmaceutical company, or global setting won\'t be less than the specified percentage.',
        'commission_flat_required' => 'The commission flat value is required.',
        'commission_percentage_required' => 'The commission percentage is required.',
        'commission_flat_numeric' => 'The commission flat value must be a number.',
        'commission_flat_min' => 'The commission flat value must be at least 1.',
        'commission_flat_max' => 'The commission flat value must not exceed 99999.',
        'commission_percentage_max' => 'The percentage value must not exceed 100.',
        'commission_updated' => 'Commission settings have been updated successfully.',
    ],

    'banner' => [
        'banner_type_required' => 'The banner type field is required.',
        'sequence_required' => 'The sequence field is required.',
        'redirect_to_required' => 'The redirect to field is required.',
        'redirect_to_id_required' => 'The redirect on field is required.',
        'start_date_required' => 'Start date is required',
        'start_date_before' => 'Start date must be before end date',
        'start_date_expired' => 'The date has expired. Please choose a new date starting from today.',
        'end_date_required' => 'End date is required',
        'end_date_after' => 'End date must be after start date',
        'end_date_expired' => 'The date has expired. Please choose a new date starting from today.',
        'pop_up_status_validation' => 'Only one pop-up banner status can be active. Please deactivate another one first.',
        'status_updated' => 'Status has been updated successfully.',
        'delete_success' => 'Banner has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count banner(s).',
        'bulk_activate_success' => 'Selected banners have been activated successfully.',
        'bulk_inactivate_success' => 'Selected banners have been deactivated successfully.',
        'update_success' => 'Banner has been updated successfully.',
        'create_success' => 'Banner has been created successfully.',
        'pop_up_status_validation' => 'Only one pop-up banner can be active at a time.',
        'pop_up_status_validation_date' => 'Only one popup banner can be active for a given date range. Please ensure there are no overlapping date ranges.',
        'pop_up_overlap_selected' => 'Cannot activate multiple popup banners with overlapping date ranges. Please select popup banners with distinct date ranges.',


        'title' => [
            'updated' => 'Banner Updated',
            'created' => 'Banner Created',
            'warning' => 'Warning',
            'deleted' => 'Banner Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Banners Activated',
            'deactivated' => 'Banners Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'container' => [
        'required' => 'The package name field is required.',
        'max' => 'The package name must not exceed 100 characters.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'case_sensitive_unique' => "This package name has already been taken.",
        'delete_warning' => 'This package is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Package has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count package(s).',
        'bulk_delete_failed' => 'The following packages could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected packages activated successfully.',
        'bulk_inactivate_success' => 'Selected packages deactivated successfully.',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Package has been updated successfully.',
        'create_success' => 'Package has been created successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following packages as they are used in products: :names',


        'title' => [
            'updated' => 'Package Updated',
            'created' => 'Package Created',
            'warning' => 'Warning',
            'deleted' => 'Package Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Packages Activated',
            'deactivated' => 'Packages Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'distributor' => [
        'required' => 'The distributor name field is required.',
        'max' => 'The distributor name must not exceed 100 characters.',
        'regex' => "Emojis, special symbols, and non-English characters are not allowed.",
        'case_sensitive_unique' => "This distributor name has already been taken.",
        'delete_warning' => 'This distributor is used in a product and cannot be deleted. Please remove the product first.',
        'delete_success' => 'Distributor has been deleted successfully.',
        'bulk_delete_success' => 'Successfully deleted :count distributor(s).',
        'bulk_delete_failed' => 'The following distributors could not be deleted because they are linked to products: :names',
        'bulk_activate_success' => 'Selected distributors have been activated successfully.',
        'bulk_inactivate_success' => 'Selected distributors  have been deactivated successfully.',
        'bulk_inactivate_failed' => 'Could not inactivate the following distributors as they are used in products: :names',
        'status_updated' => 'Status has been updated successfully.',
        'update_success' => 'Distributor has been updated successfully.',
        'create_success' => 'Distributor has been created successfully.',


        'title' => [
            'updated' => 'Distributor Updated',
            'created' => 'Distributor Created',
            'warning' => 'Warning',
            'deleted' => 'Distributor Deleted',
            'partial_deleted' => 'Some Items Not Deleted',
            'activated' => 'Distributors Activated',
            'deactivated' => 'Distributors Deactivated',
            'deletion_completed' => 'Deletion Completed',
            'partial_inactivated' => 'Some Items Were Not Inactivated',
            'saved' => 'Status Changed',
        ],
    ],

    'support_ticket_assigned' => [
        'breadcrumb_title' => 'All Conversation',
        'empty_message_error' => 'You cannot send an empty message. Please add some text or attach a file.',
        'file_upload_failed_title' => 'File Upload Failed',
        'file_upload_failed_body' => 'One or more files could not be uploaded.',
        'file_upload_limit_error_title' => 'Too many files',
        'file_upload_limit_error' => 'You can only upload up to 5 files at a time.',
        'file_type_limit_error' => 'Only images (JPG, PNG, GIF) and PDFs up to 2MB each are allowed.',
        'invalid_file_error' => 'Invalid file(s)',
        'breadcrumb_support' => 'Support',
        'breadcrumb_dpharma_support' => 'Dpharma Support',
        'breadcrumb_ticket_details' => 'Ticket Details',
        'breadcrumb_add_ticket' => 'Add Ticket',
        'breadcrumb_list' => 'List',
    ],

    'support_ticket_received' => [
        'closed_ticket_body' => 'This ticket is already closed',
        'closed_ticket_body' => 'This ticket has already been closed.',
        'closed_send_error_title' => 'Cannot send message',
        'closed_send_error_body' => 'This ticket is closed. No further messages or files can be sent.',
        'empty_message_error' => 'You cannot send an empty message. Please add some text or attach a file.',
        'invalid_file_error' => 'Your message was not sent because the attached files are invalid.',
        'file_upload_failed_title' => 'File Upload Failed',
        'file_upload_failed_body' => 'One or more files could not be uploaded.',
        'closed_file_attach_title' => 'Cannot attach files',
        'closed_file_attach_body' => 'This ticket is closed. No files can be attached.',
        'closed_typing_title' => 'Cannot type message',
        'closed_typing_body' => 'This ticket is closed. No further messages can be sent.',
        'file_type_limit_error' => 'Only images (JPG, PNG, GIF) and PDFs up to 2MB each are allowed.',
        'file_upload_limit_error' => 'You can only upload up to 5 files at a time.',
        'ticket_closed_title' => 'Ticket marked as closed',
        'ticket_deleted_title' => 'Ticket deleted',
        'breadcrumb_support' => 'Support',
        'breadcrumb_received_tickets' => 'Support Tickets',
        'breadcrumb_ticket_details' => 'Support Ticket Details',
        'breadcrumb_add_ticket' => 'Add Ticket',
        'breadcrumb_list' => 'List',
    ],

    'order_chat' => [
        'breadcrumb_title' => 'All Order Chats',
        'empty_message_error' => 'You cannot send an empty message. Please add some text or attach a file.',
        'file_upload_failed_title' => 'File Upload Failed',
        'file_upload_failed_body' => 'One or more files could not be uploaded.',
        'file_upload_limit_error_title' => 'Too many files',
        'file_upload_limit_error' => 'You can only upload up to 5 files at a time.',
        'file_type_limit_error' => 'Only images (JPG, PNG, GIF) and PDFs up to 2MB each are allowed.',
        'invalid_file_error' => 'Invalid file(s)',
        'breadcrumb_support' => 'Order Chats',
        'breadcrumb_dpharma_support' => 'Dpharma Order Chats',
        'breadcrumb_ticket_details' => 'Order Chat Details',
        'breadcrumb_add_ticket' => 'Add Order Chat',
        'breadcrumb_list' => 'List',
    ],

    'role_permission' => [
        'status_updated' => 'Status has been updated successfully.',
        'role_placeholder' => 'Role',
        'company_placeholder' => 'Pharmaceutical Company',
        'saved' => 'Saved',
        'roles_deleted_title' => 'Roles Deleted',
        'roles_deleted_body' => 'The selected roles have been deleted.',
        'some_roles_not_deleted_title' => 'Some Roles Not Deleted',
        'some_roles_not_deleted_body' => 'The following roles have users assigned and were not deleted: {roles}.',
        'protected_roles_not_deleted_title' => 'Protected Roles Not Deleted',
        'protected_roles_not_deleted_body' => 'The following protected roles were not deleted: {roles}.',
        'roles_activated_title' => 'Roles Activated',
        'roles_activated_body' => 'The selected roles have been activated.',
        'roles_inactivated_title' => 'Roles Inactivated',
        'roles_inactivated_body' => 'The selected roles have been inactivated.',
        'role_required' => 'Please select a role.',
        'cannot_change_status_title' => 'Cannot Change Status',
        'cannot_change_status_body' => 'The status cannot be changed because this role is assigned to one or more users.',
        'cannot_change_status_body_bulk' => 'The status cannot be changed for the following roles because they are assigned to users: {roles}.',
        'name_required' => 'The Role field is required.',
        'name_regex' => 'Only letters, numbers, and spaces are allowed.',
        'company_required' => 'Please select a pharmaceutical company.',
        'description' => 'Modify permissions assigned to this user',
        'cannot_change_status_body_bulk_role' => 'Cannot inactivate role ":role" because it has users: :users',
        'cannot_change_status_tooltip' => 'Status cannot be changed while users are assigned to this role',
    ],
];
