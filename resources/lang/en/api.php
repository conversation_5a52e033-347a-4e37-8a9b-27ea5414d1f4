<?php

return [

    'auth' => [
        'register_success' => 'Registration successful.',
        'register_email_error' => ':user_type registration was successful, but the email was not sent.',
        'invalid_token' => 'The verification token is invalid or has expired.',
        'already_email_verify' => 'Email has already been verified.',
        'email_verify' => 'Please verify your email to log in, or your account will be inactive.',
        'inactive_account' => 'Your account is inactive. Please contact your administrator.',
        'not_found' => "We can't find a user with that email address.",
        'email_sent' => 'A new verification email has been sent.',
        'invalid_cred' => 'Invalid credentials.',
        'user_details' => 'User  details.',
        'password_sent' => 'Your password has been reset. Please log in using your updated credentials!',
        'password_invalid_token' => 'This password reset token is invalid or you have already reset your password.',
        'unauthorized' => 'Unauthorized access.',
        'otp_send' => 'An OTP has been sent. Please check your email.',
        'otp_fail' => 'Unable to send O<PERSON>. Please re-check your mobile number or try again later.',
        'otp_invalid' => 'Invalid OTP.',
        'otp_expired' => 'OTP has expired.',
        'otp_success' => '<PERSON>TP verified successfully.',
        'mobile_verification_success' => 'Your mobile verification was successful.',
        'logout_success' => 'Logged out successfully.',
        'forgot_pass_success' => 'We have emailed your password reset link!',
        'forgot_pass_error' => 'Error sending the reset password link!',
        'user_not_found' => 'User  not found.',
        'term_conditions' => 'Please complete the following terms and conditions.',
        'added_term_conditions' => 'Terms and conditions added successfully.',
        'update_term_conditions' => 'Terms and conditions accepted successfully.',
        'email_exits' => 'The email address you used is already associated with another user account. Please use a different email.',
        'mail_issue' => 'Unable to send mail, please try again later'
    ],

    'user' => [
        'incorrect_password' => 'Incorrect old password.',
        'pass_change_success' => 'Your password has been reset. Please log in using your updated credentials!',
        'profile_update_success' => 'Your profile has been updated successfully!',
        'countries_list' => "Countries list.",
        'states_list' => "States list.",
        'cities_list' => "Cities list.",
        'landline_code_list' => "Landline code list.",
        'zip_code_code_list' => "Zip code list.",
        'profile_added' => 'Your profile image has been uploaded successfully.',
        'contact_us' => "Your inquiry has been successfully submitted to the administrator for review. They'll get back to you shortly!",
    ],

    'clinic' => [
        'onboarding_not_found' => 'Onboarding record not found for this facility.',
        'onboarding_success' => 'Onboarding step :step completed successfully.',

        'basic_details' => 'Basic details saved successfully.',
        'onboarding_add_step' => 'Billing and shipping address has been successfully saved.',
        'onboarding_dc_step' => 'Details of the person in charge have been successfully saved.',
        'onboarding_doc_step' => 'Documents have been successfully uploaded and saved.',
        'onboarding_5th_step' => 'Thank you for completing your onboarding. Once approved by the admin, your account will be activated.',
        'postal_code_invalid' => 'The postal code :code is invalid for the selected city.',
        'onboarding_details' => 'Facility onboarding details.',
        'account_list' => 'Account type list.',
        'business_type_list' => 'Business type list.',
        'banner_list' => 'Banner list.',
        'foam_list' => 'Foams list.',
        'brand_list' => 'Brand list.',
        'product_list' => 'Product list.',
        'supplier_list' => 'Suppliers list.',
        'category_list' => 'Category list.',
        'sub_category_list' => 'Subcategory list.',
        'static_page_detail' => 'Static page details.',
        'footer_detail' => 'Footer details.',
        'associated_pc_list' => 'Associated supplier list.',
        'restricted_pc_list' => 'Restricted supplier list.',
        'restricted_pc' => 'The supplier :pc is restricted. Please add this supplier to your associate list to proceed with their products',
        'associated_pc_save' => 'Associated supplier added successfully.',
        'associated_pc_delete' => 'Associated supplier deleted successfully.',
        'acp_certificate_status' => 'Facility APC certificate status retrieved successfully.',
        'apc_certificate_updated' => 'Facility APC certificate updated successfully.',
        'favourite_list' => "Added to your favourites list.",
        'favourite_remove' => "Removed from your favourites list.",
        'product_not_found' => "Product not found.",
        'product_detail' => "Product details.",
        "product_expired" => "The product :product has expired",
        "earning_point" => "You earned :POINT DPharma Points.",
        "will_earn_point" => 'You will earn :POINT DPharma Points.',

        "recent_search_list" => "Recent search product list for the facility.",
        "recent_search_cleared" => "Recent search products have been removed successfully.",
        "save_shipping_address" => "Addresses you added are sent for admin verification, it would be added to your profile once verified.",
        "request_shipping_address" => "Your shipping address request has been sent successfully.",
        "shipping_address" => "Shipping address list.",
        "set_default_address" => "Default address saved successfully.",
        "valid_default_add" => "Please select a default address.",
        "address_not_found" => "Address not found.",
        "refer_friends" => "Refer friends list.",
        "dpharma_points" => "Facility points list.",
        "clinic_default_address" => "Default address details.",
        "basic_info_success" => "Basic information updated successfully.",
        "address_success" => "Address updated successfully.",
        "doc_in_charge_success" => "Doctor in charge details updated successfully.",
        "document_success" => "Document updated successfully.",
        "referral_code_sent" => "Referral code sent successfully.",
        "request_address_success" => "Your requested address has been sent successfully.",
        "basic_info" => "Basic information has been added successfully and will be visible in the profile upon admin approval.",
        "profile_address" => "Addresses have been added successfully and will be visible in the profile upon admin approval.",
        "profile_doc_in_charge" => "Doctor in charge details have been added successfully and will be visible in the profile upon admin approval.",
        "profile_document" => "Your document has been added successfully and will be visible in the profile upon admin approval.",
        "order" => [
            "list" => "Order list.",
            "detail" => "Order details.",
            "placed" => "Order placed successfully.",
            "order_not_found" => "Order not found.",
            "payment_failed" => 'Payment failed with unknown status. Please contact support.',
            "invalid_payout" => 'Payout transaction not found.',
            "re_order" => "Your re-ordered products have been added to the cart. You can check out now.",
            "cancel" => "Your order has been cancelled successfully.",
            "payment_status" => "Payment successful. Thank you for your purchase!",
            "payment_0_status" => 'Your payment is pending. Please wait while we confirm the transaction.',
            "payment_2_status" => 'Payment failed. Please try again or use a different payment method.',
            "payment_3_status" => 'Payment was cancelled. No charges were made.',
            "payment_4_status" => 'Payment failed: Transaction limit exceeded. Please try a smaller amount.',
            "payment_5_status" => 'Payment failed: Minimum transaction amount not met.',
            "payment_6_status" => 'Payment failed: Insufficient funds. Please check your account balance.',
            "payment_7_status" => 'Payment failed: Invalid transaction. Please contact support.',
            "payment_8_status" => 'You cancelled the payment. No charges were made.',
            "payment_9_status" => 'Payment authorized. Awaiting final confirmation.',
            "payment_10_status" => 'Payment session expired. Please initiate the payment again.',
            "payment_11_status" => 'Payment has been refunded successfully.',
            "invalid_signature" => 'Invalid signature.',
            'retry_payment_status' => 'Your payment is still pending — please complete the payment to proceed.',
            "pending_payment_status" => 'Your payment is pending. Please wait while we confirm the transaction.',
            "order_payment_expired" => 'Payments can be retried within 24 hours of order placement.'
        ],
        "cart" => [
            "list" => "Cart list.",
            "detail" => "Cart details.",
            "cart_not_found" => "Cart not found.",
            "cart_empty" => "Cart is empty.",
            "cart_add" => "The product has been successfully added to your cart.",
            "cart_updated" => "Cart details updated successfully.",
            "cart_delete" => "Product deleted from the cart.",
            "cart_clear" => "All products have been removed from the cart.",
            'points_not_enough' => "The applied amount of points cannot be used because you don't have enough points.",
            "cart_payment_list" => "Cart payment list.",
            "cart_list" => "Cart list.",
            "cart_review" => "Cart Review Page Detail.",
            'applied_points_error' => "The applied D Pharma points exceed your order amount and cannot be used.",
            'wolesale_size' => 'Pack of ',
            'qty_limit_exceed' => 'We are unable to process your request as the quantity exceeds the available stock.',
            'qty_limit_exceed_sup' => 'We are unable to process your request as the requested quantity exceeds the available stock for :product in :pc',
            'add_qty' => 'You can add only :Qty more'
        ],
        "support" => [
            "category" => [
                "list" => "Support category list.",
            ],
            "list" => "Support list.",
            "create" => "Ticket created successfully!",
            "sent_message" => "Message sent successfully.",
            "ticket_closed" => "Ticket closed successfully.",
            "unable_to_send_msg" => "You cannot send a message because this ticket has been closed.",
            "not_found" => "Ticket not found.",
            "details" => "Ticket details.",
            "message_detail" => "Ticket thread message details.",
            "ticket_exists" => "A support ticket already exists for the receiver and order.",
            "un_read_msg" => "Unread message list.",
        ],
        "thread" => [
            "already_create" => "Thread already created; you can chat directly!",
            "create" => "Thread created successfully!",
            "message_detail" => "Thread message details.",
            "not_found" => "Thread not found.",
        ]
    ],

    "notifications" => [
        "new_facility_body" => "A new facility has registered.",
        "new_facility_title" => "New Facility Registered",
        "new_inquiry_body" => "A new inquiry has been submitted by a facility. Please review the details.",
        "new_inquiry_title" => "New Inquiry Submitted",
        "new_order_cancel_body" => "An order has been cancelled. Click below to view details.",
        "new_order_cancel_title" => "Order Cancelled",
        "new_support_ticket_body" => "A new Support Ticket generated from facility.",
        "new_support_ticket_title" => "A Support Ticket",
        "new_order_body" => "A new order has been received. Click below to view details.",
        "new_order_title" => "New Order",
        "new_message_body" => "A new message received from facility.",
        "new_message_title" => "A New Message ",
        "facility_profile_approval_body" => "A facility has submitted their profile for approval. Kindly review and take appropriate action regarding their profile.",
        "facility_profile_approval_title" => "Facility Approval Profile Submission",
        "pc_onboarding_title" => "Pharmaceutical Supplier profile for approval",
        'pc_onboarding_body' => 'Pharmaceutical Supplier has been submitted profile for approval'
    ]

];
