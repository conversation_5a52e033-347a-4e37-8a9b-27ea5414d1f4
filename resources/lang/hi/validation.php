<?php

return [

    'change_pass' => [
        'old_password_required' => 'पुराना पासवर्ड आवश्यक है।',
        'password_required' => 'नया पासवर्ड आवश्यक है।',
        'password_min' => 'नया पासवर्ड कम से कम 8 अक्षरों का होना चाहिए।',
        'password_max' => 'नया पासवर्ड 16 अक्षरों से अधिक नहीं होना चाहिए।',
        'password_regex' => 'नए पासवर्ड में कम से कम एक बड़ा अक्षर, एक छोटा अक्षर, एक संख्या, और एक विशेष अक्षर (@$!%*#?&) शामिल होना चाहिए।',
        'password_confirmation_required' => 'पासवर्ड पुष्टि आवश्यक है।',
        'password_confirmation_same' => 'पासवर्ड पुष्टि नए पासवर्ड से मेल खाना चाहिए।',
    ],
    'contact' => [
        'name_required' => 'नाम आवश्यक है।',
        'name_string' => 'नाम मान्य स्ट्रिंग होना चाहिए।',
        'name_max' => 'नाम 50 अक्षरों से अधिक नहीं होना चाहिए।',
        'name_regex' => 'नाम में केवल अक्षर, स्पेस और हाइफ़न हो सकते हैं।',
        'email_required' => 'ईमेल आवश्यक है।',
        'email_string' => 'ईमेल मान्य स्ट्रिंग होना चाहिए।',
        'email_email' => 'कृपया एक मान्य ईमेल पता दर्ज करें।',
        'code_nullable' => 'कोड वैकल्पिक है।',
        'landline_number_nullable' => 'लैंडलाइन नंबर वैकल्पिक है।',
        'landline_number_integer' => 'लैंडलाइन नंबर एक मान्य संख्या होनी चाहिए।',
        'subject_nullable' => 'विषय वैकल्पिक है।',
        'subject_string' => 'विषय एक मान्य स्ट्रिंग होनी चाहिए।',
        'description_required' => 'विवरण आवश्यक है।',
        'description_string' => 'विवरण एक मान्य स्ट्रिंग होनी चाहिए।',
        'description_max' => 'विवरण 500 अक्षरों से अधिक नहीं होना चाहिए।',
    ],
    'user_profile' => [
        'name_required' => 'नाम आवश्यक है।',
        'name_string' => 'नाम एक मान्य स्ट्रिंग होनी चाहिए।',
        'name_max' => 'नाम 100 अक्षरों से अधिक नहीं होना चाहिए।',
        'name_regex' => 'नाम में केवल अक्षर, स्पेस और हाइफ़न हो सकते हैं।',
        'image_required' => 'प्रोफ़ाइल छवि आवश्यक है।',
    ],
    'address' => [
        'addresses_required' => 'पता फ़ील्ड आवश्यक है।',
        'addresses_array' => 'पते एक सरणी होने चाहिए।',
        'addresses_id_exists' => 'चयनित पता मौजूद नहीं है।',
        'addresses_address_1_required' => 'पता 1 आवश्यक है।',
        'addresses_address_1_string' => 'पता 1 एक मान्य स्ट्रिंग होनी चाहिए।',
        'addresses_address_2_string' => 'पता 2 एक मान्य स्ट्रिंग होनी चाहिए।',
        'addresses_postal_code_required' => 'डाक कोड आवश्यक है।',
        'addresses_postal_code_integer' => 'डाक कोड एक मान्य संख्या होनी चाहिए।',
        'addresses_country_id_required' => 'देश आवश्यक है।',
        'addresses_state_id_required' => 'राज्य आवश्यक है।',
        'addresses_city_id_required' => 'शहर आवश्यक है।',
        'addresses_is_default_required' => 'डिफ़ॉल्ट पता फ़ील्ड आवश्यक है।',
        'addresses_is_default_boolean' => 'डिफ़ॉल्ट पता फ़ील्ड सही या गलत होना चाहिए।',
        'is_billing_address_same_boolean' => 'बिलिंग पता फ़्लैग सही या गलत होना चाहिए।',
        'b_address_1_required' => 'बिलिंग पता 1 आवश्यक है।',
        'b_address_1_string' => 'बिलिंग पता 1 एक मान्य स्ट्रिंग होनी चाहिए।',
        'b_address_2_required' => 'बिलिंग पता 2 आवश्यक है।',
        'b_address_2_string' => 'बिलिंग पता 2 एक मान्य स्ट्रिंग होनी चाहिए।',
        'b_postal_code_required' => 'बिलिंग डाक कोड आवश्यक है।',
        'b_postal_code_integer' => 'बिलिंग डाक कोड एक मान्य संख्या होनी चाहिए।',
        'b_country_id_required' => 'बिलिंग देश आवश्यक है।',
        'b_state_id_required' => 'बिलिंग राज्य आवश्यक है।',
        'b_city_id_required' => 'बिलिंग शहर आवश्यक है।',
        'remove_addresses_array' => 'हटाए गए पते एक सरणी होनी चाहिए।',
    ],
    'add_to_cart' => [
        'supplier_id_required' => 'आपूर्तिकर्ता आईडी आवश्यक है।',
        'product_id_required' => 'उत्पाद आईडी आवश्यक है।',
        'quantity_required' => 'मात्रा आवश्यक है।',
        'quantity_integer' => 'मात्रा एक पूर्णांक होनी चाहिए।',
        'quantity_min' => 'मात्रा कम से कम 1 होनी चाहिए।',
    ],
    'add_to_cart_update' => [
        'products_required' => 'प्रोडक्ट्स फ़ील्ड आवश्यक है।',
        'products_array' => 'प्रोडक्ट्स फ़ील्ड एक ऐरे होना चाहिए।',
        'product_id_required' => 'प्रत्येक प्रोडक्ट का आईडी होना आवश्यक है।',
        'product_id_string' => 'प्रत्येक प्रोडक्ट आईडी एक स्ट्रिंग होनी चाहिए।',
        'product_qty_required' => 'प्रत्येक प्रोडक्ट की मात्रा होना आवश्यक है।',
        'product_qty_integer' => 'मात्रा एक पूर्णांक (इंटीजर) होनी चाहिए।',
        'product_qty_min' => 'मात्रा कम से कम 1 होनी चाहिए।',
    ],
    'basic_info' => [
        'business_type_required' => 'व्यवसाय प्रकार आवश्यक है।',
        'clinic_name_required' => 'सुविधा का नाम आवश्यक है।',
        'clinic_name_string' => 'सुविधा का नाम स्ट्रिंग होना चाहिए।',
        'clinic_number_required' => 'सुविधा नंबर आवश्यक है।',
        'clinic_number_integer' => 'सुविधा नंबर एक मान्य पूर्णांक होना चाहिए।',
        'mobile_code_required' => 'मोबाइल कोड आवश्यक है।',
        'mobile_code_string' => 'मोबाइल कोड स्ट्रिंग होना चाहिए।',
        'mobile_number_required' => 'मोबाइल नंबर आवश्यक है।',
        'mobile_number_integer' => 'मोबाइल नंबर एक मान्य पूर्णांक होना चाहिए।',
        'landline_code_required' => 'लैंडलाइन कोड आवश्यक है।',
        'landline_code_string' => 'लैंडलाइन कोड स्ट्रिंग होना चाहिए।',
        'landline_number_required' => 'लैंडलाइन नंबर आवश्यक है।',
        'landline_number_integer' => 'लैंडलाइन नंबर एक मान्य पूर्णांक होना चाहिए।',
        'company_name_required' => 'कंपनी का नाम आवश्यक है।',
        'company_number_required' => 'कंपनी नंबर आवश्यक है।',
        'company_number_integer' => 'कंपनी नंबर एक मान्य पूर्णांक होना चाहिए।',
        'clinic_owner_required' => 'सुविधा का मालिक आवश्यक है।',
        'clinic_year_required' => 'सुविधा की स्थापना वर्ष आवश्यक है।',
        'tin_number_required' => 'टीआईएन नंबर आवश्यक है।',
        'tin_number_max' => 'टीआईएन नंबर 13 अक्षरों से अधिक नहीं हो सकता।',
        'sst_number_max' => 'एसएसटी नंबर 15 अक्षरों से अधिक नहीं हो सकता।',
    ],
    'cart_payment' => [
        'suppliers_required' => 'आपूर्तिकर्ता फ़ील्ड आवश्यक है।',
        'suppliers_array' => 'आपूर्तिकर्ता एक सरणी होनी चाहिए।',
        'supplier_id_required' => 'प्रत्येक आपूर्तिकर्ता आईडी आवश्यक है।',
        'supplier_id_string' => 'प्रत्येक आपूर्तिकर्ता आईडी एक मान्य स्ट्रिंग होनी चाहिए।',
        'payment_type_required' => 'भुगतान प्रकार आवश्यक है।',
        'payment_type_invalid' => 'भुगतान प्रकार इनमें से एक होना चाहिए: अभी भुगतान करें, बाद में भुगतान करें, या क्रेडिट लाइन।',
        'applied_points_integer' => 'लागू किए गए अंक एक मान्य पूर्णांक होने चाहिए।',
    ],
    'certificate' => [
        'apc_certificate_required' => 'APC प्रमाणपत्र आवश्यक है।',
        'apc_certificate_file' => 'APC प्रमाणपत्र एक मान्य फ़ाइल होना चाहिए।',
        'apc_certificate_mimes' => 'APC प्रमाणपत्र केवल इन प्रकार की फ़ाइल हो सकती है: jpeg, jpg, png, pdf।',
        'apc_certificate_max' => 'APC प्रमाणपत्र 10MB से अधिक नहीं होना चाहिए।',

        'expired_date_required' => 'APC प्रमाणपत्र की समाप्ति तिथि आवश्यक है।',
        'expired_date_digits' => 'APC प्रमाणपत्र की समाप्ति तिथि 4 अंकों का वर्ष होना चाहिए।',
        'expired_date_integer' => 'APC प्रमाणपत्र की समाप्ति तिथि एक मान्य वर्ष होना चाहिए।',
        'expired_date_min' => 'APC प्रमाणपत्र की समाप्ति वर्ष वर्तमान वर्ष से कम नहीं हो सकता।',
    ],
    'clinic' => [
        'account_type_required' => 'सुविधा का खाता प्रकार आवश्यक है।',
    ],
    'certificate' => [
        'invalid_file_format' => 'प्रत्येक प्रमाणपत्र एक मान्य फ़ाइल होना चाहिए।',
        'allowed_formats' => 'अनुमत फ़ाइल प्रकार: jpeg, jpg, png, pdf।',
        'file_size_limit' => 'प्रत्येक फ़ाइल 10MB से अधिक नहीं होनी चाहिए।',
    ],
    'favourite' => [
        'type_required' => 'टाइप फ़ील्ड आवश्यक है।',
        'type_invalid' => 'टाइप केवल supplier या product होना चाहिए।',

        'supplier_id_required_if' => 'जब टाइप supplier हो, तब सप्लायर आईडी आवश्यक है।',
        'product_id_required_if' => 'जब टाइप product हो, तब प्रोडक्ट आईडी आवश्यक है।',
    ],
    'forgot_password' => [
        'email_required' => 'ईमेल फ़ील्ड आवश्यक है।',
        'email_invalid' => 'कृपया एक मान्य ईमेल पता दर्ज करें।',
        'email_not_found' => 'दिया गया ईमेल हमारे रिकॉर्ड में मौजूद नहीं है।',
        'user_deleted' => 'यह उपयोगकर्ता व्यवस्थापक द्वारा हटा दिया गया है।',
        'user_inactive' => 'आपका खाता निष्क्रिय है। कृपया व्यवस्थापक से संपर्क करें।',
        'user_facility_allow' => 'यह खाता पार्टनर के रूप में पंजीकृत नहीं है।',
    ],
    'help_support' => [
        'category_required' => 'सपोर्ट श्रेणी आवश्यक है।',
        'order_required' => 'ऑर्डर आईडी आवश्यक है।',
        'subject_required' => 'विषय फ़ील्ड आवश्यक है।',
        'description_required' => 'विवरण फ़ील्ड आवश्यक है।',
    ],
    'otp' => [
        'email_required' => 'ईमेल फ़ील्ड आवश्यक है।',
        'email_invalid' => 'कृपया एक मान्य ईमेल पता दर्ज करें।',
        'email_string' => 'ईमेल एक मान्य स्ट्रिंग होनी चाहिए।',
        'email_not_found' => 'यह ईमेल पता हमारे सिस्टम में पंजीकृत नहीं है।',
        'otp_required' => 'OTP फ़ील्ड आवश्यक है।',
        'otp_invalid' => 'प्रदान किया गया OTP अमान्य है।',
        'email_unique' => 'यह ईमेल पहले से ही पंजीकृत है। कृपया कोई अन्य उपयोग करें।',
    ],
    'login' => [
        'email_required' => 'ईमेल फ़ील्ड आवश्यक है।',
        'email_invalid' => 'कृपया एक वैध ईमेल पता दर्ज करें।',
        'email_not_found' => 'यह ईमेल पता हमारे सिस्टम में पंजीकृत नहीं है।',
        'password_required' => 'पासवर्ड फ़ील्ड आवश्यक है।',
        'password_string' => 'पासवर्ड एक वैध स्ट्रिंग होनी चाहिए।',
        'stay_login_required' => 'लॉगिन बनाए रखने का फ़ील्ड आवश्यक है।',
        'stay_login_boolean' => 'लॉगिन बनाए रखने का फ़ील्ड सही (true) या गलत (false) होना चाहिए।',
    ],

    'onboarding' => [
        'completed_step_required' => 'पूरा किया गया चरण आवश्यक है।',
        'completed_step_in' => 'पूरा किया गया चरण 1, 2, 3, 4 या 5 में से एक होना चाहिए।',

        // चरण 1
        'business_type_required' => 'व्यवसाय प्रकार आवश्यक है।',
        'clinic_name_required' => 'क्लिनिक का नाम आवश्यक है।',
        'clinic_number_required' => 'क्लिनिक नंबर आवश्यक है।',
        'mobile_code_required' => 'मोबाइल कोड आवश्यक है।',
        'mobile_number_required' => 'मोबाइल नंबर आवश्यक है और एक पूर्णांक (integer) होना चाहिए।',
        'company_name_required' => 'कंपनी का नाम आवश्यक है।',
        'company_number_required' => 'कंपनी नंबर आवश्यक है।',
        'clinic_owner_required' => 'क्लिनिक मालिक का नाम आवश्यक है।',
        'clinic_year_required' => 'क्लिनिक की स्थापना वर्ष आवश्यक है।',
        'tin_number_required' => 'टीआईएन नंबर आवश्यक है।',
        'sst_number_max' => 'एसएसटी नंबर 15 अक्षरों से अधिक नहीं हो सकता।',

        // चरण 2
        'addresses_required' => 'पता सूची आवश्यक है।',
        'address_1_required' => 'पता पंक्ति 1 आवश्यक है।',
        'address_2_required' => 'पता पंक्ति 2 आवश्यक है।',
        'postal_code_required' => 'डाक कोड आवश्यक है।',
        'postal_code_digits' => 'डाक कोड बिल्कुल 5 अंकों का होना चाहिए।',
        'country_id_required' => 'देश चयन आवश्यक है।',
        'state_id_required' => 'राज्य चयन आवश्यक है।',
        'city_id_required' => 'शहर चयन आवश्यक है।',

        // चरण 3
        'dc_name_required' => 'डॉक्टर का नाम आवश्यक है।',
        'dc_nric_required' => 'डॉक्टर का एनआरआईसी आवश्यक है।',
        'dc_mmc_number_required' => 'डॉक्टर का एमएमसी नंबर आवश्यक है।',
        'dc_apc_number_max' => 'एपीसी नंबर 10 अक्षरों से अधिक नहीं हो सकता।',
        'dc_signature_mimes' => 'हस्ताक्षर फ़ाइल का प्रकार jpeg, jpg, या png होना चाहिए।',
        'is_admin_in_charge_boolean' => 'प्रशासक जिम्मेदार है या नहीं, यह सही (true) या गलत (false) होना चाहिए।',
        'ac_name_required' => 'जब डॉक्टर जिम्मेदार नहीं हो, तो प्रशासक का नाम आवश्यक है।',

        // चरण 4
        'clinic_account_type_required' => 'क्लिनिक खाता प्रकार आवश्यक है।',
        'borang_certificate_array' => 'बोरंग प्रमाणपत्र एक सूची (array) होनी चाहिए।',
        'mmc_certificate_mimes' => 'एमएमसी प्रमाणपत्र jpeg, jpg, png, या pdf फ़ाइल होनी चाहिए।',

        // चरण 5
        'is_declare_info_required' => 'आपको जानकारी की घोषणा करनी होगी।',
        'is_term_required' => 'आपको नियम और शर्तें स्वीकार करनी होंगी।',
    ],

    'payment' => [
        'session_id_required' => 'सत्र आईडी आवश्यक है।',
    ],

    'thread' => [
        'order_id_required' => 'ऑर्डर आईडी आवश्यक है।',
        'receiver_id_invalid' => 'चयनित प्राप्तकर्ता अमान्य है।',
    ],

    'thread_message' => [
        'thread_id_required' => 'थ्रेड आईडी आवश्यक है।',
        'thread_id_invalid' => 'चयनित थ्रेड अमान्य है।',
        'text_string' => 'संदेश एक मान्य टेक्स्ट होना चाहिए।',
        'text_max' => 'संदेश 16,000 अक्षरों से अधिक नहीं होना चाहिए।',
        'files_array' => 'फाइलें एक सूची (array) होनी चाहिए।',
        'files_max' => 'आप अधिकतम 5 फाइलें अपलोड कर सकते हैं।',
        'files_file' => 'प्रत्येक अपलोड की गई फाइल मान्य होनी चाहिए।',
        'files_mimes' => 'फाइलें jpg, jpeg, png, या pdf प्रकार की होनी चाहिए।',
        'files_max_size' => 'प्रत्येक फ़ाइल का आकार 2MB से अधिक नहीं होना चाहिए।',
    ],

    'term_condition' => [
        'email_required' => 'ईमेल फ़ील्ड आवश्यक है।',
        'email_invalid' => 'कृपया एक मान्य ईमेल पता प्रदान करें।',
        'email_not_found' => 'दिया गया ईमेल हमारे सिस्टम में पंजीकृत नहीं है।',
        'terms_id_required' => 'नियम आईडी आवश्यक है।',
        'terms_id_invalid' => 'चयनित नियम अमान्य हैं।',
        'password_required' => 'पासवर्ड फ़ील्ड आवश्यक है।',
        'password_string' => 'पासवर्ड एक मान्य स्ट्रिंग होनी चाहिए।',
        'stay_login_required' => 'लॉगिन बनाए रखने का फ़ील्ड आवश्यक है।',
        'stay_login_boolean' => 'लॉगिन बनाए रखने का फ़ील्ड सही (true) या गलत (false) होना चाहिए।',
    ],

    'support_message' => [
        'ticket_required' => 'समर्थन टिकट आईडी आवश्यक है।',
        'ticket_invalid' => 'चयनित समर्थन टिकट अमान्य है।',
        'text_string' => 'संदेश एक मान्य टेक्स्ट स्ट्रिंग होनी चाहिए।',
        'text_max' => 'संदेश 16,000 अक्षरों से अधिक नहीं हो सकता।',
        'files_array' => 'संलग्न फाइलें एक सूची (array) होनी चाहिए।',
        'files_max' => 'आप अधिकतम 5 फाइलें संलग्न कर सकते हैं।',
        'file_invalid' => 'प्रत्येक संलग्न फ़ाइल मान्य होनी चाहिए।',
        'file_mimes' => 'केवल JPG, JPEG, PNG और PDF फ़ाइलें अनुमत हैं।',
        'file_max_size' => 'प्रत्येक फ़ाइल 2MB से छोटी होनी चाहिए।',
    ],

    'supplier' => [
        'pc_id_required' => 'आपूर्तिकर्ता आईडी आवश्यक है।',
        'pc_id_invalid' => 'चयनित आपूर्तिकर्ता अमान्य है।',
        'account_string' => 'खाता संख्या एक मान्य स्ट्रिंग होनी चाहिए।',
        'account_min' => 'खाता संख्या कम से कम 16 अंकों की होनी चाहिए।',
        'account_max' => 'खाता संख्या 20 अंकों से अधिक नहीं हो सकती।',
    ],

    'social_login' => [
        'id_token_required' => 'आईडी टोकन आवश्यक है।',
        'id_token_string' => 'आईडी टोकन एक मान्य स्ट्रिंग होनी चाहिए।',
        'social_type_required' => 'सोशल लॉगिन प्रकार आवश्यक है।',
        'social_type_invalid' => 'अमान्य सोशल लॉगिन प्रकार। अनुमत मान: google, apple।',
    ],

    'common' => [
        'name_required' => 'नाम फ़ील्ड आवश्यक है।',
        'name_string' => 'नाम एक मान्य स्ट्रिंग होनी चाहिए।',
        'name_max' => 'नाम 100 अक्षरों से अधिक नहीं हो सकता।',
    ],
    'product_checkout' => [
        'applied_points_integer' => 'लागू किए गए अंक पूर्णांक होने चाहिए।',
        'shipping_address_required' => 'शिपिंग पता आवश्यक है।',
        'suppliers_required' => 'कम से कम एक आपूर्तिकर्ता आवश्यक है।',
        'suppliers_array' => 'आपूर्तिकर्ताओं को एक वैध सरणी होना चाहिए।',
        'supplier_id_required' => 'प्रत्येक आपूर्तिकर्ता का एक आईडी होना आवश्यक है।',
        'supplier_id_string' => 'आपूर्तिकर्ता आईडी एक मान्य स्ट्रिंग होनी चाहिए।',
        'payment_type_required' => 'प्रत्येक आपूर्तिकर्ता के लिए भुगतान प्रकार आवश्यक है।',
        'payment_type_invalid' => 'भुगतान प्रकार निम्नलिखित में से एक होना चाहिए: pay_now, pay_later, credit_line।',
    ],

    'person_in_charge' => [
        'dc_name_required' => 'प्रभारी डॉक्टर का नाम आवश्यक है।',
        'dc_nric_required' => 'प्रभारी डॉक्टर का NRIC आवश्यक है।',
        'dc_mmc_number_required' => 'प्रभारी डॉक्टर का MMC नंबर आवश्यक है।',
        'dc_mmc_number_integer' => 'प्रभारी डॉक्टर का MMC नंबर पूर्णांक होना चाहिए।',
        'dc_apc_number_required' => 'प्रभारी डॉक्टर का APC नंबर आवश्यक है।',
        'dc_phone_code_required' => 'प्रभारी डॉक्टर का फोन कोड आवश्यक है।',
        'dc_phone_number_required' => 'प्रभारी डॉक्टर का फोन नंबर आवश्यक है।',
        'dc_phone_number_integer' => 'प्रभारी डॉक्टर का फोन नंबर एक मान्य संख्या होनी चाहिए।',
        'dc_landline_number_integer' => 'प्रभारी डॉक्टर का लैंडलाइन नंबर एक मान्य संख्या होनी चाहिए।',
        'dc_signature_file' => 'प्रभारी डॉक्टर के हस्ताक्षर एक फ़ाइल होनी चाहिए।',
        'dc_signature_mimes' => 'प्रभारी डॉक्टर के हस्ताक्षर jpeg, jpg या png फ़ाइल होनी चाहिए।',
        'dc_signature_max' => 'प्रभारी डॉक्टर के हस्ताक्षर का आकार 10MB से अधिक नहीं होना चाहिए।',

        'ac_name_required_if' => 'जब डॉक्टर प्रभारी नहीं होता है, तो सहायक प्रभारी का नाम आवश्यक होता है।',
        'ac_nric_required_if' => 'जब डॉक्टर प्रभारी नहीं होता है, तो सहायक प्रभारी का NRIC आवश्यक होता है।',
        'ac_phone_code_required_if' => 'जब डॉक्टर प्रभारी नहीं होता है, तो सहायक प्रभारी का फोन कोड आवश्यक होता है।',
        'ac_phone_number_required_if' => 'जब डॉक्टर प्रभारी नहीं होता है, तो सहायक प्रभारी का फोन नंबर आवश्यक होता है।',
        'ac_phone_number_integer' => 'सहायक प्रभारी का फोन नंबर एक मान्य संख्या होनी चाहिए।',
        'ac_landline_number_integer' => 'सहायक प्रभारी का लैंडलाइन नंबर एक मान्य संख्या होनी चाहिए।',
    ],


];
