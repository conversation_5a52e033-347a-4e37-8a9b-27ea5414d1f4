@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

body {
    background-color: var(--Grey-50, #F2F6FA) !important;
}

.fi-main {
    background-color: var(--Grey-50, #F2F6FA) !important;
}

.fi-sidebar-nav {
    background-color: #fff !important;
    scrollbar-width: none;
    border-top: 1px solid #E4E7EC !important;
    @apply border-r;
    @apply border-r-gray-200;
}

.fi-sidebar-nav-groups {
    row-gap: 1rem;
}

.fi-sidebar-nav {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}

.fi-sidebar-header button:first-of-type::before {
    content: '';
    display: inline-block;
    width: 50px;
    /* fixed from 5024px */
    height: 50px;
    margin-right: 0.5rem;
    background-image: url('../../public/images/favicon.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.filepond--root[data-style-panel-layout~="circle"] .filepond--file .filepond--file-action-button[data-align*="bottom"]:not([data-align*="center"]) {
    background-color: rgba(0, 0, 0, .5);
}

/* Tooltip container */
.tippy-box[data-theme~='filament'] {
    max-width: 400px !important;
}

/* Tooltip content */
.tippy-content {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    padding: 12px !important;
    text-align: left !important;
}

/* For long unbroken strings */
.tippy-content {
    word-break: break-word !important;
}

/* Optional: Better appearance */
.tippy-box {
    background: #1f2937 !important;
    color: #f9fafb !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.profit-share {
    .fi-ta-table {

        th:first-child,
        td:first-child {
            width: 20%;
        }

        th:nth-child(2),
        td:nth-child(2) {
            width: 20%;
        }

        th:nth-child(3),
        td:nth-child(3) {
            width: 20%;
        }

        th:nth-child(4),
        td:nth-child(4) {
            width: 20%;
        }

        th:nth-child(5),
        td:nth-child(5) {
            width: 20%;
        }
    }

    .profit-share-total {
        span {
            width: 20%;
        }
    }

    /* Summary row styling */
    .fi-ta-summary-row {
        background-color: #fff !important;
        /* Light blue-gray background */
        font-weight: 600 !important;
        color: #1e40af !important;
        /* Dark blue text color */
        border-top: 6px solid #F2F6FA !important;

        span {
            color: rgba(var(--gray-950), var(--tw-text-opacity, 1));
        }
    }

    /* Hover effect */
    .fi-ta-summary-row:hover td {
        background-color: #fff !important;
        /* Lighter blue on hover */
    }

    /* Make the "Total" label stand out */
    .fi-ta-summary-row td:first-child {
        font-weight: 700 !important;
        /* color: #1e40af !important; */
        /* Dark blue for the "Total" label */
    }

    /* Add a subtle left border to the first cell */
    .fi-ta-summary-row td:first-child {
        /* Blue accent border */
        padding-left: 0.75rem !important;
    }
}

.ticketChatTable {
    .fi-ta {
        position: static;

        .fi-ta-ctn {
            position: static;
        }
    }

    .fi-pagination {
        display: flex;
        width: 100%;
        grid: none;
    }
}

.fi-sidebar-nav {
    .fi-sidebar-nav-groups {
        @apply gap-1;
    }

    .fi-sidebar-group-items {
        @apply gap-0;

        .fi-sidebar-item {
            @apply relative;

            >a {
                @apply outline-none;
                @apply bg-transparent;
                @apply py-2.5;
            }


        }

        .fi-sidebar-item-grouped-border {
            @apply absolute;
            @apply top-0;
            @apply left-2;
            @apply h-full;

            div:first-child {
                @apply top-0;
                @apply bottom-0;
                @apply w-0.5;

            }

            div:last-child {
                @apply top-0;
                @apply bottom-0;
                @apply w-0.5;
                @apply h-full;


            }

            .bg-gray-300,
            .bg-gray-400 {
                @apply bg-gray-200;
            }
        }
    }




    .fi-sidebar-item-grouped-border+.fi-sidebar-item-label {
        @apply ps-8;
    }
}

.fi-topbar {
    .fi-topbar-database-notifications-btn {
        .fi-icon-btn-badge-ctn {
            @apply transform-none;
            @apply -top-1;
            @apply right-0;
            @apply left-auto;
            @apply bg-primary-100;
            @apply border-primary-100;
        }
    }
}

.fi-sidebar-header {
    .fi-logo {
        @apply h-auto !important;
        @apply w-[175px];
    }
}


.fi-ta-actions {
    .fi-icon-btn {
        @apply border;
    }

    .fi-dropdown-panel {
        .fi-dropdown-list {
            a {
                @apply text-gray-900;

                svg {
                    @apply text-gray-900;
                    @apply w-5;
                }
            }

            button {
                @apply text-gray-900;

                svg {
                    @apply text-gray-900;
                    @apply w-5;
                }
            }
        }
    }

    .fi-color-primary {
        @apply bg-primary-600;
        @apply border-primary-600 !important;
        @apply text-white !important;

        svg {
            @apply text-white !important;
        }
    }

    .fi-color-success {
        @apply bg-success-600;
        @apply border-success-600 !important;
        @apply text-white !important;

        svg {
            @apply text-white !important;
        }
    }

    .fi-color-danger {
        @apply bg-danger-600;
        @apply border-danger-600 !important;
        @apply text-white !important;

        svg {
            @apply text-white !important;
        }
    }

    .fi-color-gray {
        @apply bg-info-400 !important;
        @apply border-info-400 !important;
        @apply text-white !important;

        svg {
            @apply text-white !important;
        }
    }

    .fi-dropdown-list {
        .fi-color-gray {
            @apply bg-white !important;

            svg {
                @apply text-primary-600 !important;
            }
        }
    }
}



.fi-btn {
    svg {
        @apply w-4;
        @apply h-4;
    }
}

.fi-ta-text-item {
    @apply py-0;

    svg {
        @apply w-4;
        @apply h-4;
    }
}

.filament-dropdown-trigger,
.fi-dropdown-trigger {
    .text-gray-400 {
        @apply text-primary-600;
    }
}



.fi-sidebar-group-button {

    .text-gray-400,
    .text-gray-500 {
        @apply text-gray-800
    }
}

.fi-sidebar-group-items {

    .text-gray-400,
    .text-gray-500 {
        @apply text-gray-800
    }
}

.fi-pagination-items {

    .fi-pagination-item {
        @apply border-primary-100;
    }


    .fi-active {
        button {
            @apply bg-primary-600;
            @apply text-white;

            span {
                @apply text-white;
            }
        }
    }
}

.fi-tabs {
    .fi-tabs-item-active {
        @apply bg-primary-600;

        .fi-tabs-item-label {
            @apply text-white;
        }
    }
}

.fi-topbar-database-notifications-btn {
    svg {
        @apply text-primary-600;
    }
}

.paybtn {
    span {
        @apply hidden
    }
}
/* Wrapper label for checkbox + text */
/* Align checkbox and label text vertically */
.fix-checkbox-label-click label.fi-fo-checkbox-list-option-label {
    display: inline-flex !important;
    align-items: center !important; /* ✅ this centers checkbox & text vertically */
    gap: 0.5rem !important;
    line-height: 1.25 !important;
    padding: 0 !important;
    white-space: normal !important;
}

/* Ensure label text inside doesn't shift */
.fix-checkbox-label-click label.fi-fo-checkbox-list-option-label .fi-fo-checkbox-list-option-label {
    position: static !important;
    line-height: inherit !important;
    top: auto !important;
}

/* Checkbox alignment fix */
input[type="checkbox"].fi-checkbox-input {
    vertical-align: middle !important;
    margin-top: 0 !important;
}

/* Custom tooltip for disabled buttons */
.custom-tooltip {
    position: relative;
    display: inline-block;
}

.custom-tooltip .tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #1f2937;
    color: #f9fafb;
    text-align: center;
    border-radius: 8px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    white-space: nowrap;
}

.custom-tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #1f2937 transparent transparent transparent;
}

.custom-tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Ensure disabled buttons can still show tooltips */
button[disabled][data-tooltip-text] {
    pointer-events: auto !important;
}

button[disabled][data-tooltip-text]:hover {
    cursor: not-allowed;
}


