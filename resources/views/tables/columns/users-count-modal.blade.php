@php
use Filament\Facades\Filament;
    $roleName = ucfirst($getRecord()->name); // assuming name is the role name
    $panelId = Filament::getCurrentPanel()?->getId(); // 'admin' or 'pc'

    if (!function_exists('profileUrlByRole')) {
        function profileUrlByRole($user, $panelId = 'admin') {
            $role = optional($user->roles->first())->name ?? 'User';

            if ($panelId === 'pc') {
                // Logic for PC panel
                return match($role) {
                    'Pharmaceutical Company' => url('/user-manages', $user->id),
                    'Clinic' => route('filament.pc.resources.clinics.view', $user->id),
                    default => url('/user-manages', $user->id),
                };
            }

            // Logic for Admin panel
            return match($role) {
                'Super Admin' => route('filament.admin.resources.users.view', $user->id),
                'Pharmaceutical Company' => url('/ps-user-manages', $user->id),
                'Clinic' => url('/clinics', $user->id),
                default =>url('/user-manages', $user->id),
            };
        }
    }

    $users = $getRecord()->users->map(function ($u) use ($panelId) {
        return [
            'id' => $u->id,
            'name' => $u->name,
            'email' => $u->email,
            'role' => optional($u->roles->first())->name ?? 'User',
            'profileUrl' => profileUrlByRole($u, $panelId),
        ];
    });

@endphp

<div
    x-data="{
        open: false,
        tooltip: false,
        search: '',
        page: 1,
        perPage: 10,
        roleName: '{{ $roleName }}',
        users: @js($users),
        get filteredUsers() {
            if (this.search === '') return this.users;
            return this.users.filter(u =>
                (u.name + ' ' + u.email).toLowerCase().includes(this.search.toLowerCase())
            );
        },
        get totalPages() {
            return Math.ceil(this.filteredUsers.length / this.perPage);
        },
        paginatedUsers() {
            const start = (this.page - 1) * this.perPage;
            return this.filteredUsers.slice(start, start + this.perPage);
        },
        nextPage() {
            if (this.page < this.totalPages) this.page++;
        },
        prevPage() {
            if (this.page > 1) this.page--;
        },
        toggleModal(state) {
            this.open = state;
            if (state) {
                document.body.classList.add('overflow-hidden');
            } else {
                document.body.classList.remove('overflow-hidden');
            }
        }
    }"
    wire:ignore
>

    <!-- Trigger -->
    <div
        @mouseenter="tooltip = true"
        @mouseleave="tooltip = false"
         @click.prevent.stop="toggleModal(true)"
        class="fi-table-cell-users-count relative w-full h-full cursor-pointer text-primary-600 hover:underline py-4">
        {{ $getRecord()->users_count }}
        <!-- Tooltip -->
        <div
            x-show="tooltip"
            x-cloak
            class="absolute -top-10 left-1/2 transform -translate-x-1/2 px-3 py-1 rounded-lg text-sm font-semibold  whitespace-nowrap  select-none"
           style="z-index: 9999;background-color: #1f2937;box-shadow:0 4px 6px -1px;rgb(0 0 0 / 0.1);color:#fff;padding:12px;"
          >
            View Users
        </div>
    </div>

    <!-- Modal -->
    <div x-show="open" @click.prevent.stop  x-cloak class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
        <div
            class="bg-white w-full max-w-2xl max-h-[80vh] overflow-y-auto rounded-lg shadow-lg p-6"
            @click.prevent.stop
        >
            <!-- Header -->
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-lg font-semibold" x-text="roleName.replace(/^\d+-\s*/, '') "></h2>
                <button @click.prevent.stop="toggleModal(false)" class="text-gray-700 text-xl font-bold">&times;</button>
            </div>

            <!-- Search -->
            <input
                type="text"
                x-model="search"
                placeholder="Search users..."
                class="w-full mb-4 border p-2 rounded"
            />

            <!-- Table -->
            <table class="w-full text-left border border-gray-200 rounded">
                <thead class="bg-gray-100">
                    <tr>
                         
                        <th class="p-2">Name</th>
                        <th class="p-2">Email</th>
                        
                    </tr>
                </thead>
                <tbody>
                    <template x-for="user in paginatedUsers()" :key="user.id">
                        <tr class="border-t hover:bg-gray-100 cursor-pointer"  @click="window.location.href = user.profileUrl">
                           
                            <td class="p-2" x-text="user.name"></td>
                            <td class="p-2" x-text="user.email"></td>
                          
                        </tr>
                    </template>
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="mt-4 flex justify-center items-center space-x-2">
                <button
                    @click="prevPage"
                    class="px-3 py-1 rounded bg-gray-200 hover:bg-gray-300 text-sm"
                    :disabled="page === 1"
                >
                    Prev
                </button>

                <template x-for="p in totalPages" :key="p">
                    <button
                        @click="page = p"
                        class="px-3 py-1 rounded text-sm"
                        :class="page === p ? 'bg-primary-500 text-white' : 'bg-gray-100 hover:bg-gray-200'"
                        x-text="p"
                    ></button>
                </template>

                <button
                    @click="nextPage"
                    class="px-3 py-1 rounded bg-gray-200 hover:bg-gray-300 text-sm"
                    :disabled="page === totalPages"
                >
                    Next
                </button>
            </div>
        </div>
    </div>
</div>
<style>
    body.overflow-hidden {
        overflow: hidden;
    }
</style>
