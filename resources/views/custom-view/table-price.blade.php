@php
$user = $getRecord();
// dd($user);
$product = $getLivewire()->record;
$relation = \App\Models\ProductRelation::where('product_id', $product->id)->where('user_id', $user->id)->first();
$price = \App\Models\ProductRelationPrice::where('product_relation_id', $relation->id)->first();
$stock = \App\Models\ProductRelationStock::where('product_relation_id', $relation->id)->first();
$userId = getUser($user)->id;
$stockNumber = 0;
$stockType = "By Stock";
$stockStatus = "In Stock";
if($stock?->is_batch_wise_stock == true){

$batches = \App\Models\ProductBatch::where('product_id', $product->id)->where('user_id', $userId)->where('products_relation_id', $relation->id)->get();
$stockNumber = $batches->sum('available_stock');
$stockStatus = $stockNumber == 0 ? 'Out of Stock' : 'In Stock';
$stockType = "By Batch";
} else {
$stockNumber = $stock?->stock ?? 0;
$stockStatus = $stockNumber == 0 ? 'Out of Stock' : 'In Stock';
$stockType = "By Stock";
}
$priceType = $product->productDataForPc($user->id)?->price_type;

@endphp

<div x-data="{ open: false }" class="min-w-full px-1 py-1 mt-2 mb-2 overflow-hidden border-2 rounded-lg">
    <div @click="open = !open" class="flex items-center justify-between px-4 py-2 rounded-lg cursor-pointer">
        <h2 class="text-sm text-gray-700">
            Price & Stock
        </h2>
        <svg x-bind:class="{ 'rotate-180': open }"
            class="w-5 h-5 text-gray-500 transition-transform duration-200 transform" fill="none" stroke="currentColor"
            viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
    </div>



    <div x-show="open" x-transition>
        <div class="min-w-full px-4 py-2 mt-4 mb-2 overflow-hidden text-sm font-bold rounded-lg">
            Price Type : {{ \Str::title($priceType) ?? '' }}
        </div>
        @if($priceType == 'tier')
        @include('custom-view.tables.tier-price-table', ['price' => $price])
        @endif

        @if($priceType == 'bonus')
        @include('custom-view.tables.bonus-price-table', ['price' => $price])
        @endif

        @if($priceType == 'fixed')
        <div class="mt-4 overflow-x-auto border border-gray-200 rounded-xl">
            <table class="min-w-full text-sm text-gray-700 divide-y divide-gray-200">

                <thead class="text-xs font-semibold tracking-wider text-left text-gray-600 uppercase bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 font-medium text-left text-gray-500">Zone</th>
                        <th class="px-4 py-2 font-medium text-left text-gray-500">Price</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <tr>
                        <td class="px-4 py-2">West Zone</td>
                        <td class="px-4 py-2">{{ $price?->west_zone_price }}</td>
                    </tr>
                    <tr>
                        <td class="px-4 py-2">East Zone</td>
                        <td class="px-4 py-2">{{ $price?->east_zone_price }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        @endif
        <div class="flex items-center justify-between gap-4 px-4 py-2 mt-4 border-2 rounded-lg">
            <p class="text-sm font-bold text-gray-500">Stock: {{ $stockNumber }}</p>
            <p class="text-sm font-bold text-gray-500">Stock Type: {{ $stockType }}</p>
            <p class="text-sm font-bold text-gray-500">Stock Status: {{ $stockStatus }}</p>
        </div>
        @if($stockType == 'By Batch')
        <div class="mt-4">
            @include('custom-view.batch-repeatable-entry', ['batches' => $batches])
        </div>
        @endif

    </div>

</div>