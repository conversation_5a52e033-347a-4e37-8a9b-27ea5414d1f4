<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body>
    <table cellpadding="0" cellspacing="0"
        style="max-width: 680px; margin: 0 auto; width: 100%; font-family: 'Golos Text', system-ui, sans-serif">
        <tr>
            <td colspan="2" style="height: 32px;"></td>
        </tr>
        <tr>
            <td>
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td><a href=""><img src="{{  url('images/logo.svg') }}"
                                    alt="DPharma" style="width: 180px;"></a>
                        </td>
                        <td
                            style="text-align: right; font-size: 11px; font-weight: 500; color: #212121; line-height: 1.3;">
                            <span style="display: inline-block; width: 100%; margin-bottom: 2px;">Purchase Order:
                                {{$poNumber}}</span>
                            <span style="display: inline-block; width: 100%;">Issue Date: {{ $order->created_at->format('d M Y') }}</span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 32px;"></td>
        </tr>
        <tr>
            <td>
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td
                            style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; line-height: 1.8;">
                            Buyer Information (From)</td>
                    </tr>
                    <tr>
                        <td
                            style="padding: 8px; color: #0B1215; font-size: 10px; font-size: 10px; color: #212121; font-weight: 400; line-height: 1.3;">
                            <p style="margin-top: 0;">{{$facilityClinicData->clinic_name}}</p>
                            <p style="margin-top: 0;">{{$buyAdd}}</p>
                            <p style="margin-top: 0;">Tel: +{{$facilityClinicData->mobile_code}}-{{$facilityClinicData->mobile_number}} | Email: {{$facility->email}}</p>
                            <p style="margin-top: 0;">Company Reg. No.: {{$facilityClinicData->company_number}}</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td
                            style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; line-height: 1.8;">
                            Seller Information (To)</td>
                    </tr>
                    <tr>
                        <td
                            style="padding: 8px; color: #0B1215; font-size: 10px; font-size: 10px; color: #212121; font-weight: 400; line-height: 1.3;">
                            <p style="margin-top: 0;">{{ pcCompanyName($pcDetails)}}</p>
                            <p style="margin-top: 0;">{{$sellerAdd}}</p>
                            <p style="margin-top: 0;">Tel: +{{$pcDetails->phone_number_code}}-{{$pcDetails->phone_number}} | Email: {{$subOrder->user->email}}</p>
                            <p style="margin-top: 0;">Company Reg. No.: {{$pcDetails->company_registration_number}}</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td
                style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; padding: 8px;">
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td style="line-height: 2; padding-bottom: 4px;">
                            Order Details
                        </td>
                    </tr>
                </table>
                <table cellpadding="0" cellspacing="0"
                    style="width: 100%; border-collapse: collapse; background-color: #fff; border: 1px solid #212121; ">
                    <thead>
                        <tr>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Item No.</th>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Product Name
                            </th>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Quantity</th>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Wholesale Pack</th>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Unit Price(RM)
                            </th>
                            <th
                                style="padding: 8px; color: #212121; font-size: 10px; font-weight: 600; text-align: left; border: 1px solid #212121;">
                                Total
                                Amount(RM)</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ( $subOrder->orderProducts as $orderProduct)
                        @php
                            if ($orderProduct->bonus_final_qty > 0) {
                                $calQty = $orderProduct->quantity + $orderProduct->bonus_final_qty;
                                $finalQty = $calQty . ' ('.$orderProduct->quantity .'+ '.$orderProduct->bonus_final_qty.' Free)';
                            }else{
                                $finalQty = $calQty = $orderProduct->quantity;
                            }
                            $wps = $orderProduct->stock_type == 'wps' &&  $orderProduct->wholesale_pack_size ?
                            trans('api.clinic.cart.wolesale_size') . ($orderProduct->wholesale_pack_size * $calQty). ' ' . \Illuminate\Support\Str::plural($orderProduct->product->container->name) . ' ('. ($orderProduct->wholesale_pack_size  * $orderProduct->product->quantity_per_unit * $calQty) . ' ' . \Illuminate\Support\Str::plural($orderProduct->product->foam->name).')' :
                                '-';
                        @endphp
            

                            <tr>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                        {{ $orderProduct->id }}</td>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                    {{ $orderProduct->product->name }}</td>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                    {{$finalQty}} </td>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                    {{$wps}} </td>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                    {{$orderProduct->price_per_qty}}
                                </td>
                                <td
                                    style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 400;">
                                    {{$orderProduct->total_price}}
                                </td>
                            </tr>
                        @endforeach
                          <tr>
                            <td colspan="5"
                                style="padding: 8px; color: #0B1215; font-size: 12px; border: 1px solid #212121; font-weight: 600; text-align: right;">
                                Total: </td>
                            <td
                                style="padding: 8px; color: #0B1215; font-size: 12px; border: 1px solid #212121; font-weight: 700;">
                                RM {{ $supAmt }}</td>
                        </tr>
                        <tr>
                            <td colspan="6"
                                style="padding: 8px; color: #0B1215; font-size: 10px; border: 1px solid #212121; font-weight: 500; text-align: left; line-height: 1.4;">
                                <p style="margin: 0;">Amount in Words:</p>
                                <p style="margin: 0;">{{$amountInWords}}</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>

        <tr>
            <td style="height: 16px;"></td>
        </tr>
        <tr>
            <td>
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td style="width: 48%; vertical-align: top;">
                            <table cellpadding="0" cellspacing="0" style="width: 100%;">
                                <tr>

                                    <td
                                        style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; line-height: 1.8;">
                                        Delivery Details</td>
                                </tr>
                                <tr>
                                    <td
                                        style="padding: 8px; color: #0B1215; font-size: 10px; font-size: 10px; color: #212121; font-weight: 400; line-height: 1.3;">
                                        <p style="margin-top: 0;">Address: {{$deliveryAdd}}</p>
                                        @if($expectedDeliveryDate)
                                            <p style="margin-top: 0;">Expected Delivery Date: {{$expectedDeliveryDate}}
                                            </p>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td style="width: 2%; vertical-align: top;"></td>
                        <td style="width: 48%; vertical-align: top;">
                            <table cellpadding="0" cellspacing="0" style="width: 100%;">
                                <tr>

                                    <td
                                        style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; line-height: 1.8;">
                                        Payment Terms</td>
                                </tr>
                                <tr>
                                    <td
                                        style="padding: 8px; color: #0B1215; font-size: 10px; font-size: 10px; color: #212121; font-weight: 400; line-height: 1.3;">
                                        <p style="margin-top: 0;">Payment Method: {{$payment_method}}</p>
                                        @if($payment_method != 'Business Credit')
                                            <p style="margin-top: 0;">Payment Status: Received</p>
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                </table>
            </td>
        </tr>
        <tr>
            <td>
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td
                            style="background-color: #E6E6E6; padding: 4px 8px 4px 8px; color: #0B1215; font-weight: 600; font-size: 12px; line-height: 1.8;">
                            Notes:</td>
                    </tr>
                    <tr>
                        <td
                            style="padding: 8px; color: #0B1215; font-size: 10px; font-size: 10px; color: #212121; font-weight: 400; line-height: 1.3;">
                            <ul style="margin-top: 0; list-style: disc; padding-left: 16px;">
                                <!-- <li style="margin-top: 0; margin-bottom: 4px;">Please acknowledge this PO within 24
                                    hours.
                                </li>
                                <li style="margin-top: 0; margin-bottom: 4px;">Include batch numbers and expiry dates on
                                    delivery note.</li> -->
                                <li style="margin-top: 0; margin-bottom: 4px;">All deliveries must comply with Malaysian
                                    Ministry of Health
                                    guidelines.
                                </li>
                            </ul>

                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 60px;"></td>
        </tr>
        <tr>
            <td style="text-align: right;">
                <table cellpadding="0" cellspacing="0" style="width: 100%;">
                    <tr>
                        <td
                            style="text-align: right; font-size: 10px; font-weight: 400; color: #212121; line-height: 1.3; padding-right: 8px;">
                            Authorized By:</td>
                        <td style="width: 170px; text-align: center;">
                            <img src="{{$facilitySignature}}" alt="Signature"
                                width="130" style="margin: 0 auto;">
                            <p
                                style="font-size:10px; margin:0; color: #212121; border-top: 1px solid #D1D5DB; padding-top: 4px; margin-top: 8px; text-align: left;  ">
                                (Doctor
                                in charge, {{$facilityClinicData->dc_name}})</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 32px;"></td>
        </tr>
    </table>
</body>

</html>