<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        body {
            margin: 0;
            padding: 0;
        }
    </style>
</head>

<body>
    <table cellpadding="0" cellspacing="0"
        style="max-width: 680px; margin: 0 auto; width: 100%; font-family: 'Golos Text', system-ui, sans-serif">
        <tr>
            <td colspan="2" style="height: 32px;"></td>
        </tr> 
        <tr>
            <td>
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <td>
                            <a href="">
                                <img src="{{  url('images/logo.svg') }}"
                                    alt="DPharma" width="180">
                            </a>
                        </td>
                        <td align="right">
                            <span style="color: #212121; font-size: 11px; font-weight: 500; display: block; padding-bottom: 2px;"> 
                                Order Number: #{{$order->order_number}}
                            </span>
                            <span style="color: #212121; font-size: 11px; font-weight: 500;"> 
                               {{ $order->created_at->format('d M Y') }}
                            </span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 32px;"></td>
        </tr>
        <tr>
            <td style="color:#FF7B00; font-size: 12px; font-weight: 600; text-align: center; padding-bottom: 8px;">Final Details for Order #{{$order->order_number}}</td>
        </tr>
        <tr>
            <td style="font-size: 10px; color: #212121; border-bottom: 1px solid #212121; padding-bottom: 16px;">
                <p style="margin: 0 0 4px 0;"><strong style="font-weight: 600;">Order Placed:</strong> {{ $order->created_at->format('d M Y') }}</p>
                <p style="margin: 0 0 4px 0;"><strong style="font-weight: 600;">order number:</strong> {{$order->order_number}}</p>
                <p style="margin: 0 0 4px 0;"><strong style="font-weight: 600;">Order Total:</strong> RM {{ $order->amount }}</p>
            </td>
        </tr>
        <tr>
            <td style="height: 16px;"></td>
        </tr>
        
        <tr>
            <td style="height: 16px;"></td>
        </tr>
        <tr>
            <td style="color: #212121; font-size: 12px; line-height: 17px; padding-bottom: 12px;">
                Items Ordered
            </td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 10%; text-align: center;">Item No.</th>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 40%;">Product Name</th>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 10%; text-align: center;">Quantity</th>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 30%; text-align: center;"> Wholesale Pack</th>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 10%; text-align: center;">Unit Price</th>
                        <th style="text-align: left; font-weight: 600; font-size: 10px; color: #212121; padding-bottom: 16px; width: 10%; text-align: right;">Total</th>
                    </tr>
                        @foreach ( $order->orderProducts as $orderProduct)

                        @php
                            if ($orderProduct->bonus_final_qty > 0) {
                                $calQty = $orderProduct->quantity + $orderProduct->bonus_final_qty;
                                $finalQty = $calQty . ' ('.$orderProduct->quantity .'+ '.$orderProduct->bonus_final_qty.' Free)';
                            }else{
                                $finalQty = $calQty = $orderProduct->quantity;
                            }
                            $wps = $orderProduct->stock_type == 'wps' && $orderProduct->wholesale_pack_size ?
                            trans('api.clinic.cart.wolesale_size') . ($orderProduct->wholesale_pack_size * $calQty). ' ' . \Illuminate\Support\Str::plural($orderProduct->product->container->name) . ' ('. ($orderProduct->wholesale_pack_size  * $orderProduct->product->quantity_per_unit * $calQty) . ' ' . \Illuminate\Support\Str::plural($orderProduct->product->foam->name).')' :
                                '-';
                        @endphp

							<tr>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; text-align: center; width: 10%;">{{ $orderProduct->id }}</td>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; width: 40%;">
									{{ $orderProduct->product->name }}
									<p style="margin: 4px 0 0px 0; font-size: 8px;">Sold By: {{pcCompanyName($orderProduct->subOrder->pcDetail)}}</p>
								</td>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; text-align: center; width: 10%;">{{$finalQty}}</td>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; text-align: center; width: 30%;">{{$wps}}</td>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; text-align: center; width: 10%;">{{$orderProduct->price_per_qty}}</td>
								<td style="font-size:10px; color: #212121; padding-bottom: 16px; text-align: right; width: 10%;">{{$orderProduct->total_price}}</td>
							</tr>
						@endforeach
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 16px;"></td>
        </tr>
        <tr>
            <td>
                <h2 style="color: #212121; font-weight: 600; font-size: 12px; padding-bottom: 8px;">Delivery Address</h2>
                <p style="color: #212121; font-size: 10px; margin-top: 0;">{{$order->user->name}}</p>
                <p style="color: #212121; font-size: 10px; margin-top: 0;">{{$order->shipping_address_1}} , {{$order->shipping_address_2}} , {{$order->shippingCity->name}}, {{$order->shippingCountry->name}} - {{$order->shipping_postal_code}}</p>
                <p style="color: #212121; font-size: 10px; margin-top: 0;">Tel: +{{$order->user->clinicData->mobile_code}} {{$order->user->clinicData->mobile_number}} | Email: {{$order->user->email}}</p>
                <p style="color: #212121; font-size: 10px; margin-top: 0;">Company Reg. No.: {{$order->user->clinicData->company_number}}</p>
            </td>
        </tr>
        <tr>
            <td style="height: 24px;"></td>
        </tr>
       
        <tr>
            <td style="height: 16px;"></td>
        </tr>
        <tr>
            <td style="text-align: center;">
                <h2 style="color: #0B1215; font-weight: 600; font-size: 12px;">Payment Information</h2>
            </td>
        </tr>
        <tr>
            <td>
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <td style="width: 65%;">
                            <table width="100%" cellpadding="0" cellspacing="0">
                                
                                <tr>
                                    <td style="height: 16px;"></td>
                                </tr>
                                <tr>
                                    <td>
                                        <h5 style="font-size: 10px; color: #212121; font-weight: 600; margin: 0 0 8px 0;">Billing Address</h5>
                                        <p style="color: #212121; font-size: 10px; margin-top: 0;">{{$order->user->name}}</p>
                                        <p style="color: #212121; font-size: 10px; margin-top: 0;">{{$order->billing_address_1}} , {{$order->billing_address_2}} , {{$order->billingCity->name}}, {{$order->billingCountry->name}} - {{$order->billing_postal_code}}</p>
                                        <p style="color: #212121; font-size: 10px; margin-top: 0;">Tel: +{{$order->user->clinicData->mobile_code}} {{$order->user->clinicData->mobile_number}} | Email: {{$order->user->email}}</p>
                                        <p style="color: #212121; font-size: 10px; margin-top: 0;">Company Reg. No.: {{$order->user->clinicData->company_number}}</p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                        <td width="35%">
                            <table width="100%" cellpadding="0" cellspacing="0">
                                <tr>
                                    <td style="font-size: 10px; color: #212121;"><span>Item(s) Subtotal:</span></td>
                                    <td style="text-align: right; font-size: 10px; color: #212121;">RM {{$supTotalAmt}}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 10px; color: #212121;"><span>Shipping:</span></td>
                                    <td style="text-align: right; font-size: 10px; color: #212121;"> RM {{$shippingAmt}}</td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="text-align: right; line-height: 30px;">----</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 10px; color: #212121;"><span>Total:</span></td>
                                    <td style="text-align: right; font-size: 10px; color: #212121;">RM {{$supTotalAmt + $shippingAmt}}</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 10px; color: #212121;"><span>Promotion Applied:</span></td>
                                    <td style="text-align: right; font-size: 10px; color: #212121;">RM {{$supPointApplied}}</td>
                                </tr>
                                <tr>
                                    <td colspan="2" style="text-align: right; line-height: 30px;">----</td>
                                </tr>
                                <tr>
                                    <td style="font-size: 10px; color: #212121; font-weight: 600;"><span>Grand Total:</span></td>
                                    <td style="text-align: right; font-size: 10px; color: #212121; font-weight: 600;">RM {{ $order->amount}}</td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="height: 16px;"></td>
        </tr>
    </table>
</body>

</html>