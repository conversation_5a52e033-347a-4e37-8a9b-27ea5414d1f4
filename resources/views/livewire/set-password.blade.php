<div>
    @vite('resources/css/app.css')
    @filamentStyles()
    <x-filament-panels::page>
        <div class="w-full">
            <img src="{{ asset('images/logo.svg') }}" style="height: 38px;" class="fi-logo mx-auto mb-5" alt="DPharma Logo">
            <h2 class="mb-6 text-2xl font-bold text-gray-800">Create Password</h2>

            {{-- Render the Form --}}
            <div class="space-y-6">
                {{ $this->form }}
            </div>

            {{-- Render the Action Button --}}
            <div class="mt-6 flex justify-between items-center">
                <div class="inline-block text-white rounded-lg bg-blue-950 hover:bg-blue-950">
                    {{$this->logoutAction}}
                </div>
                <div class="inline-block text-white rounded-lg bg-blue-950 hover:bg-blue-950">
                    {{$this->createAction}}
                </div>
            </div>
        </div>
        <div 
        x-data
        x-on:redirect-after-notification.window="setTimeout(() => window.location.href = $event.detail.redirectUrl, 1500)"
    ></div>
    </x-filament-panels::page>

    <style>
        .fi-btn-color-primary {
            @apply bg-primary-600 text-white hover:bg-primary-500 focus:ring-2 focus:ring-primary-500/50 !important;
        }
        .bg-custom-600:hover{
            background-color: #22347100;

        }
    </style>
    @filamentScripts()
</div>
