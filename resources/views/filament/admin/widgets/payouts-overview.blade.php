<div style="--col-span-default: 1 / -1;" class="col-[--col-span-default] fi-wi-widget filament-widgets-chart-widget filament-apex-charts-widget">
    <!-- Filter Section -->
    <div class="flex flex-wrap justify-end items-center gap-3 mb-4
                sm:flex-nowrap sm:gap-4
                ">
        <form wire:submit.prevent="applyFilter"
              class="flex flex-col w-full gap-3 items-stretch
                     sm:flex-row sm:items-center sm:space-x-4 sm:gap-0 sm:w-auto">
            <!-- Filter Option Dropdown -->
            <div class="flex items-center w-full sm:w-auto">
                <select
                    id="filterOption"
                    wire:model="filterOption"
                    wire:change="onFilterOptionChange"
                    class="mt-1 block w-full py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm sm:w-40">
                    <option value="today">Today</option>
                    <option value="this_week">This Week</option>
                    <option value="this_month">This Month</option>
                    <option value="this_year">This Year</option>
                    <option value="custom_dates">Custom Dates</option>
                </select>
            </div>

            <!-- Start Date and End Date Filters (Visible only for Custom Date) -->
            @if($filterOption == 'custom_dates')
            <div
                x-data="{ startDate: @entangle('startDate'), endDate: @entangle('endDate'), today: '{{ now()->toDateString() }}' }"
                class="flex flex-col gap-2 w-full sm:flex-row sm:items-center sm:space-x-4 sm:gap-0 sm:w-auto">
                <!-- Start Date -->
                <input
                    type="date"
                    id="startDate"
                    x-model="startDate"
                    :max="endDate || today"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm sm:w-40">

                <!-- End Date -->
                <input
                    type="date"
                    id="endDate"
                    x-model="endDate"
                    :min="startDate"
                    :max="today"
                    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm sm:w-40">

                <!-- Fetch Button -->
                <button
                    type="button"
                    @click="$wire.set('startDate', startDate); $wire.set('endDate', endDate); $wire.applyFilter()"
                    class="w-full px-4 py-2 bg-primary-500 text-white rounded-md sm:w-24">
                    Apply
                </button>
            </div>
            @endif
        </form>
    </div>

    <!-- Stats Section -->
    <div class="mb-4">

        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            @foreach ($stats as $stat)
            <div class="fi-wi-stats-overview-stat relative rounded-xl p-6 shadow-sm ring-1 ring-gray-950/5 dark:ring-white/10  bg-white dark:bg-gray-900" style="background-color: {{ $stat['bg'] ?? '' }};">
                <div class="flex gap-y-2 gap-x-3">
                    <div class="dark:bg-primary-950 p-1 rounded-full w-10 h-10 flex justify-center items-center" style="background: {{ $stat['color'] ?? '' }}">
                        @php
                        // Define the icon dynamically based on the stat
                        $icon = 'heroicon-o-' . $stat['icon'];
                        @endphp

                        <!-- Use dynamic component rendering -->
                        <x-dynamic-component :component="$icon" class="w-6 h-6" style="color: {{ $stat['iconColor'] }}" />
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-x-2">
                            <span class="fi-wi-stats-overview-stat-label text-lg font-semibold text-gray-950 dark:text-white">
                                {{$stat['label']}}
                            </span>
                        </div>
                        <div class="fi-wi-stats-overview-stat-label font-medium text-gray-500 text-sm dark:text-white">
                            {{ $stat['query'] ?? 0 }}
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

    </div>
</div>