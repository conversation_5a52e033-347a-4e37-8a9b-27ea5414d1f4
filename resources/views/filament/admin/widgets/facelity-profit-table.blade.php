<x-filament-widgets::widget class="w-full">
    <div x-data="{open:false}">

        <div class="flex flex-col md:flex-row md:items-end gap-4 mb-6">

            <!-- Year Select -->
            <div class="flex flex-col w-full md:w-1/4"
                x-data="{
                    years: [],
                    selectedYear: '',
                    init() {
                        let currentYear = new Date().getFullYear();
                        let startYear = currentYear - 10;
                        for (let year = currentYear; year >= startYear; year--) {
                            this.years.push(year);
                        }
                        this.selectedYear = currentYear;
                    }
                }">
                <label for="year" class="mb-1 text-sm font-medium text-gray-700">Select Year</label>
                <select
                    id="year"
                    name="year"
                    x-model="selectedYear"
                    wire:model.defer="year"
                    class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500">
                    <option value="">-- Please Select a Year --</option>
                    <template x-for="year in years" :key="year">
                        <option :value="year" x-text="year"></option>
                    </template>
                </select>
            </div>

            <!-- Amount Input -->
            <div class="flex flex-col w-full md:w-1/4">
                <label for="amount" class="mb-1 text-sm font-medium text-gray-700">Amount</label>
                <input
                    type="text"
                    id="amount"
                    wire:model.defer="amount"
                    placeholder="Enter Amount"
                    class="border-gray-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500" />
            </div>

            <!-- Check Calculations Button -->
            <div class="flex flex-col">
                <button
                    type="button"
                    wire:click="checkCalculation"
                    class="bg-primary-600  text-white px-4 py-2 rounded-md whitespace-nowrap cursor-pointer">
                    Check Calculations
                </button>
            </div>

            <!-- Submit Button (Conditional) -->
            @if($this->submitButton)
            <div class="flex flex-col">
                <button
                    type="button"
                    @click="open = true"
                    class="bg-primary-600 hover:bg-primary-700 text-white font-semibold px-6 py-2 rounded-md shadow cursor-pointer">
                    Submit
                </button>
            </div>
            @endif

        </div>

        <!-- Modal -->
        <div
            x-show="open"
            x-cloak
            x-transition
            class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center"
            x-init="
        window.addEventListener('close-profit-modal', () => open = false);
    ">
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                <h2 class="text-lg font-semibold mb-2">Submit Profit Share</h2>
                <p class="text-gray-600 mb-4">Are you sure you want to submit this Profit Share?</p>
                <div class="flex justify-end gap-2">
                    <button 
                        @click="
                            open = false;
                            otpOpen = false;
                        " 
                        class="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 cursor-pointer">
                        Cancel
                    </button>
                    <button
                        x-data="{ loading: false }"
                        @click="
                            loading = true;
                            $wire.submitForVerification().then(() => loading = false).catch(() => loading = false);
                        "
                        :disabled="loading"
                        class="px-4 py-2 rounded bg-primary-500 hover:bg-primary-400 text-white flex items-center justify-center cursor-pointer"
                    >
                        <template x-if="loading">
                            <svg class="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                            </svg>
                        </template>
                        <span x-text="loading ? 'Submitting...' : 'Yes, Submit'">Yes, Submit</span>
                    </button>
                </div>
            </div>
        </div>
        <div class="profit-share">
            @if($this->tableHidden)
            {{ $this->table }}
            @endif
            @if($this->tableHidden)

            <!-- <div class="profit-share-total mt-4 py-4 bg-gray-100 rounded-xl text-sm text-gray-700 space-y-2">
                <div class="flex">
                    <span class="font-semibold px-3">Total :</span>
                    <span class="px-3"> {{ $this->getPurchaseTotals()['total'] }}</span>
                    <span class="px-3">{{ $this->getPurchaseTotals()['total_contribution'] }}</span>
                    <span class="px-3">{{ $this->getPurchaseTotals()['total_profit_amount'] }}</span>
                    <span class="px-3">{{ $this->getPurchaseTotals()['total_profit_points'] }}</span>
                </div>

            </div> -->
            @endif
        </div>
    </div>

    <div
        x-data="{ otpOpen: @entangle('open'), loading: false }"
        x-init="
        otpOpen = {{ $this->open ? 'true' : 'false' }};
        window.addEventListener('otp-verified', () => otpOpen = false);
    ">
        <div
            x-show="otpOpen"
            x-cloak
            x-transition
            class="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center">
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
                <h2 class="text-lg font-semibold mb-2">OTP Verification</h2>
                <p class="text-gray-600 mb-4">Enter the OTP sent to your registered contact email.</p>
                <p class="text-gray-600 mb-4">{{ $this->email }}</p>

                <!-- OTP Form -->
                <div class="mb-4">
                    <form wire:submit.prevent="submit" class="space-y-4" x-data>
                        {{ $this->form }}
                        <x-filament::button
                            type="submit"
                            color="primary"
                            class="cursor-pointer flex items-center"
                            wire:loading.attr="disabled"
                            wire:target="submit"
                            x-ref="submitBtn"
                             @submit.prevent="loading = true"
                        >
                            <template x-if="loading">
                                <svg class="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path>
                                </svg>
                            </template>
                            <span>
                                <span x-show="!loading" wire:loading.remove wire:target="submit">Submit</span>
                                <span x-show="loading" wire:loading wire:target="submit">Submitting...</span>
                            </span>
                        </x-filament::button>
                        <a 
                            @click="
                                otpOpen = false; 
                                $wire.cancelVerification();
                                // Reset all form fields inside the form
                                let form = $el.closest('form');
                                if(form) {
                                    form.querySelectorAll('input').forEach(input => input.value = '');
                                }
                                // Dispatch event to close the first modal
                                window.dispatchEvent(new Event('close-profit-modal'));
                            " 
                            class="px-4 py-2 rounded bg-gray-300 hover:bg-gray-400 cursor-pointer"
                        >
                            Cancel
                        </a>
                    </form>
                    <div id="timer" class="flex items-center gap-2 px-4 py-2 text-gray-400 rounded">
                        <span>Didn't get code?</span>
                        <button
                            id="resendOTP"
                            wire:click="resend"
                            type="button"
                            class="text-gray-400 bg-transparent border-none resend-btn"

                            wire:target="resend">
                            Resend OTP
                        </button>
                        <div id="parentTimer" class="ml-2 text-sm text-gray-500" style="display: inline-flex !important;">
                            <span>in&nbsp;</span>
                            <span id="timer-minutes" class="font-medium">00</span>:<span id="timer-seconds" class="font-medium">00</span>
                        </div>
                    </div>
                    <div style="display: none">
                        <input type="text" id="timer_config" value="{{ config('filament-email-2fa.expiry_time_by_mins', 5) }}">
                    </div>

                </div>
            </div>
        </div>
    </div>
</x-filament-widgets::widget>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Example: Timer logic
        let resendBtn = document.getElementById('resendOTP');
        let timerMinutes = document.getElementById('timer-minutes');
        let timerSeconds = document.getElementById('timer-seconds');
        let parentTimer = document.getElementById('parentTimer');
        let expiryTimeInMinutes = parseInt(document.getElementById('timer_config')?.value || 5);

        function startCountdown(durationInSeconds) {
            let timer = durationInSeconds;
            resendBtn.disabled = true;

            const interval = setInterval(() => {
                const minutes = Math.floor(timer / 60);
                const seconds = timer % 60;

                timerMinutes.textContent = String(minutes).padStart(2, '0');
                timerSeconds.textContent = String(seconds).padStart(2, '0');

                if (--timer < 0) {
                    clearInterval(interval);
                    resendBtn.disabled = false;
                    parentTimer.style.display = 'none';
                }
            }, 1000);
        }

        // Auto-start countdown on page load
        if (resendBtn && timerMinutes && timerSeconds && parentTimer) {
            parentTimer.style.display = 'inline-flex';
            startCountdown(expiryTimeInMinutes * 60);
        }

        // Optional: restart timer on Resend
        resendBtn?.addEventListener('click', () => {
            parentTimer.style.display = 'inline-flex';
            startCountdown(expiryTimeInMinutes * 60);
        });
    });
    
</script>