<div style="--col-span-default: 1 / -1;" class="col-[--col-span-default] fi-wi-widget filament-widgets-chart-widget filament-apex-charts-widget">
    <!-- Filter Section -->
    <div class="flex justify-end items-center gap-3 mb-4">
        <!-- <div class="flex items-center">
            <button
                type="button"
                wire:click="download"
                class="bg-primary-500 text-white rounded-md h-8 w-8 flex justify-center items-center">
                <svg viewBox="0 0 24 24" fill="currentColor" class="w-4 h-4">
                    <path fillRule="evenodd" d="M12 2.25a.75.75 0 0 1 .75.75v11.69l3.22-3.22a.75.75 0 1 1 1.06 1.06l-4.5 4.5a.75.75 0 0 1-1.06 0l-4.5-4.5a.75.75 0 1 1 1.06-1.06l3.22 3.22V3a.75.75 0 0 1 .75-.75Zm-9 13.5a.75.75 0 0 1 .75.75v2.25a1.5 1.5 0 0 0 1.5 1.5h13.5a1.5 1.5 0 0 0 1.5-1.5V16.5a.75.75 0 0 1 1.5 0v2.25a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3V16.5a.75.75 0 0 1 .75-.75Z" clipRule="evenodd" />
                </svg>

            </button>
        </div> -->
        <form wire:submit.prevent="applyFilter" class="flex space-x-4">
            <!-- Filter Option Dropdown -->
            <div class="flex items-center">
                <select
                    id="filterOption"
                    wire:model="filterOption"
                    wire:change="onFilterOptionChange"
                    class="mt-1 block w-40 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    <option value="today">Today</option>
                    <option value="this_week">This Week</option>
                    <option value="this_month">This Month</option>
                    <option value="this_year">This Year</option>
                    <option value="custom_dates">Custom Dates</option>
                </select>
            </div>

            <!-- Start Date and End Date Filters (Visible only for Custom Date) -->
            @if($filterOption == 'custom_dates')
            <div
                x-data="{ startDate: @entangle('startDate'), endDate: @entangle('endDate'), today: '{{ now()->toDateString() }}' }"
                class="flex items-center space-x-4">
                <!-- Start Date -->
                <input
                    type="date"
                    id="startDate"
                    x-model="startDate"
                    :max="endDate || today"
                    class="block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">

                <!-- End Date -->
                <input
                    type="date"
                    id="endDate"
                    x-model="endDate"
                    :min="startDate"
                    :max="today"
                    class="block w-40 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">

                <!-- Fetch Button -->
                <button
                    type="button"
                    @click="$wire.set('startDate', startDate); $wire.set('endDate', endDate); $wire.applyFilter()"
                    class="w-24 px-4 py-2 bg-primary-500 text-white rounded-md">
                    Apply
                </button>
            </div>
            @endif
        </form>
    </div>

    <!-- Stats Section -->
    <div class="mb-4">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
        @foreach ($stats as $stat)
            @php
            $url = $stat['url'] ?? null;
            $icon = 'heroicon-o-' . $stat['icon'];
            @endphp
            @if ($url)
            <a href="{{ $url }}">
            @endif
            <div class="fi-wi-stats-overview-stat relative rounded-xl p-6 shadow-sm ring-1 ring-gray-950/5 dark:ring-white/10  bg-white dark:bg-gray-900" style="background-color: {{ $stat['bg'] ?? '' }};">
                <div class="flex items-center gap-y-2 gap-x-3">
                    <div class="dark:bg-primary-950 h-10 w-10 flex justify-center items-center p-1 rounded-full" style="background: {{ $stat['color'] ?? '' }}">
                        @php
                        // Define the icon dynamically based on the stat
                        $icon = 'heroicon-o-' . $stat['icon'];
                        @endphp

                        <!-- Use dynamic component rendering -->
                        <x-dynamic-component :component="$icon" class="w-6 h-6" style="color: {{ $stat['iconColor'] }}" />
                    </div>
                    <div class="flex-grow">
                        <div class="flex items-center gap-x-2">
                            <span class="fi-wi-stats-overview-stat-label text-lg font-semibold text-gray-950 dark:text-white">
                                {{$stat['label']}}
                            </span>
                        </div>
                        <div class="fi-wi-stats-overview-stat-label font-normal text-sm text-gray-500 dark:text-white">
                            {{ $stat['query'] ?? 0 }}
                        </div>
                    </div>
                </div>
            </div>
            @if ($url)
            </a>
            @endif
            @endforeach
        </div>
    </div>
</div>