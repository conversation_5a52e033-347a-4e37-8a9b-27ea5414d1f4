@php
    $latestImport = \App\Models\ProductImports::with('user')->latest()->first();

    $success = $error = $total = 0;

    if ($latestImport && $latestImport->description) {
        preg_match('/Success\s*Rows:\s*(\d+)/i', $latestImport->description, $successMatch);
        preg_match('/errorRows:\s*(\d+)/i', $latestImport->description, $errorMatch);
        preg_match('/Total:\s*(\d+)/i', $latestImport->description, $totalMatch);

        $success = isset($successMatch[1]) ? (int) $successMatch[1] : 0;
        $error = isset($errorMatch[1]) ? (int) $errorMatch[1] : 0;
        $total = isset($totalMatch[1]) ? (int) $totalMatch[1] : ($success + $error);
    }
@endphp
@php
$timeZone = Auth::user()->timezone ?? config('app.timezone');
@endphp
@if ($latestImport)
    <div class="overflow-x-auto mt-6 border rounded-lg shadow bg-white">
        <table class="min-w-full text-sm text-left text-gray-700">
            <thead class=" text-gray-600 uppercase text-xs border-b">
                <tr>
                    <th class="px-4 py-3">Imported By</th>
                    <th class="px-4 py-3">Date/Time</th>
                    <th class="px-4 py-3">Status</th>
                    <th class="px-4 py-3">Description</th>
                    <th class="px-4 py-3">Log File</th>
                </tr>
            </thead>
            <tbody>
                <tr class="border-t">
                    <td class="px-4 py-3">{{ $latestImport->user->name ?? 'N/A' }}</td>
                    <td class="px-4 py-3">
                        {{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $latestImport->created_at, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A') }}
                    </td>
                    
                    <td class="px-4 py-3 @if($latestImport->status=="Success") text-green-600 @else text-red-600 @endif font-medium">
                        {{ ucfirst($latestImport->status) }}
                    </td>
                    <td class="px-4 py-3 space-x-2">
                        <span class="inline-block bg-green-100 text-green-800 text-xs font-semibold px-2 py-0.5 rounded">
                            Valid: {{ $success }}
                        </span>
                        <span class="inline-block bg-red-100 text-red-800 text-xs font-semibold px-2 py-0.5 rounded">
                            Invalid: {{ $error }}
                        </span>
                        <span class="inline-block bg-gray-200 text-gray-900 text-xs font-semibold px-2 py-0.5 rounded">
                            Total: {{ $total }}
                        </span>
                    </td>
                    <td class="px-4 py-3">
                        @if ($latestImport->audit_file && !in_array($latestImport->status, ['Pending', 'InProgress']))
                            <a href="{{ url('/' . $latestImport->audit_file) }}"
                            target="_blank"
                            class="inline-flex items-center gap-1.5 text-primary-600 dark:text-primary-400 font-bold hover:underline group cursor-pointer">
                                <x-heroicon-o-document-arrow-down class="w-4 h-4 text-primary-600 group-hover:text-primary-700" />
                                Download Log
                            </a>
                        @else
                            <span class="text-gray-400">—</span>
                        @endif



                    </td>
                </tr>
            </tbody>
        </table>
    </div>
@endif
