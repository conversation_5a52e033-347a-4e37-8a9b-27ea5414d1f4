<div>
    @if ($requests->isNotEmpty())
    <div class="overflow-x-auto">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">Nick Name</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">Street 1</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">Street 2</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">City</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">State</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">Country</th>
                    <th class="py-2 px-4 border-b border-r font-semibold text-left">Postal Code</th>
                    <th class="py-2 px-4 border-b font-semibold text-left">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($requests as $request)
                    <tr class="hover:bg-gray-50 transition-colors">
                        <td class="py-2 px-4 border-b border-r">{{ $request->nick_name }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->address_1 }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->address_2 }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->city->name }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->state->name }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->country->name }}</td>
                        <td class="py-2 px-4 border-b border-r">{{ $request->postal_code }}</td>
                        <td class="py-2 px-4 border-b">
                            <div class="flex space-x-2 justify-start">
                                <!-- Approve Button -->
                                @if(auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_shipping address approve'))
                                <button
                                    type="button"
                                    title="Approve"
                                    class="p-2  text-white border rounded  transition-colors border-success-600 bg bg-success-600"  style="padding:0.3rem;"
                                    wire:click="approveRequest({{ $request->id }})"
                                    wire:loading.attr="disabled"
                                    wire:loading.class="opacity-50 cursor-not-allowed">
                                    <!-- Show spinner during loading -->
                                    <span wire:loading.remove wire:target="approveRequest({{ $request->id }})">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </span>
                                    <!-- Show spinner during loading -->
                                    <span wire:loading wire:target="approveRequest({{ $request->id }})">
                                        <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </span>
                                </button>
                                @endif
                                @if(auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_shipping address reject'))
                                <!-- Reject Button with Modal -->
                                <div x-data="{ open: false, reason: '' }">
                                        <button
                                            type="button"
                                            title="Reject"
                                            style="border-color:red;padding:0.3rem;"
                                            class="p-2  text-white border  rounded  border-danger-600 bg bg-danger-600 transition-colors"
                                            @click="open = true">
                                            <!-- Icon for Reject Button -->
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5"  viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                        <!-- Modal -->
                                        <div x-show="open" class="fixed inset-0 z-40"></div>
                                            <div x-show="open" class="fixed inset-0 z-50 flex items-center justify-center  bg-black bg-opacity-50">
                                                <div class="bg-white p-6 rounded-lg shadow-lg w-96">
                                                    <h2 class="text-lg font-semibold mb-4">Confirm Rejection</h2>
                                                    <p class="text-gray-600 mb-6">Are you sure you want to reject this address?</p>
                                                    <div class="flex justify-end space-x-3">
                                                        <button @click="open = false" class="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50">Cancel</button>
                                                        <button @click="open = false; $wire.rejectRequest({{ $request->id }})" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">Confirm</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                </div>
                                @endif
                            </div>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @else
    <div class="bg-white border border-gray-200 rounded-lg p-4 text-center text-gray-500">
            No shipping address requests found.
        </div>
    @endif
</div>