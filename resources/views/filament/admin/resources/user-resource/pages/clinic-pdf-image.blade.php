<div class="container p-6 bg-white hmx-auto max-h-100">
    @php
        // Convert single file path to array for consistent handling
        $filePaths = is_array($filePaths ?? null) ? $filePaths : (isset($filePath) ? [$filePath] : []);

        // Determine title based on certificate type
        $title = match($name) {
            'borang_certificate' => 'Certificate of Registration (Account Holder as per Form B/F)',
            'mmc_certificate' => 'MMC Registration Certificate for Person In-charge',
            'apc_certificate' => 'Current Annual Practicing Certificate (APC)',
            'arc_certificate' => 'Annual Retention Certificate (ARC)',
            'poison_license' => 'Poison A License',
            'other_relevant_documents' => 'Other Relevant Documents',
            default => 'Document'
        };
    @endphp
@if(count($filePaths) > 0)
    <section class="section">
        <h2 class="text-xl font-semibold mb-4">{{ $title }}</h2>

        @if(count($filePaths) > 0)
            @foreach($filePaths as $currentFilePath)
                @php
                    $fileExtension = pathinfo($currentFilePath, PATHINFO_EXTENSION);
                    $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                    $isPdf = strtolower($fileExtension) === 'pdf';
                    $filename = basename($currentFilePath);
                    $currentFilePath = getImage($filename,'/images/clinic/'.$record->clinicData->id);
                @endphp

                <div class="mb-3 p-2 border rounded">
                    @if ($isImage)
                        
                            <div class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span class="text-sm">{{ basename($currentFilePath) }}</span>
                                <a href="{{ $currentFilePath }}" target="_blank"
                                    class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                                    title="View">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                            </div>
                        
                    @elseif ($isPdf)
                       
                            <div class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white" stroke="black"/>
                                    <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em" font-weight="bold">PDF</text>
                                </svg>
                                <span class="text-sm">{{ basename($currentFilePath) }}</span>
                                <a href="{{ $currentFilePath }}" target="_blank"
                                    class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                                    title="View">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                            </div>
                        
                    @else
                        <div class="flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <span class="text-sm">{{ basename($currentFilePath) }}</span>
                        </div>
                    @endif
                </div>
            @endforeach
        @else
            <p class="font-medium text-center text-red-500">No documents uploaded</p>
        @endif

        {{-- Show pending changes for all certificate types --}}
        @if(count($changes ?? []) > 0)
            <div class="mt-4 border-t pt-4">
                @foreach($changes as $filename)
                    <div class="mb-2 p-2 border rounded bg-yellow-100"> <!-- Changed bg-yellow-50 to bg-yellow-100 for more visibility -->
                        @php
                            $fileExtension = pathinfo($filename, PATHINFO_EXTENSION);
                            $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                            $isPdf = strtolower($fileExtension) === 'pdf';
                            $filename = basename($filename);
                            $currentFilePath = getImage($filename,'/images/clinic/'.$record->clinicData->id);
                        @endphp

                        @if ($isImage)
                           
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <span class="text-sm font-medium text-yellow-800">{{ basename($filename) }}</span>
                                    <a href="{{ $currentFilePath }}" target="_blank" class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100" title="View">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                    </a>
                                </div>
                            
                        @elseif ($isPdf)
                            <a href="{{ asset('storage/images/clinic/' . $record->clinicData->id . '/' . $filename) }}"
                            download="{{ basename($filename) }}"
                            class="flex items-center gap-2">
                                <div class="flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white" stroke="black"/>
                                        <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em" font-weight="bold">PDF</text>
                                    </svg>
                                    <span class="text-sm font-medium text-yellow-800">{{ basename($filename) }}</span>
                                </div>
                            </a>
                        @else
                            <div class="flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="text-sm font-medium text-yellow-800">{{ basename($filename) }}</span>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        @endif
    </section>
@endif
</div>