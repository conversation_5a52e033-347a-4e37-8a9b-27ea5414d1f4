<div class="space-y-4">
    @if ($suborders->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            PO Number</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Supplier Name</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Order Amount</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Admin <PERSON></th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Net Earnings</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Purchase Order</th>
                            <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            PS Invoice</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Order Status</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Credit Line</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Payout Type</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Payout Status</th>
                        <th
                            class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                           Outstanding Commission Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach ($suborders as $suborder)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ ($suborder->po_number) ? $suborder->po_number : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ pcCompanyName($suborder->user->pcDetails) ?? 'N/A' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                RM {{ number_format($suborder->total_sub_order_value ?? 0, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                RM {{ number_format($suborder->orderProducts->sum('total_commission') ?? 0, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                RM {{ number_format($suborder->net_earnings ?? 0, 2) }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @php
                                     $fileName = $suborder->invoice_path;
                                     $url = '';
                                        if ($fileName) {
                                            $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                            $filePath = $invoicePath . $fileName;

                                            $url = Storage::disk('s3')->temporaryUrl(
                                                $filePath,
                                                now()->addMinutes(15) // URL will be valid for 15 minutes
                                            );
                                        }


                                        $isDisabled = empty($url);
                                    @endphp
                                    @if (!$isDisabled)
                                    <a
                                    @if (!$isDisabled) href="{{ $url }}" target="_blank" @endif
                                    class="inline-flex items-center text-blue-600 hover:underline {{ $isDisabled ? 'pointer-events-none text-gray-400 cursor-not-allowed' : '' }}"
                                >
                                <x-heroicon-o-arrow-down-tray class="w-5 h-5" />

                                </a>
                                @else
                                <span>-</span>
                                @endif

                            </td>

                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @php
                                     $fileName = $suborder->getFirstMedia('invoices');
                                     $url = '';
                                        if ($fileName) {
                                            $url = $fileName->getFullUrl();
                                        }


                                        $isDisabled = empty($url);
                                    @endphp
                                     @if (!$isDisabled)
                                   <a
                                   @if (!$isDisabled) href="{{ $url }}" target="_blank" @endif
                                   class="inline-flex items-center text-blue-600 hover:underline {{ $isDisabled ? 'pointer-events-none text-gray-400 cursor-not-allowed' : '' }}"
                               >

                               <x-heroicon-o-arrow-down-tray class="w-5 h-5" />

                               </a>
                               @else
                               <span>-</span>
                               @endif


                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @php
                                    $status = strtolower($suborder->status);
                                    $style = match ($status) {
                                        'pending'
                                            => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;',
                                        'rejected'
                                            => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;',
                                        'accepted'
                                            => 'background-color:rgba(243, 251, 255, 1); border: 1px solid rgba(206, 237, 253, 1); border-radius: 6px; color:rgba(0, 70, 104, 1); padding: 4px 8px; width: fit-content;',
                                        'cancelled'
                                            => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;',
                                        'delivered'
                                            => 'background-color:rgba(242, 253, 245, 1); border: 1px solid rgba(211, 243, 223, 1); border-radius: 6px; color:rgba(22, 163, 74, 1); padding: 4px 8px; width: fit-content;',
                                        default
                                            => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;',
                                    };
                                    $statusLabel = match ($suborder->status) {
                                        'accepted' => 'Accepted',
                                        'cancelled' => 'Cancelled',
                                        'delivered' => 'Delivered',
                                        'in_transit' => 'In Transit',
                                        'pending' => 'Pending',
                                        'ready_for_pickup' => 'Ready For Pickup',
                                        'rejected' => 'Rejected',
                                        default => Str::title(str_replace('_', ' ', $suborder->status)),
                                    };
                                @endphp
                                <span style="{{ $style }}">
                                    {{ ucfirst($statusLabel) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                @php
                                    $status = strtolower($suborder->payment_type);
                                    $style = match ($status) {
                                        'pay_now'
                                            => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;',
                                        'credit_line'
                                            => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;',
                                         default
                                            => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;',
                                    };
                                @endphp
                                <span style="{{ $style }}">
                                    {{ ($suborder->payment_type == 'credit_line') ? 'Yes': 'No' }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ ($suborder?->payoutSubOrder?->Payout?->payout_type) ? ucfirst($suborder?->payoutSubOrder?->Payout?->payout_type) : '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ ($suborder?->payoutSubOrder?->Payout?->is_payout) ? 'Yes' : 'No' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ ($suborder?->payoutSubOrder?->Payout?->outstanding_commission_status) ? ucfirst($suborder?->payoutSubOrder?->Payout?->outstanding_commission_status) : 'Pending' }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="text-center text-gray-500">
            No suborders found for this order.
        </div>
    @endif
</div>
