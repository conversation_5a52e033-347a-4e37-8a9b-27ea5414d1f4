<x-filament::page>
    <div class="space-y-8">
        <!-- Cards Section -->
        <div class="grid grid-cols-1 gap-6">
            <!-- Card 1 -->
            <div class="bg-white shadow rounded-lg">
                <div class="bg-white shadow rounded-lg">
                    <div class="bg-gray-100 px-6 py-4 border-b border-gray-200 rounded-t-lg">
                        <div class="flex items-center justify-between">
                            <h2 class="text-lg font-semibold text-gray-800">
                                Ticket Details
                            </h2>
                            <!-- Ticket Header -->

                            @php
                            $isAdminCompany = isAdmin();
                            $canMarkAsClosed = $isAdminCompany || auth()->user()->hasRole('Super Admin') ||
                            auth()->user()->can('support-tickets_support ticket mark as closed');
                            @endphp
                            <div class="flex space-x-2">
                                @if ($canMarkAsClosed && $this->ticket->status !== 'closed')
                                    {{ $this->markAsClosedAction }}
                                @endif
                                <a href="{{ \App\Filament\Admin\Resources\SupportTicketResource::getUrl('index', ['activeTab' => session('support_ticket_active_tab', 'all')]) }}"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50">
                                    Back
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-y-4 gap-x-6">
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Ticket ID</p>
                            <p class="text-gray-900 text-sm">TKT-{!! $ticket->id !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Order ID</p>
                            @if(
                                $ticket->order_id && 
                                (
                                    auth()->user()->hasRole('Super Admin') || 
                                    auth()->user()->can('all-orders_view details')
                                )
                            )
                            <a href="{{ route('filament.admin.resources.orders.view' , ['record' => $ticket->order_id]) }}" class="text-sm text-blue-600 hover:underline">
                                #{!! $ticket->order->order_number ?? '-' !!}
                            </a>
                            @else
                                <p class="text-sm text-blue-600" title="You do not have permission to view this order.">
                                    {!! $ticket->order->order_number ? '#' . $ticket->order->order_number : '-' !!}
                                </p>
                            @endif
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Name</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->name ? ucfirst($ticket->name) : '-' !!}</p>
                        </div>
                         <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">User Type</p>
                            <p class="text-gray-900 text-sm">
                                @php
                                $getFormattedRole = function ($roleName) {
                                return match (strtolower($roleName)) {
                                'pharmaceutical company' => 'Pharmaceutical Supplier',
                                'clinic' => 'Facility',
                                default => ucfirst($roleName),
                                };
                                };
                                @endphp

                                @if ($ticket->sender->parent_id === null)
                                @php
                                $role = $ticket->sender->roles->first();
                                $formattedRole = $role ? $getFormattedRole($role->name) : 'No Role';
                                @endphp
                                {!! $formattedRole !!}
                                @else
                                @php
                                $parent = $ticket->sender->parent;
                                $parentRole = $parent ? $parent->roles->first() : null;
                                $formattedParentRole = $parentRole ? $getFormattedRole($parentRole->name) : 'No Parent Role';
                                @endphp
                                {!! $formattedParentRole !!}
                                @endif
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Raised From</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->sender->name ? ucfirst($ticket->sender->name) : '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Raised To</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->receiver->name ? ucfirst($ticket->receiver->name) : '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Created Date</p>
                            <p class="text-gray-900 text-sm">
                                @if(auth()->user()->timezone)
                                {!! \Carbon\Carbon::parse($ticket->created_at)
                                ->timezone(auth()->user()->timezone)
                                ->format('M d, Y h:i A') !!}
                                @else
                                {!! \Carbon\Carbon::parse($ticket->created_at)
                                ->format('M d, Y h:i A') !!}
                                @endif
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Email</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->email ?? '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Category</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->category->name ?? '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Status</p>
                            <span class="px-3 py-1 rounded text-sm font-medium
                              {{ $ticket->status === 'open' ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100' }}">
                                {!! ucfirst($ticket->status) !!}
                            </span>
                        </div>
                        @if($ticket->closed_at)
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Closed Date</p>
                            <p class="text-gray-900 text-sm">
                                @if(auth()->user()->timezone)
                                {!! \Carbon\Carbon::parse($ticket->closed_at)
                                ->timezone(auth()->user()->timezone)
                                ->format('M d, Y h:i A') !!}
                                @else
                                {!! \Carbon\Carbon::parse($ticket->closed_at)
                                ->format('M d, Y h:i A') !!}
                                @endif
                            </p>
                        </div>
                        @endif
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Subject</p>
                            <p class="text-gray-900 text-sm">{!! $ticket->subject ? ucfirst($ticket->subject) : '-' !!}</p>
                        </div>
                    </div>
                    <div class="mt-6">
                        <p class="text-gray-900 text-sm font-medium mb-2.5">Description</p>
                        <p class="text-gray-900 text-sm">{!! $ticket->description ? nl2br(e($ticket->description)) : '-' !!}</p>
                    </div>
                    @if($ticket->hasMedia('support-ticket-images'))
                    <div class="mt-6">
                        <p class="text-gray-900 text-sm font-medium mb-2.5">Uploaded Images</p>
                        <div class="flex flex-wrap gap-4">
                            @foreach($ticket->getMedia('support-ticket-images') as $media)
                            @if(str_starts_with($media->mime_type, 'image/'))
                            
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) ?: $media->getUrl() }}" target="_blank" rel="noopener noreferrer" class="w-[70px] h-[70px]">
                                    <img src="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) ?: $media->getUrl() }}" alt="{{ $media->name ?? 'Support Ticket Image' }}"  class="w-full h-full object-cover rounded-lg shadow-md">
                                </a>
                            
                            @else
                            <a href="{{ $media->getUrl() ?: getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}"
                                target="_blank"
                                class="block w-[70px] h-[70px] border-2 border-gray-200 rounded-lg flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors p-2">
                                <!-- <svg width="70" height="70" viewBox="0 0 24 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M5.5 0C4.60625 0 3.875 0.73125 3.875 1.625V24.375C3.875 25.2687 4.60625 26 5.5 26H21.75C22.6437 26 23.375 25.2687 23.375 24.375V6.5L16.875 0H5.5Z" fill="#E2E5E7"/>
                                    <path d="M18.5 6.5H23.375L16.875 0V4.875C16.875 5.76875 17.6062 6.5 18.5 6.5Z" fill="#B0B7BD"/>
                                    <path d="M23.375 11.375L18.5 6.5H23.375V11.375Z" fill="#CAD1D8"/>
                                    <path d="M20.125 21.125C20.125 21.5719 19.7594 21.9375 19.3125 21.9375H1.4375C0.990625 21.9375 0.625 21.5719 0.625 21.125V13C0.625 12.5531 0.990625 12.1875 1.4375 12.1875H19.3125C19.7594 12.1875 20.125 12.5531 20.125 13V21.125Z" fill="#F15642"/>
                                    <path d="M4.16699 15.3938C4.16699 15.1793 4.33599 14.9453 4.60818 14.9453H6.10887C6.95387 14.9453 7.71437 15.5108 7.71437 16.5947C7.71437 17.6217 6.95387 18.1937 6.10887 18.1937H5.02418V19.0517C5.02418 19.3377 4.84218 19.4994 4.60818 19.4994C4.39368 19.4994 4.16699 19.3377 4.16699 19.0517V15.3938ZM5.02418 15.7635V17.382H6.10887C6.54437 17.382 6.88887 16.9977 6.88887 16.5947C6.88887 16.1405 6.54437 15.7635 6.10887 15.7635H5.02418ZM8.98674 19.4994C8.77224 19.4994 8.53824 19.3824 8.53824 19.0972V15.4068C8.53824 15.1736 8.77224 15.0038 8.98674 15.0038H10.4744C13.4433 15.0038 13.3783 19.4994 10.5329 19.4994H8.98674ZM9.39624 15.7968V18.7072H10.4744C12.2286 18.7072 12.3066 15.7968 10.4744 15.7968H9.39624ZM14.4313 15.8488V16.8815H16.088C16.322 16.8815 16.556 17.1155 16.556 17.3422C16.556 17.5567 16.322 17.7322 16.088 17.7322H14.4313V19.0964C14.4313 19.3239 14.2696 19.4986 14.0421 19.4986C13.7561 19.4986 13.5814 19.3239 13.5814 19.0964V15.406C13.5814 15.1728 13.7569 15.003 14.0421 15.003H16.3228C16.6088 15.003 16.7778 15.1728 16.7778 15.406C16.7778 15.614 16.6088 15.848 16.3228 15.848H14.4313V15.8488Z" fill="white"/>
                                    <path d="M19.3125 21.9375H3.875V22.75H19.3125C19.7594 22.75 20.125 22.3844 20.125 21.9375V21.125C20.125 21.5719 19.7594 21.9375 19.3125 21.9375Z" fill="#CAD1D8"/>
                                </svg> -->
                                <img src="{{ asset('images/pdf-icon.svg') }}" alt="PDF" class="w-16 h-16">
                            </a>
                            @endif
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Card 2 -->
             @if(auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-tickets_support ticket chat'))
            <div class="overflow-hidden rounded bg-white shadow">
                <div id="conversation-header" class="flex justify-between items-center px-5 py-2 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Conversation</h2>
                    <button @click="window.location.reload()" class="ml-2 px-2 py-1 bg-primary-600 text-white rounded hover:bg-primary-800 text-sm flex items-center">
                        <x-heroicon-o-arrow-path class="h-6 w-6 text-white-500 mr-1" />
                        Refresh For Latest Messages
                    </button>
                </div>

                <!-- Chat Messages -->
                <div class="flex-1 h-96 overflow-y-auto p-4 relative" style="height: 192px;" x-data="{
                            messagesContainer: null,
                            isLoading: false,
                            scrollPosition: 0,
                            wasAtBottom: true,
                            showNewMessageIndicator: false,
                            init() {
                                this.messagesContainer = $refs.messageContainer;
                                this.messagesContainer.addEventListener('scroll', this.onScroll.bind(this));
                                this.scrollToBottom();
                        
                                setInterval(() => {
                                    // Only auto-load new messages if user is near the bottom
                                    if (this.isNearBottom()) {
                                        this.wasAtBottom = true;
                                        @this.call('loadMessages').then(() => {
                                            if (this.wasAtBottom) {
                                                this.scrollToBottom();
                                            }
                                        });
                                    }
                                }, 3000);
                                    
                                window.addEventListener('message-sent', () => {
                                    this.wasAtBottom = true;
                                    setTimeout(() => {
                                        this.scrollToBottom();
                                    }, 100);
                                });
                                
                                window.addEventListener('messages-loaded', (event) => {
                                    if (event.detail && event.detail.preservePosition) {
                                        this.$nextTick(() => {
                                            // Restore scroll position after loading older messages
                                            const newHeight = this.messagesContainer.scrollHeight;
                                            const heightDiff = newHeight - this.scrollHeight;
                                            this.messagesContainer.scrollTop = heightDiff;
                                        });
                                    } else if (this.wasAtBottom) {
                                        // Only scroll to bottom if user was at bottom before loading
                                        this.scrollToBottom();
                                    }
                                });

                                // Listen for new messages from Livewire
                                window.addEventListener('new-message-arrived', () => {
                                    if (!this.isNearBottom()) {
                                        this.showNewMessageIndicator = true;
                                    } else {
                                        this.scrollToBottom();
                                    }
                                });
                            },
                            onScroll() {
                                const scrollTop = this.messagesContainer.scrollTop;
                                
                                // Update wasAtBottom flag
                                this.wasAtBottom = this.isNearBottom();
                                
                                // Store height before loading more messages
                                this.scrollHeight = this.messagesContainer.scrollHeight;
                                
                                // Only trigger load more if we're near the top and not already loading
                                if (scrollTop < 50 && !this.isLoading) {
                                    this.isLoading = true;
                                    this.wasAtBottom = false; // User is viewing older messages
                                    // Store current scroll position
                                    this.scrollPosition = this.messagesContainer.scrollTop;
                                    
                                    @this.call('loadMoreMessages').then(() => {
                                        this.isLoading = false;
                                        this.$wire.dispatchTo('messages-loaded', { preservePosition: true });
                                    }).catch(() => {
                                        this.isLoading = false;
                                    });
                                }
                                // Hide indicator if user scrolls to bottom
                                if (this.isNearBottom()) {
                                    this.showNewMessageIndicator = false;
                                }
                            },
                            isNearBottom() {
                                const threshold = 100; // pixels from bottom
                                return (this.messagesContainer.scrollTop + this.messagesContainer.clientHeight) >= 
                                       (this.messagesContainer.scrollHeight - threshold);
                            },
                            scrollToBottom() {
                                this.$nextTick(() => {
                                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                                    this.showNewMessageIndicator = false;
                                });
                            }
                        }" x-ref="messageContainer" class="scroll-smooth">
                    <!-- Loading indicator -->
                    <div wire:loading.delay.shortest wire:target="loadMoreMessages"
                        class="absolute top-2 left-1/2 transform -translate-x-1/2 z-50 mb-4">
                        <x-filament::loading-indicator class="h-5 w-5" />
                    </div>
                    <!-- New Messages Down Arrow -->
                    <div x-show="showNewMessageIndicator"
                         @click="scrollToBottom(); showNewMessageIndicator = false"
                         class="fixed bottom-24 right-8 z-50 cursor-pointer bg-primary-600 text-white rounded-full shadow-lg p-2 transition-opacity duration-300"
                         style="display: none;"
                         x-transition>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </div>
                    @php
                    $previousDate = null;
                    @endphp

                    @foreach ($messages as $message)
                    @php
                    $rawDate = $message->created_at->toDateString();
                    $messageDate = $message->created_at->format('d M, Y');
                    @endphp

                    @if ($previousDate !== $rawDate)
                    <div class="text-center my-2">
                        <span class="inline-block bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full">
                            {{ $rawDate === now()->toDateString() ? 'Today' : ($rawDate === now()->subDay()->toDateString() ? 'Yesterday' : $messageDate) }}
                        </span>
                    </div>
                    @php $previousDate = $rawDate; @endphp
                    @endif


                    @php
                    $user = $message->from_id == auth()->id() ? auth()->user() : $message->sender;
                    if ($user->parent_id) {
                        $mainUser = App\Models\User::find($user->parent_id);
                        $user = $mainUser ?: $user; // Fallback to original user if parent not found
                    }
                    $filename = $user->photo ? basename($user->photo) : null;

                    $imagePaths = ['users/', 'profile-photos/'];

                    $photo = null;
                    if ($filename) {
                    foreach ($imagePaths as $path) {
                    if (Storage::disk('s3')->exists($path . $filename)) {
                    $photo = Storage::disk('s3')->url($path . $filename);
                    break;
                    }
                    }
                    }
                    $photo = $photo ?: asset('images/default-avatar.png');
                    @endphp

                    <!-- All messages displayed on left side -->
                    <div class="flex justify-start items-start gap-3 mt-5">
                        <div class="w-6 h-6 rounded bg-gray-400 flex items-center justify-center overflow-hidden shadow-sm mt-1">
                            <img src="{{ $photo }}" class="w-full h-full object-cover" alt="User Photo">
                        </div>

                        <div class="w-full">
                            <div class="flex items-center mb-0.5">
                                <span class="text-sm font-semibold text-gray-800 mr-1">{{ ucwords($user->name) }}</span>
                                <span class="text-xs text-gray-500 mx-1 inline-flex items-center justify-center">
                                    <span class="w-1 h-1 bg-gray-500 rounded-full"></span>
                                </span>
                                <span class="text-xs text-gray-500">
                                    @if($user->timezone)
                                    {{ $message->created_at->timezone($user->timezone)->format('h:i A') }}
                                    @else
                                    {{ $message->created_at->format('h:i A') }}
                                    @endif
                                </span>
                            </div>

                            <!-- Media Display (Images and PDFs) -->
                            @if ($message->hasMedia('support-ticket-images'))
                            @foreach ($message->getMedia('support-ticket-images') as $media)
                            <div class="mb-1">
                                @php
                                $mimeType = $media->mime_type ?? $media->getCustomProperty('mime_type', '');
                                $isImage = strpos($mimeType, 'image/') === 0 || in_array(strtolower($media->file_name), ['jpg', 'jpeg', 'png', 'gif']);
                                $isPdf = strpos($mimeType, 'pdf') !== false;
                                @endphp

                                @if ($isImage)
                                <!-- Display Images -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="inline-block">
                                    <img src="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" class="rounded-lg shadow max-w-[200px]" alt="{{ $media->name }}">
                                </a>
                                @elseif ($isPdf)
                                <!-- Display PDFs -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @else
                                <!-- Display Other Files -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    @php
                                    $iconColor = strpos($mimeType, 'word') !== false ? 'text-blue-500' :
                                    (strpos($mimeType, 'excel') !== false ? 'text-green-500' : 'text-gray-500');
                                    @endphp
                                    <x-heroicon-o-document-text class="h-6 w-6 {{ $iconColor }}" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @endif
                            </div>
                            @endforeach
                            @endif

                            <!-- Text Message -->
                            @if($message->message)
                            <div class="mt-0.5 mb-0.5 w-relative" style="word-break: break-word; max-width: 170ch;">
                                {!! nl2br(e($message->message)) !!}
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Message Input Section -->
                <div class="p-3 border-t bg-white rounded-b-lg pt-6">
                    @if($ticket->status != 'closed')
                    <div class="flex items-center space-x-2">
                        <!-- File Upload Input -->
                        <input type="file" wire:model="attachedFiles" class="hidden" id="file-upload" multiple accept="image/*,.pdf" @if($ticket->status == 'closed') disabled @endif>
                        <label for="file-upload" style="margin:-8px 0 0 0;" class="cursor-pointer p-2 rounded-full bg-gray-200 @if($ticket->status == 'closed') opacity-50 cursor-not-allowed @endif">
                            <x-heroicon-o-paper-clip class="h-5 w-5 text-gray-500" />
                        </label>


                        <!-- File Preview Section -->
                        @if (!empty($attachedFiles) && $ticket->status != 'closed')
                        <div class="flex space-x-1 my-1">
                            @foreach ($attachedFiles as $index => $file)
                            <div class="relative group">
                                @php
                                $fileType = $file->getMimeType();
                                $isImage = strpos($fileType, 'image/') === 0;
                                @endphp

                                @if ($isImage)
                                <!-- Image Preview -->
                                <img src="{{ $file->temporaryUrl() }}" class="h-10 w-10 rounded-lg border object-cover">
                                @else
                                <!-- Document Preview -->
                                <div class="h-10 w-10 rounded-lg border flex items-center justify-center bg-gray-100">
                                    @if (strpos($fileType, 'pdf') !== false)
                                    <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                                    @else
                                    <x-heroicon-o-document class="h-6 w-6 text-gray-500" />
                                    @endif
                                </div>
                                @endif

                                <!-- File Name Tooltip -->
                                <div class="absolute -bottom-5 left-0 bg-gray-800 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    {{ $file->getClientOriginalName() }}
                                </div>

                                <!-- Remove Button -->
                                <button wire:click="removeFile({{ $index }})" class="absolute -top-1 -right-1 bg-red-500 rounded-full p-1">
                                    <x-heroicon-o-x-mark class="h-4 w-4 text-white" />
                                </button>
                            </div>
                            @endforeach
                        </div>
                        @endif

                        <!-- Message Input -->
                        <div class="flex-1 relative">
                            <textarea wire:model.defer="messageText" placeholder="Write your message"
                                class="w-full rounded-lg border border-gray-300 px-3 py-2 pr-12 focus:border-primary-500 focus:ring-primary-500 resize-none @if($ticket->status == 'closed') bg-gray-100 cursor-not-allowed @endif @error('messageText') border-red-500 @enderror"
                                x-data="{
                                        charCount: 0,
                                        maxChars: 500,
                                        adjustHeight() {
                                            this.$el.style.height = '38px';
                                            this.$el.style.height = Math.min(this.$el.scrollHeight, 100) + 'px';
                                            this.$el.style.overflowY = this.$el.scrollHeight > 100 ? 'auto' : 'hidden';
                                        },
                                        countChars() {
                                            this.charCount = this.$el.value.length;
                                            this.adjustHeight();
                                        },
                                        sendMessage(event) {
                                            if (!event.shiftKey && this.charCount <= this.maxChars) {
                                                event.preventDefault();
                                                @this.sendMessage();
                                                this.$el.style.height = '38px';
                                                this.charCount = 0;
                                            }
                                        }
                                    }"
                                x-init="adjustHeight()"
                                @input="countChars()"
                                @keydown.enter="sendMessage($event)"
                                style="height: 38px; overflow-y: hidden"
                                maxlength="500"
                                @if($ticket->status == 'closed') disabled @endif></textarea>
                        </div>


                        <!-- Send Button -->
                        <div class="plane-fs" style="margin:-10px 0 0 10px;">
                            <button type="button" wire:click="sendMessage" id="send-button"
                                class="inline-flex items-center justify-center rounded-full bg-primary-600 p-2 text-white relative disabled:opacity-50"
                                wire:loading.attr="disabled" wire:target="sendMessage" @if($ticket->status == 'closed') disabled @endif>
                                <span wire:loading.remove wire:target="sendMessage">
                                    <x-heroicon-o-paper-airplane class="h-5 w-5" />
                                </span>
                                <span wire:loading wire:target="sendMessage">
                                    <x-filament::loading-indicator class="h-5 w-5" />
                                </span>
                            </button>
                        </div>

                    </div>
                    <div class="ml-5">
                        @error('messageText')
                        <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                        @enderror
                        @error('attachedFiles')
                        <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                        @enderror
                    </div>
                    @endif
                    @if($ticket->status == 'closed')
                    <p class="text-sm text-danger-500 flex justify-center mb-2">This ticket is closed. No further messages or files can be sent.</p>
                    @endif
                </div>
            </div>
            @endif
        </div>
    </div>
</x-filament::page>