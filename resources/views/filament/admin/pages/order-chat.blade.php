<x-filament::page>
    <x-filament::section>
        <div class="flex flex-col md:flex-row gap-3 overflow-hidden" style="height: calc(100vh - 245px)">
            <!-- Messages Table Section -->
            <div class="w-full md:w-3/3 bg-white rounded-lg shadow overflow-hidden flex flex-col">
                <div class="flex-1 overflow-auto p-4 ticketChatTable relative">
                    {{ $this->table }}
                </div>
            </div>

            <!-- Chat Section -->
            @if($selectTicket)
            <div class="w-full md:w-3/3 bg-white rounded-lg shadow flex flex-col ">
                <!-- Chat Header -->
                <div class="p-4 border-b bg-gray-50 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">
                                @php
                                $name = $selectTicket->sender_id == auth()->id()
                                ? ($selectTicket->receiver->name ?? null)
                                : ($selectTicket->sender->name ?? null);

                                $orderNumber = $selectTicket->order?->order_number;

                                if ($name && $orderNumber) {
                                    echo ucwords($name) . ' | ' . ' Order #' . $orderNumber;
                                } elseif ($name) {
                                    echo ucwords($name);
                                } elseif ($orderNumber) {
                                    echo 'Order #' . $orderNumber;
                                } else {
                                    echo 'Unknown User';
                                }
                                @endphp
                            </h3>
                        </div>
                    </div>
                </div>

                <!-- Chat Messages -->
                <div class="flex-1 overflow-y-auto p-4 relative" x-data="{
                            messagesContainer: null,
                            isLoading: false,
                            scrollPosition: 0,
                            wasAtBottom: true,
                            showNewMessageIndicator: false,
                            init() {
                                this.messagesContainer = $refs.messageContainer;
                                this.messagesContainer.addEventListener('scroll', this.onScroll.bind(this));
                                this.scrollToBottom();
                        
                                setInterval(() => {
                                    // Only auto-load new messages if user is near the bottom
                                    if (this.isNearBottom()) {
                                        this.wasAtBottom = true;
                                        @this.call('loadMessages').then(() => {
                                            if (this.wasAtBottom) {
                                                this.scrollToBottom();
                                            }
                                        });
                                    }
                                }, 3000);
                                    
                                window.addEventListener('message-sent', () => {
                                    this.wasAtBottom = true;
                                    setTimeout(() => {
                                        this.scrollToBottom();
                                    }, 100);
                                });
                                
                                window.addEventListener('messages-loaded', (event) => {
                                    if (event.detail && event.detail.preservePosition) {
                                        this.$nextTick(() => {
                                            // Restore scroll position after loading older messages
                                            const newHeight = this.messagesContainer.scrollHeight;
                                            const heightDiff = newHeight - this.scrollHeight;
                                            this.messagesContainer.scrollTop = heightDiff;
                                        });
                                    } else if (this.wasAtBottom) {
                                        // Only scroll to bottom if user was at bottom before loading
                                        this.scrollToBottom();
                                    }
                                });

                                // Listen for new messages from Livewire
                                window.addEventListener('new-message-arrived', () => {
                                    if (!this.isNearBottom()) {
                                        this.showNewMessageIndicator = true;
                                    } else {
                                        this.scrollToBottom();
                                    }
                                });
                            },
                            onScroll() {
                                const scrollTop = this.messagesContainer.scrollTop;
                                
                                // Update wasAtBottom flag
                                this.wasAtBottom = this.isNearBottom();
                                
                                // Store height before loading more messages
                                this.scrollHeight = this.messagesContainer.scrollHeight;
                                
                                // Only trigger load more if we're near the top and not already loading
                                if (scrollTop < 50 && !this.isLoading) {
                                    this.isLoading = true;
                                    this.wasAtBottom = false; // User is viewing older messages
                                    // Store current scroll position
                                    this.scrollPosition = this.messagesContainer.scrollTop;
                                    
                                    @this.call('loadMoreMessages').then(() => {
                                        this.isLoading = false;
                                        this.$wire.dispatchTo('messages-loaded', { preservePosition: true });
                                    }).catch(() => {
                                        this.isLoading = false;
                                    });
                                }
                                // Hide indicator if user scrolls to bottom
                                if (this.isNearBottom()) {
                                    this.showNewMessageIndicator = false;
                                }
                            },
                            isNearBottom() {
                                const threshold = 100; // pixels from bottom
                                return (this.messagesContainer.scrollTop + this.messagesContainer.clientHeight) >= 
                                       (this.messagesContainer.scrollHeight - threshold);
                            },
                            scrollToBottom() {
                                this.$nextTick(() => {
                                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                                    this.showNewMessageIndicator = false;
                                });
                            }
                        }" x-ref="messageContainer" class="scroll-smooth">
                    <!-- Loading indicator -->
                    <div wire:loading.delay.shortest wire:target="loadMoreMessages"
                        class="absolute top-2 left-1/2 transform -translate-x-1/2 z-50 mb-4">
                        <x-filament::loading-indicator class="h-5 w-5" />
                    </div>
                    @php $previousDate = null; @endphp
                    @foreach ($messages as $message)
                    @php
                    $rawDate = $message->created_at->toDateString();
                    $messageDate = $message->created_at->format('d M, Y');
                    @endphp

                    @if ($previousDate !== $rawDate)
                    <div class="text-center my-2">
                        <span class="inline-block bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full">
                            {{ $rawDate === now()->toDateString() ? 'Today' : ($rawDate === now()->subDay()->toDateString() ? 'Yesterday' : $messageDate) }}
                        </span>
                    </div>
                    @php $previousDate = $rawDate; @endphp
                    @endif

                    @php
                    $user = $message->from_id == auth()->id() ? auth()->user() : $message->sender;
                    if ($user->parent_id) {
                        $mainUser = App\Models\User::find($user->parent_id);
                        $user = $mainUser ?: $user; // Fallback to original user if parent not found
                    }
                    $filename = $user->photo ? basename($user->photo) : null;

                    $imagePaths = ['users/', 'profile-photos/'];

                    $photo = null;
                    if ($filename) {
                    foreach ($imagePaths as $path) {
                    if (Storage::disk('s3')->exists($path . $filename)) {
                    $photo = Storage::disk('s3')->url($path . $filename);
                    break;
                    }
                    }
                    }
                    $photo = $photo ?: asset('images/default-avatar.png');
                    @endphp


                    <!-- All messages displayed on left side -->
                    <div class="flex justify-start items-start gap-3 mt-5">
                        <div class="w-6 h-6 rounded bg-gray-400 flex items-center justify-center overflow-hidden shadow-sm mt-1">
                            <img src="{{ $photo }}" class="w-full h-full object-cover" alt="User Photo">
                        </div>

                        <div class="w-full">
                            <div class="flex items-center mb-0.5">
                                <span class="text-sm font-semibold text-gray-800 mr-1">{{ ucwords($user->name) }}</span>
                                <span class="text-xs text-gray-500 mx-1 inline-flex items-center justify-center">
                                    <span class="w-1 h-1 bg-gray-500 rounded-full"></span>
                                </span>
                                <span class="text-xs text-gray-500">
                                    @if($user->timezone)
                                        {{ $message->created_at->timezone($user->timezone)->format('h:i A') }}
                                    @else
                                        {{ $message->created_at->format('h:i A') }}
                                    @endif
                                </span>
                            </div>

                            @if ($message->hasMedia('thread-chat-images'))
                            @foreach ($message->getMedia('thread-chat-images') as $media)
                            <div class="mb-1">
                                @php
                                $mimeType = $media->mime_type ?? $media->getCustomProperty('mime_type', '');
                                $isImage = strpos($mimeType, 'image/') === 0 || in_array(strtolower($media->file_name), ['jpg', 'jpeg', 'png', 'gif']);
                                $isPdf = strpos($mimeType, 'pdf') !== false;
                                @endphp

                                @if ($isImage)
                                <!-- Display Images -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $selectTicket->id) }}" target="_blank" class="inline-block">
                                    <img src="{{ getImage($media->file_name, config('constants.api.media.thread') . $selectTicket->id) }}" class="rounded-lg shadow max-w-[200px]" alt="{{ $media->name }}">
                                </a>
                                @elseif ($isPdf)
                                <!-- Display PDFs -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $selectTicket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @else
                                <!-- Display Other Files -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    @php
                                    $iconColor = strpos($mimeType, 'word') !== false ? 'text-blue-500' :
                                    (strpos($mimeType, 'excel') !== false ? 'text-green-500' : 'text-gray-500');
                                    @endphp
                                    <x-heroicon-o-document-text class="h-6 w-6 {{ $iconColor }}" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @endif
                            </div>
                            @endforeach
                            @endif

                            <!-- Text Message -->
                            @if($message->message)
                            <div class="mt-0.5 mb-0.5 w-relative" style="word-break: break-word; max-width: 70ch;">
                                {!! nl2br(e($message->message)) !!}
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Message Input Section -->
                <div class="p-3 border-t bg-white rounded-b-lg pt-6">
                    <div class="flex items-center space-x-2">
                        <!-- File Upload Input -->
                        <input type="file" wire:model="attachedFiles" class="hidden" id="file-upload" multiple accept="image/*,.pdf" @if($selectTicket->status == 'closed') disabled @endif>
                        <label for="file-upload" style="margin:-8px 0 0 0;" class="cursor-pointer p-2 rounded-full bg-gray-200 @if($selectTicket->status == 'closed') opacity-50 cursor-not-allowed @endif">
                            <x-heroicon-o-paper-clip class="h-5 w-5 text-gray-500" />
                        </label>

                        <!-- File Preview Section -->
                        @if (!empty($attachedFiles) && $selectTicket->status != 'closed')
                        <div class="flex space-x-1 my-1" 
                            x-data="{ 
                                files: @entangle('attachedFiles').defer,
                                showPreview: true,
                                init() {
                                    this.$watch('files', (value) => {
                                        this.showPreview = Array.isArray(value) && value.length > 0;
                                    });
                                }
                            }"
                            x-show="showPreview && files && files.length > 0"
                            x-transition:enter="transition ease-out duration-200"
                            x-transition:enter-start="opacity-0 transform scale-95"
                            x-transition:enter-end="opacity-100 transform scale-100"
                            x-transition:leave="transition ease-in duration-150"
                            x-transition:leave-start="opacity-100 transform scale-100"
                            x-transition:leave-end="opacity-0 transform scale-95">
                            @foreach ($attachedFiles as $index => $file)
                            @if($file && method_exists($file, 'getMimeType'))
                            <div class="relative group">
                                @php
                                try {
                                    $fileType = $file->getMimeType();
                                    $isImage = strpos($fileType, 'image/') === 0;
                                    $temporaryUrl = $file->temporaryUrl();
                                } catch (Exception $e) {
                                    // Handle case where file object is invalid
                                    continue;
                                }
                                @endphp

                                @if ($isImage && $temporaryUrl)
                                <!-- Image Preview -->
                                <img src="{{ $temporaryUrl }}" 
                                    class="h-10 w-10 rounded-lg border object-cover"
                                    onerror="this.style.display='none'">
                                @else
                                <!-- Document Preview -->
                                <div class="h-10 w-10 rounded-lg border flex items-center justify-center bg-gray-100">
                                    @if (strpos($fileType, 'pdf') !== false)
                                    <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                                    @else
                                    <x-heroicon-o-document class="h-6 w-6 text-gray-500" />
                                    @endif
                                </div>
                                @endif

                                <!-- File Name Tooltip -->
                                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10 pointer-events-none">
                                    @try
                                    {{ Str::limit($file->getClientOriginalName(), 20) }}
                                    @catch(Exception $e)
                                    Unknown File
                                    @endtry
                                </div>

                                <!-- Remove Button -->
                                <button wire:click="removeFile({{ $index }})" 
                                        type="button"
                                        class="absolute -top-1 -right-1 bg-red-500 hover:bg-red-600 rounded-full p-1 transition-colors">
                                    <x-heroicon-o-x-mark class="h-3 w-3 text-white" />
                                </button>
                            </div>
                            @endif
                            @endforeach
                        </div>      
                        @endif

                        <!-- Message Input -->
                        <div class="flex-1 auto" wire:key="message-input">
                            <textarea wire:model.defer="messageText" placeholder="Write your message"
                                class="w-full rounded-lg border border-gray-300 px-3 py-2 pr-12 focus:border-primary-500 focus:ring-primary-500 resize-none @if($selectTicket->status == 'closed') bg-gray-100 cursor-not-allowed @endif @error('messageText') border-red-500 @enderror"
                                x-data="{
                                        charCount: 0,
                                        maxChars: 500,
                                        adjustHeight() {
                                            this.$el.style.height = '38px';
                                            this.$el.style.height = Math.min(this.$el.scrollHeight, 100) + 'px';
                                            this.$el.style.overflowY = this.$el.scrollHeight > 100 ? 'auto' : 'hidden';
                                        },
                                        countChars() {
                                            this.charCount = this.$el.value.length;
                                            this.adjustHeight();
                                        },
                                        sendMessage(event) {
                                            if (!event.shiftKey && this.charCount <= this.maxChars) {
                                                event.preventDefault();
                                                @this.sendMessage();
                                                this.$el.style.height = '38px';
                                                this.charCount = 0;
                                            }
                                        }
                                    }"
                                x-init="adjustHeight()"
                                @input="countChars()"
                                @keydown.enter="sendMessage($event)"
                                style="height: 38px; overflow-y: hidden"
                                maxlength="500"
                                @if($selectTicket->status == 'closed') disabled @endif></textarea>
                        </div>

                        <!-- Send Button -->
                        <div class="plane-fs" style="margin:-10px 0 0 10px;">
                            <button type="button" wire:click="sendMessage" id="send-button"
                                class="inline-flex items-center justify-center rounded-full bg-primary-600 p-2 text-white relative disabled:opacity-50"
                                wire:loading.attr="disabled" wire:target="sendMessage" @if($selectTicket->status == 'closed') disabled @endif>
                                <span wire:loading.remove wire:target="sendMessage">
                                    <x-heroicon-o-paper-airplane class="h-5 w-5" />
                                </span>
                                <span wire:loading wire:target="sendMessage">
                                    <x-filament::loading-indicator class="h-5 w-5" />
                                </span>
                            </button>
                        </div>
                    </div>
                    <div class="ml-5">
                        @error('messageText')
                        <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                        @enderror
                        @error('attachedFiles')
                        <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                        @enderror
                    </div>
                    @if($selectTicket->status == 'closed')
                    <p class="text-sm text-danger-500 mt-2">This ticket is closed. No further messages or files can be sent.</p>
                    @endif
                </div>
            </div>
            @else
            <!-- No Selected Thread State -->
            <div class="w-full md:w-3/3 bg-white rounded-lg shadow flex flex-col items-center justify-center p-8 h-full">
                <div class="text-gray-400 mb-4">
                    <x-heroicon-o-chat-bubble-left-ellipsis class="h-16 w-16" />
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-1">No conversation selected</h3>
                <p class="text-gray-500 text-center">Select a conversation from the list to view messages.</p>
            </div>
            @endif
        </div>
    </x-filament::section>
</x-filament::page>