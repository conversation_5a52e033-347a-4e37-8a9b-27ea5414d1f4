@vite('resources/css/app.css')
<x-filament-panels::page>
    <div class="bg-white shadow-sm fi-section rounded-xl ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
        <div class="flex flex-col gap-3 px-6 py-4 fi-section-header">
            <div class="flex items-center gap-3">
                <div class="grid flex-1 gap-y-1">
                    <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">Personal Details</h3>
                </div>
            </div>
        </div>
        <div class="border-t border-gray-200 fi-section-content-ctn dark:border-white/10">
            <div class="p-6 fi-section-content">
                <div class="flex gap-6">
                    @php
                    $photoUrl = $user->photo
                    ? \Illuminate\Support\Facades\Storage::disk('s3')->url($user->photo)
                    : asset('images/user-avatar.png');
                    @endphp

                    <span>
                        <img
                            src="{{ $photoUrl }}"
                            alt="Profile Picture"
                            class="mr-4 w-[138px] h-[138px] w-20 h-20 rounded-full bg-black object-cover">
                    </span>

                    <div class="flex flex-wrap w-full gap-y-5">
                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Name</label>
                            <span class="text-sm">{{ $user->name }}</span>
                        </div>
                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Email</label>
                            <span class="text-sm">{{ $user->email }}</span>
                        </div>
                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Created
                                On</label>
                                <span class="text-sm">
                                    {{ \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $user->created_at, 'UTC')
                                        ->setTimezone($user->timezone ?? config('app.timezone'))
                                        ->format('M d, Y | h:i A') }}
                                </span>
                        </div>
                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Phone Number</label>
                            <span class="text-sm">{{ $this->user->phone ?? 'N/A' }}</span>
                            
                        </div>
                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Role</label>
                            <span class="text-sm">{{ $this->user->roles[0]->name ?? 'N/A' }}</span>
                            
                        </div>

                        <div class="w-1/3">
                            <label
                                class="fi-fo-field-wrp-label inline-block w-full items-center gap-x-3 text-sm text-neutral-900 font-medium leading-6 mb-2.5">Status</label>
                            <span
                                class="text-sm text-green-600 bg-[#F2FDF5] font-medium border border-[#D3F3DF] leading-5 py-1 px-1.5 rounded-md">{{ $user->is_active == 1 ? 'Active' : 'Inactive' }}</span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>

</x-filament-panels::page>