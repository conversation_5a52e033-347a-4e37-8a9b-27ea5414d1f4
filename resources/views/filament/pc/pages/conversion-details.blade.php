<x-filament::page>
    <div class="space-y-8">

        <!-- Cards Section -->
        <div class="grid grid-cols-1 gap-6">
            <!-- Card 1 -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 bg-gray-100 border-b border-gray-200 rounded-t-lg">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-gray-800">
                            Ticket Details
                        </h2>
                        <!-- Ticket Header -->
                        @php
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        $canMarkAsClosed = $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') ||
                        auth()->user()->can('facilities-support_mark as closed');
                        @endphp
                        
                        <div class="flex space-x-2">
                        @if ($canMarkAsClosed && $this->ticket->status !== 'closed')
                            {{ $this->markAsClosedAction }}
                        @endif
                            <a href="{{ \App\Filament\Pc\Resources\SupportTicketReceivedResource::getUrl() }}"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50">
                                Back
                            </a>

                        </div>
                       
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-y-4 gap-x-6">
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Ticket ID</p>
                            <p class="text-sm text-gray-900">TKT-{!! $ticket->id !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Order ID</p>
                            @if (
                                $ticket->order && 
                                !empty($subOrder) && 
                                $ticket->order_id &&
                                (
                                    auth()->user()->hasRole('Pharmaceutical Company') || 
                                    auth()->user()->can('all-orders_view details')
                                )
                            )
                                <a href="{{ route('filament.pc.resources.orders.view', ['record' => $subOrder->id]) }}"
                                    class="text-sm text-blue-600 hover:underline">
                                    #{{ $ticket->order->order_number }}
                                </a>
                            @elseif ($ticket->order && !empty($subOrder))
                                <span class="text-sm text-blue-600" title="You do not have permission to view this order.">
                                    #{{ $ticket->order->order_number }}
                                </span>
                            @else
                                <span class="text-sm text-gray-900">-</span>
                            @endif
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Name</p>
                            <p class="text-sm text-gray-900">{!! $ticket->name ? ucfirst($ticket->name) : '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Raised From</p>
                            <p class="text-sm text-gray-900">{!! $ticket->receiver->name ?
                                ucfirst($ticket->receiver->name) : '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Created Date</p>
                            <p class="text-sm text-gray-900">
                                @if(auth()->user()->timezone)
                                {!! \Carbon\Carbon::parse($ticket->created_at)
                                ->timezone(auth()->user()->timezone)
                                ->format('M d, Y h:i A') !!}
                                @else
                                {!! \Carbon\Carbon::parse($ticket->created_at)
                                ->format('M d, Y h:i A') !!}
                                @endif
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Email</p>
                            <p class="text-sm text-gray-900">{!! $ticket->email ?? '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Category</p>
                            <p class="text-sm text-gray-900">{!! $ticket->category->name ?? '-' !!}</p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Subject</p>
                            <p class="text-sm text-gray-900">{!! $ticket->subject ? ucfirst($ticket->subject) : '-' !!}
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Status</p>
                            <span
                                class="px-3 py-1 rounded text-sm font-medium
                              {{ $ticket->status === 'open' ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100' }}">
                                {!! ucfirst($ticket->status) !!}
                            </span>
                        </div>
                        @if($ticket->closed_at)
                        <div>
                            <p class="text-gray-900 text-sm font-medium mb-2.5">Closed Date</p>
                            <p class="text-gray-900 text-sm">
                                @if(auth()->user()->timezone)
                                {!! \Carbon\Carbon::parse($ticket->closed_at)
                                ->timezone(auth()->user()->timezone)
                                ->format('M d, Y h:i A') !!}
                                @else
                                {!! \Carbon\Carbon::parse($ticket->closed_at)
                                ->format('M d, Y h:i A') !!}
                                @endif
                            </p>
                        </div>
                        @endif
                    </div>
                    <div class="mt-6">
                        <p class="text-gray-900 text-sm font-medium mb-2.5">Description</p>
                        <p class="text-sm text-gray-900">{!! $ticket->description ? nl2br(e($ticket->description)) : '-'
                            !!}</p>
                    </div>
                    @if($ticket->hasMedia('support-ticket-images'))
                    <div class="mt-6">
                        <p class="text-gray-900 text-sm font-medium mb-2.5">Uploaded Images</p>
                        <div class="flex flex-wrap gap-4">
                            @foreach($ticket->getMedia('support-ticket-images') as $media)
                            @if(str_starts_with($media->mime_type, 'image/'))
                            
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) ?: $media->getUrl() }}" target="_blank" rel="noopener noreferrer" class="w-[70px] h-[70px]">
                                    <img src="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) ?: $media->getUrl() }}" alt="{{ $media->name ?? 'Support Ticket Image' }}"  class="w-full h-full object-cover rounded-lg shadow-md">
                                </a>
                            
                            @else
                            <a href="{{ $media->getUrl() ?: getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}"
                                target="_blank"
                                class="block w-[70px] h-[70px] border-2 border-gray-200 rounded-lg flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 transition-colors p-2">
                                <img src="{{ asset('images/pdf-icon.svg') }}" alt="PDF" class="w-16 h-16">
                            </a>
                            @endif
                            @endforeach
                        </div>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Card 2 -->
            @if(auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->can('facilities-support_chat'))
            <div class="overflow-hidden rounded bg-white shadow">
                <div id="conversation-header" class="flex justify-between items-center px-5 py-2 border-b">
                    <h2 class="text-lg font-semibold text-gray-900">Conversation</h2>
                    <button @click="window.location.reload()" class="ml-2 px-2 py-1 bg-primary-600 text-white rounded hover:bg-primary-800 text-sm flex items-center">
                        <x-heroicon-o-arrow-path class="h-6 w-6 text-white-500 mr-1" />
                        Refresh For Latest Messages
                    </button>
                </div>

                <!-- Chat Messages -->
                <div class="flex-1 h-96 overflow-y-auto p-4 relative" style="height: 192px;" x-data="{
                            messagesContainer: null,
                            isLoading: false,
                            scrollPosition: 0,
                            wasAtBottom: true,
                            showNewMessageIndicator: false,
                            init() {
                                this.messagesContainer = $refs.messageContainer;
                                this.messagesContainer.addEventListener('scroll', this.onScroll.bind(this));
                                this.scrollToBottom();
                        
                                setInterval(() => {
                                    // Only auto-load new messages if user is near the bottom
                                    if (this.isNearBottom()) {
                                        this.wasAtBottom = true;
                                        @this.call('loadMessages').then(() => {
                                            if (this.wasAtBottom) {
                                                this.scrollToBottom();
                                            }
                                        });
                                    }
                                }, 3000);
                                    
                                window.addEventListener('message-sent', () => {
                                    this.wasAtBottom = true;
                                    setTimeout(() => {
                                        this.scrollToBottom();
                                    }, 100);
                                });
                                
                                window.addEventListener('messages-loaded', (event) => {
                                    if (event.detail && event.detail.preservePosition) {
                                        this.$nextTick(() => {
                                            // Restore scroll position after loading older messages
                                            const newHeight = this.messagesContainer.scrollHeight;
                                            const heightDiff = newHeight - this.scrollHeight;
                                            this.messagesContainer.scrollTop = heightDiff;
                                        });
                                    } else if (this.wasAtBottom) {
                                        // Only scroll to bottom if user was at bottom before loading
                                        this.scrollToBottom();
                                    }
                                });

                                // Listen for new messages from Livewire
                                window.addEventListener('new-message-arrived', () => {
                                    if (!this.isNearBottom()) {
                                        this.showNewMessageIndicator = true;
                                    } else {
                                        this.scrollToBottom();
                                    }
                                });
                            },
                            onScroll() {
                                const scrollTop = this.messagesContainer.scrollTop;
                                
                                // Update wasAtBottom flag
                                this.wasAtBottom = this.isNearBottom();
                                
                                // Store height before loading more messages
                                this.scrollHeight = this.messagesContainer.scrollHeight;
                                
                                // Only trigger load more if we're near the top and not already loading
                                if (scrollTop < 50 && !this.isLoading) {
                                    this.isLoading = true;
                                    this.wasAtBottom = false; // User is viewing older messages
                                    // Store current scroll position
                                    this.scrollPosition = this.messagesContainer.scrollTop;
                                    
                                    @this.call('loadMoreMessages').then(() => {
                                        this.isLoading = false;
                                        this.$wire.dispatchTo('messages-loaded', { preservePosition: true });
                                    }).catch(() => {
                                        this.isLoading = false;
                                    });
                                }
                                // Hide indicator if user scrolls to bottom
                                if (this.isNearBottom()) {
                                    this.showNewMessageIndicator = false;
                                }
                            },
                            isNearBottom() {
                                const threshold = 100; // pixels from bottom
                                return (this.messagesContainer.scrollTop + this.messagesContainer.clientHeight) >= 
                                       (this.messagesContainer.scrollHeight - threshold);
                            },
                            scrollToBottom() {
                                this.$nextTick(() => {
                                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                                    this.showNewMessageIndicator = false;
                                });
                            }
                        }" x-ref="messageContainer" class="scroll-smooth">
                    <!-- Loading indicator -->
                    <div wire:loading.delay.shortest wire:target="loadMoreMessages"
                        class="absolute top-2 left-1/2 transform -translate-x-1/2 z-50 mb-4">
                        <x-filament::loading-indicator class="h-5 w-5" />
                    </div>
                    <!-- New Messages Down Arrow -->
                    <div x-show="showNewMessageIndicator"
                         @click="scrollToBottom(); showNewMessageIndicator = false"
                         class="fixed bottom-24 right-8 z-50 cursor-pointer bg-primary-600 text-white rounded-full shadow-lg p-2 transition-opacity duration-300"
                         style="display: none;"
                         x-transition>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                        </svg>
                    </div>
                    @php
                    $previousDate = null;
                    @endphp

                    @foreach ($messages as $message)
                    @php
                    $rawDate = $message->created_at->toDateString();
                    $messageDate = $message->created_at->format('d M, Y');
                    @endphp

                    @if ($previousDate !== $rawDate)
                    <div class="my-2 text-center">
                        <span class="inline-block px-3 py-1 text-xs text-gray-600 bg-gray-200 rounded-full">
                            {{ $rawDate === now()->toDateString() ? 'Today' : ($rawDate ===
                            now()->subDay()->toDateString() ? 'Yesterday' : $messageDate) }}
                        </span>
                    </div>
                    @php $previousDate = $rawDate; @endphp
                    @endif


                    @php
                    $user = $message->from_id == auth()->id() ? auth()->user() : $message->sender;
                    if ($user->parent_id) {
                        $mainUser = App\Models\User::find($user->parent_id);
                        $user = $mainUser ?: $user; // Fallback to original user if parent not found
                    }
                    $filename = $user->photo ? basename($user->photo) : null;

                    $imagePaths = ['users/', 'profile-photos/'];

                    $photo = null;
                    if ($filename) {
                    foreach ($imagePaths as $path) {
                    if (Storage::disk('s3')->exists($path . $filename)) {
                    $photo = Storage::disk('s3')->url($path . $filename);
                    break;
                    }
                    }
                    }
                    $photo = $photo ?: asset('images/default-avatar.png');
                    @endphp


                    <!-- All messages displayed on left side -->
                    <div class="flex items-start justify-start gap-3 mt-5">
                        <div
                            class="flex items-center justify-center w-6 h-6 mt-1 overflow-hidden bg-gray-400 rounded shadow-sm">
                            <img src="{{ $photo }}" class="object-cover w-full h-full" alt="User Photo">
                        </div>

                        <div class="w-full">
                            <div class="flex items-center mb-0.5">
                                <span class="mr-1 text-sm font-semibold text-gray-800">{{ ucwords($user->name) }}</span>
                                <span class="inline-flex items-center justify-center mx-1 text-xs text-gray-500">
                                    <span class="w-1 h-1 bg-gray-500 rounded-full"></span>
                                </span>
                                <span class="text-xs text-gray-500">

                                    @if($user->timezone)
                                    {{ $message->created_at->timezone($user->timezone)->format('h:i A') }}
                                    @else
                                    {{ $message->created_at->format('h:i A') }}
                                    @endif
                                </span>
                            </div>


                            <!-- Media Display (Images and PDFs) -->
                            @if ($message->hasMedia('support-ticket-images'))
                            @foreach ($message->getMedia('support-ticket-images') as $media)
                            <div class="mb-1">
                                @php
                                $mimeType = $media->mime_type ?? $media->getCustomProperty('mime_type', '');
                                $isImage = strpos($mimeType, 'image/') === 0 || in_array(strtolower($media->file_name), ['jpg', 'jpeg', 'png', 'gif']);
                                $isPdf = strpos($mimeType, 'pdf') !== false;
                                @endphp

                                @if ($isImage)
                                <!-- Display Images -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="inline-block">
                                    <img src="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" class="rounded-lg shadow max-w-[200px]" alt="{{ $media->name }}">
                                </a>
                                @elseif ($isPdf)
                                <!-- Display PDFs -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @else
                                <!-- Display Other Files -->
                                <a href="{{ getImage($media->file_name, config('constants.api.media.support_ticket') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                                    @php
                                    $iconColor = strpos($mimeType, 'word') !== false ? 'text-blue-500' :
                                    (strpos($mimeType, 'excel') !== false ? 'text-green-500' : 'text-gray-500');
                                    @endphp
                                    <x-heroicon-o-document-text class="h-6 w-6 {{ $iconColor }}" />
                                    <span>{{ $media->name }}</span>
                                </a>
                                @endif
                            </div>
                            @endforeach
                            @endif

                            <!-- Text Message -->
                            @if($message->message)
                            <div class="mt-0.5 mb-0.5 w-relative" style="word-break: break-word; max-width: 170ch;">
                                {!! nl2br(e($message->message)) !!}
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Message Input Section -->
                <div class="p-3 pt-5 bg-white border-t rounded-b-lg">
                    @if($ticket->status != 'closed')
                    <div class="flex items-center space-x-2">
                        <!-- File Upload Input -->
                        <input type="file" wire:model="attachedFiles" class="hidden" id="file-upload" multiple
                            accept="image/*,.pdf" @if($ticket->status == 'closed') disabled @endif>
                        <label for="file-upload" style="margin:-8px 0 0 0;"
                            class="cursor-pointer p-2 rounded-full bg-gray-200 @if($ticket->status == 'closed') opacity-50 cursor-not-allowed @endif">
                            <x-heroicon-o-paper-clip class="w-5 h-5 text-gray-500" />
                        </label>

                        <!-- File Preview Section -->
                        @if (!empty($attachedFiles) && $ticket->status != 'closed')
                        <div class="flex my-1 space-x-1">
                            @foreach ($attachedFiles as $index => $file)
                            <div class="relative group">
                                @php
                                $fileType = $file->getMimeType();
                                $isImage = strpos($fileType, 'image/') === 0;
                                @endphp

                                @if ($isImage)
                                <!-- Image Preview -->
                                <img src="{{ $file->temporaryUrl() }}" class="object-cover w-10 h-10 border rounded-lg">
                                @else
                                <!-- Document Preview -->
                                <div class="flex items-center justify-center w-10 h-10 bg-gray-100 border rounded-lg">
                                    @if (strpos($fileType, 'pdf') !== false)
                                    <x-heroicon-o-document-text class="w-6 h-6 text-red-500" />
                                    @else
                                    <x-heroicon-o-document class="w-6 h-6 text-gray-500" />
                                    @endif
                                </div>
                                @endif

                                <!-- File Name Tooltip -->
                                <div
                                    class="absolute -bottom-5 left-0 bg-gray-800 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                    {{ $file->getClientOriginalName() }}
                                </div>

                                <!-- Remove Button -->
                                <button wire:click="removeFile({{ $index }})"
                                    class="absolute p-1 bg-red-500 rounded-full -top-1 -right-1">
                                    <x-heroicon-o-x-mark class="w-4 h-4 text-white" />
                                </button>
                            </div>
                            @endforeach
                        </div>
                        @endif

                        <!-- Message Input -->
                        <div class="relative flex-1">
                            <textarea wire:model.defer="messageText" placeholder="Write your message"
                                class="w-full rounded-lg border border-gray-300 px-3 py-2 pr-12 focus:border-primary-500 focus:ring-primary-500 resize-none @if($ticket->status == 'closed') bg-gray-100 cursor-not-allowed @endif @error('messageText') border-red-500 @enderror"
                                x-data="{
                                        charCount: 0,
                                        maxChars: 500,
                                        adjustHeight() {
                                            this.$el.style.height = '38px';
                                            this.$el.style.height = Math.min(this.$el.scrollHeight, 100) + 'px';
                                            this.$el.style.overflowY = this.$el.scrollHeight > 100 ? 'auto' : 'hidden';
                                        },
                                        countChars() {
                                            this.charCount = this.$el.value.length;
                                            this.adjustHeight();
                                        },
                                        sendMessage(event) {
                                            if (!event.shiftKey && this.charCount <= this.maxChars) {
                                                event.preventDefault();
                                                @this.sendMessage();
                                                this.$el.style.height = '38px';
                                                this.charCount = 0;
                                            }
                                        }
                                    }" x-init="adjustHeight()" @input="countChars()"
                                @keydown.enter="sendMessage($event)" style="height: 38px; overflow-y: hidden"
                                maxlength="500" @if($ticket->status == 'closed') disabled @endif></textarea>
                        </div>

                        <!-- Send Button -->
                        <div class="plane-fs" style="margin:-10px 0 0 10px;">
                            <button type="button" wire:click="sendMessage" id="send-button"
                                class="relative inline-flex items-center justify-center p-2 text-white rounded-full bg-primary-600 disabled:opacity-50"
                                wire:loading.attr="disabled" wire:target="sendMessage" @if($ticket->status == 'closed')
                                disabled @endif>
                                <span wire:loading.remove wire:target="sendMessage">
                                    <x-heroicon-o-paper-airplane class="w-5 h-5" />
                                </span>
                                <span wire:loading wire:target="sendMessage">
                                    <x-filament::loading-indicator class="w-5 h-5" />
                                </span>
                            </button>
                        </div>
                    </div>

                    <div class="ml-5">
                        @error('messageText')
                        <p class="mt-1 text-sm text-red-500 ps-10">{{ $message }}</p>
                        @enderror
                        @error('attachedFiles')
                        <p class="mt-1 text-sm text-red-500 ps-10">{{ $message }}</p>
                        @enderror
                    </div>
                    @endif
                    @if($ticket->status == 'closed')
                    <p class="text-sm text-danger-500 flex justify-center mb-2">This ticket is closed. No further messages or files can be sent.</p>
                    @endif
                </div>
            </div>
            @endif

        </div>
    </div>
</x-filament::page>

<script>
    // Livewire.on('messagePosted', () => {
    //     const chatContainer = document.querySelector('.chat-container');
    //     if (chatContainer) {
    //         chatContainer.scrollTop = chatContainer.scrollHeight; // Scroll to the bottom
    //     }
    // }); 
</script>