@php 
    filament()->getCurrentPanel()->sidebarWidth('0rem');
@endphp
<x-filament-panels::page>
<style>.fi-sidebar-nav{display: none}</style>
    <head>
        <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    </head>
    <div class="flex items-center justify-center h-[calc(100vh-64px)] bg-[#F2F6FA] -m-8 ">
        <div class="p-6 sm:p-12 rounded-2xl shadow shadow-[#0000000F] bg-white max-w-lg mx-10">
          <div class="flex flex-col items-center text-center">
            <div class="">
              <img src="{{ asset('images/icon-reject.png') }}" alt="" width="118" height="118">
            </div>
            <h2 class="my-5 text-2xl text-[#171717] font-bold">Profile Verification Rejected</h2>
            <p class="text-[#737373] text-base mb-5">We regret to inform you that your profile verification was not successful. Please check your registered email for detailed feedback and the reason(s) for rejection.</p>
            @php
                $reason = $this->user->rejection_reason ?? '';
                $shortReason = mb_strimwidth($reason, 0, 20, '...');
            @endphp
            <p 
                class="text-[#737373] text-base mb-5"
                @if(strlen($reason) > 20)
                    title="{{ $reason }}"
                @endif
            >
                "{{ $shortReason }}"
            </p>
            <p class="text-[#737373] text-base">Kindly make the changes and resubmit again.</p>
            <!-- <a href="{{ route('filament.pc.pages.onboarding') }}" class="flex text-white text-sm items-center bg-primary-500 rounded-lg mt-7 py-2 px-4 font-semibold">
              Update Profile
            </a> -->
            <a href="{{ route('filament.pc.pages.review-profile') }}" class="flex text-white text-sm items-center bg-primary-500 rounded-lg mt-7 py-2 px-4 font-semibold">
              Update Profile
            </a>
          </div>
        </div>
      </div>

</x-filament-panels::page>
