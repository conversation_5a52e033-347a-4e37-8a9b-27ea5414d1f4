@php 

    filament()->getCurrentPanel()->sidebarWidth('0rem');
@endphp
<x-filament-panels::page>
    <style>.fi-sidebar-nav{display: none}</style>
    <div class="flex items-center justify-center h-[calc(100vh-64px)] bg-[#F2F6FA] -m-8 ">
        <div class="p-6 sm:p-12 rounded-2xl shadow shadow-[#0000000F] bg-white max-w-lg mx-10">
          <div class="flex flex-col items-center text-center">
            <div class="">
              <img src="{{ asset('images/icon-success.png') }}" alt="" width="118" height="118">
            </div>
            <h2 class="my-5 text-2xl text-[#171717] font-bold">Profile Verified Successfully!</h2>
            <p class="text-[#737373] text-base">Your profile has been successfully verified! You can now enjoy full access to your account and all its features. Thank you for your cooperation.</p>
            
            <!-- Simple form to capture timezone and go to dashboard -->
            <form id="dashboardForm" action="{{ route('pc.first-login-complete') }}" method="GET" style="display: inline;">
                <input type="hidden" id="browserTimezone" name="timezone" value="">
                <button type="submit" class="flex text-white text-sm items-center bg-primary-500 rounded-lg mt-7 py-2 px-4 font-semibold">
                    Go To Dashboard
                </button>
            </form>
          </div>
        </div>
      </div>

    <script>
        // Detect and set timezone on page load
        document.addEventListener('DOMContentLoaded', function() {
            const deprecatedTimezones = {
                'Asia/Calcutta': 'Asia/Kolkata'
            };

            let timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

            // Normalize deprecated timezone names
            if (deprecatedTimezones[timezone]) {
                timezone = deprecatedTimezones[timezone];
            }

            console.log('Detected timezone:', timezone);

            // Set the hidden field value
            const hiddenField = document.getElementById('browserTimezone');
            if (hiddenField) {
                hiddenField.value = timezone;
            }
        });
    </script>

</x-filament-panels::page>
