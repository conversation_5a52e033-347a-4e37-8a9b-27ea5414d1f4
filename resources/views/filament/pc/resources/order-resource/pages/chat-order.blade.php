<x-filament::page>
    <div class="overflow-hidden rounded-t-xl bg-white shadow">
        <div class="flex justify-between items-center px-5 py-2 border-b">
            <h2 class="text-lg font-semibold text-gray-900">Conversation</h2>
        </div>
        <!-- <div class="flex md:flex-row gap-2" style="height: 632px;"> -->
        <!-- Chat Section -->
        <!-- <div class="w-full md:w-3/3 bg-white rounded-lg shadow flex flex-col"> -->
        <!-- Chat Messages -->
        <div class="flex-1 overflow-y-auto p-4 relative" style="height: calc(100vh - 359px);" x-data="{
                            messagesContainer: null,
                            isLoading: false,
                            scrollPosition: 0,
                            wasAtBottom: true,
                            init() {
                                this.messagesContainer = $refs.messageContainer;
                                this.messagesContainer.addEventListener('scroll', this.onScroll.bind(this));
                                this.scrollToBottom();
                        
                                setInterval(() => {
                                    // Only auto-load new messages if user is near the bottom
                                    if (this.isNearBottom()) {
                                        this.wasAtBottom = true;
                                        @this.call('loadMessages').then(() => {
                                            if (this.wasAtBottom) {
                                                this.scrollToBottom();
                                            }
                                        });
                                    }
                                }, 3000);
                                    
                                window.addEventListener('message-sent', () => {
                                    this.wasAtBottom = true;
                                    setTimeout(() => {
                                        this.scrollToBottom();
                                    }, 100);
                                });
                                
                                window.addEventListener('messages-loaded', (event) => {
                                    if (event.detail && event.detail.preservePosition) {
                                        this.$nextTick(() => {
                                            // Restore scroll position after loading older messages
                                            const newHeight = this.messagesContainer.scrollHeight;
                                            const heightDiff = newHeight - this.scrollHeight;
                                            this.messagesContainer.scrollTop = heightDiff;
                                        });
                                    } else if (this.wasAtBottom) {
                                        // Only scroll to bottom if user was at bottom before loading
                                        this.scrollToBottom();
                                    }
                                });
                            },
                            onScroll() {
                                const scrollTop = this.messagesContainer.scrollTop;
                                
                                // Update wasAtBottom flag
                                this.wasAtBottom = this.isNearBottom();
                                
                                // Store height before loading more messages
                                this.scrollHeight = this.messagesContainer.scrollHeight;
                                
                                // Only trigger load more if we're near the top and not already loading
                                if (scrollTop < 50 && !this.isLoading) {
                                    this.isLoading = true;
                                    this.wasAtBottom = false; // User is viewing older messages
                                    // Store current scroll position
                                    this.scrollPosition = this.messagesContainer.scrollTop;
                                    
                                    @this.call('loadMoreMessages').then(() => {
                                        this.isLoading = false;
                                        this.$wire.dispatchTo('messages-loaded', { preservePosition: true });
                                    }).catch(() => {
                                        this.isLoading = false;
                                    });
                                }
                            },
                            isNearBottom() {
                                const threshold = 100; // pixels from bottom
                                return (this.messagesContainer.scrollTop + this.messagesContainer.clientHeight) >= 
                                       (this.messagesContainer.scrollHeight - threshold);
                            },
                            scrollToBottom() {
                                this.$nextTick(() => {
                                    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
                                });
                            }
                        }" x-ref="messageContainer" class="scroll-smooth">
            <!-- Loading indicator -->
            <div wire:loading.delay.shortest wire:target="loadMoreMessages" class="absolute z-50 mb-4 transform -translate-x-1/2 top-2 left-1/2">
               <x-filament::loading-indicator class="w-5 h-5" />
            </div>
            @php
                    $previousDate = null;
                    @endphp

                    @foreach ($messages as $message)
                    @php
                    $rawDate = $message->created_at->toDateString();
                    $messageDate = $message->created_at->format('d M, Y');
                    @endphp

                    @if ($previousDate !== $rawDate)
                    <div class="my-2 text-center">
                        <span class="inline-block px-3 py-1 text-xs text-gray-600 bg-gray-200 rounded-full">
                            {{ $rawDate === now()->toDateString() ? 'Today' : ($rawDate ===
                            now()->subDay()->toDateString() ? 'Yesterday' : $messageDate) }}
                        </span>
                    </div>
                    @php $previousDate = $rawDate; @endphp
                    @endif

                    @php
                    $user = $message->from_id == auth()->id() ? auth()->user() : $message->sender;
                    if ($user->parent_id) {
                        $mainUser = App\Models\User::find($user->parent_id);
                        $user = $mainUser ?: $user; // Fallback to original user if parent not found
                    }
                    $filename = $user->photo ? basename($user->photo) : null;

                    $imagePaths = ['users/', 'profile-photos/'];

                    $photo = null;
                    if ($filename) {
                    foreach ($imagePaths as $path) {
                    if (Storage::disk('s3')->exists($path . $filename)) {
                    $photo = Storage::disk('s3')->url($path . $filename);
                    break;
                    }
                    }
                    }
                    $photo = $photo ?: asset('images/default-avatar.png');
                    @endphp

            <!-- All messages displayed on left side -->
            <div class="flex justify-start items-start gap-3 mt-5">
                <div class="w-6 h-6 rounded bg-gray-400 flex items-center justify-center overflow-hidden shadow-sm mt-1">
                    <img src="{{ $photo }}" class="w-full h-full object-cover" alt="User Photo">
                </div>

                <div class="w-full">
                    <div class="flex items-center mb-0.5">
                        <span class="text-sm font-semibold text-gray-800 mr-1">{{ ucwords($user->name) }}</span>
                        <span class="text-xs text-gray-500 mx-1 inline-flex items-center justify-center">
                            <span class="w-1 h-1 bg-gray-500 rounded-full"></span>
                        </span>
                        <span class="text-xs text-gray-500">

                            @if($user->timezone)
                                {{ $message->created_at->timezone($user->timezone)->format('h:i A') }}
                            @else
                                {{ $message->created_at->format('h:i A') }}
                            @endif
                        </span>
                    </div>

                    <!-- Media Display (Images and PDFs) -->
                    @if ($message->hasMedia('thread-chat-images'))
                    @foreach ($message->getMedia('thread-chat-images') as $media)
                    <div class="mb-1">
                        @php
                        $mimeType = $media->mime_type ?? $media->getCustomProperty('mime_type', '');
                        $isImage = strpos($mimeType, 'image/') === 0 || in_array(strtolower($media->file_name), ['jpg', 'jpeg', 'png', 'gif']);
                        $isPdf = strpos($mimeType, 'pdf') !== false;
                        @endphp

                        @if ($isImage)
                        <!-- Display Images -->
                        <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $ticket->id) }}" target="_blank" class="inline-block">
                            <img src="{{ getImage($media->file_name, config('constants.api.media.thread') . $ticket->id) }}" class="rounded-lg shadow max-w-[200px]" alt="{{ $media->name }}">
                        </a>
                        @elseif ($isPdf)
                        <!-- Display PDFs -->
                        <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                            <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                            <span>{{ $media->name }}</span>
                        </a>
                        @else
                        <!-- Display Other Files -->
                        <a href="{{ getImage($media->file_name, config('constants.api.media.thread') . $ticket->id) }}" target="_blank" class="flex items-center space-x-1 text-black-500 hover:text-blue-700">
                            @php
                            $iconColor = strpos($mimeType, 'word') !== false ? 'text-blue-500' :
                            (strpos($mimeType, 'excel') !== false ? 'text-green-500' : 'text-gray-500');
                            @endphp
                            <x-heroicon-o-document-text class="h-6 w-6 {{ $iconColor }}" />
                            <span>{{ $media->name }}</span>
                        </a>
                        @endif
                    </div>
                    @endforeach
                    @endif

                    <!-- Text Message -->
                    @if($message->message)
                    <div class="mt-0.5 mb-0.5 w-relative" style="word-break: break-word; max-width: 130ch;">
                        {!! nl2br(e($message->message)) !!}
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <!-- Message Input Section -->
        <div class="border-t bg-white rounded-b-lg pt-3 pb-2">
            <div class="flex items-center space-x-3">
                <!-- File Upload Input -->
                <input type="file" style="margin:-8px 0 0 0;" wire:model="attachedFiles" class="hidden" id="file-upload" multiple accept="image/*,.pdf">
                <label for="file-upload" class="cursor-pointer p-2 rounded-full bg-gray-200">
                    <x-heroicon-o-paper-clip class="h-5 w-5 text-gray-500" />
                </label>

                <!-- File Preview Section -->
                @if (!empty($attachedFiles))
                <div class="flex space-x-1 my-1">
                    @foreach ($attachedFiles as $index => $file)
                    <div class="relative group">
                        @php
                        $fileType = $file->getMimeType();
                        $isImage = strpos($fileType, 'image/') === 0;
                        @endphp

                        @if ($isImage)
                        <!-- Image Preview -->
                        <img src="{{ $file->temporaryUrl() }}" class="h-10 w-10 rounded-lg border object-cover">
                        @else
                        <!-- Document Preview -->
                        <div class="h-10 w-10 rounded-lg border flex items-center justify-center bg-gray-100">
                            @if (strpos($fileType, 'pdf') !== false)
                            <x-heroicon-o-document-text class="h-6 w-6 text-red-500" />
                            @else
                            <x-heroicon-o-document class="h-6 w-6 text-gray-500" />
                            @endif
                        </div>
                        @endif

                        <!-- File Name Tooltip -->
                        <div class="absolute -bottom-5 left-0 bg-gray-800 text-white text-xs px-1 py-0.5 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                            {{ $file->getClientOriginalName() }}
                        </div>

                        <!-- Remove Button -->
                        <button wire:click="removeFile({{ $index }})" class="absolute -top-1 -right-1 bg-red-500 rounded-full p-1">
                            <x-heroicon-o-x-mark class="h-4 w-4 text-white" />
                        </button>
                    </div>
                    @endforeach
                </div>
                @endif

                <!-- Message Input -->
                <div class="flex-1 relative">
                    <textarea wire:model.defer="messageText" placeholder="Write your message"
                        class="w-full rounded-lg border border-gray-300 px-3 py-2 pr-12 focus:border-primary-500 focus:ring-primary-500 resize-none"
                        x-data="{
                                    charCount: 0,
                                    maxChars: 500,
                                    adjustHeight() {
                                        this.$el.style.height = '38px';
                                        this.$el.style.height = Math.min(this.$el.scrollHeight, 100) + 'px';
                                        this.$el.style.overflowY = this.$el.scrollHeight > 100 ? 'auto' : 'hidden';
                                    },
                                    countChars() {
                                        this.charCount = this.$el.value.length;
                                        this.adjustHeight();
                                    },
                                    sendMessage(event) {
                                        if (!event.shiftKey && this.charCount <= this.maxChars) {
                                            event.preventDefault();
                                            @this.sendMessage();
                                            this.$el.style.height = '38px';
                                            this.charCount = 0;
                                        }
                                    }
                                }"
                        x-init="adjustHeight()"
                        @input="countChars()"
                        @keydown.enter="sendMessage($event)"
                        style="height: 38px; overflow-y: hidden"
                        maxlength="500">
                            </textarea>
                </div>

                <!-- Send Button -->
                <div class="plane-fs pe-2" style="margin:-10px 0 0 10px;">
                    <button type="button" wire:click="sendMessage" id="send-button"
                        class="inline-flex items-center justify-center rounded-full bg-primary-600 p-2 text-white relative disabled:opacity-50"
                        wire:loading.attr="disabled" wire:target="sendMessage">
                        <span wire:loading.remove wire:target="sendMessage">
                            <x-heroicon-o-paper-airplane class="h-5 w-5" />
                        </span>
                        <span wire:loading wire:target="sendMessage">
                            <x-filament::loading-indicator class="h-5 w-5" />
                        </span>
                    </button>
                </div>
            </div>
            <div class="ml-5">
                @error('messageText')
                <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                @enderror
                @error('attachedFiles')
                <p class="text-red-500 text-sm mt-1 ps-10">{{ $message }}</p>
                @enderror
            </div>
        </div>
        <!-- </div> -->
    </div>
</x-filament::page>