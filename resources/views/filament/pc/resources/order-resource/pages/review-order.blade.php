<x-filament-panels::page>
    <form wire:submit.prevent="submitOrder">
        <div class="flex justify-between gap-8">
            <div
                class="flex-grow overflow-hidden bg-white shadow-sm fi-section rounded-xl ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
                <div class="flex flex-col gap-3 px-6 py-4 fi-section-header">
                    <div class="flex items-center justify-between gap-4">
                        <div class="flex items-center gap-5">
                            <h3 class="text-lg font-semibold leading-6 text-gray-900 dark:text-white">
                                Items
                            </h3>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-200 fi-section-content-ctn dark:border-white/10">
                    <!-- Product listing -->
                    <div class="space-y-4">
                        <div wire:loading wire:target="selectedBatches" class="flex justify-center items-center py-4">
                            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                            <span class="ml-2">Loading batch...</span>
                        </div>
                        @foreach ($order->orderProducts as $index => $product)
                            @php
                            // dd($product);
                                $productData = $productsData[$product->product_id] ?? [];
                                $isBatchWiseStock = $productData['isBatchWiseStock'] ?? false;
                                $productStock = $productData['productStock'] ?? 0;
                                $batches = $productData['batches'] ?? collect([]);
                            @endphp

                            <div x-data="{ open: true }" class="accordion" wire:key="product-{{ $index }}-{{ $product->product_id }}">
                                <!-- Accordion Header -->
                                <div class="relative flex justify-between items-center p-4 cursor-pointer bg-[#FAFAFA] border-t border-[#E5E5E5]"
                                    @click="open = !open">
                                    <div class="flex items-center gap-3">
                                        <div
                                            class="inline-flex justify-center items-center w-9 h-9 bg-[#F5F5F5] rounded-md p-2 shrink-0">
                                            <img src="https://picsum.photos/200/300" width="20" height="20"
                                                class="object-contain w-5 h-5" alt="Product img" />
                                        </div>
                                        <span>{{ $product->product->name ?? '-' }}</span>
                                        {{-- <span>Quantity :- ({{ $product->quantity ?? '-' }}{{ ($product->bonus_final_qty) ? '+'.$product->bonus_final_qty:'' }})</span> --}}
                                    </div>

                                    <!-- Accordion Arrow -->
                                    <span :class="{ 'rotate-180': open }"
                                        class="arrow transition-transform duration-300">
                                        &#9660;
                                    </span>

                                    <!-- Badge for stock status -->
                                    @if (!$isBatchWiseStock && $product->quantity > $productStock)
                                        <div
                                            class="stock-label absolute right-2 top-2 py-1 px-3 text-xs font-semibold rounded-full bg-red-100 text-red-600">
                                            <span class="badge">InSufficient stock</span>
                                        </div>
                                    @endif

                                    <!-- Show Batch Detail Not Found -->
                                    @if ($isBatchWiseStock && $batches->count() == 0)
                                        <div
                                            class="batch-label absolute right-2 top-2 py-1 px-3 text-xs font-semibold rounded-full bg-orange-200 text-orange-700">
                                            <span class="batch-badge">Batch detail Pending</span>
                                        </div>
                                    @endif
                                </div>

                                <!-- Accordion Content -->
                                <div x-show="open" class="px-4 py-3 bg-gray-100 space-y-3">
                                    @if (isset($validationErrors["product.{$index}"]))
                                        <div class="text-red-500 text-sm mb-2 error">
                                            {{ $validationErrors["product.{$index}"] }}
                                        </div>
                                    @endif

                                    @if (isset($validationErrors["product.{$index}.total"]))
                                        <div class="text-red-500 text-sm mb-2 error">
                                            {{ $validationErrors["product.{$index}.total"] }}
                                        </div>
                                    @endif

                                    @if (isset($validationErrors["product.{$index}.stock"]))
                                        <div class="text-red-500 text-sm mb-2 error">
                                            {{ $validationErrors["product.{$index}.stock"] }}
                                        </div>
                                    @endif

                                    <div class="flex gap-4">
                                        <div class="flex-1 ">
                                            <label class="block text-sm font-medium text-gray-700">Order
                                                Quantity :- {{ $product->quantity+$product->bonus_final_qty ?? '-' }}{{ ($product->bonus_final_qty) ? ' ('.$product->quantity.'+'.$product->bonus_final_qty .' Free)':'' }}</label>

                                            <input type="number" value="{{ $product->quantity+$product->bonus_final_qty }}"
                                                class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                disabled>
                                        </div>

                                        @if ($isBatchWiseStock)
                                            <div class="flex-1 relative">
                                                <label class="block text-sm font-medium text-gray-700">Batch Number<span
                                                        style="color: red;">*</span></label>
                                                <select placeholder="Select Batch" x-data="{
                                                    productId: {{ $product->product_id }},
                                                    initTomSelect() {
                                                        const config = {
                                                            plugins: ['remove_button'],
                                                            dropdownParent: 'body',
                                                            onDelete: (values) => {
                                                                @this.call('handleBatchSelection', {{ $index }}, this.productId);
                                                            },
                                                            onChange: (values) => {
                                                                @this.call('handleBatchSelection', {{ $index }}, this.productId);
                                                            }
                                                        };
                                                        new TomSelect(this.$el, config);
                                                    }
                                                }"
                                                    x-init="initTomSelect()" multiple
                                                    wire:model="selectedBatches.{{ $index }}"
                                                    class="tom-select-batch-{{ $index }} w-full">
                                                    @foreach ($batches as $batchIndex => $batch)
                                                        <option value="{{ $batchIndex }}">
                                                            {{ $batch->batch_name }} </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        @else
                                            <div class="flex-1">
                                                <label class="block text-sm font-medium text-gray-700">Available
                                                    Stock</label>
                                                <input type="number" value="{{ $productStock }}"
                                                    class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                    disabled>
                                            </div>

                                        @endif
                                    </div>

                                    @if ($isBatchWiseStock && $batches->count() > 0)
                                        <div wire:loading.remove wire:target="selectedBatches">
                                            @foreach ($batches as $batchIndex => $batch)
                                                @if (in_array($batchIndex, $selectedBatches[$index] ?? []))
                                                    <div class="flex gap-4" wire:key="batch-section-{{ $index }}-{{ $batchIndex }}">
                                                        <div class="flex-1">
                                                            <label
                                                                class="block text-sm font-medium text-gray-700">{{ ($batch->batch_name) }}
                                                                Available Stock</label>
                                                            <input type="number" value="{{ $batch->available_stock }}"
                                                                class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                                disabled>
                                                        </div>
                                                        <div class="flex-1">
                                                            <label
                                                                class="block text-sm font-medium text-gray-700">{{ ucfirst($batch->batch_name) }}
                                                                Expiry Date</label>
                                                            <input type="text" value="{{ getFormatedDate($batch->expiry_date) }}"
                                                                class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                                disabled>
                                                        </div>
                                                        <div class="flex-1">
                                                            <label
                                                                class="block text-sm font-medium text-gray-700">Assign
                                                                Quantity<span style="color: red;">*</span></label>
                                                            <input type="number"
                                                                wire:model.live="batchQuantities.{{ $index }}.{{ $batchIndex }}"
                                                                wire:key="batch-qty-{{ $index }}-{{ $batchIndex }}"
                                                                class="mt-1 p-2 w-full rounded-md border-gray-300 @error('batchQuantities.' . $index . '.' . $batchIndex) border-red-500 @enderror"
                                                                min="0" max="{{ $batch->available_stock }}"
                                                                oninput="this.value = this.value.replace(/[^0-9]/g, '')">
                                                            @if (isset($validationErrors["product.{$index}.batch.{$batchIndex}"]))
                                                                <p class="mt-1 text-sm text-red-500 error">
                                                                    {{ $validationErrors["product.{$index}.batch.{$batchIndex}"] }}
                                                                </p>
                                                            @endif
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                    @elseif($isBatchWiseStock && $batches->count() == 0)
                                        <div class="flex gap-4">
                                            <div class="flex-1">
                                                <label class="block text-sm font-medium text-gray-700">Available
                                                    Stock</label>
                                                <input type="number" value="-"
                                                    class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                    disabled>
                                            </div>
                                            <div class="flex-1">
                                                <label class="block text-sm font-medium text-gray-700">Assign
                                                    Quantity<span style="color: red;">*</span></label>
                                                <input type="number" value="-"
                                                    class="mt-1 p-2 w-full rounded-md border-gray-300 cursor-not-allowed"
                                                    disabled>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

    </form>

    <!-- Invoice Modal -->
    <div x-data="{ show: @entangle('showInvoiceModal') }" x-show="show" class="fixed inset-0 z-50 overflow-y-auto"
        style="background-color: rgba(0, 0, 0, 0.5);" x-cloak>
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center">
            <div x-show="show" x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="relative inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">

                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">
                        Accept Order #{{ $this->order->order->order_number }}
                    </h3>
                    <button @click="show = false" class="text-gray-400 hover:text-gray-500">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                <form wire:submit.prevent="saveInvoice" class="space-y-4">
                    <div class="relative">
                        <label for="invoiceNumber" class="block text-sm font-medium text-gray-700 flex items-center">
                            Invoice Number<span style="color: red;">*</span>
                            <div class="relative ml-1" x-data="{ showTooltip: false }">
                                <svg @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                    class="w-6 h-6 text-gray-500 cursor-pointer" xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24" fill="none" stroke="gray" stroke-width="2"
                                    stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="8"></line>
                                    <!-- Small dot inside -->
                                    <line x1="12" y1="12" x2="12" y2="16"></line>
                                    <!-- Vertical line inside -->
                                </svg>
                                <!-- Tooltip -->
                                <div x-show="showTooltip" x-transition
                                    class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2
                                           bg-gray-800 text-white font-semibold text-xs font-semibold rounded-md py-1 px-2
                                           whitespace-nowrap shadow-lg z-50">
                                    Enter a valid invoice number
                                </div>
                            </div>
                        </label>
                        <input type="text" id="invoiceNumber" wire:model="invoiceNumber"
                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="Enter invoice number">
                        @error('invoiceNumber')
                            <p class="mt-1 text-sm text-red-600 error">{{ $message }}</p>
                        @enderror
                    </div>



                    <div>
                        <label for="invoiceUpload" class="block cursor-pointer">
                            <div
                                class="mt-1 flex items-center justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:bg-gray-50 transition">
                                <div class="space-y-1 text-center">
                                    @if ($invoiceFile)
                                        <div class="text-sm text-gray-600 mb-2 break-all">
                                            Selected file: {{ $invoiceFile->getClientOriginalName() }}
                                        </div>
                                        @if (Str::startsWith($invoiceFile->getMimeType(), 'image/'))
                                            <div class="mb-2">
                                                @php
                                                    try {
                                                        $imageContent = base64_encode(file_get_contents($invoiceFile->getRealPath()));
                                                        $mimeType = $invoiceFile->getMimeType();
                                                        $previewUrl = "data:{$mimeType};base64,{$imageContent}";
                                                    } catch (\Exception $e) {
                                                        $previewUrl = null;
                                                    }
                                                @endphp

                                                @if($previewUrl)
                                                    <img src="{{ $previewUrl }}" alt="Preview"
                                                        class="mx-auto h-32 object-contain rounded border border-gray-300" />
                                                @else
                                                    <div class="mx-auto h-32 flex items-center justify-center border border-gray-300 rounded bg-gray-50">
                                                        <span class="text-gray-500 text-sm">Preview not available</span>
                                                    </div>
                                                @endif
                                            </div>
                                        @endif

                                        <button type="button"
                                            wire:click="removeInvoiceFile"
                                            class="text-xs text-red-600 hover:underline focus:outline-none">
                                            Remove file
                                        </button>
                                    @endif

                                    <div class="flex text-sm text-gray-600 justify-center">
                                        <span class="rounded-md font-medium text-primary-600 hover:text-primary-500">
                                            {{ $invoiceFile ? 'Change file' : 'Upload a file' }}
                                        </span>
                                        @if (!$invoiceFile)
                                            <p class="pl-1">or drag and drop</p>
                                        @endif
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        PDF, PNG, JPG up to 10MB
                                    </p>
                                </div>
                            </div>
                            <input id="invoiceUpload" wire:model="invoiceFile" type="file"
                                class="sr-only" accept=".pdf,.jpg,.jpeg,.png">
                        </label>


                        @error('invoiceFile')
                            <p class="mt-1 text-sm text-red-600 error">{{ $message }}</p>
                        @enderror

                        <!-- Upload Progress -->
                        <div wire:loading wire:target="invoiceFile" class="mt-2">
                            <div class="flex items-center justify-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-600"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span class="text-sm text-gray-500">Uploading file...</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end gap-3 mt-6">

                        <button type="submit" wire:loading.attr="disabled"
                            wire:loading.class="opacity-50 cursor-not-allowed"
                            class="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span wire:loading.remove wire:target="saveInvoice">Accept</span>
                            <span wire:loading wire:target="saveInvoice" class="inline-flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                Saving...
                            </span>
                        </button>
                        <button type="button" @click="show = false"
                            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <style>
        [x-cloak] {
            display: none !important;
        }

        .arrow {
            display: inline-block;
            font-size: 18px;
            transform-origin: center;
            transition: transform 0.3s ease-in-out;
        }

        .rotate-180 {
            transform: rotate(180deg);
        }

        .stock-label {
            position: absolute;
            right: 45px;
            top: 2px;
        }

        .batch-label {
            position: absolute;
            right: 45px;
            top: 2px;
        }

        .batch-badge {
            background-color: orange;
            color: white;
            padding: 4px 8px;
            text-align: center;
            border-radius: 5px;
        }

        .badge {
            background-color: red;
            color: white;
            padding: 4px 8px;
            text-align: center;
            border-radius: 5px;
        }

        input[disabled] {
            background-color: #e5e7eb;
            cursor: not-allowed;
        }

        .error {
            color: red;
        }

        select[multiple] option {
            padding: 8px 12px;
            margin: 2px 0;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        select[multiple] option:checked {
            background: linear-gradient(0deg, rgba(var(--primary-500), 0.1), rgba(var(--primary-500), 0.1));
            color: rgb(var(--primary-600));
            font-weight: 500;
        }

        select[multiple]:focus option:checked {
            background: linear-gradient(0deg, rgba(var(--primary-500), 0.2), rgba(var(--primary-500), 0.2));
        }
    </style>
    @push('scripts')
        <link href="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/css/tom-select.css" rel="stylesheet">
        <script src="https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/js/tom-select.complete.min.js"></script>
        <style>
            .ts-wrapper {
                &:after {
                    content: '';
                    background: url({{ asset('images/downarrow.svg') }}) no-repeat right center;
                    display: inline-block;
                    width: 16px;
                    height: 16px;
                    background-size: 16px;
                    position: absolute;
                    right: 10px;
                    bottom: 50%;
                    transform: rotate(180deg) translateY(-50%);
                    z-index: 1;
                }

            }
        </style>
        <script>
            document.addEventListener('alpine:init', () => {
                Alpine.data('tomSelect', (initialValue) => ({
                    instance: null,
                    selected: initialValue,
                    initTomSelect() {
                        const selectElement = this.$el;
                        this.instance = new TomSelect(selectElement, {
                            plugins: ['remove_button'],
                            maxItems: null,
                            dropdownParent: 'body',
                            render: {
                                option: (data, escape) => {
                                    return `<div class="py-2 px-3">${escape(data.text)}</div>`;
                                },
                                item: (data, escape) => {
                                    return `<div class="bg-primary-100 text-primary-700 rounded px-2 py-1 mr-1 inline-block">${escape(data.text)}</div>`;
                                }
                            },
                            onDelete: () => {
                                this.selected = this.instance.getValue();
                                this.$wire.set('selectedBatches.{{ $index }}', this
                                    .selected);
                            },
                            onChange: () => {
                                this.selected = this.instance.getValue();
                                this.$wire.set('selectedBatches.{{ $index }}', this
                                    .selected);
                                this.$wire.call('handleBatchSelection', {{ $index }});
                            }
                        });
                    }
                }));
            });
        </script>
    @endpush
</x-filament-panels::page>
