<div class="container p-6 bg-white hmx-auto max-h-100">
    @php
        $files = is_array($filePath) ? $filePath : [$filePath];
        $changes = $changes ?? [];
        $imagePath = config('constants.api.media.pc_medias').$record->id;

        $changeFiles = [];
        if (!empty($changes)) {
            $changeFiles = is_array($changes) ? array_filter($changes, 'is_string') : (is_string($changes) ? [$changes] : []);
        }
    @endphp

    {{-- Always check for company_registration_certificate first --}}
    @if ($name === 'company_registration_certificate')
    
        <section class="section">
            <h2 class="text-xl font-semibold mb-4">Company Registration Certificate</h2>
            @foreach($files as $file)
                @php
                    $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                    $isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                    $isPdf = $fileExtension === 'pdf';
                    $imageName = basename($file);
                @endphp
                <div class="mb-4">
            @if ($isImage)
               
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm">{{ Str::limit(basename($imageName), 20) }}</span>
                        <a href="{{ getImage($imageName,$imagePath) }}" target="_blank"
                            class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                            title="View">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                        </a>
                    </div>
            @elseif ($isPdf)
                
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white"
                                  stroke="black"/>
                            <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em"
                                  font-weight="bold">PDF</text>
                        </svg>
                        <span class="text-sm">{{ Str::limit(basename($imageName), 20) }}</span>
                        <a href="{{ getImage($imageName,$imagePath) }}" target="_blank"
                            class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                            title="View">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                        </a>
                    </div>
                
            @else
                <p class="font-medium text-center text-red-500">{{ empty($filePath) ? 'No Document Uploaded' : 'Unsupported file type.' }}</p>
            @endif
            </div>
            @endforeach
            @if(!empty($changeFiles))
                <div style='background-color: yellow;'>
                    @foreach($changeFiles as $changeFile)
                        @php
                            $changeExtension = pathinfo($changeFile, PATHINFO_EXTENSION);
                            $isChangeImage = in_array(strtolower($changeExtension), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                            $isChangePdf = strtolower($changeExtension) === 'pdf';
                            $changeName = basename($changeFile);
                        @endphp
                        
                        @if ($isChangeImage || $isChangePdf)
                            <div class="flex items-center gap-2 mb-2">
                                @if ($isChangeImage)
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                @else
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white" stroke="black"/>
                                        <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em" font-weight="bold">PDF</text>
                                    </svg>
                                @endif
                                <span class="text-sm">{{ $changeName }}</span>
                                <a href="{{ getImage($changeName, $imagePath) }}" target="_blank" class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100" title="View">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                            </div>
                        @else
                            <p class="font-medium text-center text-red-500">Unsupported file type: {{ $changeName }}</p>
                        @endif
                    @endforeach
                </div>
            @endif
        </section>
    @endif

    {{-- Then check for license_permit --}}
    @if ($name === 'license_permit')
    
        <section class="section">
            <h2 class="text-xl font-semibold mb-4">Relevant certification</h2>
            @foreach($files as $file)
                @php
                    $fileExtension = pathinfo($file, PATHINFO_EXTENSION);
                    $isImage = in_array($fileExtension, ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                    $isPdf = $fileExtension === 'pdf';
                    $imageName = basename($file);
                @endphp
            <div class="mb-4">
            @if ($isImage)                
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span class="text-sm">{{ Str::limit(basename($imageName), 20) }}</span>
                        <a href="{{ getImage($imageName,$imagePath) }}" target="_blank"
                            class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                            title="View">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                        </a>
                    </div>
                
            @elseif ($isPdf)
                
                    <div class="flex items-center gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none"
                             stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white"
                                  stroke="black"/>
                            <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em"
                                  font-weight="bold">PDF</text>
                        </svg>
                        <span class="text-sm">{{ Str::limit(basename($imageName), 20) }}</span>
                        <a href="{{ getImage($imageName,$imagePath) }}" target="_blank"
                            class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100"
                            title="View">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                            </svg>
                        </a>
                    </div>
                
            @else
                <p class="font-medium text-center text-red-500">{{ empty($filePath) ? 'No Document Uploaded' : 'Unsupported file type.' }}</p>
            @endif
            </div>
            @endforeach
            @if(!empty($changeFiles))
                <div style='background-color: yellow;'>
                    @foreach($changeFiles as $changeFile)
                        @php
                            $changeExtension = pathinfo($changeFile, PATHINFO_EXTENSION);
                            $isChangeImage = in_array(strtolower($changeExtension), ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']);
                            $isChangePdf = strtolower($changeExtension) === 'pdf';
                            $changeName = basename($changeFile);
                        @endphp
                        
                        @if ($isChangeImage || $isChangePdf)
                            <div class="flex items-center gap-2 mb-2">
                                @if ($isChangeImage)
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                @else
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white" stroke="black"/>
                                        <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em" font-weight="bold">PDF</text>
                                    </svg>
                                @endif
                                <span class="text-sm">{{ $changeName }}</span>
                                <a href="{{ getImage($changeName, $imagePath) }}" target="_blank" class="p-2 text-blue-600 hover:text-primary-600 rounded-full hover:bg-blue-100" title="View">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                    </svg>
                                </a>
                            </div>
                        @else
                            <p class="font-medium text-center text-red-500">Unsupported file type: {{ $changeName }}</p>
                        @endif
                    @endforeach
                </div>
            @endif
        </section>
    @endif

    {{-- If no document is uploaded or unsupported file type --}}
    @if (!in_array($name, ['company_registration_certificate', 'license_permit']))
        <p class="font-medium text-center text-red-500">{{ empty($filePath) ? 'No Document Uploaded' : 'Unsupported file type.' }}</p>
    @endif
</div>