<div class="rounded-2xl border border-[#E5E5E5] overflow-hidden">
    <div class="px-4 py-4 text-base font-semibold text-gray-900 bg-white">Items</div>

    <div class="overflow-hidden rounded-b-2xl">
        <table class="w-full border-collapse">
            <thead>
                <tr class="bg-[#FAFAFA] border-t border-[#E5E5E5]">
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Product Name</th>
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Batch (Quantity)</th>
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Pricing Type</th>
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Admin Fees</th>
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Quantity</th>
                    {{-- <th class="p-4 text-sm font-semibold text-left text-gray-700">Bonus Quantity</th> --}}
                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Stock Type</th>

                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Wholesale Pack Size</th>

                    <th class="p-4 text-sm font-semibold text-left text-gray-700">Total</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($order->orderProducts->load('product') as $product)
                @php
                            if ($product->bonus_final_qty > 0) {
                                $calQty = $product->quantity + $product->bonus_final_qty;
                                $finalQty = $calQty . ' ('.$product->quantity .'+ '.$product->bonus_final_qty.' Free)';
                            }else{
                                $finalQty = $calQty = $product->quantity;
                            }
                            $wps = ($product->stock_type == 'wps' && $product->wholesale_pack_size) ?
                            trans('api.clinic.cart.wolesale_size') . $product->wholesale_pack_size * $calQty. ' ' . \Illuminate\Support\Str::plural($product->product->container->name) . ' ('. ($product->wholesale_pack_size  * $product->product->quantity_per_unit * $calQty) . ' ' . \Illuminate\Support\Str::plural($product->product->foam->name).')' :
                                '-';

                if(!empty($product?->product?->productDataForPc(getUser(auth()->user())->id)?->productRelationStock?->wholesale_pack_size)){
                    $packVal = $product?->product?->productDataForPc(getUser(auth()->user())->id)?->productRelationStock?->wholesale_pack_size;
                }else{
                    $packVal = '-';
                }

                @endphp
                <tr class="bg-white border-t border-[#E5E5E5]">
                        <td class="px-4 py-3 text-sm text-gray-800">
                            <div class="flex items-center gap-3">
                                <div class="w-9 h-9 bg-[#F5F5F5] rounded-md flex items-center justify-center">
                                    <img src="https://picsum.photos/200/300" alt="Product img" class="object-contain w-5 h-5">
                                </div>
                                <a href="{{ route('filament.pc.resources.products.view', ['record' => $product->product->id]) }}" class="text-blue-600 hover:underline">
                                    <span>{{ $product->product->name }}</span>
                                </a>
                            </div>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            {{ $product->orderProductBatches
                                ->filter(fn($batch) => $batch->productBatchInfo && $batch->productBatchInfo->batch_name)
                                ->map(function ($batch) {
                                    return $batch->productBatchInfo->batch_name . ' (Quantity : ' . $batch->assign_stock . ')';
                                })
                                ->implode(', ') ?? '-' }}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            {{ ucfirst($product->price_type) ?? '-' }}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            {{ isset($product->total_commission) ? 'RM ' . number_format($product->total_commission, 0) : '-' }}
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            {{ $product->quantity + $product->bonus_final_qty }}
                            @if($product->bonus_final_qty)
                            ({{ $product->quantity }} {{ ($product->bonus_final_qty)? '+ '.$product->bonus_final_qty .' Free' : '' }})
                            @endif
                            {{($product->product?->container?->name)?  $product->product?->container?->name : '-' }}</td>
                        {{-- <td class="px-4 py-3 text-sm text-gray-800">{{ ($product->bonus_final_qty)? $product->bonus_final_qty : '-' }}</td> --}}
                        <td class="px-4 py-3 text-sm text-gray-800">{{ ($product->stock_type) ? ($product->stock_type == 'wps') ? 'Wholesale Pack Size' :ucfirst($product->stock_type)   : '-' }}</td>
                        <td class="px-4 py-3 text-sm text-gray-800">{{ $wps}}</td>
                        <td class="px-4 py-3 text-sm text-gray-800">
                            RM {{ $product->total_price }}
                        </td>
                    </tr>
                @endforeach

                <tr class="border-t border-[#E5E5E5] bg-white">
                    <td colspan="6"></td>
                    <td class="px-4 py-3 text-sm font-semibold text-gray-900">Total</td>
                    <td class="px-4 py-3 text-sm font-semibold text-gray-900">
                        RM {{ $order->total_sub_order_value }}
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
