@php
    $fileName = $getRecord()->invoice_path;
                    $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                    $disk = Storage::disk('s3');
                    $filePath = $invoicePath . $fileName;
    $isPdf = $fileName && pathinfo($fileName, PATHINFO_EXTENSION) === 'pdf';
    $fileNewName =$fileName;
    if (Storage::disk('s3')->exists($filePath)) {
    $url = Storage::disk('s3')->temporaryUrl(
        $filePath,
        now()->addMinutes(15),
        [
            'ResponseContentDisposition' => 'attachment',
        ]
    );
} else {
    $url = null; // or handle as needed
}
@endphp

@if (!empty($url) && !empty($fileNewName))
<a
    href="{{ $url }}"
    class="text-primary-600 hover:text-primary-800"
>
    <div>
        <button type="button" wire:click="mountAction('downloadInvoice')"
            class="flex items-center gap-2 text-primary-600 hover:text-primary-500">
            @if ($isPdf)
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M14 2H6a2 2 0 00-2 2v12a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm0 0L18 8M14 2v6h6" fill="white"
                        stroke="black" />
                    <text x="50%" y="50%" text-anchor="middle" fill="red" font-size="5" dy=".3em"
                        font-weight="bold">PDF</text>
                </svg>
            @else
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
            @endif
            <span class="text-sm">{{ $fileNewName }}</span>
        </button>
    </div>
</a>
@else
    <div class="text-sm text-gray-500">Purchase Order is being generated. Please check back shortly</div>
@endif
