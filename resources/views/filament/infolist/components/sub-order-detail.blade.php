<table class="w-full">

    <thead>
        <tr class="bg-[#FAFAFA] border-t border-[#E5E5E5]">
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Product Name</th>
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Pricing Type</th>
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Quantity</th>
            {{-- <th scope="col" class="p-4 font-semibold text-left text-sm/6">Bonus Quantity</th> --}}

            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Stock Type</th>
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Wholesale Pack Size</th>
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Admin Fee</th>
            <!-- <th scope="col" class="p-4 font-semibold text-left text-sm/6">Earned DP Points</th> -->
            <th scope="col" class="p-4 font-semibold text-left text-sm/6">Order Total</th>
        </tr>
    </thead>
    @php
        $suborder = $suborder->loadMissing(['orderProducts', 'orderProducts.product']);
    @endphp
    <tbody>
        @foreach ($suborder->orderProducts as $product)
        @php
        if ($product->bonus_final_qty > 0) {
                                $calQty = $product->quantity + $product->bonus_final_qty;
                                $finalQty = $calQty . ' ('.$product->quantity .'+ '.$product->bonus_final_qty.' Free)';
                            }else{
                                $finalQty = $calQty = $product->quantity;
                            }
          $wps = ($product->stock_type == 'wps' && $product->wholesale_pack_size) ?
                            trans('api.clinic.cart.wolesale_size') . $product->wholesale_pack_size * $calQty. ' ' . \Illuminate\Support\Str::plural($product->product->container->name) . ' ('. ($product->wholesale_pack_size  * $product->product->quantity_per_unit * $calQty) . ' ' . \Illuminate\Support\Str::plural($product->product->foam->name).')' :
                                '-';
        @endphp
            <tr class="bg-white border-t border-[#E5E5E5]">
                <td class="px-4 py-3 text-sm/6">
                    <div class="flex items-center gap-3">
                        <div
                            class="inline-flex justify-center items-center w-9 h-9 bg-[#F5F5F5] rounded-md p-2 shrink-0">
                            <img src="https://picsum.photos/200/300" width="20" height="20"
                                class="object-contain w-5 h-5" alt="Product img" />
                        </div>
                        <span>{{ $product->product->name ?? '-' }}</span>
                    </div>
                </td>
                <td class="px-4 py-3 text-sm/6">{{ ucfirst($product->price_type) ?? '-' }}
                </td>
                <td class="px-4 py-3 text-sm/6"> {{ $product->quantity + $product->bonus_final_qty }}
                    @if($product->bonus_final_qty)
                    ({{ $product->quantity }} {{ ($product->bonus_final_qty)? '+ '.$product->bonus_final_qty .' Free' : '' }})
                    @endif
                    {{($product->product?->container?->name)?  $product->product?->container?->name : '-' }}</td>
                {{-- <td class="px-4 py-3 text-sm/6">{{ ($product->bonus_final_qty)? $product->bonus_final_qty : '-' }}</td> --}}
                <td class="px-4 py-3 text-sm/6">{{ ($product->stock_type) ? ($product->stock_type == 'wps') ? 'Wholesale Pack Size' :ucfirst($product->stock_type)   : '-' }}</td>
                <td class="px-4 py-3 text-sm/6">{{ $wps }}</td>
                <td class="px-4 py-3 text-sm/6">
                    {{ isset($product->total_commission) ? 'RM ' . $product->total_commission : '-' }}
                </td>
                <td class="px-4 py-3 text-sm/6">
                    RM {{ number_format($product->total_price, 2) }}
                </td>
            </tr>
        @endforeach

        <tr class="bg-white border-t border-[#E5E5E5]">
            <td colspan="5">&nbsp;</td>
            <td class="px-4 py-3 text-sm/6" colspan="1">Order Value</td>
            <td class="px-4 py-3 text-sm/6">
                RM {{ number_format($suborder->total_sub_order_value ?? 0, 2) }}
            </td>
        </tr>
        <tr class="bg-white border-t border-[#E5E5E5]">
            <td colspan="5">&nbsp;</td>
            <td class="px-4 py-3 text-sm/6" colspan="1">Shipping</td>
            <td class="px-4 py-3 text-sm/6">RM {{number_format($suborder->total_shipping_amount ?? 0, 2)}}</td>
        </tr>
        <tr class="bg-white border-t border-[#E5E5E5]">
            <td colspan="5">&nbsp;</td>
            <td class="px-4 py-3 text-sm/6" colspan="1">Dpharma Points</td>
            <td class="px-4 py-3 text-sm/6">
                -RM {{ number_format($suborder->total_dpharma_points_used ?? 0, 2) }}
            </td>
        </tr>
        <tr class="bg-white border-t border-[#E5E5E5]">
            <td colspan="5">&nbsp;</td>
            <td class="px-4 py-3 font-semibold text-sm/6" colspan="1">Order Amount</td>
            <td class="px-4 py-3 font-semibold text-sm/6">
                RM {{ number_format($suborder->total_amount ?? 0, 2) }}
            </td>
        </tr>
    </tbody>
</table>
