@php
$payoutId = $getRecord()?->payoutSubOrder?->Payout?->id;

if ($payoutId) {
    $orderFileName = 'payout_' . $payoutId . '_sub_order_' . $getRecord()->id . '.pdf';
    $filePath = config('constants.api.payout_pdf.order_payout_path') . $orderFileName;

    if (Storage::disk('s3')->exists($filePath)) {
        $url = Storage::disk('s3')->temporaryUrl(
            $filePath,
            now()->addMinutes(15),
            ['ResponseContentDisposition' => 'attachment']
        );
    } else {
        $url = null;
    }
} else {
    $url = null;
}
@endphp
@if($url)
<a
    href="{{ $url }}"
    title="Download Invoice"
    class="text-primary-600 hover:text-primary-800"
>
    <x-heroicon-o-arrow-down-tray class="w-5 h-5" />
</a>
@else
<span style="fi-ta-text-item-label text-sm leading-6 text-gray-500 dark:text-gray-400   ">-</span>
@endif
