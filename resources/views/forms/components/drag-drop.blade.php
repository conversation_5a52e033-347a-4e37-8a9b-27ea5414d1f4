<!-- <script src="https://cdn.tailwindcss.com"></script> -->
<x-dynamic-component :component="$getFieldWrapperView()" :field="$field">

    <div x-init="console.log('Initial files:', files);
    console.log('Files structure:', Array.isArray(files) ? 'Array' : 'Object');" x-data="{
        state: @js($getState()),
        {{-- isEditPage: @js(str_contains(request()->path(), '/edit')), --}}
        files: @js($this->record?->getMedia('product-images')?->map(fn($media) => [
            'id' => $media->id,
            'original_url' => $media->getFullUrl(),
            'name' => $media->file_name,
            'isDefault' => $media->id == $this->record?->default_image_id
        ])->toArray() ?? []), // Changed from JSON.parse(JSON.stringify())

        tempFileId: 0,
        uploading: false,
        uploadProgress: 0,
        defaultImage: @js($this->record?->default_image_id),
        handleFiles(fileList) {
            console.log('Existing files before upload:', this.files);
            console.log('New files received:', fileList);

            this.uploading = true;

            const uploads = [];

            Array.from(fileList).forEach(file => {
                if (file.size <= 10 * 1024 * 1024) {   // Limit file size
                    uploads.push(file);                // Push actual file objects
                }
            });

            if (uploads.length === 0) {
                this.uploading = false;
                return;
            }

            // Use standard Livewire upload for new files only
            $wire.uploadMultiple('{{ $getStatePath() }}', uploads, (uploadedFiles) => {
                console.log('Upload success, uploaded files:', uploadedFiles);
                
                // Add new files to frontend display
                const newFiles = uploads.map((file, index) => ({
                    original_url: URL.createObjectURL(file),
                    name: file.name,
                    id: null,
                    file: file,
                    isTemporary: true // Mark as temporary for backend processing
                }));
                
                console.log('New files for frontend:', newFiles);
                this.files = [...this.files, ...newFiles];
                
                this.uploading = false;
                
                this.$nextTick(() => {
                    console.log('Final frontend files:', this.files);
                });
            }, (error) => {
                console.error('Upload failed:', error);
                this.uploading = false;
            });
        },
        setDefaultImage(fileName, index) {
            if (!fileName) return;

            this.files = this.files.map((file, i) => ({
                ...file,
                isDefault: i === index
            }));

            this.defaultImage = index;
            {{-- $wire.dispatch('updateDefaultImage', { fileIndex: fileName }); --}}
            $wire.call('setDefaultImage', index);
        },
        removeFile(index) {
            console.log('Removing file at index:', index);
            const file = this.files[index];
            console.log('File to remove:', file);

            // Remove file from local state
            this.files.splice(index, 1);
            
            // If it's an existing media file, call removeMedia
            if(file.id) {
                $wire.call('removeMedia', file.id);
            } else if (file.isTemporary) {
                // For temporary files, we need to remove them from the Livewire state
                // This will be handled when the form saves
                console.log('Marked temporary file for removal');
            }
            
            // Clear default image if needed
            if (file.isDefault) {
                this.defaultImage = null;
                if (this.files.length > 0) {
                    this.files[0].isDefault = true;
                }
            }

            // Force Alpine to update the UI
            this.$nextTick(() => {
                console.log('After removal - Frontend files:', this.files);
            });
        }
    }" wire:ignore>
        <div class="space-y-4">
            <div class="p-4 bg-white rounded-lg shadow">
                <!-- Upload Area -->
                <div class="relative flex items-center justify-center w-full border-2 border-gray-300 rounded-2xl min-h-32"
                    x-on:dragover.prevent="$el.classList.add('border-indigo-600')"
                    x-on:dragleave.prevent="$el.classList.remove('border-indigo-600')"
                    x-on:drop.prevent="$el.classList.remove('border-indigo-600'); handleFiles($event.dataTransfer.files)">
                    <!-- Loading Overlay -->
                    <div x-show="uploading"
                        class="absolute inset-0 z-50 flex items-center justify-center bg-white bg-opacity-80 rounded-2xl">
                        <div class="text-center">
                            <div
                                class="inline-block w-8 h-8 border-4 border-t-4 border-indigo-500 rounded-full animate-spin">
                            </div>
                            <p class="mt-2 text-sm text-gray-600">Uploading...</p>
                        </div>
                    </div>

                    <input type="file" multiple class="absolute inset-0 z-40 w-full h-full opacity-0 cursor-pointer"
                        x-on:change="handleFiles($event.target.files)" wire:model.defer="{{ $getStatePath() }}"
                        :disabled="uploading" />

                    <div class="inline-block text-center align-middle">
                        <h3 class="mt-2 text-lg font-light text-gray-500">
                            <span>Drag and drop your files or</span>
                            <span class="ml-1 text-indigo-600">browse</span>
                        </h3>
                    </div>
                </div>

                <p class="mt-2 font-light text-center text-gray-500 text-md">
                    Supported format JPG, PNG
                </p>

                <!-- Preview Area -->
                <div class="p-4 mt-4 bg-white rounded-lg shadow" x-show="files.length > 0">
                    <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
                        <template x-for="(file, index) in files" :key="index">
                            <div class="relative flex justify-center gap-3 group">
                                <div class="relative flex items-center justify-center p-2 border-2 border-blue-300 rounded-lg shadow-sm"
                                    style="width: 130px; height: 130px;">
                                    <img x-data="console.log(file)"
                                        :src="file.original_url || (file.file ? URL.createObjectURL(file.file) : '')"
                                        class="object-contain max-w-full max-h-24" style="max-width: 112px;">
                                    <svg class="absolute w-5 h-5 p-1 text-white rounded-full bg-blue-950 top-1 right-1"
                                        x-show="file.isDefault || (file.id && file.id === defaultImage)" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>

                                <div
                                    class="absolute inset-0 flex items-center justify-center transition-opacity rounded-lg opacity-0 bg-black/50 group-hover:opacity-100">
                                    <button type="button" @click="setDefaultImage(file.name,index)"
                                        class="p-1 text-white bg-indigo-500 rounded-full hover:bg-indigo-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7" />
                                        </svg>
                                    </button>
                                    <button type="button" @click="removeFile(index)"
                                        class="p-1 ml-2 text-white bg-red-500 rounded-full hover:bg-red-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-dynamic-component>