<x-filament-panels::page.simple>
    <style>
        .resend-btn:disabled {
            cursor: not-allowed !important;
            color: #BDBDBD !important;
        } 

        #parentTimer {
            display: inline-flex !important;
            align-items: center;
            margin-left: 5px;
            font-size: 0.875rem;
            color: #6b7280;
            visibility: visible !important;
            opacity: 1 !important;
        }
    </style>
    <x-filament-panels::form wire:submit="save">
        <div class="text-center">
            <span class="mb-4 text-base text-gray-500">
                Please enter the verification code sent to <strong class="w-full font-semibold block text-primary-600">{{$this->getUser()->email}}</strong>
            </span>
        </div>

        @if (session()->has('resent-success'))
        <span class="text-sm text-green-500 alert">
            {{ session('resent-success') }}
        </span>
        @endif

        {{ $this->form }}

        <x-filament-panels::form.actions class="w-full" :actions="$this->getFormActions()"
            :full-width="$this->hasFullWidthFormActions()" />

        <div class="flex items-center justify-center mt-4" wire:ignore>
            <div id="timer" class="flex items-center gap-2 py-2 text-gray-400 rounded">
                <span>Didn't get code?</span>
                <button id="resendOTP" wire:click="resend" type="button"
                    class="text-primary-600 bg-transparent border-none resend-btn font-medium" wire:loading.attr="disabled"
                    wire:target="resend">
                    Resend OTP
                </button>

            </div>
            <div class="mr-0">
                <div id="parentTimer" class="flex items-center text-sm text-gray-500">
                    <span>in&nbsp;</span>
                    <span id="timer-minutes" class="font-medium">00</span>:<span id="timer-seconds"
                        class="font-medium">00</span>
                </div>
                <!-- Hidden input kept outside the flex to avoid layout impact -->
                <input type="text" id="timer_config" class="hidden"
                    value="{{ config('filament-email-2fa.expiry_time_by_mins', 5) }}">
                <!-- Hidden flag for new OTP (based on database) -->
                <input type="text" id="otp_created_at" class="hidden"
                    value="{{ $this->getUser()->latest_2fa_code?->created_at?->timestamp ?? 0 }}">
            </div>
        </div>



        @if (filament()->hasLogin() && !empty($this->loginAction))
        <div class="mb-4 text-center">
            <span class="flex items-center justify-center text-gray-400 font-medium text-sm">
                <span class="pl-2">Back to&nbsp;{{ $this->loginAction }}</span>
            </span>
        </div>
        @endif

        {{ \Filament\Support\Facades\FilamentView::renderHook(\Filament\View\PanelsRenderHook::PAGE_END) }}


        <script>
            let globalTimerInterval = null;
            let timerCheckInterval = null;

            function initOTPTimer() {
                const timerMinutes = document.getElementById('timer-minutes');
                const timerSeconds = document.getElementById('timer-seconds');
                const resendOTP = document.getElementById('resendOTP');
                const timerConfigElement = document.getElementById('timer_config');
                const parentTimer = document.getElementById('parentTimer');
                const otpCreatedAtElement = document.getElementById('otp_created_at');
                
                if (!timerMinutes || !timerSeconds || !resendOTP || !timerConfigElement) return;
        
                const timerDuration = Math.max(1, parseInt(timerConfigElement.value) || 5) * 60;

                function updateDisplay(secondsLeft) {
                    const minutes = Math.floor(secondsLeft / 60);
                    const seconds = secondsLeft % 60;
                    timerMinutes.textContent = String(minutes).padStart(2, '0');
                    timerSeconds.textContent = String(seconds).padStart(2, '0');
                }
        
                function disableButton() {
                    resendOTP.disabled = true;
                    resendOTP.style.color = '#BDBDBD';
                    parentTimer.style.display = 'inline-flex';
                }
        
                function enableButton() {
                    resendOTP.disabled = false;
                    resendOTP.style.color = '#003f5e';
                    resendOTP.classList.add('enabled');
                    parentTimer.style.display = 'none';
                }

                function checkTimerStatus() {
                    const savedEndTime = parseInt(localStorage.getItem('otpEndTime'));
                    const now = Math.floor(Date.now() / 1000);
                    
                    if (!savedEndTime || savedEndTime <= now) {
                        enableButton();
                        clearInterval(timerCheckInterval);
                    }
                }
        
                function startTimer(duration) {
                    if (!duration || isNaN(duration)) {
                        duration = timerDuration;
                    }
                    
                    if (globalTimerInterval) {
                        clearInterval(globalTimerInterval);
                    }

                    const endTime = Math.floor(Date.now() / 1000) + duration;
                    localStorage.setItem('otpEndTime', endTime);
                    disableButton();
                    runCountdown(endTime);

                    if (timerCheckInterval) {
                        clearInterval(timerCheckInterval);
                    }
                    timerCheckInterval = setInterval(checkTimerStatus, 1000);
                }
        
                function runCountdown(endTime) {
                    if (globalTimerInterval) {
                        clearInterval(globalTimerInterval);
                    }
                    disableButton();
        
                    function tick() {
                        const currentTime = Math.floor(Date.now() / 1000);
                        const secondsLeft = endTime - currentTime;
        
                        if (secondsLeft > 0) {
                            updateDisplay(secondsLeft);
                            disableButton();
                        } else {
                            clearInterval(globalTimerInterval);
                            localStorage.removeItem('otpEndTime');
                            updateDisplay(0);
                            enableButton();
                        }
                    }

                    globalTimerInterval = setInterval(tick, 1000);
                    tick();
                }

                // Handle form submission
                Livewire.on('verification-failed', () => {
                    const savedEndTime = parseInt(localStorage.getItem('otpEndTime'));
                    const now = Math.floor(Date.now() / 1000);
                    
                    if (savedEndTime && savedEndTime > now) {
                        runCountdown(savedEndTime);
                    }
                });

                // Handle resend OTP click
                resendOTP.addEventListener('click', () => {
                    // Mark that a resend is happening to prevent timer reset on refresh
                    localStorage.setItem('otpResendInProgress', Date.now().toString());
                    startTimer(timerDuration);
                });

                // Handle OTP resent event
                window.addEventListener('otp-resent', () => {
                    startTimer(timerDuration);
                });

                // Handle timer reset event
                window.addEventListener('reset-otp-timer', (event) => {
                    const expiryTime = event.detail?.expiryTime;
                    const duration = expiryTime ? Math.floor((expiryTime - Date.now()) / 1000) : timerDuration;
                    startTimer(duration);

                    // Add setTimeout to check and enable button when timer expires
                    // setTimeout(() => {
                    //     const savedEndTime = parseInt(localStorage.getItem('otpEndTime'));
                    //     const now = Math.floor(Date.now() / 1000);
                        
                    //     if (!savedEndTime || savedEndTime <= now) {
                    //         enableButton();
                    //     }
                    // }, (duration * 1000) + 1000); // Add 1 second buffer
                });

                // Check if this is a new OTP based on database creation timestamp
                const otpCreatedAt = otpCreatedAtElement ? parseInt(otpCreatedAtElement.value) : 0;
                const savedTimerEndTime = parseInt(localStorage.getItem('otpEndTime'));
                const savedOtpCreatedAt = parseInt(localStorage.getItem('otpCreatedAt') || '0');
                const now = Math.floor(Date.now() / 1000);
                
                // Check if a resend was recently triggered (within last 10 seconds)
                const resendInProgress = localStorage.getItem('otpResendInProgress');
                const isRecentResend = resendInProgress && (Date.now() - parseInt(resendInProgress)) < 10000;
                
                // If database OTP timestamp is different from saved, it's a new OTP (unless it's a recent resend)
                const isNewOtpSession = otpCreatedAt !== savedOtpCreatedAt && otpCreatedAt > 0 && !isRecentResend;
                
                if (isNewOtpSession) {
                    // New OTP detected - start fresh timer and save timestamp
                    localStorage.removeItem('otpEndTime');
                    localStorage.setItem('otpCreatedAt', otpCreatedAt.toString());
                    startTimer(timerDuration);
                } else if (savedTimerEndTime && savedTimerEndTime > now) {
                    // Timer still active - restore it (regardless of OTP timestamp if recent resend)
                    if (isRecentResend) {
                        // Update the saved OTP timestamp for future comparisons and clear resend flag
                        localStorage.setItem('otpCreatedAt', otpCreatedAt.toString());
                        localStorage.removeItem('otpResendInProgress');
                    }
                    runCountdown(savedTimerEndTime);
                } else {
                    // No active timer or expired, start fresh timer
                    if (savedTimerEndTime) {
                        localStorage.removeItem('otpEndTime');
                    }
                    // Save current OTP timestamp and clear any resend flag
                    if (otpCreatedAt > 0) {
                        localStorage.setItem('otpCreatedAt', otpCreatedAt.toString());
                    }
                    localStorage.removeItem('otpResendInProgress');
                    startTimer(timerDuration);
                }
            }

            function cleanup(clearStorage = false) {
                if (globalTimerInterval) {
                    clearInterval(globalTimerInterval);
                }
                if (timerCheckInterval) {
                    clearInterval(timerCheckInterval);
                }
                // Only clear timer state if explicitly requested (actual navigation)
                if (clearStorage) {
                    localStorage.removeItem('otpEndTime');
                }
            }

            // Clear timer state when user navigates away
            function handlePageLeave(event) {
                // Only clear intervals, not localStorage for page refreshes
                // The new session detection will handle actual navigation
                cleanup(false);
            }

            // Initialize timer
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initOTPTimer);
            } else {
                initOTPTimer();
            }

            // Cleanup and reinitialize on Livewire updates
            document.addEventListener('livewire:navigated', () => {
                cleanup(false); // Don't clear storage on Livewire navigation
                initOTPTimer();
            });

            // Handle page leave events - only clear intervals, not localStorage
            // localStorage will persist and be checked on page load
            window.addEventListener('beforeunload', handlePageLeave);
            window.addEventListener('pagehide', handlePageLeave);
            window.addEventListener('unload', handlePageLeave);
        </script>






    </x-filament-panels::form>
</x-filament-panels::page.simple>