@php
    use Filament\Notifications\Livewire\Notifications;
    use Filament\Support\Enums\Alignment;
    use Filament\Support\Enums\VerticalAlignment;
    use Illuminate\Support\Arr;
    use Illuminate\Notifications\DatabaseNotification;
    use Carbon\Carbon;

    $color = $getColor() ?? 'gray';
    $isInline = $isInline();
    $status = $getStatus();
    $title = $getTitle();
    $hasTitle = filled($title);
    $body = $getBody();
    $hasBody = filled($body);

    //$dateTime = Carbon::parse($getDate()); // Ensure it's Carbon instance

    $value = $getDate();

    // Convert "5 hours from now" to "+5 hours" if needed
    if (is_string($value) && str_contains($value, 'from now')) {
        $value = '+' . trim(str_replace('from now', '', $value));
    }

    try {
        $dateTime = Carbon::parse($value);
    } catch (\Exception $e) {
        $dateTime = Carbon::now(); // fallback if parsing fails
    }
    $diff = $dateTime->diffForHumans();
    $daysAgoPattern = '/^\d+\sday[s]?\sago$/';
    $formattedDate = $dateTime->format('Y-m-d h:i:a');

    // If "0 seconds ago", skip showing
    $showDate = $diff !== '0 seconds ago';

    $date = preg_match($daysAgoPattern, $diff) ? $formattedDate : $diff;
    $hasDate = $showDate && filled($date);

    $dbNotification = DatabaseNotification::find($notification->getId());
    $isRead = $dbNotification?->read_at !== null;
    
@endphp

<x-filament-notifications::notification
    :notification="$notification"
    :x-transition:enter-start="
        Arr::toCssClasses([
            'opacity-0 -translate-y-12', // Always slide from top for center positioning
        ])
    "
    :x-transition:leave-end="
        Arr::toCssClasses([
            'opacity-0 -translate-y-12', // Always slide to top for center positioning
            'scale-95' => ! $isInline,
        ])
    "
    @class([
        'fi-no-notification w-full max-w-2xl mx-auto overflow-hidden transition duration-15000',
        !$isRead ? 'bg-green-50 border-green-400' : 'bg-red-50 border-red-400',
        'read' => $isRead,
        'unread' => ! $isRead,
        // Added mx-auto and max-w-md for center positioning
        ...match ($isInline) {
            true => [
                'fi-inline',
            ],
            false => [
                'rounded-xl bg-white shadow-lg ring-1 dark:bg-gray-900',
                // Removed max-w-sm to allow wider notifications
                match ($color) {
                    'gray' => 'ring-gray-950/5 dark:ring-white/10',
                    default => 'fi-color-custom ring-custom-600/20 dark:ring-custom-400/30',
                },
                is_string($color) ? 'fi-color-' . $color : null,
                'fi-status-' . $status => $status,
            ],
        },
    ])
    @style([
        \Filament\Support\get_color_css_variables(
            $color,
            shades: [50, 400, 600],
            alias: 'notifications::notification',
        ) => ! ($isInline || $color === 'gray'),
    ])
>
    <div
        @class([
            'flex w-full gap-3 p-4',
            match ($color) {
                'gray' => null,
                default => 'bg-custom-50 dark:bg-custom-400/10',
            },
        ])
    >
        @if ($icon = $getIcon())
            <x-filament-notifications::icon
                :color="$getIconColor()"
                :icon="$icon"
                :size="$getIconSize()"
            />
        @endif

        <div class="mt-0.5 grid flex-1">
            @if ($hasTitle)
                <x-filament-notifications::title>
                    {{ str($title)->sanitizeHtml()->toHtmlString() }}
                </x-filament-notifications::title>
            @endif

            @if ($hasDate)
                <x-filament-notifications::date @class(['mt-1' => $hasTitle])>
                    {{ $date }}
                </x-filament-notifications::date>
            @endif

            @if ($hasBody)
                <x-filament-notifications::body
                    @class(['mt-1' => $hasTitle || $hasDate])
                >
                    {{ str($body)->sanitizeHtml()->toHtmlString() }}
                </x-filament-notifications::body>
            @endif

            @if ($actions = $getActions())
                <x-filament-notifications::actions
                    :actions="$actions"
                    @class(['mt-3' => $hasTitle || $hasDate || $hasBody])
                />
            @endif
        </div>
        
        <x-filament-notifications::close-button />
    </div>
</x-filament-notifications::notification>