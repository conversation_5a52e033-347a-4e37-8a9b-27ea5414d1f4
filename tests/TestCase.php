<?php

declare(strict_types=1);

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\Concerns\InteractsWithDatabase;
use Illuminate\Contracts\Console\Kernel;
use Illuminate\Support\Facades\Schema;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;
    use RefreshDatabase {
        RefreshDatabase::refreshDatabase as refreshDatabaseTrait;
    }
    
    /**
     * Creates the application and ensures test database configuration.
     */
    public function createApplication()
    {
        // Configure test database BEFORE application creation
        $this->configureTestEnvironment();
        
        $app = require __DIR__.'/../bootstrap/app.php';
        
        $app->make(Kernel::class)->bootstrap();
        
        return $app;
    }
    
    /**
     * Configure test environment variables before application boots
     */
    private function configureTestEnvironment(): void
    {
        // Determine database type based on environment variable
        $usePersistentDb = $_ENV['USE_PERSISTENT_TEST_DB'] ?? false;
        $database = $usePersistentDb ? database_path('testing.sqlite') : ':memory:';
        
        // Create the SQLite file if using persistent database
        if ($usePersistentDb && !file_exists($database)) {
            touch($database);
        }
        
        // Set environment variables for testing
        $_ENV['APP_ENV'] = 'testing';
        $_ENV['DB_CONNECTION'] = 'testing';
        $_ENV['DB_DATABASE'] = $database;
        $_ENV['DB_FOREIGN_KEYS'] = 'true';
        
        putenv('APP_ENV=testing');
        putenv('DB_CONNECTION=testing');
        putenv("DB_DATABASE={$database}");
        putenv('DB_FOREIGN_KEYS=true');
    }
    
    protected function setUp(): void
    {
        // 🚨 Configure test database BEFORE parent::setUp() to prevent production connections
        $this->configureTestDatabase();
        
        parent::setUp();
        
        // 🚨 CRITICAL SAFETY CHECKS - Protect production database
        $this->enforceTestingEnvironment();
        
        // Force the testing database connection configuration
        $usePersistentDb = env('USE_PERSISTENT_TEST_DB', false);
        $database = $usePersistentDb ? database_path('testing.sqlite') : ':memory:';
        
        config([
            'database.default' => 'testing',
            'database.connections.testing' => [
                'driver' => 'sqlite',
                'database' => $database,
                'prefix' => '',
                'foreign_key_constraints' => true,
            ]
        ]);
        
        // Ensure SQLite foreign key constraints are enabled
        $this->app['db']->connection('testing')->getPdo()->exec('PRAGMA foreign_keys=ON;');
    }
    
    /**
     * Override refreshDatabase to handle SQLite-specific migration issues
     */
    protected function refreshDatabase()
    {
        $database = $this->app->make('db');
        
        // For SQLite testing, we'll create basic tables without running all migrations
        // This avoids PostgreSQL-specific syntax issues
        if (config('database.connections.testing.driver') === 'sqlite') {
            $this->usingInMemoryDatabase() ? $this->refreshInMemoryDatabase() : $this->refreshTestDatabase();
        } else {
            $this->refreshDatabaseTrait();
        }
    }
    
    /**
     * Refresh the in-memory database.
     */
    protected function refreshInMemoryDatabase()
    {
        $this->artisan('migrate', $this->migrateUsing());
        $this->app[Kernel::class]->setArtisan(null);
    }
    
    /**
     * Refresh a conventional test database.
     */
    protected function refreshTestDatabase()
    {
        if (! RefreshDatabase::$migrated) {
            $this->artisan('migrate:fresh', $this->migrateFreshUsing());
            RefreshDatabase::$migrated = true;
        }
        
        $this->beginDatabaseTransaction();
    }
    
    /**
     * Determine if the test is using an in-memory database.
     */
    protected function usingInMemoryDatabase()
    {
        return config('database.connections.testing.database') === ':memory:';
    }
    
    /**
     * Get the parameters for the migrate command.
     */
    protected function migrateUsing()
    {
        return [
            '--database' => 'testing',
            '--path' => 'database/migrations',
            '--realpath' => true,
        ];
    }
    
    /**
     * Get the parameters for the migrate:fresh command.
     */
    protected function migrateFreshUsing()
    {
        return [
            '--database' => 'testing',
            '--drop-views' => true,
        ];
    }
    
    /**
     * Create basic tables needed for tests without running problematic migrations
     */
    private function createBasicTestTables()
    {
        try {
            // Only create the most essential tables for testing
            Schema::create('users', function ($table) {
                $table->id();
                $table->string('name', 255)->nullable();
                $table->string('phone_code', 5)->nullable();
                $table->string('phone', 12)->nullable();
                $table->string('email', 255)->unique();
                $table->string('password', 255)->nullable();
                $table->timestamp('email_verified_at')->nullable();
                $table->rememberToken();
                $table->boolean('status')->nullable();
                $table->string('token', 255)->nullable();
                $table->boolean('is_admin_verified')->nullable();
                $table->boolean('is_otp_verified')->nullable()->default(false);
                $table->timestamps();
                $table->softDeletes();
            });
            
            // Add other essential tables as needed for tests
            Schema::create('migrations', function ($table) {
                $table->increments('id');
                $table->string('migration');
                $table->integer('batch');
            });
            
        } catch (\Exception $e) {
            // If table creation fails, fall back to trait method
            $this->refreshDatabaseTrait();
        }
    }
    
    /**
     * Configure test database before application boots
     */
    private function configureTestDatabase(): void
    {
        // Determine database type based on environment variable
        $usePersistentDb = env('USE_PERSISTENT_TEST_DB', false);
        $database = $usePersistentDb ? database_path('testing.sqlite') : ':memory:';
        
        // Create the SQLite file if using persistent database
        if ($usePersistentDb && !file_exists($database)) {
            touch($database);
        }
        
        // Set environment variables before application boots
        putenv('DB_CONNECTION=testing');
        putenv("DB_DATABASE={$database}");
        putenv('DB_FOREIGN_KEYS=true');
        
        $_ENV['DB_CONNECTION'] = 'testing';
        $_ENV['DB_DATABASE'] = $database;
        $_ENV['DB_FOREIGN_KEYS'] = true;
    }
    
    /**
     * Critical safety check to prevent tests from running against production database
     */
    private function enforceTestingEnvironment(): void
    {
        // Check APP_ENV is testing
        if ($this->app->environment() !== 'testing') {
            throw new \Exception(
                "CRITICAL SAFETY VIOLATION: Tests must only run in testing environment. " .
                "Current environment: {$this->app->environment()}. " .
                "Ensure APP_ENV=testing is set in phpunit.xml"
            );
        }
        
        // Check for production database names in ANY connection
        $connections = config('database.connections', []);
        $productionDatabases = ['dpharma_new', 'dpharma_qa', 'dpharma_prod', 'dpharma_production', 'dpharma'];
        
        foreach ($connections as $name => $connection) {
            $database = $connection['database'] ?? null;
            if ($database && in_array($database, $productionDatabases)) {
                throw new \Exception(
                    "CRITICAL SAFETY VIOLATION: Production database '{$database}' detected in connection '{$name}'. " .
                    "Tests must never access production data. " .
                    "Check your .env and phpunit.xml configuration."
                );
            }
        }
        
        // Verify default connection is testing
        if (config('database.default') !== 'testing') {
            throw new \Exception(
                "CRITICAL SAFETY VIOLATION: Default database connection is not 'testing'. " .
                "Current default: " . config('database.default') . ". " .
                "Tests must use isolated test database."
            );
        }
    }
}
