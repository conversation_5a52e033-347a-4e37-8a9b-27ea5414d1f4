<?php

declare(strict_types=1);

use App\Models\User;
use App\Models\Product;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Unit;
use App\Models\ProductRelation;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use App\Models\ProductVariant;
use App\Models\ProductBatch;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\PcDetail;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Form;

beforeEach(function () {
    // Database protection is now handled in TestCase::setUp()
    // Create required test data
    setupTestData();
});

function setupTestData()
{
    // Create admin user
    $this->admin = User::factory()->create([
        'name' => 'Test Admin',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    // Create PC user
    $this->pcUser = User::factory()->create([
        'name' => 'Test PC',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
    ]);
    
    // Create PC details
    $this->pcDetail = PcDetail::create([
        'user_id' => $this->pcUser->id,
        'commission_percentage' => 10.00,
        'commission_type' => 'percentage',
    ]);
    
    // Create supporting models
    $this->category = Category::factory()->create(['name' => 'Test Category']);
    $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
    $this->unit = Unit::factory()->create(['name' => 'ml']);
    $this->dosageForm = DosageForm::factory()->create(['name' => 'Tablet']);
    $this->container = Container::factory()->create(['name' => 'Bottle']);
    
    // Create existing product
    $this->existingProduct = Product::factory()->create([
        'name' => 'Test Product',
        'category_id' => $this->category->id,
        'brand_id' => $this->brand->id,
        'unit_id' => $this->unit->id,
        'dosage_foams_id' => $this->dosageForm->id,
        'container_id' => $this->container->id,
        'quantity_per_unit' => 500,
        'weight' => 100,
        'is_prescription_required' => false,
    ]);
    
    // Create attributes for variant testing
    $this->colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $this->sizeAttribute = Attribute::factory()->create(['name' => 'Size']);
    
    $this->redValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Red'
    ]);
    $this->blueValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Blue'
    ]);
    $this->smallValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Small'
    ]);
    $this->largeValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Large'
    ]);
}

test('can create product from existing product with fixed pricing', function () {
    Auth::login($this->admin);
    
    $formData = [
        'sku' => 'TEST_123456',
        'price_type' => 'fixed',
        'west_zone_price' => 100.00,
        'east_zone_price' => 120.00,
        'in_stock' => 'yes',
        'stock' => 50,
        'low_stock' => 10,
        'expires_on_after' => now()->addMonths(6)->format('Y-m-d'),
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'productData' => [
            'is_batch_wise_stock' => false,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute the product creation
    $page->createProduct();
    
    // Verify ProductRelation was created
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->not->toBeNull();
    expect($productRelation->price_type)->toBe('fixed');
    expect($productRelation->sku)->toBe('TEST_123456');
    
    // Verify pricing was stored
    $pricing = ProductRelationPrice::where('product_relation_id', $productRelation->id)->first();
    expect($pricing)->not->toBeNull();
    expect($pricing->west_zone_price)->toBe(100.00);
    expect($pricing->east_zone_price)->toBe(120.00);
    
    // Verify stock was stored
    $stock = ProductRelationStock::where('product_relation_id', $productRelation->id)->first();
    expect($stock)->not->toBeNull();
    expect($stock->stock)->toBe(50);
    expect($stock->low_stock)->toBe(10);
    expect($stock->is_batch_wise_stock)->toBeFalse();
    expect($stock->stock_type)->toBe('unit');
});

test('can create product from existing product with batch-wise stock', function () {
    Auth::login($this->admin);
    
    $batchData = [
        [
            'batch_name' => 'BATCH001',
            'available_stock' => 30,
            'expiry_date' => now()->addMonths(12)->format('Y-m-d'),
        ],
        [
            'batch_name' => 'BATCH002',
            'available_stock' => 20,
            'expiry_date' => now()->addMonths(18)->format('Y-m-d'),
        ]
    ];
    
    $formData = [
        'sku' => 'TEST_BATCH_123456',
        'price_type' => 'fixed',
        'west_zone_price' => 100.00,
        'east_zone_price' => 120.00,
        'in_stock' => 'yes',
        'low_stock' => 15,
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'batches' => $batchData,
        'productData' => [
            'is_batch_wise_stock' => true,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute the product creation
    $page->createProduct();
    
    // Verify ProductRelation was created
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->not->toBeNull();
    
    // Verify stock configuration
    $stock = ProductRelationStock::where('product_relation_id', $productRelation->id)->first();
    expect($stock)->not->toBeNull();
    expect($stock->is_batch_wise_stock)->toBeTrue();
    expect($stock->total_stock)->toBe(50); // 30 + 20
    
    // Verify batches were created
    $batches = ProductBatch::where('products_relation_id', $productRelation->id)->get();
    expect($batches)->toHaveCount(2);
    expect($batches->pluck('batch_name')->toArray())->toContain('BATCH001', 'BATCH002');
    expect($batches->sum('available_stock'))->toBe(50);
});

test('can create product from existing product with tier pricing', function () {
    Auth::login($this->admin);
    
    $tierData = [
        'pcInfo_east' => [
            [
                'min_quantity' => 1,
                'max_quantity' => 10,
                'price' => 120.00,
                'admin_fees' => 10,
            ],
            [
                'min_quantity' => 11,
                'max_quantity' => 50,
                'price' => 115.00,
                'admin_fees' => 10,
            ]
        ],
        'pcInfo_west' => [
            [
                'min_quantity' => 1,
                'max_quantity' => 10,
                'price' => 100.00,
                'admin_fees' => 10,
            ],
            [
                'min_quantity' => 11,
                'max_quantity' => 50,
                'price' => 95.00,
                'admin_fees' => 10,
            ]
        ]
    ];
    
    $formData = array_merge($tierData, [
        'sku' => 'TEST_TIER_123456',
        'price_type' => 'tier',
        'in_stock' => 'yes',
        'stock' => 100,
        'low_stock' => 20,
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'productData' => [
            'is_batch_wise_stock' => false,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ]);
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute the product creation
    $page->createProduct();
    
    // Verify ProductRelation was created
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->not->toBeNull();
    expect($productRelation->price_type)->toBe('tier');
    
    // Verify tier pricing was stored
    $pricing = ProductRelationPrice::where('product_relation_id', $productRelation->id)->first();
    expect($pricing)->not->toBeNull();
    expect($pricing->east_tier_1_base_price)->toBe(120.00);
    expect($pricing->west_tier_1_base_price)->toBe(100.00);
    expect($pricing->east_tier_2_base_price)->toBe(115.00);
    expect($pricing->west_tier_2_base_price)->toBe(95.00);
    expect($pricing->east_tier_1_min_quantity)->toBe(1);
    expect($pricing->east_tier_1_max_quantity)->toBe(10);
});

test('can create product from existing product with bonus pricing', function () {
    Auth::login($this->admin);
    
    $formData = [
        'sku' => 'TEST_BONUS_123456',
        'price_type' => 'bonus',
        'west_zone_price' => 100.00,
        'east_zone_price' => 120.00,
        'west_bonus_1_quantity' => 10,
        'west_bonus_1_quantity_value' => 2,
        'east_bonus_1_quantity' => 10,
        'east_bonus_1_quantity_value' => 2,
        'in_stock' => 'yes',
        'stock' => 100,
        'low_stock' => 20,
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'productData' => [
            'is_batch_wise_stock' => false,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute the product creation
    $page->createProduct();
    
    // Verify ProductRelation was created
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->not->toBeNull();
    expect($productRelation->price_type)->toBe('bonus');
    
    // Verify bonus pricing was stored
    $pricing = ProductRelationPrice::where('product_relation_id', $productRelation->id)->first();
    expect($pricing)->not->toBeNull();
    expect($pricing->west_zone_price)->toBe(100.00);
    expect($pricing->east_zone_price)->toBe(120.00);
    expect($pricing->west_bonus_1_quantity)->toBe(10);
    expect($pricing->west_bonus_1_quantity_value)->toBe(2);
    expect($pricing->east_bonus_1_quantity)->toBe(10);
    expect($pricing->east_bonus_1_quantity_value)->toBe(2);
});

test('can create product from existing product with variants', function () {
    Auth::login($this->admin);
    
    $attributeData = [
        'price_type_toggle' => 'fixed',
        'stock_type_toggle' => 'normal',
        'image_assignment_type' => 'single',
        'attributes' => [
            $this->colorAttribute->id => [$this->redValue->id, $this->blueValue->id],
            $this->sizeAttribute->id => [$this->smallValue->id, $this->largeValue->id]
        ],
        'pricing' => [
            $this->redValue->id . '_' . $this->smallValue->id => [
                'west_zone_price' => 100.00,
                'east_zone_price' => 120.00,
            ],
            $this->redValue->id . '_' . $this->largeValue->id => [
                'west_zone_price' => 110.00,
                'east_zone_price' => 130.00,
            ],
            $this->blueValue->id . '_' . $this->smallValue->id => [
                'west_zone_price' => 105.00,
                'east_zone_price' => 125.00,
            ],
            $this->blueValue->id . '_' . $this->largeValue->id => [
                'west_zone_price' => 115.00,
                'east_zone_price' => 135.00,
            ],
        ],
        'quantity' => [
            $this->redValue->id . '_' . $this->smallValue->id => [
                'stock' => 10,
                'low_stock' => 2,
            ],
            $this->redValue->id . '_' . $this->largeValue->id => [
                'stock' => 15,
                'low_stock' => 3,
            ],
            $this->blueValue->id . '_' . $this->smallValue->id => [
                'stock' => 12,
                'low_stock' => 2,
            ],
            $this->blueValue->id . '_' . $this->largeValue->id => [
                'stock' => 18,
                'low_stock' => 4,
            ],
        ],
        'single_images' => [],
    ];
    
    $formData = [
        'sku' => 'TEST_VARIANT_123456',
        'price_type' => 'fixed', // This gets ignored when has_variants is true
        'in_stock' => 'yes',
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'productData' => [
            'is_batch_wise_stock' => false,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => true,
        'attribute_data' => $attributeData,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute the product creation
    $page->createProduct();
    
    // Verify ProductRelation was created
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->not->toBeNull();
    
    // Verify variants were created (should be 4: Red-Small, Red-Large, Blue-Small, Blue-Large)
    $variants = ProductVariant::where('product_id', $this->existingProduct->id)->get();
    expect($variants)->toHaveCount(4);
    
    // Verify each variant has pricing and stock
    foreach ($variants as $variant) {
        $variantPricing = ProductRelationPrice::where('product_variant_id', $variant->id)->first();
        $variantStock = ProductRelationStock::where('product_variant_id', $variant->id)->first();
        
        expect($variantPricing)->not->toBeNull();
        expect($variantStock)->not->toBeNull();
        expect($variantPricing->west_zone_price)->toBeGreaterThan(0);
        expect($variantStock->stock)->toBeGreaterThan(0);
    }
    
    // Verify variant attributes were linked correctly
    $redSmallVariant = $variants->filter(function ($variant) {
        $attributes = DB::table('product_variant_attributes')
            ->where('product_variant_id', $variant->id)
            ->get();
        
        $hasRed = $attributes->contains('attribute_value_id', $this->redValue->id);
        $hasSmall = $attributes->contains('attribute_value_id', $this->smallValue->id);
        
        return $hasRed && $hasSmall;
    })->first();
    
    expect($redSmallVariant)->not->toBeNull();
});

test('validates duplicate batch names', function () {
    Auth::login($this->admin);
    
    $batchData = [
        [
            'batch_name' => 'DUPLICATE_BATCH',
            'available_stock' => 30,
            'expiry_date' => now()->addMonths(12)->format('Y-m-d'),
        ],
        [
            'batch_name' => 'DUPLICATE_BATCH', // Same name
            'available_stock' => 20,
            'expiry_date' => now()->addMonths(18)->format('Y-m-d'),
        ]
    ];
    
    $formData = [
        'sku' => 'TEST_DUPLICATE_123456',
        'price_type' => 'fixed',
        'west_zone_price' => 100.00,
        'east_zone_price' => 120.00,
        'in_stock' => 'yes',
        'low_stock' => 15,
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'batches' => $batchData,
        'productData' => [
            'is_batch_wise_stock' => true,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute and expect no product relation to be created due to validation error
    $page->createProduct();
    
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->toBeNull();
});

test('validates stock is greater than low stock', function () {
    Auth::login($this->admin);
    
    $formData = [
        'sku' => 'TEST_STOCK_VALIDATION_123456',
        'price_type' => 'fixed',
        'west_zone_price' => 100.00,
        'east_zone_price' => 120.00,
        'in_stock' => 'yes',
        'stock' => 5, // Lower than low_stock
        'low_stock' => 10,
        'stock_type' => 'unit',
        'whole_sale_pack_size' => 1,
        'productData' => [
            'is_batch_wise_stock' => false,
            'quantity_per_unit' => $this->existingProduct->quantity_per_unit,
            'unit_id' => $this->existingProduct->unit_id,
            'dosage_foams_id' => $this->existingProduct->dosage_foams_id,
        ],
        'container' => [
            'name' => $this->container->name,
        ],
        'has_variants' => false,
        'attribute_data' => null,
    ];
    
    $page = new CreateNewProductFromExistingProduct();
    $page->mount($this->existingProduct->id);
    $page->pcId = $this->pcUser->id;
    $page->pharmaSupplier = $this->pcUser;
    $page->finalCommission = 10;
    $page->finalCommissionType = 'percentage';
    
    // Mock form data
    $form = Mockery::mock(Form::class);
    $form->shouldReceive('getState')->andReturn($formData);
    $form->shouldReceive('fill')->andReturn(null);
    $page->form = $form;
    
    // Execute and expect no product relation to be created due to validation error
    $page->createProduct();
    
    $productRelation = ProductRelation::where('product_id', $this->existingProduct->id)
        ->where('user_id', $this->pcUser->id)
        ->first();
    
    expect($productRelation)->toBeNull();
}); 