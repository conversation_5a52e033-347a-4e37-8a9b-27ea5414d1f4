<?php

declare(strict_types=1);

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\DosageForm;
use App\Models\Container;
use App\Services\PostSaveAttributeService;
use App\Services\AttributeDataValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Models\Activity;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create authenticated user
    $this->user = User::factory()->create([
        'name' => 'Test Admin',
        'email' => '<EMAIL>'
    ]);
    Auth::login($this->user);
    
    // Create required related models
    $this->category = Category::factory()->create(['name' => 'Test Category']);
    $this->subcategory = Category::factory()->create(['name' => 'Test Subcategory']);
    $this->brand = Brand::factory()->create(['name' => 'Test Brand']);
    $this->unit = Unit::factory()->create(['name' => 'mg']);
    $this->dosageForm = DosageForm::factory()->create(['name' => 'Tablet']);
    $this->container = Container::factory()->create(['name' => 'Bottle']);
    
    // Create base product
    $this->baseProduct = Product::factory()->create([
        'name' => 'Base Test Product',
        'sku' => 'BASE-001',
        'category_id' => $this->category->id,
        'sub_category_id' => $this->subcategory->id,
        'brand_id' => $this->brand->id,
        'unit_id' => $this->unit->id,
        'dosage_foams_id' => $this->dosageForm->id,
        'container_id' => $this->container->id,
        'quantity_per_unit' => 500
    ]);
    
    // Create test attributes and values
    $this->setupTestAttributes();
});

function setupTestAttributes() {
    $this->colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $this->sizeAttribute = Attribute::factory()->create(['name' => 'Size']);
    $this->strengthAttribute = Attribute::factory()->create(['name' => 'Strength']);
    
    // Color values
    $this->redValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Red'
    ]);
    $this->blueValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Blue'
    ]);
    $this->greenValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Green'
    ]);
    
    // Size values
    $this->smallValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Small'
    ]);
    $this->mediumValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Medium'
    ]);
    $this->largeValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Large'
    ]);
    
    // Strength values
    $this->lowStrengthValue = AttributeValue::factory()->create([
        'attribute_id' => $this->strengthAttribute->id,
        'name' => '250mg'
    ]);
    $this->highStrengthValue = AttributeValue::factory()->create([
        'attribute_id' => $this->strengthAttribute->id,
        'name' => '500mg'
    ]);
}

describe('End-to-End Attribute Persistence Integration', function () {
    
    it('creates product with simple variants successfully', function () {
        // Simulate form data from CreateNewProductFromExistingProduct
        $productData = [
            'name' => 'New Product from Base',
            'sku' => 'NEW-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            // Variant configuration
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        // Step 1: Create the product
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        // Step 2: Process attribute data (simulating afterCreate hook)
        $attributeService = new PostSaveAttributeService();
        $result = $attributeService->processAttributeData($product, $productData);
        
        // Verify results
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 2)
            ->toHaveKey('product_id', $product->id);
        
        // Verify database state
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(2);
        
        // Verify each variant has correct attributes
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(1);
            
            $priceRecord = ProductRelationPrice::where('product_variant_id', $variant->id)->first();
            expect($priceRecord)->not->toBeNull();
            
            $stockRecord = ProductRelationStock::where('product_variant_id', $variant->id)->first();
            expect($stockRecord)->not->toBeNull();
        }
        
        // Verify activity logging
        $activity = Activity::where('subject_type', Product::class)
            ->where('subject_id', $product->id)
            ->first();
        expect($activity)->not->toBeNull();
    });
    
    it('creates product with complex multi-attribute variants', function () {
        $productData = [
            'name' => 'Complex Variant Product',
            'sku' => 'COMPLEX-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [
                    $this->colorAttribute->id,
                    $this->sizeAttribute->id,
                    $this->strengthAttribute->id
                ],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
                'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
                'attribute_values_3' => [$this->lowStrengthValue->id, $this->highStrengthValue->id],
                'price_type_toggle' => 'tier',
                'stock_type_toggle' => 'batch'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $attributeService = new PostSaveAttributeService();
        $result = $attributeService->processAttributeData($product, $productData);
        
        // Should create 2 × 2 × 2 = 8 variants
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 8);
        
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(8);
        
        // Each variant should have 3 attributes
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(3);
            
            // Verify all attribute combinations are unique
            $attributeValues = $variantAttributes->pluck('attribute_value_id')->toArray();
            expect($attributeValues)->toHaveCount(3);
        }
    });
    
    it('handles product creation without variants correctly', function () {
        $productData = [
            'name' => 'Simple Product',
            'sku' => 'SIMPLE-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => false,
            'attribute_data' => null
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $attributeService = new PostSaveAttributeService();
        $result = $attributeService->processAttributeData($product, $productData);
        
        expect($result)
            ->toHaveKey('status', 'skipped')
            ->toHaveKey('message', 'No variant data to process');
        
        // Verify no variants created
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(0);
    });
    
    it('handles variant update scenarios correctly', function () {
        // Create product with initial variants
        $product = Product::create([
            'name' => 'Updatable Product',
            'sku' => 'UPDATE-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        // Initial variant configuration
        $initialData = [
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $attributeService = new PostSaveAttributeService();
        $result1 = $attributeService->processAttributeData($product, $initialData);
        
        expect($result1['variants_created'])->toBe(1);
        
        // Update with different variant configuration
        $updatedData = [
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id, $this->greenValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $result2 = $attributeService->processAttributeData($product, $updatedData);
        
        expect($result2['variants_created'])->toBe(3);
        
        // Verify old variants were replaced
        $finalVariants = ProductVariant::where('product_id', $product->id)->get();
        expect($finalVariants)->toHaveCount(3);
    });
});

describe('Validation Integration Tests', function () {
    
    it('validates attribute data before processing', function () {
        $productData = [
            'name' => 'Invalid Product',
            'sku' => 'INVALID-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [99999], // Invalid attribute ID
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $attributeService = new PostSaveAttributeService();
        
        expect(fn() => $attributeService->processAttributeData($product, $productData))
            ->toThrow(\Illuminate\Validation\ValidationException::class);
        
        // Verify no variants were created
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(0);
    });
    
    it('handles cross-attribute validation correctly', function () {
        $productData = [
            'name' => 'Cross Validation Product',
            'sku' => 'CROSS-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->smallValue->id], // Size value for Color attribute
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $attributeService = new PostSaveAttributeService();
        
        expect(fn() => $attributeService->processAttributeData($product, $productData))
            ->toThrow(\Illuminate\Validation\ValidationException::class);
    });
});

describe('Error Recovery and Rollback Tests', function () {
    
    it('rolls back transaction on failure', function () {
        $productData = [
            'name' => 'Rollback Test Product',
            'sku' => 'ROLLBACK-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        // Force error by dropping table
        DB::statement('ALTER TABLE product_variant_attributes RENAME TO product_variant_attributes_temp');
        
        $attributeService = new PostSaveAttributeService();
        
        try {
            $attributeService->processAttributeData($product, $productData);
        } catch (\Exception $e) {
            // Expected
        } finally {
            // Restore table
            DB::statement('ALTER TABLE product_variant_attributes_temp RENAME TO product_variant_attributes');
        }
        
        // Verify rollback - no variants should exist
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(0);
        
        // Verify no pricing records
        $priceRecords = ProductRelationPrice::whereHas('productVariant', function ($query) use ($product) {
            $query->where('product_id', $product->id);
        })->get();
        expect($priceRecords)->toHaveCount(0);
    });
});

describe('Performance Integration Tests', function () {
    
    it('handles complex scenarios efficiently', function () {
        // Create additional attributes for complex scenarios
        $brandAttribute = Attribute::factory()->create(['name' => 'Brand']);
        $materialAttribute = Attribute::factory()->create(['name' => 'Material']);
        
        $brandValues = AttributeValue::factory()->count(4)->create(['attribute_id' => $brandAttribute->id]);
        $materialValues = AttributeValue::factory()->count(3)->create(['attribute_id' => $materialAttribute->id]);
        
        $productData = [
            'name' => 'Performance Test Product',
            'sku' => 'PERF-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [
                    $this->colorAttribute->id,
                    $this->sizeAttribute->id,
                    $brandAttribute->id,
                    $materialAttribute->id
                ],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id, $this->greenValue->id],
                'attribute_values_2' => [$this->smallValue->id, $this->mediumValue->id, $this->largeValue->id],
                'attribute_values_3' => $brandValues->pluck('id')->toArray(),
                'attribute_values_4' => $materialValues->pluck('id')->toArray(),
                'price_type_toggle' => 'tier',
                'stock_type_toggle' => 'batch'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $startTime = microtime(true);
        
        $attributeService = new PostSaveAttributeService();
        $result = $attributeService->processAttributeData($product, $productData);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Should create 3 × 3 × 4 × 3 = 108 variants
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 108);
        
        // Should complete within reasonable time
        expect($executionTime)->toBeLessThan(10.0);
        
        // Verify data integrity
        $variants = ProductVariant::where('product_id', $product->id)->get();
        expect($variants)->toHaveCount(108);
        
        // Verify all variants have correct number of attributes
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(4);
        }
    });
});

describe('Activity Logging Integration', function () {
    
    it('logs comprehensive activity data for variant creation', function () {
        $productData = [
            'name' => 'Activity Log Product',
            'sku' => 'ACTIVITY-001',
            'category_id' => $this->category->id,
            'sub_category_id' => $this->subcategory->id,
            'brand_id' => $this->brand->id,
            'unit_id' => $this->unit->id,
            'dosage_foams_id' => $this->dosageForm->id,
            'container_id' => $this->container->id,
            'quantity_per_unit' => 500,
            
            'has_variants' => true,
            'attribute_data' => [
                'attribute_ids' => [$this->colorAttribute->id, $this->sizeAttribute->id],
                'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
                'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => 'stock'
            ]
        ];
        
        $product = Product::create([
            'name' => $productData['name'],
            'sku' => $productData['sku'],
            'category_id' => $productData['category_id'],
            'sub_category_id' => $productData['sub_category_id'],
            'brand_id' => $productData['brand_id'],
            'unit_id' => $productData['unit_id'],
            'dosage_foams_id' => $productData['dosage_foams_id'],
            'container_id' => $productData['container_id'],
            'quantity_per_unit' => $productData['quantity_per_unit'],
            'add_request_by' => $this->user->id,
            'is_created_by_admin' => true
        ]);
        
        $attributeService = new PostSaveAttributeService();
        $result = $attributeService->processAttributeData($product, $productData);
        
        // Verify activity was logged
        $activity = Activity::where('subject_type', Product::class)
            ->where('subject_id', $product->id)
            ->where('causer_id', $this->user->id)
            ->first();
        
        expect($activity)->not->toBeNull();
        expect($activity->description)->toContain('Product variants created');
        
        // Verify activity properties contain variant creation details
        $properties = $activity->properties->toArray();
        expect($properties)
            ->toHaveKey('attributes')
            ->and($properties['attributes'])
            ->toHaveKey('variants_created', 4)
            ->toHaveKey('pricing_records', 4)
            ->toHaveKey('stock_records', 4);
    });
}); 