<?php

declare(strict_types=1);

use App\Models\ProductRelationPrice;
use App\Models\ProductRelation;
use App\Models\ProductRelationStock;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct;
use App\Services\PostSaveAttributeService;

test('product relation price model has all required fillable fields', function () {
    $model = new ProductRelationPrice();
    $fillable = $model->getFillable();
    
    // Test that all pricing fields are fillable
    $requiredFields = [
        'product_relation_id',
        'east_zone_price',
        'west_zone_price',
        'east_tier_1_base_price',
        'west_tier_1_base_price',
        'east_tier_2_base_price',
        'west_tier_2_base_price',
        'east_tier_3_base_price',
        'west_tier_3_base_price',
        'east_tier_1_min_quantity',
        'west_tier_1_min_quantity',
        'east_tier_2_min_quantity',
        'west_tier_2_min_quantity',
        'east_tier_3_min_quantity',
        'west_tier_3_min_quantity',
        'east_tier_1_max_quantity',
        'west_tier_1_max_quantity',
        'east_tier_2_max_quantity',
        'west_tier_2_max_quantity',
        'east_tier_3_max_quantity',
        'west_tier_3_max_quantity',
        'east_bonus_1_quantity',
        'east_bonus_1_quantity_value',
        'east_bonus_2_quantity',
        'east_bonus_2_quantity_value',
        'east_bonus_3_quantity',
        'east_bonus_3_quantity_value',
        'west_bonus_1_quantity',
        'west_bonus_1_quantity_value',
        'west_bonus_2_quantity',
        'west_bonus_2_quantity_value',
        'west_bonus_3_quantity',
        'west_bonus_3_quantity_value',
        'east_bonus_1_base_price',
        'east_bonus_2_base_price',
        'east_bonus_3_base_price',
        'west_bonus_1_base_price',
        'west_bonus_2_base_price',
        'west_bonus_3_base_price',
    ];
    
    foreach ($requiredFields as $field) {
        expect(in_array($field, $fillable))->toBeTrue("Field '{$field}' should be fillable in ProductRelationPrice");
    }
});

test('product relation model has all required fillable fields', function () {
    $model = new ProductRelation();
    $fillable = $model->getFillable();
    
    $requiredFields = [
        'product_id',
        'price_type',
        'user_id',
        'admin_approval',
        'requested_by',
        'quantity_per_unit',
        'unit_id',
        'dosage_foams_id',
        'sku',
        'pc_approval',
    ];
    
    foreach ($requiredFields as $field) {
        expect(in_array($field, $fillable))->toBeTrue("Field '{$field}' should be fillable in ProductRelation");
    }
});

test('product relation stock model has all required fillable fields', function () {
    $model = new ProductRelationStock();
    $fillable = $model->getFillable();
    
    $requiredFields = [
        'product_relation_id',
        'is_batch_wise_stock',
        'stock',
        'total_stock',
        'low_stock',
        'weight',
        'expiry_date',
        'wholesale_pack_size',
        'stock_type',
    ];
    
    foreach ($requiredFields as $field) {
        expect(in_array($field, $fillable))->toBeTrue("Field '{$field}' should be fillable in ProductRelationStock");
    }
});

test('create new product from existing product class exists and has required methods', function () {
    expect(class_exists(CreateNewProductFromExistingProduct::class))->toBeTrue();
    
    $reflection = new ReflectionClass(CreateNewProductFromExistingProduct::class);
    
    // Check required methods exist
    expect($reflection->hasMethod('createProduct'))->toBeTrue();
    expect($reflection->hasMethod('form'))->toBeTrue();
    expect($reflection->hasMethod('mount'))->toBeTrue();
    expect($reflection->hasMethod('getCachedDosageForm'))->toBeTrue();
    expect($reflection->hasMethod('getCachedUnitOptions'))->toBeTrue();
    expect($reflection->hasMethod('getCachedDosageFormOptions'))->toBeTrue();
});

test('post save attribute service class exists and has required methods', function () {
    expect(class_exists(PostSaveAttributeService::class))->toBeTrue();
    
    $reflection = new ReflectionClass(PostSaveAttributeService::class);
    
    // Check required methods exist
    expect($reflection->hasMethod('processAttributeData'))->toBeTrue();
    
    // Check that private methods for saving variants exist
    $privateMethods = array_map(fn($method) => $method->getName(), $reflection->getMethods(ReflectionMethod::IS_PRIVATE));
    expect($privateMethods)->toContain('saveVariantPricing');
    expect($privateMethods)->toContain('saveVariantStock');
    expect($privateMethods)->toContain('clearExistingVariants');
});

test('net earnings calculation works correctly', function () {
    // Test percentage commission
    $result = CreateNewProductFromExistingProduct::netEarnings(100, 10, 'percentage');
    expect($result)->toBe('90.00');
    
    // Test fixed commission
    $result = CreateNewProductFromExistingProduct::netEarnings(100, 10, 'fixed');
    expect($result)->toBe('90.00');
    
    // Test edge case - commission equals price
    $result = CreateNewProductFromExistingProduct::netEarnings(100, 100, 'percentage');
    expect($result)->toBe('0.00');
    
    // Test edge case - commission higher than price (fixed)
    $result = CreateNewProductFromExistingProduct::netEarnings(50, 100, 'fixed');
    expect($result)->toBe('-50.00');
});

test('numeric value validation rule returns correct attributes', function () {
    $rules = CreateNewProductFromExistingProduct::numericValueValidationRule();
    
    expect($rules)->toHaveKey('x-data');
    expect($rules)->toHaveKey('x-on:input');
    expect($rules['x-on:input'])->toBe('sanitizeInput($event)');
    expect($rules['x-data'])->toContain('sanitizeInput');
});

test('admin create new product from existing has correct view and resource', function () {
    $reflection = new ReflectionClass(\App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class);
    
    // Check static properties
    $resourceProperty = $reflection->getProperty('resource');
    $resourceProperty->setAccessible(true);
    expect($resourceProperty->getValue())->toBe(\App\Filament\Admin\Resources\ProductResource::class);
    
    $viewProperty = $reflection->getProperty('view');
    $viewProperty->setAccessible(true);
    expect($viewProperty->getValue())->toBe('filament.pc.resources.product-resource.pages.create-new-product-from-existing-product');
});

test('pc create new product from existing has correct view and resource', function () {
    $reflection = new ReflectionClass(\App\Filament\Pc\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class);
    
    // Check static properties
    $resourceProperty = $reflection->getProperty('resource');
    $resourceProperty->setAccessible(true);
    expect($resourceProperty->getValue())->toBe(\App\Filament\Pc\Resources\ProductResource::class);
    
    $viewProperty = $reflection->getProperty('view');
    $viewProperty->setAccessible(true);
    expect($viewProperty->getValue())->toBe('filament.pc.resources.product-resource.pages.create-new-product-from-existing-product');
});

test('both admin and pc versions have caching methods', function () {
    $adminClass = \App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class;
    $pcClass = \App\Filament\Pc\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class;
    
    foreach ([$adminClass, $pcClass] as $class) {
        $reflection = new ReflectionClass($class);
        
        expect($reflection->hasMethod('getCachedDosageForm'))->toBeTrue();
        expect($reflection->hasMethod('getCachedUnitOptions'))->toBeTrue();
        expect($reflection->hasMethod('getCachedDosageFormOptions'))->toBeTrue();
        expect($reflection->hasMethod('clearFormCaches'))->toBeTrue();
        expect($reflection->hasMethod('refreshCachedData'))->toBeTrue();
    }
});

test('both versions have required properties', function () {
    $adminClass = \App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class;
    $pcClass = \App\Filament\Pc\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct::class;
    
    foreach ([$adminClass, $pcClass] as $class) {
        $reflection = new ReflectionClass($class);
        
        $properties = array_map(fn($prop) => $prop->getName(), $reflection->getProperties());
        
        expect($properties)->toContain('data');
        expect($properties)->toContain('commission');
        expect($properties)->toContain('finalCommission');
        expect($properties)->toContain('finalCommissionType');
        expect($properties)->toContain('sku');
        expect($properties)->toContain('images');
        expect($properties)->toContain('wholeSalePackSize');
        expect($properties)->toContain('stockType');
    }
});

test('price type validation logic exists in admin version', function () {
    // Read the admin file content to check for price type validation
    $adminFile = file_get_contents(app_path('Filament/Admin/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    
    // Check that the price_type null fix is present
    expect($adminFile)->toContain("!empty(\$productData['price_type']) ? \$productData['price_type'] : null");
    
    // Check that bonus validation exists
    expect($adminFile)->toContain("if (\$productData['price_type'] == 'bonus')");
    
    // Check that tier validation exists
    expect($adminFile)->toContain("if (\$productData['price_type'] == 'tier')");
    
    // Check that batch validation exists
    expect($adminFile)->toContain('Duplicate batch name found');
});

test('stock validation logic exists in pc version', function () {
    // Read the PC file content to check for validation
    $pcFile = file_get_contents(app_path('Filament/Pc/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    
    // Check that stock validation exists
    expect($pcFile)->toContain('The stock is lower than the low stock value');
    
    // Check that duplicate batch validation exists
    expect($pcFile)->toContain('Duplicate batch name found');
    
    // Check that product relation creation exists
    expect($pcFile)->toContain('ProductRelation::create');
});

test('database table inserts use correct table names', function () {
    $adminFile = file_get_contents(app_path('Filament/Admin/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    $pcFile = file_get_contents(app_path('Filament/Pc/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    
    foreach ([$adminFile, $pcFile] as $content) {
        // Check that correct table names are used
        expect($content)->toContain('product_relation_prices');
        expect($content)->toContain('product_relation_stocks');
        
        // Check that ProductBatch is used
        expect($content)->toContain('ProductBatch::');
    }
});

test('activity logging is implemented in both versions', function () {
    $adminFile = file_get_contents(app_path('Filament/Admin/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    $pcFile = file_get_contents(app_path('Filament/Pc/Resources/ProductResource/Pages/CreateNewProductFromExistingProduct.php'));
    
    foreach ([$adminFile, $pcFile] as $content) {
        // Check that activity logging exists
        expect($content)->toContain('activity()');
        expect($content)->toContain('->performedOn(');
        expect($content)->toContain('->causedBy(');
        expect($content)->toContain('->withProperties(');
    }
}); 