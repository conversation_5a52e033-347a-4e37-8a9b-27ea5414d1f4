<?php

declare(strict_types=1);

use App\Models\Product;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Services\AttributeDataValidationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test product
    $this->product = Product::factory()->create([
        'name' => 'Test Product',
        'sku' => 'TEST-001'
    ]);
    
    // Create test attributes and values
    $this->colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $this->sizeAttribute = Attribute::factory()->create(['name' => 'Size']);
    
    $this->redValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Red'
    ]);
    $this->blueValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Blue'
    ]);
    $this->smallValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Small'
    ]);
    $this->largeValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Large'
    ]);
    
    $this->validator = new AttributeDataValidationService();
});

describe('AttributeDataValidationService Success Cases', function () {
    
    it('validates correct attribute data successfully', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id, $this->sizeAttribute->id],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
        
        expect($result)->toBe($attributeData);
    });
    
    it('validates single attribute with multiple values', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
        
        expect($result)->toBe($attributeData);
    });
    
    it('validates different pricing types', function () {
        foreach (['fixed', 'bonus', 'tier'] as $priceType) {
            $attributeData = [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => $priceType,
                'stock_type_toggle' => 'stock'
            ];
            
            $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
            
            expect($result)->toBe($attributeData);
        }
    });
    
    it('validates different stock types', function () {
        foreach (['stock', 'batch'] as $stockType) {
            $attributeData = [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => $stockType
            ];
            
            $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
            
            expect($result)->toBe($attributeData);
        }
    });
    
    it('validates empty attribute_ids when no variants are needed', function () {
        $attributeData = [
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
        
        expect($result)->toBe($attributeData);
    });
});

describe('AttributeDataValidationService Validation Errors', function () {
    
    it('throws exception when product does not exist', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData(99999, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData(99999, $attributeData))
            ->toThrow(ValidationException::class, 'Product with ID 99999 not found');
    });
    
    it('throws exception when attribute does not exist', function () {
        $attributeData = [
            'attribute_ids' => [99999], // Non-existent attribute
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'Attribute with ID 99999 not found');
    });
    
    it('throws exception when attribute value does not exist', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [99999], // Non-existent attribute value
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'Attribute value with ID 99999 not found');
    });
    
    it('throws exception when attribute value belongs to wrong attribute', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->smallValue->id], // Size value for Color attribute
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, "Attribute value {$this->smallValue->id} does not belong to attribute {$this->colorAttribute->id}");
    });
    
    it('throws exception when no values selected for attribute', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [], // Empty values
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'No values selected for attribute: Color');
    });
    
    it('throws exception when missing attribute values key', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            // Missing 'attribute_values_1'
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'No values selected for attribute: Color');
    });
    
    it('throws exception for invalid price type', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'invalid_type',
            'stock_type_toggle' => 'stock'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'Invalid price type: invalid_type');
    });
    
    it('throws exception for invalid stock type', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'invalid_type'
        ];
        
        expect(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class)
            ->and(fn() => $this->validator->validateAttributeData($this->product->id, $attributeData))
            ->toThrow(ValidationException::class, 'Invalid stock type: invalid_type');
    });
});

describe('AttributeDataValidationService Assignment Type Validation', function () {
    
    it('validates assignment types correctly when attributes are present', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_assignment_type' => 'attribute',
            'quantity_assignment_type' => 'attribute'
        ];
        
        $warnings = $this->validator->validateAssignmentTypes($attributeData);
        
        expect($warnings)->toBeEmpty();
    });
    
    it('returns warnings when price assignment is attribute but no attributes selected', function () {
        $attributeData = [
            'price_assignment_type' => 'attribute',
            // No attribute_ids
        ];
        
        $warnings = $this->validator->validateAssignmentTypes($attributeData);
        
        expect($warnings)
            ->toHaveCount(1)
            ->and($warnings[0])->toBe("Price assignment type is set to 'attribute' but no attributes are selected");
    });
    
    it('returns warnings when quantity assignment is attribute but no attributes selected', function () {
        $attributeData = [
            'quantity_assignment_type' => 'attribute',
            // No attribute_ids
        ];
        
        $warnings = $this->validator->validateAssignmentTypes($attributeData);
        
        expect($warnings)
            ->toHaveCount(1)
            ->and($warnings[0])->toBe("Quantity assignment type is set to 'attribute' but no attributes are selected");
    });
    
    it('returns multiple warnings for multiple assignment issues', function () {
        $attributeData = [
            'price_assignment_type' => 'attribute',
            'quantity_assignment_type' => 'attribute',
            // No attribute_ids
        ];
        
        $warnings = $this->validator->validateAssignmentTypes($attributeData);
        
        expect($warnings)->toHaveCount(2);
    });
    
    it('returns no warnings for non-attribute assignment types', function () {
        $attributeData = [
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'sku',
            // No attribute_ids - but that's okay for these assignment types
        ];
        
        $warnings = $this->validator->validateAssignmentTypes($attributeData);
        
        expect($warnings)->toBeEmpty();
    });
});

describe('AttributeDataValidationService Variant Data Validation', function () {
    
    it('validates correct variant data', function () {
        $variantData = [
            'attributes' => [
                [
                    'attribute_id' => $this->colorAttribute->id,
                    'value_id' => $this->redValue->id
                ],
                [
                    'attribute_id' => $this->sizeAttribute->id,
                    'value_id' => $this->smallValue->id
                ]
            ]
        ];
        
        $result = $this->validator->validateVariantData($variantData);
        
        expect($result)->toBeTrue();
    });
    
    it('returns false for empty variant data', function () {
        $result = $this->validator->validateVariantData([]);
        
        expect($result)->toBeFalse();
    });
    
    it('returns false for variant data without attributes', function () {
        $variantData = [
            'pricing' => ['price' => 100],
            'quantity' => ['stock' => 50]
        ];
        
        $result = $this->validator->validateVariantData($variantData);
        
        expect($result)->toBeFalse();
    });
    
    it('returns false for variant data with empty attributes', function () {
        $variantData = [
            'attributes' => []
        ];
        
        $result = $this->validator->validateVariantData($variantData);
        
        expect($result)->toBeFalse();
    });
    
    it('returns false for variant attributes missing required fields', function () {
        $variantData = [
            'attributes' => [
                [
                    'attribute_id' => $this->colorAttribute->id,
                    // Missing 'value_id'
                ]
            ]
        ];
        
        $result = $this->validator->validateVariantData($variantData);
        
        expect($result)->toBeFalse();
    });
    
    it('returns false for variant attributes with only value_id', function () {
        $variantData = [
            'attributes' => [
                [
                    'value_id' => $this->redValue->id,
                    // Missing 'attribute_id'
                ]
            ]
        ];
        
        $result = $this->validator->validateVariantData($variantData);
        
        expect($result)->toBeFalse();
    });
});

describe('AttributeDataValidationService Data Sanitization', function () {
    
    it('removes null and empty values from attribute data', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => '',
            'empty_field' => null,
            'another_empty' => '',
            'valid_field' => 'valid_value'
        ];
        
        $sanitized = $this->validator->sanitizeAttributeData($attributeData);
        
        expect($sanitized)
            ->toHaveKey('attribute_ids')
            ->toHaveKey('attribute_values_1')
            ->toHaveKey('price_type_toggle')
            ->toHaveKey('valid_field')
            ->not->toHaveKey('stock_type_toggle')
            ->not->toHaveKey('empty_field')
            ->not->toHaveKey('another_empty');
    });
    
    it('converts numeric strings to integers for specific fields', function () {
        $attributeData = [
            'attribute_ids' => ['1', '2'],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'other_field' => '123'
        ];
        
        $sanitized = $this->validator->sanitizeAttributeData($attributeData);
        
        expect($sanitized['attribute_ids'])->toBe(['1', '2']); // Arrays stay as arrays
        expect($sanitized['price_type_toggle'])->toBe('fixed'); // String stays string
        expect($sanitized['stock_type_toggle'])->toBe('stock'); // String stays string
        expect($sanitized['other_field'])->toBe('123'); // Other fields unchanged
    });
    
    it('handles mixed data types correctly', function () {
        $attributeData = [
            'attribute_ids' => [1, 2, 3],
            'string_field' => 'test',
            'numeric_string' => '456',
            'boolean_field' => true,
            'null_field' => null,
            'zero_value' => 0,
            'empty_string' => ''
        ];
        
        $sanitized = $this->validator->sanitizeAttributeData($attributeData);
        
        expect($sanitized)
            ->toHaveKey('attribute_ids')
            ->toHaveKey('string_field')
            ->toHaveKey('numeric_string')
            ->toHaveKey('boolean_field')
            ->toHaveKey('zero_value') // 0 should be kept
            ->not->toHaveKey('null_field')
            ->not->toHaveKey('empty_string');
    });
});

describe('AttributeDataValidationService Complex Scenarios', function () {
    
    it('validates complex multi-attribute scenario', function () {
        // Create additional attributes
        $brandAttribute = Attribute::factory()->create(['name' => 'Brand']);
        $materialAttribute = Attribute::factory()->create(['name' => 'Material']);
        
        $nikeValue = AttributeValue::factory()->create([
            'attribute_id' => $brandAttribute->id,
            'name' => 'Nike'
        ]);
        $adidasValue = AttributeValue::factory()->create([
            'attribute_id' => $brandAttribute->id,
            'name' => 'Adidas'
        ]);
        $cottonValue = AttributeValue::factory()->create([
            'attribute_id' => $materialAttribute->id,
            'name' => 'Cotton'
        ]);
        $polyesterValue = AttributeValue::factory()->create([
            'attribute_id' => $materialAttribute->id,
            'name' => 'Polyester'
        ]);
        
        $attributeData = [
            'attribute_ids' => [
                $this->colorAttribute->id,
                $this->sizeAttribute->id,
                $brandAttribute->id,
                $materialAttribute->id
            ],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
            'attribute_values_3' => [$nikeValue->id, $adidasValue->id],
            'attribute_values_4' => [$cottonValue->id, $polyesterValue->id],
            'price_type_toggle' => 'tier',
            'stock_type_toggle' => 'batch'
        ];
        
        $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
        
        expect($result)->toBe($attributeData);
    });
    
    it('handles partial attribute value assignments correctly', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id, $this->sizeAttribute->id],
            'attribute_values_1' => [$this->redValue->id], // Only one color
            'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id], // Both sizes
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock'
        ];
        
        $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
        
        expect($result)->toBe($attributeData);
    });
    
    it('validates all pricing and stock type combinations', function () {
        $priceTypes = ['fixed', 'bonus', 'tier'];
        $stockTypes = ['stock', 'batch'];
        
        foreach ($priceTypes as $priceType) {
            foreach ($stockTypes as $stockType) {
                $attributeData = [
                    'attribute_ids' => [$this->colorAttribute->id],
                    'attribute_values_1' => [$this->redValue->id],
                    'price_type_toggle' => $priceType,
                    'stock_type_toggle' => $stockType
                ];
                
                $result = $this->validator->validateAttributeData($this->product->id, $attributeData);
                
                expect($result)->toBe($attributeData);
            }
        }
    });
}); 