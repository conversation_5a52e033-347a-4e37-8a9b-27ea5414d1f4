<?php

declare(strict_types=1);

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use App\Models\User;
use App\Services\PostSaveAttributeService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Models\Activity;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test user and authenticate
    $this->user = User::factory()->create();
    Auth::login($this->user);
    
    // Create test product
    $this->product = Product::factory()->create([
        'name' => 'Test Product',
        'sku' => 'TEST-001'
    ]);
    
    // Create test attributes and values
    $this->colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $this->sizeAttribute = Attribute::factory()->create(['name' => 'Size']);
    
    $this->redValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Red'
    ]);
    $this->blueValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Blue'
    ]);
    $this->smallValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Small'
    ]);
    $this->largeValue = AttributeValue::factory()->create([
        'attribute_id' => $this->sizeAttribute->id,
        'name' => 'Large'
    ]);
    
    $this->service = new PostSaveAttributeService();
});

describe('PostSaveAttributeService Success Scenarios', function () {
    
    it('skips processing when no variant data is provided', function () {
        $formData = ['has_variants' => false];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('status', 'skipped')
            ->toHaveKey('message', 'No variant data to process');
    });
    
    it('skips processing when has_variants is false', function () {
        $formData = [
            'has_variants' => false,
            'attribute_data' => ['some' => 'data']
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('status', 'skipped');
    });
    
    it('creates simple variants with single attribute', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 2)
            ->toHaveKey('product_id', $this->product->id);
            
        // Verify variants created
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        expect($variants)->toHaveCount(2);
        
        // Verify variant attributes
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(1);
        }
    });
    
    it('creates complex variants with multiple attributes', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id, $this->sizeAttribute->id],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 4); // 2 colors × 2 sizes = 4 variants
            
        // Verify all combinations created
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        expect($variants)->toHaveCount(4);
        
        // Each variant should have 2 attributes
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(2);
        }
    });
    
    it('creates variants with fixed pricing', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single',
            'single_price_east' => 100.00,
            'single_price_west' => 120.00
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('pricing_records', 1);
            
        // Verify pricing record created
        $variant = ProductVariant::where('product_id', $this->product->id)->first();
        $priceRecord = ProductRelationPrice::where('product_variant_id', $variant->id)->first();
        
        expect($priceRecord)
            ->not->toBeNull();
            
        // Verify pricing data was saved (check for zone prices since that's what fixed pricing uses)
        expect($priceRecord->west_zone_price)->toBe('120.00');
        expect($priceRecord->east_zone_price)->toBe('100.00');
    });
    
    it('creates variants with stock data', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('stock_records', 1);
            
        // Verify stock record created
        $variant = ProductVariant::where('product_id', $this->product->id)->first();
        $stockRecord = ProductRelationStock::where('product_variant_id', $variant->id)->first();
        
        expect($stockRecord)
            ->not->toBeNull();
            
        // Verify stock data was saved
        expect($stockRecord->stock)->toBeInt();
        expect($stockRecord->is_batch_wise_stock)->toBeFalse(); // 'stock' type means not batch-wise
    });
    
    it('logs activity after successful variant creation', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $this->service->processAttributeData($this->product, $formData);
        
        // Verify activity logged
        $activity = Activity::where('subject_type', Product::class)
            ->where('subject_id', $this->product->id)
            ->where('causer_id', $this->user->id)
            ->first();
            
        expect($activity)
            ->not->toBeNull()
            ->and($activity->description)->toContain('Product variants created');
    });
    
    it('clears existing variants when updating', function () {
        // Create initial variant
        $existingVariant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'sku' => 'OLD-VARIANT'
        ]);
        
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $this->service->processAttributeData($this->product, $formData);
        
        // Verify old variant was deleted
        expect(ProductVariant::find($existingVariant->id))->toBeNull();
        
        // Verify new variant created
        $newVariants = ProductVariant::where('product_id', $this->product->id)->get();
        expect($newVariants)->toHaveCount(1);
        expect($newVariants->first()->sku)->not->toBe('OLD-VARIANT');
    });
});

describe('PostSaveAttributeService Error Handling', function () {
    
    it('handles validation errors gracefully', function () {
        // Create attribute data with invalid attribute ID
        $attributeData = [
            'attribute_ids' => [99999], // Non-existent attribute
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        expect(fn() => $this->service->processAttributeData($this->product, $formData))
            ->toThrow(Exception::class);
    });
    
    it('handles database transaction rollback on error', function () {
        // Mock a scenario that will fail after some variants are created
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        // Force an error by dropping a required table temporarily
        DB::statement('ALTER TABLE product_variant_attributes RENAME TO product_variant_attributes_temp');
        
        try {
            $this->service->processAttributeData($this->product, $formData);
        } catch (Exception $e) {
            // Expected to fail
        } finally {
            // Restore table
            DB::statement('ALTER TABLE product_variant_attributes_temp RENAME TO product_variant_attributes');
        }
        
        // Verify no variants were created due to rollback
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        expect($variants)->toHaveCount(0);
    });
    
    it('throws exception when no valid variant combinations can be generated', function () {
        // Create attribute data that will result in no valid combinations
        // (using non-existent attribute values)
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [99999], // Non-existent attribute value
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        // Since the attribute value doesn't exist, the validation service should throw an exception
        expect(fn() => $this->service->processAttributeData($this->product, $formData))
            ->toThrow(\Illuminate\Validation\ValidationException::class);
    });
});

describe('PostSaveAttributeService Edge Cases', function () {
    
    it('handles missing attribute values gracefully', function () {
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [99999], // Non-existent attribute value
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        expect(fn() => $this->service->processAttributeData($this->product, $formData))
            ->toThrow(Exception::class);
    });
    
    it('handles media upload errors without failing variant creation', function () {
        // This would require mocking the media library, but the service should 
        // continue even if media upload fails
        $attributeData = [
            'attribute_ids' => [$this->colorAttribute->id],
            'attribute_values_1' => [$this->redValue->id],
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 1);
    });
    
    it('handles different pricing types correctly', function () {
        foreach (['fixed', 'bonus', 'tier'] as $priceType) {
            // Clear previous variants and their related data
            $variants = ProductVariant::where('product_id', $this->product->id)->get();
            foreach ($variants as $variant) {
                DB::table('product_variant_attributes')->where('product_variant_id', $variant->id)->delete();
                DB::table('product_relation_prices')->where('product_variant_id', $variant->id)->delete();
                DB::table('product_relation_stocks')->where('product_variant_id', $variant->id)->delete();
                $variant->delete();
            }
            
            $attributeData = [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => $priceType,
                'stock_type_toggle' => 'stock',
                'price_assignment_type' => 'single',
                'quantity_assignment_type' => 'single'
            ];
            
            // Add pricing data based on the type
            if ($priceType === 'fixed') {
                $attributeData['single_price_east'] = 100.00;
                $attributeData['single_price_west'] = 120.00;
            } elseif ($priceType === 'bonus') {
                $attributeData['single_bonus_price_east'] = 100.00;
                $attributeData['single_bonus_price_west'] = 120.00;
            } elseif ($priceType === 'tier') {
                $attributeData['single_tier_1_price_east'] = 100.00;
                $attributeData['single_tier_1_price_west'] = 120.00;
            }
            
            $formData = [
                'has_variants' => true,
                'attribute_data' => $attributeData
            ];
            
            $result = $this->service->processAttributeData($this->product, $formData);
            
            expect($result)
                ->toHaveKey('status', 'success')
                ->toHaveKey('variants_created', 1);
                
            // Verify pricing record was created for the price type
            $variant = ProductVariant::where('product_id', $this->product->id)->first();
            $priceRecord = ProductRelationPrice::where('product_variant_id', $variant->id)->first();
            
            expect($priceRecord)->not->toBeNull();
            
            // Verify pricing record was created successfully for each type
            if ($priceType === 'fixed') {
                expect($priceRecord->west_zone_price)->not->toBeNull();
                expect($priceRecord->east_zone_price)->not->toBeNull();
            } elseif ($priceType === 'bonus') {
                // For bonus pricing, verify bonus price fields were saved
                expect($priceRecord->west_bonus_1_base_price)->toBe('120.00');
                expect($priceRecord->east_bonus_1_base_price)->toBe('100.00');
            } elseif ($priceType === 'tier') {
                // For tier pricing, just verify the record was created (tier data structure may be complex)
                expect($priceRecord->id)->not->toBeNull();
            }
        }
    });
    
    it('handles different stock types correctly', function () {
        foreach (['stock', 'batch'] as $stockType) {
            // Clear previous variants and their related data
            $variants = ProductVariant::where('product_id', $this->product->id)->get();
            foreach ($variants as $variant) {
                DB::table('product_variant_attributes')->where('product_variant_id', $variant->id)->delete();
                DB::table('product_relation_prices')->where('product_variant_id', $variant->id)->delete();
                DB::table('product_relation_stocks')->where('product_variant_id', $variant->id)->delete();
                $variant->delete();
            }
            
            $attributeData = [
                'attribute_ids' => [$this->colorAttribute->id],
                'attribute_values_1' => [$this->redValue->id],
                'price_type_toggle' => 'fixed',
                'stock_type_toggle' => $stockType,
                'price_assignment_type' => 'single',
                'quantity_assignment_type' => 'single'
            ];
            
            $formData = [
                'has_variants' => true,
                'attribute_data' => $attributeData
            ];
            
            $result = $this->service->processAttributeData($this->product, $formData);
            
            expect($result)
                ->toHaveKey('status', 'success')
                ->toHaveKey('variants_created', 1);
                
            // Verify stock record was created for the stock type
            $variant = ProductVariant::where('product_id', $this->product->id)->first();
            $stockRecord = ProductRelationStock::where('product_variant_id', $variant->id)->first();
            
            expect($stockRecord)->not->toBeNull();
            expect($stockRecord->is_batch_wise_stock)->toBe($stockType === 'batch');
        }
    });
});

describe('PostSaveAttributeService Performance', function () {
    
    it('handles large number of variant combinations efficiently', function () {
        // Create additional attributes and values for more combinations
        $brandAttribute = Attribute::factory()->create(['name' => 'Brand']);
        $materialAttribute = Attribute::factory()->create(['name' => 'Material']);
        
        $brandValues = AttributeValue::factory()->count(3)->create(['attribute_id' => $brandAttribute->id]);
        $materialValues = AttributeValue::factory()->count(3)->create(['attribute_id' => $materialAttribute->id]);
        
        $attributeData = [
            'attribute_ids' => [
                $this->colorAttribute->id, 
                $this->sizeAttribute->id, 
                $brandAttribute->id, 
                $materialAttribute->id
            ],
            'attribute_values_1' => [$this->redValue->id, $this->blueValue->id],
            'attribute_values_2' => [$this->smallValue->id, $this->largeValue->id],
            'attribute_values_3' => $brandValues->pluck('id')->toArray(),
            'attribute_values_4' => $materialValues->pluck('id')->toArray(),
            'price_type_toggle' => 'fixed',
            'stock_type_toggle' => 'stock',
            'price_assignment_type' => 'single',
            'quantity_assignment_type' => 'single'
        ];
        
        $formData = [
            'has_variants' => true,
            'attribute_data' => $attributeData
        ];
        
        $startTime = microtime(true);
        
        $result = $this->service->processAttributeData($this->product, $formData);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Should create 2 × 2 × 3 × 3 = 36 variants
        expect($result)
            ->toHaveKey('status', 'success')
            ->toHaveKey('variants_created', 36);
            
        // Should complete within reasonable time (less than 5 seconds)
        expect($executionTime)->toBeLessThan(5.0);
        
        // Verify all variants have correct number of attributes
        $variants = ProductVariant::where('product_id', $this->product->id)->get();
        foreach ($variants as $variant) {
            $variantAttributes = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->get();
            expect($variantAttributes)->toHaveCount(4);
        }
    });
}); 