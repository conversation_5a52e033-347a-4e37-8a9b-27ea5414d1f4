<?php

declare(strict_types=1);

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\DosageForm;
use App\Models\Container;
use App\Models\ProductRelation;
use App\Models\PcDetail;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;

class ProductCreationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Ensure we're using the testing database
        $this->assertEquals('testing', config('database.default'));
    }

    public function test_database_connection_is_testing()
    {
        // Verify we're using the testing database
        $this->assertEquals('testing', config('database.default'));
        $this->assertEquals(':memory:', config('database.connections.testing.database'));
    }

    public function test_product_relation_creation_inserts_all_required_data()
    {
        // Create required dependencies
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $unit = Unit::factory()->create();
        $dosageForm = DosageForm::factory()->create();
        $container = Container::factory()->create();
        
        // Create a PC user
        $pcUser = User::factory()->create();
        PcDetail::factory()->create([
            'user_id' => $pcUser->id,
            'commission_percentage' => 10,
            'commission_type' => 'percentage'
        ]);

        // Create a product
        $product = Product::factory()->create([
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'unit_id' => $unit->id,
            'dosage_foams_id' => $dosageForm->id,
            'container_id' => $container->id,
            'quantity_per_unit' => 100,
        ]);

        // Create a product relation
        $productRelation = ProductRelation::create([
            'product_id' => $product->id,
            'price_type' => 'fixed',
            'user_id' => $pcUser->id,
            'admin_approval' => true,
            'requested_by' => $pcUser->id,
            'quantity_per_unit' => $product->quantity_per_unit,
            'unit_id' => $product->unit_id,
            'dosage_foams_id' => $product->dosage_foams_id,
            'sku' => 'TEST_SKU_' . rand(100000, 999999),
        ]);

        // Test that prices can be inserted
        DB::table('product_relation_prices')->insert([
            'product_relation_id' => $productRelation->id,
            'east_zone_price' => 100.50,
            'west_zone_price' => 95.75,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Test that stocks can be inserted
        DB::table('product_relation_stocks')->insert([
            'product_relation_id' => $productRelation->id,
            'is_batch_wise_stock' => false,
            'wholesale_pack_size' => 1,
            'low_stock' => 10,
            'stock' => 100,
            'stock_type' => 'unit',
            'total_stock' => 100,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Verify the data was inserted correctly
        $this->assertDatabaseHas('products_relation', [
            'id' => $productRelation->id,
            'product_id' => $product->id,
            'user_id' => $pcUser->id,
            'price_type' => 'fixed',
        ]);

        $this->assertDatabaseHas('product_relation_prices', [
            'product_relation_id' => $productRelation->id,
            'east_zone_price' => 100.50,
            'west_zone_price' => 95.75,
        ]);

        $this->assertDatabaseHas('product_relation_stocks', [
            'product_relation_id' => $productRelation->id,
            'is_batch_wise_stock' => false,
            'stock' => 100,
            'stock_type' => 'unit',
        ]);
    }

    public function test_bonus_pricing_data_can_be_inserted()
    {
        // Create required dependencies
        $category = Category::factory()->create();
        $brand = Brand::factory()->create();
        $unit = Unit::factory()->create();
        $dosageForm = DosageForm::factory()->create();
        $container = Container::factory()->create();
        
        // Create a PC user
        $pcUser = User::factory()->create();
        PcDetail::factory()->create([
            'user_id' => $pcUser->id,
            'commission_percentage' => 10,
            'commission_type' => 'percentage'
        ]);

        // Create a product
        $product = Product::factory()->create([
            'category_id' => $category->id,
            'brand_id' => $brand->id,
            'unit_id' => $unit->id,
            'dosage_foams_id' => $dosageForm->id,
            'container_id' => $container->id,
            'quantity_per_unit' => 100,
        ]);

        // Create a product relation
        $productRelation = ProductRelation::create([
            'product_id' => $product->id,
            'price_type' => 'bonus',
            'user_id' => $pcUser->id,
            'admin_approval' => true,
            'requested_by' => $pcUser->id,
            'quantity_per_unit' => $product->quantity_per_unit,
            'unit_id' => $product->unit_id,
            'dosage_foams_id' => $product->dosage_foams_id,
            'sku' => 'TEST_BONUS_' . rand(100000, 999999),
        ]);

        // Test that bonus pricing data can be inserted (including the previously missing fields)
        DB::table('product_relation_prices')->insert([
            'product_relation_id' => $productRelation->id,
            'east_zone_price' => 100.00,
            'west_zone_price' => 95.00,
            'east_bonus_1_quantity' => 10,
            'east_bonus_1_quantity_value' => 2,
            'east_bonus_1_base_price' => 100.00,
            'east_bonus_2_base_price' => 95.00,
            'east_bonus_3_base_price' => 90.00,
            'west_bonus_1_quantity' => 10,
            'west_bonus_1_quantity_value' => 2,
            'west_bonus_1_base_price' => 95.00,
            'west_bonus_2_base_price' => 90.00,
            'west_bonus_3_base_price' => 85.00,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Verify all bonus pricing fields were inserted correctly
        $this->assertDatabaseHas('product_relation_prices', [
            'product_relation_id' => $productRelation->id,
            'east_bonus_1_quantity' => 10,
            'east_bonus_1_quantity_value' => 2,
            'east_bonus_1_base_price' => 100.00,
            'east_bonus_2_base_price' => 95.00,
            'east_bonus_3_base_price' => 90.00,
            'west_bonus_1_base_price' => 95.00,
            'west_bonus_2_base_price' => 90.00,
            'west_bonus_3_base_price' => 85.00,
        ]);
    }
} 