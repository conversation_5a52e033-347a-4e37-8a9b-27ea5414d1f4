<?php

declare(strict_types=1);

use App\Models;
use App\Http\Controllers;
use App\Filament;
use App\Services;
use App\Actions;
use App\Jobs;
use App\Mail;
use App\Policies;

/*
|--------------------------------------------------------------------------
| Code Quality & Debugging
|--------------------------------------------------------------------------
*/

test('no debugging statements in Controllers')
    ->expect('App\Http\Controllers')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Models')
    ->expect('App\Models')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Services')
    ->expect('App\Services')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Actions')
    ->expect('App\Actions')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Jobs')
    ->expect('App\Jobs')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Mail')
    ->expect('App\Mail')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Filament Admin')
    ->expect('App\Filament\Admin')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Filament PC')
    ->expect('App\Filament\Pc')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Filament Clinic')
    ->expect('App\Filament\Clinic')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Livewire')
    ->expect('App\Livewire')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Helpers')
    ->expect('App\Helpers')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Repositories')
    ->expect('App\Repositories')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in Database namespace')
    ->expect('Database')
    ->not->toUse(['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit']);

test('no debugging statements in modules directory', function () {
    $moduleDirectories = [
        'modules/cms-pages/src',
        'modules/email-template/src',
        'modules/filament-email-2fa-main/src',
        'modules/filament-shield/src',
        'modules/filament-social-master/src',
        'modules/settings/src',
    ];
    
    $debuggingFunctions = ['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit'];
    $foundDebugging = [];
    
    foreach ($moduleDirectories as $directory) {
        $fullPath = base_path($directory);
        if (!is_dir($fullPath)) {
            continue;
        }
        
        $files = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($fullPath, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($files as $file) {
            if ($file->getExtension() !== 'php') {
                continue;
            }
            
            // Skip vendor directories
            if (strpos($file->getPathname(), '/vendor/') !== false) {
                continue;
            }
            
            $content = file_get_contents($file->getPathname());
            $lines = explode("\n", $content);
            
            foreach ($lines as $lineNumber => $line) {
                $trimmedLine = trim($line);
                
                // Skip commented lines
                if (str_starts_with($trimmedLine, '//') || 
                    str_starts_with($trimmedLine, '*') || 
                    str_starts_with($trimmedLine, '/*')) {
                    continue;
                }
                
                foreach ($debuggingFunctions as $function) {
                    // Check for function calls (with opening parenthesis)
                    if (preg_match('/\b' . preg_quote($function, '/') . '\s*\(/', $line)) {
                        // Allow exit() calls in console commands as they can be legitimate
                        if ($function === 'exit' && strpos($file->getPathname(), '/Commands/') !== false) {
                            continue;
                        }
                        
                        $foundDebugging[] = [
                            'file' => str_replace(base_path(), '', $file->getPathname()),
                            'line' => $lineNumber + 1,
                            'function' => $function,
                            'content' => $trimmedLine
                        ];
                    }
                }
            }
        }
    }
    
    if (!empty($foundDebugging)) {
        $message = "Found debugging statements in modules:\n";
        foreach ($foundDebugging as $debug) {
            $message .= "- {$debug['function']}() in {$debug['file']}:{$debug['line']} -> {$debug['content']}\n";
        }
        throw new Exception($message);
    }
    
    expect($foundDebugging)->toBeEmpty();
});

test('no var_export in App namespace')
    ->expect('App')
    ->not->toUse(['var_export']);

test('no console.log in our JavaScript files')
    ->expect(['console.log', 'console.error', 'console.warn'])
    ->not->toBeUsed()
    ->ignoring('node_modules')
    ->ignoring('vendor')
    ->ignoring('public/js/filament')
    ->ignoring('public/css');

// test('no Laravel debugging helpers')
//     ->expect(['logger', 'info', 'debug'])
//     ->not->toBeUsed()
//     ->ignoring('vendor')
//     ->ignoring('config')
//     ->ignoring('bootstrap');

// test('no print statements in app directory')
//     ->expect(['print', 'printf'])
//     ->not->toBeUsed()
//     ->ignoring('vendor')
//     ->ignoring('resources/views')
//     ->ignoring('node_modules');

// test('controllers should not use Log facade for debugging')
//     ->expect('App\Http\Controllers')
//     ->not->toUse('Illuminate\Support\Facades\Log');

// test('models should not use Log facade for debugging')
//     ->expect('App\Models')
//     ->not->toUse('Illuminate\Support\Facades\Log');

// /*
// |--------------------------------------------------------------------------
// | Laravel Conventions
// |--------------------------------------------------------------------------
// */

// test('controllers')
//     ->expect('App\Http\Controllers')
//     ->toOnlyUse([
//         'Illuminate\Http\Request',
//         'Illuminate\Http\Response',
//         'Illuminate\Http\JsonResponse',
//         'Illuminate\Http\RedirectResponse',
//         'Illuminate\Routing\Controller',
//         'Illuminate\Foundation\Auth\Access\AuthorizesRequests',
//         'Illuminate\Foundation\Validation\ValidatesRequests',
//         'Illuminate\Validation\ValidationException',
//         'Illuminate\Support\Facades',
//         'Illuminate\Support',
//         'App\Models',
//         'App\Services',
//         'App\Actions',
//         'App\Http\Requests',
//         'App\Http\Resources',
//         'App\Jobs',
//         'Carbon\Carbon',
//         'Filament',
//         'Livewire',
//     ])
//     ->and('App\Http\Controllers')
//     ->toHaveSuffix('Controller')
//     ->toBeFinal()
//     ->not->toBeUsed();

// test('models follow conventions')
//     ->expect('App\Models')
//     ->toExtend('Illuminate\Database\Eloquent\Model')
//     ->toOnlyUse([
//         'Illuminate\Database\Eloquent',
//         'Illuminate\Database\Eloquent\Model',
//         'Illuminate\Database\Eloquent\Relations',
//         'Illuminate\Database\Eloquent\Factories',
//         'Illuminate\Database\Eloquent\SoftDeletes',
//         'Illuminate\Support',
//         'Carbon\Carbon',
//         'Spatie',
//         'App\Models',
//         'App\Traits',
//         'App\Enums',
//         'App\Observers',
//     ]);

// test('services are final classes')
//     ->expect('App\Services')
//     ->toBeFinal()
//     ->toHaveSuffix('Service');

// test('actions are final classes')
//     ->expect('App\Actions')
//     ->toBeFinal()
//     ->toHaveSuffix('Action');

// test('jobs implement ShouldQueue')
//     ->expect('App\Jobs')
//     ->toImplement('Illuminate\Contracts\Queue\ShouldQueue')
//     ->toHaveSuffix('Job');

// test('mail classes extend Mailable')
//     ->expect('App\Mail')
//     ->toExtend('Illuminate\Mail\Mailable')
//     ->toHaveSuffix('Mail');

// test('policies follow naming convention')
//     ->expect('App\Policies')
//     ->toHaveSuffix('Policy')
//     ->toOnlyUse([
//         'Illuminate\Auth\Access\HandlesAuthorization',
//         'Illuminate\Auth\Access\Response',
//         'App\Models',
//         'Illuminate\Support',
//     ]);

// /*
// |--------------------------------------------------------------------------
// | Filament Architecture
// |--------------------------------------------------------------------------
// */

// test('filament resources follow conventions')
//     ->expect('App\Filament')
//     ->toOnlyUse([
//         'Filament',
//         'Illuminate\Database\Eloquent\Builder',
//         'Illuminate\Database\Eloquent\Model',
//         'Illuminate\Support',
//         'Illuminate\Validation',
//         'Illuminate\Http',
//         'App\Models',
//         'App\Services',
//         'App\Actions',
//         'App\Enums',
//         'App\Policies',
//         'App\Filters',
//         'App\Forms\Components',
//         'App\Infolists\Components',
//         'Carbon\Carbon',
//         'Spatie',
//         'Livewire',
//         'Awcodes',
//     ]);

// test('filament resources have correct suffix')
//     ->expect('App\Filament\Admin\Resources')
//     ->toHaveSuffix('Resource')
//     ->and('App\Filament\Pc\Resources')
//     ->toHaveSuffix('Resource')
//     ->and('App\Filament\Clinic\Resources')
//     ->toHaveSuffix('Resource');

// test('filament pages have correct suffix')
//     ->expect('App\Filament\Admin\Pages')
//     ->classes()
//     ->toHaveSuffix('Page')
//     ->and('App\Filament\Pc\Pages')
//     ->classes()
//     ->toHaveSuffix('Page')
//     ->and('App\Filament\Clinic\Pages')
//     ->classes()
//     ->toHaveSuffix('Page');

// /*
// |--------------------------------------------------------------------------
// | Dependency Rules
// |--------------------------------------------------------------------------
// */

// test('models should not use controllers')
//     ->expect('App\Models')
//     ->not->toUse('App\Http\Controllers');

// test('controllers should not use jobs directly')
//     ->expect('App\Http\Controllers')
//     ->not->toUse('App\Jobs');

// test('models should not use HTTP classes')
//     ->expect('App\Models')
//     ->not->toUse([
//         'Illuminate\Http\Request',
//         'Illuminate\Http\Response',
//         'Symfony\Component\HttpFoundation',
//     ]);

// test('services should not use HTTP classes directly')
//     ->expect('App\Services')
//     ->not->toUse([
//         'Illuminate\Http\Request',
//         'Illuminate\Http\Response',
//         'Illuminate\Http\JsonResponse',
//     ]);

// /*
// |--------------------------------------------------------------------------
// | Security & Best Practices
// |--------------------------------------------------------------------------
// */

// test('no raw SQL queries in models')
//     ->expect('App\Models')
//     ->not->toUse(['DB::raw', 'DB::statement']);

// test('proper request validation')
//     ->expect('App\Http\Controllers')
//     ->not->toUse('Illuminate\Http\Request')
//     ->when(fn ($class) => !str_contains($class, 'Api'));

// test('strict types declaration')
//     ->expect('App')
//     ->toUseStrictTypes();

// /*
// |--------------------------------------------------------------------------
// | Naming Conventions
// |--------------------------------------------------------------------------
// */

// test('repositories have correct suffix')
//     ->expect('App\Repositories')
//     ->toHaveSuffix('Repository');

// test('traits have correct suffix')
//     ->expect('App\Traits')
//     ->toHaveSuffix('Trait');

// test('enums are properly named')
//     ->expect('App\Enums')
//     ->toHaveSuffix('Enum')
//     ->toBeEnums();

// test('observers have correct suffix')
//     ->expect('App\Observers')
//     ->toHaveSuffix('Observer');

// /*
// |--------------------------------------------------------------------------
// | Performance & Optimization
// |--------------------------------------------------------------------------
// */

// test('no N+1 query indicators in models')
//     ->expect('App\Models')
//     ->not->toUse(['DB::enableQueryLog', 'DB::getQueryLog']);

// test('caching services use proper contracts')
//     ->expect('App\Services')
//     ->when(fn ($class) => str_contains($class, 'Cache'))
//     ->toUse(['Illuminate\Contracts\Cache\Repository']);

// /*
// |--------------------------------------------------------------------------
// | API Conventions
// |--------------------------------------------------------------------------
// */

// test('API controllers return JSON responses')
//     ->expect('App\Http\Controllers\Api')
//     ->toOnlyUse([
//         'Illuminate\Http\JsonResponse',
//         'Illuminate\Http\Request',
//         'Illuminate\Http\Response',
//         'Illuminate\Routing\Controller',
//         'Illuminate\Foundation\Auth\Access\AuthorizesRequests',
//         'Illuminate\Foundation\Validation\ValidatesRequests',
//         'Illuminate\Support',
//         'App\Models',
//         'App\Services',
//         'App\Http\Resources',
//         'App\Http\Requests\Api',
//         'App\Repositories',
//         'Carbon\Carbon',
//     ]);

// test('API resources have correct suffix')
//     ->expect('App\Http\Resources')
//     ->toHaveSuffix('Resource')
//     ->toExtend('Illuminate\Http\Resources\Json\JsonResource');

// /*
// |--------------------------------------------------------------------------
// | Module Architecture (if using modules)
// |--------------------------------------------------------------------------
// */

// test('modules follow directory structure')
//     ->expect('Modules')
//     ->when(fn () => file_exists(base_path('modules')))
//     ->toOnlyUse([
//         'Illuminate',
//         'App',
//         'Modules',
//         'Filament',
//     ]);

