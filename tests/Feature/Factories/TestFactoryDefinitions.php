<?php

declare(strict_types=1);

/**
 * Test Factory Definitions and Helpers
 * 
 * This file ensures all required model factories are available for testing.
 * Add any missing factory definitions here if they don't exist in your project.
 */

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\DosageForm;
use App\Models\Container;
use Illuminate\Support\Facades\DB;

// If these factories don't exist in your project, add them to database/factories/

/*
// Example Product Factory (database/factories/ProductFactory.php)
class ProductFactory extends Factory
{
    protected $model = Product::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->productName(),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}-[0-9]{3}'),
            'generic_name' => $this->faker->word(),
            'low_stock' => $this->faker->numberBetween(1, 10),
            'category_id' => Category::factory(),
            'sub_category_id' => Category::factory(),
            'dosage_foams_id' => DosageForm::factory(),
            'unit_id' => Unit::factory(),
            'quantity_per_unit' => $this->faker->numberBetween(1, 1000),
            'is_prescription_required' => $this->faker->boolean(),
            'commission_type' => 'percentage',
            'commission_amount' => $this->faker->randomFloat(2, 0, 100),
            'add_request_by' => User::factory(),
            'is_created_by_admin' => true,
        ];
    }
}

// Example ProductVariant Factory (database/factories/ProductVariantFactory.php)
class ProductVariantFactory extends Factory
{
    protected $model = ProductVariant::class;
    
    public function definition()
    {
        return [
            'product_id' => Product::factory(),
            'sku' => $this->faker->unique()->regexify('[A-Z]{3}-VAR-[0-9]{3}'),
        ];
    }
}

// Example Attribute Factory (database/factories/AttributeFactory.php)
class AttributeFactory extends Factory
{
    protected $model = Attribute::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->word(),
        ];
    }
}

// Example AttributeValue Factory (database/factories/AttributeValueFactory.php)
class AttributeValueFactory extends Factory
{
    protected $model = AttributeValue::class;
    
    public function definition()
    {
        return [
            'attribute_id' => Attribute::factory(),
            'name' => $this->faker->word(),
        ];
    }
}

// Example ProductRelationPrice Factory (database/factories/ProductRelationPriceFactory.php)
class ProductRelationPriceFactory extends Factory
{
    protected $model = ProductRelationPrice::class;
    
    public function definition()
    {
        return [
            'product_variant_id' => ProductVariant::factory(),
            'user_id' => User::factory(),
            'price_type' => $this->faker->randomElement(['fixed', 'bonus', 'tier']),
            'west_zone_price' => $this->faker->randomFloat(2, 10, 1000),
            'east_zone_price' => $this->faker->randomFloat(2, 10, 1000),
        ];
    }
}

// Example ProductRelationStock Factory (database/factories/ProductRelationStockFactory.php)
class ProductRelationStockFactory extends Factory
{
    protected $model = ProductRelationStock::class;
    
    public function definition()
    {
        return [
            'product_variant_id' => ProductVariant::factory(),
            'user_id' => User::factory(),
            'stock' => $this->faker->numberBetween(0, 1000),
            'low_stock' => $this->faker->numberBetween(1, 10),
            'stock_type' => $this->faker->randomElement(['stock', 'batch']),
            'is_batch_wise_stock' => $this->faker->boolean(),
            'total_stock' => $this->faker->numberBetween(0, 1000),
        ];
    }
}

// Helper factories for related models

// Category Factory
class CategoryFactory extends Factory
{
    protected $model = Category::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->word(),
        ];
    }
}

// Brand Factory
class BrandFactory extends Factory
{
    protected $model = Brand::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->company(),
        ];
    }
}

// Unit Factory
class UnitFactory extends Factory
{
    protected $model = Unit::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->randomElement(['mg', 'g', 'ml', 'l', 'units']),
        ];
    }
}

// DosageForm Factory
class DosageFormFactory extends Factory
{
    protected $model = DosageForm::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->randomElement(['Tablet', 'Capsule', 'Syrup', 'Injection', 'Cream']),
        ];
    }
}

// Container Factory
class ContainerFactory extends Factory
{
    protected $model = Container::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->randomElement(['Bottle', 'Blister', 'Vial', 'Tube', 'Box']),
        ];
    }
}

// User Factory (if not already exists)
class UserFactory extends Factory
{
    protected $model = User::class;
    
    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
        ];
    }
}
*/

// Test Helper Functions

/**
 * Create a complete test product with all required relationships
 */
function createTestProductWithRelations(): Product
{
    return Product::factory()->create([
        'category_id' => Category::factory()->create()->id,
        'sub_category_id' => Category::factory()->create()->id,
        'brand_id' => Brand::factory()->create()->id,
        'unit_id' => Unit::factory()->create()->id,
        'dosage_foams_id' => DosageForm::factory()->create()->id,
        'container_id' => Container::factory()->create()->id,
    ]);
}

/**
 * Create test attributes with values
 */
function createTestAttributesWithValues(): array
{
    $colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $sizeAttribute = Attribute::factory()->create(['name' => 'Size']);
    
    return [
        'color_attribute' => $colorAttribute,
        'size_attribute' => $sizeAttribute,
        'color_values' => [
            AttributeValue::factory()->create(['attribute_id' => $colorAttribute->id, 'name' => 'Red']),
            AttributeValue::factory()->create(['attribute_id' => $colorAttribute->id, 'name' => 'Blue']),
            AttributeValue::factory()->create(['attribute_id' => $colorAttribute->id, 'name' => 'Green']),
        ],
        'size_values' => [
            AttributeValue::factory()->create(['attribute_id' => $sizeAttribute->id, 'name' => 'Small']),
            AttributeValue::factory()->create(['attribute_id' => $sizeAttribute->id, 'name' => 'Medium']),
            AttributeValue::factory()->create(['attribute_id' => $sizeAttribute->id, 'name' => 'Large']),
        ]
    ];
}

/**
 * Create sample attribute data for testing
 */
function createSampleAttributeData(array $attributes): array
{
    return [
        'attribute_ids' => [
            $attributes['color_attribute']->id,
            $attributes['size_attribute']->id
        ],
        'attribute_values_1' => $attributes['color_values']->take(2)->pluck('id')->toArray(),
        'attribute_values_2' => $attributes['size_values']->take(2)->pluck('id')->toArray(),
        'price_type_toggle' => 'fixed',
        'stock_type_toggle' => 'stock'
    ];
}

/**
 * Create test form data for product creation with variants
 */
function createTestFormDataWithVariants(Product $product, array $attributeData): array
{
    return [
        'name' => $product->name . ' - Variant Test',
        'sku' => 'TEST-' . time(),
        'category_id' => $product->category_id,
        'sub_category_id' => $product->sub_category_id,
        'brand_id' => $product->brand_id,
        'unit_id' => $product->unit_id,
        'dosage_foams_id' => $product->dosage_foams_id,
        'container_id' => $product->container_id,
        'quantity_per_unit' => 500,
        'has_variants' => true,
        'attribute_data' => $attributeData
    ];
}

/**
 * Assert variant database state is correct
 */
function assertVariantDatabaseState(Product $product, int $expectedVariantCount, int $expectedAttributesPerVariant = 1): void
{
    // Check variants were created
    $variants = ProductVariant::where('product_id', $product->id)->get();
    expect($variants)->toHaveCount($expectedVariantCount);
    
    // Check each variant has correct attributes, pricing, and stock
    foreach ($variants as $variant) {
        // Check variant attributes
        $variantAttributes = DB::table('product_variant_attributes')
            ->where('product_variant_id', $variant->id)
            ->get();
        expect($variantAttributes)->toHaveCount($expectedAttributesPerVariant);
        
        // Check pricing record exists
        $priceRecord = ProductRelationPrice::where('product_variant_id', $variant->id)->first();
        expect($priceRecord)->not->toBeNull();
        
        // Check stock record exists
        $stockRecord = ProductRelationStock::where('product_variant_id', $variant->id)->first();
        expect($stockRecord)->not->toBeNull();
    }
}

/**
 * Create orphaned variant for cleanup testing
 */
function createOrphanedVariant(): ProductVariant
{
    return ProductVariant::factory()->create([
        'product_id' => 99999, // Non-existent product
        'sku' => 'ORPHANED-' . time()
    ]);
}

/**
 * Create orphaned variant with full related data
 */
function createOrphanedVariantWithRelatedData(): array
{
    $orphanedVariant = createOrphanedVariant();
    
    // Create test attribute for relation
    $attribute = Attribute::factory()->create();
    $attributeValue = AttributeValue::factory()->create(['attribute_id' => $attribute->id]);
    
    // Add related data
    $attributeId = DB::table('product_variant_attributes')->insertGetId([
        'product_variant_id' => $orphanedVariant->id,
        'attribute_id' => $attribute->id,
        'attribute_value_id' => $attributeValue->id,
        'created_at' => now(),
        'updated_at' => now(),
    ]);
    
    $priceRecord = ProductRelationPrice::factory()->create([
        'product_variant_id' => $orphanedVariant->id
    ]);
    
    $stockRecord = ProductRelationStock::factory()->create([
        'product_variant_id' => $orphanedVariant->id
    ]);
    
    return [
        'variant' => $orphanedVariant,
        'attribute_id' => $attributeId,
        'price_record' => $priceRecord,
        'stock_record' => $stockRecord
    ];
}

/**
 * Assert cleanup results are correct
 */
function assertCleanupResults(array $orphanedData, bool $shouldBeDeleted = true): void
{
    if ($shouldBeDeleted) {
        expect(ProductVariant::find($orphanedData['variant']->id))->toBeNull();
        expect(DB::table('product_variant_attributes')->where('id', $orphanedData['attribute_id'])->exists())->toBeFalse();
        expect(ProductRelationPrice::find($orphanedData['price_record']->id))->toBeNull();
        expect(ProductRelationStock::find($orphanedData['stock_record']->id))->toBeNull();
    } else {
        expect(ProductVariant::find($orphanedData['variant']->id))->not->toBeNull();
        expect(DB::table('product_variant_attributes')->where('id', $orphanedData['attribute_id'])->exists())->toBeTrue();
        expect(ProductRelationPrice::find($orphanedData['price_record']->id))->not->toBeNull();
        expect(ProductRelationStock::find($orphanedData['stock_record']->id))->not->toBeNull();
    }
} 