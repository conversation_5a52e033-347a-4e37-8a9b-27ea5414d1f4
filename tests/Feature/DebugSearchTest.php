<?php

declare(strict_types=1);

/*
|--------------------------------------------------------------------------
| Debug Statement Hunter
|--------------------------------------------------------------------------
| This test helps you find exact files and line numbers containing 
| debugging statements when the architecture tests fail.
*/

test('find debugging statements with file locations', function () {
    $debugStatements = ['dd', 'dump', 'ray', 'var_dump', 'print_r', 'die', 'exit'];
    $directories = ['app', 'database'];
    $findings = [];
    
    foreach ($directories as $directory) {
        if (!is_dir($directory)) continue;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->getExtension() !== 'php') continue;
            
            $content = file_get_contents($file->getPathname());
            $lines = explode("\n", $content);
            
            foreach ($lines as $lineNumber => $line) {
                foreach ($debugStatements as $statement) {
                    // Skip comments
                    $trimmedLine = trim($line);
                    if (str_starts_with($trimmedLine, '//') || str_starts_with($trimmedLine, '*') || str_starts_with($trimmedLine, '/*')) {
                        continue;
                    }
                    
                    // Look for the debugging statement
                    if (preg_match('/\b' . preg_quote($statement, '/') . '\s*\(/', $line)) {
                        $findings[] = [
                            'file' => $file->getPathname(),
                            'line' => $lineNumber + 1,
                            'statement' => $statement,
                            'code' => trim($line)
                        ];
                    }
                }
            }
        }
    }
    
    if (!empty($findings)) {
        $message = "Found debugging statements:\n";
        foreach ($findings as $finding) {
            $message .= sprintf(
                "📍 %s:%d - %s() - %s\n",
                $finding['file'],
                $finding['line'],
                $finding['statement'],
                $finding['code']
            );
        }
        
        // Make the test fail and show the exact locations
        expect($findings)->toBeEmpty($message);
    }
    
    // If no debugging statements found, test passes
    expect($findings)->toBeEmpty();
}); // Temporarily enabled to find debugging statements 