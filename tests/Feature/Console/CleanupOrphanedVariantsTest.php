<?php

declare(strict_types=1);

use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use App\Console\Commands\CleanupOrphanedVariants;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Create test user
    $this->user = \App\Models\User::factory()->create();
    
    // Create test product
    $this->product = Product::factory()->create([
        'name' => 'Test Product',
        'sku' => 'TEST-001'
    ]);
    
    // Create test attributes and values
    $this->colorAttribute = Attribute::factory()->create(['name' => 'Color']);
    $this->redValue = AttributeValue::factory()->create([
        'attribute_id' => $this->colorAttribute->id,
        'name' => 'Red'
    ]);
});

describe('CleanupOrphanedVariants Command Success Scenarios', function () {
    
    it('reports no orphaned variants when database is clean', function () {
        // Create a valid variant with proper product relationship
        $variant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'sku' => 'VALID-VARIANT'
        ]);
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)->toContain('No orphaned variants found');
        
        // Verify variant still exists
        expect(ProductVariant::find($variant->id))->not->toBeNull();
    });
    
    it('identifies orphaned variants correctly', function () {
        // Create a valid product and variant first
        $product = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $product->delete();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--dry-run' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Found 1 orphaned variants')
            ->toContain('Variant ID: ' . $orphanedVariant->id)
            ->toContain('DRY RUN - No data will be deleted');
        
        // Verify variant still exists after dry run
        expect(ProductVariant::find($orphanedVariant->id))->not->toBeNull();
    });
    
    it('shows cleanup plan in dry run mode', function () {
        // Create a valid product and variant first
        $product = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Add related data BEFORE deleting the product
        DB::table('product_variant_attributes')->insert([
            'product_variant_id' => $orphanedVariant->id,
            'attribute_id' => $this->colorAttribute->id,
            'attribute_value_id' => $this->redValue->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        // Create product relation for pricing (before deleting product)
        $productRelationId = DB::table('products_relation')->insertGetId([
            'product_id' => $product->id,
            'user_id' => $this->user->id,
            'requested_by' => $this->user->id,
            'pc_approval' => false,
            'admin_approval' => false,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        ProductRelationPrice::factory()->create([
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $orphanedVariant->id
        ]);
        
        ProductRelationStock::factory()->create([
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $orphanedVariant->id
        ]);
        
        // NOW delete the product to make the variant orphaned
        $product->delete();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--dry-run' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Cleanup plan:')
            ->toContain('Variant attributes: 1')
            ->toContain('Price records: 1')
            ->toContain('Stock records: 1')
            ->toContain('Media files: 0');
    });
    
    it('performs actual cleanup when forced', function () {
        // Create a valid product and variant first
        $product = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $product->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $product->delete();
        
        // Add related data
        $attributeId = DB::table('product_variant_attributes')->insertGetId([
            'product_variant_id' => $orphanedVariant->id,
            'attribute_id' => $this->colorAttribute->id,
            'attribute_value_id' => $this->redValue->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        $priceRecord = ProductRelationPrice::factory()->create([
            'product_variant_id' => $orphanedVariant->id,
            'price_type' => 'fixed'
        ]);
        
        $stockRecord = ProductRelationStock::factory()->create([
            'product_variant_id' => $orphanedVariant->id,
            'stock_type' => 'stock'
        ]);
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Cleanup completed successfully!')
            ->toContain('Variants | 1')
            ->toContain('Variant Attributes | 1')
            ->toContain('Price Records | 1')
            ->toContain('Stock Records | 1');
        
        // Verify all data was deleted
        expect(ProductVariant::find($orphanedVariant->id))->toBeNull();
        expect(DB::table('product_variant_attributes')->where('id', $attributeId)->exists())->toBeFalse();
        expect(ProductRelationPrice::find($priceRecord->id))->toBeNull();
        expect(ProductRelationStock::find($stockRecord->id))->toBeNull();
    });
    
    it('handles multiple orphaned variants', function () {
        // Create multiple valid products and variants first
        $product1 = Product::factory()->create();
        $product2 = Product::factory()->create();
        
        $orphanedVariant1 = ProductVariant::factory()->create([
            'product_id' => $product1->id,
            'sku' => 'ORPHANED-1'
        ]);
        
        $orphanedVariant2 = ProductVariant::factory()->create([
            'product_id' => $product2->id,
            'sku' => 'ORPHANED-2'
        ]);
        
        $product3 = Product::factory()->create();
        $orphanedVariant3 = ProductVariant::factory()->create([
            'product_id' => $product3->id,
            'sku' => 'ORPHANED-3'
        ]);
        
        // Delete the products to make the variants orphaned
        $product1->delete();
        $product2->delete();
        $product3->delete();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Found 3 orphaned variants')
            ->toContain('Variants | 3');
        
        // Verify all variants deleted
        expect(ProductVariant::find($orphanedVariant1->id))->toBeNull();
        expect(ProductVariant::find($orphanedVariant2->id))->toBeNull();
        expect(ProductVariant::find($orphanedVariant3->id))->toBeNull();
    });
    
    it('preserves valid variants while cleaning orphaned ones', function () {
        // Create valid variant
        $validVariant = ProductVariant::factory()->create([
            'product_id' => $this->product->id,
            'sku' => 'VALID-VARIANT'
        ]);
        
        // Create orphaned variant
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        // Verify valid variant preserved, orphaned variant deleted
        expect(ProductVariant::find($validVariant->id))->not->toBeNull();
        expect(ProductVariant::find($orphanedVariant->id))->toBeNull();
    });
});

describe('CleanupOrphanedVariants Command Interactive Mode', function () {
    
    it('cancels cleanup when user declines confirmation', function () {
        // Create orphaned variant
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        // Mock user input to decline confirmation
        $this->artisan('products:cleanup-orphaned-variants')
            ->expectsConfirmation('Do you want to proceed with cleanup?', 'no')
            ->expectsOutput('Cleanup cancelled.')
            ->assertExitCode(0);
        
        // Verify variant still exists
        expect(ProductVariant::find($orphanedVariant->id))->not->toBeNull();
    });
    
    it('proceeds with cleanup when user confirms', function () {
        // Create orphaned variant
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        // Mock user input to confirm
        $this->artisan('products:cleanup-orphaned-variants')
            ->expectsConfirmation('Do you want to proceed with cleanup?', 'yes')
            ->expectsOutput('Cleanup completed successfully!')
            ->assertExitCode(0);
        
        // Verify variant deleted
        expect(ProductVariant::find($orphanedVariant->id))->toBeNull();
    });
});

describe('CleanupOrphanedVariants Command Error Handling', function () {
    
    it('handles database errors gracefully', function () {
        // Create orphaned variant
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        // Simulate database error by dropping a table temporarily
        DB::statement('ALTER TABLE product_variants RENAME TO product_variants_temp');
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        // Restore table
        DB::statement('ALTER TABLE product_variants_temp RENAME TO product_variants');
        
        expect($exitCode)->toBe(1); // Command should fail
        
        $output = Artisan::output();
        expect($output)->toContain('Cleanup failed:');
    });
    
    it('handles media deletion errors gracefully', function () {
        // Create orphaned variant
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        // The command should still succeed even if media deletion fails
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        // Verify variant deleted despite media issues
        expect(ProductVariant::find($orphanedVariant->id))->toBeNull();
    });
});

describe('CleanupOrphanedVariants Command Transaction Safety', function () {
    
    it('uses database transactions for cleanup operations', function () {
        // Create orphaned variant with related data
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        ProductRelationPrice::factory()->create([
            'product_variant_id' => $orphanedVariant->id
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        // Start a transaction to test rollback behavior
        DB::beginTransaction();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        // Rollback the transaction
        DB::rollback();
        
        expect($exitCode)->toBe(0);
        
        // After rollback, the orphaned variant should still exist
        // because the command uses its own transaction
        expect(ProductVariant::find($orphanedVariant->id))->toBeNull(); // Should be deleted by command's transaction
    });
});

describe('CleanupOrphanedVariants Command Performance', function () {
    
    it('handles large number of orphaned variants efficiently', function () {
        // Create many orphaned variants
        $orphanedVariants = [];
        $orphanedProducts = [];
        for ($i = 0; $i < 100; $i++) {
            $orphanedProducts[$i] = Product::factory()->create();
            $orphanedVariants[] = ProductVariant::factory()->create([
                'product_id' => $orphanedProducts[$i]->id, // Different non-existent product IDs
                'sku' => 'ORPHANED-' . $i
            ]);
        }
        
        // Delete all products to make variants orphaned
        foreach ($orphanedProducts as $product) {
            $product->delete();
        }
        
        $startTime = microtime(true);
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)->toContain('Found 100 orphaned variants');
        
        // Should complete within reasonable time (less than 10 seconds)
        expect($executionTime)->toBeLessThan(10.0);
        
        // Verify all orphaned variants deleted
        foreach ($orphanedVariants as $variant) {
            expect(ProductVariant::find($variant->id))->toBeNull();
        }
    });
});

describe('CleanupOrphanedVariants Command Output Format', function () {
    
    it('displays results in table format', function () {
        // Create orphaned variant with all types of related data
        $orphanedProduct = Product::factory()->create();
        $orphanedVariant = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct->id,
            'sku' => 'ORPHANED-VARIANT'
        ]);
        
        // Delete the product to make the variant orphaned
        $orphanedProduct->delete();
        
        DB::table('product_variant_attributes')->insert([
            'product_variant_id' => $orphanedVariant->id,
            'attribute_id' => $this->colorAttribute->id,
            'attribute_value_id' => $this->redValue->id,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
        
        ProductRelationPrice::factory()->create([
            'product_variant_id' => $orphanedVariant->id
        ]);
        
        ProductRelationStock::factory()->create([
            'product_variant_id' => $orphanedVariant->id
        ]);
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Item')
            ->toContain('Count Deleted')
            ->toContain('Variants')
            ->toContain('Variant Attributes')
            ->toContain('Price Records')
            ->toContain('Stock Records')
            ->toContain('Media Files');
    });
    
    it('shows detailed progress during cleanup', function () {
        // Create multiple orphaned variants
        $orphanedProduct1 = Product::factory()->create();
        $orphanedProduct2 = Product::factory()->create();
        
        $orphanedVariant1 = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct1->id,
            'sku' => 'ORPHANED-1'
        ]);
        
        $orphanedVariant2 = ProductVariant::factory()->create([
            'product_id' => $orphanedProduct2->id,
            'sku' => 'ORPHANED-2'
        ]);
        
        // Delete the products to make the variants orphaned
        $orphanedProduct1->delete();
        $orphanedProduct2->delete();
        
        $exitCode = Artisan::call('products:cleanup-orphaned-variants', ['--force' => true]);
        
        expect($exitCode)->toBe(0);
        
        $output = Artisan::output();
        expect($output)
            ->toContain('Cleaning variant ID: ' . $orphanedVariant1->id)
            ->toContain('Cleaning variant ID: ' . $orphanedVariant2->id)
            ->toContain('Cleaned variant ' . $orphanedVariant1->id)
            ->toContain('Cleaned variant ' . $orphanedVariant2->id);
    });
}); 