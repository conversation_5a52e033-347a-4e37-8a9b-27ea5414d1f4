<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Redis;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Filament\Admin\Resources\UserManageResource;

class UserDeactivationLogoutTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_logout_when_deactivated()
    {
        // Create a test user
        $user = User::factory()->create([
            'is_active' => true,
            'email' => '<EMAIL>'
        ]);

        // Simulate user sessions in Redis
        $sessionId1 = 'test_session_1';
        $sessionId2 = 'test_session_2';
        $setKey = "user_sessions:{$user->id}";
        $prefix = config('database.redis.options.prefix', 'laravel_database_');

        // Add sessions to Redis
        Redis::sadd($setKey, [$sessionId1, $sessionId2]);
        Redis::set($prefix . $sessionId1, 'session_data_1');
        Redis::set($prefix . $sessionId2, 'session_data_2');

        // Verify sessions exist
        $this->assertTrue(Redis::exists($prefix . $sessionId1));
        $this->assertTrue(Redis::exists($prefix . $sessionId2));
        $this->assertEquals(2, Redis::scard($setKey));

        // Call the logout method
        UserManageResource::logoutUserById($user->id);

        // Verify sessions are deleted
        $this->assertFalse(Redis::exists($prefix . $sessionId1));
        $this->assertFalse(Redis::exists($prefix . $sessionId2));
        $this->assertEquals(0, Redis::scard($setKey));
    }

    public function test_user_deactivation_triggers_logout()
    {
        // Create a test user
        $user = User::factory()->create([
            'is_active' => true,
            'email' => '<EMAIL>'
        ]);

        // Simulate user sessions in Redis
        $sessionId = 'test_session';
        $setKey = "user_sessions:{$user->id}";
        $prefix = config('database.redis.options.prefix', 'laravel_database_');

        Redis::sadd($setKey, $sessionId);
        Redis::set($prefix . $sessionId, 'session_data');

        // Verify session exists
        $this->assertTrue(Redis::exists($prefix . $sessionId));

        // Deactivate user (this should trigger logout)
        $user->is_active = false;
        $user->save();

        // Manually call logout (in real scenario this would be called by the toggle callback)
        UserManageResource::logoutUserById($user->id);

        // Verify session is deleted
        $this->assertFalse(Redis::exists($prefix . $sessionId));
        $this->assertEquals(0, Redis::scard($setKey));
    }

    protected function setUp(): void
    {
        parent::setUp();
        
        // Clear Redis before each test
        Redis::flushdb();
    }

    protected function tearDown(): void
    {
        // Clear Redis after each test
        Redis::flushdb();
        
        parent::tearDown();
    }
}
