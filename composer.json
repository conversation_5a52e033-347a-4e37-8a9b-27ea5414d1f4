{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "abanoubnassem/filament-grecaptcha-field": "^0.0.10", "amidesfahani/filament-tinyeditor": "^2.1", "andreiio/blade-iconoir": "^4.6", "andreiio/blade-remix-icon": "^3.6", "awcodes/filament-table-repeater": "^3.1", "codeat3/blade-eos-icons": "^1.14", "codeat3/blade-pepicons": "^1.5", "codeat3/blade-phosphor-icons": "^2.2", "codeat3/blade-teeny-icons": "^1.11", "davidhsianturi/blade-bootstrap-icons": "^1.5", "defstudio/filament-searchable-input": "^1.0", "eightynine/filament-advanced-widgets": "^3.0", "elasticsearch/elasticsearch": "^8.16", "filament/filament": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filipfonal/filament-log-manager": "^2.0", "firebase/php-jwt": "^6.10", "flowframe/laravel-trend": "^0.4.0", "google/apiclient": "^2.0", "hasnayeen/blade-eva-icons": "^2.0", "indianic/cms-pages": "1.0.0", "indianic/email-template": "1.0.0", "indianic/filament-email-2fa": "1.0.0", "indianic/filament-shield": "1.0.0", "indianic/filament-social": "1.0.0", "indianic/settings": "1.0.0", "intervention/image": "^2.6", "laravel/framework": "^11.31", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.16", "laravel/tinker": "^2.9", "league/flysystem-aws-s3-v3": "^3.0", "leandrocfe/filament-apex-charts": "^3.1", "njxqlus/filament-progressbar": "^1.0", "nnjeim/world": "^1.1", "owenvoke/blade-fontawesome": "^2.8", "pxlrbt/filament-excel": "^2.3", "rmsramos/activitylog": "^1.0", "saade/filament-autograph": "^3.1", "socialiteproviders/apple": "^5.6", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-medialibrary": "^11.12", "spatie/laravel-pdf": "^1.5", "stripe/stripe-php": "^16.6", "tomatophp/console-helpers": "^1.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.7", "pestphp/pest-plugin-arch": "^3.0", "pestphp/pest-plugin-laravel": "^3.0"}, "repositories": [{"type": "path", "url": "./modules/*"}], "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/General.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}