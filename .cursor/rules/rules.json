{"rules": [{"name": "Always use the 11 version of Laravel", "description": "Always use the 11 version of <PERSON><PERSON> for new projects.", "severity": "error"}, {"name": "Always use the 3.2 version of Filament", "description": "Always use the 3.2 version of Filament for new projects.", "severity": "error"}, {"name": "Always use the 3 version of Livewire", "description": "Always use the 3 version of Livewire for new projects.", "severity": "error"}, {"name": "Always use saila to run php artisan commands", "description": "Always use saila to run php artisan commands.", "severity": "error"}, {"name": "Always use the context7 mcp in case of any doubt", "description": "Always use the context7 mcp in case of any doubt.", "severity": "error"}, {"name": "Always use the Alpine.js instead of javascript", "description": "Always use the Alpine.js instead of javascript.", "severity": "error"}]}