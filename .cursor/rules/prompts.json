{"prompts": [{"name": "Explain Code", "prompt": "Explain what this Laravel + Filament code does, and check if it follows best practices."}, {"name": "Refactor for Best Practices", "prompt": "Refactor this code to follow Laravel 11, Filament 3.2 and Livewire 3 best practices."}, {"name": "Add Comments", "prompt": "Add clear PHPDoc and inline comments to explain this code."}, {"name": "Generate Migration", "prompt": "Generate a Laravel migration for this model or table."}, {"name": "Write Pest Test", "prompt": "Write a Pest test for this code or feature."}]}