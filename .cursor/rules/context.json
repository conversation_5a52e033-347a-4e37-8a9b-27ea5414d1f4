{"project": "Laravel + Filament Multiplanel Ecommerce Platform", "description": "A B2B and B2C ecommerce platform built with Laravel 11, Filament 3.2, Livewire 3, Tailwind CSS, and Alpine.js. The project includes three panels: Admin, PC (Pharmaceutical Company), and Clinic. Built using Laravel Sail for local development.", "techStack": ["Laravel 11", "Filament PHP 3.2", "Livewire 3", "Tailwind CSS", "Alpine.js", "Spatie packages (activitylog, medialibrary, pdf)", "Stripe PHP", "Elasticsearch", "Laravel Socialite"], "panels": ["Admin: manage global settings, users, products, logs, reports", "PC: manage pharmaceutical catalogs, stock, promotions", "Clinic: browse/order products, manage staff, view orders"], "bestPractices": ["Follow official documentation (<PERSON><PERSON>, Filament, Livewire) for API and architecture", "Do not invent or guess Filament or Livewire methods; always check the docs or use contet7 MCP server", "Use service classes to separate business logic", "Prefer Blade components and Filament resources for UI", "Use Tailwind CSS classes, avoid inline styles", "Leverage Alpine.js for lightweight frontend behavior", "Validate data with FormRequest or Filament rules", "Write unit and feature tests with Pest", "Use ActivityLog for auditing key actions", "Keep controllers and Livewire components slim"], "docs": {"laravel": "https://laravel.com/docs/11.x", "filament": "https://filamentphp.com/docs/3.x", "livewire": "https://livewire.laravel.com/docs/3.x", "packages": "Check each package README or GitHub repo for usage"}, "important": ["If AI is unsure, always refer to the official documentation or package repo", "Do not guess method names, file paths, or APIs"], "build": "<PERSON><PERSON> (Docker) for local dev", "devTools": ["<PERSON><PERSON><PERSON>", "pint", "pail", "debugbar", "pestphp"], "note": "Project uses modules/* as local composer path repos. PHP version ^8.2. Composer scripts automate dev setup and upgrades."}