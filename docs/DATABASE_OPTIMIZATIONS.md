# Database Optimizations for Cached Queries

This document describes the database optimizations implemented to improve performance for cached queries, specifically targeting the duplicate `DosageForm::find()` and `Unit::all()` queries.

## 🎯 Problem Statement

The application was experiencing duplicate database queries in form components:
- `DosageForm::find($id)` was being called multiple times for the same ID
- `Unit::all()->pluck('name', 'id')` was being executed repeatedly
- Reactive form components were triggering unnecessary database calls

## 🚀 Solution Overview

### 1. **Application-Level Caching**
- Implemented caching methods in form pages
- Added automatic cache invalidation on model changes
- Created console commands for cache management

### 2. **Database-Level Optimizations**
- Added PostgreSQL-specific indexes
- Implemented composite and partial indexes
- Optimized foreign key relationships

## 📊 Database Indexes Added

### Primary Tables

#### `dosage_foams` Table
```sql
-- Composite index for status + soft delete filtering
CREATE INDEX idx_dosage_foams_status_deleted ON dosage_foams (status, deleted_at);

-- Composite index for name lookups with filtering
CREATE INDEX idx_dosage_foams_name_status_deleted ON dosage_foams (name, status, deleted_at);

-- Partial index for active records only (PostgreSQL specific)
CREATE INDEX idx_dosage_foams_active_records ON dosage_foams (id, name) 
WHERE status = true AND deleted_at IS NULL;
```

#### `units` Table
```sql
-- Composite index for status + soft delete filtering
CREATE INDEX idx_units_status_deleted ON units (status, deleted_at);

-- Composite index for name lookups with filtering
CREATE INDEX idx_units_name_status_deleted ON units (name, status, deleted_at);

-- Partial index for active records only (PostgreSQL specific)
CREATE INDEX idx_units_active_records ON units (id, name) 
WHERE status = true AND deleted_at IS NULL;
```

### Foreign Key Optimization

#### `products` Table
```sql
-- Foreign key indexes for better join performance
CREATE INDEX idx_products_dosage_foams_id ON products (dosage_foams_id);
CREATE INDEX idx_products_unit_id ON products (unit_id);
CREATE INDEX idx_products_status_deleted ON products (status, deleted_at);
```

#### `products_relation` Table
```sql
-- Foreign key indexes
CREATE INDEX idx_products_relation_dosage_foams_id ON products_relation (dosage_foams_id);
CREATE INDEX idx_products_relation_unit_id ON products_relation (unit_id);

-- Composite indexes for common query patterns
CREATE INDEX idx_products_relation_user_product ON products_relation (user_id, product_id);
CREATE INDEX idx_products_relation_approvals ON products_relation (admin_approval, pc_approval);
```

### Stock Management Optimization

#### `product_relation_stocks` Table
```sql
-- Indexes for stock filtering
CREATE INDEX idx_product_relation_stocks_stock_type ON product_relation_stocks (stock_type);
CREATE INDEX idx_product_relation_stocks_batch_wise ON product_relation_stocks (is_batch_wise_stock);
CREATE INDEX idx_product_relation_stocks_stock_expiry ON product_relation_stocks (stock, expiry_date);
```

## 🔧 Implementation Commands

### Run Migration
```bash
php artisan migrate
```

### Create Concurrent Indexes (Zero Downtime)
```bash
# Preview what will be created
php artisan db:create-concurrent-indexes --dry-run

# Create concurrent indexes for production
php artisan db:create-concurrent-indexes

# Or run the SQL script manually
psql -d your_database -f database/scripts/create_concurrent_indexes.sql
```

### Clear Form Caches
```bash
# Clear all form caches
php artisan cache:clear-forms

# Clear specific cache types
php artisan cache:clear-forms --type=unit
php artisan cache:clear-forms --type=dosage
```

### Analyze Performance
```bash
# Basic performance analysis
php artisan analyze:cached-queries

# Detailed analysis with query plans
php artisan analyze:cached-queries --explain
```

## 📈 Performance Benefits

### Query Optimization
- **Primary Key Lookups**: Optimized with existing indexes
- **Composite Queries**: Faster filtering with multi-column indexes
- **Partial Indexes**: Reduced index size and improved performance for active records
- **Foreign Key Joins**: Faster joins between related tables

### Cache Benefits
- **Eliminated Duplicate Queries**: Reduced database load by caching results
- **Automatic Invalidation**: Ensures data freshness when models change
- **Flexible Cache Management**: Console commands for manual control

## 🔍 Query Patterns Optimized

### 1. **DosageForm Queries**
```php
// Before: Multiple DB queries
DosageForm::find($id);  // Called multiple times
DosageForm::all()->pluck('name', 'id');  // Repeated calls

// After: Cached queries
$this->getCachedDosageForm($id);  // Cached result
$this->getCachedDosageFormOptions();  // Cached options
```

### 2. **Unit Queries**
```php
// Before: Repeated DB queries
Unit::all()->pluck('name', 'id');  // Multiple calls

// After: Cached query
$this->getCachedUnitOptions();  // Cached result
```

### 3. **Status Filtering**
```sql
-- Optimized with composite indexes
SELECT * FROM dosage_foams WHERE status = true AND deleted_at IS NULL;
SELECT * FROM units WHERE status = true AND deleted_at IS NULL;
```

## 🛠️ Monitoring and Maintenance

### Performance Monitoring
```bash
# Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename IN ('dosage_foams', 'units', 'products', 'products_relation')
ORDER BY tablename, idx_scan DESC;
```

### Cache Monitoring
```bash
# Analyze cache effectiveness
php artisan analyze:cached-queries
```

### Table Statistics
```sql
-- Monitor table growth and activity
SELECT 
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows
FROM pg_stat_user_tables 
WHERE tablename IN ('dosage_foams', 'units', 'products', 'products_relation');
```

## 🔮 Future Optimizations

### Potential Improvements
1. **Connection Pooling**: Implement PostgreSQL connection pooling
2. **Read Replicas**: Use read replicas for query-heavy operations
3. **Materialized Views**: For complex aggregations
4. **Cache Warming**: Implement cache warming strategies
5. **Query Optimization**: Monitor slow queries and optimize further

### Recommended Monitoring
- Set up query performance monitoring
- Monitor cache hit ratios
- Track index usage statistics
- Monitor database connection pools

## 🚨 Important Notes

### PostgreSQL Transaction Limitations
- **Migration Indexes**: The migration creates regular indexes (non-concurrent) to work within Laravel's transaction block
- **Concurrent Option**: For zero-downtime production deployment, use the concurrent index commands
- **Two-Phase Approach**: Run migration first, then concurrent indexes for optimal performance

### PostgreSQL Specific Features
- **Partial Indexes**: Leverages PostgreSQL's ability to index subsets of data
- **Query Planner**: Optimized for PostgreSQL's query planner
- **Concurrent Creation**: Available via separate commands for production use

### Cache Invalidation Strategy
- **Automatic**: Model events trigger cache clearing
- **Manual**: Console commands for manual cache management
- **Granular**: Individual and bulk cache clearing options

### Production Considerations
- Test index performance in production-like environment
- Monitor disk space usage (indexes consume storage)
- Use concurrent index creation for zero downtime
- Set up appropriate monitoring and alerting

### Index Creation Options

#### Option 1: Migration Only (Recommended for Development)
```bash
php artisan migrate
```
- Creates indexes within transaction
- Small downtime during index creation
- Suitable for development/staging

#### Option 2: Migration + Concurrent Indexes (Recommended for Production)
```bash
# 1. Run migration first
php artisan migrate

# 2. Create concurrent indexes (zero downtime)
php artisan db:create-concurrent-indexes
```
- Best of both worlds
- Zero downtime for production
- Optimized index structure 