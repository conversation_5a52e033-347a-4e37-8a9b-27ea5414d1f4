# Caching Optimization for Duplicate Query Elimination

This document describes the comprehensive caching system implemented to eliminate duplicate database queries identified in the application.

## Overview

The caching system addresses the following duplicate query patterns:
1. **Role lookups**: Repeated queries for 'Pharmaceutical Company' role
2. **State/City lookups**: Repeated fetching of location data by ID
3. **Complex user address queries**: Heavy queries with role-based filtering

## Implemented Solutions

### 1. Role Caching (`app/Models/Role.php`)

**Problem**: Multiple calls to `select * from "roles" where "name" = 'Pharmaceutical Company' and "guard_name" = 'web' limit 1`

**Solution**: 
- Added `getCachedByName()` method for optimized role lookups
- Automatic cache invalidation on role create/update/delete
- 5-minute cache TTL

**Usage**:
```php
// Instead of:
$role = Role::where('name', 'Pharmaceutical Company')->where('guard_name', 'web')->first();

// Use:
$role = Role::getCachedByName('Pharmaceutical Company');
```

### 2. Location Caching (`app/Services/LocationCacheService.php`)

**Problem**: Repeated State/City lookups like:
- `select * from "states" where "states"."id" in (2501)`
- `select * from "cities" where "cities"."id" in (66602)`

**Solution**:
- Centralized caching service for State and City models
- Bulk lookup methods for arrays of IDs
- Cached dropdown options for forms
- 5-minute cache TTL

**Usage**:
```php
// Single lookups
$state = LocationCacheService::getCachedState($id);
$city = LocationCacheService::getCachedCity($id);

// Bulk lookups
$states = LocationCacheService::getCachedStates([2501, 2502]);
$cities = LocationCacheService::getCachedCities([66602, 66603]);

// Dropdown options
$stateOptions = LocationCacheService::getCachedStatesByCountry(132); // Malaysia
$cityOptions = LocationCacheService::getCachedCitiesByState($stateId);
```

### 3. User Address Caching (`app/Services/UserAddressCacheService.php`)

**Problem**: Complex queries with multiple joins:
```sql
select * from "user_addresses" where exists (
  select * from "users" where "user_addresses"."user_id" = "users"."id" 
  and exists (
    select * from "roles" inner join "model_has_roles" 
    on "roles"."id" = "model_has_roles"."role_id" 
    where "users"."id" = "model_has_roles"."model_id" 
    and "model_has_roles"."model_type" = 'App\Models\User' 
    and "roles"."id" in (2)
  ) 
  and "users"."deleted_at" is null
)
```

**Solution**:
- Pre-computed caches for role-based address queries
- Specialized methods for pharmaceutical companies
- Automatic cache clearing when addresses or user roles change

**Usage**:
```php
// Instead of complex queries, use:
$addresses = UserAddressCacheService::getCachedPharmaceuticalCompanyAddresses();
$states = UserAddressCacheService::getCachedPharmaceuticalCompanyStates();
$cities = UserAddressCacheService::getCachedPharmaceuticalCompanyCities();
```

### 4. Form Data Caching (Extended existing)

Enhanced existing `DosageForm` and `Unit` caching with proper invalidation strategies.

## Cache Invalidation Strategy

### Automatic Invalidation

**Model Events**: All cached models use Laravel's model events to automatically clear relevant caches:

- **Role Model**: Clears role caches on save/update/delete
- **UserAddress Model**: Clears address and location caches on changes
- **User Model**: Clears address caches when roles are assigned/removed
- **DosageForm/Unit Models**: Clear form caches on changes

### Manual Cache Clearing

**Console Command**:
```bash
# Clear all caches
php artisan cache:clear-all

# Clear specific cache types
php artisan cache:clear-all --type=roles
php artisan cache:clear-all --type=locations
php artisan cache:clear-all --type=addresses
php artisan cache:clear-all --type=forms
```

**Programmatic Clearing**:
```php
// Clear all caches
Role::clearAllCache();
LocationCacheService::clearAllCache();
UserAddressCacheService::clearAllCache();

// Clear specific caches
LocationCacheService::clearStateCache($stateId);
LocationCacheService::clearCityCache($cityId);
UserAddressCacheService::clearRoleCache('Pharmaceutical Company');
```

## Updated Code Examples

### UserResource.php Updates

**State/City Filter Options**:
```php
// Before: Complex query every time
SelectFilter::make('state')->options(function () {
    return UserAddress::whereHas('user', function ($query) {
        $query->role('Pharmaceutical Company');
    })->with('state')->get()->pluck('state.name', 'state.id')->filter()->unique()->sort()->toArray();
})

// After: Cached query
SelectFilter::make('state')->options(function () {
    return UserAddressCacheService::getCachedPharmaceuticalCompanyStates();
})
```

**Role Assignment**:
```php
// Before: Direct query
$user->assignRole('Pharmaceutical Company');

// After: Cached lookup
$role = Role::getCachedByName('Pharmaceutical Company');
if ($role) {
    $user->assignRole($role);
}
```

**State Options in Forms**:
```php
// Before: Direct query every time
->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())

// After: Cached options
->options(LocationCacheService::getCachedStatesByCountry(132))
```

## Performance Benefits

### Expected Improvements:

1. **Role Queries**: Reduced from multiple DB calls to single cached lookup
2. **Location Queries**: Batch lookups and cached dropdown options
3. **Complex Address Queries**: Pre-computed results instead of expensive joins
4. **Form Data**: Consistent caching across all form-related queries

### Cache Statistics:

- **Cache TTL**: 5 minutes (300 seconds)
- **Cache Keys**: Prefixed and organized by type
- **Memory Usage**: Minimal due to short TTL and selective caching
- **Hit Rate**: Expected 80%+ for frequently accessed data

## Monitoring and Maintenance

### Cache Key Patterns:
- Roles: `role_{name}_{guard}`
- States: `state_{id}`, `states_country_{countryId}`
- Cities: `city_{id}`, `cities_state_{stateId}`
- Addresses: `user_addresses_role_{roleName}`, `states_by_role_{roleName}`

### Debugging:
```php
// Check if cache exists
if (Cache::has('role_Pharmaceutical Company_web')) {
    // Cache hit
}

// Get cache value
$cached = Cache::get('role_Pharmaceutical Company_web');
```

### Production Considerations:

1. **Redis**: Consider using Redis for better cache persistence
2. **Cache Tags**: Implement for more efficient bulk clearing
3. **Monitoring**: Add cache hit/miss metrics
4. **Memory Limits**: Monitor cache memory usage

## Deployment Notes

### Development Environment:
```bash
# Run migrations
php artisan migrate

# Clear caches
php artisan cache:clear-all
```

### Production Environment:
```bash
# Step 1: Run migrations (creates basic indexes)
php artisan migrate

# Step 2: Create concurrent indexes for zero downtime 
php artisan db:create-caching-indexes

# Step 3: Clear all caches
php artisan cache:clear-all

# Step 4: Optimize application
php artisan optimize
```

### Post-Deployment Verification:
```bash
# Verify indexes were created
php artisan db:create-caching-indexes --dry-run

# Check cache functionality  
php artisan cache:clear-all --type=roles

# Monitor query performance
tail -f storage/logs/laravel.log | grep "database"
```

**Important Notes**:
1. **Index creation**: May take 5-15 minutes on large tables
2. **Cache clearing**: Required after any role/location data changes  
3. **Environment variables**: Configure cache drivers appropriately

## Troubleshooting

### Common Issues:

1. **Stale Data**: Clear specific cache type if data appears outdated
2. **Memory Issues**: Reduce cache TTL if memory usage is high
3. **Performance**: Monitor slow query logs to identify uncached queries

## Database Indexing for Performance

### Index Optimization Strategy

In addition to caching, comprehensive database indexing has been implemented to optimize the underlying queries when cache misses occur.

**Migration**: `database/migrations/2025_07_15_100000_add_caching_optimization_indexes.php`

### Key Indexes Added:

#### 1. **Role Lookups** 
```sql
-- Composite index for: select * from "roles" where "name" = 'X' and "guard_name" = 'web'
CREATE INDEX idx_roles_name_guard ON roles (name, guard_name);
```

#### 2. **Location Queries**
```sql
-- State lookups by country
CREATE INDEX idx_states_country_id ON states (country_id);

-- City lookups by state  
CREATE INDEX idx_cities_state_id ON cities (state_id);

-- Dropdown optimization
CREATE INDEX idx_states_country_name ON states (country_id, name);
CREATE INDEX idx_cities_state_name ON cities (state_id, name);
```

#### 3. **Complex User Address Queries**
```sql
-- Critical for role-based address filtering
CREATE INDEX idx_model_roles_lookup ON model_has_roles (model_id, model_type, role_id);
CREATE INDEX idx_user_addresses_user_id ON user_addresses (user_id);
CREATE INDEX idx_user_addresses_state_id ON user_addresses (state_id);
CREATE INDEX idx_user_addresses_city_id ON user_addresses (city_id);
```

#### 4. **PostgreSQL Partial Indexes** (Most Optimized)
```sql
-- Active users only
CREATE INDEX idx_users_active_only 
ON users (id, email, name) 
WHERE is_active = true AND deleted_at IS NULL;

-- App\Models\User model type optimization (general)
CREATE INDEX idx_model_has_roles_app_user 
ON model_has_roles (model_id, role_id) 
WHERE model_type = 'App\Models\User';

-- Pharmaceutical Company role optimization (created dynamically)
-- Note: Created by console command to avoid subquery issues
```

### Zero-Downtime Index Creation

For production environments, use concurrent index creation:

```bash
# Step 1: Run migration (creates indexes normally)
php artisan migrate

# Step 2: Create additional concurrent indexes for zero downtime
php artisan db:create-caching-indexes

# Dry run first to see what will be executed
php artisan db:create-caching-indexes --dry-run
```

**Concurrent Script**: `database/scripts/create_caching_optimization_indexes_concurrent.sql`

### Performance Impact:

**Expected Query Time Reductions**:
- Role lookups: 90%+ reduction (from table scan to index lookup)
- State/City queries: 80%+ reduction (indexed foreign key lookups)  
- Complex address queries: 70%+ reduction (optimized joins)
- Filter operations: 60%+ reduction (indexed WHERE clauses)

### Debug Commands:
```bash
# Clear and rebuild all caches
php artisan cache:clear-all
php artisan optimize:clear
php artisan cache:clear-all

# Create optimized indexes
php artisan db:create-caching-indexes

# Check cache configuration
php artisan config:show cache

# Verify indexes in PostgreSQL
psql -d database_name -c "\di+ idx_roles_name_guard"
``` 