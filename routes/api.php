<?php

use App\Http\Controllers\Api\V1\Clinic\SupportCategoryController;
use App\Http\Controllers\Api\V1\Clinic\AssociatedSupplierController;
use App\Http\Controllers\Api\V1\Clinic\AccountTypeController;
use App\Http\Controllers\Api\V1\Clinic\OnboardingController;
use App\Http\Controllers\Api\V1\Clinic\AuthController;
use App\Http\Controllers\Api\V1\Clinic\BannerController;
use App\Http\Controllers\Api\V1\Clinic\BrandController;
use App\Http\Controllers\Api\V1\Clinic\CartController;
use App\Http\Controllers\Api\V1\Clinic\CategoryController;
use App\Http\Controllers\Api\V1\Clinic\FavouriteController;
use App\Http\Controllers\Api\V1\Clinic\SupportController;
use App\Http\Controllers\Api\V1\Clinic\OrderController;
use App\Http\Controllers\Api\V1\Clinic\ProductController;
use App\Http\Controllers\Api\V1\Clinic\ReferFriendController;
use App\Http\Controllers\Api\V1\Clinic\SocialController;
use App\Http\Controllers\Api\V1\Clinic\SupplierController;
use App\Http\Controllers\Api\V1\Clinic\UserController;
use App\Http\Controllers\Api\V1\Clinic\DpharmaPointController;
use App\Http\Controllers\Api\V1\Clinic\ThreadController;
use App\Http\Controllers\Api\V1\CountryCityController;
use App\Http\Controllers\Api\V1\StaticPageController;
use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Clinic API routes
Route::prefix('v1')->middleware(['api', 'set.locale','set.timezone'])->group(function () {
    Route::get('/test', function (Request $request) {
        return response()->json(['message' => 'API is working!', 'version' => 'v1']);
    });

    Route::get('login', function () {
        return response()->json(['message' => 'Unauthorized'], 401);
    });
Route::post('webhook/payment-handler-1',function(Request $request) {

    DB::table('transaction_webhooks')->insert([
        'meta_data' => json_encode($request->all()), 
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    Log::error('webhook call with this request from route file => ', [
        'request' => $request->all(),
    ]);
});



    Route::get('clinic/account-types' , [AccountTypeController::class, 'index']);
    Route::get('countries' , [CountryCityController::class, 'countries']);
    Route::get('states/{country_id}' , [CountryCityController::class, 'states']);
    Route::get('cities/{state_id}' , [CountryCityController::class, 'cities']);
    Route::get('postal-codes/{city_id}' , [CountryCityController::class, 'getPostalcode']);
    Route::get('landline-code' , [CountryCityController::class, 'getLandlineCode']);
    Route::get('static-page',[StaticPageController::class,'index']);
    Route::get('footer',[StaticPageController::class,'footer']);
    Route::get('suppliers',[SupplierController::class,'index']);
    Route::post('webhook/payment-handler', function (Illuminate\Http\Request $request) {
        // Set CORS header
        header('Access-Control-Allow-Origin: *');

        // Optionally, handle preflight requests
        if ($request->getMethod() === "OPTIONS") {
            header('Access-Control-Allow-Methods: POST, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization');
            exit(0);
        }

        // Call the controller method
        return app(\App\Http\Controllers\Api\V1\Clinic\OrderController::class)->webhook($request);
    });
    Route::post('contact-us/',[UserController::class, "contactUs"]);
    Route::get('shipping/webhook-update-status', [\App\Http\Controllers\Api\V1\Clinic\ShippingWebhookController::class, 'updateStatus']);


    Route::get("product/payment-handler/{transactionId}", [OrderController::class, "paymentHandler"]);


    Route::group(['prefix' => 'oauth'], function () {

        Route::group(['prefix' => 'clinic'], function () {

            Route::post('/send-sign-up-otp', [AuthController::class, 'sendSignupOtp']);
            Route::post('/register', [AuthController::class, 'register']);
            Route::post('/login', [AuthController::class, 'login']);
            Route::post('/send-login-otp', [AuthController::class, 'sendLoginOtp']);
            Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);

            Route::post('/social-login', [SocialController::class, 'socialLogin']);
            // Route::post('/social-apple-login', [SocialController::class, 'socialAppleLogin']);

            Route::post('/re-sent-otp', [AuthController::class, 'reSentOtp']);
            Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
            Route::post('/forgot-password-otp-verify', [AuthController::class, 'forgotPasswordOtpVerify']);
            Route::post('reset-password', [AuthController::class, 'resetPassword'])->name('password.reset');
            Route::post('/terms-condition', [AuthController::class, 'updateTermCondition']);



        });
    });

    Route::middleware(['auth:sanctum'])->group(function () {

        Route::post('/logout', [UserController::class, 'logout']);
        Route::prefix('clinic')->group(function () {

            Route::post('/onboarding-save', [OnboardingController::class, 'store']);
            Route::get('/onboarding-detail', [OnboardingController::class, 'detail']);
            Route::get('/business-types', [AccountTypeController::class, 'businessType']);
            Route::post('/social-additional-details', [UserController::class, 'socialAdditonalInfo']);
            Route::post("shipping-address", [OnboardingController::class, "saveShippingaddress"]);
            Route::post("profile-upload",[OnboardingController::class, 'profileUpload']);

            Route::middleware(['is_admin_verified'])->group(function () {
                Route::get('/detail', [UserController::class, 'details']);
                Route::post('/password-change', [UserController::class, 'changePassword']);
                Route::post('/update-profile', [UserController::class, 'updateProfile']);



                //Onboarding
                Route::get('banners' , [BannerController::class, 'index']);
                Route::get('suppliers',[SupplierController::class,'index']);

                Route::get('categories' , [CategoryController::class, 'index']);
                Route::get('sub-categories/{categoryId}' , [CategoryController::class, 'subCategory']);
                Route::get('brands' , [BrandController::class, 'index']);
                Route::get('foams' , [CategoryController::class, 'foams']);

                Route::get('restricted-suppliers',[AssociatedSupplierController::class,'restrictedSuppliers']);
                Route::get('associated-suppliers',[AssociatedSupplierController::class,'index']);
                Route::post('associated-suppliers',[AssociatedSupplierController::class,'save']);
                Route::delete('associated-suppliers/{id}',[AssociatedSupplierController::class,'delete']);

                Route::get('home',[OnboardingController::class,'getClinicDetail']);
                Route::post('apc-certificate-update',[OnboardingController::class,'apcCertificateUpdate']);


                Route::get('products',[ProductController::class,'list']);
                Route::get('products/filters',[ProductController::class,'filterList']);
                Route::get('product/{id}',[ProductController::class,'detail']);
                Route::get('product/{id}/suppliers',[ProductController::class,'productSupplier']);
                Route::get('global-search',[ProductController::class,'gloabalSearch']);
                Route::get('recent-search',[ProductController::class,'recentSearches']);
                Route::delete('delete/recent-search',[ProductController::class,'clearRecentSearches']);
                Route::get('browser-product-history',[ProductController::class,'browserProductHistory']);
                Route::get('top-products',[ProductController::class,'topProducts']);

                Route::get('favourites',[FavouriteController::class,'index']);
                Route::post('favourite',[FavouriteController::class,'save']);
                Route::post('favourite/remove',[FavouriteController::class,'remove']);

                Route::get("carts", [CartController::class, "index"]);
                Route::get("cart-details", [CartController::class, "detail"]);
                Route::post("carts", [CartController::class, "addCart"]);
                Route::delete("carts/{id}", [CartController::class, "delete"]);
                Route::post("carts/clear", [CartController::class, "clear"]);
                Route::post("update-carts", [CartController::class, "update"]);
                Route::post("cart-payment-detail", [CartController::class, "cartPaymentDetail"]);
                Route::get("cart-review", [CartController::class, "cartReview"]);

                Route::post("product/checkout", [OrderController::class, "productCheckout"]);
                Route::post("product/payment-handler", [OrderController::class, "paymentHandler"]);
                Route::get("orders", [OrderController::class, "index"]);
                Route::get("order/{id}", [OrderController::class, "show"]);
                Route::post("re-order/{id}", [OrderController::class, "productReOrder"]);
                Route::post("order/cancel/{id}", [OrderController::class, "cancelOrder"]);
                Route::post("order/re-payment/{id}", [OrderController::class, "reTryPayment"]);

                Route::get("support-category", [SupportCategoryController::class, "index"]);
                Route::get("help-support-ticket", [SupportController::class, "index"]);
                Route::post("help-support-ticket", [SupportController::class, "store"]);
                Route::get("help-support-ticket/{id}", [SupportController::class, "show"]);
                Route::get("support/orders", [SupportController::class, "orderList"]);
                Route::post("support/message", [SupportController::class, "messageSave"]);
                Route::get("support/message-detail/{ticket_id}", [SupportController::class, "messageDetail"]);
                Route::post("support/message/{ticket_id}", [SupportController::class, "closedTicket"]);


                Route::post("thread-ticket", [ThreadController::class, "store"]);
                Route::get("thread/messages", [ThreadController::class, "messageList"]);
                Route::get("thread/message-detail/{ticket_id}", [ThreadController::class, "messageDetail"]);
                Route::post("thread/message", [ThreadController::class, "messageSave"]);
                Route::get("thread/un-messages", [ThreadController::class, "unReadMessages"]);


                Route::get("shipping-address", [OnboardingController::class, "shippingAddresses"]);
                Route::post("set-default-address/{id}", [OnboardingController::class, "setDefaultAddress"]);

                Route::get('refer-friends',[ReferFriendController::class, "list"]);
                Route::get('dpharma-points',[DpharmaPointController::class, "list"]);
                Route::get('dpharma-point-detail',[DpharmaPointController::class, "pointsDetail"]);
                Route::post('send-refer-code',[ReferFriendController::class, "friendReferral"]);

                Route::post('basic-info/update',[OnboardingController::class, "basicInfoUpdate"]);
                Route::post('address/update',[OnboardingController::class, "addressUpdate"]);
                Route::post('person-in-charge/update',[OnboardingController::class, "personInChargeUpdate"]);
                Route::post('documents/update',[OnboardingController::class, "documentsUpdate"]);

                Route::get('account-detail',[UserController::class, "accountDetail"]);

                Route::post('home/update-terms-condition', [UserController::class, "updateTermCondition"]);
            });
        });
    });
});
