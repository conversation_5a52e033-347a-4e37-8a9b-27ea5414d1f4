<?php

use Illuminate\Http\Request;
use App\Livewire\SetPassword;
use App\Livewire\UserPassword;
use Filament\Facades\Filament;
use App\Livewire\PrivacyPolicy;
use App\Livewire\TermsAndConditions;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Log;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use Filament\Notifications\Notification;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\EcommerceController;
use App\Filament\Pc\Pages\CreatePasswordPage;
use App\Filament\Pc\Pages\ResetPasswordOtpPage;
use App\Http\Controllers\NotificationController;

Route::get('/home', function () {
    return view('welcome');
})->name('pc.home');


Route::get('pc/register-success', function () {
    if (Auth::check()) {
        $user = Auth::user();
        if ($user->is_admin_verified == true && $user->pcDetails->step == 5) {
            return redirect()->to(Filament::getCurrentPanel()->getHomeUrl());
        } else {
            return view('custom-view.pc-register.register-success');
        }
    }
})->name('pc.register-success');

Route::get('pc/approval-success', function () {
    if (Auth::check()) {
        return view('custom-view.pc-register.approval-success');
    }
})->name('pc.approval-success');

Route::get('pc/reject-success', function () {
    if (Auth::check()) {
        return view('custom-view.pc-register.reject-success');
    }
})->name('pc.reject-success');


Route::post('pc/logout/', function (Request $request) {
        $user = Auth::user();
        $userId = Auth::id();
        if ($user) {
            activity()
                ->performedOn($user)
                ->causedBy($user)
                ->withProperties(['attributes' => [
                    'User ID' => $user->id,
                    'User Name' => $user->name,
                    'User Email' => $user->email,
                    'Logout Time' => now()->format('Y-m-d H:i:s'),
                    'IP Address' => $request->ip(),
                    'User Agent' => $request->userAgent(),
                    'Session ID' => $request->session()->getId(),
                ]])
                ->log("PS user '{$user->name}' logged out successfully");
        }
    Auth::logout();
    Session::flush();
    $loginUrl = Filament::getPanel('pc')->getLoginUrl(); // Retrieve the login URL

    return redirect()->to($loginUrl);
})->name('filament.pc.auth.logout.test')->middleware('web');

Route::get('pc/first-login-complete', function (Request $request) {
    $user = Auth::user();
    if ($user && $user->pcDetails) {
        $user->pcDetails->update(['is_first_login' => false]);
    }
    // Update timezone if provided
    if ($request->has('timezone') && $user) {
        $timezone = $request->get('timezone', 'Asia/Kolkata');

        // Validate timezone
        // if (in_array($timezone, \DateTimeZone::listIdentifiers())) {
            // Update user with timezone and IP
            $user->update([
                'timezone' => $timezone,
            ]);

            Log::info('PC First Login Complete - Timezone Updated', [
                'user_id' => $user->id,
                'timezone' => $timezone,
            ]);
        // }
    }

    return redirect()->route('filament.pc.pages.dashboard');
})->name('pc.first-login-complete');



Route::get('/optimize', function () {
    Artisan::call('optimize:clear');
});

// Route::get('pending-approval-products', PendingApprovalProducts::class)->middleware('auth')->name('pending-approval-products');
Route::get('/download-invoice/{id}', [InvoiceController::class, 'download'])->name('download.invoice');

Route::get('passwor-reset/otp/{id}', ResetPasswordOtpPage::class)->name('pc.passwor-reset.otp');
Route::get('create-password/{id}', CreatePasswordPage::class)->name('pc.create-password');
Route::get('/activate-account/{id}', UserPassword::class)->name('activate');
Route::get('supplier-payment-handler', [EcommerceController::class, 'supplierPaymentHandler']);
Route::get('onboard-seller', [StripeController::class, 'onboard']);
Route::get('onboard-result/{token}', [StripeController::class, 'onBoardResult'])->name('stripe.onboard-result');
Route::get('/privacy-policy', PrivacyPolicy::class)->name('pc.privacy.policy');
Route::get('/term-and-conditions', TermsAndConditions::class)->name('pc.term-and-conditions');
Route::get('/download/sample/{filename}', function ($filename) {
    $path = storage_path("download/sample/{$filename}");

    if (!File::exists($path)) {
        return response()->json([
            'message' => 'File not found',
            'checked_path' => $path,
            'full_path' => $path,
        ], 404);
    }

    return response()->download($path);
})->name('sample.download');

Route::get('/download/{filePath}', [StripeController::class, 'downloadFile'])
    ->name('download.file');

Route::get('/set-password/{id}', SetPassword::class)->name('set-password');

Route::get('/notifications/redirect/{notification}', [NotificationController::class, 'redirect'])->name('notifications.redirect');

Route::get('/audit-download/{file}', [App\Filament\Admin\Resources\ImportResource\Pages\CreateImport::class, 'download'])->name('audit.download');

Route::post('/users/{id}/toggle-status', [StripeController::class, 'toggle'])->name('users.toggle-status');

Route::get('/pc/user-manual-download', function () {
    if (File::exists(public_path('pdf/sample.pdf'))) {
        return response()->download(public_path('pdf/sample.pdf'));
    } else {
        Notification::make()
            ->title('File not found')
            ->body('The requested file is unavailable or could not be located.')
            ->danger()
            ->send();

        return redirect()->back();
    }

});

Route::get('/login', function () {
    $prevUrl = url()->previous();
    $panels = Filament::getPanels();

    // Default panel fallback
    $defaultPanel = array_key_first($panels);
    $panelId = $defaultPanel;

    if ($prevUrl) {
        $path = parse_url($prevUrl, PHP_URL_PATH);
        $pathSegments = explode('/', trim($path, '/'));
        $firstSegment = $pathSegments[0] ?? '';

        // Check if the first segment is a valid panel
        if (array_key_exists($firstSegment, $panels)) {
            $panelId = $firstSegment;
        }
    }

    // Ensure we have a valid panel
    if (! array_key_exists($panelId, $panels)) {
        $panelId = $defaultPanel;
    }

    $guard = $panels[$panelId]->getAuthGuard();

    if (Auth::guard($guard)->check()) {
        // User is authenticated for this panel, redirect to dashboard
        $dashboardRoute = "filament.{$panelId}.pages.dashboard";
        if (Route::has($dashboardRoute)) {
            return redirect()->route($dashboardRoute);
        }
        // Fallback to panel URL if route doesn't exist
        return redirect()->to("/{$panelId}");
    }

    // Not authenticated, redirect to the correct panel login page
    $loginRoute = "filament.{$panelId}.auth.login";
    if (Route::has($loginRoute)) {
        return redirect()->route($loginRoute);
    }

    // Final fallback - redirect to panel URL
    return redirect()->to("/{$panelId}/login");
})->name('login');

Route::get('/refresh-csrf', function () {
    return response()->json(['token' => csrf_token()]);
})->name('refresh-csrf');


Route::get('/check-payout-payment-status', [EcommerceController::class, 'checkPayoutPaymentStatus'])
    ->name('payment-status');




