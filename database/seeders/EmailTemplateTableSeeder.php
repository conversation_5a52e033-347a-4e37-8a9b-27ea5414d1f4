<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EmailTemplateTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('email_templates')->truncate();
        DB::table('email_templates')->insert([
            0 => [
                'id' => 1,
                'key' => 'REGISTER_USER_EMAIL_VERIFICATION_LINK',
                'title' => 'Register User Email Verification Link',
                'subject' => 'Digital Pharma - Register User Email Verification Link',
                'email_content' => '<p>Hello NAME,</p>

                        <p>To verify your email address, follow this link (or paste into your browser)</p>
                        <p><a href="URL">URL</a></p>
                        <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            1 => [
                'id' => 2,
                'key' => 'USER_FORGOT_PASSWORD',
                'title' => 'Reset Password Request',
                'subject' => 'Digital Pharma - Reset Password Request',
                'email_content' => '<p>Hello NAME,</p>

                        <p>You are receiving this email because we received a password reset request for your account.</p>
                        <p><a href="URL" style="color: #fff;background-color: #03a9f3;padding:8px 25px;font-size:15px;font-weigth:700;display: inline-block; border: 1px solid #03a9f3;text-transform: uppercase;text-decoration: none; border-radius: 4px;">Reset Password</a></p>
                        <p>If you did not request a password reset, no further action is required.</p>
                        <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            2 => [
                'id' => 3,
                'key' => 'ADMIN_FORGOT_PASSWORD',
                'title' => 'Admin Reset Password Request',
                'subject' => 'Digital Pharma - Admin Reset Password Request',
                'email_content' => '<p>Hello NAME,</p>

                    <p>You are receiving this email because we received a password reset request for your account.</p>

                    <p><a href="URL" style="color: #fff;background-color: #03a9f3;padding:8px 25px;font-size:15px;font-weigth:700;display: inline-block; border: 1px solid #03a9f3;text-transform: uppercase;text-decoration: none; border-radius: 4px;">Reset Password</a></p>
                    <p>
                    If you did not request a password reset, no further action is required.</p>
                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            3 => [
                'id' => 4,
                'key' => 'NEWSLETTER_NOTIFICATION',
                'title' => 'Newsletter Notification',
                'subject' => 'Digital Pharma - Newsletter Notification',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>BODY</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            4 => [
                'id' => 5,
                'key' => 'FACILITY_REGISTER_OTP_SEND',
                'title' => 'Facility Registration otp Send',
                'subject' => 'Digital Pharma - Registration otp',
                'email_content' => '<p>Hello Facility,</p>
                                <p>Your otp for registration is: <strong>OTP</strong> .</p>
                                <p> This code will expire in TIME minutes. Please use it to complete your registration process.</p>
                                <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            5 => [
                'id' => 6,
                'key' => 'FACILITY_OTP_RE_SEND',
                'title' => 'Facility otp re-Send',
                'subject' => 'Digital Pharma - Re-send otp',
                'email_content' => '<p>Hello Facility,</p>
                                <p>Your re-sent otp is: <strong>OTP</strong> .</p>
                                <p> This code will expire in TIME minutes. Please use it to complete your registration process.</p>
                                <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            6 => [
                'id' => 7,
                'key' => 'FACILITY_LOGIN_OTP_SEND',
                'title' => 'Facility login otp Send',
                'subject' => 'Digital Pharma - Login otp',
                'email_content' => '<p>Hello Facility,</p>
                                    <p>Your otp for login is: <strong>OTP</strong> .</p>
                                    <p> This code will expire in TIME minutes. Please use it to complete your login process.</p>
                                    <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            7 => [
                'id' => 8,
                'key' => 'FACILITY_FORGOT_PASSWORD_OTP_SEND',
                'title' => 'Facility forgot password otp Send',
                'subject' => 'Digital Pharma - Forgot password otp',
                'email_content' => '<p>Hello Facility,</p>
                                    <p>Your otp for forgot password is: <strong>OTP</strong> .</p>
                                    <p> This code will expire in TIME minutes. Please use it to complete your forgot password process.</p>
                                    <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            8 => [
                'id' => 9,
                'key' => 'FACILITY_WELCOME',
                'title' => 'Facility Welcome mail',
                'subject' => 'Digital Pharma - Welcome to Our Platform',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>Welcome to Digital Pharma — we’re thrilled to have your facility join our growing community!</p>
                                    <p>Your registration is now complete, and you’re one step closer to connecting with healthcare providers and reaching new customers.</p>
                                    <p>We’re committed to supporting you every step of the way as you grow your business with us.</p>
                                    <p> Visit Your Dashboard : <a href="URL" style="color: #0d6efd; text-decoration: underline;">Click here to log in</a></p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            9 => [
                'id' => 10,
                'key' => 'REJECT_PC_USER',
                'title' => 'Reject PC User mail',
                'subject' => 'Digital Pharma - Rejected you profile',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We regret to inform you that your profile registration on Digital Pharma has been reviewed and rejected by our administration team for the following reason :</p>
                                    <p>REASON</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            10 => [
                'id' => 11,
                'key' => 'VERSION_APPROVAL',
                'title' => 'Profile Update Pending Approval',
                'subject' => 'Digital Pharma - Profile Approval Pending',
                'email_content' => '<p>Dear Admin,</p> <br />
                                    <p> We would like to inform you that a PC with below details has submitted updates to their profile information</p>
                                    <p><strong>Supplier Name:</strong> NAME</p>
                                    <p><strong>Supplier Email:</strong> EMAIL</p>

                                    <p> These changes are now pending your review and approval</p
                                    <p> Please log in to the admin panel to verify and approve the submitted updates at your earliest convenience.</p><br />
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            11 => [
                'id' => 12,
                'key' => 'CHANGES_APPROVAL',
                'title' => 'Changes Approved mail',
                'subject' => 'Digital Pharma - Changes Approved',
                'email_content' => '<p>Dear NAME,</p> <br />
                                    <p> Your changes have been approved by admin</p><br />
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            12 => [
                'id' => 13,
                'key' => 'CHANGES_REJECT',
                'title' => 'Changes Rejected mail',
                'subject' => 'Digital Pharma - Changes Rejected',
                'email_content' => '<p>Dear NAME,</p>
                                    <p> Your changes have been Rejected by admin, please make changes again!</p>>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            13 => [
                'id' => 14,
                'key' => 'FACILITY_ASSOCIATE_SUPPLIER',
                'title' => 'Facility Associate with supplier',
                'subject' => 'Digital Pharma - A new facility wants to associate with you',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>This is an open association request. If you wish to approve, please log in to the D Pharma Portal and review the request.</p>
                                    <p>Once approved, this facility will be able to purchase your products.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            14 => [
                'id' => 15,
                'key' => 'FACILITY_ONBOARDING',
                'title' => 'Facility onboarding review mail to super admin',
                'subject' => 'Digital Pharma - New Facility Onboarding Review Request',
                'email_content' => '<p>Hello DPharma,</p>
                                    <p>A new facility has submitted their profile for review and approval. Please log in to the admin portal to verify the details and take appropriate action.</p>
                                    <p>Name : NAME</p>
                                    <p>Email : EMAIL</p>
                                    <p> If everything is in order, you can approve the profile, allowing the facility to start offering their services on our platform.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            15 => [
                'id' => 16,
                'key' => 'FACILITY_REFERRAL',
                'title' => 'Facility congratulations mail',
                'subject' => 'Digital Pharma - Congratulations! Your Digital Pharmaa Referral Code Was Used- Get Ready for Rewards🎉',
                'email_content' => '<p>Hello, FACILITYNAME</p>
                                    <p>We are pleased to inform you that REGISTERFACILITYNAME has successfully used your referral code while signing up with Digital Pharma.</p>
                                    <p>As part of our referral program, once they achieve an order limit of ' . config('constants.api.referral_code.order_value') . ', you will earn ' . config('constants.api.referral_code.point') . ' points, which will be credited to your Digital Pharma account.</p><br />
                                    <p>Thank you for being a valued member of our community. If you have any questions, feel free to contact us.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            16 => [
                'id' => 17,
                'key' => 'FACILITY_ORDER_CANCEL_CONFIRMATION',
                'title' => 'Facility Order Cancel Confirmation',
                'subject' => 'Digital Pharma - Order Cancellation Confirmation',
                'email_content' => '<p>Dear, NAME</p>
                                    <p> We have received your request to cancel Order ID:  ORDERID placed on CREATEDDATE .</p>
                                    <p>We confirm that your order has been successfully cancelled. If any refund is applicable, it will be processed as per our refund policy, and you will be notified accordingly..</p>
                                    <p>If you have any questions or need further assistance, please feel free to contact our support team..</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            17 => [
                'id' => 18,
                'key' => 'ORDER_CANCEL_PC_MAIL',
                'title' => 'Order Cancel inform mail to pc',
                'subject' => 'Digital Pharma - Order Cancellation',
                'email_content' => '<p>Dear, NAME</p>
                                    <p>  We are writing to inform you that the Order ID:  ORDERID placed on CREATEDDATE has been cancelled.</p>
                                    <p>If you have any questions or need further assistance, please feel free to contact our support team.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            18 => [
                'id' => 19,
                'key' => 'FACILITY_REFERRAL_CODE',
                'title' => 'Facility send referral code to another facility',
                'subject' => 'Digital Pharma - 🎉 Congratulations! You Have Received a New Referral Code',
                'email_content' => '<p>Hello</p>
                                    <p> Great news — you’ve just received a brand-new referral code from FACILITYNAME</p>
                                    <p>When You will sign up using this referral code, they’ll receive a special reward, and you’ll earn exclusive benefits too</p>
                                    <p>Your Referral Code: REFERRALCODE</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            19 => [
                'id' => 20,
                'key' => 'PC_COMMISSION_RECEIVED_MAIL',
                'title' => 'Commission Payment Received mail send to pc',
                'subject' => 'Digital Pharma - Commission Payment Received',
                'email_content' => '<p>Hello NAME</p>
                                    <p>  We’re happy to inform you that we’ve received your <strong>commission payment</strong>. Thank you for your continued support and partnership with Digital Pharam.</p>
                                    <p> Please find your invoice attached with this email for your reference.</p>
                                    <p> If you have any questions or need assistance, feel free to contact our support team.</p>
                                    <P>Thank you for choosing Digital Pharma!</P>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            20 => [
                'id' => 21,
                'key' => 'FACILITY_PAY_NOW_ORDER_CONFIRMATION',
                'title' => 'Facility pay now order payment confirmation',
                'subject' => 'Digital Pharma - Payment Confirmation',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We are pleased to inform you that your order has been successfully placed and payment has been successfully processed</p>
                                    <p> Order Details:</p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <strong>Total Amount: RM </strong> AMOUNT<br>
                                    <strong>Payment Status:</strong>PAYMENTSTATUS<br>
                                    </p>
                                    <p> TRANSACTIONSTATUS.</p>
                                    <p> If you have any questions or need assistance, feel free to contact our support team.</p>
                                    <P>Thank you for choosing Digital Pharma!</P>
                                ',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            21 => [
                'id' => 22,
                'key' => 'FACILITY_CREDIT_LINE_ORDER_CONFIRMATION',
                'title' => 'Facility credit line order confirmation',
                'subject' => 'Digital Pharma - Order Confirmation',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We are delighted to inform you that your order has been successfully processed. According to our records, payment is now due, as it has been charged to your credit line</p>
                                    <p> Order Details:</p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <strong>Total Amount:</strong> RM AMOUNT</p>
                                    <p>Your order is now being processed, and you will receive a notification once it has been dispatched..</p>
                                    <p> If you have any questions or need assistance, feel free to contact our support team.</p>
                                    <P>Thank you for choosing Digital Pharma!</P>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            22 => [
                'id' => 23,
                'key' => 'SUPPORTTICKET_CREATE',
                'title' => 'SupportTicket Create Mail',
                'subject' => 'Digital Pharma - Support-Ticket Created',
                'email_content' => '<p>Hello USER_NAME,</p>
                                    <p>A new support ticket has been created with the following details:</p>
                                    <p><strong>Ticket ID:</strong> TICKET_ID</p>
                                    <p><strong>Order ID:</strong> ORDER_ID</p>
                                    <p><strong>Name:</strong> NAME</p>
                                    <p><strong>Email:</strong> EMAIL</p>
                                    <p><strong>Category:</strong> CATEGORY</p>
                                    <p><strong>Subject:</strong> SUBJECT</p>
                                    <p><strong>Description:</strong> DESCRIPTION</p>
                                    <p><strong>Status:</strong> STATUS</p>
                                    <p>Please log in to the admin portal to review the ticket and take appropriate action.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            23 => [
                'id' => 24,
                'key' => 'SUPPORTTICKET_CLOSE',
                'title' => 'SupportTicket Close Mail',
                'subject' => 'Digital Pharma - Support-Ticket Closed',
                'email_content' => '<p>Hello USER_NAME,</p>
                                    <p>The following support ticket has been closed:</p>
                                    <p><strong>Ticket ID:</strong> TICKET_ID</p>
                                    <p><strong>Order ID:</strong> ORDER_ID</p>
                                    <p><strong>Name:</strong> NAME</p>
                                    <p><strong>Email:</strong> EMAIL</p>
                                    <p><strong>Category:</strong> CATEGORY</p>
                                    <p><strong>Subject:</strong> SUBJECT</p>
                                    <p><strong>Description:</strong> DESCRIPTION</p>
                                    <p><strong>Status:</strong> STATUS</p></br>
                                    <p>This ticket is now closed, and no additional actions are permitted.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin, Facility'
            ],
            24 => [
                'id' => 25,
                'key' => 'PC_NEW_ORDER_MAIL',
                'title' => 'Pharmaceutical Supplier Order mail',
                'subject' => 'Digital Pharma - A new Order received',
                'email_content' => '<p>Hello NAME, </p>
                                    <p>We’re excited to let you know that you’ve received a new order!</p>
                                    <p><strong>Order Details:</strong></p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <p> If you have any questions or need assistance, feel free to contact our support team.</p>
                                    <P>Thank you for choosing Digital Pharma!</P>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            25 => [
                'id' => 26,
                'key' => 'PC_REJECTED_ORDER',
                'title' => 'Pharmaceutical Supplier rejected order mail',
                'subject' => 'Digital Pharma - Order has been Rejected',
                'email_content' => '<p>Hello USERNAME,&nbsp;</p>
                                <p>This is to inform you that your order (Order No: ORDERNUMBER) placed on ORDERDATE has been rejected for the following reason(s). Please find the order details below. If you need further help, our support team is ready to assist you.</p>
                                <p>Reason: REASON</p>
                                <p><strong>Order Details:</strong>ORDERDETAIL</p>
                                <p>Kindly review the order and submit a revised request if still required. If you need assistance or clarification, feel free to reach out.</p>
                                <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            26 => [
                'id' => 27,
                'key' => 'PC_REGISTRATION_MAIL',
                'title' => 'Pharmaceutical Supplier Onboarding mail',
                'subject' => 'Digital Pharma - New Pharmaceutical Supplier Onboarding',
                'email_content' => '<p>Hello Admin,</p> <br />
                                    <p>A new Pharmaceutical Supplier registration requires your review:</p>
                                    <p><strong>Supplier Name:</strong> NAME</p>
                                    <p><strong>Supplier Email:</strong> EMAIL</p>
                                    <p><strong>Click Here :</strong> LINK</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            27 => [
                'id' => 28,
                'key' => 'PC_APPROVED_ORDER',
                'title' => 'Pharmaceutical Supplier approved order mail',
                'subject' => 'Digital Pharma - Order has been Accepted',
                'email_content' => '<p>Hello NAME,&nbsp;</p>
                                    <p>This is to inform you that the order product placed on ORDERDATE has been accepted</p>
                                    <p><strong>Order Details:</strong>ORDERDETAILS</p>
                                    <p>Kindly review the order and submit a revised request if still required. If you need assistance or clarification, feel free to reach out.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
           28 => [
                'id' => 29,
                'key' => 'PC_CREDITLINE_ORDER_PAID',
                'title' => 'Pharmaceutical Supplier order credit paid mail',
                'subject' => 'Digital Pharma - Order credit amount has been Paid',
                'email_content' => '<p>Hello NAME, </p>
                                    <p>This is to inform you that the order credit amount has been paid</p>
                                    <p><strong>Order Details:</strong></p>
                                    <p> <strong>Order Number:</strong> ORDERNUMBER</br>
                                     <strong>Supplier Name:</strong> SUPPLIER</br>
                                     <strong>Order Date:</strong> ORDERDATE</p>
                                    <p>Kindly review the order and submit a revised request if still required. If you need assistance or clarification, feel free to reach out.</p><br />
                                    <P>Thank you for choosing Digital Pharma!</P>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            29 => [
                'id' => 30,
                'key' => 'TWO_FA_CODE_LOGIN',
                'title' => '2FA Code Login',
                'subject' => 'Digital Pharma - 2FA Code Login',
                'email_content' => '<p>Hello {{ name }},</p>
                                    <p>Your 2FA code is: {{ code }} .</p>
                                    <p> This code will expire in 1 minute. Please use it to complete your login process.</p>
                                    <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
               'email_to' => 'Admin, Pharmaceutical Supplier'
            ],
            30 => [
                'id' => 31,
                'key' => 'PRODUCT_REJECTED',
                'title' => 'Product Rejected',
                'subject' => 'Digital Pharma - Product Rejected',
               'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>Your product has been rejected by admin, following are the details.</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p><strong>Reason:</strong> {{ reason }}</p>
                                    <p> Please make the changes and resubmit again.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>
                                <br />',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            31 => [
                'id' => 32,
                'key' => 'PS_RESET_PASSWORD',
                'title' => 'Reset Password',
                'subject' => 'Digital Pharma - Reset Password',
                'email_content' => '<p>Hello {{ name }},</p>
                                    <p>Your 2FA code is: {{ code }} .</p>
                                    <p> This code will expire in 1 minute. Please use it to complete your rest password process.</p>
                                    <p> If you didn’t request this code, please ignore this email or contact support immediately.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            32 => [
                'id' => 33,
                'key' => 'PS_PAYOUT_SEND',
                'title' => 'PS Payout Send',
                'subject' => 'Digital Pharma - Your payout has been processed',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We are pleased to inform you that your payout has been successfully processed.</p>
                                    <p><strong>Payout Details:</strong></p>
                                    <p><strong>Cycle Date:</strong> : CYCLEDATE</p>
                                    <p><strong>Total Payout:</strong> : PAYABLEAMOUNT</p>

                                    <p>Your payout has been successfully processed on PAYOUTDATE.</p>

                                    <p>PAYOUTTYPETEXT</p>
                                   <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            33 => [
                'id' => 34,
                'key' => 'PS_PAYOUT_COMMISSION',
                'title' => 'PS Payout Commission',
                'subject' => 'Digital Pharma - Payout Commission Report (CYCLEDATE)',
                'email_content' => '<p>Hello NAME,</p>
                                   <p>Your payout commission report for the cycle <strong>CYCLEDATE</strong> has been generated and is attached to this email.</p>
                                    <p>As the TYPE payout for this cycle has been completed, the corresponding admin fee is now due.</p>
                                    <p><strong>Admin fee:</strong> : ADMINFEE</p>
                                    <p>MERCHANTPAYMENTLINK</p>
                                    <p>Your payout commission report for the cycle <strong>CYCLEDATE</strong> has been generated and is attached to this email.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            34 => [
                'id' => 35,
                'key' => 'FACILITY_PROFILE_APPROVAL',
                'title' => 'Facility Approval Profile Submission',
                'subject' => 'Digital Pharma - Facility Approval Profile Submission',
                'email_content' => '<p>Hello Super Admin,</p>
                                    <p> A facility has submitted their profile for approval. Kindly review and take appropriate action regarding their profile.</p>
                                    <p>Facility Detail : </p>
                                    <p>Name : NAME</p>
                                    <p>Email : EMAIL</p>
                                    <p>Click here to review facility profile :  <a href="URL" style="display: inline-block; padding: 10px 20px; background-color: #007BFF; color: white; text-decoration: none; border-radius: 5px;">Review Profile</a> </p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            35 => [
                'id' => 36,
                'key' => 'FACILITY_PROFILE_APPROVAL_SELF',
                'title' => 'Profile Approval Submission',
                'subject' => 'Digital Pharma - Profile Approval Submission',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>Thank you for submitting your profile on dPharma.</p>
                                    <p>We’ve received the information for the <strong> TAB </strong> section, and your profile will update accordingly.</p>
                                    <p>Your profile is now under review. Once it is approved by our Dpharma team, it will be visible on the DPharma portal.</p>
                                    <p>If you have any questions or need further assistance, feel free to reach out to our support team.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            36 => [
                'id' => 37,
                'key' => 'PRODUCT_EXISTING_CREATED',
                'title' => 'Product created from existing product',
                'subject' => 'Digital Pharma - Product created from existing product',
                'email_content' => '<p>Dear Super Admin</p>
                                    <p>Product has been successfully created and is now pending review:</p>
                                    <p>Product has beem created by {{ user_name }}.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            37 => [
                'id' => 38,
                'key' => 'PRODUCT_MASTER_CREATED',
                'title' => 'Master Product Created',
                'subject' => 'Digital Pharma - Master Product Created',
                'email_content' => '<p>Dear Super Admin,</p>
                                    <p>Master Product has been successfully created and is now pending review:</p>
                                    <p>Product has beem created by {{ user_name }}.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            38 => [
                'id' => 39,
                'key' => 'ADDRESS_APPROVAL',
                'title' => 'Shipping Address Approved mail',
                'subject' => 'Digital Pharma - Shipping Address Approved',
                'email_content' => '<p>Dear NAME,</p>
                                    <p> Your All Requested Shipping Address changes has been approved by admin</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            39 => [
                'id' => 40,
                'key' => 'ADDRESS_REJECTED',
                'title' => 'Shipping Address Rejected mail',
                'subject' => 'Digital Pharma - Shipping Address Rejected',
                'email_content' => '<p>Dear NAME,</p>
                                    <p> Your All Requested Shipping Address changes has been rejected by admin</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            41 => [
                'id' => 42,
                'key' => 'MULTIPLE_ADDRESS_APPROVAL',
                'title' => 'Shipping Address Approved mail',
                'subject' => 'Digital Pharma - Shipping Address Approved',
                'email_content' => '<p>Dear NAME,</p>
                                    <p> Your Shipping Address changes for the nickname (NICK) has been approved by admin</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            42 => [
                'id' => 43,
                'key' => 'MULTIPLE_ADDRESS_REJECT',
                'title' => 'Shipping Address Rejected mail',
                'subject' => 'Digital Pharma - Shipping Address Rejected',
                'email_content' => '<p>Dear NAME,</p>
                                    <p> Your Shipping Address changes for the nickname (NICK) has been rejected by admin</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            43 => [
                'id' => 44,
                'key' => 'PRODUCT_RESUBMITTED',
                'title' => 'Product Resubmitted',
                'subject' => 'Digital Pharma - Product Resubmitted',
                'email_content' => '<p>Dear Admin,</p>
                                    <p>{{ user }} has submitted following product for approval:</p>
                                    <p><strong>{{ product_name }}</strong><p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            44 => [
                'id' => 45,
                'key' => 'FACILITY_ORDER_PAYMENT_FAIL',
                'title' => 'Facility pay now order payment failure',
                'subject' => 'Digital Pharma - Payment Failure',
                'email_content' => '<p>Hello NAME</p>
                                    We regret to inform you that there was an issue processing your payment for the order.</p>
                                    <p> Order Details:</p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <strong>Total Amount: RM </strong> AMOUNT<br>
                                    <strong>Payment Status:</strong> PAYMENTSTATUS<br>
                                    <strong>Fail Reason:</strong> REASON<br>
                                    </p>
                                    <p>Please check your payment details and try again. If the issue persists or if you have any questions, feel free to contact our support team.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => ''
            ],
            45 => [
                'id' => 46,
                'key' => 'PC_USER_MAIL',
                'title' => 'PC Onboarding Submission Confirmation',
                'subject' => 'Digital Pharma – Your Profile Is Under Review',
                'email_content' => '<p>Hello USER,</p>
                                    <p>Thank you for submitting your profile to Digital Pharma.</p>
                                    <p>We are pleased to inform you that your profile has been received and is currently under review by our team.</p>
                                    <p>Once your profile is approved, you will gain full access to the Digital Pharma platform.</p>
                                    <p>You can access your account using the link below after approval:</p>
                                    <p><a href="LOGINURL" style="color: #0d6efd; text-decoration: underline;">Click here to log in</a></p>
                                    <p>You will receive a confirmation email once the approval process is complete.</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            46 => [
                'id' => 47,
                'key' => 'PC_PROFILE_APPROVE',
                'title' => 'Pharmaceutical Supplier Onboarding approved',
                'subject' => 'Digital Pharma – Your Profile Approved',
                'email_content' => '<p>Hello USER,</p> <br />
                                    <p>We are pleased to inform you that your profile has been successfully approved.</p>
                                    <p>You can now log in to your account and access all available features on the Digital Pharma platform.</p>
                                    <p>Click <a href="LOGINURL" style="color: #0d6efd; text-decoration: underline;">here</a> to get started and access your account.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            47 => [
                'id' => 48,
                'key' => 'PC_ORDER_INTRANSIT',
                'title' => 'Order Status Update – Now In Transit',
                'subject' => 'Digital Pharma - Order Status In Transit',
                'email_content' => '<p>Hello USER,</p><br />
                                    <p>We are pleased to inform you that your order number <strong>ORDERNUMBER</strong> has been updated and is now <strong>In Transit</strong>.</p>
                                    <p>You can track the status of your order and view shipping details by logging into your Digital Pharma account.</p>
                                    <p><strong>Order Details:</strong> ORDERDETAIL</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            48 => [
                'id' => 49,
                'key' => 'ADMIN_ORDER_INTRANSIT',
                'title' => 'Admin Order Status Update – Now In Transit',
                'subject' => 'Digital Pharma – Order Status In Transit',
                'email_content' => '<p>Hello USER,</p>
                                    <p>The order with order number <strong>ORDERNUMBER</strong> has been updated and is now marked as <strong>In Transit</strong>.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            50 => [
                'id' => 51,
                'key' => 'PC_ORDER_READY_FOR_PICKUP',
                'title' => 'Order Status Update – Now Ready For Pickup',
                'subject' => 'Digital Pharma – Order Status Ready For Pickup',
                'email_content' => '<p>Hello USER,</p>
                    <p>The order with order number <strong>ORDERNUMBER</strong> has been updated and is now marked as <strong>ready for pickup</strong>.</p>
                    <p>You can track the status of your order and view shipping details by logging into your Digital Pharma account.</p>
                    <p><strong>Order Details:</strong>ORDERDETAIL</p>
                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            51 => [
                'id' => 52,
                'key' => 'ADMIN_ORDER_READY_FOR_PICKUP',
                'title' => 'Admin Order Status Update – Now Ready For Pickup',
                'subject' => 'Digital Pharma – Order Status Ready For Pickup',
                'email_content' => '<p>Hello USER,</p><p>The order with order number <strong>ORDERNUMBER</strong> has been updated and is now marked as <strong>ready for pickup</strong>.</p>
                <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            //Delivered
            52 => [
                'id' => 53,
                'key' => 'PC_ORDER_DELIVERED',
                'title' => 'Order Status Update – Delivered Successfully',
                'subject' => 'Digital Pharma – Order Delivered',
                'email_content' => '<p>Hello USER,</p>
                <p>The order with order number <strong>ORDERNUMBER</strong> has been successfully <strong>delivered</strong>.</p>
                <p>You can view the delivery details and order history by logging into your Digital Pharma account.</p>
                <p><strong>Order Details:</strong> ORDERDETAIL</p>
                <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            53 => [
                'id' => 54,
                'key' => 'ADMIN_ORDER_DELIVERED',
                'title' => 'Admin Order Status Update – Delivered Successfully',
                'subject' => 'Digital Pharma – Order Delivered',
                'email_content' => '<p>Hello USER,</p><p>The order with order number <strong>ORDERNUMBER</strong> has been successfully <strong>delivered</strong>.</p><p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],

            54 => [
                'id' => 55,
                'key' => 'ADMIN_REJECTED_ORDER',
                'title' => 'Admin rejected order mail',
                'subject' => 'Digital Pharma - Order has been Rejected',
                'email_content' => '<p>Hello USERNAME,&nbsp;<br>This is to inform you that order (Order No: ORDERNUMBER), placed on ORDERDATE, has been rejected for the following reason(s). Please find the order details below.</p><p>Cancellation Reason : REASON</p>
                <p><strong>Order Details : </strong>ORDERDETAIL</p><p>Thank you for choosing Digital Pharma!&nbsp;</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            55 => [
                'id' => 56,
                'key' => 'CLINIC_PROFILE_APPROVE',
                'title' => 'Clinic Onboarding Approved',
                'subject' => 'Digital Pharma – Your Profile Approved',
                'email_content' => '<p>Hello USER,</p>
                                    <p>We are pleased to inform you that your profile has been successfully approved.</p>
                                    <p>You can now log in to your account and access all available features on the Digital Pharma platform.</p>
                                    <p>Click <a href="LOGINURL" style="color: #0d6efd; text-decoration: underline;">here</a> to get started and access your account.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            56 => [
                'id' => 57,
                'key' => 'CLINIC_PROFILE_REJECT',
                'title' => 'Clinic Onboarding Rejected',
                'subject' => 'Digital Pharma - Your Profile Rejected',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We regret to inform you that your profile registration on Digital Pharma has been reviewed and rejected by our administration team for the following reason :</p>
                                    <p>REASON</p>
                                    <p> Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            57 => [
                'id' => 58,
                'key' => 'ADMIN_USER_PASSWORD',
                'title' => 'User Credential',
                'subject' => 'Digital Pharma has been created your account',
                'email_content' => '<p>Hello USER_NAME,</p>
                                    <p>Login URL: <a href="LOGINURL" style="color:rgb(5, 54, 128); text-decoration: underline;">Click here</a></p>
                                    <p>Password: PASSWORD</p>
                                    <p>Email: EMAIL</p>
                                    <p>Please keep these credentials safe and secure.</p>
                                      <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            58 => [
                'id' => 59,
                'key' => 'ADMIN_CREATED_NEW_PRODUCT_FOR_PC',
                'title' => 'Admin Created Product',
                'subject' => 'Digital Pharma - Admin Created A Product For You.',
                'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>Admin has created a new product for you.</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            59 => [
                'id' => 60,
                'key' => 'PC_APPROVED_PRODUCT',
                'title' => 'Pc Approved Product',
                'subject' => 'Digital Pharma - Pharmaceutical Supplier has approved the Product',
                'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>The product created on my behalf has been approved</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p><strong>Approved By:</strong> {{ pc_name }}</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            60=> [
                'id' => 61,
                'key' => 'PC_USER_PASSWORD',
                'title' => 'User Credential',
                'subject' => 'Digital Pharma - PARENT_NAME has been created your account',
                'email_content' => '<p>Hello USER_NAME,</p>
                                    <p>Login URL: <a href="LOGINURL" style="color:rgb(5, 54, 128); text-decoration: underline;">Click here</a></p>
                                    <p>Password: PASSWORD</p>
                                    <p>Email: EMAIL</p>
                                    <p>Please keep these credentials safe and secure.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier, Super Admin'
            ],
            61 => [
                'id' => 62,
                'key' => 'ADMIN_UPDATED_PRICE_FOR_PC',
                'title' => 'Dpharma - Admin Updated Price',
                'subject' => 'Digital Pharma - Admin has updated the price for your product.',
                'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>Admin has updated the price for following product.</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            62 => [
                'id' => 63,
                'key' => 'ADMIN_UPDATED_STOCK_FOR_PC',
                'title' => 'Admin Updated Stock',
                'subject' => 'Digital Pharma - Admin has updated the stock for your product',
                'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>Admin has updated the stock for following product.</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            63 => [
                'id' => 64,
                'key' => 'PC_REJECTED_PRODUCT',
                'title' => 'Pc Rejected Product',
                'subject' => 'Digital Pharma - Pharmaceutical Supplier has rejected the Product',
                'email_content' => '<p>Hello {{ user_name }},</p>
                    <p>The product created on my behalf has been rejected</p>
                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                    <p><strong>Category:</strong> {{ category_name }}</p>
                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                    <p><strong>Rejection Reason:</strong> {{ reason }}</p>
                    <p><strong>Rejected By:</strong> {{ pc_name }}</p>
                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Admin'
            ],
            64 => [
                'id' => 65,
                'key' => 'FACILITY_ORDER_RETRY_PAYMENT',
                'title' => 'Facility order payment retry',
                'subject' => 'Digital Pharma - Retry Payment for Your Recent Order',
                'email_content' => '<p>Hello NAME</p>
                                   <p>We regret to inform you that there was an issue processing your recent payment for the order listed below.</p>
                                    <p> Order Details:</p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <strong>Total Amount: RM </strong> AMOUNT<br>
                                    <strong>Payment Status:</strong> PAYMENTSTATUS<br>
                                    </p>
                                    <p>To avoid any interruption in your service or delivery, we request you to retry the payment at your earliest convenience.</p>
                                    <p>If you have already completed the payment, please disregard this message.</p>
                                    <p>Please check your payment details and try again. If the issue persists or if you have any questions, feel free to contact our support team.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            65 => [
                'id' => 66,
                'key' => 'PRODUCT_APPROVED',
                'title' => 'Product Approved',
                'subject' => 'Digital Pharma - Product Approved',
                'email_content' => '<p>Hello {{ user_name }},</p>
                                    <p>The following product has been approved</p>
                                    <p><strong>Product Name:</strong> {{ product_name }}</p>
                                    <p><strong>Category:</strong> {{ category_name }}</p>
                                    <p><strong>Subcategory:</strong> {{ subcategory_name }}</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            66 => [
                'id' => 67,
                'key' => 'CLINIC_PASSWORD',
                'title' => 'Facility Account Created',
                'subject' => 'Digital Pharma - Your Account Has Been Created',
                'email_content' => '<p>Hello,</p>
                                    <p>Your account has been created by Digital Pharma. Please find your login credentials below:</p>
                                    <p><strong>Email:</strong> EMAIL</p>
                                    <p><strong>Password:</strong> PASSWORD</p>
                                    <p><strong>Login URL:</strong> <a href="LOGINURL" style="color: #0d6efd; text-decoration: underline;">Click here</a></p>
                                    <p>Please keep these credentials safe and secure. We recommend changing your password after your first login.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            67 => [
                'id' => 68,
                'key' => 'FACILITY_SELF_INQUIRY',
                'title' => 'Facility Self-Inquiry Received',
                'subject' => 'Digital Pharma - Inquiry Received',
                'email_content' => '<p>Hello NAME</p>
                                   <p>We have received your inquiry submitted through the Digital Pharma. Thank you for reaching out to us.</p>
                                   <p>Our support team is currently reviewing your request and will get back to you within <strong>24–48 business hours</strong>.</p>

                                    <p> Inquiry Details:</p>
                                    <p> <strong>Name:</strong> NAME<br>
                                    <strong>Subject:</strong> SUBJECT<br>
                                    </p>
                                   <p>We appreciate your engagement and will ensure your concerns are addressed promptly.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            68 => [
                'id' => 69,
                'key' => 'FACILITY_ORDER_PAYMENT_PENDING',
                'title' => 'Facility pay now order payment pending',
                'subject' => 'Digital Pharma - Payment Pending',
                'email_content' => '<p>Hello NAME</p>
                                    <p>Thank you for initiating your payment for <strong>Order Number: ORDERNUMBER</strong>.</p>

                                    <p>We would like to inform you that your payment is currently <strong>pending</strong> from our end. Our system is in the process of verifying the transaction, and this may take up to <strong>24–48 business hours</strong>.</p>

                                    <p>Once the payment is successfully confirmed, you will receive a notification and your order will be processed accordingly.</p>


                                    <p> Order Details:</p>
                                    <p> <strong>Order ID:</strong> ORDERNUMBER<br>
                                    <strong>Order Date:</strong> CREATEDDATE<br>
                                    <strong>Total Amount: RM </strong> AMOUNT<br>
                                    <strong>Payment Status:</strong> PAYMENTSTATUS<br>
                                    </p>

                                    <p>We appreciate your patience and understanding.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            69 => [
                'id' => 70,
                'key' => 'FACILITY_SUPPORT_TICKET_GENERATE',
                'title' => 'Facility Support Ticket Generate Successfully',
                'subject' => 'Digital Pharma - Support Ticket Generate Successfully',
                'email_content' => '<p>Hello NAME</p>
                                <p>We have received your support ticket submitted through the Digital Pharma. Thank you for reaching out to us.</p>
                                <p>Our support team is currently reviewing your request and will get back to you within <strong>24–48 business hours</strong>.</p>

                                    <p> Support Ticket Details:</p>
                                    <p> <strong>Name:</strong> NAME</p>
                                    <p> <strong>Order Number:</strong> ORDERNUMBER</p>
                                    <p> <strong>Raised To:</strong> RAISEDTO</p>
                                    </p><strong>Subject:</strong> SUBJECT</p>

                                <p>We appreciate your engagement and will ensure your concerns are addressed promptly.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Facility'
            ],
            70 => [
                'id' => 71,
                'key' => 'ADMIN_INQUIRY',
                'title' => 'New Inquiry Request Submitted',
                'subject' => 'Digital Pharma - New Inquiry Received',
                'email_content' => '<p>Hello NAME</p>
                                  <p>You have received a new inquiry request from a FACILITYNAME. Below are the details:</p>
                                    <p>
                                        <strong>Name:</strong> NAME<br>
                                        <strong>Subject:</strong> SUBJECT
                                    </p>
                                    <p>Please log in to your admin panel to view and respond to the inquiry.</p>

                                ',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],
            71 => [
                'id' => 72,
                'key' => 'ASSIGN_CREDIT',
                'title' => 'New Credit Assigned',
                'subject' => 'Credit Limit Assigned by Supplier',
                'email_content' => '<p>Hello USER</p>
                                  <p>You have been assigned a new credit limit of ASSIGNCREDIT by SUPPLIER for your future purchase orders.</p>
                                    <p>Your total available credit limit from this supplier is now TOTALCREDIT.</p>
                                    <p>This credit limit allows you to place orders up to the specified amount without immediate payment, making your purchasing process smoother and more flexible.</p>

                                ',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],
            72 => [
                'id' => 73,
                'key' => 'UPDATE_CREDIT',
                'title' => 'Updated Credit Limit from Supplier',
                'subject' => 'Updated Credit Limit from Supplier',
                'email_content' => '<p>Hello USER</p>
                                  <p>Your credit limit from SUPPLIER has been updated.</p>
                                    <p>Your total available credit limit from this supplier is now TOTALCREDIT.</p>
                                    <p>This credit limit allows you to place orders up to the specified amount without immediate payment, making your purchasing process smoother and more flexible.</p>

                                ',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],
            71 => [
                'id' => 72,
                'key' => 'MERCHANT_PAYOUT_COMMISSION',
                'title' => 'Merchant Payout Commission',
                'subject' => 'Digital Pharma - Payout Commission Report (CYCLEDATE)',
                'email_content' => '<p>Hello NAME</p>
                                    <p>The commission payout for the cycle <strong>CYCLEDATE</strong> has been successfully processed.</p>
                                    <p>The <strong>TYPE</strong> payout for this cycle is now complete, and the corresponding admin fee has been settled.</p>
                                    <p><strong>Admin Fee:</strong> ADMINFEE</p>
                                    <p><strong>Supplier Name:</strong> PCNAME</p>
                                    <p><strong>Supplier Email:</strong> EMAIL</p>
                                    <p>This is a confirmation for your records. No further action is required.</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],
            72 => [
                'id' => 73,
                'key' => 'PC_PAYOUT_COMMISSION_CONFIRMATION',
                'title' => 'PC Payout Commission Confirmation',
                'subject' => 'Digital Pharma - Payout Commission successfully for this cycle (CYCLEDATE)',
                'email_content' => '<p>Hello NAME,</p>
                                    <p>We are pleased to inform you that your payment for the cycle <strong>CYCLEDATE</strong> has been successfully received to <strong>Digital Pharma</strong>.</p>
                                    <p>The <strong>TYPE</strong> payout for this cycle is now complete, and the corresponding admin fee has been settled.</p>
                                    <p><strong>Amount Paid:</strong> ADMINFEE</p>
                                    <p>Thank you for your prompt payment and continued partnership.</p>
                                    <p>If you have any questions or need assistance, please contact our support team.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Pharmaceutical Supplier'
            ],
            73 => [
                'id' => 74,
                'key' => 'ASSIGN_CREDIT',
                'title' => 'New Credit Assigned',
                'subject' => 'Digital Pharma - Credit Limit Assigned by Supplier',
                'email_content' => '<p>Hello USER</p>
                                    <p>You have been assigned a new credit limit of ASSIGNCREDIT by SUPPLIER for your future purchase orders.</p>
                                    <p>Your total available credit limit from this supplier is now TOTALCREDIT.</p>
                                    <p>This credit limit allows you to place orders up to the specified amount without immediate payment, making your purchasing process smoother and more flexible.</p>
                                    <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],
            74 => [
                'id' => 75,
                'key' => 'UPDATE_CREDIT',
                'title' => 'Updated Credit Limit from Supplier',
                'subject' => 'Digital Pharma - Updated Credit Limit from Supplier',
                'email_content' => '<p>Hello USER</p>
                                <p>Your credit limit from SUPPLIER has been updated.</p>
                                <p>Your total available credit limit from this supplier is now TOTALCREDIT.</p>
                                <p>This credit limit allows you to place orders up to the specified amount without immediate payment, making your purchasing process smoother and more flexible.</p>
                                <p>Thank you for choosing Digital Pharma!</p>',
                'is_sms' => 0,
                'is_email' => 1,
                'email_to' => 'Super Admin'
            ],

        ]);
    }
}
