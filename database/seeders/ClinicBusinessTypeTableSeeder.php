<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;


class ClinicBusinessTypeTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $businessTypes = [
            'Sole Proprietary',
            'Sdn Bhd',
            'Pvt Ltd',
            'Berhad',
        ];

    foreach ($businessTypes as $name) {
        DB::table('business_types')->updateOrInsert(
            ['name' => $name], 
            ['key' => Str::slug($name)]
        );
    } 
    }
}
