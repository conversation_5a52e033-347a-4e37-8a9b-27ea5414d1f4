<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate table and reset auto-increment
        DB::table('countries')->truncate();
        DB::statement("ALTER SEQUENCE countries_id_seq RESTART WITH 1");

        // Read and decode the JSON file
        $jsonPath = resource_path('json/countries.json');

        if (!File::exists($jsonPath)) {
            $this->command->error("File not found: $jsonPath");
            return;
        }

        $jsonData = File::get($jsonPath);
        $countries = json_decode($jsonData, true);

        if (is_array($countries)) {
            DB::table('countries')->insert($countries);
            $this->command->info('Countries seeded successfully.');
        } else {
            $this->command->error('Failed to decode JSON or empty array.');
        }
    }
}
