<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    protected $toTruncate = [
        'account_types',
        'clinic_account_types',
        'cms_pages',
        'email_templates',
        'support_categories',
        'term_conditions',
        'pc_company_types',
        'business_types',
        'zip_codes',
    ];

    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();
        foreach ($this->toTruncate as $table) {
            DB::table($table)->truncate();
        }

        $this->call(AccountTypeTableSeeder::class);
        $this->call(ClinicBusinessTypeTableSeeder::class);
        $this->call(ClinicAccountTypeTableSeeder::class);
        $this->call(CMSPageTableSeeder::class);
        $this->call(EmailTemplateTableSeeder::class);
        // $this->call(WorldSeeder::class);
        $this->call(CountrySeeder::class);
        $this->call(StateSeeder::class);
        $this->call(CitySeeder::class);
        // $this->call(NewCityTableSeeder::class);
        $this->call(ZipCodeTableSeeder::class);
        $this->call(HelpSupportCategorySeeder::class);
        $this->call(CityLandlineSeeder::class);
        $this->call(TermConditionSeeder::class);
        $this->call(PcCompanyType::class);
        $roles = [
            'Super Admin',
            'Pharmaceutical Company',
            'Clinic',
        ];
        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role, 'guard_name' => 'web']);
        }
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('Password#1'),
        ]);

        //model_has_roles
        DB::table('model_has_roles')->insert([
            'role_id' => 1,
            'model_type' => 'App\Models\User',
            'model_id' => 1
        ]);
        DB::statement('ALTER TABLE notifications ALTER COLUMN data TYPE JSONB USING data::JSONB');

        // DB::table('states')->update([
        //     'zone' => DB::raw("CASE
        //         WHEN name = 'Sabah' OR name = 'Sarawak' THEN 'east'
        //         ELSE 'west'
        //     END")
        // ]);
    }
}
