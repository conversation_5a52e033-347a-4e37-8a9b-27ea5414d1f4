<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate table and reset auto-increment
        DB::table('states')->truncate();
        DB::statement("ALTER SEQUENCE states_id_seq RESTART WITH 1");

        // Read and decode the JSON file
        $jsonPath = resource_path('json/states.json');

        if (!File::exists($jsonPath)) {
            $this->command->error("File not found: $jsonPath");
            return;
        }

        $jsonData = File::get($jsonPath);
        $states = json_decode($jsonData, true);

        if (is_array($states)) {
            DB::table('states')->insert($states);
            $this->command->info('states seeded successfully.');
        } else {
            $this->command->error('Failed to decode JSON or empty array.');
        }
    }
}
