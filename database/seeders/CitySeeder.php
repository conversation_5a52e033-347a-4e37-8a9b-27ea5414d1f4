<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Truncate table and reset auto-increment
        DB::table('cities')->truncate();
        DB::statement("ALTER SEQUENCE cities_id_seq RESTART WITH 1");

        // Read and decode the JSON file
        $jsonPath = resource_path('json/cities.json');

        if (!File::exists($jsonPath)) {
            $this->command->error("File not found: $jsonPath");
            return;
        }

        $jsonData = File::get($jsonPath);
        $cities = json_decode($jsonData, true);

        if (is_array($cities)) {
            DB::table('cities')->insert($cities);
            $this->command->info('cities seeded successfully.');
        } else {
            $this->command->error('Failed to decode JSON or empty array.');
        }
    }
}
