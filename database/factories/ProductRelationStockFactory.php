<?php

namespace Database\Factories;

use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductRelationStock>
 */
class ProductRelationStockFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $stock = fake()->numberBetween(0, 1000);
        $isBatchWise = fake()->boolean();
        
        return [
            'product_relation_id' => 1, // Will be overridden in tests
            'product_variant_id' => ProductVariant::factory(),
            'stock' => $stock,
            'low_stock' => fake()->numberBetween(1, 10),
            'is_batch_wise_stock' => $isBatchWise,
            'total_stock' => $stock,
            'expiry_date' => fake()->optional()->dateTimeBetween('now', '+2 years'),
        ];
    }

    /**
     * Stock type state
     */
    public function stockType(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_batch_wise_stock' => false,
        ]);
    }

    /**
     * Batch type state
     */
    public function batchType(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_batch_wise_stock' => true,
            'expiry_date' => fake()->dateTimeBetween('now', '+2 years'),
        ]);
    }

    /**
     * Low stock state
     */
    public function lowStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => fake()->numberBetween(0, 5),
            'total_stock' => fake()->numberBetween(0, 5),
        ]);
    }

    /**
     * High stock state
     */
    public function highStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock' => fake()->numberBetween(100, 1000),
            'total_stock' => fake()->numberBetween(100, 1000),
        ]);
    }
} 