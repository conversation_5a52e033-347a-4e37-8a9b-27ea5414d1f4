<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\DosageForm;
use App\Models\Container;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->words(3, true),
            'is_import_mims' => false,
            'low_stock' => fake()->numberBetween(1, 10),
            'category_id' => Category::factory(),
            'sub_category_id' => null, // Made nullable in later migration
            'dosage_foams_id' => DosageForm::factory(),
            'sku' => fake()->unique()->regexify('[A-Z]{3}-[0-9]{3}'),
            'unit_id' => null, // Made nullable in later migration
            'quantity_per_unit' => null, // Made nullable and string in later migration
            'is_prescription_required' => fake()->boolean(),
            'description_active_benefits' => fake()->paragraph(),
            'description_strength' => fake()->sentence(),
            'description_indications' => fake()->paragraph(),
            'description_contradictions' => fake()->paragraph(),
            'description_how_to_use' => fake()->paragraph(),
            'description_storage_instructions' => fake()->paragraph(),
            'description_side_effects' => fake()->paragraph(),
            'description_faq' => fake()->paragraph(),
            'mda_number' => fake()->optional()->numerify('MDA###'),
            'mda_certificate' => fake()->optional()->word(),
            'add_request_by' => User::factory(),
            'is_created_by_admin' => fake()->boolean(),
            'approved_by' => null, // Made nullable in later migration
            'approved_on' => fake()->optional()->dateTimeBetween('-1 year', 'now'),
            'admin_verified_on' => fake()->optional()->dateTimeBetween('-1 year', 'now'),
            'admin_verification_submitted_on' => fake()->optional()->dateTimeBetween('-1 year', 'now'),
            'status' => fake()->randomElement(['pending', 'approved', 'rejected']),
            'brand_id' => Brand::factory(), // Added from additional migration
            'container_id' => Container::factory(), // Added from additional migration
            'generic_name_id' => null, // Replaced generic_name column
        ];
    }

    /**
     * Indicate that the product is approved
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'approved',
            'approved_by' => User::factory(),
            'approved_on' => now(),
        ]);
    }

    /**
     * Indicate that the product is pending
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'approved_by' => null,
            'approved_on' => null,
        ]);
    }

    /**
     * Indicate that the product is created by admin
     */
    public function createdByAdmin(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_created_by_admin' => true,
        ]);
    }
} 