<?php

namespace Database\Factories;

use App\Models\Attribute;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AttributeValue>
 */
class AttributeValueFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'attribute_id' => Attribute::factory(),
            'name' => fake()->word(),
        ];
    }

    /**
     * Create color attribute values
     */
    public function color(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->colorName(),
        ]);
    }

    /**
     * Create size attribute values
     */
    public function size(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->randomElement(['Small', 'Medium', 'Large', 'XS', 'XL', 'XXL']),
        ]);
    }

    /**
     * Create strength attribute values
     */
    public function strength(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => fake()->randomElement(['250mg', '500mg', '1000mg', '100ml', '200ml']),
        ]);
    }
} 