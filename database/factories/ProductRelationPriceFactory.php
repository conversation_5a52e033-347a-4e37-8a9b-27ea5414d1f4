<?php

namespace Database\Factories;

use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductRelationPrice>
 */
class ProductRelationPriceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'product_relation_id' => 1, // Will be overridden in tests
            'product_variant_id' => ProductVariant::factory(),
            'west_zone_price' => fake()->randomFloat(2, 10, 1000),
            'east_zone_price' => fake()->randomFloat(2, 10, 1000),
            
            // Bonus pricing fields
            'west_bonus_1_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'east_bonus_1_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'west_bonus_1_quantity' => fake()->optional()->numberBetween(1, 100),
            'east_bonus_1_quantity' => fake()->optional()->numberBetween(1, 100),
            'west_bonus_1_quantity_value' => fake()->optional()->numberBetween(1, 10),
            'east_bonus_1_quantity_value' => fake()->optional()->numberBetween(1, 10),
            
            // Tier pricing fields
            'west_tier_1_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'east_tier_1_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'west_tier_2_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'east_tier_2_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'west_tier_3_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
            'east_tier_3_base_price' => fake()->optional()->randomFloat(2, 10, 1000),
        ];
    }

    /**
     * Fixed pricing type
     */
    public function fixed(): static
    {
        return $this->state(fn (array $attributes) => [
            'west_zone_price' => fake()->randomFloat(2, 10, 1000),
            'east_zone_price' => fake()->randomFloat(2, 10, 1000),
        ]);
    }

    /**
     * Bonus pricing type
     */
    public function bonus(): static
    {
        return $this->state(fn (array $attributes) => [
            'west_bonus_1_base_price' => fake()->randomFloat(2, 10, 1000),
            'east_bonus_1_base_price' => fake()->randomFloat(2, 10, 1000),
            'west_bonus_1_quantity' => fake()->numberBetween(10, 50),
            'east_bonus_1_quantity' => fake()->numberBetween(10, 50),
            'west_bonus_1_quantity_value' => fake()->numberBetween(1, 5),
            'east_bonus_1_quantity_value' => fake()->numberBetween(1, 5),
        ]);
    }

    /**
     * Tier pricing type
     */
    public function tier(): static
    {
        return $this->state(fn (array $attributes) => [
            'west_tier_1_base_price' => fake()->randomFloat(2, 10, 500),
            'east_tier_1_base_price' => fake()->randomFloat(2, 10, 500),
            'west_tier_2_base_price' => fake()->randomFloat(2, 500, 800),
            'east_tier_2_base_price' => fake()->randomFloat(2, 500, 800),
            'west_tier_3_base_price' => fake()->randomFloat(2, 800, 1000),
            'east_tier_3_base_price' => fake()->randomFloat(2, 800, 1000),
        ]);
    }
} 