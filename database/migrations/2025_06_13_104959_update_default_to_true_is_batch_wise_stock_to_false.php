<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('product_relation_stocks')->whereNull('is_batch_wise_stock')->update(['is_batch_wise_stock' => true]);
        Schema::table('product_relation_stocks', function (Blueprint $table) {
            $table->boolean('is_batch_wise_stock')->default(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_relation_stocks', function (Blueprint $table) {
            $table->dropColumn('is_batch_wise_stock');
        });
    }
};
