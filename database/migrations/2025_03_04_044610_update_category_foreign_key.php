<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('support_tickets', function (Blueprint $table) {
            $table->dropForeign(['category_id']); // Drop the existing foreign key
            $table->foreign('category_id')->references('id')->on('support_categories')->cascadeOnDelete(); // Add new foreign key
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('support_tickets', function (Blueprint $table) {
            DB::table('support_tickets')
                ->whereNotNull('category_id')
                ->whereNotIn('category_id', function ($query) {
                    $query->select('id')->from('support_categories');
                })
                ->update(['category_id' => null]);
            $table->dropForeign(['category_id']); // Drop the new foreign key
            $table->foreign('category_id')->references('id')->on('categories')->cascadeOnDelete()->nullable(); // Revert back to old foreign key
        });
    }
};
