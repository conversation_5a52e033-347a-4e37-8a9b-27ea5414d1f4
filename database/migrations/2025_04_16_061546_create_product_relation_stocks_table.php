<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_relation_stocks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_relation_id')->constrained('products_relation');
            $table->foreignId('product_attribute_id')->constrained('product_attributes')->nullable();
            $table->foreignId('product_variant_id')->constrained('product_variants')->nullable();
            
            $table->boolean('is_batch_wise_stock')->default(0);

            $table->integer('stock')->nullable();
            $table->integer('total_stock')->nullable();
            $table->integer('low_stock')->nullable();
            $table->integer('weight')->nullable(); //need to confirm with chatgpt
            $table->timestamp('expiry_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_relation_stocks');
    }
};
