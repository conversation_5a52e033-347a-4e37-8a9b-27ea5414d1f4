<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
         Schema::table('order_products', function (Blueprint $table) {
            $table->string('stock_type')->nullable();
            $table->integer('wholesale_pack_size')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::table('order_products', function (Blueprint $table) {
            $table->dropColumn(['stock_type','wholesale_pack_size']);
        });
    }
};
