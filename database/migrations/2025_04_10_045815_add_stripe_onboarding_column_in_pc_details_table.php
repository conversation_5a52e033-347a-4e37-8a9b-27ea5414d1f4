<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            $table->string('stripe_connect_id')->nullable();
            $table->enum('stripe_on_boarding_status', ['pending', 'completed'])->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            //
        });
    }
};
