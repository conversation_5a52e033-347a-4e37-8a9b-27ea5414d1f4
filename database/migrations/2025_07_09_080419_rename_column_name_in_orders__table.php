<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE orders RENAME COLUMN stripe_session_id TO ecommerce_tran_id');

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       
        DB::statement('ALTER TABLE orders RENAME COLUMN ecommerce_tran_id TO stripe_session_id');

    }
};
