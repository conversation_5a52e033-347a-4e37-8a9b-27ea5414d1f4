<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_relation_prices', function (Blueprint $table) {
            $table->dropForeign(['product_attribute_id']);
            $table->foreignId('product_attribute_id')->nullable()->change();
            $table->foreign('product_attribute_id')->references('id')->on('product_attributes');


            $table->dropForeign(['product_variant_id']);
            $table->foreignId('product_variant_id')->nullable()->change();
            $table->foreign('product_variant_id')->references('id')->on('product_variants');
        
        });

        Schema::table('product_relation_stocks', function (Blueprint $table) {
            $table->dropForeign(['product_attribute_id']);
            $table->foreignId('product_attribute_id')->nullable()->change();
            $table->foreign('product_attribute_id')->references('id')->on('product_attributes');


            $table->dropForeign(['product_variant_id']);
            $table->foreignId('product_variant_id')->nullable()->change();
            $table->foreign('product_variant_id')->references('id')->on('product_variants');
        
        });

        Schema::table('products_batch', function (Blueprint $table) {
            $table->dropForeign(['product_relation_stock_id']);
            $table->foreignId('product_relation_stock_id')->nullable()->change();
            $table->foreign('product_relation_stock_id')->references('id')->on('products_relation');
        
        });
        
    }
};
