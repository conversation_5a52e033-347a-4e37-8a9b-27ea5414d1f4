<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payouts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->references('id')->on('users');
            $table->enum('payout_type', ['full', 'schedule']);
            $table->string('transaction_id')->references('id')->on('transactions')->nullable();
            $table->enum('cycle_type', ['bi_weekly', 'monthly'])->nullable();
            $table->boolean('is_payout')->default(false);
            $table->timestamp('payout_on')->nullable();
            $table->enum('payment_type', ['online', 'offline'])->nullable();
            $table->boolean('is_received')->default(false);
            $table->timestamp('received_on')->nullable();
            $table->text('received_marked')->nullable();
            $table->enum('outstanding_commission_status', ['pending', 'received', 'rejected'])->nullable();
            $table->integer('remainder_count')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->foreignId('rejected_by')->nullable()->references('id')->on('users');
            $table->text('rejected_note')->nullable();
            $table->dateTime('start_date')->nullable();
            $table->dateTime('end_date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payouts');
    }
};
