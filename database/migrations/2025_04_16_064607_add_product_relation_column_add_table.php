<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products_batch', function (Blueprint $table) {
            $table->foreignId('product_relation_stock_id')->constrained('product_relation_stocks')->nullable();
        });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products_batch', function (Blueprint $table) {

            if (Schema::hasColumn('products_batch', 'product_relation_stock_id')) {
                $table->dropForeign(['product_relation_stock_id']);
                $table->dropColumn('product_relation_stock_id');
            }
        });
    }
};
