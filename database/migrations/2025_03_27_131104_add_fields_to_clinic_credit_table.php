<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinic_credit', function (Blueprint $table) {
            $table->decimal('debit_amount', 10, 2)->nullable();
            $table->decimal('remaining_balance', 10, 2)->nullable();
            $table->decimal('total_credit_amount', 10, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinic_credit', function (Blueprint $table) {
            $table->dropColumn(['debit_amount', 'remaining_balance', 'total_credit_amount']);
        });
    }
};
