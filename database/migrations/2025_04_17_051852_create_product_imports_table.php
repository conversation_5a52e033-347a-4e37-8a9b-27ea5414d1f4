<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (!Schema::hasTable('product_imports')) {
            Schema::create('product_imports', function (Blueprint $table) {
                $table->id();
                $table->string('name')->nullable();
                $table->string('folder_path')->nullable();
                $table->string('audit_file')->nullable(); // For storing the audit log filename
                $table->enum('status', [
                    'Completed',
                    'Pending',
                    'Inprogress',
                    'Failed',
                    'CompletedWithError',
                    'Killed'
                ])->default('Pending'); // Status of the import
                $table->text('command')->nullable();
                $table->string('process_id')->nullable()->after('folder_path');
                $table->integer('retry_frequency')->nullable()->default(0)->after('process_id');
                $table->timestamp('start_date_time')->nullable()->after('retry_frequency');
                $table->timestamp('end_date_time')->nullable()->after('start_date_time');
                $table->text('description')->nullable()->after('end_date_time');
                $table->timestamps();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_imports');
    }
};
