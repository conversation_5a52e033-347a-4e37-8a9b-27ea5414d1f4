<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSubOrderIdToTransactionsTable extends Migration
{
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->unsignedBigInteger('sub_order_id')->nullable()->after('order_id'); // or after any relevant column

            // Optional: Add foreign key constraint if sub_orders table exists
            // $table->foreign('sub_order_id')->references('id')->on('sub_orders')->onDelete('set null');
        });
    }

    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // First drop foreign key if added
            // $table->dropForeign(['sub_order_id']);
            $table->dropColumn('sub_order_id');
        });
    }
};
