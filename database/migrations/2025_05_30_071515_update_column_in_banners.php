<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Drop only the check constraint (so we can modify nullability/default)
        DB::statement("ALTER TABLE banners DROP CONSTRAINT IF EXISTS banners_redirect_to_check");

        // Allow NULLs and remove default — this does not affect existing values
        DB::statement("ALTER TABLE banners ALTER COLUMN redirect_to DROP NOT NULL");
        DB::statement("ALTER TABLE banners ALTER COLUMN redirect_to DROP DEFAULT");

        // Re-add the check constraint (enum-like) to keep validation
        DB::statement("ALTER TABLE banners ADD CONSTRAINT banners_redirect_to_check CHECK (redirect_to IS NULL OR redirect_to IN ('seller', 'product', 'category', 'brand'))");
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
