<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_order_trackings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipping_label_id')->constrained('shipping_labels');
            $table->string('status')->nullable();
            $table->string('status_code')->nullable();
            $table->string('shipping_code')->nullable();
            $table->longText('shipping_response')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_order_trackings');
    }
};
