<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('filament_email_2fa_codes', function (Blueprint $table) {
            $table->integer('two_fa_attempts')->default(0);
            $table->timestamp('two_fa_banned_until')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('filament_email_2fa_codes', function (Blueprint $table) {
            $table->dropColumn('two_fa_attempts');
            $table->dropColumn('two_fa_banned_until');
        });
    }
};
