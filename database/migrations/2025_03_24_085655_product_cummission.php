<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_cummission', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('east_fixed_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_fixed_cummission_value')->nullable();
            $table->enum('west_fixed_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_fixed_cummission_value')->nullable();
            $table->enum('east_tier_1_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_tier_1_cummission_value')->nullable();
            $table->enum('east_tier_2_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_tier_2_cummission_value')->nullable();
            $table->enum('east_tier_3_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_tier_3_cummission_value')->nullable();
            $table->enum('west_tier_1_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_tier_1_cummission_value')->nullable();
            $table->enum('west_tier_2_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_tier_2_cummission_value')->nullable();
            $table->enum('west_tier_3_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_tier_3_cummission_value')->nullable();

            $table->enum('east_bonus_1_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_bonus_1_cummission_value')->nullable();
            $table->enum('east_bonus_2_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_bonus_2_cummission_value')->nullable();
            $table->enum('east_bonus_3_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('east_bonus_3_cummission_value')->nullable();

            $table->enum('west_bonus_1_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_bonus_1_cummission_value')->nullable();
            $table->enum('west_bonus_2_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_bonus_2_cummission_value')->nullable();
            $table->enum('west_bonus_3_cummission_type', ['flat', 'percentage'])->nullable()->default('flat');
            $table->string('west_bonus_3_cummission_value')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_cummission');
    }
};
