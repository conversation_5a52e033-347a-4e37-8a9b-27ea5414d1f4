<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            $table->string('delivery_days_west')->nullable()->after('delivery_days');
            $table->boolean('is_version_pending')->default(false)->after('delivery_days');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            $table->dropColumn(['delivery_days_west', 'is_version_pending']);
        });
    }
};
