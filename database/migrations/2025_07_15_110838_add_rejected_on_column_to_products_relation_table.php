<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products_relation', function (Blueprint $table) {
            $table->dateTime('rejected_on')->nullable();
            $table->dateTime('approved_on')->nullable();
            $table->dateTime('submitted_on')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products_relation', function (Blueprint $table) {
            $table->dropColumn('rejected_on');
            $table->dropColumn('approved_on');
            $table->dropColumn('submitted_on');
        });
    }
};
