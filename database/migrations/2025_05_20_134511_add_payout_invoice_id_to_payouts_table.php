<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->string('payout_invoice_id')->nullable(); // or choose an appropriate column to position after
        });
    }

    public function down(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->dropColumn('payout_invoice_id');
        });
    }
};

