<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->enum('payout_status', ['pending', 'paid','failed'])
                ->default('pending')
                ->comment('Payout status for vendor: pending, paid, or failed');
            $table->text('payout_failed_reason')->nullable();

        DB::statement("COMMENT ON COLUMN payouts.payout_type IS 'Type of payout: full or schedule';");
        DB::statement("COMMENT ON COLUMN payouts.transaction_id IS 'Reference to stripe transaction ID';");
        DB::statement("COMMENT ON COLUMN payouts.cycle_type IS 'Cycle type : bi_weekly,monthly';");
        DB::statement("COMMENT ON COLUMN payouts.payment_type IS 'Payment method : online , offline';");
        DB::statement("COMMENT ON COLUMN payouts.outstanding_commission_status IS 'Status : pending, received, rejected';");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->dropColumn(['payout_status','payout_failed_reason']);
        });
    }
};
