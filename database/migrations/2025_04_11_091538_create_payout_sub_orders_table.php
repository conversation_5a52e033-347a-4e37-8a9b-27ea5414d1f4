<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payout_sub_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payout_id')->constrained('payouts');
            $table->foreignId('sub_order_id')->constrained('sub_orders');
            $table->foreignId('order_id')->nullable()->constrained('orders');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payout_sub_orders');
    }
};
