<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->text('product_description')->nullable();
            $table->text('description_dosage')->nullable();
            $table->text('description_ingredients')->nullable();
            $table->text('description_safety_information')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('product_description');
            $table->dropColumn('description_ingredients');
            $table->dropColumn('description_dosage');
            $table->dropColumn('description_safety_information');
        });
    }
};
