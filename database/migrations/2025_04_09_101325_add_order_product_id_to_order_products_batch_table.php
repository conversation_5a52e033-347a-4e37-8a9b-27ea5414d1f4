<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_products_batch', function (Blueprint $table) {
            $table->unsignedBigInteger('order_product_id')->after('id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_products_batch', function (Blueprint $table) {
            $table->dropColumn(['order_product_id']);
        });
    }
};
