<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE product_imports DROP CONSTRAINT IF EXISTS product_imports_status_check");

        // Add the new CHECK constraint with 'Success' included
        DB::statement("
            ALTER TABLE product_imports 
            ADD CONSTRAINT product_imports_status_check 
            CHECK (status IN (
                'Completed', 
                'Pending', 
                'Inprogress', 
                'Failed', 
                'CompletedWithError', 
                'Killed', 
                'Success'
            ))
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE product_imports DROP CONSTRAINT IF EXISTS product_imports_status_check");

        DB::statement("
            ALTER TABLE product_imports 
            ADD CONSTRAINT product_imports_status_check 
            CHECK (status IN (
                'Completed', 
                'Pending', 
                'Inprogress', 
                'Failed', 
                'CompletedWithError', 
                'Killed'
            ))
        ");
    }
};
