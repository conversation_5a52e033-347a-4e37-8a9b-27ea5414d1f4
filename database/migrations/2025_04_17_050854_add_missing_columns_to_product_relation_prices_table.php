<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_relation_prices', function (Blueprint $table) {
            $table->decimal("east_bonus_1_base_price", 10, 2)->nullable();
            $table->decimal('east_bonus_2_base_price', 10, 2)->nullable();
            $table->decimal('east_bonus_3_base_price', 10, 2)->nullable();
            $table->decimal('west_bonus_1_base_price', 10, 2)->nullable();
            $table->decimal('west_bonus_2_base_price', 10, 2)->nullable();
            $table->decimal('west_bonus_3_base_price', 10, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_relation_prices', function (Blueprint $table) {
            $table->dropColumn('east_bonus_1_base_price');
            $table->dropColumn('east_bonus_2_base_price');
            $table->dropColumn('east_bonus_3_base_price');
            $table->dropColumn('west_bonus_1_base_price');
            $table->dropColumn('west_bonus_2_base_price');
            $table->dropColumn('west_bonus_3_base_price');
        });
    }
};
