<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
             $table->enum('new_status', ['pending', 'approved', 'rejected'])->nullable();
        });

        DB::statement("UPDATE clinic_pharma_suppliers SET new_status = CAST(status AS TEXT)");

        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->renameColumn('new_status', 'status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->enum('old_status', ['approved', 'rejected'])->nullable();
        });

        DB::statement("UPDATE clinic_pharma_suppliers SET old_status = CAST(status AS TEXT) WHERE status IN ('approved', 'rejected')");

        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->renameColumn('old_status', 'status');
        });
    }
};
