<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create("distributor_product", function (Blueprint $table) {
            $table->bigIncrements("id");
            $table->foreignId('product_id')->constrained('products');
            $table->foreignId('distributor_id')->constrained('distributors');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
