<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products_relation', function (Blueprint $table) {
           
            $table->enum('variant_images_option', ['skip_image', 'attribute_image','variant_image'])
            ->nullable()
            ->comment('product image options: skip_image, attribute_image, or variant_image');

            $table->enum('variant_price_option', ['skip_price', 'attribute_price','variant_price'])
            ->nullable()
            ->comment('product price options: skip_price, attribute_price, or variant_price');

            $table->enum('variant_stock_option', ['skip_stock', 'attribute_stock','variant_stock'])
            ->nullable()
            ->comment('product stock options: skip_stock, attribute_stock, or variant_stock');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products_relation', function (Blueprint $table) {
            
        });
    }
};
