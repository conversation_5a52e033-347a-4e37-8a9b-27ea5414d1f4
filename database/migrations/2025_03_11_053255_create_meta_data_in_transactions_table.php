<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->json('meta_data')->nullable();
            if (Schema::hasColumn('transactions', 'payment_method')) {
                $table->string('payment_method')
                        ->comment('values: credit_card, debit_card, wire_transfer,netbanking')
                        ->change();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('meta_data');
            if (Schema::hasColumn('transactions', 'payment_method')) {
                $table->string('payment_method')
                    ->comment(null)
                    ->change();
            }
        });
    }
};
