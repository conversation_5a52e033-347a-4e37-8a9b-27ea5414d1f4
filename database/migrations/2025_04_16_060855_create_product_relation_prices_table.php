<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_relation_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_relation_id')->constrained('products_relation');
            $table->foreignId('product_attribute_id')->constrained('product_attributes')->nullable();
            $table->foreignId('product_variant_id')->constrained('product_variants')->nullable();
            
            $table->integer('east_bonus_1_quantity')->nullable();
            $table->integer('east_bonus_1_quantity_value')->nullable();
            $table->integer('east_bonus_2_quantity')->nullable();
            $table->integer('east_bonus_2_quantity_value')->nullable();
            $table->integer('east_bonus_3_quantity')->nullable();
            $table->integer('east_bonus_3_quantity_value')->nullable();
            $table->integer('west_bonus_1_quantity')->nullable();
            $table->integer('west_bonus_1_quantity_value')->nullable();
            $table->integer('west_bonus_2_quantity')->nullable();
            $table->integer('west_bonus_2_quantity_value')->nullable();
            $table->integer('west_bonus_3_quantity')->nullable();
            $table->integer('west_bonus_3_quantity_value')->nullable();

            $table->integer('east_tier_1_min_quantity')->nullable();
            $table->integer('east_tier_1_max_quantity')->nullable();
            $table->decimal('east_tier_1_base_price', 8, 2)->nullable();
            $table->integer('east_tier_2_min_quantity')->nullable();
            $table->integer('east_tier_2_max_quantity')->nullable();
            $table->decimal('east_tier_2_base_price', 8, 2)->nullable();
            $table->integer('east_tier_3_min_quantity')->nullable();
            $table->integer('east_tier_3_max_quantity')->nullable();
            $table->decimal('east_tier_3_base_price', 8, 2)->nullable();

            $table->integer('west_tier_1_min_quantity')->nullable();
            $table->integer('west_tier_1_max_quantity')->nullable();
            $table->decimal('west_tier_1_base_price', 8, 2)->nullable();
            $table->integer('west_tier_2_min_quantity')->nullable();
            $table->integer('west_tier_2_max_quantity')->nullable();
            $table->decimal('west_tier_2_base_price', 8, 2)->nullable();
            $table->integer('west_tier_3_min_quantity')->nullable();
            $table->integer('west_tier_3_max_quantity')->nullable();
            $table->decimal('west_tier_3_base_price', 8, 2)->nullable();

           
            $table->decimal('east_zone_price', 8, 2)->nullable();
            $table->decimal('west_zone_price', 8, 2)->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_relation_prices');
    }
};
