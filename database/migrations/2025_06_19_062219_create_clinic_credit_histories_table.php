<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinic_credit_histories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('facility_id')->constrained('users')->cascadeOnDelete();
            $table->foreignId('supplier_id')->constrained('users')->cascadeOnDelete();
            $table->decimal('credit_amount', 10, 2)->nullable();
            $table->decimal('debit_amount', 10, 2)->nullable();
            $table->decimal('remaining_amount', 10, 2)->nullable();
            $table->decimal('edit_credit', 10, 2)->nullable();
            $table->decimal('order_credit_used', 10, 2)->nullable();
            $table->decimal('total_credit_amount', 10, 2)->nullable();
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->string('reference_value')->nullable();
            $table->string('action')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinic_credit_histories');
    }
};
