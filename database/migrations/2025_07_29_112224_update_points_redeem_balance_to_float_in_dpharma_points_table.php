<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('dpharma_points', function (Blueprint $table) {
            $table->decimal('points', 8, 2)->nullable()->change();
            $table->decimal('redeem', 8, 2)->nullable()->change();
            $table->decimal('balance', 8, 2)->nullable()->change();
        });
    }

    public function down()
    {
        Schema::table('dpharma_points', function (Blueprint $table) {
            $table->integer('points')->change();
            $table->integer('redeem')->change();
            $table->integer('balance')->change();
        });
    }
};
