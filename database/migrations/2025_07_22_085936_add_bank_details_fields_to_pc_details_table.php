<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            $table->string('beneficiary_name')->nullable()->after('delivery_days_west');
            $table->string('bank_name')->nullable()->after('beneficiary_name');
            $table->string('account_number')->nullable()->after('bank_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pc_details', function (Blueprint $table) {
            $table->dropColumn(['beneficiary_name', 'bank_name', 'account_number']);
        });
    }
};
