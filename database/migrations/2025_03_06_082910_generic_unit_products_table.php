<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('generic_name');
            // Modify columns to be nullable
            $table->unsignedBigInteger('sub_category_id')->nullable()->change();
            $table->unsignedBigInteger('approved_by')->nullable()->change();
            $table->unsignedBigInteger('unit_id')->nullable()->change();
            $table->string('quantity_per_unit')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('generic_name')->nullable();
            // Revert nullable changes
            /* $table->unsignedBigInteger('sub_category_id')->nullable(false)->change();
            $table->unsignedBigInteger('approved_by')->nullable(false)->change();
            $table->unsignedBigInteger('unit_id')->nullable(false)->change();
            $table->string('quantity_per_unit')->nullable(false)->change(); */
        });
    }
};
