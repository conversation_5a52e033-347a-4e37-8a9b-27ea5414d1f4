<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->text('reject_reason')->nullable()->after('is_open_account');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('clinic_pharma_suppliers', function (Blueprint $table) {
            $table->dropColumn('reject_reason');
        });
    }
};
