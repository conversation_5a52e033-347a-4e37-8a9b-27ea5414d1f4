<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove default and make nullable for all ENUM columns
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_fixed_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_fixed_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_fixed_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_fixed_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_1_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_1_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_2_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_2_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_3_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_tier_3_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_1_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_1_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_2_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_2_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_3_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_tier_3_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_1_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_1_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_2_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_2_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_3_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_bonus_3_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_1_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_1_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_2_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_2_cummission_type DROP NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_3_cummission_type DROP DEFAULT");
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_bonus_3_cummission_type DROP NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reapply default value and NOT NULL constraint
        DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_fixed_cummission_type SET DEFAULT 'flat'");
        //DB::statement("ALTER TABLE product_cummission ALTER COLUMN east_fixed_cummission_type SET NOT NULL");

        DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_fixed_cummission_type SET DEFAULT 'flat'");
        //DB::statement("ALTER TABLE product_cummission ALTER COLUMN west_fixed_cummission_type SET NOT NULL");
    }
};
