<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('new_status', ['pending', 'delivered','cancelled','in_transit'])->nullable();
        });

        DB::statement("UPDATE orders SET new_status = CAST(status AS TEXT)");

        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->renameColumn('new_status', 'status');
        });
    }

    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('old_status', ['pending', 'delivered','cancelled','in_transit'])->nullable();
        });

        // Revert status values
        DB::statement("UPDATE orders SET old_status = CAST(status AS TEXT) WHERE status IN ('pending', 'delivered','cancelled')");

        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('status');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->renameColumn('old_status', 'status');
        });
    }
};
