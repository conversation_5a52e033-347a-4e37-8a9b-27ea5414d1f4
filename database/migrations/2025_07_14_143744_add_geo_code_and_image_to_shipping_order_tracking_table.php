<?php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddGeoCodeAndImageToShippingOrderTrackingTable extends Migration
{
    public function up()
    {
        Schema::table('shipping_order_trackings', function (Blueprint $table) {
            $table->string('geo_code')->nullable()->after('shipping_response'); // replace 'existing_column' as needed
            $table->string('image')->nullable()->after('geo_code');
        });
    }

    public function down()
    {
        Schema::table('shipping_order_trackings', function (Blueprint $table) {
            $table->dropColumn(['geo_code', 'image']);
        });
    }
}

