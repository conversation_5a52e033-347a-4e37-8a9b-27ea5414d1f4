<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add composite indexes for dosage_foams table
        Schema::table('dosage_foams', function (Blueprint $table) {
            // Composite index for common query patterns (status + deleted_at)
            $table->index(['status', 'deleted_at'], 'idx_dosage_foams_status_deleted');
            
            // Composite index for name lookups with active records
            $table->index(['name', 'status', 'deleted_at'], 'idx_dosage_foams_name_status_deleted');
        });

        // Add partial index for active dosage forms only (PostgreSQL specific)
        // Note: CONCURRENTLY removed to work within transaction block
        DB::statement('CREATE INDEX IF NOT EXISTS idx_dosage_foams_active_records 
                       ON dosage_foams (id, name) 
                       WHERE status = true AND deleted_at IS NULL');

        // Add composite indexes for units table
        Schema::table('units', function (Blueprint $table) {
            // Composite index for common query patterns (status + deleted_at)
            $table->index(['status', 'deleted_at'], 'idx_units_status_deleted');
            
            // Composite index for name lookups with active records
            $table->index(['name', 'status', 'deleted_at'], 'idx_units_name_status_deleted');
        });

        // Add partial index for active units only (PostgreSQL specific)
        // Note: CONCURRENTLY removed to work within transaction block
        DB::statement('CREATE INDEX IF NOT EXISTS idx_units_active_records 
                       ON units (id, name) 
                       WHERE status = true AND deleted_at IS NULL');

        // Add foreign key indexes for better join performance
        Schema::table('products', function (Blueprint $table) {
            // Index for dosage_foams_id if not already exists
            if (!$this->indexExists('products', 'products_dosage_foams_id_foreign')) {
                $table->index('dosage_foams_id', 'idx_products_dosage_foams_id');
            }
            
            // Index for unit_id if not already exists
            if (!$this->indexExists('products', 'products_unit_id_foreign')) {
                $table->index('unit_id', 'idx_products_unit_id');
            }
            
            // Composite index for common product lookups
            $table->index(['status', 'deleted_at'], 'idx_products_status_deleted');
        });

        // Add foreign key indexes for products_relation table
        Schema::table('products_relation', function (Blueprint $table) {
            // Index for dosage_foams_id if not already exists
            if (!$this->indexExists('products_relation', 'products_relation_dosage_foams_id_foreign')) {
                $table->index('dosage_foams_id', 'idx_products_relation_dosage_foams_id');
            }
            
            // Index for unit_id if not already exists
            if (!$this->indexExists('products_relation', 'products_relation_unit_id_foreign')) {
                $table->index('unit_id', 'idx_products_relation_unit_id');
            }
            
            // Composite index for user-specific product lookups
            $table->index(['user_id', 'product_id'], 'idx_products_relation_user_product');
            
            // Index for admin approval filtering
            $table->index(['admin_approval', 'pc_approval'], 'idx_products_relation_approvals');
        });

        // Add indexes for frequently queried columns in product_relation_stocks
        Schema::table('product_relation_stocks', function (Blueprint $table) {
            // Index for stock type filtering
            $table->index('stock_type', 'idx_product_relation_stocks_stock_type');
            
            // Index for batch-wise stock filtering
            $table->index('is_batch_wise_stock', 'idx_product_relation_stocks_batch_wise');
            
            // Composite index for stock and expiry date queries
            $table->index(['stock', 'expiry_date'], 'idx_product_relation_stocks_stock_expiry');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop PostgreSQL specific partial indexes
        DB::statement('DROP INDEX IF EXISTS idx_dosage_foams_active_records');
        DB::statement('DROP INDEX IF EXISTS idx_units_active_records');

        // Drop indexes from dosage_foams table
        Schema::table('dosage_foams', function (Blueprint $table) {
            $table->dropIndex('idx_dosage_foams_status_deleted');
            $table->dropIndex('idx_dosage_foams_name_status_deleted');
        });

        // Drop indexes from units table
        Schema::table('units', function (Blueprint $table) {
            $table->dropIndex('idx_units_status_deleted');
            $table->dropIndex('idx_units_name_status_deleted');
        });

        // Drop indexes from products table
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_dosage_foams_id');
            $table->dropIndex('idx_products_unit_id');
            $table->dropIndex('idx_products_status_deleted');
        });

        // Drop indexes from products_relation table
        Schema::table('products_relation', function (Blueprint $table) {
            $table->dropIndex('idx_products_relation_dosage_foams_id');
            $table->dropIndex('idx_products_relation_unit_id');
            $table->dropIndex('idx_products_relation_user_product');
            $table->dropIndex('idx_products_relation_approvals');
        });

        // Drop indexes from product_relation_stocks table
        Schema::table('product_relation_stocks', function (Blueprint $table) {
            $table->dropIndex('idx_product_relation_stocks_stock_type');
            $table->dropIndex('idx_product_relation_stocks_batch_wise');
            $table->dropIndex('idx_product_relation_stocks_stock_expiry');
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = ? AND indexname = ?
        ", [$table, $index]);

        return count($indexes) > 0;
    }
}; 