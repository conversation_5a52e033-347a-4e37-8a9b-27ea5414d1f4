<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
{
    Schema::table('transactions', function (Blueprint $table) {
        $table->enum('order_status', ['cancelled', 'approved', 'rejected'])->nullable();
        $table->unsignedBigInteger('sub_orders_id')->nullable(); // nullable on the column
        $table->foreign('sub_orders_id')->references('id')->on('sub_orders');
    });

    Schema::table('sub_orders', function (Blueprint $table) {
        $table->unsignedBigInteger('rejected_by')->nullable(); // nullable on the column
        $table->foreign('rejected_by')->references('id')->on('users');
    });
}

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropForeign(['sub_orders_id']);
            $table->dropColumn(['order_status', 'sub_orders_id']);
        });

        Schema::table('sub_orders', function (Blueprint $table) {
            $table->dropForeign(['rejected_by']);
            $table->dropColumn(['rejected_by']);
        });
    }

};
