<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Helper function to check if index exists
        $indexExists = function($table, $indexName) {
            $exists = DB::select("SELECT 1 FROM pg_indexes WHERE indexname = ?", [$indexName]);
            return !empty($exists);
        };

        // Add indexes for products_relation table
        if (Schema::hasTable('products_relation')) {
            Schema::table('products_relation', function (Blueprint $table) use ($indexExists) {
                // Composite index for product_id and user_id (most common query pattern)
                if (!$indexExists('products_relation', 'idx_products_relation_product_user')) {
                    $table->index(['product_id', 'user_id'], 'idx_products_relation_product_user');
                }
                
                // Index for user_id (for user-specific queries)
                if (!$indexExists('products_relation', 'idx_products_relation_user')) {
                    $table->index(['user_id'], 'idx_products_relation_user');
                }
                
                // Index for product_id (for product-specific queries)
                if (!$indexExists('products_relation', 'idx_products_relation_product')) {
                    $table->index(['product_id'], 'idx_products_relation_product');
                }
                
                // Index for deleted_at (for soft deletes)
                if (!$indexExists('products_relation', 'idx_products_relation_deleted_at')) {
                    $table->index(['deleted_at'], 'idx_products_relation_deleted_at');
                }
                
                // Composite index for user_id and deleted_at (for notAdded scope)
                if (!$indexExists('products_relation', 'idx_products_relation_user_deleted')) {
                    $table->index(['user_id', 'deleted_at'], 'idx_products_relation_user_deleted');
                }
            });
        }

        // Add indexes for product_relation_stocks table
        if (Schema::hasTable('product_relation_stocks')) {
            Schema::table('product_relation_stocks', function (Blueprint $table) use ($indexExists) {
                // Index for product_relation_id (most common query pattern)
                if (!$indexExists('product_relation_stocks', 'idx_product_relation_stocks_relation_id')) {
                    $table->index(['product_relation_id'], 'idx_product_relation_stocks_relation_id');
                }
                
                // Index for stock-related queries
                if (!$indexExists('product_relation_stocks', 'idx_product_relation_stocks_stock')) {
                    $table->index(['stock'], 'idx_product_relation_stocks_stock');
                }
                
                // Index for batch-wise stock queries
                if (!$indexExists('product_relation_stocks', 'idx_product_relation_stocks_batch_wise')) {
                    $table->index(['is_batch_wise_stock'], 'idx_product_relation_stocks_batch_wise');
                }
                
                // Index for stock type queries
                if (!$indexExists('product_relation_stocks', 'idx_product_relation_stocks_stock_type')) {
                    $table->index(['stock_type'], 'idx_product_relation_stocks_stock_type');
                }
            });
        }

        // Add indexes for product_relation_prices table
        if (Schema::hasTable('product_relation_prices')) {
            Schema::table('product_relation_prices', function (Blueprint $table) use ($indexExists) {
                // Index for product_relation_id (most common query pattern)
                if (!$indexExists('product_relation_prices', 'idx_product_relation_prices_relation_id')) {
                    $table->index(['product_relation_id'], 'idx_product_relation_prices_relation_id');
                }
            });
        }

        // Add indexes for products table to improve notAdded scope performance
        if (Schema::hasTable('products')) {
            Schema::table('products', function (Blueprint $table) use ($indexExists) {
                // Composite index for status and deleted_at
                if (!$indexExists('products', 'idx_products_status_deleted')) {
                    $table->index(['status', 'deleted_at'], 'idx_products_status_deleted');
                }
                
                // Index for similarity searches on name
                if (!$indexExists('products', 'idx_products_name')) {
                    $table->index(['name'], 'idx_products_name');
                }
            });
        }

        // Add indexes for products_batch table
        if (Schema::hasTable('products_batch')) {
            Schema::table('products_batch', function (Blueprint $table) use ($indexExists) {
                // Composite index for product_id and user_id
                if (!$indexExists('products_batch', 'idx_products_batch_product_user')) {
                    $table->index(['product_id', 'user_id'], 'idx_products_batch_product_user');
                }
                
                // Index for user_id
                if (!$indexExists('products_batch', 'idx_products_batch_user')) {
                    $table->index(['user_id'], 'idx_products_batch_user');
                }
                
                // Index for product_relation_id
                if (!$indexExists('products_batch', 'idx_products_batch_relation_id')) {
                    $table->index(['products_relation_id'], 'idx_products_batch_relation_id');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Helper function to check if index exists
        $indexExists = function($table, $indexName) {
            $exists = DB::select("SELECT 1 FROM pg_indexes WHERE indexname = ?", [$indexName]);
            return !empty($exists);
        };

        // Drop indexes from products_relation table
        if (Schema::hasTable('products_relation')) {
            Schema::table('products_relation', function (Blueprint $table) use ($indexExists) {
                if ($indexExists('products_relation', 'idx_products_relation_product_user')) {
                    $table->dropIndex('idx_products_relation_product_user');
                }
                if ($indexExists('products_relation', 'idx_products_relation_user')) {
                    $table->dropIndex('idx_products_relation_user');
                }
                if ($indexExists('products_relation', 'idx_products_relation_product')) {
                    $table->dropIndex('idx_products_relation_product');
                }
                if ($indexExists('products_relation', 'idx_products_relation_deleted_at')) {
                    $table->dropIndex('idx_products_relation_deleted_at');
                }
                if ($indexExists('products_relation', 'idx_products_relation_user_deleted')) {
                    $table->dropIndex('idx_products_relation_user_deleted');
                }
            });
        }

        // Drop indexes from product_relation_stocks table
        if (Schema::hasTable('product_relation_stocks')) {
            Schema::table('product_relation_stocks', function (Blueprint $table) use ($indexExists) {
                if ($indexExists('product_relation_stocks', 'idx_product_relation_stocks_relation_id')) {
                    $table->dropIndex('idx_product_relation_stocks_relation_id');
                }
                if ($indexExists('product_relation_stocks', 'idx_product_relation_stocks_stock')) {
                    $table->dropIndex('idx_product_relation_stocks_stock');
                }
                if ($indexExists('product_relation_stocks', 'idx_product_relation_stocks_batch_wise')) {
                    $table->dropIndex('idx_product_relation_stocks_batch_wise');
                }
                if ($indexExists('product_relation_stocks', 'idx_product_relation_stocks_stock_type')) {
                    $table->dropIndex('idx_product_relation_stocks_stock_type');
                }
            });
        }

        // Drop indexes from product_relation_prices table
        if (Schema::hasTable('product_relation_prices')) {
            Schema::table('product_relation_prices', function (Blueprint $table) use ($indexExists) {
                if ($indexExists('product_relation_prices', 'idx_product_relation_prices_relation_id')) {
                    $table->dropIndex('idx_product_relation_prices_relation_id');
                }
            });
        }

        // Drop indexes from products table
        if (Schema::hasTable('products')) {
            Schema::table('products', function (Blueprint $table) use ($indexExists) {
                if ($indexExists('products', 'idx_products_status_deleted')) {
                    $table->dropIndex('idx_products_status_deleted');
                }
                if ($indexExists('products', 'idx_products_name')) {
                    $table->dropIndex('idx_products_name');
                }
            });
        }

        // Drop indexes from products_batch table
        if (Schema::hasTable('products_batch')) {
            Schema::table('products_batch', function (Blueprint $table) use ($indexExists) {
                if ($indexExists('products_batch', 'idx_products_batch_product_user')) {
                    $table->dropIndex('idx_products_batch_product_user');
                }
                if ($indexExists('products_batch', 'idx_products_batch_user')) {
                    $table->dropIndex('idx_products_batch_user');
                }
                if ($indexExists('products_batch', 'idx_products_batch_relation_id')) {
                    $table->dropIndex('idx_products_batch_relation_id');
                }
            });
        }
    }
};
