<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
             $table->string('merchant_ecommerce_tran_id')->nullable();
            $table->string('merchant_ecommerce_status')->nullable();
            $table->string('payout_merchant_failed_reason')->nullable();
            $table->enum('mechant_payment_type',['online','manual'])->nullable();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payouts', function (Blueprint $table) {
            $table->dropColumn([
                'merchant_ecommerce_tran_id',
                'merchant_ecommerce_status',
                'payout_merchant_failed_reason',
                'mechant_payment_type',
            ]);

            //
        });
    }
};
