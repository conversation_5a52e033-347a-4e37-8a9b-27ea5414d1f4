<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->string('type')->default('facility');
            $table->unsignedBigInteger('order_id')->nullable()->change();
            $table->unsignedBigInteger('sender_id')->nullable()->change();

            $table->unsignedBigInteger('payout_id')->nullable();
            $table->foreign('payout_id')->references('id')->on('payouts')->onDelete('cascade');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
       Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn(['payout_id','type']);
        });
    }
};
