<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_cummission', function (Blueprint $table) {
            $table->foreignId('product_relation_id')->constrained('products_relation')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_cummission', function (Blueprint $table) {
            $table->dropForeign(['product_relation_id']);
            $table->dropColumn(['product_relation_id']);
        });
    }
};
