<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. ROLES TABLE - Optimize role lookups like: 
        // select * from "roles" where "name" = 'Pharmaceutical Company' and "guard_name" = 'web' limit 1
        Schema::table('roles', function (Blueprint $table) {
            // Composite index for name + guard_name lookups (most frequent query)
            if (!$this->indexExists('roles', 'idx_roles_name_guard')) {
                $table->index(['name', 'guard_name'], 'idx_roles_name_guard');
            }
            
            // Index on guard_name for guard-based filtering
            if (!$this->indexExists('roles', 'idx_roles_guard_name')) {
                $table->index('guard_name', 'idx_roles_guard_name');
            }
        });

        // 2. STATES TABLE - Optimize state lookups and country-based queries
        Schema::table('states', function (Blueprint $table) {
            // Index for country-based state queries (LocationCacheService::getCachedStatesByCountry)
            if (!$this->indexExists('states', 'idx_states_country_id')) {
                $table->index('country_id', 'idx_states_country_id');
            }
            
            // Composite index for country + name for dropdown queries
            if (!$this->indexExists('states', 'idx_states_country_name')) {
                $table->index(['country_id', 'name'], 'idx_states_country_name');
            }
        });

        // 3. CITIES TABLE - Optimize city lookups and state-based queries  
        Schema::table('cities', function (Blueprint $table) {
            // Index for state-based city queries (LocationCacheService::getCachedCitiesByState)
            if (!$this->indexExists('cities', 'idx_cities_state_id')) {
                $table->index('state_id', 'idx_cities_state_id');
            }
            
            // Composite index for state + name for dropdown queries
            if (!$this->indexExists('cities', 'idx_cities_state_name')) {
                $table->index(['state_id', 'name'], 'idx_cities_state_name');
            }
            
            // Index for landline_code lookups (used in phone forms)
            if (!$this->indexExists('cities', 'idx_cities_landline_code')) {
                $table->index('landline_code', 'idx_cities_landline_code');
            }
        });

        // 4. USER_ADDRESSES TABLE - Optimize complex address queries with role filtering
        Schema::table('user_addresses', function (Blueprint $table) {
            // Index for user relationship lookups
            if (!$this->indexExists('user_addresses', 'idx_user_addresses_user_id')) {
                $table->index('user_id', 'idx_user_addresses_user_id');
            }
            
            // Index for state relationship lookups
            if (!$this->indexExists('user_addresses', 'idx_user_addresses_state_id')) {
                $table->index('state_id', 'idx_user_addresses_state_id');
            }
            
            // Index for city relationship lookups
            if (!$this->indexExists('user_addresses', 'idx_user_addresses_city_id')) {
                $table->index('city_id', 'idx_user_addresses_city_id');
            }
            
            // Index for address type filtering
            if (!$this->indexExists('user_addresses', 'idx_user_addresses_type')) {
                $table->index('address_type', 'idx_user_addresses_type');
            }
            
            // Composite index for user + address type queries
            if (!$this->indexExists('user_addresses', 'idx_user_addresses_user_type')) {
                $table->index(['user_id', 'address_type'], 'idx_user_addresses_user_type');
            }
        });

        // 5. USERS TABLE - Optimize user queries with role filtering
        Schema::table('users', function (Blueprint $table) {
            // Index for soft delete filtering (deleted_at already indexed, but ensure it exists)
            if (!$this->indexExists('users', 'users_deleted_at_index')) {
                $table->index('deleted_at', 'idx_users_deleted_at');
            }
            
            // Index for created_by relationship
            if (!$this->indexExists('users', 'idx_users_created_by')) {
                $table->index('created_by', 'idx_users_created_by');
            }
            
            // Index for verification status filtering
            if (!$this->indexExists('users', 'idx_users_verification_status')) {
                $table->index('verification_status', 'idx_users_verification_status');
            }
            
            // Index for active status filtering
            if (!$this->indexExists('users', 'idx_users_is_active')) {
                $table->index('is_active', 'idx_users_is_active');
            }
            
            // Composite index for common filtering patterns
            if (!$this->indexExists('users', 'idx_users_active_deleted')) {
                $table->index(['is_active', 'deleted_at'], 'idx_users_active_deleted');
            }
        });

        // 6. MODEL_HAS_ROLES TABLE (Spatie Permission) - Optimize role-based user queries
        Schema::table('model_has_roles', function (Blueprint $table) {
            // Composite index for the complex role queries
            // This optimizes: exists (select * from "roles" inner join "model_has_roles"...)
            if (!$this->indexExists('model_has_roles', 'idx_model_roles_lookup')) {
                $table->index(['model_id', 'model_type', 'role_id'], 'idx_model_roles_lookup');
            }
            
            // Index for role-based filtering
            if (!$this->indexExists('model_has_roles', 'idx_model_roles_role_id')) {
                $table->index('role_id', 'idx_model_roles_role_id');
            }
            
            // Index for model type filtering
            if (!$this->indexExists('model_has_roles', 'idx_model_roles_model_type')) {
                $table->index('model_type', 'idx_model_roles_model_type');
            }
        });

        // 7. PC_DETAILS TABLE - Optimize PC-specific queries
        if (Schema::hasTable('pc_details')) {
            Schema::table('pc_details', function (Blueprint $table) {
                // Index for user relationship
                if (!$this->indexExists('pc_details', 'idx_pc_details_user_id')) {
                    $table->index('user_id', 'idx_pc_details_user_id');
                }
                
                // Index for submission status filtering
                if (!$this->indexExists('pc_details', 'idx_pc_details_is_submitted')) {
                    $table->index('is_submitted', 'idx_pc_details_is_submitted');
                }
                
                // Index for restriction status filtering
                if (!$this->indexExists('pc_details', 'idx_pc_details_is_restricted')) {
                    $table->index('is_restricted', 'idx_pc_details_is_restricted');
                }
                
                // Composite index for common filtering patterns
                if (!$this->indexExists('pc_details', 'idx_pc_details_status_combo')) {
                    $table->index(['is_submitted', 'is_restricted'], 'idx_pc_details_status_combo');
                }
            });
        }

        // 8. Add PostgreSQL-specific optimizations for complex queries
        if (DB::getDriverName() === 'pgsql') {
            // Partial index for active users only
            DB::statement('CREATE INDEX IF NOT EXISTS idx_users_active_only 
                           ON users (id, email, name) 
                           WHERE is_active = true AND deleted_at IS NULL');
            
            // Partial index for App\Models\User model type (without subquery)
            DB::statement('CREATE INDEX IF NOT EXISTS idx_model_has_roles_app_user 
                           ON model_has_roles (model_id, role_id) 
                           WHERE model_type = \'App\\Models\\User\'');
            
            // Note: Pharmaceutical Company specific index will be created via concurrent script
            // where we can dynamically get the role_id
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop PostgreSQL-specific indexes
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('DROP INDEX IF EXISTS idx_users_active_only');
            DB::statement('DROP INDEX IF EXISTS idx_model_has_roles_app_user');
        }

        // Drop pc_details indexes
        if (Schema::hasTable('pc_details')) {
            Schema::table('pc_details', function (Blueprint $table) {
                $table->dropIndex('idx_pc_details_user_id');
                $table->dropIndex('idx_pc_details_is_submitted');
                $table->dropIndex('idx_pc_details_is_restricted');
                $table->dropIndex('idx_pc_details_status_combo');
            });
        }

        // Drop model_has_roles indexes
        Schema::table('model_has_roles', function (Blueprint $table) {
            $table->dropIndex('idx_model_roles_lookup');
            $table->dropIndex('idx_model_roles_role_id');
            $table->dropIndex('idx_model_roles_model_type');
        });

        // Drop users indexes
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_deleted_at');
            $table->dropIndex('idx_users_created_by');
            $table->dropIndex('idx_users_verification_status');
            $table->dropIndex('idx_users_is_active');
            $table->dropIndex('idx_users_active_deleted');
        });

        // Drop user_addresses indexes
        Schema::table('user_addresses', function (Blueprint $table) {
            $table->dropIndex('idx_user_addresses_user_id');
            $table->dropIndex('idx_user_addresses_state_id');
            $table->dropIndex('idx_user_addresses_city_id');
            $table->dropIndex('idx_user_addresses_type');
            $table->dropIndex('idx_user_addresses_user_type');
        });

        // Drop cities indexes
        Schema::table('cities', function (Blueprint $table) {
            $table->dropIndex('idx_cities_state_id');
            $table->dropIndex('idx_cities_state_name');
            $table->dropIndex('idx_cities_landline_code');
        });

        // Drop states indexes
        Schema::table('states', function (Blueprint $table) {
            $table->dropIndex('idx_states_country_id');
            $table->dropIndex('idx_states_country_name');
        });

        // Drop roles indexes
        Schema::table('roles', function (Blueprint $table) {
            $table->dropIndex('idx_roles_name_guard');
            $table->dropIndex('idx_roles_guard_name');
        });
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $index): bool
    {
        if (DB::getDriverName() === 'pgsql') {
            $indexes = DB::select("
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = ? AND indexname = ?
            ", [$table, $index]);
            
            return count($indexes) > 0;
        }
        
        // For MySQL
        $indexes = DB::select("
            SHOW INDEX FROM `{$table}` 
            WHERE Key_name = ?
        ", [$index]);
        
        return count($indexes) > 0;
    }
}; 