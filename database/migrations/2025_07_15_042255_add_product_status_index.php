<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add index for status column for faster tab filtering
            $table->index('status', 'products_status_index');
            
            // Add composite index for common queries
            $table->index(['status', 'deleted_at'], 'products_status_deleted_index');
        });
    }

    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('products_status_index');
            $table->dropIndex('products_status_deleted_index');
        });
    }
};
