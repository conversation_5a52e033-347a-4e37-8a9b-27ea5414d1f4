<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
           DB::statement('ALTER TABLE users DROP CONSTRAINT IF EXISTS users_email_unique');

        // Add a partial unique index on email (only for active users)
        DB::statement('
            CREATE UNIQUE INDEX users_email_unique_active
            ON users (email)
            WHERE deleted_at IS NULL
        ');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            DB::statement('DROP INDEX IF EXISTS users_email_unique_active');

            // Recreate the original unique index on the email column
            DB::statement('
            CREATE UNIQUE INDEX users_email_unique 
            ON users (email)
        ');
        });
    }
};
