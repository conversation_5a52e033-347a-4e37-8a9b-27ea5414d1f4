<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_order_trackings', function (Blueprint $table) {
            $table->json('package_detail_json')->nullable()->after('image'); // replace 'some_existing_column' as needed
            $table->decimal('total_charge', 8, 2)->nullable()->after('package_detail_json');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_order_trackings', function (Blueprint $table) {
            $table->dropColumn('package_detail_json', 'total_charge');
        });
    }
};
