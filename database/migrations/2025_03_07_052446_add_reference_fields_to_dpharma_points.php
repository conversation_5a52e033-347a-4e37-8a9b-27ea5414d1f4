<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dpharma_points', function (Blueprint $table) {
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->string('reference_value')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dpharma_points', function (Blueprint $table) {
            $table->dropColumn(['reference_id', 'reference_value']);
        });
    }
};
