<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for performance optimization.
     */
    public function up(): void
    {
        // Add composite indexes for approvals table to speed up pending approval queries
        try {
            Schema::table('approvals', function (Blueprint $table) {
                // Index for the main approval query pattern
                $table->index([
                    'approvalable_id', 
                    'approvalable_type', 
                    'user_type', 
                    'steps',
                    'approved_by',
                    'approved_at',
                    'rejected_by',
                    'rejected_at'
                ], 'idx_approvals_pending_lookup');
                
                // Separate index for existence checks
                $table->index([
                    'approvalable_id',
                    'user_type', 
                    'steps',
                    'approved_by',
                    'rejected_by'
                ], 'idx_approvals_exists_check');
                
                // Index for steps filtering
                $table->index(['steps', 'user_type'], 'idx_approvals_steps_usertype');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for warehouse relationships  
        try {
            Schema::table('ware_houses', function (Blueprint $table) {
                $table->index('user_id', 'idx_warehouses_user_id');
                $table->index(['city_id', 'state_id'], 'idx_warehouses_city_state');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for user addresses
        try {
            Schema::table('user_addresses', function (Blueprint $table) {
                $table->index('user_id', 'idx_user_addresses_user_id');
                $table->index(['city_id', 'state_id'], 'idx_user_addresses_city_state');
                $table->index(['address_type', 'status'], 'idx_user_addresses_type_status');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for pc_details table
        try {
            Schema::table('pc_details', function (Blueprint $table) {
                $table->index('user_id', 'idx_pc_details_user_id');
                $table->index(['is_submitted', 'is_restricted', 'is_version_pending'], 'idx_pc_details_status_flags');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for cities and states tables if they don't exist
        try {
            Schema::table('cities', function (Blueprint $table) {
                $table->index('state_id', 'idx_cities_state_id');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        try {
            Schema::table('states', function (Blueprint $table) {
                $table->index('country_id', 'idx_states_country_id');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for users table performance
        try {
            Schema::table('users', function (Blueprint $table) {
                $table->index('verification_status', 'idx_users_verification_status');
                $table->index(['is_active', 'created_at'], 'idx_users_active_created');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }

        // Add indexes for pc_certificate_files
        try {
            Schema::table('pc_certificate_files', function (Blueprint $table) {
                $table->index(['user_id', 'type', 'status'], 'idx_pc_certificate_user_type_status');
            });
        } catch (\Exception $e) {
            // Index might already exist, continue
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        try {
            Schema::table('approvals', function (Blueprint $table) {
                $table->dropIndex('idx_approvals_pending_lookup');
                $table->dropIndex('idx_approvals_exists_check');
                $table->dropIndex('idx_approvals_steps_usertype');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('ware_houses', function (Blueprint $table) {
                $table->dropIndex('idx_warehouses_user_id');
                $table->dropIndex('idx_warehouses_city_state');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('user_addresses', function (Blueprint $table) {
                $table->dropIndex('idx_user_addresses_user_id');
                $table->dropIndex('idx_user_addresses_city_state');
                $table->dropIndex('idx_user_addresses_type_status');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('pc_details', function (Blueprint $table) {
                $table->dropIndex('idx_pc_details_user_id');
                $table->dropIndex('idx_pc_details_status_flags');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('cities', function (Blueprint $table) {
                $table->dropIndex('idx_cities_state_id');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('states', function (Blueprint $table) {
                $table->dropIndex('idx_states_country_id');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('users', function (Blueprint $table) {
                $table->dropIndex('idx_users_verification_status');
                $table->dropIndex('idx_users_active_created');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }

        try {
            Schema::table('pc_certificate_files', function (Blueprint $table) {
                $table->dropIndex('idx_pc_certificate_user_type_status');
            });
        } catch (\Exception $e) {
            // Index might not exist, continue
        }
    }


}; 