<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    public function up(): void
    {
        DB::statement('ALTER TABLE orders DROP CONSTRAINT IF EXISTS orders_new_status_check1');
    }

    public function down(): void
    {
        // Re-add the constraint here if needed
        // For example:
        // DB::statement("ALTER TABLE orders ADD CONSTRAINT orders_new_status_check1 CHECK (new_status IN ('pending', 'approved', 'delivered'))");
    }
};
