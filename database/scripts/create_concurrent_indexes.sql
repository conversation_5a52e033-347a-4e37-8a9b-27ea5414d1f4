-- PostgreSQL Concurrent Index Creation Script
-- Run this manually for zero-downtime index creation in production
-- 
-- IMPORTANT: 
-- 1. Run this outside of Laravel migrations
-- 2. Ensure no transactions are active
-- 3. Monitor progress with: SELECT * FROM pg_stat_progress_create_index;

-- ============================================================================
-- PARTIAL INDEXES FOR ACTIVE RECORDS (CONCURRENT)
-- ============================================================================

-- Create concurrent partial index for active dosage forms
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dosage_foams_active_records_concurrent 
ON dosage_foams (id, name) 
WHERE status = true AND deleted_at IS NULL;

-- Create concurrent partial index for active units
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_units_active_records_concurrent 
ON units (id, name) 
WHERE status = true AND deleted_at IS NULL;

-- ============================================================================
-- ADDITIONAL CONCURRENT INDEXES (OPTIONAL)
-- ============================================================================

-- Concurrent composite index for dosage forms status filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dosage_foams_status_deleted_concurrent 
ON dosage_foams (status, deleted_at);

-- Concurrent composite index for units status filtering  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_units_status_deleted_concurrent 
ON units (status, deleted_at);

-- Concurrent index for product foreign keys
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_dosage_foams_id_concurrent 
ON products (dosage_foams_id) 
WHERE dosage_foams_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_unit_id_concurrent 
ON products (unit_id) 
WHERE unit_id IS NOT NULL;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check index creation progress
-- SELECT 
--     pid,
--     datname,
--     command,
--     phase,
--     tuples_total,
--     tuples_done,
--     partitions_total,
--     partitions_done
-- FROM pg_stat_progress_create_index;

-- Verify indexes were created
-- SELECT 
--     schemaname,
--     tablename,
--     indexname,
--     indexdef
-- FROM pg_indexes 
-- WHERE indexname LIKE '%_concurrent'
-- ORDER BY tablename, indexname;

-- Check index sizes
-- SELECT 
--     schemaname,
--     tablename,
--     indexname,
--     pg_size_pretty(pg_relation_size(indexname::regclass)) as size
-- FROM pg_indexes 
-- WHERE indexname LIKE '%_concurrent'
-- ORDER BY pg_relation_size(indexname::regclass) DESC;

-- ============================================================================
-- CLEANUP (if needed)
-- ============================================================================

-- To drop concurrent indexes if needed:
-- DROP INDEX CONCURRENTLY IF EXISTS idx_dosage_foams_active_records_concurrent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_units_active_records_concurrent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_dosage_foams_status_deleted_concurrent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_units_status_deleted_concurrent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_products_dosage_foams_id_concurrent;
-- DROP INDEX CONCURRENTLY IF EXISTS idx_products_unit_id_concurrent; 