-- Caching Optimization Indexes - Concurrent Creation Script
-- This script creates indexes CONCURRENTLY for zero-downtime deployment in production
-- Run this AFTER the migration has been executed

-- 1. ROLES TABLE INDEXES
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_name_guard_concurrent 
ON roles (name, guard_name);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_roles_guard_name_concurrent 
ON roles (guard_name);

-- 2. STATES TABLE INDEXES  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_states_country_id_concurrent 
ON states (country_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_states_country_name_concurrent 
ON states (country_id, name);

-- 3. CITIES TABLE INDEXES
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cities_state_id_concurrent 
ON cities (state_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cities_state_name_concurrent 
ON cities (state_id, name);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cities_landline_code_concurrent 
ON cities (landline_code);

-- 4. USER_ADDRESSES TABLE INDEXES
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_user_id_concurrent 
ON user_addresses (user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_state_id_concurrent 
ON user_addresses (state_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_city_id_concurrent 
ON user_addresses (city_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_type_concurrent 
ON user_addresses (address_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_user_type_concurrent 
ON user_addresses (user_id, address_type);

-- 5. USERS TABLE INDEXES
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_deleted_at_concurrent 
ON users (deleted_at);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_by_concurrent 
ON users (created_by);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_verification_status_concurrent 
ON users (verification_status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_is_active_concurrent 
ON users (is_active);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_deleted_concurrent 
ON users (is_active, deleted_at);

-- 6. MODEL_HAS_ROLES TABLE INDEXES (Critical for role-based queries)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_roles_lookup_concurrent 
ON model_has_roles (model_id, model_type, role_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_roles_role_id_concurrent 
ON model_has_roles (role_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_roles_model_type_concurrent 
ON model_has_roles (model_type);

-- 7. PC_DETAILS TABLE INDEXES (if table exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pc_details_user_id_concurrent 
ON pc_details (user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pc_details_is_submitted_concurrent 
ON pc_details (is_submitted);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pc_details_is_restricted_concurrent 
ON pc_details (is_restricted);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_pc_details_status_combo_concurrent 
ON pc_details (is_submitted, is_restricted);

-- 8. POSTGRESQL-SPECIFIC PARTIAL INDEXES (Most optimized for complex queries)
-- Partial index for active users only
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_active_only_concurrent 
ON users (id, email, name) 
WHERE is_active = true AND deleted_at IS NULL;

-- Note: Pharmaceutical Company specific index will be created dynamically by the console command
-- This avoids subquery issues in index predicates

-- Partial index for App\Models\User model type
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_has_roles_app_user_concurrent 
ON model_has_roles (model_id, role_id) 
WHERE model_type = 'App\Models\User';

-- 9. ADDITIONAL SPECIALIZED INDEXES FOR HEAVY QUERIES

-- Index for complex address filtering with multiple conditions
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_complex_filter_concurrent 
ON user_addresses (user_id, state_id, city_id, address_type) 
WHERE address_type IS NOT NULL;

-- Index for role-based filtering with active users
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_roles_active_users_concurrent 
ON model_has_roles (role_id, model_id) 
WHERE model_type = 'App\Models\User';

-- Index for location-based queries with names  
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_states_name_lookup_concurrent 
ON states (name, country_id, id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cities_name_lookup_concurrent 
ON cities (name, state_id, id);

-- After all indexes are created, analyze tables for better query planning
ANALYZE roles;
ANALYZE states;  
ANALYZE cities;
ANALYZE user_addresses;
ANALYZE users;
ANALYZE model_has_roles;
ANALYZE pc_details; 