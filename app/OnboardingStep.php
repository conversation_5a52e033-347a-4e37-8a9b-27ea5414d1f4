<?php

namespace App;

enum OnboardingStep: int
{
    case BASIC_INFO = 1;
    case ADDRESS = 2;
    case DOCTOR_INCHARGE = 3;
    case DOCUMENTS = 4;
    case CONTACT = 5;
    case PERSON_IN_CHARGE = 6;

    public function label(): string
    {
        return match($this) {
            self::BASIC_INFO => 'Basic Information',
            self::ADDRESS => 'Address Details',
            self::DOCTOR_INCHARGE => 'Doctor In-Charge',
            self::DOCUMENTS => 'Document Verification',
            self::CONTACT => 'Contact Details',
            self::PERSON_IN_CHARGE => 'Person In-Charge',
        };
    }

    public static function options(): array
    {
        return [
            self::BASIC_INFO->value => self::BASIC_INFO->label(),
            self::ADDRESS->value => self::ADDRESS->label(),
            self::DOCTOR_INCHARGE->value => self::DOCTOR_INCHARGE->label(),
            self::DOCUMENTS->value => self::DOCUMENTS->label(),
            self::CONTACT->value => self::CONTACT->label(),
            self::PERSON_IN_CHARGE->value => self::PERSON_IN_CHARGE->label(),
        ];
    }

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    // Helper method to get next step
    public function nextStep(): ?self
    {
        return match($this) {
            self::BASIC_INFO => self::ADDRESS,
            self::ADDRESS => self::DOCTOR_INCHARGE,
            self::DOCTOR_INCHARGE => self::DOCUMENTS,
            self::DOCUMENTS => null,
            self::CONTACT => null,
            self::PERSON_IN_CHARGE => null
        };
    }
}
