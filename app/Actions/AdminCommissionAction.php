<?php

namespace App\Actions;

use Filament\Tables\Actions\Action;
use Filament\Forms\Components\TextInput;

class AdminCommissionAction
{
    public function make()
    {
        return Action::make('Edit Price')
            ->modalSubmitActionLabel('Save')
            ->iconButton()
            ->icon('heroicon-s-currency-dollar')
            ->tooltip('Update Price')
            ->extraAttributes(['style' => 'margin-right: 0.5px;'])
            ->outlined()
            ->size('xl')
            ->form(function ($record) {
                return [
                    TextInput::make('test'),
                ];
            });
    }
}
