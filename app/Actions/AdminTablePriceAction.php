<?php

namespace App\Actions;

use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\PcDetail;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Models\ProductRelationPrice;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Support\Enums\Alignment;
use App\Service\TierValidationService;
use Filament\Forms\Components\Section;
use App\Service\PriceManagementService;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Placeholder;
use Illuminate\Validation\ValidationException;
use Awcodes\TableRepeater\Components\TableRepeater;

class AdminTablePriceAction
{
    public static function make($finalCommission = null, $finalCommissionType = null, $product = null, $userId = null)
    {
        return Action::make('Edit Price')
            ->modalSubmitActionLabel('Save')
            ->iconButton()
            ->icon('heroicon-s-currency-dollar')
            ->tooltip('Update Price')
            ->extraAttributes(['style' => 'margin-right: 0.5px;'])
            ->outlined()
            ->size('xl')
            ->form(function ($record) use ($finalCommission, $finalCommissionType, $product, $userId) {
                // dd($record);
                $pcCommission = PcDetail::where('user_id', $record->id)->first();
                $finalCommissionType = $pcCommission?->commission_type ?: null;
                $finalCommission = $pcCommission?->commission_percentage ?: 0;
                $user = $record;
                $productData = $record->productDataForPc($userId);
                $productRelationData = ProductRelationPrice::where('product_relation_id', $productData?->id)->first();
                return [
                    Section::make()->schema([
                        Radio::make('price_type')
                            ->label('Price Type')
                            ->extraAttributes(['class' => 'rajen'])
                            ->inline()
                            ->live()
                            ->formatStateUsing(function ($record) use ($productData) {
                                return $productData?->price_type ?? 'fixed';
                            })
                            ->options(fn($record) => self::getEditPriceOptions($record)),
                    ]),
                    Section::make()
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'fixed';
                        })
                        ->heading('Fixed')
                        ->schema(fn($record) => self::getFixedPriceSchema($record, $finalCommission, $finalCommissionType, $userId)),
                    Group::make()
                        ->extraAttributes([
                            'style' => 'max-height: 600px; overflow-y: auto;'
                        ])
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'bonus';
                        })
                        ->schema([
                            Section::make()
                                ->heading('East Malaysia')
                                ->schema(fn($record) => self::getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)),
                            Section::make()
                                ->heading(heading: 'West Malaysia')
                                ->schema(fn($record) => self::getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)),
                        ]),

                    Group::make()
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'tier';
                        })
                        ->schema(fn($record): array => self::tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData)),
                ];
            })
            ->action(function ($data, $record, PriceManagementService $service) use ($userId) {

                if ($data['price_type'] == 'bonus') {
                    // Sequential validation for East bonus pricing
                    for ($i = 1; $i <= 3; $i++) {
                        $quantity = $data["east_bonus_{$i}_quantity"] ?? null;
                        $quantityValue = $data["east_bonus_{$i}_quantity_value"] ?? null;
                        
                        // Both quantity and quantity_value must be present together or both must be empty
                        if ((!empty($quantity) && empty($quantityValue)) || (empty($quantity) && !empty($quantityValue))) {
                            Notification::make()
                                ->title('Sequential Validation Error')
                                ->body("For East Bonus {$i}, both Quantity and Bonus Qty must be filled together or both must be empty.")
                                ->danger()
                                ->send();
                                
                            throw ValidationException::withMessages([
                                "east_bonus_{$i}_quantity" => ["Both quantity and bonus quantity must be provided together for East Bonus {$i}."],
                            ]);
                        }
                    }
                    
                    // Check sequential completion for East - Bonus 1 must be completed before Bonus 2, etc.
                    $eastBonus1Complete = !empty($data['east_bonus_1_quantity']) && !empty($data['east_bonus_1_quantity_value']);
                    $eastBonus2Complete = !empty($data['east_bonus_2_quantity']) && !empty($data['east_bonus_2_quantity_value']);
                    $eastBonus3Complete = !empty($data['east_bonus_3_quantity']) && !empty($data['east_bonus_3_quantity_value']);
                    
                    if ($eastBonus2Complete && !$eastBonus1Complete) {
                        Notification::make()
                            ->title('Sequential Validation Error')
                            ->body('Please complete East Bonus 1 before filling East Bonus 2.')
                            ->danger()
                            ->send();
                            
                        throw ValidationException::withMessages([
                            'east_bonus_1_quantity' => ['East Bonus 1 must be completed before Bonus 2.'],
                        ]);
                    }
                    
                    if ($eastBonus3Complete && !$eastBonus2Complete) {
                        Notification::make()
                            ->title('Sequential Validation Error')
                            ->body('Please complete East Bonus 2 before filling East Bonus 3.')
                            ->danger()
                            ->send();
                            
                        throw ValidationException::withMessages([
                            'east_bonus_2_quantity' => ['East Bonus 2 must be completed before Bonus 3.'],
                        ]);
                    }
                    
                    // Sequential validation for West bonus pricing
                    for ($i = 1; $i <= 3; $i++) {
                        $quantity = $data["west_bonus_{$i}_quantity"] ?? null;
                        $quantityValue = $data["west_bonus_{$i}_quantity_value"] ?? null;
                        
                        // Both quantity and quantity_value must be present together or both must be empty
                        if ((!empty($quantity) && empty($quantityValue)) || (empty($quantity) && !empty($quantityValue))) {
                            Notification::make()
                                ->title('Sequential Validation Error')
                                ->body("For West Bonus {$i}, both Quantity and Bonus Qty must be filled together or both must be empty.")
                                ->danger()
                                ->send();
                                
                            throw ValidationException::withMessages([
                                "west_bonus_{$i}_quantity" => ["Both quantity and bonus quantity must be provided together for West Bonus {$i}."],
                            ]);
                        }
                    }
                    
                    // Check sequential completion for West - Bonus 1 must be completed before Bonus 2, etc.
                    $westBonus1Complete = !empty($data['west_bonus_1_quantity']) && !empty($data['west_bonus_1_quantity_value']);
                    $westBonus2Complete = !empty($data['west_bonus_2_quantity']) && !empty($data['west_bonus_2_quantity_value']);
                    $westBonus3Complete = !empty($data['west_bonus_3_quantity']) && !empty($data['west_bonus_3_quantity_value']);
                    
                    if ($westBonus2Complete && !$westBonus1Complete) {
                        Notification::make()
                            ->title('Sequential Validation Error')
                            ->body('Please complete West Bonus 1 before filling West Bonus 2.')
                            ->danger()
                            ->send();
                            
                        throw ValidationException::withMessages([
                            'west_bonus_1_quantity' => ['West Bonus 1 must be completed before Bonus 2.'],
                        ]);
                    }
                    
                    if ($westBonus3Complete && !$westBonus2Complete) {
                        Notification::make()
                            ->title('Sequential Validation Error')
                            ->body('Please complete West Bonus 2 before filling West Bonus 3.')
                            ->danger()
                            ->send();
                            
                        throw ValidationException::withMessages([
                            'west_bonus_2_quantity' => ['West Bonus 2 must be completed before Bonus 3.'],
                        ]);
                    }

                    // East bonus quantity validation (existing code)
                    $bonusEastQuantity = [];
                    for ($i = 1; $i < 4; $i++) {
                        if (!empty($data["east_bonus_{$i}_quantity"])) {
                            $bonusEastQuantity[$i] = $data["east_bonus_{$i}_quantity"];
                        }
                    }

                    $eastIndexes = array_keys($bonusEastQuantity);
                    $expectedEast = range(1, count($bonusEastQuantity));

                    if ($eastIndexes !== $expectedEast) {
                        Notification::make()
                            ->body('Please enter east bonus quantities in sequential order (no gaps).')
                            ->danger()
                            ->send();

                        throw ValidationException::withMessages([
                            'east_bonus_quantity' => ['Please fill East quantities in order (no skipping).'],
                        ]);
                    }

                    $eastUnique = array_unique($bonusEastQuantity);
                    if (count($bonusEastQuantity) !== count($eastUnique)) {
                        Notification::make()
                            ->body('Please make sure that all east quantities are unique.')
                            ->danger()
                            ->send();

                        throw ValidationException::withMessages([
                            'east_bonus_quantity' => ['East quantities must be unique.'],
                        ]);
                    }

                    // West bonus quantity validation (existing code)
                    $bonusWestQuantity = [];
                    for ($i = 1; $i < 4; $i++) {
                        if (!empty($data["west_bonus_{$i}_quantity"])) {
                            $bonusWestQuantity[$i] = $data["west_bonus_{$i}_quantity"];
                        }
                    }

                    $westIndexes = array_keys($bonusWestQuantity);
                    $expectedWest = range(1, count($bonusWestQuantity));

                    if ($westIndexes !== $expectedWest) {
                        Notification::make()
                            ->body('Please enter west bonus quantities in sequential order (no gaps).')
                            ->danger()
                            ->send();

                        throw ValidationException::withMessages([
                            'west_bonus_quantity' => ['Please fill West quantities in order (no skipping).'],
                        ]);
                    }

                    $westUnique = array_unique($bonusWestQuantity);
                    if (count($bonusWestQuantity) !== count($westUnique)) {
                        Notification::make()
                            ->body('Please make sure that all west quantities are unique.')
                            ->danger()
                            ->send();

                        throw ValidationException::withMessages([
                            'west_bonus_quantity' => ['West quantities must be unique.'],
                        ]);
                    }
                }

                if ($data['price_type'] == 'tier') {
                    $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
                    $westTierPriceInfo = $data['pcInfo_west'] ?? [];

                    $lastIndexForEast = count($eastTierPriceInfo) - 1;
                    $lastIndexForWest = count($westTierPriceInfo) - 1;

                    foreach ($eastTierPriceInfo as $index => $row) {
                        if ($index !== $lastIndexForEast && empty($row['max_quantity'])) {
                            Notification::make()
                                ->title('Max quantity is required for all but the last row in East pricing.')
                                ->danger()
                                ->send();
                            throw ValidationException::withMessages([
                                'pcInfo_east.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in East pricing.',
                            ]);
                        }
                    }


                    foreach ($westTierPriceInfo as $index => $row) {
                        if ($index !== $lastIndexForWest && empty($row['max_quantity'])) {
                            Notification::make()
                                ->title('Max quantity is required for all but the last row in East pricing.')
                                ->danger()
                                ->send();
                            throw ValidationException::withMessages([
                                'pcInfo_west.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in West pricing.',
                            ]);
                        }
                    }
                }
                $relation = self::getPcData($record, $userId);
                $product = $relation->product;
                $service->store($data, $product, $relation->id, user: $record);
                Notification::make()->title('Product Price Updated')->success()->send();
            });
    }

    public static function getEditPriceOptions($record)
    {
        return [
            'fixed' => new HtmlString('Fixed &nbsp;
            <div x-data="{ tooltip: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip: true}" x-on:mouseleave="{tooltip: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip : true}">Sets a fixed price that does not vary with quantity or duration.</title>
            </svg>
            </div>
            '),

                'bonus' => new HtmlString('Bonus &nbsp;
            <div x-data="{ tooltip2: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip2: true}" x-on:mouseleave="{tooltip2: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip2 : true}">Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.</title>
            </svg>
            </div>
            '),

                'tier' => new HtmlString('Tier &nbsp;
            <div x-data="{ tooltip3: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip3: true}" x-on:mouseleave="{tooltip3: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip3 : true}">Applies different prices based on quantity purchased — more units may cost less per unit.</title>
            </svg>
            </div>
            '),
        ];
    }

    public static function getFixedPriceSchema($record, $finalCommission, $finalCommissionType, $userId)
    {
        return [
            Group::make()
                ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                ->schema([
                    Placeholder::make('Region'),
                    Placeholder::make('Price')->label('Price By'),
                    Placeholder::make('Admin Fees'),
                    Placeholder::make('Net Earnings'),
                ])->columns(4),
            Group::make()->schema([
                Placeholder::make('East Malaysia')
                    ->label("")
                    ->content("East Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('east_zone_price_1')
                    ->validationAttribute('east zone price')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric', 'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->formatStateUsing(function () use ($record, $userId) {
                        // dd($record, $userId);
                        return (ProductRelationPrice::where('product_relation_id', $record->productDataForPC($userId)?->id)->first()?->east_zone_price);
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {

                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('east_zone_price_1') / 100;
                            $earning = (float)$get('east_zone_price_1') - (float)$commission;

                            return 'RM ' . number_format($earning, 2);
                        } else {
                            $earning = (float)$get('east_zone_price_1') - (float)$finalCommission;
                            return 'RM ' . number_format($earning, 2);
                        }

                        return 'RM15';
                    }),

                Placeholder::make('West Malaysia')
                    ->label("")
                    ->content("West Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('west_zone_price_1')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric',  'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->validationMessages([
                        'gt' => 'The west zone price must be greater than 0.',
                    ])
                    ->validationAttribute('west zone price')
                    ->formatStateUsing(function () use ($record, $userId) {

                        return ProductRelationPrice::where('product_relation_id', $record->productDataForPc($userId)?->id)->first()?->west_zone_price;
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('west_zone_price_1') / 100;
                            $earning = (float)$get('west_zone_price_1') - (float)$commission;
                            return 'RM ' . number_format((float)$earning, 2);
                        } else {
                            $earning = (float)$get('west_zone_price_1') - $finalCommission;

                            return 'RM ' . number_format((float)$earning, 2);
                        }

                        return 'RM15';
                    }),
            ])->columns(4),
        ];
    }

    public static function getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()
                ->schema([
                    Group::make()
                        ->schema([
                            TextInput::make('east_zone_price_bonus')
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_zone_price;
                                })
                                ->rules(function (Get $get) {
                                    $priceType = $get('price_type');
                                    if ($priceType == 'bonus') {
                                        return ['required', 'numeric', 'gt:0', 'price'];
                                    }
                                    return ['numeric', 'price'];
                                })
                                ->placeholder('Enter Price')
                                ->validationAttribute('east bonus price')
                                ->calculateNetEarnings(
                                    commission: $finalCommission,
                                    commissionType: $finalCommissionType,
                                    fieldId: 'mountedTableActionsData.0.east_bonus_net_earnings',
                                    currentField: 'mountedTableActionsData.0.east_bonus_base_price',
                                )
                                ->prefix('RM')
                                ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_admin_fees')
                                ->label('Admin Fees')
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission;
                                    }

                                    return $finalCommission;
                                })
                                ->disabled()
                                ->prefix(function () use ($finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        return '%';
                                    }

                                    return 'RM';
                                }),
                            TextInput::make('east_bonus_net_earnings')
                                ->disabled()
                                ->prefix('RM')
                                ->reactive()
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData) {
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $productRelationData?->east_zone_price / 100;
                                        $earning = $productRelationData?->east_zone_price - $commission;
                                    } else {
                                        $earning = $productRelationData?->east_zone_price - $finalCommission;
                                    }

                                    return $earning;
                                })

                                ->label('Net Earnings'),
                        ])
                        ->columns(3),
                    Group::make()
                        ->columns(2)
                        ->schema([
                            TextInput::make('east_bonus_1_quantity')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity')
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', 'max:999999', 'integer', 'gt:0'];
                                    }
                                    return [];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity;
                                })
                                ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_1_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_1_quantity') ?: 0;
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', "max:$max", 'integer', 'gt:0'];
                                    }
                                    return ['nullable', "max:$max", 'integer', 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity_value;
                                })
                                ->label(new HtmlString('Bonus Qty. <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_2_quantity')
                                ->reactive()
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity')
                                ->rules(['max:999999', 'integer', 'gt:0'])
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_2_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_2_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                            TextInput::make('east_bonus_3_quantity')
                                ->validationAttribute('east bonus 3 quantity')
                                ->rules(['integer', 'max:999999', 'gt:0'])
                                ->numeric()
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_3_quantity_value')
                                ->validationAttribute('east bonus 3 quantity value')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_3_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                        ]),
                ]),

        ];
    }

    public static function getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()

                ->schema([
                    TextInput::make('west_zone_price_bonus')
                        ->placeholder('Enter Price')
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_zone_price;
                        })
                        ->validationAttribute('west zone price')
                        ->calculateNetEarnings(
                            commission: $finalCommission,
                            commissionType: $finalCommissionType,
                            fieldId: 'mountedTableActionsData.0.west_bonus_net_earnings',
                            currentField: 'mountedTableActionsData.0.west_bonus_base_price',
                        )
                        ->extraAttributes(fn() => self::numericValueValidationRule())
                        ->prefix('RM')
                        ->rules(function (Get $get) {
                            if ($get('price_type') === 'bonus') {
                                return ['required', 'numeric', 'gt:0', 'price'];
                            }
                            return ['nullable', 'numeric', 'gt:0', 'price'];
                        })
                        ->validationAttribute('west zone bonus price')
                        ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_admin_fees')
                        ->label('Admin Fees')
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return $finalCommission;
                            }

                            return $finalCommission;
                        })
                        ->disabled()
                        ->prefix(function () use ($finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return '%';
                            }

                            return 'RM';
                        }),
                    TextInput::make('west_bonus_net_earnings')
                        ->disabled()
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData) {
                            if ($finalCommissionType == 'percentage') {
                                $commission = $finalCommission * $productRelationData?->west_zone_price / 100;
                                $earning = $productRelationData?->west_zone_price - $commission;
                            } else {
                                $earning = $productRelationData?->west_zone_price - $finalCommission;
                            }

                            return $earning;
                        })
                        ->prefix('RM')
                        ->reactive()
                        ->label('Net Earnings'),
                ])
                ->columns(3),
            Group::make()
                ->columns(2)
                ->schema([
                    TextInput::make('west_bonus_1_quantity')
                        ->validationAttribute('west bonus 1 quantity')
                        ->numeric()
                        ->rules(['required', 'max:999999', 'integer', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity;
                        })
                        ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_1_quantity_value')
                        ->validationAttribute('west bonus 1 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_1_quantity') ?: 0;
                            return ['required', 'integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity_value;
                        })
                        ->label(new HtmlString('Quantity Qty. <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_2_quantity')
                        ->validationAttribute('west bonus 2 quantity')
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_2_quantity_value')
                        ->validationAttribute('west bonus 2 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_2_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                    TextInput::make('west_bonus_3_quantity')
                        ->validationAttribute('west bonus 3 quantity')
                        ->numeric()
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_3_quantity_value')
                        ->validationAttribute('west bonus 3 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_3_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                ]),
        ];
    }

    public static function tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()
                // ->visible(fn(Get $get) => $get('productData.price_type') === 'tier')
                ->schema(function ($get) use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                    return [
                        Section::make('East Malaysia')->schema([
                            TableRepeater::make('pcInfo_east')
                                ->live()
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The east zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_east')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->maxItems(3)
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                                    $tiers = [];
                                    if ($productRelationData?->east_tier_1_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->east_tier_1_min_quantity,
                                            'max_quantity' => $productRelationData?->east_tier_1_max_quantity,
                                            'price' => $productRelationData?->east_tier_1_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_1_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->east_tier_2_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->east_tier_2_min_quantity,
                                            'max_quantity' => $productRelationData?->east_tier_2_max_quantity,
                                            'price' => $productRelationData?->east_tier_2_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->east_tier_3_min_quantity != null) {
                                        $tier3maxQty = $productRelationData?->east_tier_3_max_quantity == 0 ? null : $productRelationData?->east_tier_3_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->east_tier_3_min_quantity,
                                            'max_quantity' => $tier3maxQty,
                                            'price' => $productRelationData?->east_tier_3_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }

                                    return $tiers;
                                })
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();

                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            $set('pcInfo_east', $updatedTiers);
                                        }
                                    });
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->formatStateUsing(fn() => 1)
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->placeholder('and above')
                                        ->numeric()
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, Component $component) {
                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->step(1) // Ensure whole numbers only
                                        ->live(onBlur: true) // Capture on blur to ensure complete value
                                        ->dehydrateStateUsing(fn($state) => (int)$state) // Ensure integer when saving
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->afterStateUpdated(function ($state, Set $set, Get $get, $component) {

                                            $maxQty = (int)$state;

                                            // Get the row path
                                            $rowPath = $component->getStatePath();
                                            $parentPath = substr($rowPath, 0, strrpos($rowPath, '.'));
                                            $rowKey = substr($rowPath, strrpos($rowPath, '.') + 1);

                                            $tiers = $get('../../pcInfo_east');

                                            // Update the specific tier
                                            if (isset($tiers[$rowKey])) {
                                                $tiers[$rowKey]['max_quantity'] = $maxQty;

                                                $set('../../pcInfo_east', $tiers);
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ]),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->numeric()
                                        ->prefix('RM')
                                        ->live(onBlur: true)
                                        ->formatStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->dehydrateStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        ),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function () use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        ->reactive()
                                        ->readOnly()
                                        ->prefix('RM')
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();

                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            $set('pcInfo_east', $updatedTiers);
                                        }
                                    });
                                })
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {
                                            $tiers = $get('pcInfo_east');
                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    ->title('Invalid Tier Addition')
                                                    ->body('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $tiers);
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ]),

                        Section::make('West Malaysia')->schema([
                            TableRepeater::make('pcInfo_west')
                                ->maxItems(3)
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The west zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_west')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                                    $tiers = [];
                                    if ($productRelationData?->west_tier_1_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->west_tier_1_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_1_max_quantity,
                                            'price' => $productRelationData?->west_tier_1_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_1_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_2_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->west_tier_2_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_2_max_quantity,
                                            'price' => $productRelationData?->west_tier_2_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_3_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->west_tier_3_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_3_max_quantity,
                                            'price' => $productRelationData?->west_tier_3_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }

                                    return $tiers;
                                })
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $tiers = $get('pcInfo_west');

                                    if (!empty($tiers)) {
                                        $tierKeys = array_keys($tiers);
                                        $tiersArray = array_values($tiers);

                                        // Renumber tiers sequentially regardless of which one was removed
                                        for ($i = 0; $i < count($tiersArray); $i++) {
                                            $tierNumber = $i + 1;
                                            $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;

                                            // For the first tier, min_quantity is always 1
                                            if ($i == 0) {
                                                $tiersArray[$i]['min_quantity'] = 1;
                                            }
                                            // For subsequent tiers, min_quantity is previous tier's max_quantity + 1
                                            else {
                                                $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                    (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                if ($previousMax > 0) {
                                                    $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                }
                                            }
                                        }

                                        // Rebuild the array with original keys but updated values
                                        $updatedTiers = [];
                                        foreach ($tierKeys as $index => $key) {
                                            if (isset($tiersArray[$index])) {
                                                $updatedTiers[$key] = $tiersArray[$index];
                                            }
                                        }

                                        $set('pcInfo_west', $updatedTiers);
                                    }
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->formatStateUsing(fn() => 1)
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->placeholder('and above')
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, Component $component) {
                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->numeric()
                                        ->live('blur')
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ])
                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                            $tiers = $get('pcInfo_west');
                                            if (!empty($tiers)) {
                                                $set('pcInfo_west', $tiers);
                                            }
                                        })
                                        ->validationAttribute('Maximum quantity'),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->afterStateUpdated(function (Set $set, Get $get) use ($finalCommissionType) {
                                            $price = (int)$get('price');
                                            $adminFees = (int)$get('admin_fees');
                                            $commissionType = $finalCommissionType;

                                            if ($commissionType === 'percentage') {
                                                $commission = $price * $adminFees / 100;
                                            } else {
                                                $commission = $adminFees;
                                            }

                                            $netEarnings = $price - $commission;
                                            $set('net_earnings', number_format($netEarnings, 2, '.', ''));
                                        })
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        )
                                        ->numeric()
                                        ->prefix('RM'),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function () use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        // ->reactive()
                                        ->disabled()
                                        ->prefix('RM')

                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {

                                            $tiers = $get('pcInfo_west');
                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    ->title('Invalid Tier Addition')
                                                    ->body('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_west', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ])
                    ];
                })
        ];
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {
        if ($finalCommissionType == 'percentage') {
            $commission = $finalCommission * $price / 100;
            $earning = $price - $commission;
        } else {
            $earning = $price - $finalCommission;
        }

        return number_format($earning, 2);
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }

    public static function getPcData($record, $userId)
    {

        return $record->productDataForPc($userId);
    }
}
