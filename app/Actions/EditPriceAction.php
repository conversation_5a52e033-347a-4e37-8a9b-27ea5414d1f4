<?php

namespace App\Actions;

use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\Alignment;
use App\Service\TierValidationService;
use Filament\Forms\Components\Section;
use App\Service\PriceManagementService;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Placeholder;
use Illuminate\Validation\ValidationException;
use Awcodes\TableRepeater\Components\TableRepeater;

class EditPriceAction
{
    public static function make($finalCommission, $finalCommissionType)
    {
        return Action::make('Edit Price')
            ->modalSubmitActionLabel('Save')
            ->icon('heroicon-o-pencil')
            ->outlined()
            ->form(function ($record) use ($finalCommission, $finalCommissionType) {
                $user = getUser(Auth::user());
                $productData = $record->productDataForPc($user->id);
                $productRelationData = ProductRelationPrice::where('product_relation_id', $productData?->id)->first();
                return [
                    Section::make()->schema([
                        Radio::make('price_type')
                            ->label('Price Type')
                            ->extraAttributes(['class' => 'rajen'])
                            ->inline()
                            ->live()
                            ->formatStateUsing(function ($record) use ($productData) {
                                return $productData?->price_type ?? 'fixed';
                            })
                            ->options(fn($record) => self::getEditPriceOptions($record)),
                    ]),
                    Placeholder::make('discription')
                        ->hiddenLabel()
                        ->content(function ($record) {
                            $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                            if (empty($relationData?->id)) {
                                return "-";
                            }

                            $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                            $packSize = $stockData?->wholesale_pack_size;

                            if (empty($packSize)) {
                                return "-";
                            }

                            $text = '';

                            $text .= $packSize . ' ';

                            $containerName = $record->container?->name;
                            if (!empty($containerName)) {
                                $text .= Str::plural($containerName) . ' of ';
                            }

                            $quantityPerUnit = $record->quantity_per_unit;
                            $foamName = $record->foam?->name;

                            if (!empty($quantityPerUnit) && !empty($foamName)) {
                                $qty = $quantityPerUnit * (int) $packSize;
                                $doses = Str::plural($foamName);

                                $text .= "( {$qty} {$doses} ) ";
                            }
                            return new HtmlString("<div class='px-5 py-2 font-bold border-2 rounded-lg text-md'>Pricing : {$text}</div>");
                        }),
                    Section::make()
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'fixed';
                        })
                        ->heading('Fixed')
                        ->schema(fn($record) => self::getFixedPriceSchema($record, $finalCommission, $finalCommissionType)),

                    Group::make()
                        ->extraAttributes([
                            'style' => 'max-height: 600px; overflow-y: auto;'
                        ])
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'bonus';
                        })
                        ->schema([
                            Section::make()
                                ->heading('East Malaysia')
                                ->schema(fn($record) => self::getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)),
                            Section::make()
                                ->heading('West Malaysia')
                                ->schema(fn($record) => self::getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)),
                        ]),

                    Group::make()
                        ->visible(function (Get $get) {
                            return $get('price_type') == 'tier';
                        })
                        ->schema(fn($record) => self::tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData)),
                ];
            })
            ->action(function ($data, $record, PriceManagementService $service) {
                // Capture original state for activity log
                $user = getUser(Auth::user());
                $userId = $user->id;
                $relation = self::getPcData($record, $userId);
                $originalPriceData = ProductRelationPrice::where('product_relation_id', $relation?->id)->first();
                $originalState = [
                    'relation' => $relation ? $relation->toArray() : [],
                    'price' => $originalPriceData ? $originalPriceData->toArray() : []
                ];
                
                if ($data['price_type'] == 'bonus') {
                    $bonusEastQuantity = [];
                    for ($i = 1; $i < 4; $i++) {
                        if (!empty($data["east_bonus_{$i}_quantity"])) {

                            $bonusEastQuantity[] = $data["east_bonus_{$i}_quantity"];
                        }
                    }
                    $eastUnique = array_unique($bonusEastQuantity);
                    if ($bonusEastQuantity != $eastUnique) {
                        Notification::make()
                            ->title("")
                            ->body('Please make sure that all east quantities are unique.')
                            ->danger()
                            ->send();
                        throw ValidationException::withMessages([
                            '' => $eastUnique,
                        ]);
                    }
                    $bonusWestQuantity = [];
                    for ($i = 1; $i < 4; $i++) {
                        if (!empty($data["west_bonus_{$i}_quantity"])) {
                            $bonusWestQuantity[] = $data["west_bonus_{$i}_quantity"];
                        }
                    }
                    $westUnique = array_unique($bonusWestQuantity);

                    if ($bonusWestQuantity != $westUnique) {
                        Notification::make()
                            ->title("")
                            ->body('Please make sure that all west quantities are unique.')
                            ->danger()
                            ->send();
                        throw ValidationException::withMessages([
                            '' => $eastUnique,
                        ]);
                    }

                    // Validate bonus quantity and value sequence
                    $regions = ['east', 'west'];
                    
                    foreach ($regions as $region) {
                        $regionTitle = ucfirst($region);
                        
                        // Get all bonus data for this region
                        $bonus1Qty = $data["{$region}_bonus_1_quantity"] ?? null;
                        $bonus1Value = $data["{$region}_bonus_1_quantity_value"] ?? null;
                        $bonus2Qty = $data["{$region}_bonus_2_quantity"] ?? null;
                        $bonus2Value = $data["{$region}_bonus_2_quantity_value"] ?? null;
                        $bonus3Qty = $data["{$region}_bonus_3_quantity"] ?? null;
                        $bonus3Value = $data["{$region}_bonus_3_quantity_value"] ?? null;
                        
                        // Check if both quantity and value are provided together
                        $bonus1Filled = !empty($bonus1Qty) && !empty($bonus1Value);
                        $bonus2Filled = !empty($bonus2Qty) && !empty($bonus2Value);
                        $bonus3Filled = !empty($bonus3Qty) && !empty($bonus3Value);
                        
                        // Check individual field pairing
                        if ((!empty($bonus1Qty) && empty($bonus1Value)) || (empty($bonus1Qty) && !empty($bonus1Value))) {
                            Notification::make()
                                ->title('Bonus Field Error')
                                ->body("{$regionTitle} Bonus 1: Both Quantity and Bonus Qty. must be entered together.")
                                ->danger()
                                ->persistent()
                                ->send();
                            throw ValidationException::withMessages([
                                "{$region}_bonus_1_quantity" => "Both quantity and bonus qty. must be entered together.",
                                "{$region}_bonus_1_quantity_value" => "Both quantity and bonus qty. must be entered together."
                            ]);
                        }
                        
                        if ((!empty($bonus2Qty) && empty($bonus2Value)) || (empty($bonus2Qty) && !empty($bonus2Value))) {
                            Notification::make()
                                ->title('Bonus Field Error')
                                ->body("{$regionTitle} Bonus 2: Both Quantity and Bonus Qty. must be entered together.")
                                ->danger()
                                ->persistent()
                                ->send();
                            throw ValidationException::withMessages([
                                "{$region}_bonus_2_quantity" => "Both quantity and bonus qty. must be entered together.",
                                "{$region}_bonus_2_quantity_value" => "Both quantity and bonus qty. must be entered together."
                            ]);
                        }
                        
                        if ((!empty($bonus3Qty) && empty($bonus3Value)) || (empty($bonus3Qty) && !empty($bonus3Value))) {
                            Notification::make()
                                ->title('Bonus Field Error')
                                ->body("{$regionTitle} Bonus 3: Both Quantity and Bonus Qty. must be entered together.")
                                ->danger()
                                ->persistent()
                                ->send();
                            throw ValidationException::withMessages([
                                "{$region}_bonus_3_quantity" => "Both quantity and bonus qty. must be entered together.",
                                "{$region}_bonus_3_quantity_value" => "Both quantity and bonus qty. must be entered together."
                            ]);
                        }
                        
                        // Check sequential order
                        if ($bonus2Filled && !$bonus1Filled) {
                            Notification::make()
                                ->title('Bonus Sequence Error')
                                ->body("{$regionTitle} Bonus 1 must be completed before entering Bonus 2. Please fill Bonus 1 first.")
                                ->danger()
                                ->persistent()
                                ->send();
                            throw ValidationException::withMessages([
                                "{$region}_bonus_2_quantity" => "Please complete Bonus 1 before entering Bonus 2.",
                                "{$region}_bonus_2_quantity_value" => "Please complete Bonus 1 before entering Bonus 2."
                            ]);
                        }
                        
                        if ($bonus3Filled && (!$bonus1Filled || !$bonus2Filled)) {
                            $missingBonuses = [];
                            if (!$bonus1Filled) $missingBonuses[] = "Bonus 1";
                            if (!$bonus2Filled) $missingBonuses[] = "Bonus 2";
                            
                            $missingText = implode(' and ', $missingBonuses);
                            Notification::make()
                                ->title('Bonus Sequence Error')
                                ->body("{$regionTitle} {$missingText} must be completed before entering Bonus 3. Please fill bonuses in sequence.")
                                ->danger()
                                ->persistent()
                                ->send();
                            throw ValidationException::withMessages([
                                "{$region}_bonus_3_quantity" => "Please complete {$missingText} before entering Bonus 3.",
                                "{$region}_bonus_3_quantity_value" => "Please complete {$missingText} before entering Bonus 3."
                            ]);
                        }
                    }
                }
                if ($data['price_type'] == 'tier') {
                    $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
                    $westTierPriceInfo = $data['pcInfo_west'] ?? [];

                    $lastIndexForEast = count($eastTierPriceInfo) - 1;
                    $lastIndexForWest = count($westTierPriceInfo) - 1;

                    foreach ($eastTierPriceInfo as $index => $row) {
                        if ($index !== $lastIndexForEast && empty($row['max_quantity'])) {
                            Notification::make()
                                ->title('Max quantity is required for all but the last row in East pricing.')
                                ->danger()
                                ->send();
                            throw ValidationException::withMessages([
                                'pcInfo_east.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in East pricing.',
                            ]);
                        }
                    }

                    foreach ($westTierPriceInfo as $index => $row) {
                        if ($index !== $lastIndexForWest && empty($row['max_quantity'])) {
                            Notification::make()
                                ->title('Max quantity is required for all but the last row in East pricing.')
                                ->danger()
                                ->send();
                            throw ValidationException::withMessages([
                                'pcInfo_west.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in West pricing.',
                            ]);
                        }
                    }
                }
                
                $service->store($data, $record, $relation->id, $user);
                
                // Clear cache after price update
                \App\Services\ProductRelationCacheService::clearProductRelationPriceCache($relation->id);
                \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);
                
                // Clear additional price-related caches
                \Illuminate\Support\Facades\Cache::forget("product_price_{$record->id}_{$userId}");
                \Illuminate\Support\Facades\Cache::forget("product_relation_price_{$relation->id}");
                
                // Capture final state for activity log
                $finalPriceData = ProductRelationPrice::where('product_relation_id', $relation->id)->first();
                $finalState = [
                    'relation' => $relation->fresh()->toArray(),
                    'price' => $finalPriceData ? $finalPriceData->toArray() : []
                ];
                
                // Extract only changed fields
                $changedData = [];
                $oldChangedData = [];
                
                foreach ($finalState as $key => $newValues) {
                    $oldValues = $originalState[$key] ?? [];
                    
                    // For arrays, compare individual keys
                    $changes = [];
                    $oldChanges = [];
                    
                    foreach ($newValues as $field => $newValue) {
                        $oldValue = $oldValues[$field] ?? null;
                        
                        // Convert both to same type for proper comparison
                        $normalizedOld = is_numeric($oldValue) ? (float)$oldValue : $oldValue;
                        $normalizedNew = is_numeric($newValue) ? (float)$newValue : $newValue;
                        
                        if ($normalizedOld !== $normalizedNew) {
                            $changes[$field] = $newValue;
                            $oldChanges[$field] = $oldValue;
                        }
                    }
                    
                    if (!empty($changes)) {
                        $changedData[$key] = $changes;
                        $oldChangedData[$key] = $oldChanges;
                    }
                }
                
                if (!empty($changedData)) {
                    $user = getUser(Auth::user());
                    
                    // 🎯 Create comprehensive human-readable activity data
                    $humanReadableData = [
                        // Basic Context
                        'Product Name' => $record->name,
                        'SKU' => $relation->sku ?? 'Not Set',
                        'Updated By PC' => $user->name,
                        'Updated By ID' => $user->id,
                        'Update Type' => 'PC Price Update',
                        
                        // Product Details
                        'Category' => $record->category?->name ?? 'Unknown',
                        'Brand' => $record->brand?->name ?? 'Unknown',
                        'Pricing Type' => ucfirst($data['price_type']),
                        
                        // Commission Information
                        'Commission Type' => $relation->commission_type ?? 'Not Set',
                        'Commission Value' => $relation->commission_type === 'percentage' 
                            ? ($relation->commission_percentage ?? 0) . '%'
                            : 'RM ' . number_format($relation->commission_amount ?? 0, 2),
                    ];
                    
                    // Add detailed pricing information based on type
                    if ($data['price_type'] === 'fixed') {
                        $humanReadableData['Fixed Pricing'] = [
                            'East Malaysia' => 'RM ' . number_format($data['east_zone_price_1'] ?? $data['fixed_price_east'] ?? 0, 2),
                            'West Malaysia' => 'RM ' . number_format($data['west_zone_price_1'] ?? $data['fixed_price_west'] ?? 0, 2),
                        ];
                    } elseif ($data['price_type'] === 'bonus') {
                        $humanReadableData['Bonus Pricing'] = [
                            'East Malaysia' => [
                                'Base Price' => 'RM ' . number_format($data['east_zone_price_bonus'] ?? $data['east_base_price'] ?? 0, 2),
                                'Bonus Structure' => self::formatBonusStructureForLog($data, 'east')
                            ],
                            'West Malaysia' => [
                                'Base Price' => 'RM ' . number_format($data['west_zone_price_bonus'] ?? $data['west_base_price'] ?? 0, 2),
                                'Bonus Structure' => self::formatBonusStructureForLog($data, 'west')
                            ]
                        ];
                    } elseif ($data['price_type'] === 'tier') {
                        $humanReadableData['Tier Pricing'] = [
                            'East Malaysia' => self::formatTierStructureForLog($data['pcInfo_east'] ?? []),
                            'West Malaysia' => self::formatTierStructureForLog($data['pcInfo_west'] ?? [])
                        ];
                    }
                    
                    // Add technical changes for reference
                    $humanReadableData['Technical Changes'] = [
                        'Changed Fields' => array_keys($changedData),
                        'Previous Price Type' => $previousPriceType ?? 'Not Set',
                        'New Price Type' => $data['price_type'],
                        'Has Previous Data' => !empty($originalPriceDataArray) ? 'Yes' : 'No',
                        'Updated At' => now()->format('Y-m-d H:i:s'),
                    ];

                    // 🎯 Create clean, user-friendly old data structure
                    $humanReadableOldData = [
                        'Product Name' => $record->name,
                        'Previous Configuration' => 'Price update from ViewProduct page',
                        'Updated By PC' => $user->name,
                        'Update Source' => 'ViewProduct Page'
                    ];
                    
                    // Get previous price type and format old data accordingly
                    $originalPriceDataArray = $originalState['price'] ?? [];
                    $previousPriceType = $originalState['relation']['price_type'] ?? null;
                    
                    // Add previous price type info
                    $humanReadableOldData['Previous Price Type'] = $previousPriceType ? ucfirst($previousPriceType) : 'Not Set';
                    
                    if ($previousPriceType === 'fixed') {
                        $humanReadableOldData['Previous Fixed Pricing'] = [
                            'East Malaysia' => 'RM ' . number_format($originalPriceDataArray['east_zone_price'] ?? 0, 2),
                            'West Malaysia' => 'RM ' . number_format($originalPriceDataArray['west_zone_price'] ?? 0, 2),
                        ];
                    } elseif ($previousPriceType === 'bonus') {
                        $humanReadableOldData['Previous Bonus Pricing'] = [
                            'East Malaysia' => [
                                'Base Price' => 'RM ' . number_format($originalPriceDataArray['east_zone_price'] ?? 0, 2),
                                'Bonus Structure' => self::formatOldBonusStructureForLog($originalPriceDataArray, 'east')
                            ],
                            'West Malaysia' => [
                                'Base Price' => 'RM ' . number_format($originalPriceDataArray['west_zone_price'] ?? 0, 2),
                                'Bonus Structure' => self::formatOldBonusStructureForLog($originalPriceDataArray, 'west')
                            ]
                        ];
                    } elseif ($previousPriceType === 'tier') {
                        $humanReadableOldData['Previous Tier Pricing'] = [
                            'East Malaysia' => self::formatOldTierStructureForLog($originalPriceDataArray, 'east'),
                            'West Malaysia' => self::formatOldTierStructureForLog($originalPriceDataArray, 'west')
                        ];
                    } else {
                        // Handle case where there's no previous pricing data
                        $humanReadableOldData['Previous Pricing'] = [
                            'Status' => 'No previous pricing configuration found',
                            'Note' => 'This may be the first time pricing is being set for this product'
                        ];
                    }

                    activity()
                        ->performedOn($record)
                        ->causedBy(Auth::user())
                        ->withProperties([
                            'old' => $humanReadableOldData,
                            'attributes' => $humanReadableData
                        ])
                        ->log("PC '{$user->name}' updated pricing for '{$record->name}' - {$humanReadableData['Pricing Type']} pricing structure applied");
                }
                
                Notification::make()->title('Product Price Updated')->success()->send();
            });
    }

    public static function getEditPriceOptions($record)
    {
        return [
            'fixed' => new HtmlString('Fixed &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sets a fixed price that does not vary with quantity or duration.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            '),

                'bonus' => new HtmlString('Bonus &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            
            
            '),

                'tier' => new HtmlString('Tier &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Applies different prices based on quantity purchased — more units may cost less per unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            '),
        ];
    }


    public static function getFixedPriceSchema($record, $finalCommission, $finalCommissionType)
    {

        return [
            Group::make()
                ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                ->schema([
                    Placeholder::make('Region'),
                    Placeholder::make('Price')->label('Price By'),
                    Placeholder::make('Admin Fees'),
                    Placeholder::make('Net Earnings'),
                ])->columns(4),
            Group::make()->schema([
                Placeholder::make('East Malaysia')
                    ->label("")
                    ->content("East Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('east_zone_price_1')
                    ->validationAttribute('east zone price')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric', 'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->formatStateUsing(function ($record) {
                        return (ProductRelationPrice::where('product_relation_id', $record->productDataForPC(getUser(Auth::id()))?->id)->first()?->east_zone_price);
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('east_zone_price_1') / 100;
                            $earning = (float)$get('east_zone_price_1') - (float)$commission;

                            return 'RM ' . number_format($earning, 2);
                        } else {
                            $earning = (float)$get('east_zone_price_1') - (float)$finalCommission;
                            return 'RM ' . number_format($earning, 2);
                        }

                        return 'RM15';
                    }),

                Placeholder::make('West Malaysia')
                    ->label("")
                    ->content("West Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('west_zone_price_1')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric',  'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->validationMessages([
                        'gt' => 'The west zone price must be greater than 0.',
                    ])
                    ->validationAttribute('west zone price')
                    ->formatStateUsing(function ($record) {
                        return (ProductRelationPrice::where('product_relation_id', $record->productDataForPc(getUser(Auth::id()))?->id)->first()?->west_zone_price);
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('west_zone_price_1') / 100;
                            $earning = (float)$get('west_zone_price_1') - (float)$commission;
                            return 'RM ' . number_format((float)$earning, 2);
                        } else {
                            $earning = (float)$get('west_zone_price_1') - $finalCommission;

                            return 'RM ' . number_format((float)$earning, 2);
                        }

                        return 'RM15';
                    }),
            ])->columns(4),
        ];
    }

    public static function getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()
                ->schema([
                    Group::make()
                        ->schema([
                            TextInput::make('east_zone_price_bonus')
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_zone_price;
                                })
                                ->rules(function (Get $get) {
                                    $priceType = $get('price_type');
                                    if ($priceType == 'bonus') {
                                        return ['required', 'numeric', 'gt:0', 'price'];
                                    }
                                    return ['numeric', 'price'];
                                })
                                ->placeholder('Enter Price')
                                ->validationAttribute('east bonus price')
                                ->calculateNetEarnings(
                                    commission: $finalCommission,
                                    commissionType: $finalCommissionType,
                                    fieldId: 'mountedTableActionsData.0.east_bonus_net_earnings',
                                    currentField: 'mountedTableActionsData.0.east_bonus_base_price',
                                )
                                ->prefix('RM')
                                ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_admin_fees')
                                ->label('Admin Fees')
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission;
                                    }

                                    return $finalCommission;
                                })
                                ->disabled()
                                ->prefix(function () use ($finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        return '%';
                                    }

                                    return 'RM';
                                }),
                            TextInput::make('east_bonus_net_earnings')
                                ->disabled()
                                ->prefix('RM')
                                ->reactive()
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData) {
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $productRelationData?->east_zone_price / 100;
                                        $earning = $productRelationData?->east_zone_price - $commission;
                                    } else {
                                        $earning = $productRelationData?->east_zone_price - $finalCommission;
                                    }

                                    return $earning;
                                })

                                ->label('Net Earnings'),
                        ])
                        ->columns(3),
                    Group::make()
                        ->columns(2)
                        ->schema([
                            TextInput::make('east_bonus_1_quantity')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity')
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', 'max:999999', 'integer', 'gt:0'];
                                    }
                                    return [];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity;
                                })
                                ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_1_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_1_quantity') ?: 0;
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', "max:$max", 'integer', 'gt:0'];
                                    }
                                    return ['nullable', "max:$max", 'integer', 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity_value;
                                })
                                ->label(new HtmlString('Bonus Qty. <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_2_quantity')
                                ->reactive()
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity')
                                ->rules(['max:999999', 'integer', 'gt:0'])
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_2_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_2_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                            TextInput::make('east_bonus_3_quantity')
                                ->validationAttribute('east bonus 3 quantity')
                                ->rules(['integer', 'max:999999', 'gt:0'])
                                ->numeric()
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_3_quantity_value')
                                ->validationAttribute('east bonus 3 quantity value')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_3_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                        ]),
                ]),

        ];
    }

    public static function getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()

                ->schema([
                    TextInput::make('west_zone_price_bonus')
                        ->placeholder('Enter Price')
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_zone_price;
                        })
                        ->validationAttribute('west zone price')
                        ->calculateNetEarnings(
                            commission: $finalCommission,
                            commissionType: $finalCommissionType,
                            fieldId: 'mountedTableActionsData.0.west_bonus_net_earnings',
                            currentField: 'mountedTableActionsData.0.west_bonus_base_price',
                        )
                        ->extraAttributes(fn() => self::numericValueValidationRule())
                        ->prefix('RM')
                        ->rules(function (Get $get) {
                            if ($get('price_type') === 'bonus') {
                                return ['required', 'numeric', 'gt:0', 'price'];
                            }
                            return ['nullable', 'numeric', 'gt:0', 'price'];
                        })
                        ->validationAttribute('west zone bonus price')
                        ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_admin_fees')
                        ->label('Admin Fees')
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return $finalCommission;
                            }

                            return $finalCommission;
                        })
                        ->disabled()
                        ->prefix(function () use ($finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return '%';
                            }

                            return 'RM';
                        }),
                    TextInput::make('west_bonus_net_earnings')
                        ->disabled()
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData) {
                            if ($finalCommissionType == 'percentage') {
                                $commission = $finalCommission * $productRelationData?->west_zone_price / 100;
                                $earning = $productRelationData?->west_zone_price - $commission;
                            } else {
                                $earning = $productRelationData?->west_zone_price - $finalCommission;
                            }

                            return $earning;
                        })
                        ->prefix('RM')
                        ->reactive()
                        ->label('Net Earnings'),
                ])
                ->columns(3),
            Group::make()
                ->columns(2)
                ->schema([
                    TextInput::make('west_bonus_1_quantity')
                        ->validationAttribute('west bonus 1 quantity')
                        ->numeric()
                        ->rules(['required', 'max:999999', 'integer', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity;
                        })
                        ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_1_quantity_value')
                        ->validationAttribute('west bonus 1 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_1_quantity') ?: 0;
                            return ['required', 'integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity_value;
                        })
                        ->label(new HtmlString('Quantity Qty. <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_2_quantity')
                        ->validationAttribute('west bonus 2 quantity')
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_2_quantity_value')
                        ->validationAttribute('west bonus 2 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_2_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                    TextInput::make('west_bonus_3_quantity')
                        ->validationAttribute('west bonus 3 quantity')
                        ->numeric()
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_3_quantity_value')
                        ->validationAttribute('west bonus 3 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_3_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                ]),
        ];
    }

    public static function tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData)
    {
        return [
            Group::make()
                ->schema(function ($get) use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                    return [
                        Section::make('East Malaysia')->schema([
                            TableRepeater::make('pcInfo_east')
                                ->live()
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The east zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_east')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->maxItems(3)
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                                    $tiers = [];
                                    if ($productRelationData?->east_tier_1_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->east_tier_1_min_quantity,
                                            'max_quantity' => $productRelationData?->east_tier_1_max_quantity == 0 ? null : $productRelationData?->east_tier_1_max_quantity,
                                            'price' => $productRelationData?->east_tier_1_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_1_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->east_tier_2_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->east_tier_2_min_quantity,
                                            'max_quantity' => $productRelationData?->east_tier_2_max_quantity == 0 ? null : $productRelationData?->east_tier_2_max_quantity,
                                            'price' => $productRelationData?->east_tier_2_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->east_tier_3_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->east_tier_3_min_quantity,
                                            'max_quantity' => $productRelationData?->east_tier_3_max_quantity == 0 ? null : $productRelationData?->east_tier_3_max_quantity,
                                            'price' => $productRelationData?->east_tier_3_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }

                                    return $tiers;
                                })
                                ->afterStateUpdated(function (Get $get, Set $set) use ($finalCommissionType, $finalCommission) {
                                    $tiers = $get('pcInfo_east');
                                    $westTiers = $get('pcInfo_west');
                                    if (!empty($tiers)) {
                                        // Iterate over tiers without modifying keys
                                        $tierKeys = array_keys($tiers);
                                        $tiersArray = array_values($tiers);

                                        for ($i = 1; $i < count($tiersArray); $i++) {
                                            $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                            if ($previousMax > 0) {
                                                // Update min_quantity without changing the key
                                                $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                            }
                                            $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);
                                        }
                                        foreach ($westTiers as $key => $tier) {
                                            $westTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                        }

                                        $set('pcInfo_west', $westTiers);
                                        // Update the state without modifying keys
                                        $set('pcInfo_east', $tiers);
                                    }
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->formatStateUsing(fn() => 1)
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->live(onBlur: true)
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, Component $component) {

                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->placeholder('and above')
                                        ->numeric()
                                        ->step(1) // Ensure whole numbers only
                                        ->live(onBlur: true) // Capture on blur to ensure complete value
                                        ->dehydrateStateUsing(fn($state) => (int)$state) // Ensure integer when saving
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->afterStateUpdated(function ($state, Set $set, Get $get, $component) {

                                            $maxQty = (int)$state;

                                            // Get the row path
                                            $rowPath = $component->getStatePath();
                                            $parentPath = substr($rowPath, 0, strrpos($rowPath, '.'));
                                            $rowKey = substr($rowPath, strrpos($rowPath, '.') + 1);

                                            $tiers = $get('../../pcInfo_east');

                                            // Update the specific tier
                                            if (isset($tiers[$rowKey])) {
                                                $tiers[$rowKey]['max_quantity'] = $maxQty;

                                                $set('../../pcInfo_east', $tiers);
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ]),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->numeric()
                                        ->prefix('RM')
                                        ->live(onBlur: true)
                                        ->formatStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->dehydrateStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        ),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function () use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        ->reactive()
                                        ->readOnly()
                                        ->prefix('RM')
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();
                                        $eastTiers = $get('pcInfo_west');
                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            if (count($eastTiers) > 0) {
                                                foreach ($eastTiers as $key => $tier) {
                                                    $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                                }
                                            }
                                            $set('pcInfo_west', $eastTiers);
                                            $set('pcInfo_east', $updatedTiers);
                                        }
                                    });
                                })
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {
                                            $tiers = $get('pcInfo_east');
                                            $westTiers = $get('pcInfo_west');

                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    ->title('Invalid Tier Addition')
                                                    ->body('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $tiers);
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            foreach ($westTiers as $key => $tier) {
                                                $westTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_west', $westTiers);
                                            $set('pcInfo_east', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ]),

                        Section::make('West Malaysia')->schema([
                            TableRepeater::make('pcInfo_west')
                                ->maxItems(3)
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The west zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_west')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->afterStateUpdated(function (Get $get, Set $set) use ($finalCommissionType) {
                                    $tiers = $get('pcInfo_west');
                                    $eastTiers = $get('pcInfo_east');
                                    if (!empty($tiers)) {
                                        $tierKeys = array_keys($tiers);
                                        $tiersArray = array_values($tiers);

                                        for ($i = 1; $i < count($tiersArray); $i++) {
                                            $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                            if ($previousMax > 0) {
                                                $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                            }
                                            $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);
                                        }
                                        foreach ($tiers as $key => $tier) {
                                            $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                        }
                                        $set('pcInfo_east', $eastTiers);
                                        $set('pcInfo_west', $tiers);
                                    }
                                })
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();
                                        $eastTiers = $get('pcInfo_east');
                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            if (count($eastTiers) > 0) {
                                                foreach ($eastTiers as $key => $tier) {
                                                    $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                                }
                                            }
                                            $set('pcInfo_east', $eastTiers);
                                            $set('pcInfo_west', $updatedTiers);
                                        }
                                    });
                                })
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                                    $tiers = [];
                                    if ($productRelationData?->west_tier_1_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->west_tier_1_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_1_max_quantity == 0 ? null : $productRelationData?->west_tier_1_max_quantity,
                                            'price' => $productRelationData?->west_tier_1_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_1_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_2_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->west_tier_2_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_2_max_quantity == 0 ? null : $productRelationData?->west_tier_2_max_quantity,
                                            'price' => $productRelationData?->west_tier_2_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_3_min_quantity != null) {
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->west_tier_3_min_quantity,
                                            'max_quantity' => $productRelationData?->west_tier_3_max_quantity == 0 ? null : $productRelationData?->west_tier_3_max_quantity,
                                            'price' => $productRelationData?->west_tier_3_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }

                                    return $tiers;
                                })
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $tiers = $get('pcInfo_west');

                                    if (!empty($tiers)) {
                                        $tierKeys = array_keys($tiers);
                                        $tiersArray = array_values($tiers);

                                        // Renumber tiers sequentially regardless of which one was removed
                                        for ($i = 0; $i < count($tiersArray); $i++) {
                                            $tierNumber = $i + 1;
                                            $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;

                                            // For the first tier, min_quantity is always 1
                                            if ($i == 0) {
                                                $tiersArray[$i]['min_quantity'] = 1;
                                            }
                                            // For subsequent tiers, min_quantity is previous tier's max_quantity + 1
                                            else {
                                                $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                    (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                if ($previousMax > 0) {
                                                    $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                }
                                            }
                                        }

                                        // Rebuild the array with original keys but updated values
                                        $updatedTiers = [];
                                        foreach ($tierKeys as $index => $key) {
                                            if (isset($tiersArray[$index])) {
                                                $updatedTiers[$key] = $tiersArray[$index];
                                            }
                                        }

                                        $set('pcInfo_west', $updatedTiers);
                                    }
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->formatStateUsing(fn() => 1)
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, Component $component) {
                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->placeholder('and above')
                                        ->numeric()
                                        ->live('blur')
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ])
                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                            $tiers = $get('pcInfo_west');
                                            if (!empty($tiers)) {
                                                $set('pcInfo_west', $tiers);
                                            }
                                        })
                                        ->validationAttribute('Maximum quantity'),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->afterStateUpdated(function (Set $set, Get $get) use ($finalCommissionType) {
                                            $price = (int)$get('price');
                                            $adminFees = (int)$get('admin_fees');
                                            $commissionType = $finalCommissionType;

                                            if ($commissionType === 'percentage') {
                                                $commission = $price * $adminFees / 100;
                                            } else {
                                                $commission = $adminFees;
                                            }

                                            $netEarnings = $price - $commission;
                                            $set('net_earnings', number_format($netEarnings, 2, '.', ''));
                                        })
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        )
                                        ->numeric()
                                        ->prefix('RM'),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function () use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        // ->reactive()
                                        ->disabled()
                                        ->prefix('RM')

                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {

                                            $tiers = $get('pcInfo_west');
                                            $eastTiers = $get('pcInfo_east');

                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    ->title('Invalid Tier Addition')
                                                    ->body('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            foreach ($eastTiers as $key => $tier) {
                                                $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $eastTiers);
                                            $set('pcInfo_west', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ])
                    ];
                })
        ];
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {
        if ($finalCommissionType == 'percentage') {
            $commission = (int)$finalCommission * (int)$price / 100;
            $earning = (int)$price - (int)$commission;
        } else {
            $earning = (int)$price - (int)$finalCommission;
        }

        return number_format($earning, 2);
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }

    public static function getPcData($record, $userId)
    {
        return $record->productDataForPc($userId);
    }

    /**
     * Format bonus structure for activity logging
     */
    private static function formatBonusStructureForLog($data, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 3; $i++) {
            $qty = $data["{$region}_bonus_{$i}_quantity"] ?? null;
            $bonusQty = $data["{$region}_bonus_{$i}_quantity_value"] ?? null;
            
            if (!empty($qty) && !empty($bonusQty)) {
                $structure["Level {$i}"] = "Buy {$qty}, Get {$bonusQty} Free";
            }
        }
        return empty($structure) ? ['No bonus structure set'] : $structure;
    }

    /**
     * Format tier structure for activity logging
     */
    private static function formatTierStructureForLog($tierData): array
    {
        if (empty($tierData)) {
            return ['No tier structure set'];
        }
        
        $structure = [];
        foreach ($tierData as $index => $tier) {
            $tierNum = $index + 1;
            $minQty = $tier['min_quantity'] ?? 'N/A';
            $maxQty = $tier['max_quantity'] ?? 'Above';
            $price = isset($tier['price']) ? 'RM ' . number_format($tier['price'], 2) : 'Not Set';
            
            if ($maxQty === 'Above' || empty($maxQty)) {
                $structure["Tier {$tierNum}"] = "{$minQty}+ units = {$price} each";
            } else {
                $structure["Tier {$tierNum}"] = "{$minQty}-{$maxQty} units = {$price} each";
            }
        }
        return $structure;
    }

    /**
     * Format old bonus structure for activity logging
     */
    private static function formatOldBonusStructureForLog($oldPriceData, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 3; $i++) {
            $qty = $oldPriceData["{$region}_bonus_{$i}_quantity"] ?? null;
            $bonusQty = $oldPriceData["{$region}_bonus_{$i}_quantity_value"] ?? null;
            
            if (!empty($qty) && !empty($bonusQty)) {
                $structure["Level {$i}"] = "Buy {$qty}, Get {$bonusQty} Free";
            }
        }
        return empty($structure) ? ['No bonus structure set'] : $structure;
    }

    /**
     * Format old tier structure for activity logging
     */
    private static function formatOldTierStructureForLog($oldPriceData, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 5; $i++) { // Assuming max 5 tiers
            $minQty = $oldPriceData["{$region}_tier_{$i}_min_quantity"] ?? null;
            $maxQty = $oldPriceData["{$region}_tier_{$i}_max_quantity"] ?? null;
            $price = $oldPriceData["{$region}_tier_{$i}_base_price"] ?? null;
            
            if (!empty($minQty) && !empty($price)) {
                $formattedPrice = 'RM ' . number_format($price, 2);
                if (empty($maxQty) || $maxQty === 'Above') {
                    $structure["Tier {$i}"] = "{$minQty}+ units = {$formattedPrice} each";
                } else {
                    $structure["Tier {$i}"] = "{$minQty}-{$maxQty} units = {$formattedPrice} each";
                }
            }
        }
        return empty($structure) ? ['No tier structure set'] : $structure;
    }
}
