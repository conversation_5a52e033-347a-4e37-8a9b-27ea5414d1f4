<?php

namespace App\Actions;

use Carbon\Carbon;
use Filament\Forms\Get;
use Illuminate\Support\Str;
use App\Models\ProductBatch;
use App\Models\ProductRelation;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Mail\AdminUpdatedStockForPc;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Support\Enums\ActionSize;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Illuminate\Validation\ValidationException;
use Awcodes\TableRepeater\Components\TableRepeater;

class AdminStockUpdateAction
{
    public static function make($product)
    {
        return Action::make('update_stock')
            // ->visible(function ($record) {
            //     $approved = $record->status == 'approved';
            //     $user = auth()->user();
            //     return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_update stock'));
            // })
            ->visible(function () {
                $user = Auth::user();
                return $user->hasRole('Super Admin') || $user->can('products_stock change');
            })
            ->modalSubmitActionLabel('Save')
            ->size(ActionSize::ExtraLarge)
            ->modalWidth(MaxWidth::SevenExtraLarge)
            ->label('Update Stock')
            ->iconButton()
            ->tooltip('Update Stock')
            ->icon('heroicon-o-pencil')
            ->action(function (array $data, $record) use ($product) {
                $data['wholesale_pack_size'] = $data['stock_type'] == 'wps' ? $data['wholesale_pack_size'] : 1;

                $userId = $record->id;
                $relation = $product->productDataForPc($record->id);
                ProductRelationStock::updateOrCreate(['product_relation_id' => $relation->id]);
                $productStock = ProductRelationStock::where('product_relation_id', $relation->id)->first();
                $originalBatches = ProductBatch::where(['product_id' => $product->id, 'user_id' => $record->id])->get()->toArray();

                // 🎯 Capture clean original state with only essential data
                $originalState = [
                    'stock' => $productStock ? [
                        'stock' => $productStock->stock,
                        'total_stock' => $productStock->total_stock,
                        'is_batch_wise_stock' => $productStock->is_batch_wise_stock,
                        'low_stock' => $productStock->low_stock,
                        'wholesale_pack_size' => $productStock->wholesale_pack_size,
                        'stock_type' => $productStock->stock_type,
                    ] : [],
                    'batches' => collect($originalBatches)->map(function ($batch) {
                        return [
                            'batch_number' => $batch['batch_name'] ?? null,
                            'available_stock' => $batch['available_stock'] ?? null,
                            'expiry_date' => $batch['expiry_date'] ?? null,
                        ];
                    })->toArray()
                ];
                if ($data['stock_status'] == 'out_of_stock') {
                    if ($productStock?->is_batch_wise_stock) {
                        ProductBatch::where(['product_id' => $product->id, 'user_id' => $record->id])->delete();
                    }
                    $productStock->update(['stock' => 0, 'total_stock' => 0, 'is_batch_wise_stock' => false, 'wholesale_pack_size' => $data['wholesale_pack_size'], 'stock_type' => $data['stock_type']]);

                    // Clear cache after stock update
                    \App\Services\ProductRelationCacheService::clearProductRelationStockCache($relation->id);
                    \App\Services\ProductRelationCacheService::clearProductRelationCache($product->id, $record->id);

                    // 🎯 Create clean comparison data for out of stock scenario
                    $finalStockData = $productStock->fresh();

                    // Extract meaningful changed data for logging (out of stock)
                    $changedData = [];
                    $oldChangedData = [];

                    // Compare stock data with meaningful field names only
                    $originalStockData = $originalState['stock'] ?? [];
                    $stockChanges = [];
                    $oldStockChanges = [];

                    // Check key stock fields that changed to out of stock
                    $stockFields = ['stock', 'total_stock', 'is_batch_wise_stock'];
                    foreach ($stockFields as $field) {
                        $oldValue = $originalStockData[$field] ?? null;
                        $newValue = $field === 'stock' || $field === 'total_stock' ? 0 : ($field === 'is_batch_wise_stock' ? false : $finalStockData?->$field);

                        if ($oldValue != $newValue) {
                            $stockChanges[$field] = $newValue;
                            $oldStockChanges[$field] = $oldValue;
                        }
                    }

                    if (!empty($stockChanges)) {
                        $changedData['stock'] = $stockChanges;
                        $oldChangedData['stock'] = $oldStockChanges;
                    }

                    // Compare batch data (all batches deleted for out of stock)
                    $originalBatches = $originalState['batches'] ?? [];
                    if (!empty($originalBatches)) {
                        $changedData['batches'] = []; // Empty because all batches deleted
                        $oldChangedData['batches'] = $originalBatches;
                    }

                    if (!empty($changedData)) {
                        // Create clean, user-friendly old data structure
                        $humanReadableOldData = [
                            'Product Name' => $product->name,
                            'SKU' => $relation->sku ?? 'Not Set',
                            'Category' => $product->category?->name ?? 'Unknown',
                            'Brand' => $product->brand?->name ?? 'Unknown',
                            'Pharmaceutical Company' => $record->name ?? 'Unknown PC',
                        ];

                        // Add old stock configuration if it existed
                        if (!empty($originalState['stock'])) {
                            $originalStock = $originalState['stock'];
                            $humanReadableOldData['Previous Stock Configuration'] = [
                                'Stock Status' => ($originalStock['stock'] ?? 0) > 0 ? 'In Stock' : 'Out of Stock',
                                'Stock Management' => $originalStock['is_batch_wise_stock'] ? 'By Batch' : 'Simple Stock',
                                'Stock Type' => $originalStock['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                                'Wholesale Pack Size' => $originalStock['wholesale_pack_size'] ?? 'Not Set',
                                'Current Stock' => $originalStock['stock'] ?? 0,
                                'Total Stock' => $originalStock['total_stock'] ?? 0,
                                'Low Stock Trigger' => $originalStock['low_stock'] ?? 'Not Set',
                            ];
                        }

                        // Add old batch details if they existed
                        if (!empty($originalState['batches'])) {
                            $humanReadableOldData['Previous Batch Details'] = [];
                            foreach ($originalState['batches'] as $index => $batch) {
                                $batchNum = $index + 1;
                                $humanReadableOldData['Previous Batch Details']["Batch {$batchNum}"] = [
                                    'Batch Number' => $batch['batch_number'] ?? 'Not Set',
                                    'Available Stock' => $batch['available_stock'] ?? 0,
                                    'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
                                ];
                            }
                        }

                        // 🎯 Create comprehensive human-readable activity data
                        $humanReadableData = [
                            // Basic Context
                            'Product Name' => $product->name,
                            'SKU' => $relation->sku ?? 'Not Set',
                            'Updated By Admin' => Auth::user()->name,
                            'Update Type' => 'Admin Stock Update - Out of Stock',
                            'Pharmaceutical Company' => $record->name ?? 'Unknown PC',

                            // Product Details
                            'Category' => $product->category?->name ?? 'Unknown',
                            'Brand' => $product->brand?->name ?? 'Unknown',

                            // Stock Configuration
                            'Stock Status' => 'Out of Stock',
                            'Stock Management' => 'Simple Stock (Batches Cleared)',
                            'Stock Type' => $data['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                            'Wholesale Pack Size' => $data['wholesale_pack_size'] ?? 'Not Set',
                            'Current Stock' => '0',
                            'Total Stock' => '0',
                            'Low Stock Trigger' => $productStock?->low_stock ?? 'Not Set',

                            // Actions Taken
                            'Batches Cleared' => $productStock?->is_batch_wise_stock ? 'Yes' : 'No',
                            'Previous Batch Count' => count($originalBatches),

                            // Timestamp
                            'Updated At' => now()->format('Y-m-d H:i:s'),
                        ];

                        activity()
                            ->performedOn($product)
                            ->causedBy(Auth::user())
                            ->withProperties([
                                'old' => $humanReadableOldData,
                                'attributes' => $humanReadableData
                            ])
                            ->log("Admin set '{$product->name}' to out of stock for PC: {$record->name} - {$humanReadableData['Previous Batch Count']} batches cleared");
                    }

                    return;
                }
                $productData = $product->productDataForPc($record->id);
                $productRelationData = ProductRelationStock::where('product_relation_id', $productData?->id)->update(['low_stock' => $data['low_stock'], 'wholesale_pack_size' => $data['wholesale_pack_size'], 'stock_type' => $data['stock_type']]);
                $batches = $data['batches'] ?? [];
                $batchNames = array_column($batches, 'batch_name');
                $batchNames = array_filter($batchNames, fn($name) => is_string($name) && trim($name) !== '');
                $batchStock = 0;
                $duplicates = array_filter(array_count_values($batchNames), fn($count) => $count > 1);
                $isDuplicateName = count($duplicates) > 0;
                if ($isDuplicateName) {
                    Notification::make()
                        // ->title('Duplicate Batch Name')
                        ->title('Duplicate batch name found. Please check the batch names.')
                        ->danger()
                        ->send();
                    throw ValidationException::withMessages([
                        '' => 'Duplicate batch name found. Please check the batch names.',
                    ]);
                    // return;
                }
                if (!empty($data['batches'])) {
                    foreach ($data['batches'] as $batch) {
                        // dd($batch);
                        $batchStock += $batch['available_stock'];
                        $data['productData']['total_stock'] = $batchStock;
                    }
                } else {
                    $data['productData']['total_stock'] = $data['productData']['stock'];
                }
                $userId = $record->id;
                if (empty($data['batches'])) {
                    $userId = $record->id;
                    ProductBatch::where(['product_id' => $product->id, 'user_id' => $userId])->delete();
                }

                $relation = ProductRelation::where(['product_id' => $product->id, 'user_id' => $userId]);
                $relation?->update(['sku' => $data['sku']]);
                $productRelationId = $relation?->first()?->id;
                $data['productData']['low_stock'] = $data['low_stock'];
                $data['productData']['is_batch_wise_stock'] = $data['productData']['is_batch_wise_stock'] ?? false;
                $data['productData']['expiry_date'] = !empty($data['productData']['expiry_date']) ? Carbon::parse($data['productData']['expiry_date'])->setTime(23, 59, 0) : null;
                $stock = ProductRelationStock::updateOrCreate(['product_relation_id' => $productRelationId], $data['productData']);

                $batchesData = [];
                $relation = $relation?->first();
                if (!empty($data['batches'])) {

                    $batchesData = [];
                    foreach ($data['batches'] as $batch) {
                        $batchesData[] = [
                            'product_id' => $product?->id,
                            'user_id' => $userId,
                            'products_relation_id' => $relation?->id,
                            'available_stock' => $batch['available_stock'],
                            'batch_name' => $batch['batch_name'],
                            'expiry_date' => $batch['expiry_date'],
                        ];
                    }
                    ProductBatch::where(['product_id' => $product->id, 'user_id' => $userId])->delete();


                    $stock->update(['is_batch_wise_stock' => true, 'stock' => null, 'expiry_date' => null]);


                    ProductBatch::insert($batchesData);
                    $insertedBatchIds = ProductBatch::where([
                        'product_id' => $product?->id,
                        'user_id' => $userId,
                        'products_relation_id' => $relation?->id,
                    ])
                        ->orderByDesc('id')
                        ->limit(count($batchesData))
                        ->pluck('id')
                        ->toArray();
                }

                // Clear cache after stock update
                \App\Services\ProductRelationCacheService::clearProductRelationStockCache($relation->id);
                \App\Services\ProductRelationCacheService::clearProductRelationCache($product->id, $record->id);

                // 🎯 Create clean comparison data with only essential stock information
                $finalBatches = ProductBatch::where(['product_id' => $product->id, 'user_id' => $record->id])->get();
                $finalStockData = ProductRelationStock::where('product_relation_id', $relation->id)->first();

                // Extract meaningful changed data for logging
                $changedData = [];
                $oldChangedData = [];

                // Compare stock data with meaningful field names only
                $originalStockData = $originalState['stock'] ?? [];
                $stockChanges = [];
                $oldStockChanges = [];

                // Check key stock fields
                $stockFields = ['stock', 'total_stock', 'is_batch_wise_stock', 'low_stock', 'wholesale_pack_size', 'stock_type'];
                foreach ($stockFields as $field) {
                    $oldValue = $originalStockData[$field] ?? null;
                    $newValue = $finalStockData?->$field ?? null;

                    if ($oldValue != $newValue) {
                        $stockChanges[$field] = $newValue;
                        $oldStockChanges[$field] = $oldValue;
                    }
                }

                if (!empty($stockChanges)) {
                    $changedData['stock'] = $stockChanges;
                    $oldChangedData['stock'] = $oldStockChanges;
                }

                // Compare batch data
                $originalBatches = $originalState['batches'] ?? [];
                // dd($originalBatches, $finalBatches);
                if (
                    count($originalBatches) !== $finalBatches->count() ||
                    json_encode(collect($originalBatches)->pluck('batch_number', 'available_stock')) !==
                    json_encode($finalBatches->pluck('batch_name', 'available_stock'))
                ) {

                    $changedData['batches'] = $finalBatches->map(function ($batch) {
                        return [
                            'batch_number' => $batch->batch_name,
                            'available_stock' => $batch->available_stock,
                            'expiry_date' => $batch->expiry_date,
                        ];
                    })->toArray();

                    $oldChangedData['batches'] = collect($originalBatches)->map(function ($batch) {
                        return [
                            'batch_number' => $batch['batch_name'] ?? null,
                            'available_stock' => $batch['available_stock'] ?? null,
                            'expiry_date' => $batch['expiry_date'] ?? null,
                        ];
                    })->toArray();
                }

                if (!empty($changedData)) {
                    // Calculate total stock and batch details
                    $finalBatches = ProductBatch::where(['product_id' => $product->id, 'user_id' => $record->id])->get();
                    $totalBatchStock = $finalBatches->sum('available_stock');
                    $finalStockData = ProductRelationStock::where('product_relation_id', $relation->id)->first();

                    // Create clean, user-friendly old data structure
                    $humanReadableOldData = [
                        'Product Name' => $product->name,
                        'SKU' => $relation->sku ?? 'Not Set',
                        'Category' => $product->category?->name ?? 'Unknown',
                        'Brand' => $product->brand?->name ?? 'Unknown',
                        'Pharmaceutical Company' => $record->name ?? 'Unknown PC',
                    ];

                    // Add old stock configuration if it existed
                    if (!empty($originalState['stock'])) {
                        $originalStock = $originalState['stock'];
                        $humanReadableOldData['Previous Stock Configuration'] = [
                            'Stock Status' => ($originalStock['stock'] ?? 0) > 0 ? 'In Stock' : 'Out of Stock',
                            'Stock Management' => $originalStock['is_batch_wise_stock'] ? 'By Batch' : 'Simple Stock',
                            'Stock Type' => $originalStock['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                            'Wholesale Pack Size' => $originalStock['wholesale_pack_size'] ?? 'Not Set',
                            'Current Stock' => $originalStock['stock'] ?? 0,
                            'Total Stock' => $originalStock['total_stock'] ?? 0,
                            'Low Stock Trigger' => $originalStock['low_stock'] ?? 'Not Set',
                        ];
                    }

                    // Add old batch details if they existed
                    if (!empty($originalState['batches'])) {
                        $humanReadableOldData['Previous Batch Details'] = [];
                        foreach ($originalState['batches'] as $index => $batch) {
                            $batchNum = $index + 1;
                            $humanReadableOldData['Previous Batch Details']["Batch {$batchNum}"] = [
                                'Batch Number' => $batch['batch_number'] ?? 'Not Set',
                                'Available Stock' => $batch['available_stock'] ?? 0,
                                'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
                            ];
                        }
                    }

                    // 🎯 Create comprehensive human-readable activity data
                    $humanReadableData = [
                        // Basic Context
                        'Product Name' => $product->name,
                        'SKU' => $relation->sku ?? 'Not Set',
                        'Updated By Admin' => Auth::user()->name,
                        'Update Type' => 'Admin Stock Update - In Stock',
                        'Pharmaceutical Company' => $record->name ?? 'Unknown PC',

                        // Product Details
                        'Category' => $product->category?->name ?? 'Unknown',
                        'Brand' => $product->brand?->name ?? 'Unknown',

                        // Stock Configuration
                        'Stock Status' => 'In Stock',
                        'Stock Management' => $finalStockData?->is_batch_wise_stock ? 'By Batch' : 'Simple Stock',
                        'Stock Type' => $data['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                        'Wholesale Pack Size' => $data['wholesale_pack_size'] ?? 'Not Set',
                        'Low Stock Trigger' => $data['low_stock'] ?? 'Not Set',

                        // Stock Details
                        'Current Stock' => $finalStockData?->is_batch_wise_stock
                            ? $totalBatchStock . ' (from batches)'
                            : ($finalStockData?->stock ?? 'Not Set'),
                        'Total Stock' => $finalStockData?->total_stock ?? 'Not Set',
                        'Batch Count' => $finalBatches->count(),

                        // Timestamp
                        'Updated At' => now()->format('Y-m-d H:i:s'),
                    ];
                    // dd($finalBatches);    
                    // Add batch details if applicable
                    if ($finalStockData?->is_batch_wise_stock && $finalBatches->count() > 0) {
                        $humanReadableData['Batch Details'] = [];

                        foreach ($finalBatches as $index => $batch) {

                            $batchNum = $index + 1;
                            $humanReadableData['Batch Details']["Batch {$batchNum}"] = [
                                'Batch Number' => $batch->batch_name ?? 'Not Set',
                                'Available Stock' => $batch->available_stock ?? 0,
                                'Expiry Date' => $batch->expiry_date ?? 'Not Set',
                            ];
                        }
                    }
                    // dd($humanReadableOldData);
                    Mail::to($record->email)->send(new AdminUpdatedStockForPc($product, $record));
                    activity()
                        ->performedOn($product)
                        ->causedBy(Auth::user())
                        ->withProperties([
                            'old' => $humanReadableOldData,
                            'attributes' => $humanReadableData
                        ])
                        ->log("Admin updated stock for '{$product->name}' (PC: {$record->name}) - {$humanReadableData['Stock Management']} with {$humanReadableData['Batch Count']} batches");
                }
            })
            ->form(function ($record) use ($product) {
                $userId = $record->id;
                $productData = $product->productDataForPc($userId);
                $batchDetail = ProductRelationStock::where('product_relation_id', $productData?->id)->first();
                return [
                    Section::make()->schema([
                        Group::make()->schema([
                            Placeholder::make('name')
                                ->label('Product Name')
                                ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent'])
                                ->content(function () use ($product) {
                                    return $product->name ?? "";
                                }),
                            Placeholder::make('unit')
                                ->label('Volume Unit')
                                ->content(function ($record) use ($product) {
                                    return $product->unit?->name ?? "";
                                })
                                ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent']),
                            TextInput::make('sku')
                                ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                                ->validationAttribute('SKU')
                                ->rules([
                                    'required',
                                    'string',
                                ])
                                ->formatStateUsing(function () use ($userId, $product) {

                                    return $product->productDataForPc($userId)->sku ?? "";
                                }),
                            TextInput::make('low_stock')
                                ->label(function () use ($product) {
                                    $containerName = $product->container?->name ?? '';
                                    return new HtmlString("Low Stock Trigger Value by {$containerName} <span class='text-red-500' style='color:red;'>*</span>");
                                })
                                ->numeric()
                                ->validationAttribute('low stock')
                                ->rules(['integer', 'required', 'gt:0'])
                                ->default('-')
                                ->formatStateUsing(function () use ($userId, $product) {
                                    $productRelation = $product->productDataForPc($userId);
                                    return ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->low_stock;
                                }),
                            Toggle::make('productData.is_batch_wise_stock')
                                ->label('By Batch')
                                ->inline(false)
                                ->extraAttributes(['style' => 'marign-top: 30px; !important'])
                                // ->disabled(function() use ($batchDetail) {
                                //     return $batchDetail?->stock > 0;
                                // })
                                // ->helperText(function() use ($batchDetail) {
                                //     return $batchDetail?->stock > 0 
                                //         ? 'Cannot change the low stock trigger value when there is existing stock. Please clear stock first.'
                                //         : null;
                                // })
                                ->live()
                                ->formatStateUsing(function ($record) use ($batchDetail) {
                                    return $batchDetail?->is_batch_wise_stock;
                                }),
                            Select::make('stock_status')
                                ->options([
                                    'in_stock' => 'In Stock',
                                    'out_of_stock' => 'Out of Stock',
                                ])
                                ->afterStateUpdated(function (Get $get) {
                                    if ($get('stock_status') == 'out_of_stock') {
                                        Notification::make()
                                            ->body('This will remove all the current stock data !')
                                            ->info()
                                            ->send();
                                    }
                                })
                                ->formatStateUsing(function () use ($userId, $product) {
                                    $productRelation = $product->productDataForPc($userId);
                                    $stockData = ProductRelationStock::where(['product_relation_id' => $productRelation?->id])->first();
                                    if ($stockData?->is_batch_wise_stock) {
                                        $currentStock = ProductBatch::where(['product_id' => $product->id, 'user_id' => $userId])->sum('available_stock');

                                        if ($currentStock > 0) {
                                            return 'in_stock';
                                        } else {
                                            return 'out_of_stock';
                                        }
                                    } else {
                                        $currentStock = $stockData?->stock;
                                        if ($currentStock > 0) {
                                            return 'in_stock';
                                        } else {
                                            return 'out_of_stock';
                                        }
                                    }
                                })
                                ->live(),
                            TextInput::make('wholesale_pack_size')
                                ->visible(fn(Get $get) => $get('stock_type') == 'wps')
                                ->rules(function (Get $get) {
                                    if ($get('stock_type') == 'wps') {
                                        return ['integer', 'required', 'gt:1'];
                                    }
                                    return ['integer', 'required', 'gt:0'];
                                })

                                ->columnSpan(2)
                                ->live()
                                ->formatStateUsing(function ($record, Get $get) use ($userId, $product) {
                                    $productRelation = $product->productDataForPc($userId);
                                    return ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->wholesale_pack_size ?? null;
                                })
                                ->prefix(function ($record, Get $get) use ($userId, $product) {
                                    return new HtmlString("<div class='text-sm font-semibold text-gray-600'>" . Str::plural($product->container?->name ?? '') . " of</div>");
                                })
                                ->suffix(function ($record, Get $get) use ($userId, $product) {
                                    $productRelation = $product->productDataForPc($userId);
                                    $qty = $product->quantity_per_unit;
                                    if ($get('wholesale_pack_size') && $get('wholesale_pack_size') > 0) {
                                        $qty = $qty * (int) $get('wholesale_pack_size');
                                    }
                                    if (!empty($record?->wholesale_pack_size) && $record->wholesale_pack_size > 0) {
                                        $qty = $qty * (int) $record->wholesale_pack_size;
                                    }
                                    return new HtmlString("<div class='text-sm font-semibold text-gray-600'>{$qty} " . Str::plural($product->foam->name ?? '') . "</div>");
                                }),
                            Radio::make('stock_type')
                                ->label('Stock Type')
                                ->options([
                                    'unit' => 'Unit',
                                    'wps' => new HtmlString('Wholesale Pack<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Indicates the quantity of items included in one wholesale unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                            </svg>'),
                                ])
                                ->live()
                                ->inline()
                                ->rules(['required'])
                                ->columnSpanFull()
                                ->validationAttribute('stock type')
                                ->formatStateUsing(function ($record) use ($userId, $product) {
                                    $productRelation = $product?->productDataForPc($userId);
                                    $stock = ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->stock_type ?? 'unit';
                                    return $stock;
                                }),
                        ])->columns(4),
                    ]),
                    Section::make()
                        ->heading(fn(Get $get) => new HtmlString(
                            '<div class="flex items-center justify-between w-full">
        <span class="text-base font-bold text-gray-900">Manage Batch</span>
        <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                                collect($get('batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                            ) . '</span>
    </div>'
                        ))
                        ->visible(fn(Get $get) => $get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                        ->schema(function (Get $get) use ($userId, $product) {
                            return [
                                TableRepeater::make('batches')
                                    ->live()
                                    ->defaultItems(1)
                                    ->default(function () use ($userId, $product) {

                                        $productRelationId = ProductRelation::where('product_id', $product->id)->where('user_id', $userId)->first()->id;
                                        return $product?->batches?->where('user_id', $userId)->where('products_relation_id', $productRelationId)->map(fn($batch) => [
                                            'batch_name' => $batch?->batch_name,
                                            'available_stock' => $batch?->available_stock,
                                            'expiry_date' => $batch?->expiry_date,
                                        ])->toArray() ?? [['batch_name' => '', 'available_stock' => '', 'expiry_date' => '']];
                                    })
                                    ->minItems(fn(Get $get) => $get('productData.is_batch_wise_stock') ? 1 : 0)
                                    ->addActionAlignment(Alignment::End)
                                    ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                        return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                            ->extraAttributes([
                                                'style' => 'border: none !important; box-shadow: none !important;'
                                            ]);
                                    })
                                    ->reorderable(false)
                                    ->headers([
                                        Header::make('batch_name')->label('Batch Number'),
                                        Header::make('available_stock')->label('Stock by Packaging'),
                                        Header::make('expiry_date')->label('Expiry Date'),
                                    ])
                                    ->schema(function (Get $get, $record) use ($product) {
                                        return [
                                            TextInput::make('batch_name')
                                                ->label('Batch Name')
                                                ->rules(function ($record) use ($get, $product) {
                                                    // dd($product);
                                                    return $get('productData.is_batch_wise_stock')
                                                        ? ['required', 'string', 'max:255', Rule::unique('products_batch', 'batch_name')->ignore($product?->id, 'product_id')]
                                                        : ['nullable', 'string', 'max:255', Rule::unique('products_batch', 'batch_name')->ignore($product?->id, 'product_id')];
                                                })
                                                ->validationAttribute('Batch Name'),
                                            TextInput::make('available_stock')
                                                ->label('Available Stock')
                                                ->rules(function () use ($get) {
                                                    return $get('productData.is_batch_wise_stock')
                                                        ? ['required', 'numeric', 'max:999999', 'gt:0']
                                                        : ['nullable', 'numeric', 'max:999999', 'gt:0'];
                                                })
                                                ->numeric(),
                                            DatePicker::make('expiry_date')
                                                ->placeholder('Select the expiry date')
                                                ->label('Expiry Date')
                                                ->minDate(today())
                                                ->rules(function () use ($get) {
                                                    return $get('productData.is_batch_wise_stock')
                                                        ? ['required', 'date', 'after_or_equal:today']
                                                        : ['nullable', 'date', 'after_or_equal:today'];
                                                })
                                                ->validationMessages([
                                                    'after_or_equal' => 'The expiry date must be after or equal to today.',
                                                ])
                                                ->validationAttribute('Expiry Date')
                                        ];
                                    })
                                    ->reactive()

                            ];
                        }),
                    Group::make()
                        ->visible(fn(Get $get) => !$get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('productData.stock')
                                    ->label(function () use ($product) {
                                        $containerName = $product->container?->name ?? '';
                                        return new HtmlString("Stock by {$containerName}<span class='font-bold text-red-500'>*</span>");
                                    })
                                    ->validationAttribute('Stock')

                                    ->rules(function ($record, Get $get) {
                                        $lowStock = $record->productData?->low_stock ?? 0;
                                        if (!$get('productData.is_batch_wise_stock')) {
                                            return ['required', 'numeric', 'max:999999', 'gt:' . $lowStock];
                                        }
                                        return [];
                                    })
                                    ->formatStateUsing(function ($record) use ($batchDetail) {
                                        // dd($batchDetail);
                                        return $batchDetail->stock ?? null;
                                    }),
                                DatePicker::make('productData.expiry_date')
                                    ->placeholder('Select the expiry date')
                                    ->label(new HtmlString('Expiry Date<span class="font-bold text-red-500">*</span>'))
                                    ->minDate(today())
                                    ->rules(function (Get $get) {
                                        if (!$get('productData.is_batch_wise_stock')) {
                                            return ['required', 'date', 'after_or_equal:today'];
                                        }
                                        return [];
                                    })
                                    ->validationAttribute('Expiry Date')
                                    ->rules(function (Get $get) {
                                        if (!$get('productData.is_batch_wise_stock')) {
                                            return ['required', 'date', 'after_or_equal:today'];
                                        }
                                        return [];
                                    })
                                    ->validationMessages([
                                        'after_or_equal' => 'The expiry date must be after or equal to today.',
                                    ])
                                    ->default(function ($record) use ($batchDetail) {
                                        return $batchDetail->expiry_date ?? null;
                                    })
                            ])->columns(2)
                        ]),

                ];
            });
    }
}
