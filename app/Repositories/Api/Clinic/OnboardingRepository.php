<?php

namespace App\Repositories\Api\Clinic;


use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\V1\UserAddressResource;
use App\Jobs\SuperAdminNotificationJob;
use App\Mail\FacilityProfileApprovalSelfMail;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use App\Models\ClinicCertificateFile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\ClinicDetail;
use App\Models\UserAddress;
use App\Models\ThreadMessage;
use App\Models\Approval;
use App\Models\User;
use App\Models\ZipCode;
use Carbon\Carbon;
use Exception;

class OnboardingRepository
{
    public function save($request)
    {
        try {
            $step = $request->input('completed_step');
            $auth = Auth::user();
            $authId =$auth->id;
            // Retrieve the existing onboarding record or create a new one if it doesn't exist
            $onboarding = ClinicDetail::where('user_id', $authId)->first();
            if ($step == '1') {
                return $this->firstStep($request,$authId);
            }

            if(!$onboarding) {
                return [
                    'success'   => false,
                    'message'   => __('api.clinic.onboarding_not_found'),
                    'code'      => Response::HTTP_NOT_FOUND
                ];
            }

            if ($step == '2') {
                $onboardingValues =  $this->secondStep($request, $authId);
                $msg =  __('api.clinic.onboarding_add_step');
            }
            if ($step == '3') {
                $onboardingValues =  $this->thirdStep($request,$onboarding);
                $msg =  __('api.clinic.onboarding_dc_step');
            }
            if ($step == '4') {
                $onboardingValues =  $this->fourthStep($request,$onboarding);
                unset($onboardingValues['is_approve']);
                $msg =  __('api.clinic.onboarding_doc_step');

            }
            if ($step == '5') {
                $defaultAdd = UserAddress::where('user_id', Auth::user()->id)->where('is_default', true)->first();
                $onboardingValues =  [
                    "is_declare_info" => $request->is_declare_info,
                    "is_term" => $request->is_term,
                    'reject_reason' =>  null,
                    'is_submitted' => true,
                    'zone' =>  $defaultAdd->state->zone ?? 'east', // need to remove this
                ];

                $auth->update(['verification_status' =>  'send_for_approval']);

                SuperAdminNotificationJob::dispatch('new_facility_onboarding',Auth::user()->id,Auth::user());   

                $msg =  __('api.clinic.onboarding_5th_step');
            }
            $onboarding['completed_step'] =  $request->input('completed_step');
            $onboarding['updated_at'] =  now();

            if(isset($request->from_review_page) && $request->from_review_page){ 
                
                unset($onboarding['completed_step']);
                $onboardingValues['reject_reason'] = null;
                $this->ClinicUserDetailsUpdate();
                if(!$onboarding->reject_reason){
                    $onboardingValues['completed_step'] = 4;
                }
            }
            $onboarding->update($onboardingValues);
            $response  = [
                'success'   => true,
                'message'   => $msg,
                'code'      => Response::HTTP_OK
            ];


        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }

    public function basicInfo($request,$authId) {
        try{
            $existingData = ClinicDetail::where('user_id', $authId)->first();
            $newData = [];
            $originalData = [];
            $fields = [
                'completed_step' => $request->input('completed_step') ?? $existingData->completed_step ?? null,
                'business_type_id' => decryptParam($request->input('business_type_id')),
                'clinic_name' => $request->input('clinic_name') ?? null,
                'clinic_number' => $request->input('clinic_number'),
                'company_name' => $request->input('company_name') ?? null,
                'company_number' => $request->input('company_number') ?? null,
                'clinic_owner' => $request->input('clinic_owner') ?? null,
                'clinic_year' => $request->input('clinic_year'),
                'tin_number' => $request->input('tin_number') ?? null,
                'sst_number' => $request->input('sst_number') ?? null,
                'mobile_code' => $request->input('mobile_code') ?? null,
                'mobile_number' => $request->input('mobile_number') ?? null,
                'landline_code' => $request->input('landline_code') ?? null,
                'landline_number' => $request->input('landline_number') ?? null,
            ];

            if ($existingData) {
                foreach ($fields as $key => $value) {
                    if ($existingData->$key != $value) {
                        $newData[$key] = $value;
                        $originalData[$key] = $existingData->$key;
                    }
                }
            }

            if (!empty($newData)) {
                $this->approvalData($newData,$originalData,"1");
            }

            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.basic_info'),
                'code'      => Response::HTTP_CREATED
            ];
        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }
    public function addressUpdate($request,$authId)
    {
        DB::beginTransaction();
        try {
            
            $existingData = UserAddress::where('user_id', $authId)->where('address_type' , 'billing')->first();
            $newData = [];
            $originalData = [];
            //For billing address
            $fields = [
                'address_1' => $request->input('b_address_1'),
                'address_2' => $request->input('b_address_2') ?? null,
                'postal_code' => $request->input('b_postal_code'),
                'country_id' => decryptParam($request->input('b_country_id')),
                'state_id' => decryptParam($request->input('b_state_id')),
                'city_id' => decryptParam($request->input('b_city_id')),
                
            ];

            if ($existingData) {
                foreach ($fields as $key => $value) {
                    if ($existingData->$key != $value) {
                        $newData[$key] = $value;
                        $originalData[$key] = $existingData->$key;
                    }
                }
            }
            if (!empty($newData)) {
                $newData['address_type'] = 'billing';
                $this->approvalData($newData,$originalData,"2");
            }

            //$existingData->update(['nick_name' => $request->input('b_nick_name')]);
            //For shipping address
            $shippingAddData = [];
            $shippingOriginalData = [];
            foreach ($request['addresses'] as $key => $address) {
                $this->zipCodeValid($address['postal_code'],decryptParam($address['city_id']));

                $fields = [
                    'address_1' => $address['address_1'],
                    'address_2' => $address['address_2'] ?? null,
                    'postal_code' => $address['postal_code'],
                    'nick_name' => $address['nick_name'] ?? null,
                    'country_id' => decryptParam($address['country_id']),
                    'state_id' => decryptParam($address['state_id']),
                    'city_id' => decryptParam($address['city_id']),
                    'is_onboarding'=>  true,
                    'address_type'=>'shipping',
                    'is_default'=> isset($address['is_default']) ? $address['is_default'] : false,
                    'is_same_as_billing'=> isset($address['is_same_as_billing']) ? $address['is_same_as_billing'] : false,
                ];
               
                    if(isset($address['id']) && $address['id'] != null) {
                        $existingData =  UserAddress::where('id', decryptParam($address['id']))
                                                        ->where('address_type','shipping')->first();
                        if ($existingData) { 
                            $existingData->update(['nick_name' => $address['nick_name']]);
                            unset($fields['nick_name']);
                            foreach ($fields as $fieldKey => $value) {
                                if ($existingData->$fieldKey != $value && ($address['is_same_as_billing'] != $existingData->is_same_as_billing || !$address['is_same_as_billing'])) {
                                    $shippingAddData[$key][$fieldKey] = $value;
                                    $shippingOriginalData[$key][$fieldKey] = $existingData->$fieldKey;
                                }
                                
                            }
                            if($address['is_same_as_billing'] = $existingData->is_same_as_billing &&
                                $address['is_default'] != $existingData->is_default) { 
                                    $shippingAddData[$key]['is_default'] = $address['is_default'];
                            }
                            if(isset($shippingAddData[$key])) {
                                $shippingAddData[$key]['id'] = decryptParam($address['id']);
                            }
                            
                        }else{
                            $shippingAddData[$key] = $fields;
                        }
                    }else{
                        $shippingAddData[$key] = $fields;
                    }
                
            }
            if (!empty($shippingAddData)) {
                $shippingAddData['address_type'] = 'shipping';
                $this->approvalData($shippingAddData,$shippingOriginalData,"2");

            }
            DB::commit();

            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.profile_address'),
                'code'      => Response::HTTP_CREATED
            ];

            SuperAdminNotificationJob::dispatch('facility_approve',Auth::user()->id);   
            
            $newData = array_merge($newData, $shippingAddData);
            Mail::to(Auth::user()->email)->send(new FacilityProfileApprovalSelfMail(Auth::user(),$newData, "2"));
        } catch (\Exception $e) {
            DB::rollBack();

            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }

    public function doctorInchargeUpdate($request)
    {
        try{
            $authId = Auth::user()->id;
            $existingData = ClinicDetail::where('user_id', $authId)->first();
            $newData = [];
            $originalData = [];
            $path = null;
            // Handle image uploads
            if ($request->file('dc_signature')) {
                $folderPath = config('constants.api.media.clinic_medias'). $existingData->id;
                $currentDate = Carbon::now()->timestamp;
                    $path = uploadFile($request->file('dc_signature'), $folderPath, true,100,100,"dc_signature_".$currentDate);
                    $existingData->dc_signature = $path;
                    $existingData->signature_type =  $request->input('signature_type') ?? false;
                    $existingData->save();
            }

            $fields = [
                'dc_name' => $request->input('dc_name'),
                'dc_nric' => $request->input('dc_nric') ?? null,
                'dc_mmc_number' => $request->input('dc_mmc_number'),
                'dc_apc_number' => $request->input('dc_apc_number') ?? null,
                'dc_phone_code' => $request->input('dc_phone_code'),
                'dc_phone_number' => $request->input('dc_phone_number'),
                'dc_landline_code' => $request->input('dc_landline_code'),
                'dc_landline_number' => $request->input('dc_landline_number'),
                'is_admin_in_charge' => $request->input('is_admin_in_charge'),
                'ac_name' => $request->input('ac_name') ?? null,
                'ac_nric' => $request->input('ac_nric') ?? null,
                'ac_phone_code' => $request->input('ac_phone_code') ?? null,
                'ac_phone_number' => $request->input('ac_phone_number') ?? null,
                'ac_landline_code' => $request->input('ac_landline_code') ?? null,
                'ac_landline_number' => $request->input('ac_landline_number') ?? null,
                'apc_certificate_expired_date' =>  $request->input('apc_certificate_expired_date') ?? null

            ];

            if ($existingData) {
                foreach ($fields as $key => $value) {
                    if ($existingData->$key != $value) {
                        $newData[$key] = $value;
                        $originalData[$key] = $existingData->$key;
                    }
                }
            }

            if (!empty($newData)) {
                $this->approvalData($newData,$originalData,"3");
            }

            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.profile_doc_in_charge'),
                'dc_signature' => $path ? Storage::url($folderPath.'/'.$path) : null,
                'code'      => Response::HTTP_CREATED
            ];
        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }

    public function documentsUpdate($request)
    {
        try {
            $clinicDataId = Auth::user()->clinicData->id;
            $newData = [];
            $folderPath = config('constants.api.media.clinic_medias').$clinicDataId;
            $currentDate = Carbon::now()->timestamp;
            $certificates = [
                'borang_certificate',
                'mmc_certificate',
                'apc_certificate',
                'arc_certificate',
                'poison_license',
                'other_relevant_documents',
            ];

            foreach ($certificates as $type) {

                if ($request->hasFile($type)) {
                    $files = is_array($request->file($type)) ? $request->file($type) : [$request->file($type)];

                    foreach ($files as $fileKey => $file) {
                        $fileName = "{$type}_{$fileKey}_{$currentDate}";
                        $filePath = uploadFile($file, $folderPath, false, 100, 100, $fileName);
                        $fileSize = $file->getSize();

                        $newData[$type][$fileKey] = [
                            'user_id' => Auth::user()->id,
                            'type' => $type,
                            'status' => 'active',
                            'name' => $filePath,
                            'size'=>$fileSize
                        ];
                    }
                }
            }
            if($request->removed_borang_certificate){
                $newData['removed_borang_certificate'] = decryptParam($request->removed_borang_certificate);
            }
            if($request->removed_mmc_certificate){
                $newData['removed_mmc_certificate'] = decryptParam($request->removed_mmc_certificate);
            }
            if($request->removed_apc_certificate){
                $newData['removed_apc_certificate'] = decryptParam($request->removed_apc_certificate);
            }
            if($request->removed_arc_certificate){
                $newData['removed_arc_certificate'] = decryptParam($request->removed_arc_certificate);
            }
            if($request->removed_poison_license){
                $newData['removed_poison_license'] = decryptParam($request->removed_poison_license);
            }
            if($request->removed_other_relevant_documents){
                $newData['removed_other_relevant_documents'] = decryptParam($request->removed_other_relevant_documents);
            }
            if (!empty($newData)) {
                $this->approvalData($newData,[],"4");
            }
            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.profile_document'),
                'code'      => Response::HTTP_CREATED
            ];
        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }
    public function firstStep($request,$authId)
    {
        try{
            $attributes = [
                'user_id' => $authId,
            ];

            $existingData = ClinicDetail::where($attributes)->first();
            $isApproved = false;

            $values = [
                'completed_step' => $request->input('completed_step') ?? $existingData->completed_step ?? null,
                'business_type_id' => decryptParam($request->input('business_type_id')),
                'clinic_name' => $request->input('clinic_name') ?? null,
                'clinic_number' => $request->input('clinic_number'),
                'mobile_code' => $request->input('mobile_code') ?? null,
                'mobile_number' => $request->input('mobile_number') ?? null,
                'landline_code' => $request->input('landline_code'),
                'landline_number' => $request->input('landline_number'),
                'company_name' => $request->input('company_name') ?? null,
                'company_number' => $request->input('company_number') ?? null,
                'clinic_owner' => $request->input('clinic_owner') ?? null,
                'clinic_year' => $request->input('clinic_year'),
                'tin_number' => $request->input('tin_number') ?? null,
                'sst_number' => $request->input('sst_number') ?? null,
                'reject_reason' =>  null,
            ];

            if ($existingData) {
                $fieldsToCheck = [
                    'business_type_id',
                    'clinic_name',
                    'clinic_number',
                    'company_name',
                    'company_number',
                    'clinic_owner',
                    'clinic_year',
                    'tin_number',
                    'sst_number',
                ];

                foreach ($fieldsToCheck as $field) {
                    if ($existingData->$field != $values[$field]) {
                        $isApproved = true;
                        break;
                    }
                }
            }

            // if($isApproved){
            //     $this->ClinicUserDetailsUpdate();
            // }
            if(isset($request->from_review_page) && $request->from_review_page){
                unset($values['completed_step']);
                $this->ClinicUserDetailsUpdate();
                if(!$existingData->reject_reason){
                    $values['completed_step'] = 4;
                }
            }
            $onboarding = ClinicDetail::updateOrCreate($attributes, $values);

            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.basic_details'),
                'data'      => ['clinic_onboarding_id' => encryptParam($onboarding->id)],
                'is_approved' => $isApproved,
                'code'      => Response::HTTP_CREATED
            ];
        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }

    public function secondStep($request, $authId)
    {
        $onboardingValues = [];
        DB::beginTransaction();
        try {
            $hasDefault = collect($request['addresses'])->contains('is_default', true);
            if(!$hasDefault){
                throw new Exception(_('api.clinic.valid_default_add'));
            }

            if(isset($request['remove_addresses']) && !empty($request['remove_addresses'])) {
                foreach ($request['remove_addresses'] as  $removeId) {
                    $address = UserAddress::find(decryptParam($removeId));
                    if($address) {
                        $address->delete();
                    }
                }
            }

            $attributes = [
                'address_type' => 'billing',
                'user_id' => $authId,
                'is_onboarding'=> true,
            ];
            $values = [
                'address_1' => $request->input('b_address_1'),
                'address_2' => $request->input('b_address_2') ?? null,
                'postal_code' => $request->input('b_postal_code'),
                'country_id' => decryptParam($request->input('b_country_id')),
                'state_id' => decryptParam($request->input('b_state_id')),
                'city_id' => decryptParam($request->input('b_city_id')),
                //'nick_name' => $request->input('b_nick_name') ?? null,
            ];
            $userBillAdd = UserAddress::updateOrCreate($attributes, $values);

            foreach ($request['addresses'] as $key => $address) {

                $this->zipCodeValid($address['postal_code'],decryptParam($address['city_id']));
                $values = [
                    'address_1' => $address['address_1'],
                    'address_2' => $address['address_2'] ?? null,
                    'postal_code' => $address['postal_code'],
                    'nick_name' => $address['nick_name'] ?? null,
                    'country_id' => decryptParam($address['country_id']),
                    'state_id' => decryptParam($address['state_id']),
                    'city_id' => decryptParam($address['city_id']),
                    'is_onboarding'=>  true,
                    'address_type'=>'shipping',
                    'user_id'=>$authId,
                    'is_default'=> isset($address['is_default']) ? $address['is_default'] : false,
                    'is_same_as_billing'=> isset($address['is_same_as_billing']) ? $address['is_same_as_billing'] : false,
                ];

                if(isset($address['id']) && $address['id'] != null) {
                    $userShipAdd= UserAddress::where('id', decryptParam($address['id']))
                                                ->where('address_type','shipping')
                                                ->first();

                    $userShipAdd->update($values);
                }else{
                    $userShipAdd = UserAddress::create($values);
                }

                if($address['is_default'] == true) {
                    $onboardingValues = [
                            'is_billing_address_same' => false,
                            'shipping_addresses_id' => $userShipAdd->id,
                            'billing_addresses_id' => $userBillAdd->id
                        ];
                }
            }
            DB::commit();
        }catch (\Throwable $th) {
            DB::rollBack();
            $onboardingValues = exceptionResponse($th->getMessage());
        }
        return $onboardingValues;
    }

    public function thirdStep($request , $record) {
        $path  = null;
        $folderPath = config('constants.api.media.clinic_medias'). $record->id;
        $currentDate = Carbon::now()->timestamp;
        // Handle image uploads
        if ($request->file('dc_signature')) {
                $path = uploadFile($request->file('dc_signature'), $folderPath, true,100,100,"dc_signature_".$currentDate);
        }
        $expireDate = $request->input('apc_certificate_expired_date') ?? null;

        return [
            'dc_name' => $request->input('dc_name'),
            'dc_nric' => $request->input('dc_nric') ?? null,
            'dc_mmc_number' => $request->input('dc_mmc_number'),
            'dc_apc_number' => $request->input('dc_apc_number') ?? null,
            'dc_phone_code' => $request->input('dc_phone_code'),
            'dc_phone_number' => $request->input('dc_phone_number'),
            'dc_landline_code' => $request->input('dc_landline_code'),
            'dc_landline_number' => $request->input('dc_landline_number'),
            'dc_signature' => $path ? $path : $record->dc_signature,
            'is_admin_in_charge' => $request->input('is_admin_in_charge'),
            'ac_name' => $request->input('ac_name') ?? null,
            'ac_nric' => $request->input('ac_nric') ?? null,
            'signature_type' =>  $request->input('signature_type') ?? false,
            'ac_phone_code' => $request->input('ac_phone_code') ?? null,
            'ac_phone_number' => $request->input('ac_phone_number') ?? null,
            'ac_landline_code' => $request->input('ac_landline_code') ?? null,
            'ac_landline_number' => $request->input('ac_landline_number') ?? null,
            'apc_certificate_expired_date' => $expireDate,

        ];
    }

    public function fourthStep($request , $record)
    {

        $folderPath = config('constants.api.media.clinic_medias') . $record->id;
        $currentDate = Carbon::now()->timestamp;
        $isApprove = false;
        // Handle image uploads
        $certificates = [
            'borang_certificate' => true,
            'mmc_certificate' => true,
            'apc_certificate' => false,
            'arc_certificate' => true,
            'poison_license' => true,
            'other_relevant_documents' => true,
        ];

        foreach ($certificates as $type => $approveFlag) {

            if ($request->hasFile($type)) {
                // if($type != 'borang_certificate'){
                //     $this->deleteCertificateByPath($type,$folderPath);
                // }

                $files = is_array($request->file($type)) ? $request->file($type) : [$request->file($type)];

                foreach ($files as $key => $file) {
                    $fileName = "{$type}_{$key}_{$currentDate}";
                    $filePath = uploadFile($file, $folderPath, false, 100, 100, $fileName);
                    $fileSize = $file->getSize();
                    ClinicCertificateFile::create([
                        'user_id' => Auth::user()->id,
                        'type' => $type,
                        'status' => 'active',
                        'name' => $filePath,
                        'size'=>$fileSize
                    ]);

                    if ($approveFlag) {
                        $isApprove = true;
                    }
                }
            }
        }

        if($request->removed_borang_certificate){
            $this->deleteCertificateById($request->removed_borang_certificate,$folderPath);
        }
        if($request->removed_mmc_certificate){
            $this->deleteCertificateById($request->removed_mmc_certificate,$folderPath);
        }
        if($request->removed_apc_certificate){
            $this->deleteCertificateById($request->removed_apc_certificate,$folderPath);
        }
        if($request->removed_arc_certificate){
            $this->deleteCertificateById($request->removed_arc_certificate,$folderPath);
        }
        if($request->removed_poison_license){
            $this->deleteCertificateById($request->removed_poison_license,$folderPath);
        } 
        if($request->removed_other_relevant_documents){
            $this->deleteCertificateById($request->removed_other_relevant_documents,$folderPath);
        }
        if ($isApprove) {
            $this->ClinicUserDetailsUpdate();
        }

        return ['is_approve' => $isApprove];
    }

    public function ClinicUserDetailsUpdate()
    {
        User::where('id',Auth::user()->id)->update([
            'is_admin_verified' => false,
            'verification_status' =>  'send_for_approval'
        ]);

        return true;
    }

    public function deleteCertificateById($ids,$path) {

        $docs =  ClinicCertificateFile::whereIn('id',decryptParam($ids))->get();
        return $this->removeDoc($docs,$path);
    }
    public function deleteCertificateByPath($type,$path) {

        $docs =  ClinicCertificateFile::where('type',$type)
                ->where('user_id', Auth::user()->id)->get();
        return $this->removeDoc($docs,$path);
    }

    public function removeDoc($docs,$path){
        if($docs)
        {
            foreach ($docs as $key => $doc) {
                deleteImage($doc->name, $path);
                $doc->delete();
            }
        }
        return true;
    }

    public function certificateUpdate($request)
    {
        try {
            $authId = Auth::user()->id;
            $onboarding = ClinicDetail::where('user_id', $authId)->first();
            $folderPath = config('constants.api.media.clinic_medias') . $onboarding->id;
            $currentDate = Carbon::now()->timestamp;

            if ($request->file('apc_certificate')) {
                $apcPath = uploadFile($request->file('apc_certificate'), $folderPath, false,100,100,"apc_certificate_".$currentDate);

            }
            $onboarding->update([
                'apc_certificate' => $apcPath,
                'apc_certificate_expired_date' => $request->input('apc_certificate_expired_date'),
            ]);
            $response  = [
                'success'   => true,
                'message'   => __('api.clinic.apc_certificate_updated'),
                'code'      => Response::HTTP_OK
            ];

        } catch (\Exception $e) {
            $response  = [
                'success'   => false,
                'message'   =>__($e->getMessage()),
                'code'      => Response::HTTP_INTERNAL_SERVER_ERROR
            ];
        }
        return $response;
    }

    public function shippingAddress($authId,$isOnboarding = true)
    {
        $auth = Auth::user();
        //$clinicData = $auth->clinicData;

        $query = UserAddress::with(['country','city','state'])->where('user_id',$authId)
                                        ->where('is_approved','true')
                                        ->where('status','approved')
                                        ->where('address_type','shipping');
        /* if($clinicData->shipping_addresses_id != $clinicData->billing_addresses_id) {
            $query->where('address_type','shipping');
        } */
        if($isOnboarding){
            $query->where('is_onboarding',true);
        }

        $shippingAddress = $query->orderBy('id', 'asc')->get();
        return  $shippingAddress ? new CustomCollection($shippingAddress, UserAddressResource::class) : null;
    }
    public function addresses($authId)
    {
        $query = UserAddress::with(['country','city','state'])
                            ->where('user_id',$authId)
                            ->where('is_approved','true')
                            ->where('status','approved');

        $billingAddress = $query->orderBy('id', 'asc')->get();
        return  $billingAddress ? new CustomCollection($billingAddress, UserAddressResource::class) : null;
    }

    public function termConditions($authId)
    {

        return DB::selectOne("
            SELECT tc.status
            FROM term_conditions tc
            JOIN user_term_conditions utc ON tc.id = utc.term_condition_id
            WHERE utc.user_id = ?
            AND tc.status = true
            ORDER BY tc.id ASC
            LIMIT 1
        ", [$authId]) ?? false;
    }

    public function unReadMsgCount($authId){
        return ThreadMessage::whereHas('ticket', function ($query) use($authId) {
            $query->where('sender_id',$authId);
        })
        ->where('is_read', false)
        ->whereNot('from_id',$authId)
        ->count();
    }

    public function approvalData($newData,$originalData, $step){
        $auth = Auth::user();
        $authId = $auth->id;
        Approval::create([
            "approvalable_type" => "App\Models\User",
            "approvalable_id" => $authId,
            "status" => "pending",
            "new_data" => json_encode($newData),
            "original_data" => json_encode($originalData),
            "steps" => $step,
        ]);

        if ($step != "2") {
            SuperAdminNotificationJob::dispatch('facility_approve_not_step_2',$authId,$auth);
            Mail::to($auth->email)->send(new FacilityProfileApprovalSelfMail(Auth::user(),$newData, $step));
        }
    }

    public function zipCodeValid($postal_code,$city_id)
    {
        $isValid = ZipCode::where('city_id', $city_id)
                         ->where('code', $postal_code)
                         ->exists();

        throw_if(!$isValid, Exception::class, __('api.clinic.postal_code_invalid',['code' => $postal_code]));

    }
}
