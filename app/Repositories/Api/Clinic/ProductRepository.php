<?php

namespace App\Repositories\Api\Clinic;

use App\Models\Category;
use Illuminate\Database\Eloquent\Builder;
use App\Models\ClinicRecentSearch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;


class ProductRepository
{
    public function products($request){

        $auth = Auth::user();
        $authId = $auth->id;
        $zone = getClinicZone();

        $key = $request->key ?? '';
        $value = $request->value ?? '';
        $query = $this->baseQuery($request,$zone,$authId);

        // Apply sorting based on the request->sort parameter
        if ($request->has('sort')) {
            switch ($request->sort) {
                case 'oldest':
                    $query->orderBy('products.created_at', 'asc');
                    break;

                    case 'high':
                        $query->orderBy(DB::raw("
                            (
                                SELECT MAX(
                                    CASE 
                                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                                        WHEN pr.price_type = 'tier' THEN GREATEST(
                                            COALESCE(prp.{$zone}_tier_1_base_price, 0), 
                                            COALESCE(prp.{$zone}_tier_2_base_price, 0), 
                                            COALESCE(prp.{$zone}_tier_3_base_price, 0)
                                        )
                                    END
                                )
                                FROM products_relation pr
                                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                                WHERE pr.product_id = products.id
                                AND pr.pc_approval = true
                                AND pr.admin_approval = true
                            )
                        "), 'desc');
                        break;
            
                    case 'low':
                        $query->orderBy(DB::raw("
                            (
                                SELECT MIN(
                                    CASE 
                                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                                        WHEN pr.price_type = 'tier' THEN LEAST(
                                            COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                                            COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                                            COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                                        )
                                    END
                                )
                                FROM products_relation pr
                                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                                WHERE pr.product_id = products.id
                                AND pr.pc_approval = true
                                AND pr.admin_approval = true
                            )
                        "), 'asc');
                        break;
                default:
                    $query->orderBy('products.created_at', 'desc');
                    break;
            }
        } else {
            $query->orderBy('products.created_at', 'desc');
        }
         // Handle category and sub-category filters
        if ($request->has('price_min') && $request->has('price_max')) {
            $priceMin = $request->price_min;
            $priceMax = $request->price_max;
        
            $query->whereRaw("
            (
                    SELECT MIN(
                        CASE 
                            WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                            WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                            WHEN pr.price_type = 'tier' THEN LEAST(
                                COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                                COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                                COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                            )
                        END
                    )
                    FROM products_relation pr
                    JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                    WHERE pr.product_id = products.id
                    AND pr.pc_approval = true
                    AND pr.admin_approval = true
                ) BETWEEN ? AND ?
            ", [$priceMin, $priceMax]);
    
        }
            if ($request->has('categories') && is_array($request->categories)) {
                $categories = $request->categories;
                $query->whereIn('main_category.name', $categories);
            }
            
            if ($request->has('sub_categories') && is_array($request->sub_categories)) {
                $sub_categories = $request->sub_categories;
                $query->whereIn('sub_category.name', $sub_categories);
            }
            if ($request->has('foams') && is_array($request->foams)) {
                $foams = $request->foams;
                $query->whereIn('dosage_foams.name', $foams);
            }
            
            
            if (
                $request->has('exclude_out_stock') &&
                ($request->input('exclude_out_stock') === true || $request->input('exclude_out_stock') === 'true')
            ) {
                $query->whereExists(function ($subQuery) {
                    $subQuery->select(DB::raw(1))
                        ->from('products_relation as pr_stock')
                        ->join('product_relation_stocks as prs', 'prs.product_relation_id', '=', 'pr_stock.id')
                        ->whereColumn('pr_stock.product_id', 'products.id')
                        ->whereNull('pr_stock.deleted_at')
                        ->where('pr_stock.pc_approval', true)
                        ->where('pr_stock.admin_approval', true)
                        ->where(function ($cond) {
                            $cond->where(function ($q) {
                                $q->where('prs.is_batch_wise_stock', false)
                                    ->where('prs.stock', '>', 0);
                            })
                            ->orWhere(function ($q) {
                                $q->where('prs.is_batch_wise_stock', true)
                                    ->whereExists(function ($batchQuery) {
                                        $batchQuery->select(DB::raw(1))
                                            ->from('products_batch as pb')
                                            ->whereColumn('pb.products_relation_id', 'prs.product_relation_id')
                                            ->where('pb.available_stock', '>', 0);
                                    });
                            });
                        });
                });
            }

            if ($request->has('distributors') && is_array($request->distributors)) { 
                $distributors = $request->distributors;
                $query->whereExists(function ($q) use ($distributors) {
                    $q->select(DB::raw(1))
                        ->from('distributor_product as dp2')
                        ->join('distributors as d2', 'd2.id', '=', 'dp2.distributor_id')
                        ->whereColumn('dp2.product_id', 'products.id')
                        ->whereIn('d2.name', $distributors);
                });
                
            }
            // Handle search filters

            if($key && $value) {

                if ($key == 'search') {
                    $query->where(function ($subQuery) use ($value) {
                        $subQuery->where('products.name', 'ILIKE', "%{$value}%")
                            ->orWhere('generic_names.name', 'ILIKE', "%{$value}%")
                            ->orWhere('main_category.name', 'ILIKE', "%{$value}%")
                            ->orWhere('sub_category.name', 'ILIKE', "%{$value}%")
                            ->orWhere('brands.name', 'ILIKE', "%{$value}%")
                            ->orWhere('distributors.name', 'ILIKE', "%{$value}%")
                            ->orWhereExists(function ($subSubQuery) use ($value) {
                                $subSubQuery->select(DB::raw(1))
                                    ->from('pc_details')
                                    ->join('products_relation', 'products_relation.user_id', '=', 'pc_details.user_id')
                                    ->leftJoin('pc_company_types', 'pc_company_types.id', '=', 'pc_details.company_type_id')
                                    ->where(function ($nameQuery) use ($value) {
                                        $nameQuery->where('pc_details.company_name', 'ILIKE', "%{$value}%")
                                                  ->orWhere(function ($businessNameQuery) use ($value) {
                                                      $businessNameQuery->where(function ($emptyCheck) {
                                                          $emptyCheck->whereNull('pc_details.company_name')
                                                                     ->orWhere('pc_details.company_name', '');
                                                      })
                                                      ->where('pc_company_types.name', 'Sole Proprietary')
                                                      ->where('pc_details.business_name', 'ILIKE', "%{$value}%");
                                                  });
                                    });
                            });
                    });
                }elseif($key == 'generic name'){
                    $query->where('generic_names.name',$value);
                }elseif($key == 'brand'){
                    $query->where('brands.name',$value);
                }

                if($request->has('from_listing') && ($request->from_listing)) {
                     //Recent search save with 5 limit
                    ClinicRecentSearch::updateOrCreate(['user_id'=>$authId,'key'=>$key,'value'=>$value]);
                    $recentSearchCount = ClinicRecentSearch::where('user_id',$authId)->count();
                        if($recentSearchCount > 5) {
                            ClinicRecentSearch::where('user_id',$authId)->oldest()->take(1)->delete();
                        } 
                }
            }

        return $query;
    }

    public function suppliers($request,$id) {
        $auth = Auth::user();
        $authUserId = $auth->id;
        $zone = $auth->clinicDetails->zone;
        $id = decryptParam($id);
            //add is_restricted condition for pc
            $query =  DB::table('products_relation as pr')
                    ->join('users as u', 'pr.user_id', '=', 'u.id') // userDetails relation
                    ->join('products as p', 'pr.product_id', '=', 'p.id') // products relation
                    ->join('units', 'p.unit_id', '=', 'units.id') // unit relation
                    ->leftJoin('containers', 'p.container_id', '=', 'containers.id')
                    ->join('dosage_foams as df', 'p.dosage_foams_id', '=', 'df.id') // dosage_foams relation
                    ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                    ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                    ->join('pc_details as pc', function ($join) {
                        $join->on('pr.user_id', '=', 'pc.user_id');
                    })->where(function ($query) {
                        $query->where('pc.is_restricted', false) // If is_restricted is false, include the record
                              ->orWhereExists(function ($subquery) {
                                  $subquery->select(DB::raw(1))
                                      ->from('clinic_pharma_suppliers as cps')
                                      ->whereColumn('cps.pc_id', 'pc.user_id') // Ensuring user_id matches
                                       ->where('cps.clinic_id', Auth::user()->id)
                                      ->where('cps.status', 'approved'); // Only if status is "approved"
                              });
                    })
                    ->leftJoin('favourites as f', function ($join) use ($authUserId) {
                        $join->on('pr.user_id', '=', 'f.supplier_id')
                            ->where('f.user_id', '=', $authUserId);
                    })
                    ->leftJoin('clinic_add_carts as cart', function ($join) use ($authUserId) {
                        $join->on('pr.product_id', '=', 'cart.product_id')
                        ->on('pr.user_id', '=', 'cart.supplier_id')
                            ->where('cart.user_id', '=', $authUserId);
                    })
                    ->leftJoin(DB::raw("(SELECT product_id, user_id, SUM(available_stock) as batch_stock 
                                        FROM products_batch 
                                        WHERE expiry_date >= CURRENT_DATE 
                                        GROUP BY product_id, user_id) as pb"), function ($join) {
                        $join->on('pr.product_id', '=', 'pb.product_id')
                            ->on('pr.user_id', '=', 'pb.user_id');
                    })
                    ->select([
                        'pr.id as id',
                        'pr.product_id as product_id',
                        'pr.user_id as user_id',
                        'pr.is_tier_pricing as is_tier_pricing',
                        'pr.is_bonus_pricing as is_bonus_pricing',
                        'pr.expires_on_after',
                        'pr.is_admin_verified',
                        'pr.price_type',
                        
                        'u.name as user_name',
                        'u.photo',
                        'pc.business_name',
                        'pc.company_name',
                        'pc.delivery_days',
                        'pc.delivery_days_west',
                        'pc.min_order_value',
                        'p.quantity_per_unit',
                        'units.name as unit_name',
                        'df.name as dosage_foams_name',
                        'containers.name as container_name',
                        'f.id as is_fav',
                        'cart.quantity as quantity',
                        DB::raw("CASE 
                                WHEN prs.is_batch_wise_stock = true THEN COALESCE(pb.batch_stock, 0) 
                                ELSE prs.stock 
                            END as stock"),
                        'prs.wholesale_pack_size',
                        'prs.stock_type',
                        'prp.east_bonus_1_quantity',
                        'prp.east_bonus_1_quantity_value',
                        'prp.east_bonus_2_quantity',
                        'prp.east_bonus_2_quantity_value',
                        'prp.east_bonus_3_quantity',
                        'prp.east_bonus_3_quantity_value',

                        'prp.west_bonus_1_quantity',
                        'prp.west_bonus_1_quantity_value',
                        'prp.west_bonus_2_quantity',
                        'prp.west_bonus_2_quantity_value',
                        'prp.west_bonus_3_quantity',
                        'prp.west_bonus_3_quantity_value',

                        'prp.east_tier_1_min_quantity',
                        'prp.east_tier_1_max_quantity',
                        'prp.east_tier_1_base_price',
                        'prp.east_tier_2_min_quantity',
                        'prp.east_tier_2_max_quantity',
                        'prp.east_tier_2_base_price',
                        'prp.east_tier_3_min_quantity',
                        'prp.east_tier_3_max_quantity',
                        'prp.east_tier_3_base_price',

                        'prp.west_tier_1_min_quantity',
                        'prp.west_tier_1_max_quantity',
                        'prp.west_tier_1_base_price',
                        'prp.west_tier_2_min_quantity',
                        'prp.west_tier_2_max_quantity',
                        'prp.west_tier_2_base_price',
                        'prp.west_tier_3_min_quantity',
                        'prp.west_tier_3_max_quantity',
                        'prp.west_tier_3_base_price',

                        'prp.east_zone_price',
                        'prp.west_zone_price',

                       //'prs.stock',
                        ])
                   
                    ->where('pr.pc_approval', true)
                    ->where('pr.product_id', $id)
                    ->where('pr.admin_approval', true)
                    ->whereNull('pr.deleted_at')
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            // For fixed and bonus, east and west prices must be present
                            $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                            ->whereNotNull('prp.east_zone_price')
                            ->where('prp.east_zone_price', '>', 0)
                            ->whereNotNull('prp.west_zone_price')
                            ->where('prp.west_zone_price', '>', 0);
                        })->orWhere(function ($q) {
                            // For tier, all 6 tier prices must be present
                            $q->where('pr.price_type', 'tier')
                            ->whereNotNull('prp.east_tier_1_base_price')
                            ->where('prp.east_tier_1_base_price', '>', 0);
                        });
                    })
                    // Filter out products with expired dates
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->where('prs.is_batch_wise_stock', false)
                            ->where(function ($expiry) {
                                $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                            })
                            ->orWhere(function($batchWise) {
                                $batchWise->where('prs.is_batch_wise_stock', true)
                                ->whereExists(function($query) {
                                    $currentDate = date('Y-m-d');
                                    $query->select(DB::raw(1))
                                            ->from('products_batch')
                                            ->whereColumn('products_batch.product_id', 'pr.product_id')
                                            ->whereColumn('products_batch.user_id', 'pr.user_id')
                                            ->where(DB::raw("DATE(products_batch.expiry_date)"), '>=', $currentDate)
                                            ->where('products_batch.available_stock', '>', 0);
                                });
                            });
                        });
                    })
                    ->groupBy([
                        'pr.id',
                        'u.name',
                        'u.photo',
                        'pc.business_name',
                        'pc.company_name',
                        'pc.delivery_days',
                        'pc.delivery_days_west',
                        'pc.min_order_value',
                        'p.quantity_per_unit',
                        'f.id',
                        'units.name',
                        'df.name',
                        'containers.name',
                        'cart.quantity',
                        'prs.wholesale_pack_size',
                        'prs.stock_type',
                        'prp.east_bonus_1_quantity',
                        'prp.east_bonus_1_quantity_value',
                        'prp.east_bonus_2_quantity',
                        'prp.east_bonus_2_quantity_value',
                        'prp.east_bonus_3_quantity',
                        'prp.east_bonus_3_quantity_value',

                        'prp.west_bonus_1_quantity',
                        'prp.west_bonus_1_quantity_value',
                        'prp.west_bonus_2_quantity',
                        'prp.west_bonus_2_quantity_value',
                        'prp.west_bonus_3_quantity',
                        'prp.west_bonus_3_quantity_value',

                        'prp.east_tier_1_min_quantity',
                        'prp.east_tier_1_max_quantity',
                        'prp.east_tier_1_base_price',
                        'prp.east_tier_2_min_quantity',
                        'prp.east_tier_2_max_quantity',
                        'prp.east_tier_2_base_price',
                        'prp.east_tier_3_min_quantity',
                        'prp.east_tier_3_max_quantity',
                        'prp.east_tier_3_base_price',

                        'prp.west_tier_1_min_quantity',
                        'prp.west_tier_1_max_quantity',
                        'prp.west_tier_1_base_price',
                        'prp.west_tier_2_min_quantity',
                        'prp.west_tier_2_max_quantity',
                        'prp.west_tier_2_base_price',
                        'prp.west_tier_3_min_quantity',
                        'prp.west_tier_3_max_quantity',
                        'prp.west_tier_3_base_price',

                        'prp.east_zone_price',
                        'prp.west_zone_price',

                        'prs.stock', 
                        'prs.is_batch_wise_stock', 
                        'pb.batch_stock' 
                    ]);
                    // Add stock filter
                    if ($request->has('is_stock') && $request->input('is_stock') == true) {
                        $query->whereRaw("(CASE 
                                            WHEN prs.is_batch_wise_stock = true 
                                            THEN COALESCE(SUM(pb.available_stock), 0) 
                                            ELSE prs.stock 
                                        END) > 0");
                    }
                if ($request->has('delivery_days')) {
                    $column = ($zone === 'east') ? 'pc.delivery_days' : 'pc.delivery_days_west';
                    $query->where($column, '<=', $request->input('delivery_days'));
                }
                if ($request->has('min_order')) {
                    $query->whereRaw('COALESCE(pc.min_order_value, 0) <= ?', [$request->input('min_order')]);
                }
                if($request->has('company_name')) {
                    $companyName = $request->input('company_name');
                    $query->where(function ($nameQuery) use ($companyName) {
                        $nameQuery->where('pc.company_name', 'ILIKE', "%{$companyName}%")
                                  ->orWhere(function ($businessNameQuery) use ($companyName) {
                                      $businessNameQuery->where(function ($emptyCheck) {
                                          $emptyCheck->whereNull('pc.company_name')
                                                     ->orWhere('pc.company_name', '');
                                      })
                                      ->whereExists(function ($typeQuery) {
                                          $typeQuery->select(DB::raw(1))
                                                    ->from('pc_company_types')
                                                    ->whereColumn('pc_company_types.id', 'pc.company_type_id')
                                                    ->where('pc_company_types.name', 'Sole Proprietary');
                                      })
                                      ->where('pc.business_name', 'ILIKE', "%{$companyName}%");
                                  });
                    });
                }

            
            if ($request->has('rating')) {
                //Need to add filter when rating functionality done properly
            }
        
            // Add key-value filters for sorting
            if ($request->has('key')) { 
               
                $key = $request->key;
                $value = (int) $request->input('value') ?? 1;
                $zonePriceColumn = "prp.{$zone}_zone_price";
                if ($key === 'unit') {
                    $query->orderByRaw("
                        CASE 
                            WHEN pr.price_type = 'fixed' THEN {$zonePriceColumn}
                            WHEN pr.price_type = 'bonus' THEN {$zonePriceColumn}
                            WHEN pr.price_type = 'tier' THEN LEAST(
                                COALESCE(prp.{$zone}_tier_1_base_price, 999999)
                            )
                        END ASC
                    ");
                } elseif ($key === 'quantity') { //for bonuses which price take 
                    $query->orderByRaw("
                        CASE 
                            WHEN pr.price_type = 'fixed' THEN ({$value} * {$zonePriceColumn})
                            WHEN pr.price_type = 'bonus' THEN ({$value} * {$zonePriceColumn})
                            WHEN pr.price_type = 'tier' THEN 
                                CASE 
                                    WHEN {$value} BETWEEN prp.{$zone}_tier_1_min_quantity AND prp.{$zone}_tier_1_max_quantity 
                                        THEN ({$value} * prp.{$zone}_tier_1_base_price)
                                    WHEN {$value} BETWEEN prp.{$zone}_tier_2_min_quantity AND prp.{$zone}_tier_2_max_quantity 
                                        THEN ({$value} * prp.{$zone}_tier_2_base_price)
                                    WHEN {$value} BETWEEN prp.{$zone}_tier_3_min_quantity AND prp.{$zone}_tier_3_max_quantity 
                                        THEN ({$value} * prp.{$zone}_tier_3_base_price)
                                    ELSE ({$value} * {$zonePriceColumn}) -- Default if no match
                                END
                        END ASC
                    ");
                }
            }
            return $query;
    }

    public function baseQuery($request,$zone,$authId){
        $userClinicAccountTypeId = Auth::user()->clinicData->clinic_account_type_id ?? null;
        $suppliers = $request->suppliers ? "'" . implode("','", $request->suppliers) . "'" : null;
        $priceCon = $suppliers ? '(pd.company_name IN ('.$suppliers.') OR (
            (pd.company_name IS NULL OR pd.company_name = \'\')
            AND EXISTS (
                SELECT 1 FROM pc_company_types pct 
                WHERE pct.id = pd.company_type_id 
                AND pct.name = \'Sole Proprietary\'
            )
            AND pd.business_name IN ('.$suppliers.')
        ))' : '1=1';  
       
        $query =  DB::table('products')

        ->select(
            'products.id as id',
            'products.category_id as category_id',
            'products.sub_category_id as sub_category_id',
            'dp.distributor_id as distributor_id',
            'products.dosage_foams_id as dosage_foams_id',
            'products.name as name',
            'products.generic_name_id',
            'generic_names.name as generic_name',
            'units.short_form as unit_name',
            'brands.name as brand_name',
            'products.created_at',
            DB::raw('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END AS is_fav'),
            'media.file_name as media_file_name',
            'media.id as media_id',
        )
        ->addSelect([
            'lowest_price' => DB::raw("(
                SELECT MIN(
                    CASE 
                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'tier' THEN LEAST(
                            COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                            COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                            COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                        )
                    END
                )
                FROM products_relation pr
                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                JOIN pc_details pd ON pd.user_id = pr.user_id
                WHERE 
                pr.product_id = products.id
                AND pr.pc_approval = true
                AND pr.admin_approval = true
                AND {$priceCon}
                )"),

                'highest_price' => DB::raw("(
                SELECT MAX(
                    CASE 
                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'tier' THEN GREATEST(
                            COALESCE(prp.{$zone}_tier_1_base_price, 0), 
                            COALESCE(prp.{$zone}_tier_2_base_price, 0), 
                            COALESCE(prp.{$zone}_tier_3_base_price, 0)
                        )
                    END
                )
                FROM products_relation pr
                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                JOIN pc_details pd ON pd.user_id = pr.user_id
                WHERE 
                 pr.product_id = products.id
                AND pr.pc_approval = true
                AND pr.admin_approval = true
                AND {$priceCon}
                )")
        ])
        ->leftJoin('favourites', function ($join) {
            $join->on('favourites.product_id', '=', 'products.id')
                ->where('favourites.user_id', '=', Auth::id());
        })
        ->leftJoin(DB::raw("LATERAL (
                SELECT m1.id, m1.file_name
                FROM media m1
                WHERE (
                    products.default_image_id IS NOT NULL 
                    AND products.default_image_id <> '' 
                    AND m1.id = products.default_image_id::bigint
                )
                OR (
                    products.default_image_id IS NULL 
                    AND m1.model_id = products.id
                )
                ORDER BY m1.id ASC
                LIMIT 1
        ) as media"), DB::raw('true'), '=', DB::raw('true'))
        ->join('brands', 'brands.id', '=', 'products.brand_id')
        ->join('generic_names', 'generic_names.id', '=', 'products.generic_name_id')
        ->join('units', 'units.id', '=', 'products.unit_id')
        ->join('dosage_foams', 'dosage_foams.id', '=', 'products.dosage_foams_id')
        ->join('categories as main_category', 'main_category.id', '=', 'products.category_id')
        ->leftJoin('categories as sub_category', 'sub_category.id', '=', 'products.sub_category_id')
        ->leftJoin(DB::raw("LATERAL (
            SELECT dp.distributor_id
            FROM distributor_product dp
            WHERE dp.product_id = products.id
            ORDER BY dp.distributor_id ASC
            LIMIT 1
        ) as dp"), DB::raw('true'), '=', DB::raw('true'))
        ->leftJoin('distributors', 'distributors.id', '=', 'dp.distributor_id')
        ->whereNotExists(function ($existsQuery) use ($userClinicAccountTypeId) {
            $existsQuery->select(DB::raw(1))
                    ->from('category_clinic_account_type')
                    ->whereColumn('category_clinic_account_type.category_id', 'main_category.id')
                    ->where('category_clinic_account_type.clinic_account_type_id', '=', $userClinicAccountTypeId);
            })
            ->whereExists(function ($query) use ($authId,$request) {
                $query->select(DB::raw(1))
                    ->from('products_relation as pr')
                    ->join('pc_details as pd', function ($join) {
                        $join->on('pr.user_id', '=', 'pd.user_id')
                            ->where(function ($query) {
                                $query->where('pd.is_restricted', false)
                                    ->orWhereExists(function ($subquery) {
                                        $subquery->select(DB::raw(1))
                                            ->from('clinic_pharma_suppliers as cps')
                                            ->whereColumn('cps.pc_id', 'pd.user_id')
                                            ->where('cps.clinic_id', Auth::user()->id) 
                                            ->where('cps.status', 'approved');
                                    });
                            });
                    })
                    ->join('users as u', 'u.id', '=', 'pr.user_id')
                    ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                    ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                    
                    ->whereColumn('pr.product_id', 'products.id')
                    ->whereNull('pr.deleted_at')
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            // For fixed and bonus, east and west prices must be present
                            $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                            ->whereNotNull('prp.east_zone_price')
                            ->where('prp.east_zone_price', '>', 0)
                            ->whereNotNull('prp.west_zone_price')
                            ->where('prp.west_zone_price', '>', 0);
                        })->orWhere(function ($q) {
                            // For tier, all 6 tier prices must be present
                            $q->where('pr.price_type', 'tier')
                            ->whereNotNull('prp.east_tier_1_base_price')
                            ->where('prp.east_tier_1_base_price', '>', 0);
                        });
                    })
                    // Filter out products with expired dates
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->where('prs.is_batch_wise_stock', false)
                            ->where(function ($expiry) {
                                $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                            })
                            ->orWhere(function($batchWise) {
                                $batchWise->where('prs.is_batch_wise_stock', true)
                                ->whereExists(function($query) {
                                    $query->select(DB::raw(1))
                                            ->from('products_batch')
                                            ->whereColumn('products_batch.product_id', 'pr.product_id')
                                            ->whereColumn('products_batch.user_id', 'pr.user_id')
                                            ->where('products_batch.expiry_date', '>=', DB::raw('CURRENT_DATE'))
                                            ->where('products_batch.available_stock', '>', 0);
                                });
                            });
                        });
                    })
                    ->where('pr.pc_approval', true)
                    ->where('pr.admin_approval', true);


                if ($request->has('suppliers') && is_array($request->suppliers)) {
                    $suppliers = $request->suppliers;
                    // Filter products based on the supplier names while respecting restrictions
                    $query->where(function($q) use ($suppliers) {
                        foreach ($suppliers as $supplierName) {
                            $q->orWhere(function($innerQ) use ($supplierName) {
                                $innerQ->where(function ($nameQ) use ($supplierName) {
                                    $nameQ->where('pd.company_name', 'LIKE', "{$supplierName}")
                                          ->orWhere(function ($businessNameQ) use ($supplierName) {
                                              $businessNameQ->where(function ($emptyCheck) {
                                                  $emptyCheck->whereNull('pd.company_name')
                                                             ->orWhere('pd.company_name', '');
                                              })
                                              ->whereExists(function ($typeQuery) {
                                                  $typeQuery->select(DB::raw(1))
                                                            ->from('pc_company_types as pct')
                                                            ->whereColumn('pct.id', 'pd.company_type_id')
                                                            ->where('pct.name', 'Sole Proprietary');
                                              })
                                              ->where('pd.business_name', 'LIKE', "{$supplierName}");
                                          });
                                })
                                ->where(function ($restrictionQ) {
                                    $restrictionQ->where('pd.is_restricted', false)
                                        ->orWhereExists(function ($subquery) {
                                            $subquery->select(DB::raw(1))
                                                ->from('clinic_pharma_suppliers as cps')
                                                ->whereColumn('cps.pc_id', 'pd.user_id')
                                                ->where('cps.clinic_id', Auth::user()->id)
                                                ->where('cps.status', 'approved');
                                        });
                                });
                            });
                        }
                    });
                }
            
        })
        ->where('products.status', 'approved')
        ->whereNull('products.deleted_at') 
        ->whereNull('brands.deleted_at') 
        ->whereNull('dosage_foams.deleted_at') 
        ->whereNull('units.deleted_at') 
        ->whereNull('main_category.deleted_at') 
        ->whereNull('sub_category.deleted_at') 
        ->whereNull('generic_names.deleted_at') 
        ->whereNull('distributors.deleted_at') 
        ->groupBy(
            'products.id',
            'products.name',
            'products.category_id',
            'products.sub_category_id',
            'dp.distributor_id',
            'products.dosage_foams_id',
            'generic_names.name',
            'units.short_form',
            'brands.name',
            'products.created_at',
            'favourites.id',
            'media.file_name',
            'media.id',
        );

         //Apply the when browser history call
        if(isset($request->products) && $request->products != null){
            
            $query->whereIn('products.id', $request->products);
        }

        return $query;
    }

    public function globalSearchQuery($request){
        $userClinicAccountTypeId = Auth::user()->clinicData->clinic_account_type_id ?? null;
        $authId = Auth::user()->id;
        $keyword = $request->input('search', '');
    
        return DB::table('products')
            ->select([
                DB::raw("DISTINCT ON (products.id) products.id"),
                'products.name',
                'products.generic_name_id',
                'products.category_id',
                'products.brand_id',
                'products.sub_category_id',
                'products.unit_id',
                'products.quantity_per_unit',
                'products.is_prescription_required',
                'generic_names.name as generic_name',
                'category.name as category_name',
                'subcategory.name as sub_category_name',
                'brands.name as brand_name',
                'pc_details.user_id as user_id',
                'pc_details.company_name',
                'pc_details.business_name',
                'pc_company_types.name as company_type_name',
                'distributors.name as distributor_name',
    
                DB::raw("CASE WHEN products.name ILIKE ? THEN 1 ELSE NULL END as matched_in_name"),
                DB::raw("CASE WHEN generic_names.name ILIKE ? THEN 1 ELSE NULL END as matched_in_generic_name"),
                DB::raw("CASE WHEN category.name ILIKE ? THEN 1 ELSE NULL END as matched_in_category"),
                DB::raw("CASE WHEN subcategory.name ILIKE ? THEN 1 ELSE NULL END as matched_in_subcategory"),
                DB::raw("CASE WHEN brands.name ILIKE ? THEN 1 ELSE NULL END as matched_in_brand"),
                DB::raw("CASE WHEN distributors.name ILIKE ? THEN 1 ELSE NULL END as matched_in_distributor"),
                DB::raw("(CASE 
                    WHEN (pc_details.company_name ILIKE ? 
                          OR (
                              (pc_details.company_name IS NULL OR pc_details.company_name = '')
                              AND pc_company_types.name = 'Sole Proprietary'
                              AND pc_details.business_name ILIKE ?
                          ))
                         AND (pc_details.is_restricted = false 
                              OR EXISTS (
                                  SELECT 1 FROM clinic_pharma_suppliers 
                                  WHERE clinic_pharma_suppliers.pc_id = pc_details.user_id 
                                  AND clinic_pharma_suppliers.status = 'approved'
                              )
                         ) 
                    THEN 1 ELSE NULL 
                END) as matched_in_supplier")
            ])
            ->join('generic_names', 'generic_names.id', '=', 'products.generic_name_id')
            ->join('categories as category', 'category.id', '=', 'products.category_id')
            ->leftJoin('distributor_product as dp', 'dp.product_id', '=', 'products.id')
            ->leftJoin('distributors', 'distributors.id', '=', 'dp.distributor_id')
            ->leftJoin('categories as subcategory', 'subcategory.id', '=', 'products.sub_category_id')
            ->whereNotExists(function ($existsQuery) use ($userClinicAccountTypeId) {
                $existsQuery->select(DB::raw(1))
                    ->from('category_clinic_account_type')
                    ->whereColumn('category_clinic_account_type.category_id', 'category.id')
                    ->where('category_clinic_account_type.clinic_account_type_id', '=', $userClinicAccountTypeId);
            })
            ->join('brands', 'brands.id', '=', 'products.brand_id')
            ->join('products_relation', function ($join) {
                $join->on('products_relation.product_id', '=', 'products.id')
                    ->where('products_relation.pc_approval', true)
                    ->whereNull('products_relation.deleted_at')
                    ->where('products_relation.admin_approval', true)
                    ->join('product_relation_prices as prp', 'products_relation.id', '=', 'prp.product_relation_id')
                    ->join('product_relation_stocks as prs', 'products_relation.id', '=', 'prs.product_relation_id')
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            // For fixed and bonus, east and west prices must be present
                            $q->whereIn('products_relation.price_type', ['fixed', 'bonus'])
                            ->whereNotNull('prp.east_zone_price')
                            ->where('prp.east_zone_price', '>', 0)
                            ->whereNotNull('prp.west_zone_price')
                            ->where('prp.west_zone_price', '>', 0);
                        })->orWhere(function ($q) {
                            // For tier, all 6 tier prices must be present
                            $q->where('products_relation.price_type', 'tier')
                            ->whereNotNull('prp.east_tier_1_base_price')
                            ->where('prp.east_tier_1_base_price', '>', 0);
                        });
                    })
                    ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->where('prs.is_batch_wise_stock', false)
                            ->where(function ($expiry) {
                                $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                            })
                            ->orWhere(function($batchWise) {
                                $batchWise->where('prs.is_batch_wise_stock', true)
                                ->whereExists(function($query) {
                                    $currentDate = date('Y-m-d');
                                    $query->select(DB::raw(1))
                                            ->from('products_batch')
                                            ->whereColumn('products_batch.product_id', 'products_relation.product_id')
                                            ->whereColumn('products_batch.user_id', 'products_relation.user_id')
                                            ->where(DB::raw("DATE(products_batch.expiry_date)"), '>=', $currentDate)
                                            ->where('products_batch.available_stock', '>', 0);
                                });
                            });
                        });
                    });
            })
           // ->join('pc_details', 'pc_details.user_id', '=', 'products_relation.user_id')

            ->join('pc_details', function ($join) {
                $join->on('pc_details.user_id', '=', 'products_relation.user_id')
                ->where(function ($query) {
                    $query->where('pc_details.is_restricted', false)
                        ->orWhereExists(function ($subquery) {
                            $subquery->select(DB::raw(1))
                                    ->from('clinic_pharma_suppliers as cps')
                                    ->whereColumn('cps.pc_id', 'pc_details.user_id')
                                    ->where('cps.clinic_id', Auth::user()->id) 
                                    ->where('cps.status', 'approved');
                        });
                });
            })
            ->leftJoin('pc_company_types', 'pc_company_types.id', '=', 'pc_details.company_type_id')

            ->join('users', 'users.id', '=', 'products_relation.user_id')
           
            ->where('products.status', 'approved')
            ->whereNull('products.deleted_at')
            ->groupBy([
                'products.id',
                'products.name',
                'products.generic_name_id',
                'products.category_id',
                'products.brand_id',
                'products.sub_category_id',
                'products.unit_id',
                'products.quantity_per_unit',
                'products.is_prescription_required',
                'generic_names.name',
                'category.name',
                'subcategory.name',
                'brands.name',
                'pc_details.is_restricted',
                'pc_details.user_id',
                'users.name',
                'pc_details.company_name',
                'pc_details.business_name',
                'pc_company_types.name',
                'distributors.name',
            ])
            ->where(function ($query) use ($keyword) {
                $query->where('products.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('generic_names.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('category.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('subcategory.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('brands.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('distributors.name', 'ILIKE', "%{$keyword}%")
                    ->orWhere('pc_details.company_name', 'ILIKE', "%{$keyword}%")
                    ->orWhere(function ($subQuery) use ($keyword) {
                        $subQuery->where(function ($nameQuery) {
                            $nameQuery->whereNull('pc_details.company_name')
                                     ->orWhere('pc_details.company_name', '');
                        })
                        ->where('pc_company_types.name', 'Sole Proprietary')
                        ->where('pc_details.business_name', 'ILIKE', "%{$keyword}%");
                    });
            })
            // Bind the keyword for all the CASE WHEN ILIKE clauses
            ->setBindings(array_fill(0, 8, "%{$keyword}%"), 'select');
    }


    public function clinicCategory() {

        $userClinicAccountTypeId = Auth::user()->clinicData->clinic_account_type_id;

        return Category::where('type','b2b')
            ->with(['children'])
            ->where('parent_id',null)
            ->whereNull('deleted_at')
            ->where('status',true)
            ->whereDoesntHave('categoryAccountTypes', function ($query) use ($userClinicAccountTypeId) {
                $query->where('clinic_account_type_id', $userClinicAccountTypeId);
            });
    }
    
    public function filterBaseQuery($request,$zone,$authId){

        $key = $request->key ?? '';
        $value = $request->value ?? '';
        $userClinicAccountTypeId = Auth::user()->clinicData->clinic_account_type_id ?? null;
        $suppliers = $request->suppliers ? "'" . implode("','", $request->suppliers) . "'" : null;
        $priceCon = $suppliers ? '(pd.company_name IN ('.$suppliers.') OR (
            (pd.company_name IS NULL OR pd.company_name = \'\')
            AND EXISTS (
                SELECT 1 FROM pc_company_types pct 
                WHERE pct.id = pd.company_type_id 
                AND pct.name = \'Sole Proprietary\'
            )
            AND pd.business_name IN ('.$suppliers.')
        ))' : '1=1';  
       
        $query =  DB::table('products')
        ->select(
            'products.id as id'
        )
        ->addSelect([
            'lowest_price' => DB::raw("(
                SELECT MIN(
                    CASE 
                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'tier' THEN LEAST(
                            COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                            COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                            COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                        )
                    END
                )
                FROM products_relation pr
                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
               JOIN pc_details pd ON pd.user_id = pr.user_id
                WHERE pr.product_id = products.id
                AND pr.pc_approval = true
                AND pr.admin_approval = true
                AND {$priceCon}
                )"),

                'highest_price' => DB::raw("(
                SELECT MAX(
                    CASE 
                        WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                        WHEN pr.price_type = 'tier' THEN GREATEST(
                            COALESCE(prp.{$zone}_tier_1_base_price, 0), 
                            COALESCE(prp.{$zone}_tier_2_base_price, 0), 
                            COALESCE(prp.{$zone}_tier_3_base_price, 0)
                        )
                    END
                )
                FROM products_relation pr
                JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                JOIN pc_details pd ON pd.user_id = pr.user_id
                WHERE pr.product_id = products.id
                AND pr.pc_approval = true
                AND pr.admin_approval = true
                AND {$priceCon}
                )")
        ])
        ->join('brands', 'brands.id', '=', 'products.brand_id')
        ->join('generic_names', 'generic_names.id', '=', 'products.generic_name_id')
        ->join('units', 'units.id', '=', 'products.unit_id')
        ->join('dosage_foams', 'dosage_foams.id', '=', 'products.dosage_foams_id')
        ->join('categories as main_category', 'main_category.id', '=', 'products.category_id')
        ->leftJoin('categories as sub_category', 'sub_category.id', '=', 'products.sub_category_id')
        ->leftJoin(DB::raw("LATERAL (
            SELECT dp.distributor_id
            FROM distributor_product dp
            WHERE dp.product_id = products.id
            ORDER BY dp.distributor_id ASC
            LIMIT 1
        ) as dp"), DB::raw('true'), '=', DB::raw('true'))
        ->leftJoin('distributors', 'distributors.id', '=', 'dp.distributor_id')
        ->whereNotExists(function ($existsQuery) use ($userClinicAccountTypeId) {
            $existsQuery->select(DB::raw(1))
                    ->from('category_clinic_account_type')
                    ->whereColumn('category_clinic_account_type.category_id', 'main_category.id')
                    ->where('category_clinic_account_type.clinic_account_type_id', '=', $userClinicAccountTypeId);
            })
        ->whereExists(function ($query) use ($authId,$request) {
            $query->select(DB::raw(1))
                ->from('products_relation as pr')
                ->join('pc_details as pd', function ($join) {
                    $join->on('pr.user_id', '=', 'pd.user_id')
                        ->where(function ($query) {
                            $query->where('pd.is_restricted', false)
                                ->orWhereExists(function ($subquery) {
                                    $subquery->select(DB::raw(1))
                                        ->from('clinic_pharma_suppliers as cps')
                                        ->whereColumn('cps.pc_id', 'pd.user_id')
                                        ->where('cps.clinic_id', Auth::user()->id) 
                                        ->where('cps.status', 'approved');
                                });
                        });
                })
                ->join('users as u', 'u.id', '=', 'pr.user_id')
                ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                
                ->whereColumn('pr.product_id', 'products.id')
                ->whereNull('pr.deleted_at')
                ->where(function ($query) {
                    $query->where(function ($q) {
                        // For fixed and bonus, east and west prices must be present
                        $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                        ->whereNotNull('prp.east_zone_price')
                        ->where('prp.east_zone_price', '>', 0)
                        ->whereNotNull('prp.west_zone_price')
                        ->where('prp.west_zone_price', '>', 0);
                    })->orWhere(function ($q) {
                        // For tier, all 6 tier prices must be present
                        $q->where('pr.price_type', 'tier')
                        ->whereNotNull('prp.east_tier_1_base_price')
                        ->where('prp.east_tier_1_base_price', '>', 0);
                    });
                })
                // Filter out products with expired dates
                ->where(function ($query) {
                    $query->where(function ($q) {
                        $q->where('prs.is_batch_wise_stock', false)
                        ->where(function ($expiry) {
                            $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                        })
                        ->orWhere(function($batchWise) {
                            $batchWise->where('prs.is_batch_wise_stock', true)
                            ->whereExists(function($query) {
                                $currentDate = date('Y-m-d');
                                $query->select(DB::raw(1))
                                        ->from('products_batch')
                                        ->whereColumn('products_batch.product_id', 'pr.product_id')
                                        ->whereColumn('products_batch.user_id', 'pr.user_id')
                                        ->where(DB::raw("DATE(products_batch.expiry_date)"), '>=', $currentDate)
                                        ->where('products_batch.available_stock', '>', 0);
                            });
                        });
                    });
                })

                ->where('pr.pc_approval', true)
                ->where('pr.admin_approval', true);
            
        })
        ->where('products.status', 'approved')
        ->whereNull('products.deleted_at') 
        ->whereNull('brands.deleted_at') 
        ->whereNull('dosage_foams.deleted_at') 
        ->whereNull('units.deleted_at') 
        ->whereNull('main_category.deleted_at') 
        ->whereNull('sub_category.deleted_at') 
        ->whereNull('generic_names.deleted_at') 
        ->whereNull('distributors.deleted_at') 
        ->groupBy(
            'products.id'
        );

        if ($request->has('price_min') && $request->has('price_max')) {
            $priceMin = $request->price_min;
            $priceMax = $request->price_max;
        
            $query->whereRaw("
            (
                    SELECT MIN(
                        CASE 
                            WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                            WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                            WHEN pr.price_type = 'tier' THEN LEAST(
                                COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                                COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                                COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                            )
                        END
                    )
                    FROM products_relation pr
                    JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                    WHERE pr.product_id = products.id
                    AND pr.pc_approval = true
                    AND pr.admin_approval = true
                ) BETWEEN ? AND ?
            ", [$priceMin, $priceMax]);
    
        }
        if (
            $request->has('exclude_out_stock') &&
            ($request->input('exclude_out_stock') === true || $request->input('exclude_out_stock') === 'true')
        ) {
            $query->whereExists(function ($subQuery) {
                $subQuery->select(DB::raw(1))
                    ->from('products_relation as pr_stock')
                    ->join('product_relation_stocks as prs', 'prs.product_relation_id', '=', 'pr_stock.id')
                    ->whereColumn('pr_stock.product_id', 'products.id')
                    ->whereNull('pr_stock.deleted_at')
                    ->where('pr_stock.pc_approval', true)
                    ->where('pr_stock.admin_approval', true)
                    ->where(function ($cond) {
                        $cond->where(function ($q) {
                            $q->where('prs.is_batch_wise_stock', false)
                                ->where('prs.stock', '>', 0);
                        })
                        ->orWhere(function ($q) {
                            $q->where('prs.is_batch_wise_stock', true)
                                ->whereExists(function ($batchQuery) {
                                    $batchQuery->select(DB::raw(1))
                                        ->from('products_batch as pb')
                                        ->whereColumn('pb.products_relation_id', 'prs.product_relation_id')
                                        ->where('pb.available_stock', '>', 0);
                                });
                        });
                    });
            });
        }
        if($key && $value) {

            if ($key == 'search') {
                $query->where(function ($subQuery) use ($value) {
                    $subQuery->where('products.name', 'ILIKE', "%{$value}%")
                        ->orWhere('generic_names.name', 'ILIKE', "%{$value}%")
                        ->orWhere('main_category.name', 'ILIKE', "%{$value}%")
                        ->orWhere('sub_category.name', 'ILIKE', "%{$value}%")
                        ->orWhere('brands.name', 'ILIKE', "%{$value}%")
                        ->orWhere('distributors.name', 'ILIKE', "%{$value}%")
                        ->orWhereExists(function ($subSubQuery) use ($value) {
                            $subSubQuery->select(DB::raw(1))
                                ->from('pc_details')
                                ->join('products_relation', 'products_relation.user_id', '=', 'pc_details.user_id')
                                ->leftJoin('pc_company_types', 'pc_company_types.id', '=', 'pc_details.company_type_id')
                                ->where(function ($nameQuery) use ($value) {
                                    $nameQuery->where('pc_details.company_name', 'ILIKE', "%{$value}%")
                                              ->orWhere(function ($businessNameQuery) use ($value) {
                                                  $businessNameQuery->where(function ($emptyCheck) {
                                                      $emptyCheck->whereNull('pc_details.company_name')
                                                                 ->orWhere('pc_details.company_name', '');
                                                  })
                                                  ->where('pc_company_types.name', 'Sole Proprietary')
                                                  ->where('pc_details.business_name', 'ILIKE', "%{$value}%");
                                              });
                                });
                        });
                });
            }
            elseif($key == 'generic name'){
                $query->where('generic_names.name',$value);
            }elseif($key == 'brand'){
                $query->where('brands.name',$value);
            }
        }
        return $query;

    }
}