<?php

namespace App\Repositories\Api\Clinic;

use App\Models\ClinicAddCart;
use Illuminate\Support\Facades\Auth;
use App\Repositories\Api\Repository;
use App\Models\ProductRelation;
use App\Models\Order;
use App\Service\ShippingService;
use Illuminate\Support\Facades\Log;
use App\Jobs\PaymentSuccessfulMailJob;
use App\Jobs\PaymentFailMailJob;
use App\Jobs\PaymentPendingMailJob;
use Illuminate\Support\Facades\DB;
use App\Models\Transaction;


class OrderRepository extends Repository
{
    protected $shippingService;

    public function __construct(Order $model)
    {
        $this->model = $model;
        $this->shippingService = new ShippingService();
        
    }

    public function getOrderList($data)
    {
        $search = $data->search ?? "";
        $status = $data->status ?? [];
        $isSpend = $data->is_spend ?? false;
        $dateFilter = $data->date_filter ?? ""; 

        $orders = $this->model
                        ->where('user_id', Auth::user()->id)
                        ->withCount(['subOrder', "orderProducts"]);
        if($status) {
            $orders->whereIn('status', $status);
        }
        if($isSpend) {
            $orders->whereNot('status', 'cancelled');
        }
        // Applying search filter if provided.
        if ($search) {
            $orders->where('order_number', 'ILIKE', "%{$search}%");
        }

        // Apply date filters
        if ($dateFilter) {
            $now = now();
            if (is_numeric($dateFilter) && strlen($dateFilter) == 4) {
                $orders->whereYear('created_at', $dateFilter);
            } else {
                switch ($dateFilter) {
                    case "past_3_months":
                        $orders->where('created_at', '>=', $now->subMonths(3));
                        break;
                    case "past_month":
                        $orders->where('created_at', '>=', $now->subMonth());
                        break;
                    case "custom":
                        $startDate = $data->start_date;
                        $endDate = $data->end_date;
        
                        if ($startDate && $endDate) {
                            try {
                                $start = \Carbon\Carbon::parse($startDate)->startOfDay();
                                $end = \Carbon\Carbon::parse($endDate)->endOfDay();
        
                                $orders->whereBetween('created_at', [$start, $end]);
                            } catch (\Exception $e) {
                                Log::error("Invalid custom date range in order listing: " . $e->getMessage());
                            }
                        }
                        break;
                }
            }
        }
        return $orders;
    }

    public function getSupplierProductPrice($validatedData) 
    {
        try {
            $supplierData = collect($validatedData['suppliers']);
            $supplierIds = $supplierData->pluck('id')->toArray();

            $query = ClinicAddCart::where('user_id', Auth::id())
                                ->whereIn('supplier_id', decryptParam($supplierIds))
                                ->get();

        $cartData = $query->groupBy('supplier_id');
        $grandWithPayNowTotal = $this->cartGrandTotal($cartData,$validatedData);

        return [
            'grandWithPayNowTotal' =>round($grandWithPayNowTotal , 2),
        ];
        }catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }

    public function cartGrandTotal($cartData,$validatedData)
    { 
      
         $requestSup = $validatedData['suppliers'];

        return $cartData->map(function ($items) use ($requestSup) {
            $pc = $items->first()->supplier;
            $userId = $pc->user_id;
            $supIdToFind = encryptParam($pc->user_id);
            $paymentType = 'pay_now';
            $zonePrefix = getClinicZone();
            $wareHouseType = $pc->wareHouse ? $pc->wareHouse->warehouse_type : 'own';
            $postalCode = $pc->wareHouse->postal_code ?? null;

            $shippingFee = 0;
            $productWeight = 0;
            $supplierTotalQty = 0;
            $paymentDeliveryType = null;

            
            $productAmt = $items->sum(function ($item) use ($zonePrefix,&$productWeight,&$supplierTotalQty,&$paymentDeliveryType,&$paymentType) {
                $metaData = json_decode($item->meta_data);
                $paymentType = $metaData->payment_type;
                
                    $productWeight += $item->product->weight;
                    $supplierTotalQty += $item->quantity;
                    $paymentDeliveryType = $metaData->delivery_type;

                
                    $supPro = ProductRelation::with('productRelationPrice')
                        ->where('product_id', $item->product_id)
                        ->where('user_id', $item->supplier_id)
                        ->first();
                    
                    $priceBase = $supPro->productRelationPrice;
                    $singleProductPrice = $supPro->price_type == 'fixed' || $supPro->price_type == 'bonus'
                        ? $priceBase->{$zonePrefix . '_zone_price'}
                        : $priceBase->{$zonePrefix . '_' . $item->tier_number . '_base_price'} ?? 0;
                  
                    return $singleProductPrice * $item->quantity;
            });

            //Shipping fee calculate
            if($wareHouseType == 'dpharma') {
                $shippingDetail = $this->shippingService->getOrderShipCharge($productWeight, $supplierTotalQty, $postalCode);
               
                if ($shippingDetail && isset($shippingDetail['result'])) {
                    $shippingDetailArr = array_values(array_filter($shippingDetail['result'], 
                        fn($item) => $item['serviceType'] === $paymentDeliveryType));
                   
                    if (!empty($shippingDetailArr)) { 
                        $shippingFee = $shippingDetailArr[0]['charge'];
                    } else {
                        // If no matching service type found, use first available or fallback
                        $shippingFee = $shippingDetail['result'][0]['charge'] ?? 0;
                    }
                    $shippingFee = (float) str_replace(',', '', $shippingFee);
                }
            }

            ClinicAddCart::where('supplier_id',$userId)
            ->update([
                'meta_data' => DB::raw("jsonb_set(meta_data::jsonb, '{shipping_fee}', '\"$shippingFee\"')")
            ]);
            return $productAmt + $shippingFee;
        })->sum();
    }

    public function getTierBasedQuantity($product_id,$supplier_id,$zone,$quantity){
 
        return ProductRelation::where('product_id', $product_id)
        ->where('user_id', $supplier_id)
        ->join('product_relation_prices', 'products_relation.id', '=', 'product_relation_prices.product_relation_id')
        ->selectRaw("
            CASE
                WHEN products_relation.price_type = 'tier' THEN (
                    CASE
                        WHEN ? BETWEEN product_relation_prices.{$zone}_tier_1_min_quantity AND COALESCE(NULLIF(product_relation_prices.{$zone}_tier_1_max_quantity, 0), ?) THEN 'tier_1'
                        WHEN ? BETWEEN product_relation_prices.{$zone}_tier_2_min_quantity AND COALESCE(NULLIF(product_relation_prices.{$zone}_tier_2_max_quantity, 0), ?) THEN 'tier_2'
                        WHEN ? BETWEEN product_relation_prices.{$zone}_tier_3_min_quantity AND COALESCE(NULLIF(product_relation_prices.{$zone}_tier_3_max_quantity, 0), ?) THEN 'tier_3'
                       
                        WHEN ? < product_relation_prices.{$zone}_tier_1_min_quantity THEN 'tier_1'

                        
                        WHEN ? > GREATEST(
                                    COALESCE(NULLIF(product_relation_prices.{$zone}_tier_1_max_quantity, 0), 0),
                                    COALESCE(NULLIF(product_relation_prices.{$zone}_tier_2_max_quantity, 0), 0),
                                    COALESCE(NULLIF(product_relation_prices.{$zone}_tier_3_max_quantity, 0), 0)
                                )
                            THEN
                                CASE
                                    WHEN product_relation_prices.{$zone}_tier_3_min_quantity IS NOT NULL THEN 'tier_3'
                                    WHEN product_relation_prices.{$zone}_tier_2_min_quantity IS NOT NULL THEN 'tier_2'
                                    ELSE 'tier_1'
                                END
                        ELSE 'tier_1'
                    END
                )
                WHEN products_relation.price_type = 'bonus' THEN (
                    CASE
                        WHEN product_relation_prices.{$zone}_bonus_3_quantity IS NOT NULL AND ? >= product_relation_prices.{$zone}_bonus_3_quantity THEN 'bonus_3'
                        WHEN product_relation_prices.{$zone}_bonus_2_quantity IS NOT NULL AND ? >= product_relation_prices.{$zone}_bonus_2_quantity THEN 'bonus_2'
                        WHEN product_relation_prices.{$zone}_bonus_1_quantity IS NOT NULL AND ? >= product_relation_prices.{$zone}_bonus_1_quantity THEN 'bonus_1'
                        ELSE NULL
                    END
                )
                ELSE NULL
            END as tier_value
        ", [
            $quantity, $quantity, // for tier_1
            $quantity, $quantity, // for tier_2
            $quantity, $quantity, // for tier_3
            $quantity,            // below tier_1 min
            $quantity,            // above tier_3 max
            $quantity,            // bonus_3
            $quantity,            // bonus_2
            $quantity             // bonus_1
        ])
        ->value('tier_value'); 
    
    }


    public function handlePaymentStatus(Order $order, array $transactionResult): array
    {
        $paymentStatusCode = (int) $transactionResult['status'];
        $status = 'failed';
        $response = null;
        $failureMessage = __('api.clinic.order.payment_failed');

        switch ($paymentStatusCode) {
            case 1:
                $status = 'paid';
                if ($order->payment_status !== 'paid') {
                    PaymentSuccessfulMailJob::dispatch($order, 'success', 'pay_now');
                    //Activity Log Start
                        activity()
                        ->causedBy(Auth::user())
                        ->performedOn($order)
                        ->useLog('order_payment_done')
                        ->withProperties([
                            'attributes' => [
                                'amount' => $transactionResult['amount'] / 100,
                                'order_number' => $order->order_number,
                                'status' =>'paid'
                            ]
                        ])
                        ->log('Order #'.$order->order_number.' payment completed successfully.');
                    //Activity Log End
                }
                $response = $this->successResponse($order, $paymentStatusCode, __('api.clinic.order.payment_status'));
                break;

            case 0:
                $status = 'pending';
                $transactionResult['ecommerce_status'] =  __('api.clinic.order.payment_0_status');
                $response = $this->failResponse($order, $paymentStatusCode, __('api.clinic.order.payment_0_status'));
                if ($order->payment_status !== 'pending') {
                    PaymentPendingMailJob::dispatch($order, $transactionResult);
                }
                //send mail
                break;

            case 9:
                $status = 'authorized';
                $transactionResult['ecommerce_status'] = __('api.clinic.order.payment_9_status');
                $response = $this->successResponse($order, $paymentStatusCode, __('api.clinic.order.payment_9_status'));
                if ($order->payment_status !== 'authorized') {
                    PaymentFailMailJob::dispatch($order, $transactionResult);
                }
                break;

            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 10:
            default:
                $failureMessage = $this->getFailureMessage($paymentStatusCode);
                $transactionResult['ecommerce_status'] = $failureMessage;
                $response = $this->failResponse($order, $paymentStatusCode, $failureMessage);
                if ($order->payment_status !== 'failed') {
                    PaymentFailMailJob::dispatch($order, $transactionResult);
                }
                break;
        }

        return compact('status', 'response', 'failureMessage');
    }

    Public function getFailureMessage(int $code): string
    {
        return match($code) {
            2 => __('api.clinic.order.payment_2_status'),
            3 => __('api.clinic.order.payment_3_status'),
            4 => __('api.clinic.order.payment_4_status'),
            5 => __('api.clinic.order.payment_5_status'),
            6 => __('api.clinic.order.payment_6_status'),
            7 => __('api.clinic.order.payment_7_status'),
            8 => __('api.clinic.order.payment_8_status'),
            10 => __('api.clinic.order.payment_10_status'),
            default => __('api.clinic.order.payment_failed'),
        };
    }


    private function successResponse(Order $order, int $status, string $message)
    {
        return response()->json([
            'success' => true,
            'ecommerce_status' => $status,
            'order_id' => encryptParam($order->id),
            'message' => $message
        ]);
    }

    private function failResponse(Order $order, int $status, string $message)
    {
        return response()->json([
            'success' => false,
            'ecommerce_status' => $status,
            'order_id' => encryptParam($order->id),
            'message' => $message
        ]);
    }

    public function createTransaction(Order $order, array $transactionResult, string $status, ?string $failureMessage)
    {
        try {

            $trasRecord = Transaction::where('transaction_id', $transactionResult['transactionNumber'])
                    ->where('ecommerce_status', (int) $transactionResult['status'])->latest() 
                    ->first();
            if (!$trasRecord) {
                Transaction::create([
                    "transaction_id" => $transactionResult['transactionNumber'],
                    "order_id" => $order->id,
                    "sender_id" => $order->user_id,
                    "payment_method" => 'DEBIT',
                    "amount" => $transactionResult['amount'] / 100,
                    "status" => $this->mapTransactionStatus($status),
                    "ecommerce_status" => (int) $transactionResult['status'],
                    "meta_data" => json_encode($transactionResult),
                    "provider_transaction_id" => $transactionResult['providerTransactionNumber'] ?? null,
                    "failure_reason" => $failureMessage,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create transaction record', [
                'order_id' => $order->id,
                'transaction_number' => $transactionResult['transactionNumber'],
                'error' => $e->getMessage()
            ]);
        }
    }

    private function mapTransactionStatus($paymentStatus)
    {
        switch ($paymentStatus) {
            case 'paid':
            case 'authorized':
                return 'success';
            case 'failed':
                return 'failed';
            case 'pending':
            case 'refunded':
            default:
                return 'pending';
        }
    }

}
