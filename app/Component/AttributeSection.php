<?php

declare(strict_types=1);

namespace App\Component;

use Filament\Forms\Set;
use Filament\Forms\Get;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Section;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Actions\Action;
use App\Component\ProductVariant\Steps\ConfigurationStep;
use App\Component\ProductVariant\Steps\AttributesStep;
use App\Component\ProductVariant\Steps\QuantityStep;
use App\Component\ProductVariant\Steps\ImagesStep;
use App\Component\ProductVariant\Steps\PricingStep;
use App\Component\ProductVariant\Steps\SummaryStep;

class AttributeSection
{
    public static function make(): Section
    {
        return Section::make('Price and Variants')
            ->headerActions([
                self::createVariationsToggleAction(),
                self::createNoVariantsAction(),
                self::createYesVariantsAction(),
            ]);
    }

    private static function createVariationsToggleAction(): Action
    {
        return Action::make('variations_toggle')
            ->label('Does this product have multiple variations?')
            ->color('gray')
            ->extraAttributes([
                'style' => 'pointer-events: none; background: transparent; border: none; box-shadow: none;'
            ]);
    }

    private static function createNoVariantsAction(): Action
    {
        return Action::make('no')
            ->label('No')
            ->color('gray')
            ->size('sm')
            ->action(function (Set $set, Get $get) {
                $set('price_hidden', true); // Show pricing section when no variants
                $set('has_variants', false);
                $set('attribute_data', null); // Clear any existing attribute data
                
                Notification::make()
                    ->title('Pricing section enabled')
                    ->body('You can now configure pricing since this product has no variations.')
                    ->success()
                    ->send();
            });
    }

    private static function createYesVariantsAction(): Action
    {
        return Action::make('yes')
            ->label('Yes')
            ->color('primary')
            ->size('sm')
            ->slideOver()
            ->modalWidth(MaxWidth::Full)
            ->before(function (Set $set) {
                // Hide pricing section immediately when variants are selected
                $set('price_hidden', false);
                $set('has_variants', true);
            })
            ->action(function (array $data, callable $set) {
                // Only save to form state - actual DB persistence happens after product save
                $set('attribute_data', $data);
                $set('price_hidden', false);
                
                Notification::make()
                    ->title('Variants configured successfully')
                    ->body('Product variants have been configured. They will be saved when you save the product.')
                    ->success()
                    ->send();
            })
            ->steps([
                ConfigurationStep::make(),
                AttributesStep::make(),
                QuantityStep::make(),
                ImagesStep::make(),
                PricingStep::make(),
                SummaryStep::make(),
            ]);
    }
}
