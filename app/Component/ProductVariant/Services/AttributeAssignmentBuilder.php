<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Services;

use Filament\Forms\Get;
use App\Models\Attribute;
use App\Models\AttributeValue;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class AttributeAssignmentBuilder
{
    /**
     * Get selected attribute values from form state
     */
    private static function getSelectedAttributeValues(Get $get, int $attributeId): array
    {
        $attributeIds = $get('attribute_ids') ?? [];
        
        // Find which index this attribute is at
        $attributeIndex = null;
        foreach ($attributeIds as $index => $formAttributeId) {
            if ($formAttributeId == $attributeId) {
                $attributeIndex = $index + 1; // Form fields are 1-indexed
                break;
            }
        }
        
        if (!$attributeIndex) {
            // If not found in form, return all values (fallback)
            $attribute = Attribute::find($attributeId);
            return $attribute ? $attribute->values->toArray() : [];
        }
        
        // Get selected values for this attribute
        $selectedAttributeValues = $get("attribute_values_{$attributeIndex}") ?? [];
        
        if (empty($selectedAttributeValues)) {
            return [];
        }
        
        // Return only the selected attribute values
        return AttributeValue::whereIn('id', $selectedAttributeValues)->get()->toArray();
    }

    public static function buildImageFields(Get $get, int $attributeId): array
    {
        $attribute = Attribute::find($attributeId);
        if (!$attribute) {
            return [
                Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $attributeId);
        if (empty($attributeValues)) {
            return [
                Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields[] = SpatieMediaLibraryFileUpload::make("attribute_image_{$attributeId}_{$valueObj->id}")
                ->label("Images for {$attribute->name}: {$valueObj->name}")
                ->collection('attribute-images')
                ->multiple()
                ->image()
                ->imageEditor()
                ->openable()
                ->acceptedFileTypes(['image/*'])
                ->maxFiles(10);
        }

        return $fields;
    }

    public static function buildPriceFields(Get $get, int $attributeId): array
    {
        $attribute = Attribute::find($attributeId);
        if (!$attribute) {
            return [
                Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $attributeId);
        if (empty($attributeValues)) {
            return [
                Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields[] = TextInput::make("attribute_price_{$attributeId}_{$valueObj->id}")
                ->label("Price for {$attribute->name}: {$valueObj->name}")
                ->numeric()
                ->minValue(0)
                ->step(0.01)
                ->prefix('$')
                ->required();
        }

        return $fields;
    }

    public static function buildQuantityFields(Get $get, int $attributeId): array
    {
        $attribute = Attribute::find($attributeId);
        if (!$attribute) {
            return [
                Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $attributeId);
        if (empty($attributeValues)) {
            return [
                Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields[] = TextInput::make("attribute_quantity_{$attributeId}_{$valueObj->id}")
                ->label("Quantity for {$attribute->name}: {$valueObj->name}")
                ->numeric()
                ->minValue(0)
                ->integer()
                ->required();
        }

        return $fields;
    }

    public static function buildSkuFields(Get $get, string $type): array
    {
        $attributeIds = $get('attribute_ids') ?? [];
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [
                Placeholder::make('no_attributes')
                    ->label('')
                    ->content('Please select attributes first in the previous steps.')
            ];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            switch ($type) {
                case 'image':
                    $fields[] = SpatieMediaLibraryFileUpload::make("sku_images_{$index}")
                        ->label("Images for {$attributeData['name']}")
                        ->collection('attribute-images')
                        ->multiple()
                        ->image()
                        ->imageEditor()
                        ->openable()
                        ->acceptedFileTypes(['image/*'])
                        ->maxFiles(10);
                    break;
                    
                case 'price':
                    $fields[] = TextInput::make("sku_price_{$index}")
                        ->label("Price for {$attributeData['name']}")
                        ->numeric()
                        ->minValue(0)
                        ->step(0.01)
                        ->prefix('$')
                        ->required();
                    break;
                    
                case 'quantity':
                    $fields[] = TextInput::make("sku_quantity_{$index}")
                        ->label("Quantity for {$attributeData['name']}")
                        ->numeric()
                        ->minValue(0)
                        ->integer()
                        ->required();
                    break;
            }
        }
        
        return $fields;
    }

    public static function buildSingleFields(string $type): array
    {
        switch ($type) {
            case 'image':
                return [
                    SpatieMediaLibraryFileUpload::make('single_images')
                        ->label('Images for All SKUs')
                        ->collection('attribute-images')
                        ->multiple()
                        ->image()
                        ->imageEditor()
                        ->openable()
                        ->acceptedFileTypes(['image/*'])
                        ->maxFiles(10),
                ];
                
            case 'price':
                return [
                    TextInput::make('single_price')
                        ->label('Price for All SKUs')
                        ->numeric()
                        ->minValue(0)
                        ->step(0.01)
                        ->prefix('$')
                        ->required(),
                ];
                
            case 'quantity':
                return [
                    TextInput::make('single_quantity')
                        ->label('Quantity for All SKUs')
                        ->numeric()
                        ->minValue(0)
                        ->integer()
                        ->required(),
                ];
                
            default:
                return [];
        }
    }
} 