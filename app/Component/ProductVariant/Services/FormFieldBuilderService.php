<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Services;

use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Awcodes\TableRepeater\Header;
use Awcodes\TableRepeater\Components\TableRepeater;

class FormFieldBuilderService
{
    /**
     * Create price inputs for East/West Malaysia
     */
    public static function createRegionalPriceInputs(string $basePath, string $label = ''): array
    {
        return [
            Section::make($label)
                ->schema([
                    Grid::make(2)->schema([
                        TextInput::make("{$basePath}.east")
                            ->label('East Malaysia')
                            ->numeric()
                            ->prefix('RM'),
                        TextInput::make("{$basePath}.west")
                            ->label('West Malaysia')
                            ->numeric()
                            ->prefix('RM'),
                    ])
                ])
        ];
    }

    /**
     * Create bonus price structure for both regions
     */
    public static function createBonusPriceInputs(string $basePath, string $label = ''): array
    {
        return [
            Section::make($label)
                ->schema([
                    Grid::make(2)->schema([
                        Group::make()->schema([
                            Section::make('East Malaysia')
                                ->schema([
                                    TextInput::make("{$basePath}.east.base_price")
                                        ->label('Base Price')
                                        ->numeric()
                                        ->prefix('RM'),
                                    Grid::make(2)->schema([
                                        TextInput::make("{$basePath}.east.bonus_1_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.east.bonus_1_free")->label('Get Free')->numeric(),
                                        TextInput::make("{$basePath}.east.bonus_2_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.east.bonus_2_free")->label('Get Free')->numeric(),
                                        TextInput::make("{$basePath}.east.bonus_3_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.east.bonus_3_free")->label('Get Free')->numeric(),
                                    ])
                                ])
                        ]),
                        Group::make()->schema([
                            Section::make('West Malaysia')
                                ->schema([
                                    TextInput::make("{$basePath}.west.base_price")
                                        ->label('Base Price')
                                        ->numeric()
                                        ->prefix('RM'),
                                    Grid::make(2)->schema([
                                        TextInput::make("{$basePath}.west.bonus_1_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.west.bonus_1_free")->label('Get Free')->numeric(),
                                        TextInput::make("{$basePath}.west.bonus_2_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.west.bonus_2_free")->label('Get Free')->numeric(),
                                        TextInput::make("{$basePath}.west.bonus_3_qty")->label('Buy Quantity')->numeric(),
                                        TextInput::make("{$basePath}.west.bonus_3_free")->label('Get Free')->numeric(),
                                    ])
                                ])
                        ])
                    ])
                ])
        ];
    }

    /**
     * Create stock input field
     */
    public static function createStockInput(string $fieldPath, string $label): TextInput
    {
        return TextInput::make($fieldPath)
            ->label($label)
            ->numeric()
            ->minValue(0)
            ->suffix('units')
            ->required();
    }

    /**
     * Create batch table repeater
     */
    public static function createBatchTable(string $fieldPath, string $label = ''): Section
    {
        return Section::make($label)
            ->schema([
                TableRepeater::make($fieldPath)
                    ->label('')
                    ->headers([
                        Header::make('batch_name')->label('Batch Number'),
                        Header::make('available_stock')->label('Stock'),
                        Header::make('expiry_date')->label('Expiry Date'),
                    ])
                    ->schema([
                        TextInput::make('batch_name')
                            ->label('Batch Name')
                            ->required(),
                        TextInput::make('available_stock')
                            ->label('Available Stock')
                            ->numeric()
                            ->minValue(0)
                            ->required(),
                        \Filament\Forms\Components\DatePicker::make('expiry_date')
                            ->label('Expiry Date')
                            ->minDate(today())
                            ->required(),
                    ])
                    ->defaultItems(1)
                    ->addActionLabel('+ Add Batch')
                    ->reorderable(false)
            ]);
    }

    /**
     * Create file upload field for images with safer naming
     */
    public static function createImageUpload(string $fieldPath, string $label): SpatieMediaLibraryFileUpload
    {
        // Convert problematic field paths to safer alternatives
        $safeFieldPath = self::createSafeFieldPath($fieldPath);
        
        return SpatieMediaLibraryFileUpload::make($safeFieldPath)
            ->label($label)
            ->collection('attribute-images')
            ->multiple()
            ->image()
            ->imageEditor()
            ->openable()
            ->acceptedFileTypes(['image/*'])
            ->maxFiles(10);
    }

    /**
     * Create a simple text input with prefix
     */
    public static function createPrefixedInput(string $fieldPath, string $label, string $prefix = 'RM'): TextInput
    {
        return TextInput::make($fieldPath)
            ->label($label)
            ->numeric()
            ->prefix($prefix);
    }

    /**
     * Convert dynamic field paths to safer Livewire-compatible names
     */
    private static function createSafeFieldPath(string $fieldPath): string
    {
        // Replace problematic characters and patterns
        $safePath = str_replace(['.', '_'], ['_dot_', '_'], $fieldPath);
        
        // For dynamic attribute combinations, use a more structured approach
        if (str_contains($fieldPath, 'attribute_combination_images')) {
            return 'variant_images.' . str_replace(['attribute_combination_images.', '.'], ['', '_'], $fieldPath);
        }
        
        if (str_contains($fieldPath, 'attribute_images')) {
            return 'sku_images.' . str_replace(['attribute_images.', '.'], ['', '_'], $fieldPath);
        }
        
        return $safePath;
    }
} 