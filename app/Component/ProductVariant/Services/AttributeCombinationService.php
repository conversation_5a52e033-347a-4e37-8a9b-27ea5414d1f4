<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Services;

use App\Models\Attribute;
use App\Models\AttributeValue;

class AttributeCombinationService
{
    /**
     * Generate all possible combinations of attribute values
     */
    public static function generateCombinations(array $attributeData): array
    {
        if (empty($attributeData)) {
            return [];
        }

        $combinations = [[]];
        
        foreach ($attributeData as $attributeName => $values) {
            $newCombinations = [];
            foreach ($combinations as $combination) {
                foreach ($values as $value) {
                    $newCombination = $combination;
                    $newCombination[$attributeName] = $value;
                    $newCombinations[] = $newCombination;
                }
            }
            $combinations = $newCombinations;
        }

        return $combinations;
    }

    /**
     * Get selected attributes data from form state
     */
    public static function getSelectedAttributesData(callable $get, array $attributeIds): array
    {
        $attributeData = [];
        
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $get("attribute_values_{$index}") ?? [];
                
                if (!empty($selectedValues)) {
                    $attribute = Attribute::find($attributeId);
                    if ($attribute) {
                        $values = AttributeValue::whereIn('id', $selectedValues)->get();
                        $attributeData[$attribute->name] = $values;
                    }
                }
            }
        }
        
        return $attributeData;
    }

    /**
     * Get selected attributes as simple array
     */
    public static function getSelectedAttributes(callable $get, array $attributeIds): array
    {
        $selectedAttributes = [];
        
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $get("attribute_values_{$index}") ?? [];
                
                if (!empty($selectedValues)) {
                    $attribute = Attribute::find($attributeId);
                    if ($attribute) {
                        $selectedAttributes[] = [
                            'id' => $attribute->id,
                            'name' => $attribute->name
                        ];
                    }
                }
            }
        }
        
        return $selectedAttributes;
    }

    /**
     * Generate combination key string for form field names
     */
    public static function generateCombinationKey(array $combination): string
    {
        return implode('_', array_map(fn($value) => $value->id, $combination));
    }

    /**
     * Generate display name for attribute combination
     */
    public static function generateDisplayName(array $combination): string
    {
        return implode(', ', array_map(
            fn($value, $attributeName) => "$attributeName: {$value->name}",
            $combination,
            array_keys($combination)
        ));
    }

    /**
     * Generate SKU for attribute combination
     */
    public static function generateSku(array $combination): string
    {
        $skuParts = [];
        foreach($combination as $attributeName => $value) {
            $skuParts[] = strtoupper(substr($attributeName, 0, 3)) . '-' . strtoupper(substr($value->name, 0, 3));
        }
        return implode('-', $skuParts);
    }
} 