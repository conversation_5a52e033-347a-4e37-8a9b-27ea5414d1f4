<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Services;

use Illuminate\Support\Collection;
use App\Component\ProductVariant\DTOs\VariantSummaryItem;
use App\Component\ProductVariant\Services\AttributeCombinationService;
use App\Models\Attribute;
use App\Models\AttributeValue;

class SummaryDataService
{
    public static function generateVariantsSummary(array $formData): Collection
    {
        $attributeIds = $formData['attribute_ids'] ?? [];
        
        if (empty($attributeIds)) {
            return collect();
        }

        // Check if any assignment type is "attribute" and needs special handling
        $needsAttributeFiltering = self::needsAttributeBasedFiltering($formData);
        
        if ($needsAttributeFiltering) {
            return self::generateAttributeBasedVariants($formData, $attributeIds);
        }

        // Get all selected attribute values for normal combinations
        $attributeData = self::extractAttributeDataFromForm($formData, $attributeIds);

        if (empty($attributeData)) {
            return collect();
        }

        // Generate all possible combinations
        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        
        $variants = collect();
        
        foreach ($combinations as $index => $combination) {
            $sku = AttributeCombinationService::generateSku($combination);
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $variants->push(new VariantSummaryItem(
                index: $index,
                sku: $sku,
                displayName: $displayName,
                attributes: $combination,
                pricing: self::extractPricingData($formData, $index, $combination),
                quantity: self::extractQuantityData($formData, $index, $combination),
                images: self::extractImageData($formData, $index, $combination),
                priceDisplay: self::generatePriceDisplay($formData, $index, $combination),
                quantityDisplay: self::generateQuantityDisplay($formData, $index, $combination),
                imagesCount: self::calculateImagesCount($formData, $index, $combination),
            ));
        }
        
        return $variants;
    }

    private static function extractPricingData(array $formData, int $index, array $combination): ?array
    {
        $priceAssignmentType = $formData['price_assignment_type'] ?? null;
        $priceType = $formData['price_type_toggle'] ?? 'fixed';
        
        if (!$priceAssignmentType) {
            return null;
        }

        return match ($priceAssignmentType) {
            'single' => self::getSinglePriceData($formData, $priceType),
            'sku' => self::getSkuPriceData($formData, $index, $priceType),
            'attribute' => self::getAttributePriceData($formData, $combination, $priceType),
            default => null,
        };
    }

    private static function extractQuantityData(array $formData, int $index, array $combination): ?array
    {
        $quantityAssignmentType = $formData['quantity_assignment_type'] ?? null;
        
        if (!$quantityAssignmentType) {
            return null;
        }

        return match ($quantityAssignmentType) {
            'single' => ['quantity' => $formData['single_quantity'] ?? null],
            'sku' => ['quantity' => $formData["sku_quantity_{$index}"] ?? null],
            'attribute' => self::getAttributeQuantityData($formData, $combination),
            default => null,
        };
    }

    private static function extractImageData(array $formData, int $index, array $combination): ?array
    {
        $imageAssignmentType = $formData['image_assignment_type'] ?? null;
        
        if (!$imageAssignmentType) {
            return null;
        }

        return match ($imageAssignmentType) {
            'single' => ['images' => $formData['single_images'] ?? []],
            'sku' => ['images' => $formData["sku_images_{$index}"] ?? []],
            'attribute' => self::getAttributeImageData($formData, $combination),
            default => null,
        };
    }

    private static function getSinglePriceData(array $formData, string $priceType): array
    {
        return match ($priceType) {
            'fixed' => [
                'east_price' => $formData['single_price_east'] ?? null,
                'west_price' => $formData['single_price_west'] ?? null,
            ],
            'bonus' => [
                'east_price' => $formData['single_bonus_price_east'] ?? null,
                'west_price' => $formData['single_bonus_price_west'] ?? null,
                'east_bonus' => $formData['single_bonus_price_east_bonus'] ?? null,
                'west_bonus' => $formData['single_bonus_price_west_bonus'] ?? null,
            ],
            'tier' => [
                'east_tiers' => $formData['single_tier_prices_east'] ?? [],
                'west_tiers' => $formData['single_tier_prices_west'] ?? [],
            ],
            default => [],
        };
    }

    private static function getSkuPriceData(array $formData, int $index, string $priceType): array
    {
        return match ($priceType) {
            'fixed' => [
                'east_price' => $formData["sku_prices_{$index}_east"] ?? null,
                'west_price' => $formData["sku_prices_{$index}_west"] ?? null,
            ],
            'bonus' => [
                'east_price' => $formData["sku_bonus_prices_{$index}_east"] ?? null,
                'west_price' => $formData["sku_bonus_prices_{$index}_west"] ?? null,
                'east_bonus' => $formData["sku_bonus_prices_{$index}_east_bonus"] ?? null,
                'west_bonus' => $formData["sku_bonus_prices_{$index}_west_bonus"] ?? null,
            ],
            'tier' => [
                'east_tiers' => $formData["sku_tier_prices_{$index}_east"] ?? [],
                'west_tiers' => $formData["sku_tier_prices_{$index}_west"] ?? [],
            ],
            default => [],
        };
    }

    private static function getAttributePriceData(array $formData, array $combination, string $priceType): array
    {
        $selectedAttributeId = $formData['price_selected_attribute'] ?? null;
        
        if (!$selectedAttributeId) {
            return [];
        }

        // Find the attribute value for this combination
        $attributeValueId = null;
        foreach ($combination as $attr) {
            if ($attr['attribute_id'] == $selectedAttributeId) {
                $attributeValueId = $attr['value_id'];
                break;
            }
        }

        if (!$attributeValueId) {
            return [];
        }

        return match ($priceType) {
            'fixed' => [
                'east_price' => $formData["attribute_price_{$selectedAttributeId}_{$attributeValueId}_east"] ?? null,
                'west_price' => $formData["attribute_price_{$selectedAttributeId}_{$attributeValueId}_west"] ?? null,
            ],
            'bonus' => [
                'east_price' => $formData["attribute_bonus_price_{$selectedAttributeId}_{$attributeValueId}_east"] ?? null,
                'west_price' => $formData["attribute_bonus_price_{$selectedAttributeId}_{$attributeValueId}_west"] ?? null,
                'east_bonus' => $formData["attribute_bonus_price_{$selectedAttributeId}_{$attributeValueId}_east_bonus"] ?? null,
                'west_bonus' => $formData["attribute_bonus_price_{$selectedAttributeId}_{$attributeValueId}_west_bonus"] ?? null,
            ],
            'tier' => [
                'east_tiers' => $formData["attribute_tier_price_{$selectedAttributeId}_{$attributeValueId}_east"] ?? [],
                'west_tiers' => $formData["attribute_tier_price_{$selectedAttributeId}_{$attributeValueId}_west"] ?? [],
            ],
            default => [],
        };
    }

    private static function getAttributeQuantityData(array $formData, array $combination): array
    {
        $selectedAttributeId = $formData['quantity_selected_attribute'] ?? null;
        
        if (!$selectedAttributeId) {
            return [];
        }

        // Find the attribute value for this combination
        $attributeValueId = null;
        foreach ($combination as $attr) {
            if ($attr['attribute_id'] == $selectedAttributeId) {
                $attributeValueId = $attr['value_id'];
                break;
            }
        }

        if (!$attributeValueId) {
            return [];
        }

        return [
            'quantity' => $formData["attribute_quantity_{$selectedAttributeId}_{$attributeValueId}"] ?? null,
        ];
    }

    private static function getAttributeImageData(array $formData, array $combination): array
    {
        $selectedAttributeId = $formData['image_selected_attribute'] ?? null;
        
        if (!$selectedAttributeId) {
            return [];
        }

        // Find the attribute value for this combination
        $attributeValueId = null;
        foreach ($combination as $attr) {
            if ($attr['attribute_id'] == $selectedAttributeId) {
                $attributeValueId = $attr['value_id'];
                break;
            }
        }

        if (!$attributeValueId) {
            return [];
        }

        return [
            'images' => $formData["attribute_image_{$selectedAttributeId}_{$attributeValueId}"] ?? [],
        ];
    }

    private static function generatePriceDisplay(array $formData, int $index, array $combination): ?string
    {
        $pricing = self::extractPricingData($formData, $index, $combination);
        
        if (!$pricing) {
            return null;
        }

        $priceType = $formData['price_type_toggle'] ?? 'fixed';
        
        return match ($priceType) {
            'fixed' => self::formatFixedPrice($pricing),
            'bonus' => self::formatBonusPrice($pricing),
            'tier' => self::formatTierPrice($pricing),
            default => 'N/A',
        };
    }

    private static function generateQuantityDisplay(array $formData, int $index, array $combination): ?string
    {
        $quantity = self::extractQuantityData($formData, $index, $combination);
        
        if (!$quantity || !isset($quantity['quantity'])) {
            return null;
        }

        return (string) $quantity['quantity'];
    }

    private static function calculateImagesCount(array $formData, int $index, array $combination): ?int
    {
        $images = self::extractImageData($formData, $index, $combination);
        
        if (!$images || !isset($images['images'])) {
            return null;
        }

        return is_array($images['images']) ? count($images['images']) : 0;
    }

    private static function formatFixedPrice(array $pricing): string
    {
        $east = $pricing['east_price'] ?? 0;
        $west = $pricing['west_price'] ?? 0;
        
        if ($east == $west) {
            return "RM {$east}";
        }
        
        return "E: RM {$east} | W: RM {$west}";
    }

    private static function formatBonusPrice(array $pricing): string
    {
        $eastPrice = $pricing['east_price'] ?? 0;
        $westPrice = $pricing['west_price'] ?? 0;
        $eastBonus = $pricing['east_bonus'] ?? 0;
        $westBonus = $pricing['west_bonus'] ?? 0;
        
        return "E: RM {$eastPrice} (+{$eastBonus}) | W: RM {$westPrice} (+{$westBonus})";
    }

    private static function formatTierPrice(array $pricing): string
    {
        $eastTiers = $pricing['east_tiers'] ?? [];
        $westTiers = $pricing['west_tiers'] ?? [];
        
        $eastCount = count($eastTiers);
        $westCount = count($westTiers);
        
        return "E: {$eastCount} tiers | W: {$westCount} tiers";
    }

    private static function extractAttributeDataFromForm(array $formData, array $attributeIds): array
    {
        $attributeData = [];
        
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $formData["attribute_values_{$index}"] ?? [];
                
                if (!empty($selectedValues)) {
                    $attribute = Attribute::find($attributeId);
                    if ($attribute) {
                        $values = AttributeValue::whereIn('id', $selectedValues)->get();
                        $attributeData[$attribute->name] = $values;
                    }
                }
            }
        }
        
        return $attributeData;
    }

    private static function needsAttributeBasedFiltering(array $formData): bool
    {
        $priceAssignmentType = $formData['price_assignment_type'] ?? null;
        $quantityAssignmentType = $formData['quantity_assignment_type'] ?? null;
        $imageAssignmentType = $formData['image_assignment_type'] ?? null;

        return $priceAssignmentType === 'attribute' || 
               $quantityAssignmentType === 'attribute' || 
               $imageAssignmentType === 'attribute';
    }

    private static function generateAttributeBasedVariants(array $formData, array $attributeIds): Collection
    {
        // For attribute-based assignments, we need to generate variants only for the selected attribute values
        $variants = collect();
        
        // Get the primary selected attribute (price takes precedence, then quantity, then image)
        $selectedAttributeId = null;
        if ($formData['price_assignment_type'] === 'attribute') {
            $selectedAttributeId = $formData['price_selected_attribute'] ?? null;
        } elseif ($formData['quantity_assignment_type'] === 'attribute') {
            $selectedAttributeId = $formData['quantity_selected_attribute'] ?? null;
        } elseif ($formData['image_assignment_type'] === 'attribute') {
            $selectedAttributeId = $formData['image_selected_attribute'] ?? null;
        }
        
        if (!$selectedAttributeId) {
            return collect(); // No attribute selected for assignment
        }

        // Cast to integer since form data comes as string
        $selectedAttributeId = (int) $selectedAttributeId;

        // Find which index this attribute is at
        $attributeIndex = null;
        foreach ($attributeIds as $index => $attributeId) {
            if ($attributeId == $selectedAttributeId) {
                $attributeIndex = $index + 1; // Form fields are 1-indexed
                break;
            }
        }

        if (!$attributeIndex) {
            return collect(); // Attribute not found
        }

        // Get selected values for this attribute
        $selectedAttributeValues = $formData["attribute_values_{$attributeIndex}"] ?? [];
        
        if (empty($selectedAttributeValues)) {
            return collect(); // No values selected for this attribute
        }

        // Get the attribute and its selected values
        $attribute = Attribute::find($selectedAttributeId);
        if (!$attribute) {
            return collect();
        }

        $attributeValues = AttributeValue::whereIn('id', $selectedAttributeValues)->get();
        
        // Generate variants for each selected attribute value
        foreach ($attributeValues as $index => $attributeValue) {
            // Create a simple combination with just this attribute
            $combination = [$attribute->name => $attributeValue];
            
            $sku = AttributeCombinationService::generateSku($combination);
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $variants->push(new VariantSummaryItem(
                index: $index,
                sku: $sku,
                displayName: $displayName,
                attributes: $combination,
                pricing: self::extractPricingDataForAttribute($formData, $selectedAttributeId, $attributeValue->id),
                quantity: self::extractQuantityDataForAttribute($formData, $selectedAttributeId, $attributeValue->id),
                images: self::extractImageDataForAttribute($formData, $selectedAttributeId, $attributeValue->id),
                priceDisplay: self::generatePriceDisplayForAttribute($formData, $selectedAttributeId, $attributeValue->id),
                quantityDisplay: self::generateQuantityDisplayForAttribute($formData, $selectedAttributeId, $attributeValue->id),
                imagesCount: self::calculateImagesCountForAttribute($formData, $selectedAttributeId, $attributeValue->id),
            ));
        }
        
        return $variants;
    }

    private static function extractPricingDataForAttribute(array $formData, int $attributeId, int $valueId): ?array
    {
        $priceAssignmentType = $formData['price_assignment_type'] ?? null;
        $priceType = $formData['price_type_toggle'] ?? 'fixed';
        
        if ($priceAssignmentType !== 'attribute') {
            return null;
        }

        return match ($priceType) {
            'fixed' => [
                'east_price' => $formData["attribute_price_{$attributeId}_{$valueId}_east"] ?? null,
                'west_price' => $formData["attribute_price_{$attributeId}_{$valueId}_west"] ?? null,
            ],
            'bonus' => [
                'east_price' => $formData["attribute_bonus_price_{$attributeId}_{$valueId}_east"] ?? null,
                'west_price' => $formData["attribute_bonus_price_{$attributeId}_{$valueId}_west"] ?? null,
                'east_bonus' => $formData["attribute_bonus_price_{$attributeId}_{$valueId}_east_bonus"] ?? null,
                'west_bonus' => $formData["attribute_bonus_price_{$attributeId}_{$valueId}_west_bonus"] ?? null,
            ],
            'tier' => [
                'east_tiers' => $formData["attribute_tier_price_{$attributeId}_{$valueId}_east"] ?? [],
                'west_tiers' => $formData["attribute_tier_price_{$attributeId}_{$valueId}_west"] ?? [],
            ],
            default => [],
        };
    }

    private static function extractQuantityDataForAttribute(array $formData, int $attributeId, int $valueId): ?array
    {
        $quantityAssignmentType = $formData['quantity_assignment_type'] ?? null;
        
        if ($quantityAssignmentType !== 'attribute') {
            return null;
        }

        return [
            'quantity' => $formData["attribute_quantity_{$attributeId}_{$valueId}"] ?? null,
        ];
    }

    private static function extractImageDataForAttribute(array $formData, int $attributeId, int $valueId): ?array
    {
        $imageAssignmentType = $formData['image_assignment_type'] ?? null;
        
        if ($imageAssignmentType !== 'attribute') {
            return null;
        }

        return [
            'images' => $formData["attribute_image_{$attributeId}_{$valueId}"] ?? [],
        ];
    }

    private static function generatePriceDisplayForAttribute(array $formData, int $attributeId, int $valueId): ?string
    {
        $pricing = self::extractPricingDataForAttribute($formData, $attributeId, $valueId);
        
        if (!$pricing) {
            return null;
        }

        $priceType = $formData['price_type_toggle'] ?? 'fixed';
        
        return match ($priceType) {
            'fixed' => self::formatFixedPrice($pricing),
            'bonus' => self::formatBonusPrice($pricing),
            'tier' => self::formatTierPrice($pricing),
            default => 'N/A',
        };
    }

    private static function generateQuantityDisplayForAttribute(array $formData, int $attributeId, int $valueId): ?string
    {
        $quantity = self::extractQuantityDataForAttribute($formData, $attributeId, $valueId);
        
        if (!$quantity || !isset($quantity['quantity'])) {
            return null;
        }

        return (string) $quantity['quantity'];
    }

    private static function calculateImagesCountForAttribute(array $formData, int $attributeId, int $valueId): ?int
    {
        $images = self::extractImageDataForAttribute($formData, $attributeId, $valueId);
        
        if (!$images || !isset($images['images'])) {
            return null;
        }

        return is_array($images['images']) ? count($images['images']) : 0;
    }
} 