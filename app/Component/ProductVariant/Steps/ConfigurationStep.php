<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Wizard\Step;

class ConfigurationStep
{
    public static function make(): Step
    {
        return Step::make('Configuration')
            ->schema([
                Radio::make('stock_type_toggle')
                    ->label('Stock Type')
                    ->options([
                        'stock' => 'By Stock',
                        'batch' => 'By Batch',
                    ])
                    ->inline()
                    ->live()
                    ->default('batch')
                    ->required(),
                Radio::make('price_type_toggle')
                    ->label('Price Type')
                    ->options([
                        'fixed' => 'Fix Price',
                        'bonus' => 'Bonus Prices',
                        'tier' => 'Tier Prices',
                    ])
                    ->inline()
                    ->live()
                    ->formatStateUsing(fn() => 'fixed'),
            ]);
    }
} 