<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Get;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard\Step;
use App\Component\ProductVariant\Stock\StockAssignmentBuilder;
use App\Component\ProductVariant\Stock\BatchAssignmentBuilder;
use App\Component\ProductVariant\Components\AssignmentTypeSelector;
use App\Component\ProductVariant\Services\AttributeAssignmentBuilder;

class QuantityStep
{
    public static function make(): Step
    {
        return Step::make('Quantity')
            ->schema([
                // Stock Configuration Section - shown for all variants
                Section::make('Stock Configuration')
                    ->schema([
                        Grid::make(3)->schema([
                            TextInput::make('low_stock')
                                ->label('Low Stock Trigger Value')
                                ->numeric()
                                ->minValue(0)
                                ->visible(fn(Get $get) => $get('in_stock') === 'yes')
                                ->required(),
                            TextInput::make('whole_sale_pack_size')
                                ->label('Wholesale Pack Size')
                                ->numeric()
                                ->minValue(1)
                                ->required(),
                            \Filament\Forms\Components\DatePicker::make('expires_on_after')
                                ->label('Expiry Date')
                                ->visible(function (Get $get) {
                                    $stockTypeToggle = $get('../stock_type_toggle');
                                    return $get('in_stock') === 'yes' && $stockTypeToggle === 'stock';
                                })
                                ->minDate(today())
                                ->required(),
                            \Filament\Forms\Components\Select::make('in_stock')
                                ->label('Stock Status')
                                ->options([
                                    'yes' => 'In Stock',
                                    'no' => 'Out of Stock',
                                ])
                                ->default('yes')
                                ->live()
                                ->required(),
                        ]),
                    ]),
                    
                // Quantity Assignment Section
                Section::make('Quantity Assignment')
                    ->visible(function (callable $get) {
                        $stockTypeToggle = $get('stock_type_toggle');
                        $inStock = $get('in_stock');
                        
                        // Show when stock type is 'stock' and in stock
                        return $stockTypeToggle === 'stock' && $inStock === 'yes';
                    })
                    ->schema([
                        // Assignment Type Selector with Attribute Selection
                        ...AssignmentTypeSelector::make('Quantity'),
                        
                        // Single Quantity Section
                        Section::make('Quantity for All SKUs')
                            ->visible(fn(Get $get) => $get('quantity_assignment_type') === 'single')
                            ->schema(function (Get $get) {
                                return AttributeAssignmentBuilder::buildSingleFields('quantity');
                            }),
                            
                        // SKU-based Quantity Section
                        Section::make('Quantity by SKU')
                            ->visible(fn(Get $get) => $get('quantity_assignment_type') === 'sku')
                            ->description('Set quantities for each SKU variant')
                            ->schema(function (Get $get) {
                                return AttributeAssignmentBuilder::buildSkuFields($get, 'quantity');
                            }),
                            
                        // Attribute-based Quantity Section
                        Section::make('Quantity by Selected Attribute')
                            ->visible(function (Get $get) {
                                return $get('quantity_assignment_type') === 'attribute' && 
                                       $get('quantity_selected_attribute') !== null;
                            })
                            ->description('Set quantities for each value of the selected attribute')
                            ->schema(function (Get $get) {
                                $selectedAttributeId = AssignmentTypeSelector::getSelectedAttributeId($get, 'Quantity');
                                
                                if (!$selectedAttributeId) {
                                    return [
                                        \Filament\Forms\Components\Placeholder::make('select_attribute')
                                            ->label('')
                                            ->content('Please select an attribute above.')
                                    ];
                                }
                                
                                return AttributeAssignmentBuilder::buildQuantityFields($get, $selectedAttributeId);
                            }),
                    ]),
                    
                // Batch Assignment Section
                Section::make('Batch Assignment')
                    ->visible(function (callable $get) {
                        $stockTypeToggle = $get('stock_type_toggle');
                        $inStock = $get('in_stock');
                        
                        // Show when stock type is 'batch' and in stock
                        return $stockTypeToggle === 'batch' && $inStock === 'yes';
                    })
                    ->schema([
                        // Assignment Type Selector with Attribute Selection - same as other sections
                        ...AssignmentTypeSelector::make('Batch'),
                        
                        // Single Batch Section
                        Section::make('Batch for All SKUs')
                            ->visible(fn(Get $get) => $get('batch_assignment_type') === 'single')
                            ->schema(function (Get $get) {
                                return self::buildSingleBatchInputs($get);
                            }),
                            
                        // SKU-based Batch Section  
                        Section::make('Batch by SKU')
                            ->visible(fn(Get $get) => $get('batch_assignment_type') === 'sku')
                            ->description('Create batches for each SKU variant')
                            ->schema(function (Get $get) {
                                return self::buildSkuBatchInputs($get);
                            }),
                            
                        // Attribute-based Batch Section
                        Section::make('Batch by Selected Attribute')
                            ->visible(function (Get $get) {
                                return $get('batch_assignment_type') === 'attribute' && 
                                       $get('batch_selected_attribute') !== null;
                            })
                            ->description('Create batches for each value of the selected attribute')
                            ->schema(function (Get $get) {
                                return self::buildAttributeBatchInputs($get);
                            }),
                    ]),
            ]);
    }

    private static function buildSingleBatchInputs(Get $get): array
    {
        return BatchAssignmentBuilder::buildForAssignmentType($get, 'single', []);
    }

    private static function buildSkuBatchInputs(Get $get): array
    {
        $attributeIds = $get('attribute_ids') ?? [];
        return BatchAssignmentBuilder::buildForAssignmentType($get, 'sku', $attributeIds);
    }

    private static function buildAttributeBatchInputs(Get $get): array
    {
        $selectedAttributeId = AssignmentTypeSelector::getSelectedAttributeId($get, 'Batch');
        
        if (!$selectedAttributeId) {
            return [
                \Filament\Forms\Components\Placeholder::make('select_attribute')
                    ->label('')
                    ->content('Please select an attribute above.')
            ];
        }
        
        // Get only the selected attribute values for the chosen attribute
        $attributeIds = $get('attribute_ids') ?? [];
        
        // Find which index this attribute is at
        $attributeIndex = null;
        foreach ($attributeIds as $index => $formAttributeId) {
            if ($formAttributeId == $selectedAttributeId) {
                $attributeIndex = $index + 1; // Form fields are 1-indexed
                break;
            }
        }
        
        if (!$attributeIndex) {
            return [
                \Filament\Forms\Components\Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found in form.')
            ];
        }
        
        // Get selected values for this specific attribute
        $selectedValues = $get("attribute_values_{$attributeIndex}") ?? [];
        
        if (empty($selectedValues)) {
            return [
                \Filament\Forms\Components\Placeholder::make('no_values_selected')
                    ->label('')
                    ->content('Please select values for this attribute in the Attributes step.')
            ];
        }
        
        // Get the attribute and its selected values
        $attribute = \App\Models\Attribute::find($selectedAttributeId);
        if (!$attribute) {
            return [
                \Filament\Forms\Components\Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }
        
        $attributeValues = \App\Models\AttributeValue::whereIn('id', $selectedValues)->get();
        
        $fields = [];
        foreach ($attributeValues as $value) {
            $fields[] = \App\Component\ProductVariant\Services\FormFieldBuilderService::createBatchTable(
                "attribute_batches_{$selectedAttributeId}_{$value->id}",
                "Batch Details for {$attribute->name}: {$value->name}"
            );
        }
        
        return $fields;
    }

    private static function buildBatchInputs(Get $get): array
    {
        // This method is now deprecated in favor of the specific methods above
        // Keeping it for backward compatibility if needed
        $stockTypeToggle = $get('stock_type_toggle');
        $attributeIds = $get('attribute_ids') ?? [];
        $inStock = $get('in_stock');
        $batchAssignmentType = $get('batch_assignment_type');
        
        // Handle Batch Assignment
        if ($stockTypeToggle === 'batch' && $inStock === 'yes') {
            if (!in_array($batchAssignmentType, ['attribute', 'sku', 'single'])) {
                return [];
            }
            
            // Single batch assignment is handled by the separate TableRepeater section
            if ($batchAssignmentType === 'single') {
                return []; // Handled by separate batch management section
            }
            
            return BatchAssignmentBuilder::buildForAssignmentType($get, $batchAssignmentType, $attributeIds);
        }
        
        return [];
    }
} 