<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Get;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard\Step;
use Illuminate\Support\HtmlString;
use App\Component\ProductVariant\Services\SummaryDataService;

class SummaryStep
{
    public static function make(): Step
    {
        return Step::make('Summary')
            ->schema([
                Section::make('Product Variants Summary')
                    ->description('Review all product variants that will be created')
                    ->schema([
                        \Filament\Forms\Components\Placeholder::make('variants_summary')
                            ->label('')
                            ->content(function (callable $get) {
                                return self::generateVariantsSummary($get);
                            })
                            ->live()
                            ->columnSpanFull()
                    ])
            ]);
    }

    private static function generateVariantsSummary(callable $get): HtmlString
    {
        // Collect all form data
        $formData = [
            'attribute_ids' => $get('attribute_ids'),
            'attribute_values_1' => $get('attribute_values_1'),
            'attribute_values_2' => $get('attribute_values_2'),
            'attribute_values_3' => $get('attribute_values_3'),
            'attribute_values_4' => $get('attribute_values_4'),
            'attribute_values_5' => $get('attribute_values_5'),
            'attribute_values_6' => $get('attribute_values_6'),
            'attribute_values_7' => $get('attribute_values_7'),
            'attribute_values_8' => $get('attribute_values_8'),
            'attribute_values_9' => $get('attribute_values_9'),
            'attribute_values_10' => $get('attribute_values_10'),
            'price_assignment_type' => $get('price_assignment_type'),
            'price_selected_attribute' => $get('price_selected_attribute'),
            'price_type_toggle' => $get('price_type_toggle'),
            'quantity_assignment_type' => $get('quantity_assignment_type'),
            'quantity_selected_attribute' => $get('quantity_selected_attribute'),
            'image_assignment_type' => $get('image_assignment_type'),
            'image_selected_attribute' => $get('image_selected_attribute'),
            'single_quantity' => $get('single_quantity'),
        ];

        $variants = SummaryDataService::generateVariantsSummary($formData);
        
        if ($variants->isEmpty()) {
            return new HtmlString(view('components.empty-variants-state')->render());
        }

        return new HtmlString(view('components.variants-summary-table', [
            'variants' => $variants,
            'formData' => $formData
        ])->render());
    }
} 