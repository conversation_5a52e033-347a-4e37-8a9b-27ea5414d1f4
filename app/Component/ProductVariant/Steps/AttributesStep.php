<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Get;
use App\Models\Attribute;
use App\Models\AttributeValue;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Wizard\Step;

class AttributesStep
{
    public static function make(): Step
    {
        return Step::make('Attributs & Values')
            ->schema([
                Grid::make()
                    ->columnSpanFull()
                    ->schema([
                        // Left Panel - Attributes List
                        Group::make()
                            ->columns(1)
                            ->schema([
                                Section::make('Attributes')
                                    ->headerActions([
                                        Action::make('create')
                                            ->form([
                                                TextInput::make('name')
                                                    ->required(),
                                            ])
                                            ->action(function ($data) {
                                                Attribute::create($data);
                                                Notification::make()
                                                    ->title('Attribute Created')
                                                    ->success()
                                                    ->send();
                                            })
                                    ])
                                    ->schema([
                                        CheckboxList::make('attribute_ids')
                                            ->columns(2)
                                            ->live()
                                            ->label('Attributes')
                                            ->options(fn() => Attribute::pluck('name', 'id'))
                                            ->reactive()
                                            ->afterStateUpdated(fn(callable $set) => $set('attribute_values_by_attribute', [])),
                                    ])
                            ]),

                        // Right Panel - Attribute Value Lists (Static Schema Workaround)
                        Group::make()
                            ->columns(1)
                            ->schema([
                                // Generate static sections for the first 10 attributes (adjust as needed)
                                ...collect(range(1, 10))->map(function ($index) {
                                    return self::createAttributeValueSection($index);
                                })->toArray()
                            ]),
                    ])
                    ->columns([
                        'sm' => 3,
                    ]),
            ])
            ->columns(2);
    }

    private static function createAttributeValueSection(int $index): Section
    {
        return Section::make('')
            ->headerActions([
                Action::make('create')
                    ->form([
                        TextInput::make('name')
                            ->required(),
                    ])
                    ->action(function ($data, callable $get) use ($index) {
                        $attributeIds = $get('attribute_ids') ?? [];
                        if (isset($attributeIds[$index - 1])) {
                            $attribute = Attribute::find($attributeIds[$index - 1]);
                            if ($attribute) {
                                $attribute->values()->create($data);
                                Notification::make()
                                    ->title('Attribute Value Created')
                                    ->success()
                                    ->send();
                            }
                        }
                    })
                    ->visible(function (callable $get) use ($index) {
                        $attributeIds = $get('attribute_ids') ?? [];
                        return isset($attributeIds[$index - 1]);
                    })
            ])
            ->columns(2)
            ->heading(function (callable $get) use ($index) {
                $attributeIds = $get('attribute_ids') ?? [];
                if (isset($attributeIds[$index - 1])) {
                    $attribute = Attribute::find($attributeIds[$index - 1]);
                    return $attribute ? $attribute->name : '';
                }
                return '';
            })
            ->visible(function (callable $get) use ($index) {
                $attributeIds = $get('attribute_ids') ?? [];
                return isset($attributeIds[$index - 1]);
            })
            ->schema([
                CheckboxList::make("attribute_values_{$index}")
                    ->label('')
                    ->options(function (callable $get) use ($index) {
                        $attributeIds = $get('attribute_ids') ?? [];
                        if (isset($attributeIds[$index - 1])) {
                            $attributeId = $attributeIds[$index - 1];
                            return AttributeValue::where('attribute_id', $attributeId)
                                ->pluck('name', 'id')
                                ->toArray();
                        }
                        return [];
                    })
                    ->live()
                    ->columns(2)
                    ->afterStateUpdated(function ($state, callable $get) use ($index) {
                        $attributeIds = $get('attribute_ids') ?? [];
                        $attributeId = $attributeIds[$index - 1] ?? null;
                        
                        if ($attributeId) {
                            Log::info("Static Attribute {$attributeId} values selected", [
                                'attribute_id' => $attributeId,
                                'field_index' => $index,
                                'selected_values' => $state ?? []
                            ]);
                        }
                    })
            ]);
    }
} 