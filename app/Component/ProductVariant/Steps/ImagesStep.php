<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Get;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard\Step;
use App\Component\ProductVariant\Services\AttributeCombinationService;
use App\Component\ProductVariant\Components\AssignmentTypeSelector;
use App\Component\ProductVariant\Services\AttributeAssignmentBuilder;

class ImagesStep
{
    public static function make(): Step
    {
        return Step::make('Images')
            ->schema([
                // Assignment Type Selector with Attribute Selection
                ...AssignmentTypeSelector::make('Image'),
                    
                // Single Images Section
                Section::make('Images for All SKUs')
                    ->visible(fn(Get $get) => $get('image_assignment_type') === 'single')
                    ->schema(function (Get $get) {
                        return AttributeAssignmentBuilder::buildSingleFields('image');
                    }),
                    
                // SKU-based Images Section
                Section::make('Images by SKU')
                    ->visible(fn(Get $get) => $get('image_assignment_type') === 'sku')
                    ->description('Upload images for each SKU variant')
                    ->schema(function (Get $get) {
                        return AttributeAssignmentBuilder::buildSkuFields($get, 'image');
                    }),
                    
                // Attribute-based Images Section
                Section::make('Images by Selected Attribute')
                    ->visible(function (Get $get) {
                        return $get('image_assignment_type') === 'attribute' && 
                               $get('image_selected_attribute') !== null;
                    })
                    ->description('Upload images for each value of the selected attribute')
                    ->schema(function (Get $get) {
                        $selectedAttributeId = AssignmentTypeSelector::getSelectedAttributeId($get, 'Image');
                        
                        if (!$selectedAttributeId) {
                            return [
                                \Filament\Forms\Components\Placeholder::make('select_attribute')
                                    ->label('')
                                    ->content('Please select an attribute above.')
                            ];
                        }
                        
                        return AttributeAssignmentBuilder::buildImageFields($get, $selectedAttributeId);
                    }),
            ]);
    }
} 