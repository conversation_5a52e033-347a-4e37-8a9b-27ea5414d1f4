<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Steps;

use Filament\Forms\Get;
use App\Models\AttributeValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard\Step;
use App\Component\ProductVariant\Pricing\FixedPriceBuilder;
use App\Component\ProductVariant\Pricing\BonusPriceBuilder;
use App\Component\ProductVariant\Pricing\TierPriceBuilder;
use App\Component\ProductVariant\Components\AssignmentTypeSelector;
use App\Component\ProductVariant\Services\AttributeAssignmentBuilder;

class PricingStep
{
    /**
     * Get selected attribute values from form state
     */
    private static function getSelectedAttributeValues(Get $get, int $attributeId): array
    {
        $attributeIds = $get('attribute_ids') ?? [];
        
        // Find which index this attribute is at
        $attributeIndex = null;
        foreach ($attributeIds as $index => $formAttributeId) {
            if ($formAttributeId == $attributeId) {
                $attributeIndex = $index + 1; // Form fields are 1-indexed
                break;
            }
        }
        
        if (!$attributeIndex) {
            // If not found in form, return all values (fallback)
            $attribute = \App\Models\Attribute::find($attributeId);
            return $attribute ? $attribute->values->toArray() : [];
        }
        
        // Get selected values for this attribute
        $selectedAttributeValues = $get("attribute_values_{$attributeIndex}") ?? [];
        
        if (empty($selectedAttributeValues)) {
            return [];
        }
        
        // Return only the selected attribute values
        return AttributeValue::whereIn('id', $selectedAttributeValues)->get()->toArray();
    }

    public static function make(): Step
    {
        return Step::make('Price')
            ->schema([
                // Assignment Type Selector with Attribute Selection
                ...AssignmentTypeSelector::make('Price'),
                    
                // Single Price Section
                Section::make('Price for All SKUs')
                    ->visible(fn(Get $get) => $get('price_assignment_type') === 'single')
                    ->schema(function (Get $get) {
                        return self::buildSinglePriceInputs($get);
                    }),
                    
                // SKU-based Price Section
                Section::make('Price by SKU')
                    ->visible(fn(Get $get) => $get('price_assignment_type') === 'sku')
                    ->description('Set prices for each SKU variant')
                    ->schema(function (Get $get) {
                        return self::buildSkuPriceInputs($get);
                    }),
                    
                // Attribute-based Price Section
                Section::make('Price by Selected Attribute')
                    ->visible(function (Get $get) {
                        return $get('price_assignment_type') === 'attribute' && 
                               $get('price_selected_attribute') !== null;
                    })
                    ->description('Set prices for each value of the selected attribute')
                    ->schema(function (Get $get) {
                        $selectedAttributeId = AssignmentTypeSelector::getSelectedAttributeId($get, 'Price');
                        
                        if (!$selectedAttributeId) {
                            return [
                                \Filament\Forms\Components\Placeholder::make('select_attribute')
                                    ->label('')
                                    ->content('Please select an attribute above.')
                            ];
                        }
                        
                        return self::buildAttributePriceInputs($get, $selectedAttributeId);
                    }),
            ]);
    }

    private static function buildSinglePriceInputs(Get $get): array
    {
        $priceType = $get('price_type_toggle') ?? 'fixed';

        try {
            return match ($priceType) {
                'fixed' => FixedPriceBuilder::buildForAssignmentType($get, 'single'),
                'bonus' => BonusPriceBuilder::buildForAssignmentType($get, 'single'),
                'tier' => TierPriceBuilder::buildForAssignmentType($get, 'single'),
                default => FixedPriceBuilder::buildForAssignmentType($get, 'single'),
            };
        } catch (\Exception $e) {
            return [
                \Filament\Forms\Components\Placeholder::make('error')
                    ->label('')
                    ->content('Error loading price configuration: ' . $e->getMessage())
            ];
        }
    }

    private static function buildSkuPriceInputs(Get $get): array
    {
        $priceType = $get('price_type_toggle') ?? 'fixed';

        try {
            return match ($priceType) {
                'fixed' => FixedPriceBuilder::buildForAssignmentType($get, 'sku'),
                'bonus' => BonusPriceBuilder::buildForAssignmentType($get, 'sku'),
                'tier' => TierPriceBuilder::buildForAssignmentType($get, 'sku'),
                default => FixedPriceBuilder::buildForAssignmentType($get, 'sku'),
            };
        } catch (\Exception $e) {
            return [
                \Filament\Forms\Components\Placeholder::make('error')
                    ->label('')
                    ->content('Error loading SKU price configuration: ' . $e->getMessage())
            ];
        }
    }

    private static function buildAttributePriceInputs(Get $get, int $selectedAttributeId): array
    {
        $priceType = $get('price_type_toggle') ?? 'fixed';

        try {
            return match ($priceType) {
                'fixed' => self::buildAttributeFixedPricing($get, $selectedAttributeId),
                'bonus' => self::buildAttributeBonusPricing($get, $selectedAttributeId),
                'tier' => self::buildAttributeTierPricing($get, $selectedAttributeId),
                default => self::buildAttributeFixedPricing($get, $selectedAttributeId),
            };
        } catch (\Exception $e) {
            return [
                \Filament\Forms\Components\Placeholder::make('error')
                    ->label('')
                    ->content('Error loading attribute price configuration: ' . $e->getMessage())
            ];
        }
    }

    private static function buildAttributeFixedPricing(Get $get, int $selectedAttributeId): array
    {
        // Use FormFieldBuilderService to create regional price inputs for each selected attribute value
        $attribute = \App\Models\Attribute::find($selectedAttributeId);
        if (!$attribute) {
            return [
                \Filament\Forms\Components\Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $selectedAttributeId);
        if (empty($attributeValues)) {
            return [
                \Filament\Forms\Components\Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields = array_merge($fields, \App\Component\ProductVariant\Services\FormFieldBuilderService::createRegionalPriceInputs(
                "attribute_price_{$selectedAttributeId}_{$valueObj->id}",
                "Price for {$attribute->name}: {$valueObj->name}"
            ));
        }

        return $fields;
    }

    private static function buildAttributeBonusPricing(Get $get, int $selectedAttributeId): array
    {
        $attribute = \App\Models\Attribute::find($selectedAttributeId);
        if (!$attribute) {
            return [
                \Filament\Forms\Components\Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $selectedAttributeId);
        if (empty($attributeValues)) {
            return [
                \Filament\Forms\Components\Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields = array_merge($fields, \App\Component\ProductVariant\Services\FormFieldBuilderService::createBonusPriceInputs(
                "attribute_bonus_price_{$selectedAttributeId}_{$valueObj->id}",
                "Bonus Price for {$attribute->name}: {$valueObj->name}"
            ));
        }

        return $fields;
    }

    private static function buildAttributeTierPricing(Get $get, int $selectedAttributeId): array
    {
        $attribute = \App\Models\Attribute::find($selectedAttributeId);
        if (!$attribute) {
            return [
                \Filament\Forms\Components\Placeholder::make('attribute_not_found')
                    ->label('')
                    ->content('Selected attribute not found.')
            ];
        }

        // Get only selected attribute values instead of all values
        $attributeValues = self::getSelectedAttributeValues($get, $selectedAttributeId);
        if (empty($attributeValues)) {
            return [
                \Filament\Forms\Components\Placeholder::make('no_attribute_values')
                    ->label('')
                    ->content("No values selected for attribute: {$attribute->name}")
            ];
        }

        $fields = [];
        foreach ($attributeValues as $value) {
            $valueObj = is_array($value) ? (object) $value : $value;
            $fields[] = Section::make("Tier Price for {$attribute->name}: {$valueObj->name}")
                ->schema([
                    \Filament\Forms\Components\Grid::make(2)->schema([
                        self::createTierRepeater("attribute_tier_price_{$selectedAttributeId}_{$valueObj->id}_east", 'East Malaysia'),
                        self::createTierRepeater("attribute_tier_price_{$selectedAttributeId}_{$valueObj->id}_west", 'West Malaysia'),
                    ])
                ]);
        }

        return $fields;
    }

    private static function createTierRepeater(string $tiersPath, string $label): \Awcodes\TableRepeater\Components\TableRepeater
    {
        return \Awcodes\TableRepeater\Components\TableRepeater::make($tiersPath)
            ->label($label)
            ->headers([
                \Awcodes\TableRepeater\Header::make('min_quantity')->label("Min Qty"),
                \Awcodes\TableRepeater\Header::make('max_quantity')->label("Max Qty"),
                \Awcodes\TableRepeater\Header::make('price')->label("Price"),
            ])
            ->live()
            ->schema([
                \Filament\Forms\Components\TextInput::make('min_quantity')->numeric()->disabled()->dehydrated(),
                \Filament\Forms\Components\TextInput::make('max_quantity')->numeric()->live(onBlur: true),
                \Filament\Forms\Components\TextInput::make('price')->numeric()->prefix('RM')->required(),
            ])
            ->maxItems(3)
            ->reorderable(false)
            ->addActionAlignment(\Filament\Support\Enums\Alignment::Start)
            ->default([
                ['min_quantity' => 1, 'max_quantity' => null, 'price' => null]
            ]);
    }
} 