<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Components;

use Filament\Forms\Get;
use App\Models\Attribute;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;

class AssignmentTypeSelector
{
    public static function make(string $type): array
    {
        $fieldPrefix = strtolower($type);
        $labelSuffix = match($type) {
            'Image' => 'image(s)',
            'Price' => 'price',
            'Quantity' => 'quantity',
            default => strtolower($type)
        };

        return [
            Grid::make()
                ->schema([
                    Radio::make("{$fieldPrefix}_assignment_type")
                        ->label("{$type} Assignment")
                        ->options([
                            'single' => "Apply single {$labelSuffix} to all SKUs",
                            'sku' => "Apply unique {$labelSuffix} to each SKU",
                            'attribute' => "Apply unique {$labelSuffix} by attribute to each SKU",
                        ])
                        ->reactive()
                        ->live(),
                ]),

            // Attribute Selection Sub-section
            Section::make('Select Attribute')
                ->visible(fn(Get $get) => $get("{$fieldPrefix}_assignment_type") === 'attribute')
                ->schema([
                    Radio::make("{$fieldPrefix}_selected_attribute")
                        ->label('Choose attribute for assignment')
                        ->options(function (Get $get) {
                            $attributeIds = $get('attribute_ids') ?? [];
                            if (empty($attributeIds)) {
                                return ['no_attributes' => 'No attributes selected'];
                            }
                            
                            return Attribute::whereIn('id', $attributeIds)
                                ->pluck('name', 'id')
                                ->toArray();
                        })
                        ->reactive()
                        ->live()
                        ->required()
                        ->disabled(function (Get $get) {
                            $attributeIds = $get('attribute_ids') ?? [];
                            return empty($attributeIds);
                        }),
                ])
                ->collapsible()
                ->collapsed(false),
        ];
    }

    public static function getSelectedAttributeId(Get $get, string $type): ?int
    {
        $fieldPrefix = strtolower($type);
        $assignmentType = $get("{$fieldPrefix}_assignment_type");
        
        if ($assignmentType === 'attribute') {
            $attributeId = $get("{$fieldPrefix}_selected_attribute");
            return $attributeId ? (int) $attributeId : null;
        }
        
        return null;
    }

    public static function getAssignmentType(Get $get, string $type): ?string
    {
        $fieldPrefix = strtolower($type);
        return $get("{$fieldPrefix}_assignment_type");
    }
} 