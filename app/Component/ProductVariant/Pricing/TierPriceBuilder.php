<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Pricing;

use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Support\Enums\Alignment;
use Awcodes\TableRepeater\Header;
use Awcodes\TableRepeater\Components\TableRepeater;
use App\Component\ProductVariant\Services\AttributeCombinationService;

class TierPriceBuilder
{
    public static function buildForAssignmentType(Get $get, string $assignmentType): array
    {
        $attributeIds = $get('attribute_ids') ?? [];

        return match ($assignmentType) {
            'single' => self::buildSingleTierPricing(),
            'sku' => self::buildSkuTierPricing($get, $attributeIds),
            'attribute' => self::buildAttributeTierPricing($get, $attributeIds),
            default => [],
        };
    }

    private static function buildSingleTierPricing(): array
    {
        return [
            Section::make("Single Tier Price for All SKUs")
                ->schema([
                    Grid::make(2)->schema([
                        self::createTierRepeater('single_tier_prices_east', 'East Malaysia'),
                        self::createTierRepeater('single_tier_prices_west', 'West Malaysia'),
                    ])
                ])
        ];
    }

    private static function buildSkuTierPricing(Get $get, array $attributeIds): array
    {
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            $fields[] = Section::make("Tier Price for {$attributeData['name']}")
                ->schema([
                    Grid::make(2)->schema([
                        self::createTierRepeater("sku_tier_prices_{$index}_east", 'East Malaysia'),
                        self::createTierRepeater("sku_tier_prices_{$index}_west", 'West Malaysia'),
                    ])
                ]);
        }
        
        return $fields;
    }

    private static function buildAttributeTierPricing(Get $get, array $attributeIds): array
    {
        $attributeData = AttributeCombinationService::getSelectedAttributesData($get, $attributeIds);
        
        if (empty($attributeData)) {
            return [];
        }

        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        $fields = [];

        foreach ($combinations as $index => $combination) {
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $fields[] = Section::make("Tier Price for {$displayName}")
                ->schema([
                    Grid::make(2)->schema([
                        self::createTierRepeater("variant_tier_prices_{$index}_east", 'East Malaysia'),
                        self::createTierRepeater("variant_tier_prices_{$index}_west", 'West Malaysia'),
                    ])
                ]);
        }
        
        return $fields;
    }

    private static function createTierRepeater(string $tiersPath, string $label): TableRepeater
    {
        return TableRepeater::make($tiersPath)
            ->label($label)
            ->headers([
                Header::make('min_quantity')->label("Min Qty"),
                Header::make('max_quantity')->label("Max Qty"),
                Header::make('price')->label("Price"),
            ])
            ->live()
            ->afterStateUpdated(function (Get $get, Set $set) use ($tiersPath) {
                $tiers = $get($tiersPath);
                if (!is_array($tiers)) return;
                
                $tierKeys = array_keys($tiers);
                for ($i = 0; $i < count($tiers); $i++) {
                    $currentKey = $tierKeys[$i];
                    if ($i === 0) {
                        $tiers[$currentKey]['min_quantity'] = 1;
                    } else {
                        $previousKey = $tierKeys[$i - 1];
                        $previousMax = $tiers[$previousKey]['max_quantity'] ?? 0;
                        if (is_numeric($previousMax) && $previousMax > 0) {
                            $tiers[$currentKey]['min_quantity'] = (int)$previousMax + 1;
                        } else {
                            $tiers[$currentKey]['min_quantity'] = null;
                        }
                    }
                }
                $set($tiersPath, $tiers);
            })
            ->schema([
                TextInput::make('min_quantity')->numeric()->disabled()->dehydrated(),
                TextInput::make('max_quantity')->numeric()->live(onBlur: true),
                TextInput::make('price')->numeric()->prefix('RM')->required(),
            ])
            ->maxItems(3)
            ->reorderable(false)
            ->addActionAlignment(Alignment::Start)
            ->default([
                ['min_quantity' => 1, 'max_quantity' => null, 'price' => null]
            ])
            ->addAction(function (Action $action) use ($tiersPath) {
                return $action->label('+ Add Tier')
                    ->action(function (Get $get, Set $set) use ($tiersPath) {
                        $state = $get($tiersPath) ?? [];
                        if (count($state) >= 3) {
                            return;
                        }

                        if (!empty($state)) {
                            $lastTier = end($state);
                            if (empty($lastTier['max_quantity']) || empty($lastTier['price'])) {
                                Notification::make()
                                    ->title('Please complete the current tier before adding a new one.')
                                    ->danger()
                                    ->send();
                                return;
                            }
                            $nextMinQuantity = (int)$lastTier['max_quantity'] + 1;
                        } else {
                            $nextMinQuantity = 1;
                        }

                        $newState = $state;
                        $newState[] = ['min_quantity' => $nextMinQuantity, 'max_quantity' => null, 'price' => null];
                        $set($tiersPath, $newState);
                    });
            });
    }
} 