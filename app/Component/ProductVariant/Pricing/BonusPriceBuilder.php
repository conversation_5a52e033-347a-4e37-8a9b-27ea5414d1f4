<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Pricing;

use Filament\Forms\Get;
use App\Component\ProductVariant\Services\FormFieldBuilderService;
use App\Component\ProductVariant\Services\AttributeCombinationService;

class BonusPriceBuilder
{
    public static function buildForAssignmentType(Get $get, string $assignmentType): array
    {
        $attributeIds = $get('attribute_ids') ?? [];

        return match ($assignmentType) {
            'single' => self::buildSingleBonusPricing(),
            'sku' => self::buildSkuBonusPricing($get, $attributeIds),
            'attribute' => self::buildAttributeBonusPricing($get, $attributeIds),
            default => [],
        };
    }

    private static function buildSingleBonusPricing(): array
    {
        return FormFieldBuilderService::createBonusPriceInputs(
            'single_bonus_price',
            'Single Bonus Price for All SKUs'
        );
    }

    private static function buildSkuBonusPricing(Get $get, array $attributeIds): array
    {
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            $fields = array_merge($fields, FormFieldBuilderService::createBonusPriceInputs(
                "sku_bonus_prices_{$index}",
                "Bonus Price for {$attributeData['name']}"
            ));
        }
        
        return $fields;
    }

    private static function buildAttributeBonusPricing(Get $get, array $attributeIds): array
    {
        $attributeData = AttributeCombinationService::getSelectedAttributesData($get, $attributeIds);
        
        if (empty($attributeData)) {
            return [];
        }

        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        $fields = [];

        foreach ($combinations as $index => $combination) {
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $fields = array_merge($fields, FormFieldBuilderService::createBonusPriceInputs(
                "variant_bonus_prices_{$index}",
                "Bonus Price for {$displayName}"
            ));
        }
        
        return $fields;
    }
} 