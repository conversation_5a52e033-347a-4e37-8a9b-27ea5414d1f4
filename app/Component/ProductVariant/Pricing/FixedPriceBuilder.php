<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Pricing;

use Filament\Forms\Get;
use App\Component\ProductVariant\Services\FormFieldBuilderService;
use App\Component\ProductVariant\Services\AttributeCombinationService;

class FixedPriceBuilder
{
    public static function buildForAssignmentType(Get $get, string $assignmentType): array
    {
        $attributeIds = $get('attribute_ids') ?? [];

        return match ($assignmentType) {
            'single' => self::buildSinglePricing(),
            'sku' => self::buildSkuPricing($get, $attributeIds),
            'attribute' => self::buildAttributePricing($get, $attributeIds),
            default => [],
        };
    }

    private static function buildSinglePricing(): array
    {
        return FormFieldBuilderService::createRegionalPriceInputs(
            'single_price',
            'Single Price for All SKUs'
        );
    }

    private static function buildSkuPricing(Get $get, array $attributeIds): array
    {
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            $fields = array_merge($fields, FormFieldBuilderService::createRegionalPriceInputs(
                "sku_prices_{$index}",
                "Price for {$attributeData['name']}"
            ));
        }
        
        return $fields;
    }

    private static function buildAttributePricing(Get $get, array $attributeIds): array
    {
        $attributeData = AttributeCombinationService::getSelectedAttributesData($get, $attributeIds);
        
        if (empty($attributeData)) {
            return [];
        }

        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        $fields = [];

        foreach ($combinations as $index => $combination) {
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $fields = array_merge($fields, FormFieldBuilderService::createRegionalPriceInputs(
                "variant_prices_{$index}",
                "Price for {$displayName}"
            ));
        }
        
        return $fields;
    }
} 