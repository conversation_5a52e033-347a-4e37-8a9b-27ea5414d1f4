<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Stock;

use Filament\Forms\Get;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\HtmlString;
use Awcodes\TableRepeater\Header;
use Awcodes\TableRepeater\Components\TableRepeater;
use App\Component\ProductVariant\Services\FormFieldBuilderService;
use App\Component\ProductVariant\Services\AttributeCombinationService;

class BatchAssignmentBuilder
{
    public static function buildForAssignmentType(Get $get, string $assignmentType, array $attributeIds): array
    {
        return match ($assignmentType) {
            'single' => self::buildSingleBatchAssignment($get),
            'sku' => self::buildSkuBatchAssignment($get, $attributeIds),
            'attribute' => self::buildAttributeBatchAssignment($get, $attributeIds),
            default => [],
        };
    }

    private static function buildSingleBatchAssignment(Get $get): array
    {
        return [
            Section::make()
                ->heading(fn(Get $get) => new HtmlString(
                    '<div class="flex items-center justify-between w-full">
                        <span class="text-base font-bold text-gray-900">Manage Batch</span>
                        <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                            collect($get('single_batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                        ) . '</span>
                    </div>'
                ))
                ->schema([
                    TableRepeater::make('single_batches')
                        ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                            return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                ->extraAttributes([
                                    'style' => 'border: none !important; box-shadow: none !important;'
                                ]);
                        })
                        ->formatStateUsing(function () {
                            return [
                                [
                                    'batch_name' => '',
                                    'available_stock' => '',
                                    'expiry_date' => null,
                                ]
                            ];
                        })
                        ->defaultItems(1)
                        ->reorderable(false)
                        ->addActionAlignment(Alignment::End)
                        ->headers([
                            Header::make('Batch Number'),
                            Header::make('Stock By Packaging'),
                            Header::make('Expiry Date'),
                            Header::make('Action'),
                        ])
                        ->schema([
                            TextInput::make('batch_name')
                                ->placeholder('Batch Number')
                                ->label('Name')
                                ->rules(['required', 'distinct'])
                                ->label('Batch Name'),
                            TextInput::make('available_stock')
                                ->live(onBlur: true)
                                ->placeholder('Stock By Packaging')
                                ->numeric()
                                ->minValue(0)
                                ->rules(['required', 'numeric', 'gt:0'])
                                ->label('Available Stock'),
                            \Filament\Forms\Components\DatePicker::make('expiry_date')
                                ->rules(['required', 'date', 'after_or_equal:today'])
                                ->minDate(today())
                                ->placeholder('Select the expiry date')
                                ->label('Expiry Date'),
                        ])
                ])
        ];
    }

    private static function buildSkuBatchAssignment(Get $get, array $attributeIds): array
    {
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            $fields[] = FormFieldBuilderService::createBatchTable(
                "sku_batches_{$index}",
                "Batch Details for {$attributeData['name']}"
            );
        }
        
        return $fields;
    }

    private static function buildAttributeBatchAssignment(Get $get, array $attributeIds): array
    {
        $attributeData = AttributeCombinationService::getSelectedAttributesData($get, $attributeIds);
        
        if (empty($attributeData)) {
            return [];
        }

        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        $fields = [];

        foreach ($combinations as $index => $combination) {
            $displayName = AttributeCombinationService::generateDisplayName($combination);
            
            $fields[] = FormFieldBuilderService::createBatchTable(
                "variant_batches_{$index}",
                "Batch Details for {$displayName}"
            );
        }
        
        return $fields;
    }
} 