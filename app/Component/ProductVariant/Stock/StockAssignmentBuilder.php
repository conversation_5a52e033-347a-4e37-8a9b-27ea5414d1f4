<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\Stock;

use Filament\Forms\Get;
use App\Component\ProductVariant\Services\FormFieldBuilderService;
use App\Component\ProductVariant\Services\AttributeCombinationService;

class StockAssignmentBuilder
{
    public static function buildForAssignmentType(Get $get, string $assignmentType, array $attributeIds): array
    {
        return match ($assignmentType) {
            'single' => self::buildSingleStockAssignment(),
            'sku' => self::buildSkuStockAssignment($get, $attributeIds),
            'attribute' => self::buildAttributeStockAssignment($get, $attributeIds),
            default => [],
        };
    }

    private static function buildSingleStockAssignment(): array
    {
        return [
            FormFieldBuilderService::createStockInput(
                'single_stock',
                'Stock for All SKUs'
            )
        ];
    }

    private static function buildSkuStockAssignment(Get $get, array $attributeIds): array
    {
        $selectedAttributes = AttributeCombinationService::getSelectedAttributes($get, $attributeIds);
        
        if (empty($selectedAttributes)) {
            return [];
        }

        $fields = [];
        foreach ($selectedAttributes as $index => $attributeData) {
            $fields[] = FormFieldBuilderService::createStockInput(
                "sku_stock_{$index}",
                "Stock for {$attributeData['name']}"
            );
        }
        
        return $fields;
    }

    private static function buildAttributeStockAssignment(Get $get, array $attributeIds): array
    {
        $attributeData = AttributeCombinationService::getSelectedAttributesData($get, $attributeIds);
        
        if (empty($attributeData)) {
            return [];
        }

        $combinations = AttributeCombinationService::generateCombinations($attributeData);
        $fields = [];

        foreach ($combinations as $index => $combination) {
            $displayName = AttributeCombinationService::generateDisplayName($combination);

            $fields[] = FormFieldBuilderService::createStockInput(
                "variant_stock_{$index}",
                "Stock for {$displayName}"
            );
        }
        
        return $fields;
    }
} 