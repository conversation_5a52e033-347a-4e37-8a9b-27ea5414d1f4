<?php

declare(strict_types=1);

namespace App\Component\ProductVariant\DTOs;

class VariantSummaryItem
{
    public function __construct(
        public readonly int $index,
        public readonly string $sku,
        public readonly string $displayName,
        public readonly array $attributes,
        public readonly ?array $pricing = null,
        public readonly ?array $quantity = null,
        public readonly ?array $images = null,
        public readonly ?string $priceDisplay = null,
        public readonly ?string $quantityDisplay = null,
        public readonly ?int $imagesCount = null,
    ) {}

    public function toArray(): array
    {
        return [
            'index' => $this->index,
            'sku' => $this->sku,
            'display_name' => $this->displayName,
            'attributes' => $this->attributes,
            'pricing' => $this->pricing,
            'quantity' => $this->quantity,
            'images' => $this->images,
            'price_display' => $this->priceDisplay,
            'quantity_display' => $this->quantityDisplay,
            'images_count' => $this->imagesCount,
        ];
    }

    public static function fromArray(array $data): self
    {
        return new self(
            index: $data['index'],
            sku: $data['sku'],
            displayName: $data['display_name'],
            attributes: $data['attributes'],
            pricing: $data['pricing'] ?? null,
            quantity: $data['quantity'] ?? null,
            images: $data['images'] ?? null,
            priceDisplay: $data['price_display'] ?? null,
            quantityDisplay: $data['quantity_display'] ?? null,
            imagesCount: $data['images_count'] ?? null,
        );
    }

    public function hasPricing(): bool
    {
        return !empty($this->pricing) || !empty($this->priceDisplay);
    }

    public function hasQuantity(): bool
    {
        return !empty($this->quantity) || !empty($this->quantityDisplay);
    }

    public function hasImages(): bool
    {
        return !empty($this->images) || $this->imagesCount > 0;
    }
} 