<?php

namespace App\Component\Products;

use App\Forms\Components\DragDrop;
use Filament\Forms\Components\Section;

class ProductMultipleImageSection
{
    public static $images = [];

    public static function make()
    {
        return Section::make()
        ->extraAttributes(['style' => 'z-index: 1 !important;position:relative;'])
        ->schema([
            DragDrop::make('images')
                ->multiple()
                ->required()
                ->rules([
                    '*.max' => 'max:2048',
                    '*.mimes' => 'mimes:jpg,png,jpeg',
                    '*.dimensions' => 'dimensions:width=1140,height=1140',
                    'required',
                ])
                ->minFiles(1)
                ->validationAttribute('images')
                // ->label(new HtmlString('Images<span class="text-red-500" style="color:red;">*</span>'))
                ->collection('product-images')
                ->dehydrated(true)
                ->hint('Please upload at least 1 image and image size should be 1140px x 1140px')
                ->afterStateUpdated(function ($state) {
                    self::$images = array_filter((array) $state);
                })
            ]);
    }
}