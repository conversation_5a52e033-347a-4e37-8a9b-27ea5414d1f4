<?php
namespace App\Component\Products;

use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\RichEditor;

class ProductDescriptionSection
{
    public static function make()
    {
        return Section::make()
        ->heading('Product Description')
        ->schema([
            Tabs::make('Product Details')
                ->schema([
                    Tab::make('Product Description')
                        ->schema([
                            RichEditor::make('product_description') // need to add migration
                        ]),
                    Tab::make('Key Ingredients')
                        ->schema([
                            RichEditor::make('description_ingredients') // need to add migration
                        ]),
                    Tab::make('Storage Instructions')
                        ->schema([
                            RichEditor::make('description_storage_instructions')  // need to add migration
                        ]),
                    Tab::make('Usage/Indication')
                        ->schema([
                            RichEditor::make('description_indications')
                        ]),
                    Tab::make('Contradiction')
                        ->schema([
                            RichEditor::make('description_contradictions')
                        ]),
                    Tab::make('How to Use')
                        ->schema([
                            RichEditor::make('description_how_to_use') // need to add migration
                        ]),
                    Tab::make('Safety Information/Pregnancy')
                        ->schema([
                            RichEditor::make('description_safety_information') // need to add migration
                        ]),
                    Tab::make('Dosage Information')
                        ->schema([
                            RichEditor::make('description_dosage')
                        ]),
                    Tab::make('Side Effects')
                        ->schema([
                            RichEditor::make('description_side_effects')
                        ]),
                ])
                    ]);
    }
} 