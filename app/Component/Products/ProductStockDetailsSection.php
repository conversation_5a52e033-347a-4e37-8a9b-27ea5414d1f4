<?php

namespace App\Component\Products;

use App\Models\Unit;
use Filament\Forms\Get;
use App\Models\Container;
use App\Models\DosageForm;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Awcodes\TableRepeater\Components\TableRepeater;

class ProductStockDetailsSection
{
    public static $qty = 0;
    public static function make()
    {
        return Section::make('Stock Details')
        ->schema([
            Group::make()
                ->schema([
                    Radio::make('productData.stock_type')
                        ->label('Manage Product')
                        ->options([
                            'normal' => 'Without Batch Number',
                            'batch' => 'By Batch'
                        ])
                        ->live()
                        ->inline()
                        ->formatStateUsing(fn() => 'batch')
                        ->rules(['required'])
                        ->columnSpan(2),
                    Radio::make('stock_type')
                        // ->label(new HtmlString('Stock Type<span class="text-red-500" style="color:red;">*</span>'))
                        ->options([
                            'unit' => 'Unit',
                            'wps' => new HtmlString('Wholesale Pack<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Indicates the quantity of items included in one wholesale unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'),
                        ])
                        ->inline()
                        ->live()
                        ->rules(['required'])
                        ->validationAttribute('stock type')
                        ->formatStateUsing(fn() => 'unit')
                        ->columnSpan(2),
                ])
                ->columnSpanFull(),
            Select::make('in_stock')
                ->label(new HtmlString('Stock Status<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('stock status')
                ->live()
                ->rules(['required'])
                ->formatStateUsing(fn() => 'yes')
                ->options([
                    'yes' => 'In Stock',
                    'no' => 'Out of Stock',
                ]),
            Select::make('container_id')
                ->searchable()
                ->preload()
                ->live()
                ->label(fn()=> PackagingToolTip::tooltip())
                ->rules(['required'])
                ->validationAttribute('packaging')
                ->options(fn() =>  Container::all()->pluck('name', 'id')),
            Select::make('dosage_foams_id')
                ->searchable()
                ->preload()
                ->rules(['required'])
                ->live()
                ->validationAttribute('product form')
                ->label(new HtmlString('Product Form<span class="text-red-500" style="color:red;">*</span>'))
                ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
            TextInput::make('productData.stock')
                ->placeholder('Stock')
                ->label(function (Get $get) {
                    $containerName = Container::find($get('container_id'))?->name ?? "";
                    return new HtmlString("Stock by $containerName<span class='text-red-500' style='color:red;'>*</span>");
                })
                ->rules(['numeric', 'gt:0'])
                ->visible(function (Get $get) {
                    if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                        return true;
                    }
                    return false;
                })
                ->reactive()
                ->requiredIf(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal', true)
                ->rules(function (Get $get) {
                    if ($get('in_stock') == 'yes') {
                        return ['required'];
                    }
                    return ['sometimes'];
                })
                ->validationAttribute('stock'),
            Select::make('unit_id')
                ->searchable()
                ->preload()
                ->label(function (Get $get) {
                    if ($get('container_id')) {
                        $containerName = Container::find($get('container_id'))?->name;
                        return new HtmlString("Volume by {$containerName}<span class='text-red-500' style='color:red;'>*</span>");
                    }
                    return new HtmlString('Volume<span class="text-red-500" style="color:red;">*</span>');
                })
                ->rules(['required'])
                ->validationAttribute('volume')
                ->options(fn() => Unit::all()->pluck('name', 'id')),
            TextInput::make('quantity_per_unit')
                ->placeholder('Volume unit')
                ->validationAttribute('volume unit')
                ->numeric()
                ->live()
                ->rules(['required', 'gt:0'])
                ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>')),
            
            TextInput::make('productData.low_stock')
                ->placeholder('Low stock trigger value')
                ->numeric()
                ->visible(function (Get $get) {
                    if ($get('in_stock') == 'yes') {
                        return true;
                    }
                    return false;
                })
                ->rules(function (Get $get) {
                    if ($get('in_stock') == 'yes') {
                        return ['required', 'numeric', 'max:9999999', 'gt:0'];
                    }
                    return ['sometimes', 'numeric', 'max:9999999', 'gt:0'];
                })
                ->validationAttribute('low stock trigger value')
                ->label(function (Get $get) {
                    if($get('stock_type') == 'unit'){
                        return new HtmlString("Low Stock trigger <span class='text-red-500' style='color:red;'>*</span>");
                    } else{
                        return new HtmlString("Low Stock trigger value by<span class='text-red-500' style='color:red;'>*</span>");
                    }
                    // return new HtmlString("Low Stock trigger value by $containerName<span class='text-red-500' style='color:red;'>*</span>");
                }),
            DatePicker::make('productData.expires_on_after')
                ->placeholder('Select the expiry date')
                ->label(function (Get $get) {
                    if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                        return new HtmlString('Expiry Date<span class="text-red-500" style="color:red;">*</span>');
                    }
                    return new HtmlString('Expiry Date');
                })
                ->visible(function (Get $get) {
                    if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                        return true;
                    }
                    return false;
                })
                ->rules(function (Get $get) {
                    if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                        return ['required', 'date', 'after_or_equal:today'];
                    }
                    return ['sometimes', 'date', 'after_or_equal:today'];
                })
                ->validationAttribute('Expiry Date')
                ->minDate(today()),
            TextInput::make('weight')
                ->placeholder('Weight (gms)')
                ->numeric()
                ->label(new HtmlString('Weight (gms)<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('weight')
                ->rules(['required', 'gt:0', 'numeric']),
            TextInput::make('whole_sale_pack_size')
                ->live()
                ->label(new HtmlString('Whole Sale Pack Size<span class="text-sm text-red-500">*</span>'))
                ->validationAttribute('Whole Sale Pack Size')
                ->numeric()
                ->visible(function (Get $get) {
                    if ($get('stock_type') == 'wps') {
                        return true;
                    }
                    return false;
                })
                ->rules(function (Get $get) {
                    if ($get('stock_type') == 'wps') {
                        return ['required', 'numeric','integer', 'gt:1'];
                    }
                    return ['numeric', 'integer', 'gt:0'];
                })
                ->prefix(function (Get $get) {
                    if (!empty($get('container_id'))) {
                        $containerName = Container::find($get('container_id'))?->name;
                        $containerName = Str::plural($containerName);
                        return "$containerName of";
                    }
                    return 'of';
                })
                ->suffix(function (Get $get) {
                    self::$qty = $get('quantity_per_unit');
                    if (!empty($get('whole_sale_pack_size')) && $get('whole_sale_pack_size') > 0) {
                        self::$qty = self::$qty * (int) $get('whole_sale_pack_size');
                    }
                    $foam = DosageForm::find($get('dosage_foams_id'))?->name;
                    $foam = Str::plural($foam);
                    $qty1 = self::$qty;
                    return "$qty1 {$foam}";
                }),
                Section::make()
                        ->heading(fn(Get $get) => new HtmlString(
                            '<div class="flex items-center justify-between w-full">
        <span class="text-base font-bold text-gray-900">Manage Batch</span>
        <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                                collect($get('batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                            ) . '</span>
    </div>'
                        ))
                        ->visible(fn(Get $get) => $get('productData.stock_type') == 'batch' && $get('in_stock') == 'yes')
                        ->schema([
                            TableRepeater::make('batches')
                                ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                    return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ]);
                                })
                                ->defaultItems(3)
                                ->default([
                                    [
                                        'batch_name' => '',
                                        'available_stock' => '',
                                        'expiry_date' => null,
                                    ],

                                ])
                                ->reorderable(false)
                                ->addActionAlignment(Alignment::End)
                                ->headers([
                                    Header::make('Batch Name'),
                                    Header::make('Stock by Packaging'),
                                    Header::make('Expiry Date'),
                                    Header::make('Action'),
                                ])
                                ->schema([
                                    TextInput::make('batch_name')
                                        ->placeholder('Batch Name')
                                        ->label('Name')
                                        ->rules(function (Get $get) {
                                            $inStock = $get('../../in_stock');
                                            if ($get('productData.stock_type') == 'batch' && $get('../../in_stock') == 'yes') {
                                                return [
                                                    'required',
                                                    Rule::unique('products_batch', 'batch_name')->where(fn($query) => $query->where('user_id', auth()->id())),
                                                ];
                                            }
                                            return ['sometimes', 'distinct', Rule::unique('products_batch', 'batch_name')->where(fn($query) => $query->where('user_id', auth()->id()))];
                                        })

                                        ->label('Batch Name'),
                                    TextInput::make('available_stock')
                                        ->placeholder('Stock by Packaging')
                                        ->live(onBlur: true)
                                        ->rules(function (Get $get) {
                                            if ($get('productData.stock_type') == 'batch' && $get('../../in_stock') == 'yes') {
                                                return ['required', 'numeric', 'gt:0'];
                                            }
                                            return ['sometimes', 'numeric', 'gt:0'];
                                        })
                                        ->label('Stock by Packaging'),
                                    DatePicker::make('expiry_date')
                                        ->placeholder('Select the expiry date')
                                        ->displayFormat('M d,Y')
                                        ->native(false)
                                        ->minDate(now())
                                        ->rules(function (Get $get) {
                                            if ($get('productData.stock_type') == 'batch' && $get('../../in_stock') == 'yes') {
                                                return ['required', 'date', 'after_or_equal:today'];
                                            }
                                            return ['sometimes', 'date', 'after_or_equal:today'];
                                        })
                                        ->placeholder('Select the expiry date')
                                        // ->default(now()->addDays(10))
                                        ->label('Expiry Date'),
                                ])
                                ->visible(fn(Get $get) => $get('productData.stock_type') == 'batch')
                                ->columnSpan('full'),
                        ]),

        ])->columns(3);
    }
}