<?php

namespace App\Component\Products;

use App\Models\Unit;
use Filament\Forms\Get;
use App\Models\Container;
use App\Models\DosageForm;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;

class NewProductPackagingDetailsSection
{
    public static function make()
    {
        return Section::make()
            ->schema([
                Group::make()
                    ->schema([
                        Select::make('container_id')
                            ->label(fn() => PackagingToolTip::tooltip())
                            ->rules(['required'])
                            ->live()
                            ->validationAttribute('packaging')
                            ->options(fn() =>  Container::all()->pluck('name', 'id')),
                        Select::make('dosage_foams_id')
                            ->label(new HtmlString('Product Form<span class="text-red-500" style="color:red;">*</span>'))
                            ->validationAttribute('product form')
                            ->rules(['required'])
                            ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
                        TextInput::make('quantity_per_unit')
                            ->numeric()
                            ->placeholder('Volume')
                            ->validationAttribute('volume')
                            ->rules(['required', 'numeric', 'max:999999999', 'gt:0'])
                            ->label(function (Get $get) {
                                if (!empty($get('container_id'))) {
                                    $containerName = Container::find($get('container_id'))?->name;
                                    return new HtmlString("Volume by $containerName<span class='text-red-500' style='color:red;'>*</span>");
                                }
                                return new HtmlString('Volume<span class="text-red-500" style="color:red;">*</span>');
                            }),
                        Select::make('unit_id')
                            ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>'))
                            ->rules(['required'])
                            ->validationAttribute(label: 'volume unit')
                            ->options(fn() => Unit::all()->pluck('name', 'id')),
                        
                        TextInput::make('weight')
                            ->placeholder('Weight (gms)')
                            ->label(new HtmlString('Weight (gms)<span classsky="text-red-500" style="color:red;">*</span>'))
                            ->validationAttribute('weight')
                            ->rules(['required', 'numeric', 'gt:0'])

                    ])->columns(5)
            ]);
    }
}
