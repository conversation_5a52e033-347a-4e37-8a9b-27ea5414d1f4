<?php

namespace App\Component\Products;

use App\Models\Brand;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use App\Models\Distributor;
use App\Models\GenericName;
use App\Rules\CaseSensitiveUnique;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class ProductGeneralDetailsSection
{
    public static $searchTerm = '';

    public static function make()
    {
        return Section::make()->schema([
            TextInput::make('name')
                ->placeholder('Product Name')
                ->label('Product Name')
                ->live(onBlur: true)
                ->afterStateUpdated(function (Get $get, Set $set) {
                    if (empty($get('name')) || $get('name') == null || $get('name') == '') {
                        $set('sku', null);
                        self::$searchTerm = $get('name');
                        return;
                    }
                    self::$searchTerm = $get('name');
                    $set('sku', mb_substr($get('name'), 0, 4) . "_"  . rand(100000, 999999));
                })
                ->validationAttribute('product name')
                ->label(new HtmlString('Product Name<span class="text-red-500" style="color:red;">*</span>'))
                ->rules([
                    'required',
                    'max:255',
                    new CaseSensitiveUnique(Product::class, 'name'),
                ])
                ->validationMessages([
                    'required' => 'product name is required',
                    'max' => 'product name must not exceed 255 characters',
                    'App\\Rules\\CaseSensitiveUnique' => 'prodcut name already exists',
                ]),
            Select::make('brand_id')
                ->searchable()
                ->preload()
                ->relationship('brand', 'name')
                ->label(new HtmlString('Brand<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('brand')
                ->createOptionForm([
                    TextInput::make('name')
                        ->label(new HtmlString('Name<span class="text-red-500" style="color:red;">*</span>'))
                        ->rules([
                            'required',
                            'max:100',
                            // 'regex:/^[\w\s\p{P}]+$/u',
                            fn(Get $get) => new CaseSensitiveUnique(Brand::class, 'name', $get('id'))
                        ])
                        ->maxLength(100)
                        // ->unique(table: 'brands', column:'name', ignoreRecord: true)
                        ->validationMessages([
                            'required' => __('message.brand.required'),
                            // 'regex' => __('message.brand.regex'),
                            'max' => __('message.brand.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.brand.case_sensitive_unique'),
                        ]),
                    FileUpload::make('logo')->image()->directory('brand')->imageEditor()
                        ->markAsRequired(false)
                        ->label(new HtmlString('Logo<span class="text-red-500" style="color:red;">*</span>'))
                        ->maxSize(2048)
                        ->hint('Supported formats: JPEG, JPG, PNG (Max 2MB), dimension: 580px x 580px')
                        ->visibility('public')
                        ->disk('s3')
                        ->rules([
                            'required',
                            'image',
                            'mimes:jpeg,png,jpg',
                            'dimensions:width=580,height=580',
                        ])
                        ->validationMessages([
                            'required' => 'logo is required',
                            'image' => 'The file must be an image.',
                            'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                            'dimensions' => 'The image must be 580px x 580px.',
                        ])
                        ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])->required(),
                ])
                ->options(fn() => Brand::where('status', true)->pluck('name', 'id'))
                ->rules(['required']),
            Select::make('category_id')
                ->searchable()
                ->preload()
                ->label(new HtmlString('Category<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('category')
                ->options(fn() => Category::whereNull('parent_id')->where('status', true)->pluck('name', 'id'))
                ->reactive()
                ->rules(['required']),
            Select::make('sub_category_id')
                ->searchable()
                ->preload()
                ->label('Sub category')
                ->live()
                ->options(
                    fn(Get $get) => $get('category_id')
                        ? Category::where([
                            'parent_id' => $get('category_id'),
                            'status'    => true,
                        ])->pluck('name', 'id')
                        : []
                ),

            SpatieMediaLibraryFileUpload::make('mda_document')
                ->collection('mda-documents')
                ->disk('s3')
                ->visibility('public')
                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                ->maxSize(2048)
                ->columnSpanFull()
                ->visible(function (Get $get) {
                    return (bool) Category::find($get('sub_category_id'))?->is_mda;
                })
                ->reactive(),
            Select::make('generic_name_id')
                ->searchable()
                ->preload()
                ->relationship('generic', 'name')
                ->createOptionForm([
                    Group::make()->schema([
                        TextInput::make('name')
                            ->label(new HtmlString('Name<span class="text-red-500" style="color:red;">*</span>'))
                            ->rules([
                                'required',
                                // 'regex:/^[\w\s\p{P}]+$/u', 
                                'max:100',
                                fn(Get $get) => new CaseSensitiveUnique(GenericName::class, 'name', $get('id'))
                            ])
                            ->validationMessages([
                                'required' => __('message.generic_name.required'),
                                // 'regex' => __('message.generic_name.regex'),
                                'max' => __('message.generic_name.max'),
                                'App\\Rules\\CaseSensitiveUnique' => __('message.generic_name.case_sensitive_unique'),
                            ]),
                    ])
                    // ->extraAttributes(['style' => 'z-index:150; position:relative;']),
                ])
                ->label(new HtmlString('Generic Name<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('generic name')
                ->rules(['required'])
                ->options(fn() => GenericName::where('status', true)->pluck('name', 'id')),
            TextInput::make('sku')
                ->placeholder('SKU')
                ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('SKU')
                ->rules(['required', 'max:255']),
            Select::make('distributor_id')
                ->searchable(['name'])
                ->preload()
                ->relationship('distributors', 'name')
                ->createOptionForm([
                    Section::make()->schema([
                        TextInput::make('name')
                            ->label(new HtmlString("Distributor Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->maxLength(100)
                            ->placeholder('Enter Distributor Name')
                            ->rules([
                                'required',
                                // 'regex:/^[\w\s\p{P}]+$/u',
                                'max:100',
                                fn(Get $get) => new CaseSensitiveUnique(Distributor::class, 'name', $get('id'))
                            ])
                            ->validationMessages([
                                'required' => __('message.distributor.required'),
                                // 'regex' => __('message.distributor.regex'),
                                'max' => __('message.distributor.max'),
                                'App\\Rules\\CaseSensitiveUnique' => __('message.distributor.case_sensitive_unique'),
                            ]),

                    ]),
                ])
                ->multiple()
                ->label(new HtmlString('Distributor<span class="text-red-500" style="color:red;">*</span>'))
                ->validationAttribute('distributor')
                ->rules(['required']),
            Radio::make('is_prescription_required')
                ->label('Prescription Required')
                ->options([
                    1 => 'Yes',
                    0 => 'No',
                ])
                ->inline()
                ->formatStateUsing(fn() => 0)

        ])->columns(2);
    }
}
