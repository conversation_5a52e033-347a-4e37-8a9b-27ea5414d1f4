<?php

namespace App\Component;

use Illuminate\Support\HtmlString;

class PackagingToolTip
{
    public static function tooltip()
    {
        return new HtmlString('
                Packaging
                

                <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Defines the packaging format in which the item is sold or stored.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>
            
    ');
    }
}