<?php

namespace App\Traits;

use Filament\Actions\Action;

trait HasBackButton
{
    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->tooltip('Back to previous page')
                ->color('gray')
                ->url(fn() => url()->previous())
                ->visible(fn() => url()->previous() !== url()->current()),
        ];
    }

    public function getBackAction(): Action
    {
        return Action::make('back')
            ->label('Back')
            ->tooltip('Back to previous page')
            ->color('gray')
            ->url(fn() => url()->previous())
            ->visible(fn() => url()->previous() !== url()->current());
    }
}
