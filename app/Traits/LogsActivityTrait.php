<?php

namespace App\Traits;

use App\Models\Role;
use App\Models\User;
use Spatie\Activitylog\Models\Activity;
use Spatie\Activitylog\LogOptions;
use Illuminate\Support\Str;

trait LogsActivityTrait
{
    use \Spatie\Activitylog\Traits\LogsActivity;

    public function activityLogs()
    {
        return $this->morphMany(Activity::class, 'subject');
    }

    public function getActivitylogOptions(): LogOptions
    {
        $subjectType = $this->getTable() ?? class_basename($this);
        $formattedSubjectType = Str::title(str_replace('_', ' ', Str::singular($subjectType)));

        if ($subjectType === 'categories' && !is_null($this->parent_id)) {
            $formattedSubjectType = 'Sub Category';
        } elseif ($subjectType === 'units') {
            $formattedSubjectType = 'Volume Unit';
        } elseif ($subjectType === 'global_settings') {
            $formattedSubjectType = 'Global Setting';
        } elseif ($subjectType === 'containers') {
            $formattedSubjectType = 'Package';
        }

        if ($subjectType === 'users') {
            $roleNames = $this->roles()->pluck('name')->toArray();
            $parentRoleNames = $this->parent?->roles()->pluck('name')->toArray() ?? [];

            $rolePrefix = 'User';
            $allRoles = array_merge($roleNames, $parentRoleNames);

            foreach ($allRoles as $roleName) {
                $rolePrefix = match ($roleName) {
                    'Super Admin' => 'Admin User',
                    'Pharmaceutical Company' => 'PS User',
                    'Clinic' => 'Facility User',
                    default => $rolePrefix,
                };
                if ($rolePrefix !== 'User') {
                    break;
                }
            }

            $formattedSubjectType = $rolePrefix;
        }

        $logOptions = LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->useLogName($subjectType)
            ->dontSubmitEmptyLogs()
            ->setDescriptionForEvent(function (string $eventName) use ($formattedSubjectType, $subjectType) {
                if ($subjectType === 'threads') {
                    $authId = auth()->id();
                    if ($this->sender && $this->sender->id !== $authId) {
                        $oppositeUserName = $this->sender->name;
                    } elseif ($this->receiver && $this->receiver->id !== $authId) {
                        $oppositeUserName = $this->receiver->name;
                    } else {
                        $oppositeUserName = 'Unknown User';
                    }

                    $description = ucfirst($formattedSubjectType) . " has been " . lcfirst($eventName) . " for " . $oppositeUserName;
                } elseif ($subjectType === 'global_settings') {
                    $displayName = $this->display_name ?? $this->name ?? 'setting';
                    $description = ucfirst($formattedSubjectType) . " '$displayName' has been " . lcfirst($eventName);
                } elseif ($subjectType === 'users') {
                    $displayName = $this->display_name ?? $this->name ?? 'password';
                    $description = ucfirst($formattedSubjectType) . " password has been " . lcfirst($eventName);
                } else {
                    $rawName = $this->name ?? $this->title ?? $this->label ?? $this->banner_type;
                    $name = str_replace('_', ' ', $rawName);
                    $description = ucfirst($formattedSubjectType) . " $name has been " . lcfirst($eventName);
                }
                return $description;
            });

        // Apply logExcept based on subject type
        if ($subjectType === 'users') {
            $logOptions = $logOptions->logExcept([
                'password',
                'updated_at',
                'remember_me',
                'remember_token',
                'is_admin_verified',
                'verification_status',
                'timezone', 
                'email_verified_at',
                'created_at',
                'deleted_at', 
                'admin_verified_by',
                'admin_verified_on'
            ]);
        } else {
            $logOptions = $logOptions->logExcept([
                'updated_at',
                'remember_me',
                'remember_token',
                'created_at',
                'deleted_at'
            ]);
        }

        return $logOptions;
    }
}