<?php

namespace App\Traits;

use App\Mail\PcResetPasswordMail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Solutionforest\FilamentEmail2fa\Models\TwoFaCode;

trait SendTwoFaCode
{
    public function sendTwoFaCode($user, $type)
    {
        try {
            $code = rand(1000, 9999);
            TwoFaCode::where(['user_id' => $user->id, 'type' => $type])->delete();
            TwoFaCode::insert([
                'user_id' => $user->id,
                'code' => $code,
                'expiry_at' => now()->addMinute(), // configure this with configuration
                'user_type' => 'App\Models\User',
                'type' => $type,
                'created_at' => now(),
            ]);
            Mail::to($user->email)->send(new PcResetPasswordMail($user, $code));
            return true;
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
    }
}
