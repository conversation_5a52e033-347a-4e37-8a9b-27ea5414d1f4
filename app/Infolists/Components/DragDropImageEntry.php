<?php

namespace App\Infolists\Components;

use Closure;
use App\Models\Product;
use Illuminate\View\View;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Filament\Infolists\Components\Component;
use Illuminate\Console\Concerns\InteractsWithIO;
use App\Filament\Admin\Resources\ProductResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class DragDropImageEntry extends Component
{
    // use InteractsWithMedia;

    protected string $view = 'infolists.components.drag-drop-image-entry';

    protected $model;
    protected $collection = 'product-images';

    public $images;


    public function model($id): static
    {
        $id = (request()->route('record'));
        $this->model = Media::where('model_id', $id)->where('collection_name', $this->collection)->get();

        $this->images = Media::where('model_id', $id)->where('collection_name', $this->collection)->get();
        return $this->images;
    }

    public function getImages(): array
    {
        return $this->images;
    }

    // public function render(): View
    // {
    //     $images = $this->model->getMedia($this->collection);
    //     return view($this->view)->with('images', $images);
    // }
    public function collection($collection): static
    {
        $this->collection = $collection;
        return $this;
    }


    public function getCollection(): string
    {
        return $this->collection;
    }
    public static function make(): static
    {
        return app(static::class);
    }
}
