<?php

namespace App\Policies\Admin;

use App\Models\User;
use App\Models\Inquiry;
use Illuminate\Auth\Access\HandlesAuthorization;

class InquiryPolicy
{
    use HandlesAuthorization;

   /**
     * Determine whether the user can view any models.0
     */
    public function viewAny(User $user): bool
    {
        return $user->can('inquiries_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, InquiryPolicy $inquiryPolicy): bool
    {
        return $user->can('inquiries_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('inquiries_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, InquiryPolicy $inquiryPolicy): bool
    {
        return $user->can('inquiries_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, InquiryPolicy $inquiryPolicy): bool
    {
        return $user->can('inquiries_delete');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('inquiries_delete');
    }
}
