<?php

namespace App\Policies\Admin;

use App\Models\User;
use App\Models\GenericName;
use Illuminate\Auth\Access\HandlesAuthorization;

class GenericNamePolicy
{
    use HandlesAuthorization;

   /**
     * Determine whether the user can view any models.0
     */
    public function viewAny(User $user): bool
    {
        return $user->can('generic-names_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, GenericName $genericName): bool
    {
        return $user->can('generic-names_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('generic-names_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, GenericName $genericName): bool
    {
        return $user->can('generic-names_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, GenericName $genericName): bool
    {
        return $user->can('generic-names_delete');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('generic-names_delete');
    }
}
