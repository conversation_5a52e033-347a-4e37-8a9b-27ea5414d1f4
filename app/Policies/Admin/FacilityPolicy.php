<?php

namespace App\Policies\Admin;

use App\Models\ClinicDetail;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class FacilityPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.0
     */
    public function viewAny(User $user): bool
    {
        return $user->can('facilities_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('facilities_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('facilities_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('facilities_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('facilities_delete');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('facilities_delete');
    }
}
