<?php

namespace App\Policies;

use App\Models\Product;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProductPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {

        return $user->hasRole('Super Admin') || $user->can('products_view') || $user->hasRole('Pharmaceutical Company')
            || $user->can('products_update') || $user->can('products_delete') || $user->can('products_create') || $user->can('products_bulk stock update')
            || $user->can('products_update price') || $user->can('products_update stock') || $user->can('products_approve');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Product $product): bool
    {

        return $user->hasRole('Super Admin') || $user->can('products_view') || $user->hasRole('Pharmaceutical Company');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('products_create') || $user->hasRole('Pharmaceutical Company');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Product $product): bool
    {
        return $user->hasRole('Super Admin') || $user->can('products_update') || $user->hasRole('Pharmaceutical Company');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Product $product): bool
    {
        return $user->hasRole('Super Admin') || $user->can('products_delete') || $user->hasRole('Pharmaceutical Company');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Product $product): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Product $product): bool
    {
        return false;
    }
}
