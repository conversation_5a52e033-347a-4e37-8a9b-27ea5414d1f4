<?php

namespace App\Policies\Pc;

use App\Models\User;
use App\Models\ClinicDetail;
use Illuminate\Auth\Access\HandlesAuthorization;

class ClinicDetailPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.0
     */
    public function viewAny(User $user): bool
    {
        return $user->can('clinic-details_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('clinic-details_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('clinic-details_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('clinic-details_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->can('clinic-details_delete');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('clinic-details_delete');
    }
}
