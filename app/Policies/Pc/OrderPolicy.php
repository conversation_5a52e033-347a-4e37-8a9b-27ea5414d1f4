<?php

namespace App\Policies\Pc;

use App\Models\User;
use App\Models\Order;
use Illuminate\Auth\Access\HandlesAuthorization;

class OrderPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.0
     */
    public function viewAny(User $user): bool
    {
        // dd('here');
        return $user->can('orders_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Order $order): bool
    {
        return $user->can('orders_view');
    }
}
