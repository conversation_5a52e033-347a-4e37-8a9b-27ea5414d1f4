<?php

namespace App\Policies;

use App\Models\DosageForm;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class DosageFormPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('dosage-forms_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, DosageForm $dosageForm): bool
    {
        return $user->hasRole('Super Admin') || $user->can('dosage-forms_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('dosage-forms_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, DosageForm $dosageForm): bool
    {
        return $user->hasRole('Super Admin') || $user->can('dosage-forms_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, DosageForm $dosageForm): bool
    {
        return $user->hasRole('Super Admin') || $user->can('dosage-forms_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, DosageForm $dosageForm): bool
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, DosageForm $dosageForm): bool
    {
        return true;
    }
}
