<?php

namespace App\Policies;

use App\Models\GenericName;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class GenericNamePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('generic-names_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, GenericName $genericName): bool
    {
        return $user->hasRole('Super Admin') || $user->can('generic-names_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('generic-names_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, GenericName $genericName): bool
    {
        return $user->hasRole('Super Admin') || $user->can('generic-names_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, GenericName $genericName): bool
    {
        return $user->hasRole('Super Admin') || $user->can('generic-names_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, GenericName $genericName): bool
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, GenericName $genericName): bool
    {
        return true;
    }
}
