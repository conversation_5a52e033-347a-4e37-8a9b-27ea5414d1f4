<?php

namespace App\Policies;

use App\Models\ClinicDetail;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ClinicDetailPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facility_view any');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facility_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facility_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facility_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ClinicDetail $clinicDetail): bool
    {
        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facility_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, ClinicDetail $clinicDetail): bool
    {
        return true;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, ClinicDetail $clinicDetail): bool
    {
        return true;
    }
}
