<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Process\Exception\ProcessFailedException;
use Symfony\Component\Process\Process;
use App\Models\Import;
use App\Models\ProductImports;

class ImportService
{
    const NUMBER_OF_JOB = 20;
    const RETRY_FREQUENCY = 2;
    const STATUS_PENDING = "Pending";
    const STATUS_INPROGRESS = "Inprogress";
    const STATUS_FAILED = "Failed";
    const STATUS_KILLED = "Killed";
    const STATUS_COMPLETED = "Completed";
    const STATUS_COMPLETED_WITH_ERROR = "CompletedWithError";
    const STATUS_SUCCESS = "Success";


    // Add properties to track total counts
    private static $totalSuccessRows = 0;
    private static $totalErrorRows = 0;
    private static $totalRows = 0;

    /**
     * Execute a job from the job queue if the limit for in-progress jobs is not reached.
     */
    public static function executeJob()
    {
        self::handleStuckJobs();
        $totalInProgressJobs = self::getTotalJob(self::STATUS_INPROGRESS);
        if ($totalInProgressJobs >= self::NUMBER_OF_JOB) {
            Log::info("$totalInProgressJobs jobs are running, wait until a job completes.");
            return false;
        }
        $job = self::getNextJob();
        if ($job) {
            Log::info("Starting job: {$job->name}");
            self::runTheJob($job);
            return true;
        }
        Log::info("No jobs in the queue.");

        return true;
    }

    /**
     * Retrieve the next available job.
     */
    private static function getNextJob()
    {
        return ProductImports::where(function ($query) {
            $query->where("status", self::STATUS_PENDING)->orWhere("status", self::STATUS_FAILED);
        })->where("retry_frequency", "<", self::RETRY_FREQUENCY)->orderBy("id", "asc")->first();
    }

    /**
     * Get the total number of jobs in a specific status.
     */
    private static function getTotalJob($status)
    {
        return ProductImports::where("status", $status)->count();
    }

    /**
     * Execute the job's process.
     */
    private static function runTheJob($job)
    {
        try {
            // Reset counters for this job
            self::$totalSuccessRows = 0;
            self::$totalErrorRows = 0;
            self::$totalRows = 0;

            $commandArgs = explode("'", $job->command);
            $processArgs = array_filter(explode(" ", $commandArgs[0]));
            if (isset($commandArgs[1])) {
                $processArgs[] = $commandArgs[1];
            }
            $process = new Process($processArgs);
            $process->setTimeout(null);
            $process->start();
            self::updateJobStatus($job->id, self::STATUS_INPROGRESS, $process->getPid());

            // Collect output as it comes
            $fullOutput = '';
            $process->wait(function ($type, $buffer) use (&$fullOutput) {
                $fullOutput .= $buffer;
                Log::info("Buffer Output: " . $buffer);

                // Process intermediate chunk results if present
                self::processChunkResults($buffer);
            });

            if (!$process->isSuccessful()) {
                if ($process->getExitCode() == 143) {
                    ProductImports::where("id", $job->id)->increment("retry_frequency");
                }
                self::updateJobStatus($job->id, self::STATUS_FAILED, null, $fullOutput);
                throw new ProcessFailedException($process);
            } else {
                // Create a summary message with the final counts
                $summary = "Success Rows: " . self::$totalSuccessRows . ", errorRows: " . self::$totalErrorRows . ", Total: " . self::$totalRows;

                $finalStatus = self::$totalErrorRows > 0
                    ? self::STATUS_FAILED
                    : self::STATUS_SUCCESS;

                self::updateJobStatus($job->id, $finalStatus, null, $summary);
                Log::info("Job completed: " . $summary);
            }
        } catch (ProcessFailedException $e) {
            self::updateJobStatus($job->id, self::STATUS_FAILED, null, $e->getMessage());
            Log::error($e->getMessage());
        }
    }

    /**
     * Process intermediate chunk results from buffer output
     */
    private static function processChunkResults($buffer)
    {
        // Parse the output string using regex to extract Success Rows, errorRows, and Total
        //preg_match_all("/Success Rows:\s*(\d+),\s*errorRows:\s*(\d+),\s*Total:\s*(\d+)/", $buffer, $matches, PREG_SET_ORDER);
        preg_match_all("/Success Rows:\s*(\d+),\s*errorRows:\s*(\d+),?\s*Total:\s*(\d+)/i", $buffer, $matches, PREG_SET_ORDER);


        foreach ($matches as $match) {
            if (count($match) >= 4) {
                $successRows = (int)$match[1];
                $errorRows = (int)$match[2];
                $total = (int)$match[3];

                // Add to running totals
                self::$totalSuccessRows += $successRows;
                self::$totalErrorRows += $errorRows;
                self::$totalRows += $total;

                Log::debug("Chunk processed - Success: $successRows, Errors: $errorRows, Total: $total | Running totals - Success: " . self::$totalSuccessRows . ", Errors: " . self::$totalErrorRows . ", Total: " . self::$totalRows);
            }
        }
    }

    /**
     * Update the job status and related data.
     */
    private static function updateJobStatus($jobId, $status, $processId = null, $description = null)
    {
        $data = ["status" => $status, "start_date_time" => Carbon::now(),];
        if (in_array($status, [self::STATUS_INPROGRESS])) {
            $data["retry_frequency"] = DB::raw("retry_frequency + 1");
        }
        if (!in_array($status, [self::STATUS_INPROGRESS, self::STATUS_PENDING])) {
            $data["end_date_time"] = Carbon::now();
        }
        if ($processId !== null) {
            $data["process_id"] = $processId;
        }
        if ($description) {
            $data["description"] = $description;
        }
        ProductImports::where("id", $jobId)->update($data);
    }

    /**
     * Handle stuck jobs.
     */
    private static function handleStuckJobs()
    {
        $stuckJobs = ProductImports::where("status", self::STATUS_INPROGRESS)->where("start_date_time", "<", Carbon::now()->subHours(10))->get();
        foreach ($stuckJobs as $stuckJob) {
            self::updateJobStatus($stuckJob->id, self::STATUS_KILLED, null, "Job killed after being stuck.");
        }
    }

    /**
     * Get current status of import
     */
    public static function statusOfImport()
    {
        return [
            "totalRecords" => self::$totalRows,
            "successRecords" => self::$totalSuccessRows,
            "failRecords" => self::$totalErrorRows,
        ];
    }
}
