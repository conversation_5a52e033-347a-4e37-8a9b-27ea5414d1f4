<?php

namespace App\Services;

use App\Models\Brand;
use App\Models\Category;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\GenericName;
use App\Models\Product;
use App\Models\Unit;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Symfony\Component\Process\Process;
use ZipArchive;

class ProductService
{
    const CHUNK_SIZE = 10;
    const AUDIT_LOG_BATCH_SIZE = 10;

    public static function importData(array $data)
    {
        if (empty($data['file_path']) || empty($data['folder_id'])) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Missing file_path or folder_id'
            ]];
        }

        $s3Disk = Storage::disk('s3');
        $s3Folder = $data['file_path']; // e.g., imports/127

        $localTempDir = storage_path("app/temp_imports/{$data['folder_id']}");
        if (!file_exists($localTempDir)) {
            mkdir($localTempDir, 0755, true);
        }

        // Find files on S3
        $files = $s3Disk->files($s3Folder);

        $xlsxS3Path = collect($files)->first(fn($f) => str_ends_with($f, '.xlsx') || str_ends_with($f, '.xls'));
        $zipS3Path = collect($files)->first(fn($f) => str_ends_with($f, '.zip'));

        if (!$xlsxS3Path || !$zipS3Path) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Excel or ZIP file not found on S3'
            ]];
        }

        // Download from S3
        $localExcelPath = $localTempDir . '/uploaded_file.xlsx';
        $localZipPath = $localTempDir . '/uploaded_file.zip';

        file_put_contents($localExcelPath, $s3Disk->get($xlsxS3Path));
        file_put_contents($localZipPath, $s3Disk->get($zipS3Path));

        $extractPath = "{$localTempDir}/extracted_images";
        if (!file_exists($extractPath) && !mkdir($extractPath, 0777, true)) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Failed to create extracted_images folder'
            ]];
        }

        if (!self::extractZip($localZipPath, $extractPath)) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Failed to extract ZIP file'
            ]];
        }

        try {
            $spreadsheet = IOFactory::load($localExcelPath);
            $rows = $spreadsheet->getActiveSheet()->toArray();
            $header = array_map('strtolower', $rows[0]);
        } catch (\Exception $e) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Failed to load spreadsheet: ' . $e->getMessage()
            ]];
        }

        $imageIndex = array_search('product images', $header);
        if ($imageIndex === false) {
            return [[
                'success' => false,
                'chunk' => 0,
                'output' => 'Product images column not found in header'
            ]];
        }

        $chunks = array_chunk(array_slice($rows, 1), self::CHUNK_SIZE);
        $outputs = [];
        $allRecords = [];
        $processes = [];

        foreach ($chunks as $index => $chunk) {
            $chunkData = json_encode($chunk);
            $process = new Process([
                'php',
                base_path('artisan'),
                'process-product-chunk',
                base64_encode($chunkData),
                $data['jobId'],
                $extractPath
            ]);
            $process->start();
            $processes[$index] = $process;
        }

        foreach ($processes as $index => $process) {
            $process->wait();
            if (!$process->isSuccessful()) {
                $errorOutput = $process->getErrorOutput();
                $outputs[] = [
                    'chunk'   => $index,
                    'success' => false,
                    'output'  => $errorOutput
                ];
            } else {
                $successOutput = $process->getOutput();
                $decodedOutput = json_decode($successOutput, true);

                if (json_last_error() !== JSON_ERROR_NONE || !is_array($decodedOutput)) {
                    $outputs[] = [
                        'chunk'   => $index,
                        'success' => false,
                        'output'  => 'Invalid JSON output: ' . $successOutput
                    ];
                    continue;
                }

                $successRecords = $decodedOutput['successRecords'] ?? [];
                $failedRecords = $decodedOutput['failedRecords'] ?? [];

                foreach ($chunks[$index] as $i => $row) {
                    $found = false;
                    $productData = [];

                    foreach (self::getImportHeader() as $idx => $colName) {
                        $productData[$colName] = $row[$idx] ?? '';
                    }

                    $csvBrand = strtolower(trim(preg_replace('/\s+/', ' ', $row[0] ?? '')));
                    $csvProduct = strtolower(trim(preg_replace('/\s+/', ' ', $row[1] ?? '')));

                    foreach ($successRecords as $sRecord) {
                        $successBrand = strtolower(trim(preg_replace('/\s+/', ' ', $sRecord['brand'] ?? '')));
                        $successProduct = strtolower(trim(preg_replace('/\s+/', ' ', $sRecord['product name'] ?? '')));

                        if ($successBrand === $csvBrand && $successProduct === $csvProduct) {
                            $allRecords[] = array_merge($productData, [
                                'reason' => $sRecord['reason'] ?? 'Product processed successfully',
                                'status' => $sRecord['status'] ?? 'Success'
                            ]);
                            $found = true;
                            break;
                        }
                    }

                    if (!$found) {
                        foreach ($failedRecords as $fRecord) {
                            $failedBrand = strtolower(trim(preg_replace('/\s+/', ' ', $fRecord['brand'] ?? '')));
                            $failedProduct = strtolower(trim(preg_replace('/\s+/', ' ', $fRecord['product name'] ?? '')));

                            if ($failedBrand === $csvBrand && $failedProduct === $csvProduct) {
                                $allRecords[] = array_merge($productData, [
                                    'reason' => $fRecord['reason'] ?? 'Unknown error in failed record',
                                    'status' => $fRecord['status'] ?? 'Failed'
                                ]);
                                $found = true;
                                break;
                            }
                        }
                    }

                    if (!$found) {
                        $allRecords[] = array_merge($productData, [
                            'reason' => 'Row not processed by chunk processor',
                            'status' => 'Failed'
                        ]);
                    }
                }

                $outputs[] = [
                    'success'     => true,
                    'errorRows'   => $decodedOutput['errorRows'] ?? 0,
                    'successRows' => $decodedOutput['successRows'] ?? 0,
                    'total'       => $decodedOutput['total'] ?? 0
                ];
            }
        }

        $totalSuccessRows = count(array_filter($allRecords, fn($record) => $record['status'] === 'Success'));
        $totalErrorRows = count(array_filter($allRecords, fn($record) => $record['status'] === 'Failed'));

        CSVUtility::$records = $allRecords;
        CSVUtility::$successRows = $totalSuccessRows;
        CSVUtility::$errorRows = $totalErrorRows;

        if (!empty($data['auditFilename'])) {
            self::writeAuditLog($data['auditFilename'], $data['jobId']);
        }

        if ($totalErrorRows === 0 && $totalSuccessRows > 0) {
            Log::info("✅ All records processed successfully. Proceeding with data insertion.", [
                'total_success' => $totalSuccessRows,
                'job_id' => $data['jobId']
            ]);
            // self::insertAllProducts($allRecords);
        } else {
            Log::warning("⚠️ Some records failed. No data will be inserted.", [
                'total_success' => $totalSuccessRows,
                'total_failed' => $totalErrorRows,
                'job_id' => $data['jobId']
            ]);
        }

        // Clean up local temp
        @unlink($localExcelPath);
        @unlink($localZipPath);
        @exec("rm -rf " . escapeshellarg($extractPath));
        @rmdir($localTempDir);

        return $outputs;
    }

    // public static function importData(array $data)
    // {
    //     if (empty($data['file_path']) || empty($data['folder_id'])) {
    //         // Log::warning('Missing file_path or folder_id', $data);
    //         return [[
    //             'success' => false,
    //             'chunk' => 0,
    //             'output' => 'Missing file_path or folder_id'
    //         ]];
    //     }

    //     $folderPath  = public_path("storage/imports/{$data['folder_id']}");
    //     $xlsxFile    = "{$folderPath}/uploaded_file.xlsx";
    //     $zipFile     = "{$folderPath}/uploaded_file.zip";
    //     $extractPath = "{$folderPath}/extracted_images";

    //     if (!file_exists($xlsxFile) || !file_exists($zipFile)) {
    //         //Log::error('Excel or ZIP file not found', ['xlsx' => $xlsxFile, 'zip' => $zipFile]);
    //         return [[
    //             'success' => false,
    //             'chunk' => 0,
    //             'output' => 'Excel or ZIP file not found'
    //         ]];
    //     }

    //     if (!file_exists($extractPath)) {
    //         if (!mkdir($extractPath, 0777, true)) {
    //             //Log::error('Failed to create extracted_images folder', ['path' => $extractPath]);
    //             return [[
    //                 'success' => false,
    //                 'chunk' => 0,
    //                 'output' => 'Failed to create extracted_images folder'
    //             ]];
    //         }
    //         chmod($extractPath, 0777);
    //     }

    //     if (!self::extractZip($zipFile, $extractPath)) {
    //         //Log::error('Failed to extract ZIP file', ['zip' => $zipFile]);
    //         return [[
    //             'success' => false,
    //             'chunk' => 0,
    //             'output' => 'Failed to extract ZIP file'
    //         ]];
    //     }

    //     try {
    //         $spreadsheet = IOFactory::load($xlsxFile);
    //         $rows        = $spreadsheet->getActiveSheet()->toArray();
    //         $header      = array_map('strtolower', $rows[0]);
    //     } catch (\Exception $e) {
    //         //Log::error('Failed to load spreadsheet', ['error' => $e->getMessage()]);
    //         return [[
    //             'success' => false,
    //             'chunk' => 0,
    //             'output' => 'Failed to load spreadsheet: ' . $e->getMessage()
    //         ]];
    //     }

    //     $imageIndex = array_search('product images', $header);
    //     if ($imageIndex === false) {
    //         //Log::error('Product images column not found in header', ['header' => $header]);
    //         return [[
    //             'success' => false,
    //             'chunk' => 0,
    //             'output' => 'Product images column not found in header'
    //         ]];
    //     }

    //     $chunks = array_chunk(array_slice($rows, 1), self::CHUNK_SIZE);
    //     $outputs = [];
    //     $allRecords = [];
    //     $processes = [];

    //     foreach ($chunks as $index => $chunk) {
    //         $chunkData = json_encode($chunk);
    //         $process = new Process([
    //             'php',
    //             base_path('artisan'),
    //             'process-product-chunk',
    //             base64_encode($chunkData),
    //             $data['jobId'],
    //             $extractPath
    //         ]);
    //         $process->start();
    //         $processes[$index] = $process;
    //     }

    //     foreach ($processes as $index => $process) {
    //         $process->wait();
    //         if (!$process->isSuccessful()) {
    //             $errorOutput = $process->getErrorOutput();
    //             $outputs[] = [
    //                 'chunk'   => $index,
    //                 'success' => false,
    //                 'output'  => $errorOutput
    //             ];
    //         } else {
    //             $successOutput = $process->getOutput();
    //             $decodedOutput = json_decode($successOutput, true);
    //             if (json_last_error() !== JSON_ERROR_NONE || !is_array($decodedOutput)) {

    //                 $outputs[] = [
    //                     'chunk'   => $index,
    //                     'success' => false,
    //                     'output'  => 'Invalid JSON output: ' . $successOutput
    //                 ];
    //                 continue;
    //             }

    //             $successRecords = $decodedOutput['successRecords'] ?? [];
    //             $failedRecords = $decodedOutput['failedRecords'] ?? [];

    //             // Reconstruct records in original order
    //             foreach ($chunks[$index] as $i => $row) {
    //                 $found = false;
    //                 $productData = [];

    //                 foreach (self::getImportHeader() as $idx => $colName) {
    //                     $productData[$colName] = $row[$idx] ?? '';
    //                 }

    //                 // Normalize input row values
    //                 $csvBrand = strtolower(trim(preg_replace('/\s+/', ' ', $row[0] ?? '')));
    //                 $csvProduct = strtolower(trim(preg_replace('/\s+/', ' ', $row[1] ?? '')));

    //                 // Check success records
    //                 foreach ($successRecords as $sRecord) {
    //                     $successBrand = strtolower(trim(preg_replace('/\s+/', ' ', $sRecord['brand'] ?? '')));
    //                     $successProduct = strtolower(trim(preg_replace('/\s+/', ' ', $sRecord['product name'] ?? '')));

    //                     if ($successBrand === $csvBrand && $successProduct === $csvProduct) {
    //                         $allRecords[] = array_merge($productData, [
    //                             'reason' => $sRecord['reason'] ?? 'Product processed successfully',
    //                             'status' => $sRecord['status'] ?? 'Success'
    //                         ]);
    //                         $found = true;
    //                         break;
    //                     }
    //                 }

    //                 // Check failed records
    //                 if (!$found) {
    //                     foreach ($failedRecords as $fRecord) {
    //                         $failedBrand = strtolower(trim(preg_replace('/\s+/', ' ', $fRecord['brand'] ?? '')));
    //                         $failedProduct = strtolower(trim(preg_replace('/\s+/', ' ', $fRecord['product name'] ?? '')));

    //                         if ($failedBrand === $csvBrand && $failedProduct === $csvProduct) {
    //                             $allRecords[] = array_merge($productData, [
    //                                 'reason' => $fRecord['reason'] ?? 'Unknown error in failed record',
    //                                 'status' => $fRecord['status'] ?? 'Failed'
    //                             ]);
    //                             $found = true;
    //                             break;
    //                         }
    //                     }
    //                 }

    //                 // Fallback only if truly unmatched
    //                 if (!$found) {
    //                     $allRecords[] = array_merge($productData, [
    //                         'reason' => 'Row not processed by chunk processor',
    //                         'status' => 'Failed'
    //                     ]);
    //                 }
    //             }


    //             $outputs[] = [
    //                 'success'       => true,
    //                 'errorRows'     => $decodedOutput['errorRows'] ?? 0,
    //                 'successRows'   => $decodedOutput['successRows'] ?? 0,
    //                 'total'         => $decodedOutput['total'] ?? 0
    //             ];
    //         }
    //     }

    //     // Calculate success and error rows from allRecords
    //     $totalSuccessRows = count(array_filter($allRecords, fn($record) => $record['status'] === 'Success'));
    //     $totalErrorRows = count(array_filter($allRecords, fn($record) => $record['status'] === 'Failed'));

    //     CSVUtility::$records = $allRecords;
    //     CSVUtility::$successRows = $totalSuccessRows;
    //     CSVUtility::$errorRows = $totalErrorRows;

    //     // Write audit log with all records (success and failed)
    //     if (!empty($data['auditFilename'])) {
    //         self::writeAuditLog($data['auditFilename'], $data['jobId']);
    //     }

    //     // Only insert data if ALL records are successful (no failures)
    //     if ($totalErrorRows === 0 && $totalSuccessRows > 0) {
    //         Log::info("All records processed successfully. Proceeding with data insertion.", [
    //             'total_success' => $totalSuccessRows,
    //             'job_id' => $data['jobId']
    //         ]);
    //         // Here you can add your actual data insertion logic
    //         // self::insertAllProducts($allRecords);
    //     } else {
    //         Log::warning("Some records failed. No data will be inserted.", [
    //             'total_success' => $totalSuccessRows,
    //             'total_failed' => $totalErrorRows,
    //             'job_id' => $data['jobId']
    //         ]);
    //     }

    //     return $outputs;
    // }

    private static function extractZip($zipFile, $extractPath)
    {
        $zip = new ZipArchive();
        if ($zip->open($zipFile) !== true) {
            //Log::error('Failed to open ZIP file', ['zip' => $zipFile]);
            return false;
        }

        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        $success = true;

        for ($i = 0; $i < $zip->numFiles; $i++) {
            $filename = $zip->getNameIndex($i);

            // Skip macOS system files
            if (
                str_starts_with($filename, '__MACOSX') ||
                str_contains($filename, '/__MACOSX') ||
                str_ends_with($filename, '.DS_Store') ||
                basename($filename) === ''
            ) {
                continue;
            }

            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

            if (in_array($extension, $allowedExtensions)) {
                $basename = basename($filename);
                $destination = $extractPath . '/' . $basename;

                $fileContent = $zip->getStream($filename);
                if ($fileContent === false) {

                    $success = false;
                    continue;
                }

                $result = file_put_contents($destination, stream_get_contents($fileContent));
                fclose($fileContent);

                if ($result === false) {
                    $success = false;
                }
            }
        }

        $zip->close();
        return $success;
    }

    public static function processProductRow(array $row, $jobId, $extractPath)
    {
        $productData = [];
        $auditReason = '';
        $auditStatus = 'Failed';

        try {
            $hasErrors = false;

            // Initialize audit data early
            $row = array_map('trim', $row);
            $header = self::getImportHeader();
            foreach ($header as $index => $columnName) {
                $productData[$columnName] = $row[$index] ?? '';
            }

            if (empty(array_filter($row))) {
                $auditReason = "Empty row - no data to process";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Extract product information for audit logging
            $productName = $productData['product name'] ?? '';
            $brand = $productData['brand'] ?? '';
            $genericName = $productData['generic name'] ?? '';
            $b2bCategory = $productData['b2b category'] ?? '';
            $b2bSubcategory = $productData['b2b subcategory'] ?? '';
            $dosageForm = $productData['dosage form'] ?? '';

            $container = $productData['container'] ?? '';
            $prescriptionRequired = $productData['prescription required'] ?? '';
            $weightInGms = $productData['weight in gms'] ?? '';


            $productDescription = $productData['product description'] ?? '';
            $keyIngredients = $productData['key ingredients'] ?? '';
            $storageInstructions = $productData['storage instructions'] ?? '';
            $usageIndication = $productData['usage/indication'] ?? '';
            $contraindication = $productData['contraindication'] ?? '';
            $howToUse = $productData['how to use'] ?? '';
            $safetyInformation = $productData['safety information/pregnancy'] ?? '';
            $dosageInformation = $productData['dosage information'] ?? '';
            $sideEffects = $productData['side effects'] ?? '';
            $volume = $productData['volume'] ?? '';
            $volumeUnit = $productData['volume unit'] ?? '';

            // Process images
            $imageIndex = array_search('product images', $header);
            $imageNames = !empty($row[$imageIndex])
                ? array_filter(array_map('trim', preg_split('/[,;\n]+/', $row[$imageIndex])))
                : [];

            $imageNames = array_map('strtolower', $imageNames);
            $defaultImageName = !empty($imageNames) ? $imageNames[0] : null;

            // Validation with detailed audit reasons
            $validationErrors = [];
            if (empty($productName)) $validationErrors[] = "Product Name is required";
            if (empty($brand)) $validationErrors[] = "Brand is required";
            if (empty($genericName)) $validationErrors[] = "Generic Name is required";
            if (empty($b2bCategory)) $validationErrors[] = "B2B Category is required";
            if (empty($b2bSubcategory)) $validationErrors[] = "B2B Subcategory is required";
            if (empty($dosageForm)) $validationErrors[] = "Dosage Form is required";
            if (empty($container)) $validationErrors[] = "Container is required";
            if (empty($volume)) $validationErrors[] = "Volume is required";
            if (empty($volumeUnit)) $validationErrors[] = "Volume Unit is required";
            if (empty($weightInGms)) $validationErrors[] = "weight In Gms Form is required";

            if (!empty($validationErrors)) {
                $auditReason = "Validation failed: " . implode("; ", $validationErrors);
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Check for existing product
            $existingProduct = Product::where('name', 'ILIKE', trim($productName))->first();
            if ($existingProduct) {
                $auditReason = "Product '{$productName}' already exists in database (ID: {$existingProduct->id})";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate brand
            $brandModel = Brand::where('name', 'ILIKE', trim($brand))->first();
            if (!$brandModel) {
                $auditReason = "Brand '{$brand}' not found in database - please add brand first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate generic name
            $genericNameModel = GenericName::where('name', 'ILIKE', trim($genericName))->first();
            if (!$genericNameModel) {
                $auditReason = "Generic Name '{$genericName}' not found in database - please add generic name first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate category
            $categoryModel = Category::where('name', 'ILIKE', trim($b2bCategory))->first();
            if (!$categoryModel) {
                $auditReason = "B2B Category '{$b2bCategory}' not found in database - please add category first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate subcategory
            $subCategoryModel = Category::where('name', 'ILIKE', trim($b2bSubcategory))
                ->where('parent_id', $categoryModel->id)
                ->first();
            if (!$subCategoryModel) {
                $auditReason = "B2B Subcategory '{$b2bSubcategory}' not found under category '{$b2bCategory}' - please check category hierarchy";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate dosage form
            $trimmedDosageForm = trim($dosageForm);
            $dosageFormModel = DosageForm::where('name', 'ILIKE', $trimmedDosageForm)->first();
            if (!$dosageFormModel) {
                $auditReason = "Dosage Form '{$dosageForm}' not found in database - please add dosage form first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate container
            $container = trim($container);
            $container = preg_replace('/\s+/', ' ', $container); // Normalize spaces
            $container = Container::where('name', 'ILIKE', $container)->first();
            if (!$container) {
                $auditReason = "Container '{$container}' not found in database - please add container first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate volume unit
            $volumeUnit = trim($volumeUnit);
            $volumeUnit = preg_replace('/\s+/', ' ', $volumeUnit); //
            $unitModel = Unit::where('name', 'ILIKE', trim($volumeUnit))->first();
            if (!$unitModel) {
                $auditReason = "Volume Unit '{$volumeUnit}' not found in database - please add unit first";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate distributors
            $distributorNames = $productData['distributor'] ?? '';
            $distributorList = array_filter(array_map('trim', explode(',', trim($distributorNames))));
            $validDistributors = \App\Models\Distributor::whereIn('name', $distributorList)->get();
            if ($validDistributors->isEmpty()) {
                $auditReason = "No valid distributors found for names: " . implode(', ', $distributorList) . " - please check distributor names";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // Validate images
            $defaultImageId = null;
            $imageErrors = [];
            $actualFiles = [];

            // ✅ Only scan if path exists
            if (file_exists($extractPath)) {
                foreach (scandir($extractPath) as $file) {
                    if (in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                        $actualFiles[strtolower($file)] = $file;
                    }
                }
            } else {
                $auditReason = "Extracted image folder not found: {$extractPath}";
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            $mediaItems = [];
            foreach ($imageNames as $imageName) {
                $imageName = trim(strtolower($imageName));
                if (empty($imageName)) continue;

                if (!isset($actualFiles[$imageName])) {
                    $imageErrors[] = "Image file '{$imageName}' not found in extracted ZIP";
                    continue;
                }

                $actualFileName = $actualFiles[$imageName];
                $imagePath = "{$extractPath}/{$actualFileName}";

                try {
                    if (!is_readable($imagePath)) {
                        chmod($imagePath, 0644);
                    }
                    $mediaItems[] = [
                        'path' => $imagePath,
                        'name' => pathinfo($actualFileName, PATHINFO_FILENAME),
                        'original_filename' => $actualFileName,
                        'is_default' => (strtolower($imageName) === strtolower($defaultImageName))
                    ];
                } catch (\Exception $e) {
                    $imageErrors[] = "Failed to process image '{$imageName}': " . $e->getMessage();
                }
            }

            if (!empty($imageErrors)) {
                $auditReason = "Image processing failed: " . implode("; ", $imageErrors);
                self::recordFailedProduct($productData, $auditReason);
                return false;
            }

            // If all validations pass
            $auditReason = "Product data validated successfully";
            $auditStatus = 'Success';

            self::recordSuccessfulProduct($productData, $auditReason);

            return [
                "data" => [
                    'name' => $productName,
                    'brand_id' => $brandModel->id,
                    'category_id' => $categoryModel->id,
                    'sub_category_id' => $subCategoryModel->id,
                    'dosage_foams_id' => $dosageFormModel->id,
                    'sku' => mb_substr($productName, 0, 4) . "_" . rand(100000, 999999),
                    'generic_name_id' => $genericNameModel->id,
                    'add_request_by' => 1,
                    'product_description' => $productDescription,
                    'description_ingredients' => $keyIngredients,
                    'description_safety_information' => $safetyInformation,
                    'description_dosage' => $dosageInformation,
                    'weight' => $productData['weight in gms'] ?? null,
                    'description_indications' => $usageIndication,
                    'description_storage_instructions' => $storageInstructions,
                    'description_side_effects' => $sideEffects,
                    'description_contradictions' => $contraindication,
                    'is_prescription_required' => $prescriptionRequired === 'yes' ? 1 : 0,
                    'container_id' => $container->id,
                    'description_how_to_use' => $howToUse,
                    'status' => 'approved',
                    'unit_id' => $unitModel->id ?? null,
                    'quantity_per_unit' => $volume,
                    'created_at' => now(),
                    'owner_id' => auth()->id ?? 1,
                    'updated_at' => now(),
                ],
                "mediaItems" => $mediaItems,
                "validDistributors" => $validDistributors,
                "defaultImageName" => $defaultImageName,
                "status" => "Success",
            ];
        } catch (\Exception $e) {
            $auditReason = 'Exception occurred during processing: ' . $e->getMessage() . ' (Line: ' . $e->getLine() . ')';
            self::recordFailedProduct($productData, $auditReason);
            return false;
        }
    }

    private static function recordSuccessfulProduct(array $productData, string $reason = 'Product processed successfully')
    {
        CSVUtility::$records[] = array_merge($productData, [
            'reason' => $reason,
            'status' => 'Success'
        ]);
        CSVUtility::$successRows++;
    }

    private static function recordFailedProduct(array $productData, string $reason)
    {
        $record = [];
        foreach (self::getImportHeader() as $column) {
            $record[$column] = $productData[$column] ?? '';
        }
        $record['reason'] = $reason;
        $record['status'] = 'Failed';
        CSVUtility::$records[] = $record;
        CSVUtility::$errorRows++;
    }

    public static function getImportHeader()
    {
        return [
            'brand',
            'product name',
            'generic name',
            'b2b category',
            'b2b subcategory',
            'b2c category',
            'b2c subcategory',
            'distributor',
            'prescription required',
            'dosage form',
            'container',
            'volume',
            'volume unit',
            'weight in gms',
            'product images',
            'product description',
            'key ingredients',
            'storage instructions',
            'usage/indication',
            'contraindication',
            'how to use',
            'safety information/pregnancy',
            'dosage information',
            'side effects'
        ];
    }

    protected static function writeAuditLog($auditFilename, $jobId)
    {
        $csvUtility = new CSVUtility();
        $summaryHeader = ['Total Successful Products', 'Total Failed Products'];
        $summaryData = [[CSVUtility::$successRows, CSVUtility::$errorRows]];
        $recordHeader = self::getImportHeader();
        $recordHeader[] = 'reason';
        $recordHeader[] = 'status';

        $records = [];
        foreach (CSVUtility::$records as $row) {
            $record = [];
            foreach (self::getImportHeader() as $key) {
                $record[] = $row[$key] ?? '';
            }
            $record[] = $row['reason'] ?? '';
            $record[] = $row['status'] ?? 'Failed';
            $records[] = $record;
        }

        // Structure CSV data: Header on row 1, records, then summary
        $csvData = [
            array_map('ucfirst', $recordHeader), // Header on row 1
        ];

        // Add all records
        $csvData = array_merge($csvData, $records);

        // Add blank row and summary at the bottom
        $csvData[] = [''];
        $csvData[] = $summaryHeader;
        $csvData[] = $summaryData[0];

        $csvUtility->insertCSVRowData($csvData, $auditFilename, []);
    }

    /**
     * Method to actually insert all product data (call this only when all records are successful)
     */
    private static function insertAllProducts(array $allRecords)
    {
        try {
            DB::beginTransaction();

            $insertedCount = 0;
            foreach ($allRecords as $record) {
                if ($record['status'] === 'Success') {
                    // Add your actual product insertion logic here
                    // This is where you would insert into products table, distributor_product table, etc.
                    $insertedCount++;
                }
            }

            DB::commit();
            Log::info("Successfully inserted all products", ['inserted_count' => $insertedCount]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to insert products", ['error' => $e->getMessage()]);
            throw $e;
        }
    }
}
