<?php

namespace App\Services;

use App\Models\ProductRelation;
use App\Service\ShippingService;
use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Repositories\Api\Clinic\OrderRepository;

class CartService
{
    protected $shippingService;
    private $order;
    protected $orderRepository;

    public function __construct()
    {
        $this->shippingService = new ShippingService();
        $this->order = new Order();
        $this->orderRepository = new OrderRepository($this->order);

    }

    /**
     * Process cart data for listing
     */
    public function processCartListingData($cartItems, $isShipping = false)
    {
        $zonePrefix = getClinicZone();
        $supplier = $cartItems->first()->supplier;
        $wareHouseType = $supplier->wareHouse ? $supplier->wareHouse->warehouse_type : 'own';
        
        $productWeight = 0;
        $supplierTotalQty = 0;
        $reqAppliedPoints = 0;
        $deliveryType = null;
        $paymentType = null;

        // Map over cart items to process each product
        $products = $cartItems->map(function ($cartItem) use ($zonePrefix, $wareHouseType, &$reqAppliedPoints, &$deliveryType, &$paymentType, &$productWeight, &$supplierTotalQty) {
            $productData = $this->processCartProduct($cartItem, $zonePrefix, $wareHouseType);
            
            // Extract meta data from first item
            if (!$deliveryType && $productData['meta_data']) {
                $deliveryType = $productData['meta_data']->delivery_type ?? null;
                $paymentType = $productData['meta_data']->payment_type ?? 'pay_now';
                $reqAppliedPoints = $productData['meta_data']->applied_points ?? 0;
            }

            if ($wareHouseType == 'dpharma') {
                $productWeight += $productData['weight'];
                $supplierTotalQty += $cartItem->quantity;
            }

            return $productData['product'];
        });

        $shippingCostDetail = [];
        $shippingFee = 0;

        // Calculate shipping if needed
        if ($wareHouseType == 'dpharma' && !$isShipping) {
            $postalCode = $supplier->wareHouse->postal_code;
            $shippingCostDetail = $this->shippingService->getOrderShipCharge($productWeight, $supplierTotalQty, $postalCode);
            if ($shippingCostDetail && $shippingCostDetail['isSuccess']) {
                $deliveryType = $deliveryType ? $deliveryType : (isset($shippingCostDetail['result'][0]) ? $shippingCostDetail['result'][0]['serviceType'] : '');

                
                $shippingFeeRes = array_values(array_filter($shippingCostDetail['result'], function ($item) use ($deliveryType) {
                    return $item['serviceType'] === $deliveryType;
                }));
                $shippingFee = isset($shippingFeeRes[0]) ? (float) str_replace(',', '', $shippingFeeRes[0]['charge']) : 0;

               
            }
        }

        return [
            'supplier' => $this->getSupplierInfo($supplier, $wareHouseType),
            'products' => $products->toArray(),
            'shipping_detail' => $shippingCostDetail ?? [],
            'shipping_fee' => $shippingFee,
            'delivery_type' => $deliveryType,
            'payment_type' => $paymentType,
            'req_applied_points' => $reqAppliedPoints
        ];
    }

    /**
     * Process cart data for payment
     */
    public function processCartPaymentData($cartItems, $requestSup, $supIdToFind)
    {
        $zonePrefix = getClinicZone();
        $supplier = $cartItems->first()->supplier;
        $wareHouseType = $supplier->wareHouse ? $supplier->wareHouse->warehouse_type : 'own';
        
        $productTotalAmount = 0;
        
        // Map over cart items to process each product
        $products = $cartItems->map(function ($cartItem) use ($zonePrefix, &$productTotalAmount) {
            $productData = $this->getProductPriceData($cartItem, $zonePrefix);
            $productTotalAmount += $productData['total_amount'];
            return $productData['product'];
        });

        $shippingFee = 0;
        $shippingDetail = [];
        
        if ($wareHouseType == 'dpharma') {
            $findService = $this->requestServiceFind($requestSup, $supIdToFind);
            $shippingFee = $findService ? (float) str_replace(',', '', $findService['charge']) : 0;
            $shippingDetail = $findService;
        }

        return [
            'supplier' => $this->getSupplierInfo($supplier, $wareHouseType),
            'products' => $products->toArray(),
            'supplier_amount' => round($productTotalAmount),
            'shipping_fee' => $shippingFee,
            'shipping_detail' => $shippingDetail ?? [],
            'delivery_type' => $shippingDetail ? $shippingDetail['serviceType'] : null
        ];
    }

    /**
     * Process cart data for review
     */
    public function processCartReviewData($cartItems)
    {
        $zonePrefix = getClinicZone();
        $supplier = $cartItems->first()->supplier;
        $wareHouseType = $supplier->wareHouse ? $supplier->wareHouse->warehouse_type : 'own';
        
        $productTotalAmount = 0;
        $productWeight = 0;
        $supplierTotalQty = 0;
        $deliveryType = null;
        $reqAppliedPoints = 0;
        $grandTotal = 0;
        $paymentType = 'pay_now';

        // Map over cart items to process each product
        $products = $cartItems->map(function ($cartItem) use ($zonePrefix, $wareHouseType, &$productTotalAmount, &$deliveryType, &$reqAppliedPoints, &$paymentType, &$productWeight, &$supplierTotalQty, &$grandTotal) {
            $productData = $this->processCartProduct($cartItem, $zonePrefix, $wareHouseType, true);
            $productTotalAmount += $productData['total_amount'];
            
            // Extract meta data from first item
            if (!$deliveryType && $productData['meta_data']) {
                $deliveryType = $productData['meta_data']->delivery_type ?? null;
                $reqAppliedPoints = $productData['meta_data']->applied_points ?? 0;
                $grandTotal = $productData['meta_data']->grand_total ?? 0;
                $paymentType = $productData['meta_data']->payment_type ?? 'pay_now';
            }

            if ($wareHouseType == 'dpharma') {
                $productWeight += $productData['weight'];
                $supplierTotalQty += $cartItem->quantity;
            }

            return $productData['product'];
        });

        $shippingCostDetail = [];
        $shippingFee = 0;
        
        if ($wareHouseType == 'dpharma') {
            $postalCode = $supplier->wareHouse->postal_code;
            $shippingCostDetail = $this->shippingService->getOrderShipCharge($productWeight, $supplierTotalQty, $postalCode);
            
            if ($shippingCostDetail && $shippingCostDetail['isSuccess']) {
                $shippingFee = isset($shippingCostDetail['result'][0]) ? (float) str_replace(',', '', $shippingCostDetail['result'][0]['charge']) : 0;
            }
        }

        $supplierAmount = $productTotalAmount + $shippingFee;
        
        // Calculate applied points for this supplier
        $appliedPoints = 0;
        if ($reqAppliedPoints > 0) {
            $supplierPer = round(($supplierAmount / $grandTotal) * 100);
            $appliedPoints = ($reqAppliedPoints * $supplierPer / 100);
        }
        
        if ($appliedPoints > round($supplierAmount)) {
            throw new \Exception(trans('api.clinic.cart.applied_points_error'));
        }

        return [
            'supplier' => $this->getSupplierInfo($supplier, $wareHouseType),
            'products' => $products->toArray(),
            'supplier_amount' => round($supplierAmount),
            'shipping_fee' => $shippingFee,
            'shipping_detail' => $shippingCostDetail ?? [],
            'delivery_type' => $deliveryType,
            'payment_type' => $paymentType,
            'req_applied_points' => $reqAppliedPoints,
            'applied_points' => $appliedPoints
        ];
    }

    /**
     * Get supplier information
     */
    protected function getSupplierInfo($supplier, $wareHouseType)
    {
        return [
            'supplier_id' => encryptParam($supplier->user_id),
            'id' => encryptParam($supplier->user_id),
            'business_name' => ucfirst($supplier->business_name),
            'company_name' => pcCompanyName($supplier),
            'user_name' => $supplier->user->name,
            'is_fav' => $supplier->fav ? true : false,
            'dpharma_logistic' => $wareHouseType == 'dpharma',
            'delivery_days' => $supplier->delivery_days,
            'min_order' => (int) $supplier->min_order_value,
            'credit_line_limit' => (float) (isset($supplier->ClinicCreditLimit->remaining_amount) &&
                $supplier->ClinicCreditLimit->remaining_amount > 0 ?
                $supplier->ClinicCreditLimit->remaining_amount : 0)
        ];
    }

    /**
     * Process individual cart product
     */
    protected function processCartProduct($cartItem, $zonePrefix, $wareHouseType, $isReview = false)
    {
        $supPro = ProductRelation::where('product_id', $cartItem->product_id)
            ->with(['productRelationStock', 'productRelationPrice'])
            ->where('user_id', $cartItem->supplier_id)
            ->first();
        
        $this->updateTier($cartItem,$supPro,$zonePrefix);
        $priceBase = $supPro->productRelationPrice;
        $getFreeQty = 0;
        $cartMetaData = $cartItem['meta_data'] ? json_decode($cartItem['meta_data']) : null;

        if ($supPro->price_type == 'bonus') {
            $getFreeQty = $this->getFreeQty($supPro, $zonePrefix, $cartItem, $priceBase);
        }

        $price = $this->getProductPrice($supPro, $priceBase, $zonePrefix, $cartItem->tier_number,$cartItem->quantity);
        $totalAmount = $price * $cartItem->quantity;
        $weight = $wareHouseType == 'dpharma' ? $supPro->products->weight : 0;

        $defaultImage = $cartItem->product->getResolvedDefaultImageAttribute();

        $product = [
            "id" => encryptParam($cartItem->id),
            "supplier_id" => encryptParam($cartItem->supplier_id),
            "product_id" => encryptParam($cartItem->product_id),
            "product_name" => ucfirst($cartItem->product->name),
            "unit_name" => ucfirst($cartItem->product->unit->name) ?? null,
            "foams_name" => ucfirst($cartItem->product->foam->name),
            "is_fav" => $cartItem->product->fav ? true : false,
            "quantity_per_unit" => $cartItem->product->quantity_per_unit,
            "product_qty" => $cartItem->quantity,
            "medias" => $defaultImage ? getImage($defaultImage->file_name, $defaultImage->id) : asset('images/default-image.png'),
            "price" => $price,
            'stock' => $this->getStock($supPro),
            "wholesale_pack_size" => $this->getWholesaleSize($supPro, $cartItem),
            'get_free_qty' => $getFreeQty,
            'price_type' => $supPro->price_type,
            "is_out_stock" => $this->getStock($supPro) > 0 ? false : true
        ];

        // Add tier pricing details if not review
        if (!$isReview) {
            $product = array_merge($product, [
                "tier_1_min_quantity" => $priceBase->{$zonePrefix . '_tier_1_min_quantity'},
                "tier_1_max_quantity" => $priceBase->{$zonePrefix . '_tier_1_max_quantity'},
                "tier_1_base_price" => $priceBase->{$zonePrefix . '_tier_1_base_price'},
                "tier_2_min_quantity" => $priceBase->{$zonePrefix . '_tier_2_min_quantity'},
                "tier_2_max_quantity" => $priceBase->{$zonePrefix . '_tier_2_max_quantity'},
                "tier_2_base_price" => $priceBase->{$zonePrefix . '_tier_2_base_price'},
                "tier_3_min_quantity" => $priceBase->{$zonePrefix . '_tier_3_min_quantity'},
                "tier_3_max_quantity" => $priceBase->{$zonePrefix . '_tier_3_max_quantity'},
                "tier_3_base_price" => $priceBase->{$zonePrefix . '_tier_3_base_price'},
                "bonus_1_quantity" => $priceBase->{$zonePrefix . '_bonus_1_quantity'},
                "bonus_1_quantity_value" => $priceBase->{$zonePrefix . '_bonus_1_quantity_value'},
                "bonus_2_quantity" => $priceBase->{$zonePrefix . '_bonus_2_quantity'},
                "bonus_2_quantity_value" => $priceBase->{$zonePrefix . '_bonus_2_quantity_value'},
                "bonus_3_quantity" => $priceBase->{$zonePrefix . '_bonus_3_quantity'},
                "bonus_3_quantity_value" => $priceBase->{$zonePrefix . '_bonus_3_quantity_value'}
            ]);
        }

        return [
            'product' => $product,
            'total_amount' => $totalAmount,
            'weight' => $weight,
            'meta_data' => $cartMetaData
        ];
    }

    public function updateTier($cartItem,$supPro,$zonePrefix){
        $tierNumber = $cartItem->tier_number;
        $tierNumber = $this->orderRepository->getTierBasedQuantity($supPro->product_id,$supPro->user_id,$zonePrefix,$cartItem->quantity);
        $cartItem->update(['tier_number' => $tierNumber]);

        return true;
    }

    /**
     * Get product price data for payment
     */
    protected function getProductPriceData($cartItem, $zonePrefix)
    {
        $supPro = ProductRelation::with('productRelationPrice')
            ->where('product_id', $cartItem->product_id)
            ->where('user_id', $cartItem->supplier_id)
            ->first();

        $basePrice = $supPro->productRelationPrice;
        $singleProductPrice = $supPro->price_type == 'fixed' || $supPro->price_type == 'bonus' 
            ? $basePrice->{$zonePrefix . '_zone_price'}
            : $basePrice->{$zonePrefix . '_' . $cartItem->tier_number . '_base_price'} ?? 0;

        $totalAmount = $singleProductPrice * $cartItem->quantity;

        return [
            'product' => [
                "id" => encryptParam($cartItem->id),
                "supplier_id" => encryptParam($cartItem->supplier_id),
                "product_id" => encryptParam($cartItem->product_id),
                "product_qty" => $cartItem->quantity,
                "price" => $singleProductPrice,
            ],
            'total_amount' => $totalAmount
        ];
    }

    /**
     * Get product price based on type and tier
     */
    protected function getProductPrice($supPro, $priceBase, $zonePrefix, $tierNumber,$quantity)
    {
        if ($supPro->price_type == 'tier') {
            return $priceBase->{$zonePrefix . '_' . $tierNumber . '_base_price'};
        }
        
        return $priceBase->{$zonePrefix . '_zone_price'};
    }

    /**
     * Calculate stock for a product
     */
    protected function getStock($supPro)
    {
        $productRelationStock = $supPro->productRelationStock;

        if ($productRelationStock->is_batch_wise_stock) {
            $stock = $supPro->batches()->sum('available_stock');
        } else {
            $stock = $productRelationStock->stock;
        }

        if ($productRelationStock->stock_type == 'unit') {
            $stock = ($stock / $productRelationStock->wholesale_pack_size);
        }

        return floor($stock);
    }

    /**
     * Get wholesale pack size description
     */
    protected function getWholesaleSize($supPro, $cartItem)
    {
        $packSize = ($supPro->productRelationStock->wholesale_pack_size);

        return $packSize 
            ? trans('api.clinic.cart.wolesale_size') . $packSize . ' ' . Str::plural($cartItem->product->container->name) . ' (' . ($packSize * $cartItem->product->quantity_per_unit) . ' ' . Str::plural($cartItem->product->foam->name) . ')'
            : null;
    }

    /**
     * Calculate free quantity for bonus pricing
     */
   public function getFreeQty($supPro, $zonePrefix, $cartItem, $priceBase)
    {
        $quantity = $cartItem->quantity;
        $tierNumber = $cartItem->tier_number;
        
        return  $supPro->price_type == 'bonus' && !empty($tierNumber) ?
                floor($quantity / $priceBase->{$zonePrefix.'_'.$tierNumber.'_quantity'}) * $priceBase->{$zonePrefix.'_'.$tierNumber.'_quantity_value'} : 0;

    }

    /**
     * Find service details from request suppliers
     */
    public function requestServiceFind($requestSup, $supIdToFind)
    {
        $findService = [];
        $filteredSupArray = array_values(array_filter($requestSup, function($requestItem) use ($supIdToFind) {
            return $requestItem['id'] === $supIdToFind;
        }));
        
        if (!empty($filteredSupArray)) {
            $supShippingDetail = $filteredSupArray[0];
            $targetServiceType = $supShippingDetail['delivery_type'];
            $shippingDetail = $supShippingDetail['shipping_detail']['result'];
            $shippingDetailArr = array_values(array_filter($shippingDetail, fn($item) => $item['serviceType'] === $targetServiceType));
            
            if (!empty($shippingDetailArr)) {
                $findService = $shippingDetailArr[0];
            }
        }

        return $findService;
    }

    /**
     * Calculate cart grand total
     */
    public function calculateCartGrandTotal($cartData, $paymentTypeMapping, $validatedData)
    {
        $requestSup = $validatedData['suppliers'];

        return $cartData->map(function ($items) use ($paymentTypeMapping, $requestSup) {
            $pc = $items->first()->supplier;
            $userId = $pc->user_id;
            $supIdToFind = encryptParam($pc->user_id);
            $paymentType = $paymentTypeMapping[$userId];
            $zonePrefix = getClinicZone();
            $wareHouseType = $pc->wareHouse ? $pc->wareHouse->warehouse_type : 'own';
            
            $findService = 0;
            if ($wareHouseType == 'dpharma') {
                $findService = $this->requestServiceFind($requestSup, $supIdToFind);
            }
            
            $shippingFee = $findService ? (float) str_replace(',', '', $findService['charge']) : 0;

            $productAmt = $items->sum(function ($item) use ($paymentTypeMapping, $zonePrefix) {
                
                    $supPro = ProductRelation::with('productRelationPrice')
                        ->where('product_id', $item->product_id)
                        ->where('user_id', $item->supplier_id)
                        ->first();
                    
                    $priceBase = $supPro->productRelationPrice;
                    $singleProductPrice = $supPro->price_type == 'fixed' || $supPro->price_type == 'bonus'
                        ? $priceBase->{$zonePrefix . '_zone_price'}
                        : $priceBase->{$zonePrefix . '_' . $item->tier_number . '_base_price'} ?? 0;
                
                    return $singleProductPrice * $item->quantity;
            });
            
            return $productAmt + $shippingFee;
        })->sum();
    }

    /**
     * Calculate applied points for a supplier
     */
    public function calculateAppliedPoints($supplierAmount, $grandTotal, $totalAppliedPoints, $paymentType)
    {
        if ($totalAppliedPoints > 0) {
            $supplierPer = round(($supplierAmount / $grandTotal) * 100);
            $appliedPoints = ($totalAppliedPoints * $supplierPer / 100);
            
            if ($appliedPoints > round($supplierAmount)) {
                throw new \Exception(trans('api.clinic.cart.applied_points_error'));
            }
            
            return $appliedPoints;
        }
        
        return 0;
    }
} 