<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;

class ExtendedSessionService
{
    /**
     * Extend session lifetime for remember me users
     */
    public static function extendSessionForRememberMe(): void
    {
        if (Auth::check() && Auth::user()->remember_me) {
            // Extend session lifetime to 15 days for remember me users
            Config::set('session.lifetime', 21600); // 15 days in minutes
            
            // Don't regenerate session during auth processes to avoid breaking 2FA
            // The extended lifetime will take effect on next session save
        }
    }

    /**
     * Set standard session lifetime
     */
    public static function setStandardSessionLifetime(): void
    {
        // Set standard session lifetime to 8 hours for regular sessions
        Config::set('session.lifetime', 480); // 8 hours in minutes
    }

    /**
     * Calculate minutes for days
     */
    public static function daysToMinutes(int $days): int
    {
        return $days * 24 * 60;
    }

    /**
     * Check if user has remember me enabled
     */
    public static function hasRememberMe(): bool
    {
        // User must have remember_me set AND must have already passed 2FA at least once
        if (!Auth::check()) {
            return false;
        }

        $user = Auth::user();
        // dd($user, cache()->get('remember_me_first_time_login_'.Auth::id()));  
        // If user does not have remember_me, return false
        if (empty($user->remember_me)) {
            Log::info('hasRememberMe', [
                'user_id' => $user->id,
                'remember_me' => false,
                'reason' => 'remember_me not set'
            ]);
            return false;
        }
        $firstTimeLogin = cache()->get('remember_me_first_time_login_'.Auth::id()) ?? null;
        if (!empty($user->remember_me) && $firstTimeLogin == true) {
            Log::info('hasRememberMe', [
                'user_id' => $user->id,
                'remember_me' => true,
                'reason' => 'first time login'
            ]);
            return false;
        }

        // Check if user has already passed 2FA with remember_me before
        // We use a cache flag to track this
        $cacheKey = 'remember_me_2fa_passed_' . $user->id;
        $hasPassed2FA = cache()->get($cacheKey, false);

        Log::info('hasRememberMe', [
            'user_id' => $user->id,
            'remember_me' => true,
            'has_passed_2fa' => $hasPassed2FA
        ]);

        return $hasPassed2FA === true;
    }

    /**
     * Enable remember me for current user
     */
    public static function enableRememberMe(): void
    {
        if (Auth::check()) {
            Auth::user()->update(['remember_me' => true]);
            static::extendSessionForRememberMe();
        }
    }

    /**
     * Disable remember me for current user
     */
    public static function disableRememberMe(): void
    {
        if (Auth::check()) {
            Auth::user()->update(['remember_me' => false]);
            static::setStandardSessionLifetime();
            // Clear user-specific remember me cache
            cache()->forget('remember_me_' . Auth::id());
        }
    }


} 