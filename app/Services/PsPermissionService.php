<?php

namespace App\Services;

use App\Models\Role;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Config;

class PsPermissionService
{
    public function getPermissionsForRole(?int $roleId = null): array
    {
        $configPermissions = Config::get('data.role_permissions');
        if (empty($configPermissions)) {
            return ['permissions' => [], 'assigned' => []];
        }
        // dd($configPermissions);
        $panelId = Filament::getCurrentPanel()->getId();
        $role = $roleId ? Role::with('createdBy')->find($roleId) : null;
        $createdBy = $role?->createdBy?->id; // Using relationship access

        $roleType = $this->determineRoleType($panelId, $createdBy);
        $permissions = $configPermissions[$roleType] ?? [];
        $groupedPermissions = $this->groupPermissions($permissions);
        $assignedPermissions = $role ? $role->permissions->pluck('name')->toArray() : [];

        return [
            'permissions' => $groupedPermissions,
            'assigned' => $assignedPermissions
        ];
    }

    private function determineRoleType(string $panelId, ?int $createdBy): string
    {
        $roles = Config::get('data.roles');

        // PC panel checks createdBy to determine permissions
        if ($panelId === 'admin' && $createdBy) {
            return array_search('Pharmaceutical Company', $roles);
        }

        if ($panelId === 'admin' && !$createdBy) {
            return array_search('Pharmaceutical Company', $roles);
        }

        // Default to Super Admin permissions
        return array_search('Pharmaceutical Company', $roles);
    }

    private function groupPermissions(array $permissions): array
    {
        $grouped = [];

        foreach ($permissions as $module => $actions) {
            if (!is_array($actions)) continue;

            foreach ($actions as $action) {
                $key = strtolower($module) . '_' . strtolower($action);
                $grouped[$module][$key] = ucfirst($action);
            }
        }

        return $grouped;
    }
}
