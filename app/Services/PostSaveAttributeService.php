<?php

declare(strict_types=1);

namespace App\Services;

use Carbon\Carbon;
use App\Models\Product;
use App\Models\Attribute;
use App\Models\AttributeValue;
use App\Models\ProductVariant;
use App\Models\ProductRelation;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Illuminate\Support\Facades\Auth;
use App\Services\AttributeDataValidationService;
use App\Filament\Admin\Resources\ProductResource;
use App\Component\ProductVariant\Services\SummaryDataService;

class PostSaveAttributeService
{
    public $pcId;

    public function __construct($pcId)
    {
        $this->pcId = $pcId;
    }
    /**
     * Process and save attribute data after product is saved
     */
    public function processAttributeData(Product $product, array $formData): array
    {
        $attributeData = $formData['attribute_data'] ?? null;
        $hasVariants = $formData['has_variants'] ?? false;
        
        // Early return if variants not configured or no attribute data
        if (!$hasVariants || empty($attributeData)) {
            Log::info('Skipping variant processing', [
                'product_id' => $product->id,
                'has_variants' => $hasVariants,
                'attribute_data_present' => !empty($attributeData),
                'reason' => !$hasVariants ? 'No variants requested' : 'No attribute data provided'
            ]);
            return ['status' => 'skipped', 'message' => 'No variant data to process'];
        }
        
        // Additional validation for attribute data structure
        if (!is_array($attributeData) || empty($attributeData['attribute_ids'])) {
            Log::info('Skipping variant processing - invalid attribute data structure', [
                'product_id' => $product->id,
                'attribute_data_type' => gettype($attributeData),
                'has_attribute_ids' => isset($attributeData['attribute_ids'])
            ]);
            return ['status' => 'skipped', 'message' => 'Invalid attribute data structure'];
        }
        
        return DB::transaction(function () use ($product, $attributeData) {
            $results = [
                'status' => 'success',
                'product_id' => $product->id,
                'variants_created' => 0,
                'pricing_records' => 0,
                'stock_records' => 0,
                'errors' => []
            ];

            try {
                // Validate attribute data before processing
                // $validator = new AttributeDataValidationService();

                // $attributeData = $validator->validateAttributeData($product->id, $attributeData);
                
                // Clear existing variants if updating
                $this->clearExistingVariants($product->id);
                
                // Generate variant combinations
                $variants = SummaryDataService::generateVariantsSummary($attributeData);
                
                if ($variants->isEmpty()) {
                    throw new \Exception('No valid variant combinations could be generated from the provided data');
                }

                // Get product relation once for all variants
                $productRelation = $this->getProductRelation($product->id);
                if (!$productRelation) {
                    throw new \Exception('No product relation found for product ID: ' . $product->id);
                }

                // Prepare bulk data arrays
                $variantInsertData = [];
                $attributeInsertData = [];
                $pricingInsertData = [];
                $stockInsertData = [];
                $variantImages = [];

                // Prepare all data for bulk operations
                foreach ($variants as $variantSummary) {
                    // Prepare variant data
                    $variantInsertData[] = [
                        'product_id' => $product->id,
                        'sku' => $variantSummary->sku,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                // Bulk insert variants
                $insertedVariantIds = $this->bulkInsertVariants($variantInsertData);
                $results['variants_created'] = count($insertedVariantIds);

                // Prepare related data using the inserted variant IDs
                foreach ($variants as $index => $variantSummary) {
                    $variantId = $insertedVariantIds[$index];

                    // Prepare variant attributes data
                    $this->prepareVariantAttributesData($variantId, $variantSummary->attributes, $attributeInsertData);

                    // Prepare pricing data
                    if ($variantSummary->pricing) {
                        $this->prepareVariantPricingData($variantId, $variantSummary->pricing, $attributeData, $productRelation->id, $pricingInsertData);
                    }

                    // Prepare stock data
                    if ($variantSummary->quantity) {
                        $this->prepareVariantStockData($variantId, $variantSummary->quantity, $attributeData, $productRelation->id, $stockInsertData);
                    }

                    // Collect images for later processing
                    if ($variantSummary->images) {
                        $variantImages[$variantId] = $variantSummary->images;
                    }
                }

                // Bulk insert all related data
                if (!empty($attributeInsertData)) {
                    DB::table('product_variant_attributes')->insert($attributeInsertData);
                }

                if (!empty($pricingInsertData)) {
                    ProductRelationPrice::insert($pricingInsertData);
                    $results['pricing_records'] = count($pricingInsertData);
                }

                if (!empty($stockInsertData)) {
                    ProductRelationStock::insert($stockInsertData);
                    $results['stock_records'] = count($stockInsertData);
                }

                // Process images (these still need individual processing due to media library)
                $this->processVariantImages($variantImages);

                // Log activity
                $this->logActivity($product, $results);
                
                return $results;

            } catch (\Exception $e) {
                Log::error('Failed to save attribute data after product save', [
                    'product_id' => $product->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'attribute_data' => $attributeData
                ]);
                
                $results['status'] = 'error';
                $results['errors'][] = $e->getMessage();
                
                throw $e;
            }
        });
    }

    private function getProductRelation(int $productId): ?ProductRelation
    {
        return ProductRelation::where('product_id', $productId)
            ->where('user_id', $this->pcId)
            ->latest()
            ->first();
    }

    private function bulkInsertVariants(array $variantData): array
    {
        // Insert variants and get their IDs
        DB::table('product_variants')->insert($variantData);
        
        // Get the inserted variant IDs by querying for recently created variants
        // This approach works because we're in a transaction and the data is fresh
        $variants = ProductVariant::where('product_id', $variantData[0]['product_id'])
            ->where('created_at', '>=', now()->subMinutes(1))
            ->orderBy('id')
            ->pluck('id')
            ->toArray();

        return array_slice($variants, -count($variantData));
    }

    private function prepareVariantAttributesData(int $variantId, array $attributes, array &$attributeInsertData): void
    {
        foreach ($attributes as $attributeName => $attributeValue) {
            // Handle different attribute structures from VariantSummaryItem
            if (is_object($attributeValue) && isset($attributeValue->id)) {
                // Structure: ['Color' => AttributeValue object]
                $attribute = Attribute::where('name', $attributeName)->first();
                if (!$attribute) {
                    Log::warning('Attribute not found by name', ['attribute_name' => $attributeName]);
                    continue;
                }
                
                $attributeId = $attribute->id;
                $valueId = $attributeValue->id;
            } elseif (is_array($attributeValue) && isset($attributeValue['attribute_id']) && isset($attributeValue['value_id'])) {
                // Legacy structure: ['attribute_id' => 1, 'value_id' => 5]
                $attributeId = $attributeValue['attribute_id'];
                $valueId = $attributeValue['value_id'];
            } else {
                Log::warning('Invalid attribute data structure', ['data' => $attributeValue]);
                continue;
            }

            // Verify the attribute and value exist
            if (!Attribute::find($attributeId) || !AttributeValue::find($valueId)) {
                Log::warning('Referenced attribute or value does not exist', [
                    'attribute_id' => $attributeId,
                    'value_id' => $valueId
                ]);
                continue;
            }

            $attributeInsertData[] = [
                'product_variant_id' => $variantId,
                'attribute_id' => $attributeId,
                'attribute_value_id' => $valueId,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
    }

    private function prepareVariantPricingData(int $variantId, array $pricing, array $globalData, int $productRelationId, array &$pricingInsertData): void
    {
        $priceData = [
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $variantId,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Handle different price types based on the configuration step
        switch ($globalData['price_type_toggle'] ?? 'fixed') {
            case 'fixed':
                $priceData['west_zone_price'] = $pricing['west_price'] ?? $pricing['west_zone_price'] ?? null;
                $priceData['east_zone_price'] = $pricing['east_price'] ?? $pricing['east_zone_price'] ?? null;
                break;
                
            case 'bonus':
                $priceData['west_bonus_1_base_price'] = $pricing['west_price'] ?? $pricing['west_bonus_1_base_price'] ?? null;
                $priceData['east_bonus_1_base_price'] = $pricing['east_price'] ?? $pricing['east_bonus_1_base_price'] ?? null;
                $priceData['west_bonus_1_quantity'] = $pricing['west_bonus'] ?? $pricing['west_bonus_1_quantity'] ?? null;
                $priceData['east_bonus_1_quantity'] = $pricing['east_bonus'] ?? $pricing['east_bonus_1_quantity'] ?? null;
                $priceData['west_bonus_1_quantity_value'] = $pricing['west_bonus_1_quantity_value'] ?? null;
                $priceData['east_bonus_1_quantity_value'] = $pricing['east_bonus_1_quantity_value'] ?? null;
                break;
                
            case 'tier':
                // Handle tier pricing - the SummaryDataService returns different structure for tiers
                $westTiers = $pricing['west_tiers'] ?? [];
                $eastTiers = $pricing['east_tiers'] ?? [];
                
                $priceData['west_tier_1_base_price'] = $westTiers[0] ?? $pricing['west_tier_1_base_price'] ?? null;
                $priceData['east_tier_1_base_price'] = $eastTiers[0] ?? $pricing['east_tier_1_base_price'] ?? null;
                $priceData['west_tier_2_base_price'] = $westTiers[1] ?? $pricing['west_tier_2_base_price'] ?? null;
                $priceData['east_tier_2_base_price'] = $eastTiers[1] ?? $pricing['east_tier_2_base_price'] ?? null;
                $priceData['west_tier_3_base_price'] = $westTiers[2] ?? $pricing['west_tier_3_base_price'] ?? null;
                $priceData['east_tier_3_base_price'] = $eastTiers[2] ?? $pricing['east_tier_3_base_price'] ?? null;
                break;
        }

        $pricingInsertData[] = $priceData;
    }

    private function prepareVariantStockData(int $variantId, array $quantity, array $globalData, int $productRelationId, array &$stockInsertData): void
    {
        $stockData = [
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $variantId,
            'stock' => $quantity['stock'] ?? 0,
            'low_stock' => $quantity['low_stock'] ?? 0,
            'is_batch_wise_stock' => ($globalData['stock_type_toggle'] ?? 'batch') === 'batch',
            'total_stock' => $quantity['stock'] ?? 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        if (!empty($quantity['expiry_date'])) {
            $stockData['expiry_date'] = Carbon::parse($quantity['expiry_date'])->setTime(23, 59, 0);
        }

        $stockInsertData[] = $stockData;
    }

    private function processVariantImages(array $variantImages): void
    {
        foreach ($variantImages as $variantId => $images) {
            $variant = ProductVariant::find($variantId);
            if (!$variant) {
                continue;
            }

            foreach ($images as $image) {
                if ($image instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                    try {
                        $variant->addMedia($image->getRealPath())
                            ->usingFileName($image->getClientOriginalName())
                            ->toMediaCollection('variant-images');
                    } catch (\Exception $e) {
                        Log::error('Failed to save variant image', [
                            'variant_id' => $variant->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        }
    }

    private function clearExistingVariants(int $productId): void
    {
        $existingVariants = ProductVariant::where('product_id', $productId)->get();
        
        foreach ($existingVariants as $variant) {
            // Delete related data in proper order
            DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->delete();
                
            ProductRelationPrice::where('product_variant_id', $variant->id)->delete();
            ProductRelationStock::where('product_variant_id', $variant->id)->delete();
            
            // Clear media
            try {
                $variant->clearMediaCollection('variant-images');
            } catch (\Exception $e) {
                Log::warning('Could not clear media for variant', [
                    'variant_id' => $variant->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            // Delete the variant
            $variant->delete();
        }
    }

    private function logActivity(Product $product, array $results): void
    {
        $activityData = [
            'variants_created' => $results['variants_created'],
            'pricing_records' => $results['pricing_records'],
            'stock_records' => $results['stock_records'],
        ];

        activity()
            ->performedOn($product)
            ->causedBy(Auth::user())
            ->withProperties(['attributes' => $activityData])
            ->log("Product variants created for {$product->name}");
    }
} 