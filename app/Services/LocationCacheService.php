<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Nnjeim\World\Models\State;
use Nnjeim\World\Models\City;

class LocationCacheService
{
    /**
     * Cache duration in seconds (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get cached state by ID
     */
    public static function getCachedState($id)
    {
        if (empty($id)) {
            return null;
        }

        $cacheKey = "state_{$id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($id) {
            return State::find($id);
        });
    }

    /**
     * Get cached states by array of IDs
     */
    public static function getCachedStates(array $ids)
    {
        if (empty($ids)) {
            return collect();
        }

        sort($ids);
        $cacheKey = "states_" . implode('_', $ids);
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($ids) {
            return State::whereIn('id', $ids)->get();
        });
    }

    /**
     * Get cached city by ID
     */
    public static function getCachedCity($id)
    {
        if (empty($id)) {
            return null;
        }

        $cacheKey = "city_{$id}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($id) {
            return City::find($id);
        });
    }

    /**
     * Get cached cities by array of IDs
     */
    public static function getCachedCities(array $ids)
    {
        if (empty($ids)) {
            return collect();
        }

        sort($ids);
        $cacheKey = "cities_" . implode('_', $ids);
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($ids) {
            return City::whereIn('id', $ids)->get();
        });
    }

    /**
     * Get cached states by country ID (for dropdowns)
     */
    public static function getCachedStatesByCountry($countryId)
    {
        $cacheKey = "states_country_{$countryId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($countryId) {
            return State::where('country_id', $countryId)->pluck('name', 'id')->toArray();
        });
    }

    /**
     * Get cached cities by state ID (for dropdowns)
     */
    public static function getCachedCitiesByState($stateId)
    {
        $cacheKey = "cities_state_{$stateId}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($stateId) {
            return City::where('state_id', $stateId)->pluck('name', 'id')->toArray();
        });
    }

    /**
     * Clear all location caches
     */
    public static function clearAllCache()
    {
        // Clear common state caches
        Cache::forget('states_country_132'); // Malaysia
        
        // Clear individual state/city caches
        // Note: In production, consider using cache tags for more efficient clearing
        
        // You might want to implement a more sophisticated cache clearing strategy
        // For now, clear some common ones based on your application usage
        $commonStateIds = [2501]; // Add your commonly used state IDs
        $commonCityIds = [66602]; // Add your commonly used city IDs
        
        foreach ($commonStateIds as $id) {
            Cache::forget("state_{$id}");
            Cache::forget("cities_state_{$id}");
        }
        
        foreach ($commonCityIds as $id) {
            Cache::forget("city_{$id}");
        }
    }

    /**
     * Clear state-specific caches
     */
    public static function clearStateCache($stateId)
    {
        Cache::forget("state_{$stateId}");
        Cache::forget("cities_state_{$stateId}");
    }

    /**
     * Clear city-specific caches
     */
    public static function clearCityCache($cityId)
    {
        Cache::forget("city_{$cityId}");
    }

    /**
     * Clear caches related to a country
     */
    public static function clearCountryCache($countryId)
    {
        Cache::forget("states_country_{$countryId}");
    }

    /**
     * Get cached states for Malaysia with longer TTL for static data
     */
    public static function getCachedMalaysiaStates(): array
    {
        return Cache::remember('malaysia_states', 86400, function () { // 24 hours cache
            return \Nnjeim\World\Models\State::where('country_id', 132)
                ->select('id', 'name')
                ->pluck('name', 'id')
                ->toArray();
        });
    }

    /**
     * Invalidate location caches when data changes
     */
    public static function invalidateLocationCaches(): void
    {
        Cache::forget('malaysia_states');
        // Add other location cache keys as needed
    }
} 