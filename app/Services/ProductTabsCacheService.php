<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ProductTabsCacheService
{
    const CACHE_TTL = 300; // 5 minutes
    const CACHE_PREFIX = 'product_tabs_';

    /**
     * Get cached product counts for all tabs
     */
    public static function getTabCounts(): array
    {
        $cacheKey = self::CACHE_PREFIX . 'counts';
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() {
            // Use single query with conditional counting for better performance
            $counts = DB::table('products')
                ->selectRaw('
                    COUNT(*) as total,
                    SUM(CASE WHEN status = "pending" THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = "approved" THEN 1 ELSE 0 END) as approved,
                    SUM(CASE WHEN status = "rejected" THEN 1 ELSE 0 END) as rejected
                ')
                ->whereNull('deleted_at')
                ->first();
            
            return [
                'all' => $counts->approved ?? 0,
                'pending' => $counts->pending ?? 0,
                'rejected' => $counts->rejected ?? 0,
                'total' => $counts->total ?? 0,
            ];
        });
    }

    /**
     * Get cached count for specific tab
     */
    public static function getTabCount(string $tab): int
    {
        $counts = self::getTabCounts();
        return $counts[$tab] ?? 0;
    }

    /**
     * Clear tab counts cache
     */
    public static function clearTabCounts(): void
    {
        Cache::forget(self::CACHE_PREFIX . 'counts');
    }

    /**
     * Clear all tab caches
     */
    public static function clearAllCaches(): void
    {
        $pattern = self::CACHE_PREFIX . '*';
        $keys = Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }
}
