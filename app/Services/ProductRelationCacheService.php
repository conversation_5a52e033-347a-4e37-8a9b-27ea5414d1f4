<?php

namespace App\Services;

use App\Models\Product;
use App\Models\ProductRelation;
use App\Models\ProductRelationStock;
use App\Models\ProductRelationPrice;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ProductRelationCacheService
{
    const CACHE_TTL = 3600; // 1 hour
    const CACHE_PREFIX = 'product_relation_';

    /**
     * Get cached product relation data
     */
    public static function getProductRelation(int $productId, int $userId): ?ProductRelation
    {
        $cacheKey = self::CACHE_PREFIX . "product_{$productId}_user_{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($productId, $userId) {
            return ProductRelation::where(['product_id' => $productId, 'user_id' => $userId])->first();
        });
    }

    /**
     * Get cached product relation stock data
     */
    public static function getProductRelationStock(int $productRelationId): ?ProductRelationStock
    {
        $cacheKey = self::CACHE_PREFIX . "stock_{$productRelationId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($productRelationId) {
            return ProductRelationStock::where('product_relation_id', $productRelationId)->first();
        });
    }

    /**
     * Get cached product relation price data
     */
    public static function getProductRelationPrice(int $productRelationId): ?ProductRelationPrice
    {
        $cacheKey = self::CACHE_PREFIX . "price_{$productRelationId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($productRelationId) {
            return ProductRelationPrice::where('product_relation_id', $productRelationId)->first();
        });
    }

    /**
     * Get cached products not added by user
     */
    public static function getProductsNotAddedByUser(int $userId, int $limit = 50, bool $returnIdsOnly = false): array
    {
        $cacheKey = self::CACHE_PREFIX . "not_added_user_{$userId}_limit_{$limit}" . ($returnIdsOnly ? '_ids' : '');
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($userId, $limit, $returnIdsOnly) {
            // Get existing ProductRelation user_ids to avoid the expensive notAdded scope
            $existingProductIds = ProductRelation::where('user_id', $userId)
                ->pluck('product_id')
                ->toArray();
            
            $query = Product::query()
                ->where('status', 'approved')
                ->take($limit);
            
            // Use whereNotIn instead of notAdded scope to avoid subqueries
            if (!empty($existingProductIds)) {
                $query->whereNotIn('id', $existingProductIds);
            }
            
            if ($returnIdsOnly) {
                return $query->pluck('id')->toArray();
            }
            
            return $query->get(['id', 'name'])
                ->mapWithKeys(fn($product) => [$product->id => $product->name])
                ->toArray();
        });
    }

    /**
     * Get cached product relation count for user
     */
    public static function getProductRelationCount(int $userId): int
    {
        $cacheKey = self::CACHE_PREFIX . "count_user_{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($userId) {
            return ProductRelation::where('user_id', $userId)->count();
        });
    }

    /**
     * Check if product exists in user's relations (cached)
     */
    public static function hasProductRelation(int $productId, int $userId): bool
    {
        $cacheKey = self::CACHE_PREFIX . "exists_product_{$productId}_user_{$userId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($productId, $userId) {
            return ProductRelation::where(['product_id' => $productId, 'user_id' => $userId])->exists();
        });
    }

    /**
     * Clear cache for specific product relation
     */
    public static function clearProductRelationCache(int $productId, int $userId): void
    {
        $keys = [
            self::CACHE_PREFIX . "product_{$productId}_user_{$userId}",
            self::CACHE_PREFIX . "exists_product_{$productId}_user_{$userId}",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_9",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_50",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_1000",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_9_ids",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_50_ids",
            self::CACHE_PREFIX . "not_added_user_{$userId}_limit_1000_ids",
            self::CACHE_PREFIX . "count_user_{$userId}",
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Clear cache for product relation stock
     */
    public static function clearProductRelationStockCache(int $productRelationId): void
    {
        Cache::forget(self::CACHE_PREFIX . "stock_{$productRelationId}");
    }

    /**
     * Clear cache for product relation price
     */
    public static function clearProductRelationPriceCache(int $productRelationId): void
    {
        Cache::forget(self::CACHE_PREFIX . "price_{$productRelationId}");
    }

    /**
     * Clear all product relation caches for a user
     */
    public static function clearUserProductRelationCaches(int $userId): void
    {
        $pattern = self::CACHE_PREFIX . "*_user_{$userId}*";
        
        // Get all cache keys matching the pattern
        $keys = Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }

    /**
     * Clear all product relation caches
     */
    public static function clearAllProductRelationCaches(): void
    {
        $pattern = self::CACHE_PREFIX . "*";
        
        // Get all cache keys matching the pattern
        $keys = Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            Cache::getRedis()->del($keys);
        }
    }
} 