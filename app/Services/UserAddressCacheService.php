<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserAddress;
use App\Models\Role;
use Illuminate\Support\Facades\Cache;

class UserAddressCacheService
{
    /**
     * Cache duration in seconds (5 minutes)
     */
    const CACHE_DURATION = 300;

    /**
     * Get cached user addresses for users with specific role
     */
    public static function getCachedAddressesByRole(string $roleName)
    {
        $cacheKey = "user_addresses_role_{$roleName}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($roleName) {
            return UserAddress::whereHas('user', function ($query) use ($roleName) {
                $query->role($roleName)->whereNull('deleted_at');
            })->get();
        });
    }

    /**
     * Get cached states for users with specific role
     */
    public static function getCachedStatesByRole(string $roleName)
    {
        $cacheKey = "states_by_role_{$roleName}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($roleName) {
            return UserAddress::whereHas('user', function ($query) use ($roleName) {
                $query->role($roleName)->whereNull('deleted_at');
            })
            ->with('state')
            ->get()
            ->pluck('state.name', 'state.id')
            ->filter()
            ->unique()
            ->sort()
            ->toArray();
        });
    }

    /**
     * Get cached cities for users with specific role
     */
    public static function getCachedCitiesByRole(string $roleName)
    {
        $cacheKey = "cities_by_role_{$roleName}";
        
        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($roleName) {
            return UserAddress::whereHas('user', function ($query) use ($roleName) {
                $query->role($roleName)->whereNull('deleted_at');
            })
            ->with('city')
            ->get()
            ->pluck('city.name', 'city.id')
            ->filter()
            ->unique()
            ->sort()
            ->toArray();
        });
    }

    /**
     * Get cached pharmaceutical company addresses
     */
    public static function getCachedPharmaceuticalCompanyAddresses()
    {
        return self::getCachedAddressesByRole('Pharmaceutical Company');
    }

    /**
     * Get cached states for pharmaceutical companies
     */
    public static function getCachedPharmaceuticalCompanyStates()
    {
        return self::getCachedStatesByRole('Pharmaceutical Company');
    }

    /**
     * Get cached cities for pharmaceutical companies
     */
    public static function getCachedPharmaceuticalCompanyCities()
    {
        return self::getCachedCitiesByRole('Pharmaceutical Company');
    }

    /**
     * Clear all user address caches
     */
    public static function clearAllCache()
    {
        // Clear role-based caches
        Cache::forget('user_addresses_role_Pharmaceutical Company');
        Cache::forget('user_addresses_role_Clinic');
        Cache::forget('user_addresses_role_Super Admin');
        
        Cache::forget('states_by_role_Pharmaceutical Company');
        Cache::forget('states_by_role_Clinic');
        Cache::forget('states_by_role_Super Admin');
        
        Cache::forget('cities_by_role_Pharmaceutical Company');
        Cache::forget('cities_by_role_Clinic');
        Cache::forget('cities_by_role_Super Admin');
    }

    /**
     * Clear caches for specific role
     */
    public static function clearRoleCache(string $roleName)
    {
        Cache::forget("user_addresses_role_{$roleName}");
        Cache::forget("states_by_role_{$roleName}");
        Cache::forget("cities_by_role_{$roleName}");
    }

    /**
     * Clear caches when user address is created/updated/deleted
     */
    public static function clearUserAddressCache()
    {
        self::clearAllCache();
    }
} 