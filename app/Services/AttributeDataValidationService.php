<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Product;
use App\Models\Attribute;
use App\Models\AttributeValue;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;

class AttributeDataValidationService
{
    /**
     * Validate attribute data before persistence
     */
    public function validateAttributeData(int $productId, array $attributeData): array
    {
        // dd($productId, $attributeData);
        $errors = [];
        
        // Validate product exists
        if (!Product::find($productId)) {
            $errors[] = "Product with ID {$productId} not found";
        }
               // Validate attribute structure if variants are configured
        if (!empty($attributeData['attribute_ids'])) {
            $this->validateAttributeStructure($attributeData, $errors);
        }
        
        // Validate pricing configuration
        if (!empty($attributeData['price_type_toggle'])) {
            $this->validatePricingConfiguration($attributeData, $errors);
        }
        
        // Validate stock configuration
        if (!empty($attributeData['stock_type_toggle'])) {
            $this->validateStockConfiguration($attributeData, $errors);
        }
        
        if (!empty($errors)) {
            Log::warning('Attribute data validation failed', [
                'product_id' => $productId,
                'errors' => $errors,
                'attribute_data' => $attributeData
            ]);
            
            throw ValidationException::withMessages(['attribute_data' => $errors]);
        }
        // dd($attributeData);
        return $attributeData;
    }

    /**
     * Validate attribute structure and relationships
     */
    private function validateAttributeStructure(array $attributeData, array &$errors): void
    {
        foreach ($attributeData['attribute_ids'] as $index => $attributeId) {
            // Validate attribute exists
            $attribute = Attribute::find($attributeId);
            if (!$attribute) {
                $errors[] = "Attribute with ID {$attributeId} not found";
                continue;
            }
            
            // Validate attribute values
            $attributeValueKey = "attribute_values_" . ($index + 1);
            if (!empty($attributeData[$attributeValueKey])) {
                foreach ($attributeData[$attributeValueKey] as $valueId) {
                    $attributeValue = AttributeValue::find($valueId);
                    if (!$attributeValue) {
                        $errors[] = "Attribute value with ID {$valueId} not found";
                        continue;
                    }
                    
                    // Validate attribute value belongs to the correct attribute
                    if ($attributeValue->attribute_id !== $attributeId) {
                        $errors[] = "Attribute value {$valueId} does not belong to attribute {$attributeId}";
                    }
                }
            } else {
                $errors[] = "No values selected for attribute: {$attribute->name}";
            }
        }
    }

    /**
     * Validate pricing configuration
     */
    private function validatePricingConfiguration(array $attributeData, array &$errors): void
    {
        $priceType = $attributeData['price_type_toggle'];
        
        switch ($priceType) {
            case 'fixed':
                $this->validateFixedPricing($attributeData, $errors);
                break;
            case 'bonus':
                $this->validateBonusPricing($attributeData, $errors);
                break;
            case 'tier':
                $this->validateTierPricing($attributeData, $errors);
                break;
            default:
                $errors[] = "Invalid price type: {$priceType}";
        }
    }

    /**
     * Validate fixed pricing configuration
     */
    private function validateFixedPricing(array $attributeData, array &$errors): void
    {
        // Fixed pricing validation can be added here if needed
        // For now, basic validation is handled by form rules
    }

    /**
     * Validate bonus pricing configuration
     */
    private function validateBonusPricing(array $attributeData, array &$errors): void
    {
        // Bonus pricing validation can be added here if needed
        // For now, basic validation is handled by form rules
    }

    /**
     * Validate tier pricing configuration
     */
    private function validateTierPricing(array $attributeData, array &$errors): void
    {
        // Tier pricing validation can be added here if needed
        // For now, basic validation is handled by form rules
    }

    /**
     * Validate stock configuration
     */
    private function validateStockConfiguration(array $attributeData, array &$errors): void
    {
        $stockType = $attributeData['stock_type_toggle'];
        
        if (!in_array($stockType, ['stock', 'batch'])) {
            $errors[] = "Invalid stock type: {$stockType}";
        }
    }

    /**
     * Validate assignment types and ensure consistency
     */
    public function validateAssignmentTypes(array $attributeData): array
    {
        $warnings = [];
        
        // Check if pricing assignment type is consistent with attribute configuration
        if (!empty($attributeData['price_assignment_type']) && $attributeData['price_assignment_type'] === 'attribute') {
            if (empty($attributeData['attribute_ids'])) {
                $warnings[] = "Price assignment type is set to 'attribute' but no attributes are selected";
            }
        }
        
        // Check if quantity assignment type is consistent with attribute configuration
        if (!empty($attributeData['quantity_assignment_type']) && $attributeData['quantity_assignment_type'] === 'attribute') {
            if (empty($attributeData['attribute_ids'])) {
                $warnings[] = "Quantity assignment type is set to 'attribute' but no attributes are selected";
            }
        }
        
        if (!empty($warnings)) {
            Log::info('Attribute assignment validation warnings', [
                'warnings' => $warnings,
                'attribute_data' => $attributeData
            ]);
        }
        
        return $warnings;
    }

    /**
     * Validate variant data before creating variants
     */
    public function validateVariantData(array $variantData): bool
    {
        if (empty($variantData)) {
            return false;
        }

        // Check if variant has valid attributes
        if (empty($variantData['attributes'])) {
            Log::warning('Variant has no attributes', ['variant_data' => $variantData]);
            return false;
        }

        // Validate each attribute in the variant
        foreach ($variantData['attributes'] as $attribute) {
            if (!isset($attribute['attribute_id']) || !isset($attribute['value_id'])) {
                Log::warning('Variant attribute missing required fields', ['attribute' => $attribute]);
                return false;
            }
        }

        return true;
    }

    /**
     * Sanitize and prepare attribute data for storage
     */
    public function sanitizeAttributeData(array $attributeData): array
    {
        // Remove any empty or null values
        $sanitized = array_filter($attributeData, function ($value) {
            return $value !== null && $value !== '';
        });
        
        // Ensure numeric values are properly typed
        foreach (['attribute_ids', 'price_type_toggle', 'stock_type_toggle'] as $key) {
            if (isset($sanitized[$key]) && is_string($sanitized[$key])) {
                if (is_numeric($sanitized[$key])) {
                    $sanitized[$key] = (int) $sanitized[$key];
                }
            }
        }
        
        return $sanitized;
    }
} 