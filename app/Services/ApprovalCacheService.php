<?php

namespace App\Services;

use App\Models\Approval;
use App\OnboardingStep;
use Illuminate\Support\Facades\Cache;

class ApprovalCacheService
{
    private const CACHE_TTL = 3600; // 1 hour
    private const CACHE_PREFIX = 'approval_data';

    /**
     * Get all approval data for a user at once to avoid multiple queries
     */
    public static function getUserApprovalData(int $userId, string $userType = 'pc'): array
    {
        $cacheKey = self::CACHE_PREFIX . ":{$userType}:{$userId}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($userId, $userType) {
            // Get all approval data in one query
            $allApprovals = Approval::where('approvalable_id', $userId)
                ->where('approvalable_type', 'App\Models\User')
                ->where('user_type', $userType)
                ->where('approved_by', null)
                ->where('approved_at', null)
                ->where('rejected_at', null)
                ->where('rejected_by', null)
                ->orderBy('created_at', 'desc')
                ->select('steps', 'new_data', 'original_data', 'id', 'created_at')
                ->get()
                ->groupBy('steps');

            // Get existence check data in one query
            $pendingSteps = Approval::where('approvalable_id', $userId)
                ->where('approvalable_type', 'App\Models\User')
                ->where('user_type', $userType)
                ->where('approved_by', null)
                ->where('approved_at', null)
                ->where('rejected_at', null)
                ->where('rejected_by', null)
                ->distinct()
                ->pluck('steps')
                ->toArray();

            $processedData = [];
            foreach (OnboardingStep::cases() as $step) {
                $stepValue = $step->value;
                $stepApprovals = $allApprovals->get($stepValue);
                
                $processedData[$stepValue] = [
                    'approval' => $stepApprovals ? $stepApprovals->first() : null,
                    'has_pending' => in_array($stepValue, $pendingSteps),
                    'changes' => $stepApprovals ? self::processApprovalChanges($stepApprovals->first()) : []
                ];
            }

            return $processedData;
        });
    }

    /**
     * Get specific step approval data
     */
    public static function getStepApproval(int $userId, int $step, string $userType = 'pc'): ?array
    {
        $allData = self::getUserApprovalData($userId, $userType);
        return $allData[$step] ?? null;
    }

    /**
     * Check if user has pending approval for specific step
     */
    public static function hasPendingApproval(int $userId, int $step, string $userType = 'pc'): bool
    {
        $stepData = self::getStepApproval($userId, $step, $userType);
        return $stepData['has_pending'] ?? false;
    }

    /**
     * Get pending approval record for specific step
     */
    public static function getPendingApproval(int $userId, int $step, string $userType = 'pc'): ?Approval
    {
        $stepData = self::getStepApproval($userId, $step, $userType);
        return $stepData['approval'] ?? null;
    }

    /**
     * Process approval changes
     */
    private static function processApprovalChanges(?Approval $approval): array
    {
        if (!$approval) {
            return [];
        }

        $newData = is_string($approval->new_data) 
            ? json_decode($approval->new_data, true) 
            : $approval->new_data;
        
        $originalData = is_string($approval->original_data) 
            ? json_decode($approval->original_data, true) 
            : $approval->original_data;

        if (!$newData) {
            return [];
        }

        $changes = [];
        foreach ($newData as $key => $value) {
            $changes[$key][] = [
                'new_value' => $value,
                'original_value' => $originalData[$key] ?? 'N/A',
            ];
        }

        return $changes;
    }

    /**
     * Invalidate cache when approval data changes
     */
    public static function invalidateUserCache(int $userId, string $userType = 'pc'): void
    {
        $cacheKey = self::CACHE_PREFIX . ":{$userType}:{$userId}";
        Cache::forget($cacheKey);
    }

    /**
     * Invalidate all approval caches (use sparingly)
     */
    public static function invalidateAllCaches(): void
    {
        Cache::flush(); // Only if you have dedicated cache store for this
        // Or use pattern-based deletion if supported by your cache driver
    }
} 