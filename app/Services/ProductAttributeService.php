<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Product;
use App\Models\Attribute;
use Illuminate\Support\Str;
use App\Models\AttributeValue;
use App\Models\ProductVariant;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Illuminate\Support\Facades\Cache;
use App\Models\ProductVariantAttribute;

class ProductAttributeService
{
        /**
     * Process attribute data from AttributeSection form and store to database
     */
    public function processAttributeData(array $formData, Product $product, int $productRelationId): void
    {
        // Extract attribute data from stored JSON or form
        $attributeData = $this->extractAttributeData($formData);
        $priceType = $formData['price_type_toggle'] ?? 'fixed';
        
        // Handle fixed pricing (non-variant pricing)
        if ($priceType === 'fixed') {
            $this->processFixedPricing($formData, $product, $productRelationId);
        }
        
        $attributeIds = $attributeData['attribute_ids'] ?? [];
        $priceAssignmentType = $attributeData['price_assignment_type'] ?? null;
        $hasVariants = !empty($attributeIds) && $priceAssignmentType === 'attribute';

        if (!$hasVariants) {
            Log::info('No variant generation needed for product', [
                'product_id' => $product->id,
                'price_assignment_type' => $priceAssignmentType
            ]);
            return; // No variant generation needed
        }

        Log::info('Processing attributes for product variants', [
            'product_id' => $product->id,
            'attribute_ids' => $attributeIds,
            'product_relation_id' => $productRelationId
        ]);

        DB::transaction(function () use ($attributeData, $product, $productRelationId, $attributeIds) {
            // Generate product variants from attribute combinations
            $variants = $this->generateProductVariants($attributeData, $product, $attributeIds);

            Log::info('Generated variants', [
                'product_id' => $product->id,
                'variants_count' => count($variants)
            ]);

            // Store variants and their relationships
            foreach ($variants as $variantData) {
                $this->storeProductVariant($variantData, $product, $productRelationId, $attributeData);
            }

            // Clear caches as per user rules
            $this->clearAttributeCaches();
            
            // Clear cache for this specific product
            $this->clearProductVariantsCache($product->id);
        });
    }

    /**
     * Generate product variants from selected attributes
     */
    private function generateProductVariants(array $attributeData, Product $product, array $attributeIds): array
    {
        $attributeValuesByAttribute = [];
        
        // Collect selected attribute values from form fields
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $attributeData["attribute_values_{$index}"] ?? [];
                
                if (!empty($selectedValues)) {
                    $attributeValuesByAttribute[$attributeId] = $selectedValues;
                }
            }
        }

        if (empty($attributeValuesByAttribute)) {
            return [];
        }

        // Generate all possible combinations
        return $this->generateCombinations($attributeValuesByAttribute, $product);
    }

    /**
     * Generate all possible combinations of attribute values
     */
    private function generateCombinations(array $attributeData, Product $product): array
    {
        if (empty($attributeData)) {
            return [];
        }

        $combinations = [[]];
        
        foreach ($attributeData as $attributeId => $valueIds) {
            $newCombinations = [];
            foreach ($combinations as $combination) {
                foreach ($valueIds as $valueId) {
                    $newCombination = $combination;
                    $newCombination[] = [
                        'attribute_id' => $attributeId,
                        'attribute_value_id' => $valueId
                    ];
                    $newCombinations[] = $newCombination;
                }
            }
            $combinations = $newCombinations;
        }

        // Generate SKUs for each combination
        $variants = [];
        foreach ($combinations as $combination) {
            $sku = $this->generateVariantSku($product, $combination);
            $variants[] = [
                'sku' => $sku,
                'attributes' => $combination
            ];
        }

        return $variants;
    }

    /**
     * Generate unique SKU for product variant
     */
    private function generateVariantSku(Product $product, array $combination): string
    {
        $skuParts = [Str::upper(Str::substr($product->name, 0, 4))];
        
        foreach ($combination as $attr) {
            $attributeValue = AttributeValue::find($attr['attribute_value_id']);
            if ($attributeValue) {
                $skuParts[] = Str::upper(Str::substr($attributeValue->name, 0, 3));
            }
        }
        
        return implode('-', $skuParts) . '-' . rand(100000, 999999);
    }

    /**
     * Store individual product variant with all its relationships
     */
    private function storeProductVariant(array $variantData, Product $product, int $productRelationId, array $attributeData): void
    {
        // Create product variant
        $variant = ProductVariant::create([
            'product_id' => $product->id,
            'sku' => $variantData['sku']
        ]);

        Log::info('Created product variant', [
            'variant_id' => $variant->id,
            'sku' => $variant->sku,
            'product_id' => $product->id
        ]);

        // Link variant to attributes via pivot table
        foreach ($variantData['attributes'] as $attr) {
            DB::table('product_variant_attributes')->insert([
                'product_variant_id' => $variant->id,
                'attribute_id' => $attr['attribute_id'],
                'attribute_value_id' => $attr['attribute_value_id'],
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Also create direct product-attribute relationship for easier querying
            ProductAttribute::firstOrCreate([
                'product_id' => $product->id,
                'attribute_id' => $attr['attribute_id'],
                'attribute_value_id' => $attr['attribute_value_id']
            ]);
        }

        // Store pricing data
        $this->storePricingData($variant, $productRelationId, $attributeData, $variantData['attributes']);

        // Store stock data
        $this->storeStockData($variant, $productRelationId, $attributeData, $variantData['attributes']);

        // Store images if any
        $this->storeVariantImages($variant, $attributeData, $variantData['attributes']);
    }

    /**
     * Store pricing data for variant based on assignment type
     */
    private function storePricingData(ProductVariant $variant, int $productRelationId, array $attributeData, array $attributes): void
    {
        $priceData = [
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $variant->id,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Handle price assignment based on type
        $priceAssignmentType = $attributeData['price_assignment_type'] ?? 'single';
        
        if ($priceAssignmentType === 'attribute' && isset($attributeData['attribute_for_price'])) {
            $priceAttributeId = $attributeData['attribute_for_price'];
            $attributeValuePrices = $attributeData['attribute_value_price'] ?? [];
            
            // Find matching attribute value for this variant
            foreach ($attributes as $attr) {
                if ($attr['attribute_id'] == $priceAttributeId) {
                    $valueId = $attr['attribute_value_id'];
                    if (isset($attributeValuePrices[$valueId])) {
                        $price = (float) $attributeValuePrices[$valueId];
                        $priceData['east_zone_price'] = $price;
                        $priceData['west_zone_price'] = $price;
                        
                        Log::info('Set variant price from attribute', [
                            'variant_id' => $variant->id,
                            'attribute_value_id' => $valueId,
                            'price' => $price
                        ]);
                    }
                    break;
                }
            }
        } elseif ($priceAssignmentType === 'single') {
            // Apply single price to all variants
            $singlePrice = $attributeData['single_price'] ?? null;
            if ($singlePrice) {
                $priceData['east_zone_price'] = (float) $singlePrice;
                $priceData['west_zone_price'] = (float) $singlePrice;
            }
        }

        // Copy other pricing data from the main product relation
        $existingPrices = DB::table('product_relation_prices')
                            ->where('product_relation_id', $productRelationId)
                            ->whereNull('product_variant_id')
                            ->first();

        if ($existingPrices) {
            $priceData = array_merge($priceData, [
                'east_tier_1_base_price' => $existingPrices->east_tier_1_base_price,
                'west_tier_1_base_price' => $existingPrices->west_tier_1_base_price,
                'east_tier_2_base_price' => $existingPrices->east_tier_2_base_price,
                'west_tier_2_base_price' => $existingPrices->west_tier_2_base_price,
                'east_tier_3_base_price' => $existingPrices->east_tier_3_base_price,
                'west_tier_3_base_price' => $existingPrices->west_tier_3_base_price,
                // ... copy other pricing fields as needed
            ]);
        }

        DB::table('product_relation_prices')->insert($priceData);
    }

    /**
     * Store stock data for variant based on assignment type
     */
    private function storeStockData(ProductVariant $variant, int $productRelationId, array $attributeData, array $attributes): void
    {
        $stockData = [
            'product_relation_id' => $productRelationId,
            'product_variant_id' => $variant->id,
            'created_at' => now(),
            'updated_at' => now()
        ];

        // Handle quantity assignment based on type
        $quantityAssignmentType = $attributeData['quantity_assignment_type'] ?? 'single';
        
        if ($quantityAssignmentType === 'attribute') {
            $combinationKey = implode('_', array_column($attributes, 'attribute_value_id'));
            $combinationStock = $attributeData['attribute_combination_stock'][$combinationKey] ?? null;
            if ($combinationStock) {
                $stockData['stock'] = (int) $combinationStock;
                $stockData['total_stock'] = (int) $combinationStock;
            }
        } elseif ($quantityAssignmentType === 'sku') {
            foreach ($attributes as $attr) {
                $attributeStock = $attributeData['attribute_stock'][$attr['attribute_id']] ?? null;
                if ($attributeStock) {
                    $stockData['stock'] = (int) $attributeStock;
                    $stockData['total_stock'] = (int) $attributeStock;
                    break; // Use the first matching attribute's stock
                }
            }
        } elseif ($quantityAssignmentType === 'single') {
            // Apply single stock to all variants
            $singleStock = $attributeData['single_stock'] ?? null;
            if ($singleStock) {
                $stockData['stock'] = (int) $singleStock;
                $stockData['total_stock'] = (int) $singleStock;
            }
        }

        // Copy other stock settings from main product relation
        $existingStock = DB::table('product_relation_stocks')
                           ->where('product_relation_id', $productRelationId)
                           ->whereNull('product_variant_id')
                           ->first();

        if ($existingStock) {
            $stockData = array_merge($stockData, [
                'is_batch_wise_stock' => $existingStock->is_batch_wise_stock,
                'low_stock' => $existingStock->low_stock,
                'wholesale_pack_size' => $existingStock->wholesale_pack_size,
                'stock_type' => $existingStock->stock_type,
            ]);
        }

        DB::table('product_relation_stocks')->insert($stockData);
    }

    /**
     * Store images for variants based on assignment type
     */
    private function storeVariantImages(ProductVariant $variant, array $attributeData, array $attributes): void
    {
        $imageAssignmentType = $attributeData['image_assignment_type'] ?? 'single';
        
        if ($imageAssignmentType === 'attribute') {
            $combinationKey = implode('_', array_column($attributes, 'attribute_value_id'));
            $combinationImages = $attributeData['attribute_combination_images'][$combinationKey] ?? [];
            foreach ($combinationImages as $fileUpload) {
                $this->attachMediaToVariant($variant, $fileUpload);
            }
        } elseif ($imageAssignmentType === 'sku') {
            foreach ($attributes as $attr) {
                $attributeImages = $attributeData['attribute_images'][$attr['attribute_id']] ?? [];
                foreach ($attributeImages as $fileUpload) {
                    $this->attachMediaToVariant($variant, $fileUpload);
                }
                if (!empty($attributeImages)) break; // Attach images from the first matching attribute
            }
        } elseif ($imageAssignmentType === 'single') {
            $singleImages = $attributeData['single_images'] ?? [];
            foreach ($singleImages as $fileUpload) {
                $this->attachMediaToVariant($variant, $fileUpload);
            }
        }
    }

    private function attachMediaToVariant(ProductVariant $variant, $fileUpload): void
    {
        // Handle Livewire temporary upload if applicable
        if (method_exists($fileUpload, 'getRealPath') && method_exists($fileUpload, 'getClientOriginalName')) {
            $variant
                ->addMedia($fileUpload->getRealPath())
                ->usingFileName($fileUpload->getClientOriginalName())
                ->toMediaCollection('attribute-images');
        }
        // Handle Filament Spatie media tokens
        if (is_string($fileUpload)) {
            $variant
                ->addMediaFromTokens([$fileUpload])
                ->toMediaCollection('attribute-images');
        }
    }

    /**
     * Clear attribute-related caches following user rules
     */
    private function clearAttributeCaches(): void
    {
        // Clear specific cache keys
        Cache::forget('attributes_all');
        Cache::forget('attribute_values_all');
        Cache::forget('product_attributes_cache');
        Cache::forget('product_variants_cache');
        
        // Clear unit and form caches
        Cache::forget('unit_options');
        Cache::forget('dosage_form_options');
        
        // Clear product-specific caches (pattern-based clearing for compatibility)
        $this->clearProductCaches();
        
        Log::info('Cleared attribute-related caches');
    }

    /**
     * Clear product-related caches (compatible with all cache drivers)
     */
    private function clearProductCaches(): void
    {
        // If using Redis, we can use tags
        if (config('cache.default') === 'redis') {
            try {
                Cache::tags(['attributes', 'products'])->flush();
                return;
            } catch (\Exception $e) {
                Log::warning('Cache tagging failed, falling back to individual clearing', [
                    'error' => $e->getMessage()
                ]);
            }
        }
        
        // For other drivers, clear specific patterns or use general flush
        // Clear known product cache patterns
        $cacheKeys = [
            'product_*_variants',
            'product_attributes_*',
            'product_variants_*',
            'attributes_with_values_*',
        ];
        
        foreach ($cacheKeys as $pattern) {
            // For file/database drivers, we can't easily clear by pattern
            // so we'll rely on specific key clearing above
        }
        
        Log::info('Product caches cleared using fallback method');
    }

    /**
     * Get all variants for a product with their attribute combinations
     */
    public function getProductVariants(int $productId): array
    {
        $cacheKey = "product_{$productId}_variants";
        
        return Cache::remember($cacheKey, 300, function () use ($productId) {
            return ProductVariant::where('product_id', $productId)
                ->with(['attributeValues.attribute'])
                ->get()
                ->map(function ($variant) {
                    return [
                        'id' => $variant->id,
                        'sku' => $variant->sku,
                        'display_name' => $variant->display_name,
                        'attributes' => $variant->attribute_combination
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Clear cache for specific product variants
     */
    public function clearProductVariantsCache(int $productId): void
    {
        Cache::forget("product_{$productId}_variants");
        Log::info('Cleared cache for product variants', ['product_id' => $productId]);
    }

    /**
     * Delete all variants for a product (useful for updates)
     */
    public function deleteProductVariants(int $productId): void
    {
        DB::transaction(function () use ($productId) {
            $variantIds = ProductVariant::where('product_id', $productId)->pluck('id');
            
            if ($variantIds->isNotEmpty()) {
                // Delete variant attributes
                DB::table('product_variant_attributes')
                  ->whereIn('product_variant_id', $variantIds)
                  ->delete();
                  
                // Delete variant prices
                DB::table('product_relation_prices')
                  ->whereIn('product_variant_id', $variantIds)
                  ->delete();
                  
                // Delete variant stocks  
                DB::table('product_relation_stocks')
                  ->whereIn('product_variant_id', $variantIds)
                  ->delete();
                  
                // Delete variants
                ProductVariant::whereIn('id', $variantIds)->delete();
                
                // Clear product attributes
                ProductAttribute::where('product_id', $productId)->delete();
            }
            
            $this->clearAttributeCaches();
        });
    }

    /**
     * Extract attribute data from form or stored JSON
     */
    private function extractAttributeData(array $formData): array
    {
        // Check if we have stored attribute data from slideOver
        if (isset($formData['attribute_data']) && !empty($formData['attribute_data'])) {
            $storedData = $formData['attribute_data'];
            
            // If it's a string, decode it as JSON
            if (is_string($storedData)) {
                $decodedData = json_decode($storedData, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedData)) {
                    Log::info('Using stored attribute data from slideOver', [
                        'stored_keys' => array_keys($decodedData)
                    ]);
                    return $decodedData;
                }
            }
            
            // If it's already an array, use it directly
            if (is_array($storedData)) {
                Log::info('Using stored attribute data (array format)', [
                    'stored_keys' => array_keys($storedData)
                ]);
                return $storedData;
            }
        }
        
        // Fallback to reading directly from form fields (for backward compatibility)
        Log::info('No stored attribute data found, using direct form fields');
        return $formData;
    }

    /**
     * Process fixed pricing data (when no variants are created)
     */
    private function processFixedPricing(array $formData, Product $product, int $productRelationId): void
    {
        // Prepare ProductVariant and ProductVariantAttribute for attribute-based pricing (no combinations, just per attribute value)
        $attributeData = $formData['attribute_data'] ?? [];
        $attributeIds = $attributeData['attribute_ids'] ?? [];
        $priceAssignmentType = $attributeData['price_assignment_type'] ?? null;
        $attributeCombinationPrices = $attributeData['attribute_combination_price'] ?? [];
        $attributePrices = $attributeData['attribute_price'] ?? [];
        $singlePrice = $attributeData['single_price'] ?? [];

        // Only process if price_assignment_type is 'attribute' and attribute_for_price is set
        if (
            $priceAssignmentType === 'single' && !empty($singlePrice)
        ) {
            // Handle single pricing - apply to all without creating variants
            $this->processSinglePricing($attributeData, $product, $productRelationId, $singlePrice);
        } elseif (
            $priceAssignmentType === 'sku' && !empty($attributePrices)
        ) {
            // Handle SKU pricing - create variants for individual attribute values
            $this->processSKUPricing($attributeData, $product, $productRelationId, $attributePrices);
        } elseif (
            $priceAssignmentType === 'attribute' && !empty($attributeCombinationPrices)
        ) {
            // Handle combination pricing - create variants for all attribute combinations
            $this->processCombinationPricing($attributeData, $product, $productRelationId, $attributeCombinationPrices);
        }
    }
    
    /**
     * Process single pricing data (applies to all without variants)
     */
    private function processSinglePricing(array $attributeData, Product $product, int $productRelationId, array $singlePrice): void
    {
        // For single pricing, just store one price record that applies to all
        $relationPriceData = [
            'product_relation_id' => $productRelationId,
            'product_variant_id' => null, // No variant for single pricing
            'product_attribute_id' => null, // No specific attribute for single pricing
            'east_zone_price' => $singlePrice['east'] ?? null,
            'west_zone_price' => $singlePrice['west'] ?? null,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        // Handle different price types
        if (isset($singlePrice['east']['base_price'])) {
            // Bonus pricing structure
            $relationPriceData['east_zone_price'] = $singlePrice['east']['base_price'];
            $relationPriceData['west_zone_price'] = $singlePrice['west']['base_price'];
            
            // Add bonus fields
            $relationPriceData['east_bonus_1_quantity'] = $singlePrice['east']['bonus_1_qty'] ?? null;
            $relationPriceData['east_bonus_1_quantity_value'] = $singlePrice['east']['bonus_1_free'] ?? null;
            $relationPriceData['east_bonus_2_quantity'] = $singlePrice['east']['bonus_2_qty'] ?? null;
            $relationPriceData['east_bonus_2_quantity_value'] = $singlePrice['east']['bonus_2_free'] ?? null;
            $relationPriceData['east_bonus_3_quantity'] = $singlePrice['east']['bonus_3_qty'] ?? null;
            $relationPriceData['east_bonus_3_quantity_value'] = $singlePrice['east']['bonus_3_free'] ?? null;
            
            $relationPriceData['west_bonus_1_quantity'] = $singlePrice['west']['bonus_1_qty'] ?? null;
            $relationPriceData['west_bonus_1_quantity_value'] = $singlePrice['west']['bonus_1_free'] ?? null;
            $relationPriceData['west_bonus_2_quantity'] = $singlePrice['west']['bonus_2_qty'] ?? null;
            $relationPriceData['west_bonus_2_quantity_value'] = $singlePrice['west']['bonus_2_free'] ?? null;
            $relationPriceData['west_bonus_3_quantity'] = $singlePrice['west']['bonus_3_qty'] ?? null;
            $relationPriceData['west_bonus_3_quantity_value'] = $singlePrice['west']['bonus_3_free'] ?? null;
        } elseif (isset($singlePrice['east_tiers'])) {
            // Tier pricing structure
            $eastTiers = $singlePrice['east_tiers'] ?? [];
            $westTiers = $singlePrice['west_tiers'] ?? [];
            
            // Process tier data (up to 3 tiers)
            for ($i = 0; $i < 3; $i++) {
                $tierNum = $i + 1;
                if (isset($eastTiers[$i])) {
                    $relationPriceData["east_tier_{$tierNum}_min_quantity"] = $eastTiers[$i]['min_quantity'] ?? null;
                    $relationPriceData["east_tier_{$tierNum}_max_quantity"] = $eastTiers[$i]['max_quantity'] ?? null;
                    $relationPriceData["east_tier_{$tierNum}_base_price"] = $eastTiers[$i]['price'] ?? null;
                }
                if (isset($westTiers[$i])) {
                    $relationPriceData["west_tier_{$tierNum}_min_quantity"] = $westTiers[$i]['min_quantity'] ?? null;
                    $relationPriceData["west_tier_{$tierNum}_max_quantity"] = $westTiers[$i]['max_quantity'] ?? null;
                    $relationPriceData["west_tier_{$tierNum}_base_price"] = $westTiers[$i]['price'] ?? null;
                }
            }
        }

        // Insert the single pricing record
        ProductRelationPrice::create($relationPriceData);
    }
    
    /**
     * Generate combination data from form attribute data
     */
    private function generateCombinationDataFromForm(array $attributeData): array
    {
        $attributeIds = $attributeData['attribute_ids'] ?? [];
        $attributeValuesByAttribute = [];
        
        // Collect selected attribute values from form fields
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $attributeData["attribute_values_{$index}"] ?? [];
                
                if (!empty($selectedValues)) {
                    $attributeValuesByAttribute[$attributeId] = $selectedValues;
                }
            }
        }

        if (empty($attributeValuesByAttribute)) {
            return [];
        }

        // Generate all possible combinations
        $combinations = [[]];
        
        foreach ($attributeValuesByAttribute as $attributeId => $valueIds) {
            $newCombinations = [];
            foreach ($combinations as $combination) {
                foreach ($valueIds as $valueId) {
                    $newCombination = $combination;
                    $newCombination[] = [
                        'attribute_id' => $attributeId,
                        'attribute_value_id' => $valueId
                    ];
                    $newCombinations[] = $newCombination;
                }
            }
            $combinations = $newCombinations;
        }

        return $combinations;
    }

    /**
     * Process SKU pricing data (when no variants are created)
     */
    private function processSKUPricing(array $attributeData, Product $product, int $productRelationId, array $attributePrices): void
    {
        // For SKU pricing, we don't create variants, just store prices for attributes
        $relationPricesToInsert = [];
        $now = now();

        // Store pricing data for each attribute (no variants, no specific attribute values)
        foreach ($attributePrices as $attributeId => $priceData) {
            $attribute = Attribute::find($attributeId);
            if (!$attribute) {
                continue;
            }
            
            // Create a ProductAttribute record for the entire attribute (no specific value)
            $productAttribute = ProductAttribute::firstOrCreate([
                'product_id' => $product->id,
                'attribute_id' => $attributeId,
                'attribute_value_id' => null, // NULL means pricing applies to the entire attribute
            ]);
            
            if ($priceData) {
                $relationPricesToInsert[] = [
                    'product_relation_id' => $productRelationId,
                    'product_variant_id' => null, // No variant for SKU pricing
                    'product_attribute_id' => $productAttribute->id,
                    'east_zone_price' => $priceData['east'] ?? null,
                    'west_zone_price' => $priceData['west'] ?? null,
                    'created_at' => $now,
                    'updated_at' => $now,
                ];
            }
        }

        // Insert pricing data
        if (!empty($relationPricesToInsert)) {
            ProductRelationPrice::insert($relationPricesToInsert);
        }
    }

    /**
     * Process combination pricing data (when no variants are created)
     */
    private function processCombinationPricing(array $attributeData, Product $product, int $productRelationId, array $attributeCombinationPrices): void
    {
        $attributeIds = $attributeData['attribute_ids'] ?? [];
        $attributeValuesByAttribute = [];
        
        // Collect selected attribute values from form fields
        foreach (range(1, 10) as $index) {
            if (isset($attributeIds[$index - 1])) {
                $attributeId = $attributeIds[$index - 1];
                $selectedValues = $attributeData["attribute_values_{$index}"] ?? [];
                
                if (!empty($selectedValues)) {
                    $attributeValuesByAttribute[$attributeId] = $selectedValues;
                }
            }
        }

        if (empty($attributeValuesByAttribute)) {
            return;
        }

                $variantsToInsert = [];
                $variantAttributesToInsert = [];
                $relationPricesToInsert = [];
                $now = now();

        $combinations = $this->generateCombinations($attributeValuesByAttribute, $product);

        foreach ($combinations as $combination) {
            $combinationKey = implode('_', array_column($combination, 'attribute_value_id'));
            $priceData = $attributeCombinationPrices[$combinationKey] ?? null;
            
            if (!$priceData) {
                continue; // Skip if no price data for this combination
            }

            // Generate SKU for this combination
            $skuParts = [Str::upper(Str::substr($product->name, 0, 4))];
            foreach ($combination as $attr) {
                $attributeValue = AttributeValue::find($attr['attribute_value_id']);
                if ($attributeValue) {
                    $skuParts[] = Str::upper(Str::substr($attributeValue->name, 0, 3));
                }
            }
            $sku = implode('-', $skuParts) . '-' . rand(100000, 999999);

                    $variantsToInsert[] = [
                        'product_id' => $product->id,
                        'sku' => $sku,
                        'created_at' => $now,
                        'updated_at' => $now,
                '_combination_key' => $combinationKey, // Temporary key for mapping
                '_combination' => $combination // Temporary data for mapping
                    ];
                }

        // Insert variants and process their relationships
                    foreach ($variantsToInsert as $variantData) {
            $combinationKey = $variantData['_combination_key'];
            $combination = $variantData['_combination'];
            unset($variantData['_combination_key'], $variantData['_combination']);
            
                        $variantId = ProductVariant::insertGetId($variantData);
            
            // Store variant attributes
            foreach ($combination as $attr) {
                        $variantAttributesToInsert[] = [
                            'product_variant_id' => $variantId,
                    'attribute_id' => $attr['attribute_id'],
                    'attribute_value_id' => $attr['attribute_value_id'],
                            'created_at' => $now,
                            'updated_at' => $now,
                        ];

                // Create product attribute record
                $productAttribute = ProductAttribute::firstOrCreate([
                    'product_id' => $product->id,
                    'attribute_id' => $attr['attribute_id'],
                    'attribute_value_id' => $attr['attribute_value_id'],
                ]);
            }
            
            // Store pricing data for this combination
            $priceData = $attributeCombinationPrices[$combinationKey] ?? null;
            if ($priceData) {
                            $relationPricesToInsert[] = [
                                'product_relation_id' => $productRelationId,
                                'product_variant_id' => $variantId,
                    'product_attribute_id' => null, // For combination pricing, we don't link to a single attribute
                    'east_zone_price' => $priceData['east'] ?? null,
                    'west_zone_price' => $priceData['west'] ?? null,
                                'created_at' => $now,
                                'updated_at' => $now,
                            ];
                    }
                }

        // Bulk insert all data
                if (!empty($variantAttributesToInsert)) {
            DB::table('product_variant_attributes')->insert($variantAttributesToInsert);
                }
                if (!empty($relationPricesToInsert)) {
                    ProductRelationPrice::insert($relationPricesToInsert);
                }
    }

    /**
     * Update the product relation prices with fixed pricing data
     */
    private function updateFixedPricing(int $productRelationId, array $eastPrices, array $westPrices): void
    {
        // Check if pricing record already exists
        // $existingPrice = DB::table('product_relation_prices')
        //                   ->where('product_relation_id', $productRelationId)
        //                   ->whereNull('product_variant_id')
        //                   ->first();

        // $priceData = [
        //     'product_relation_id' => $productRelationId,
        //     'east_zone_price' => $eastPrices['1kg'], // Default to 1kg price
        //     'west_zone_price' => $westPrices['1kg'], // Default to 1kg price
            
        //     // Store both kg prices for future use
        //     'east_1kg_price' => $eastPrices['1kg'],
        //     'east_2kg_price' => $eastPrices['2kg'],
        //     'west_1kg_price' => $westPrices['1kg'],
        //     'west_2kg_price' => $westPrices['2kg'],
            
        //     'updated_at' => now(),
        // ];

        // if ($existingPrice) {
        //     // Update existing record
        //     DB::table('product_relation_prices')
        //       ->where('id', $existingPrice->id)
        //       ->update($priceData);
        // } else {
        //     // Create new record if it doesn't exist
        //     $priceData['created_at'] = now();
        //     DB::table('product_relation_prices')->insert($priceData);
        // }

        // Log::info('Fixed pricing data updated in database', [
        //     'product_relation_id' => $productRelationId,
        //     'action' => $existingPrice ? 'updated' : 'created'
        // ]);
    }
} 