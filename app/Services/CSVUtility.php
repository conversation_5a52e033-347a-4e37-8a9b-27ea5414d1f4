<?php

namespace App\Services;

use League\Csv\Writer;
use League\Csv\EncloseField;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CSVUtility
{
    protected $csvFileObject;
    protected $csvFileName;
    protected $csvMainFolder;
    public static $logRows = null;
    public static $failRows = 0;
    public static $successRows = 0;
    public static $allCompleted = 0;
    public static $errorRows = 0;
    public static $sinceId = '';
    public $directoryPath = null;
    public $tempLocalPath;
    public static $failedRecords = [];
    public static $successRecords = [];
    public static $records = [];

    public function __construct()
    {
        $this->csvFileObject = '';
        $this->csvFileName = '';
        $this->csvMainFolder = 'audit_logs';
        $this->tempLocalPath = storage_path('app/temp'); // Temp storage path
    }

    /**
     * Create CSV for logging errors
     * @param string $csvfilename
     * @param array $sheetHeader
     * @return mixed
     */
    public function csvCreateForLogError($csvfilename, $sheetHeader)
    {
        return $this->insertCSVRowDataHeader($sheetHeader, $csvfilename);
    }

    /**
     * Create a CSV file in temp storage and then move it to S3
     * @param string $folderName
     * @param string $csvfilename
     * @return string|null
     */
    public function createCSVAsset($folderName, $csvfilename)
    {
        try {
            Log::info("Creating CSV asset in folder: {$folderName} with filename: {$csvfilename}");
            $assetFileName = $csvfilename ?: 'Applicationlog-' . time() . '.csv';
            $localPath = "{$this->tempLocalPath}/{$assetFileName}";  // Temp file path
            $assetPath = "{$folderName}/{$assetFileName}"; // Final S3 path

            // Write the file to temp local storage
            $check  = Storage::disk('local')->put($localPath, file_get_contents($this->directoryPath));
            Log::info("CSV file written to local storage: {$check}");
            // Move the file to S3
            // getStorageDisk()->put($assetPath, Storage::disk('local')->get($localPath), 'public');
            getStorageDisk()->put($assetPath, Storage::disk('local')->get($localPath));

            // Delete the temp file
            Storage::disk('local')->delete($localPath);
            return $assetPath;
        } catch (Exception $ex) {
            Log::error("Error creating CSV asset: " . $ex->getMessage() . ' Line: ' . $ex->getLine());
            return null;
        }
    }

    /**
     * Insert rows into CSV file
     * @param array $data
     * @param string $csvfilename
     * @param array $sheetHeader
     * @return mixed
     */
    public function insertCSVRowData($data, $csvfilename, $sheetHeader)
    {
        try {
            $this->prepareCSVFile($csvfilename, $sheetHeader);
            $writer = Writer::createFromPath($this->directoryPath, 'a+');
            $this->configureCSVWriter($writer);
            $writer->insertAll($data);

            return $this->createCSVAsset($this->csvMainFolder, $csvfilename);
        } catch (Exception $ex) {
            Log::error("Error inserting CSV row data: " . $ex->getMessage());
        }
    }

    /**
     * Insert header into CSV file
     * @param array $data
     * @param string $csvFileName
     * @return mixed
     */
    public function insertCSVRowDataHeader($data, $csvFileName)
    {
        try {
            $this->directoryPath = "{$this->tempLocalPath}/{$csvFileName}";
            $writer = Writer::createFromPath($this->directoryPath, 'a+');
            $this->configureCSVWriter($writer);
            $writer->insertOne($data);

            return $writer;
        } catch (Exception $ex) {
            Log::error("Error inserting CSV header: " . $ex->getMessage());
        }
    }

    /**
     * Insert a single row into CSV file
     * @param array $data
     * @param string $csvfilename
     * @param array $sheetHeader
     * @return mixed
     */
    public function insertCSVSingleRow($data, $csvfilename, $sheetHeader)
    {
        try {
            $this->prepareCSVFile($csvfilename, $sheetHeader);

            $writer = Writer::createFromPath($this->directoryPath, 'a+');
            $this->configureCSVWriter($writer);
            $writer->insertOne($data);

            return $this->createCSVAsset($this->csvMainFolder, $csvfilename);
        } catch (Exception $ex) {
            Log::error("Error inserting single CSV row: " . $ex->getMessage());
        }
    }

    /**
     * Prepare CSV file by creating folders and inserting headers
     * @param string $csvfilename
     * @param array $sheetHeader
     */
    private function prepareCSVFile($csvfilename, $sheetHeader)
    {
        if (!file_exists($this->tempLocalPath)) {
            mkdir($this->tempLocalPath, 0777, true); // Ensure temp directory exists
        }

        $this->csvCreateForLogError($csvfilename, $sheetHeader);
        $this->directoryPath = "{$this->tempLocalPath}/{$csvfilename}";
    }

    /**
     * Configure the CSV writer settings
     * @param Writer $writer
     */
    private function configureCSVWriter(Writer $writer)
    {
        $writer->setDelimiter(';');
        $writer->setOutputBOM(Writer::BOM_UTF8);
        EncloseField::addTo($writer, "\t\x1f");
    }
}
