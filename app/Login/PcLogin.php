<?php

namespace App\Login;

use Livewire\Livewire;
use Filament\Forms\Set;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Pages\Auth\Login;
use App\Response\PcLoginResponse;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Hidden;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Cookie;
use App\Services\ExtendedSessionService;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Components\Placeholder;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Models\Contracts\FilamentUser;
use Livewire\Component as LivewireComponent;
use Illuminate\Validation\ValidationException;
use AbanoubNassem\FilamentGRecaptchaField\Forms\Components\GRecaptcha;
use DanHarrin\LivewireRateLimiting\Exceptions\TooManyRequestsException;

class PcLogin extends Login
{
    protected static string $view = 'custom-view.auth.pc-login';

    public function getTitle(): string
    {
        return 'Login as PS';
    }


    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(new HtmlString('<span class="">Password <span style="color: red;">*</span></span>'))
            ->helperText('Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
            ->validationAttribute('password')
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->autocomplete('current-password')
            ->rules([Password::defaults(), 'required'])
            ->extraInputAttributes(['tabindex' => 2]);
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(new HtmlString('<span class="">Email Address <span style="color: red;">*</span></span>'))
            ->validationAttribute('email')
            ->afterStateUpdated(function ($state, Set $set) {
                $set('email', trim($state));
            })
            ->formatStateUsing(function () {
                if (request()->hasCookie('last_email')) {
                    return request()->cookie('last_email');
                }
                return null;
            })
            ->rules(['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
            // ->validationMessages([
            //     'exists'=> 'These credentials do not match our records.'
            // ])
            ->dehydrateStateUsing(fn($state) => strtolower($state))
            ->autocomplete()
            ->autofocus()
            ->extraInputAttributes(['tabindex' => 1]);
    }

    public function registerAction(): Action
    {
        return Action::make('register')
            ->link()
            ->label('Sign Up')
            ->url(function () {
                return filament()->getPanel('pc')->getRegistrationUrl();
            });
    }

    protected function getAuthenticateFormAction(): Action
    {
        return Action::make('authenticate')
            ->label('Continue')
            ->submit('authenticate');
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        Group::make()->schema([
                            $this->getEmailFormComponent(),
                            $this->getPasswordFormComponent(),
                            Group::make()->schema([
                                $this->getRememberFormComponent(),
                                $this->getResetPasswordFormComponent(),
                            ])
                                ->extraAttributes(['class' => 'justify-end'])
                                ->columns(2),
                        ])->extraAttributes(['class' => 'justify-end']),
                        Group::make()->schema([
                            GRecaptcha::make('captcha')
                                ->validationMessages([
                                    'required' => 'Captcha is required.',
                                    'captcha' => 'Captcha is required.',
                                ])
                            // ->extraAttributes(['class' => 'justify-start']) // Align to the left
                        ])->columns(1)->extraAttributes(['class' => 'justify-end']),
                        Hidden::make('timezone')
                            ->label('timezone')->extraAttributes(['id' => 'timezone'])

                    ])
                    ->statePath('data'),
            ),
        ];
    }

    public function getResetPasswordFormComponent(): Component
    {
        return ViewField::make('forget_password')->label('Reset Password')->view('custom-view.fields.link')->viewData(['link' => filament()->getRequestPasswordResetUrl(), 'label' => 'Forgot Password?']);
    }

    public function getHeading(): string | Htmlable
    {
        return new HtmlString('<div class="flex items-center justify-center mb-8 text-md"><p>Login</p></div>');
    }

    public function authenticate(): ?PcLoginResponse
    {
        // Store original session ID for logging
        $originalSessionId = session()->getId();
        
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();
            LivewireComponent::dispatch('reset-captcha');
            return null;
        }

        $data = $this->form->getState();
        foreach ($data as $key => $value) {
            $data[$key] = trim($value);
        }
        
        if (! Filament::auth()->attempt($this->getCredentialsFromFormData($data), $data['remember'] ?? false)) {
            LivewireComponent::dispatch('reset-captcha');
            $this->throwFailureValidationException();
        }
        cache()->put('url.intended_user_id_'.Auth::id(), session('url.intended'));
        $user = Filament::auth()->user();
        
        // Handle remember me logic
        if ($data['remember']) {
            $cookie = cookie()->queue('last_email', $data['email'], ExtendedSessionService::daysToMinutes(15));
            ExtendedSessionService::enableRememberMe();
            try {
                $user->update(['remember_me' => true]);
                cache()->forget('remember_me_' . $user->id);
            } catch (\Exception $e) {
                Log::error('Failed to update remember_me for user: ' . $user->id . ' - ' . $e->getMessage());
            }
            cache()->put('remember_me_first_time_login_'.Auth::id(), true);
        } else {
            ExtendedSessionService::disableRememberMe();
            cookie()->queue(cookie()->forget('last_email'));
            $user->update(['remember_me' => false]);
            cache()->forget('remember_me_' . $user->id);
        }
        
        if ($data['timezone'] && Auth::check()) {
            Auth::user()->update(['timezone' => $data['timezone']]);
        }
        
        if ($user->is_active == false) {
            LivewireComponent::dispatch('reset-captcha');
            Notification::make()->title('Your account is not active. Please contact the administrator.')->danger()->send();
            return null;
        }
        
        if (
            ($user instanceof FilamentUser) &&
            (! $user->canAccessPanel(Filament::getCurrentPanel()))
        ) {
            Filament::auth()->logout();
            LivewireComponent::dispatch('reset-captcha');
            $this->throwFailureValidationException();
        }

        // Only regenerate session once at the end
        session()->regenerate();
        
        \Log::info('PC Login - Final Session Regenerated', [
            'original_session_id' => $originalSessionId,
            'new_session_id' => session()->getId(),
            'user_id' => $user->id,
        ]);

        //ActivityLog Start
        activity()
            ->causedBy($user)
            ->log('PS user has logged in successfully');
        //ActivityLog end
        
        $response = app(PcLoginResponse::class);
        return $response;
    }

    protected function onValidationError(ValidationException $exception): void
    {
        Log::error('PC Login - Validation Error', [
            'session_id' => session()->getId(),
            'error_message' => $exception->getMessage(),
            'error_code' => $exception->getCode(),
            'errors' => $exception->errors(),
            'user_agent' => request()->userAgent(),
        ]);
        
        LivewireComponent::dispatch('reset-captcha');
    }
}
