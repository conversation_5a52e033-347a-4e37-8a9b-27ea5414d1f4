<?php

namespace App\Login;

use Filament\Forms\Set;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Pages\Auth\Login;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use App\Response\AdminLoginResponse;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Hidden;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Session;
use App\Services\ExtendedSessionService;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Illuminate\Validation\Rules\Password;
use Filament\Models\Contracts\FilamentUser;
use Livewire\Component as LivewireComponent;
use Illuminate\Validation\ValidationException;
use AbanoubNassem\FilamentGRecaptchaField\Forms\Components\GRecaptcha;
use DanH<PERSON>rin\LivewireRateLimiting\Exceptions\TooManyRequestsException;

class AdminLogin extends Login
{
    protected static string $view = 'custom-view.auth.admin-login';

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(new HtmlString('<span class="">Password <span style="color: red;">*</span></span>'))
            ->helperText('Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
            ->validationAttribute('password')
            ->rules(['required', 'string', Password::default()])
            // ->hint(filament()->hasPasswordReset() ? new HtmlString(Blade::render('<x-filament::link :href="filament()->getRequestPasswordResetUrl()" tabindex="3"> {{ __(\'filament-panels::pages/auth/login.actions.request_password_reset.label\') }}</x-filament::link>')) : null)
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->autocomplete('current-password')
            ->extraInputAttributes(['tabindex' => 2]);
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(new HtmlString('<span class="">Email Address <span style="color: red;">*</span></span>'))
            // ->rules(['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'exists:users,email'])
            ->rules(['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
            ->validationAttribute('email')
            ->validationMessages([
                'exists' => 'The email address is not registered.',
                'regex' => 'Please enter a valid email address.',
                'required' => 'The email address field is required.',
                'email' => 'Please enter a valid email address.',
            ])
            ->formatStateUsing(function () {
                if (request()->hasCookie('admin_email')) {
                    return request()->cookie('admin_email');
                }
                return null;
            })
            ->dehydrateStateUsing(fn($state) => strtolower($state))
            ->afterStateUpdated(function ($state, Set $set) {
                $set('email', trim($state));
            })
            ->autocomplete()
            ->autofocus()
            ->extraInputAttributes(['tabindex' => 1]);
    }

    protected function getAuthenticateFormAction(): Action
    {
        return Action::make('authenticate')
            ->label(__('Continue'))
            ->submit('authenticate');
    }


    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getEmailFormComponent(),
                        $this->getPasswordFormComponent(),

                        Group::make()->schema([
                            $this->getRememberFormComponent(),
                            $this->getResetPasswordFormComponent(),
                        ])
                            ->extraAttributes(['class' => 'justify-end'])
                            ->columns(2),
                        Group::make()->schema([
                            GRecaptcha::make('captcha')
                                ->validationMessages([
                                    'required' => 'Captcha is required.',
                                    'captcha' => 'Captcha is required.',
                                ])
                        ]),
                        Hidden::make('timezone')
                            ->label('timezone')->extraAttributes(['id' => 'timezone'])
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    public function getResetPasswordFormComponent(): Component
    {
        return ViewField::make('rest_password')->view('custom-view.fields.link')->viewData(['link' => filament()->getRequestPasswordResetUrl(), 'label' => 'Forgot Password?']);
    }

    public function authenticate(): ?AdminLoginResponse
    {
        try {
            $this->rateLimit(5);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();
            LivewireComponent::dispatch('reset-captcha');
            return null;
        }

        $data = $this->form->getState();

        if (! Filament::auth()->attempt($this->getCredentialsFromFormData($data), $data['remember'] ?? false)) {
            LivewireComponent::dispatch('reset-captcha');
            $this->throwFailureValidationException();
        }
        cache()->put('url.intended_user_id_'.Auth::id(), session('url.intended'));
        $user = Filament::auth()->user();
        
        error_log('LOGIN DEBUG: Starting remember me processing for user: ' . $user->id);
        $setKey = "user_sessions:{$user->id}";
        $sessionId = session()->getId();
        $connection = config('session.connection') ?: null;

        Redis::connection($connection)->sadd($setKey, $sessionId);


        if ($data['remember']) {
            try {
                error_log('LOGIN DEBUG: Remember me is true');

                // Set cookie with proper configuration
                cookie()->queue('admin_email', $data['email'], ExtendedSessionService::daysToMinutes(15), '/', null, request()->isSecure(), true);

                // Update user with error handling (don't call enableRememberMe as it duplicates this)
                $updated = $user->update(['remember_me' => true]);
                if ($updated) {
                    ExtendedSessionService::extendSessionForRememberMe();
                }
                error_log('LOGIN DEBUG: User update result: ' . ($updated ? 'success' : 'failed'));

                if (!$updated) {
                    Log::warning('Failed to update remember_me for user: ' . $user->id);
                    error_log('LOGIN ERROR: Failed to update remember_me for user: ' . $user->id);
                }

                // Cache operations with error handling
                try {
                    cache()->put('remember_me_first_time_login_'.Auth::id(), true, now()->addDays(15));
                    error_log('LOGIN DEBUG: Cache put successful for first time login');

                    // Check if user already has a valid remember me cache from previous login
                    $existingCache = cache()->get('remember_me_' . $user->id);
                    error_log('LOGIN DEBUG: Existing cache check: ' . ($existingCache ? 'found' : 'not found'));

                    if (!$existingCache) {
                        // First time login with remember me - will need to verify 2FA first
                        cache()->forget('remember_me_' . $user->id);
                        error_log('LOGIN DEBUG: Cleared existing cache for user: ' . $user->id);
                    }
                } catch (\Exception $e) {
                    Log::error('Cache operation failed during remember me setup: ' . $e->getMessage());
                    error_log('LOGIN ERROR: Cache operation failed: ' . $e->getMessage());
                }

                Log::info('Remember me enabled for user: ' . $user->id);
                error_log('LOGIN DEBUG: Remember me setup completed successfully');
            } catch (\Exception $e) {
                Log::error('Remember me setup failed: ' . $e->getMessage());
                error_log('LOGIN ERROR: Remember me setup failed: ' . $e->getMessage());
            }
                } else {
            error_log('LOGIN DEBUG: Remember me is false - disabling');
            try {
                cookie()->queue(cookie()->forget('admin_email'));

                // Update user with error handling (don't call disableRememberMe as it duplicates this)
                $updated = $user->update(['remember_me' => false]);
                if ($updated) {
                    ExtendedSessionService::setStandardSessionLifetime();
                }
                error_log('LOGIN DEBUG: User remember_me disabled: ' . ($updated ? 'success' : 'failed'));

                if (!$updated) {
                    Log::warning('Failed to disable remember_me for user: ' . $user->id);
                    error_log('LOGIN ERROR: Failed to disable remember_me for user: ' . $user->id);
                }

                // Clear user-specific cache with error handling
                try {
                    cache()->forget('remember_me_' . $user->id);
                    cache()->forget('remember_me_first_time_login_' . $user->id);
                    error_log('LOGIN DEBUG: Cache cleared for user: ' . $user->id);
                } catch (\Exception $e) {
                    Log::error('Cache clear failed: ' . $e->getMessage());
                    error_log('LOGIN ERROR: Cache clear failed: ' . $e->getMessage());
                }
            } catch (\Exception $e) {
                Log::error('Remember me disable failed: ' . $e->getMessage());
                error_log('LOGIN ERROR: Remember me disable failed: ' . $e->getMessage());
            }
        }

        if ($data['timezone'] && Auth::check()) {
            Auth::user()->update(['timezone' => $data['timezone']]);
        }

        if (
            ($user instanceof FilamentUser) &&
            (! $user->canAccessPanel(Filament::getCurrentPanel()))
        ) {
            Filament::auth()->logout();
            LivewireComponent::dispatch('reset-captcha');
            $this->throwFailureValidationException();
        }


        session()->regenerate();
        session()->regenerateToken();

        //ActivityLog Start
        activity()
            ->causedBy($user)
            ->log('Admin user has logged in successfully');
        //ActivityLog end

        return app(AdminLoginResponse::class);
    }

    protected function onValidationError(ValidationException $exception): void
    {
        LivewireComponent::dispatch('reset-captcha');
    }
}
