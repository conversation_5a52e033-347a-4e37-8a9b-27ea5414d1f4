<?php

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\View\View;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Concerns\InteractsWithForms;

class UserPassword extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $layout = 'filament-email-2fa::simple-layout';
    protected static string $view = 'livewire.user-password';
    protected static ?string $title = "";

    public $userId;

    public $data = [];

    public function mount($id)
    {
        $this->userId = $id;
        $user = User::find($this->userId)?->makeVisible('password')->toArray();
        if (empty($user) || !empty($user['password'])) {
            Notification::make()
                ->title('User not found or already has a password')
                ->body('If you want to reset/forget the password, please use the password reset/forget feature.')
                ->danger()
                ->send();
            return redirect()->to(Filament::getCurrentPanel()->getLoginUrl());
        }
    }

    public function hasLogo()
    {
        return true;
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('password')
                ->label(new HtmlString("Password <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                ->password()
                ->revealable()
                ->rules(['required', 'confirmed', Password::defaults()])
                ->validationMessages([
                    'required' => 'The password field is required.',
                    'confirmed' => 'The password field confirmation does not match.',
                    'password.min' => 'The password field must be at least 8 characters.',
                    'password.mixed' => 'The password field must contain at least one uppercase and one lowercase letter.',
                    'password.numbers' => 'The password field must contain at least one number.',
                    'password.symbols' => 'The password field must contain at least one symbol.',
                    'password.uncompromised' => 'This password has appeared in a data leak. Please choose a different one.',
                ]),
            TextInput::make('password_confirmation')
                ->label(new HtmlString("Confirm Password <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                ->password()
                ->revealable()
                ->rules(['required'])
                ->validationMessages([
                    'required' => 'The password field is required.',
                ]),
        ])->statePath('data');
    }

    public function createAction()
    {
        return Action::make('create')
            ->action('submit')
            ->extraAttributes(['class' => 'w-full bg-blue-950 text-white inline-block']);
    }

    public function submit()
    {
        $this->form->getState();
        $user = User::find($this->userId)->makeVisible('password');
        if (!empty($user->password || empty($user))) {
            Notification::make()
                ->title('User Not Found or Already Has a Password')
                ->body('User not found or already has a password.')
                ->danger()
                ->send();
            return;
        }
        $user->update([
            'password' => Hash::make($this->data['password']),
            'status' => true
        ]);

        Notification::make()
            ->body('The password has been created successfully.')
            ->success()
            ->send();

        if (strtolower($user->roles->first()->panel) === 'pc') {
            $loginUrl = Filament::getPanel(strtolower($user->roles->first()->panel))->getLoginUrl();
        } else {
            
            $loginUrl = Filament::getCurrentPanel()->getLoginUrl();
        }
        return redirect()->to($loginUrl);
    }
}
