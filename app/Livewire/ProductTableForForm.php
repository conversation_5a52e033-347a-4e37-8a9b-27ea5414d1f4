<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\Product;
use Livewire\Component;
use App\Models\Category;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Illuminate\Contracts\Database\Eloquent\Builder;

class ProductTableForForm extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;
    public function render()
    {
        return view('livewire.product-table-for-form');
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->query(function () {
                $data = Product::query()->with(['category', 'subcategory', 'productData', 'batches']);
                return $data;
            })
            ->columns([
                TextColumn::make('id')->label('Product ID')
                    ->sortable(),
                TextColumn::make('name')->label('Product Name'),
                TextColumn::make('category.name')->label('Category'),
                TextColumn::make('subcategory.name')->label('Subcategory'),

            ])
            ->filters([
                SelectFilter::make('category')
                    ->options(function () {
                        return self::getCategories();
                        // return Category::whereNull('parent_id')->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->relationship('categoryForFilter', 'name'),
                SelectFilter::make('subcategory')
                    ->options(function () {
                        return Category::whereNotNull('parent_id')->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->relationship('subcategoryForFilter', 'name'),
                // Filter::make('pending approval')
                //     ->modifyQueryUsing(function ($query) {
                //         $products = $query->where('status', 'pending');
                //         return $products;
                //     }),
                Filter::make('created_at')
                    ->label('Created On')
                    ->form([
                        DatePicker::make('created_from')->label('From'),
                        DatePicker::make('created_until')->label('Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),
            ]);
    }
}
