<?php

namespace App\Livewire;

use Livewire\Component;
use Indianic\CmsPages\Models\CmsPage;

class PrivacyPolicy extends Component
{
   
    public $pageData;

    public function mount()
    {
        $page = CmsPage::where('slug', 'privacy-policy')->firstOrFail();
        $this->pageData = $page;
    }

    public function render()
    {
        return view('livewire.privacy-policy'); // optional: use your custom layout
    }
}
