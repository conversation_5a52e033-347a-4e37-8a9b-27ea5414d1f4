<?php

namespace App\Livewire;

use Carbon\Carbon;
use App\Models\Product;
use Livewire\Component;
use App\Models\Category;
use Filament\Tables\Table;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Illuminate\Contracts\Database\Eloquent\Builder;

class ProductTableForPc extends Component implements HasTable, HasForms
{
    use InteractsWithForms;
    use InteractsWithTable;


    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->query(function () {
                $userId = auth()->user()->parent_id ?? auth()->user()->id;
                $query = Product::query()->whereHas('productData', function ($query) use ($userId) {
                    $query->where('user_id', $userId);
                });
                return $query->with(['category', 'subcategory']);
            })
            ->columns([
                TextColumn::make('id')->label('Product ID')
                    ->sortable(),
                TextColumn::make('name')->label('Product Name'),
                TextColumn::make('category.name')->label('Category'),
                TextColumn::make('subcategory.name')->label('Subcategory'),
            ])
            ->filters([


                SelectFilter::make('category')
                    ->options(function () {
                        return Category::whereNull('parent_id')->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->relationship('categoryForFilter', 'name'),
                SelectFilter::make('subcategory')
                    ->options(function () {
                        return Category::whereNotNull('parent_id')->pluck('name', 'id');
                    })
                    ->multiple()
                    ->searchable()
                    ->preload()
                    ->relationship('subcategoryForFilter', 'name'),

                Filter::make('created_at')
                    ->label('Created On')
                    ->form([
                        DatePicker::make('created_from')->label('From')->maxDate(today()),
                        DatePicker::make('created_until')->label('Until')->maxDate(today()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {

                        return $query
                            ->when(
                                $data['created_from'],
                                function (Builder $query, $date): Builder {
                                    return  $query->whereHas('productData', function ($q) use ($date) {
                                        $q->where('requested_by', auth()->id())->whereDate('created_at', '>=', $date);
                                    });
                                }
                            )
                            ->when(
                                $data['created_until'],
                                function (Builder $query, $date): Builder {
                                    return $query->whereHas('productData', function ($q) use ($date) {
                                        $q->where('requested_by', auth()->id())->whereDate('created_at', '<=', $date);
                                    });
                                }
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    })
            ]);
    }

    public function render()
    {
        return view('livewire.product-table-for-pc');
    }
}
