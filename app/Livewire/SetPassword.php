<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\User;
use Livewire\Component;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\View\View;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Concerns\InteractsWithForms;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class SetPassword extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $layout = 'filament-email-2fa::simple-layout';
    protected static string $view = 'livewire.set-password';
    protected static ?string $title = "";
    public $redirectUrl; // Make this a public property

    public $userId;

    public $data = [];

    public function mount($id)
    {
        $this->userId = decryptParam($id);
        $user = User::find($this->userId);

        if (! $user) {
            Notification::make()
                ->title('Something went wrong, please try again later.')
                ->danger()
                ->send();
            
            // Check if user is authenticated before accessing roles
            if (Auth::check()) {
                $panel = strtolower(Auth::user()->roles->first()->panel);
                // Logout the user and clear any intended URL
                Auth::logout();
                Session::forget('url.intended');
                Session::invalidate();
                Session::regenerateToken();
            }
            
            return redirect()->to('/login');
        }
    }

    public function hasLogo()
    {
        return true;
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('password')
                ->label(new HtmlString("Password <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                ->password()
                ->revealable()
                ->rules(['required', 'confirmed', Password::defaults()])
                ->validationMessages([
                    'required' => 'The password field is required.',
                    'confirmed' => 'The password field confirmation does not match.',
                    'password.min' => 'The password field must be at least 8 characters.',
                    'password.mixed' => 'The password field must contain at least one uppercase and one lowercase letter.',
                    'password.numbers' => 'The password field must contain at least one number.',
                    'password.symbols' => 'The password field must contain at least one symbol.',
                    'password.uncompromised' => 'This password has appeared in a data leak. Please choose a different one.',
                ]),
            TextInput::make('password_confirmation')
                ->label(new HtmlString("Confirm Password <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                ->password()
                ->revealable()
                ->rules(['required'])
                ->validationMessages([
                    'required' => 'The password field is required.',
                ]),
        ])->statePath('data');
    }

    public function createAction()
    {
        return Action::make('create')
            ->action('submit')
            ->extraAttributes(['class' => 'w-full bg-blue-950 text-white inline-block hover:bg-blue-950']);
    }

    public function logoutAction()
    {
        return Action::make('logout')
            ->action('logout')
            ->icon('heroicon-m-arrow-left-on-rectangle')
            ->extraAttributes(['class' => 'w-full bg-blue-950 text-white inline-block hover:bg-blue-950']);
    }

    public function submit()
    {
        $this->form->getState();
        $user = User::find($this->userId);

        $user->password = Hash::make($this->data['password']);
        $user->is_temp_password = false;
        $user->save();

        Notification::make()
            ->body('The password has been created successfully.')
            ->success()
            ->send();

        $panel = strtolower($user->roles?->first()?->panel ?? '');
        
        cache()->forget('remember_me_' . Auth::id());
        Auth::logout();
        Session::forget('url.intended');
        Session::invalidate();
        Session::regenerateToken();

        // Redirect to the appropriate panel login page using Filament's method
        if ($panel === 'pc') {
            $redirectUrl = Filament::getPanel('pc')->getLoginUrl();
        } else {
            $redirectUrl = Filament::getPanel('admin')->getLoginUrl();
        }
        
        return redirect($redirectUrl);
    }

    public function logout()
    {
        $user = User::find($this->userId);
        $panel = strtolower($user->roles->first()->panel);

        // Logout the user and clear any intended URL
        Auth::logout();
        Session::forget('url.intended');
        Session::invalidate();
        Session::regenerateToken();

        // Redirect to the panel's login page
        $loginUrl = Filament::getPanel($panel)->getLoginUrl();
        return redirect()->to($loginUrl);
    }
}
