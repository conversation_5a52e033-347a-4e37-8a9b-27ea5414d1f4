<?php

declare(strict_types=1);

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;
use App\Component\ProductVariant\Services\SummaryDataService;
use App\Component\ProductVariant\DTOs\VariantSummaryItem;

class ProductVariantSummaryTable extends Component
{
    public array $formData = [];
    public Collection $variants;

    public function mount(array $formData = []): void
    {
        $this->formData = $formData;
        $this->refreshVariants();
    }

    public function updatedFormData(): void
    {
        $this->refreshVariants();
    }

    private function refreshVariants(): void
    {
        $this->variants = SummaryDataService::generateVariantsSummary($this->formData);
    }

    public function deleteVariant(int $index): void
    {
        $this->variants = $this->variants->reject(fn(VariantSummaryItem $item) => $item->index === $index);
        
        // Re-index the remaining variants
        $this->variants = $this->variants->values()->map(function (VariantSummaryItem $item, int $newIndex) {
            return new VariantSummaryItem(
                index: $newIndex,
                sku: $item->sku,
                displayName: $item->displayName,
                attributes: $item->attributes,
                pricing: $item->pricing,
                quantity: $item->quantity,
                images: $item->images,
                priceDisplay: $item->priceDisplay,
                quantityDisplay: $item->quantityDisplay,
                imagesCount: $item->imagesCount,
            );
        });

        Notification::make()
            ->title('Variant Deleted')
            ->body('The variant has been successfully deleted.')
            ->success()
            ->send();
    }

    public function deleteMultipleVariants(array $indices): void
    {
        $this->variants = $this->variants->reject(fn(VariantSummaryItem $item) => in_array($item->index, $indices));
        
        // Re-index the remaining variants
        $this->variants = $this->variants->values()->map(function (VariantSummaryItem $item, int $newIndex) {
            return new VariantSummaryItem(
                index: $newIndex,
                sku: $item->sku,
                displayName: $item->displayName,
                attributes: $item->attributes,
                pricing: $item->pricing,
                quantity: $item->quantity,
                images: $item->images,
                priceDisplay: $item->priceDisplay,
                quantityDisplay: $item->quantityDisplay,
                imagesCount: $item->imagesCount,
            );
        });

        $deletedCount = count($indices);
        
        Notification::make()
            ->title('Variants Deleted')
            ->body("{$deletedCount} variant(s) have been successfully deleted.")
            ->success()
            ->send();
    }

    public function refreshTable(): void
    {
        $this->refreshVariants();
        
        Notification::make()
            ->title('Table Refreshed')
            ->body('The variant summary has been refreshed.')
            ->info()
            ->send();
    }

    public function exportSummary(): void
    {
        // TODO: Implement export functionality
        Notification::make()
            ->title('Export functionality')
            ->body('Export feature will be implemented in the future.')
            ->info()
            ->send();
    }

    public function hasPricingData(): bool
    {
        return !empty($this->formData['price_assignment_type']);
    }

    public function hasQuantityData(): bool
    {
        return !empty($this->formData['quantity_assignment_type']);
    }

    public function hasImageData(): bool
    {
        return !empty($this->formData['image_assignment_type']);
    }

    public function getVariantsCount(): int
    {
        return $this->variants->count();
    }

    public function updateFormData(array $newFormData): void
    {
        $this->formData = $newFormData;
        $this->refreshVariants();
    }

    public function render()
    {
        return view('livewire.product-variant-summary-table');
    }
} 