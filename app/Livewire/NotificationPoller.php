<?php

namespace App\Livewire;

use Livewire\Component;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class NotificationPoller extends Component
{
    public $lastUnreadCount = 0;

    public function mount()
    {
        if (Auth::check()) {
            $this->lastUnreadCount = Auth::user()->unreadNotifications()->count();
        }
    }

    public function checkForNewNotifications()
    {
        if (!Auth::check()) return;

        $user = Auth::user();
        $currentUnreadCount = $user->unreadNotifications()->count();

        // Show toast only if a new notification has arrived
        if ($currentUnreadCount >= 1 && ($this->lastUnreadCount === null || $currentUnreadCount > $this->lastUnreadCount)) {
            $newNotification = $user->unreadNotifications()
                ->orderBy('created_at', 'desc') // Show latest one
                ->first();

            if ($newNotification) {
                $this->showToast($newNotification);
            }
        }
        $this->lastUnreadCount = $currentUnreadCount;
    }

    protected function showToast($notification)
    {
        $data = $notification->data;

        $notificationBuilder = Notification::make()
            ->title($data['title'] ?? 'New Notification')
            ->body($data['body'] ?? '')
            ->icon($data['icon'] ?? 'heroicon-o-bell')
            //->duration($data['duration'] === 'persistent' ? 0 : (int) ($data['duration'] ?? 15000))
            ->duration(15000)
            ->color($data['color'])
            ->{match ($data['status'] ?? 'success') {
                'success' => 'success',
                'warning' => 'warning',
                'danger' => 'danger',
                default => 'info',
            }}();

        // Handle actions
        $actions = $this->buildNotificationActions($data);
        if (!empty($actions)) {
            $notificationBuilder->actions($actions);
        }

        $notificationBuilder->send();

        // Dispatch event for sound/other handlers
        $this->dispatch('notification-received', [
            'id' => $notification->id,
            'title' => $data['title'],
            'type' => $data['status'] ?? 'info',
        ]);
    }

    private function buildNotificationActions($data)
    {
        $actions = [];

        // Handle actions array
        if (!empty($data['actions']) && is_array($data['actions'])) {
            foreach ($data['actions'] as $actionData) {
                if (!empty($actionData['url']) && !empty($actionData['label'])) {
                    $action = \Filament\Notifications\Actions\Action::make($actionData['name'] ?? 'action_' . uniqid())
                        ->label($actionData['label'])
                        ->color($actionData['color'] ?? 'success');

                    // Handle different URL types
                    if ($this->isExternalUrl($actionData['url'])) {
                        // External URL - open in new tab
                        $action->url($actionData['url'])->openUrlInNewTab(false);
                    } elseif ($this->isInternalRoute($actionData['url'])) {
                        // Internal route - redirect in same tab
                        $action->url($actionData['url'])->openUrlInNewTab(false);
                    } else {
                        // Custom action with closure
                        $action->url($actionData['url'])->openUrlInNewTab(false);
                    }
                    $actions[] = $action;
                }
            }
        }

        // Fallback to target_url if no actions
        if (empty($actions) && !empty($data['target_url'])) {
            $actions[] = \Filament\Notifications\Actions\Action::make('view_details')
                ->label('View Details')
                ->color('success')
                ->url($data['target_url'])
                ->openUrlInNewTab(false);
        }


        return $actions;
    }

    private function isExternalUrl($url)
    {
        return filter_var($url, FILTER_VALIDATE_URL) &&
            !str_starts_with($url, url('/')) &&
            !str_starts_with($url, request()->getSchemeAndHttpHost());
    }

    private function isInternalRoute($url)
    {
        return str_starts_with($url, url('/')) ||
            str_starts_with($url, request()->getSchemeAndHttpHost()) ||
            str_starts_with($url, '/');
    }

    public function render()
    {
        return <<<'blade'
        <div wire:poll.5s="checkForNewNotifications" wire:ignore></div>
        blade;
    }
}
