<?php

namespace App\Livewire;

use App\Models\ProductRelationPrice;
use Filament\Facades\Filament;
use Filament\Forms\Get;
use Livewire\Component;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Set;
use Filament\Infolists\Components\Group as ComponentsGroup;
use Filament\Notifications\Notification;

class InfoListPrice extends Component implements HasForms
{
    public static string $view = 'livewire.info-list-price';
    use InteractsWithForms;

    public $record;
    public $east_fixed_cummission_type;
    public $east_fixed_cummission_value;
    public $west_fixed_cummission_type;
    public $west_fixed_cummission_value;
    public $east_tier_1_cummission_type;
    public $east_tier_1_cummission_value;
    public $east_tier_2_cummission_type;
    public $east_tier_2_cummission_value;
    public $east_tier_3_cummission_type;
    public $east_tier_3_cummission_value;

    public $west_tier_1_cummission_type;
    public $west_tier_1_cummission_value;
    public $west_tier_2_cummission_type;
    public $west_tier_2_cummission_value;
    public $west_tier_3_cummission_type;
    public $west_tier_3_cummission_value;

    public $east_bonus_1_cummission_type;
    public $east_bonus_2_cummission_type;
    public $east_bonus_3_cummission_type;
    public $east_bonus_1_cummission_value;
    public $east_bonus_2_cummission_value;
    public $east_bonus_3_cummission_value;
    public $west_bonus_1_cummission_type;
    public $west_bonus_2_cummission_type;
    public $west_bonus_3_cummission_type;
    public $west_bonus_1_cummission_value;
    public $west_bonus_2_cummission_value;
    public $west_bonus_3_cummission_value;

    public $east_net_earnings;
    public $west_net_earnings;
    public $form_data;

    public int $productRelationId;

    public function mount($record)
    {
        $this->record = $record;
        $this->productRelationId = $record->id;

        $this->form_data = [
            'east_fixed_cummission_type' => $record->productCommission?->east_fixed_cummission_type,
            'east_fixed_cummission_value' => $record->productCommission?->east_fixed_cummission_value,
            'west_fixed_cummission_type' => $record->productCommission?->west_fixed_cummission_type,
            'west_fixed_cummission_value' => $record->productCommission?->west_fixed_cummission_value,
            'east_tier_1_cummission_type' => $record->productCommission?->east_tier_1_cummission_type,
            'east_tier_1_cummission_value' => $record->productCommission?->east_tier_1_cummission_value,

            'east_tier_2_cummission_type' => $record->productCommission?->east_tier_2_cummission_type,
            'east_tier_2_cummission_value' => $record->productCommission?->east_tier_2_cummission_value,

            'east_tier_3_cummission_type' => $record->productCommission?->east_tier_3_cummission_type,
            'east_tier_3_cummission_value' => $record->productCommission?->east_tier_3_cummission_value,

            'west_tier_1_cummission_type' => $record->productCommission?->west_tier_1_cummission_type,
            'west_tier_1_cummission_value' => $record->productCommission?->west_tier_1_cummission_value,

            'west_tier_2_cummission_type' => $record->productCommission?->west_tier_2_cummission_type,
            'west_tier_2_cummission_value' => $record->productCommission?->west_tier_2_cummission_value,

            'west_tier_3_cummission_type' => $record->productCommission?->west_tier_3_cummission_type,
            'west_tier_3_cummission_value' => $record->productCommission?->west_tier_3_cummission_value,

            'east_bonus_1_cummission_type' => $record->productCommission?->east_bonus_1_cummission_type,
            'east_bonus_2_cummission_type' => $record->productCommission?->east_bonus_2_cummission_type,
            'east_bonus_3_cummission_type' => $record->productCommission?->east_bonus_3_cummission_type,
            'east_bonus_1_cummission_value' => $record->productCommission?->east_bonus_1_cummission_value,
            'east_bonus_2_cummission_value' => $record->productCommission?->east_bonus_2_cummission_value,
            'east_bonus_3_cummission_value' => $record->productCommission?->east_bonus_3_cummission_value,

            'west_bonus_1_cummission_type' => $record->productCommission?->west_bonus_1_cummission_type,
            'west_bonus_2_cummission_type' => $record->productCommission?->west_bonus_2_cummission_type,
            'west_bonus_3_cummission_type' => $record->productCommission?->west_bonus_3_cummission_type,
            'west_bonus_1_cummission_value' => $record->productCommission?->west_bonus_1_cummission_value,
            'west_bonus_2_cummission_value' => $record->productCommission?->west_bonus_2_cummission_value,
            'west_bonus_3_cummission_value' => $record->productCommission?->west_bonus_3_cummission_value,

        ];

        $this->east_fixed_cummission_type = $record->productCommission?->east_fixed_cummission_type;
        $this->east_fixed_cummission_value = $record->productCommission?->east_fixed_cummission_value;
        $this->west_fixed_cummission_type = $record->productCommission?->west_fixed_cummission_type;
        $this->west_fixed_cummission_value = $record->productCommission?->west_fixed_cummission_value;
        $this->east_tier_1_cummission_type = $record->productCommission?->east_tier_1_cummission_type;
        $this->east_tier_1_cummission_value = $record->productCommission?->east_tier_1_cummission_value;
        $this->east_tier_2_cummission_type = $record->productCommission?->east_tier_2_cummission_type;
        $this->east_tier_2_cummission_value = $record->productCommission?->east_tier_2_cummission_value;
        $this->east_tier_3_cummission_type = $record->productCommission?->east_tier_3_cummission_type;
        $this->east_tier_3_cummission_value = $record->productCommission?->east_tier_3_cummission_value;
        $this->west_tier_1_cummission_type = $record->productCommission?->west_tier_1_cummission_type;
        $this->west_tier_1_cummission_value = $record->productCommission?->west_tier_1_cummission_value;
        $this->west_tier_2_cummission_type = $record->productCommission?->west_tier_2_cummission_type;
        $this->west_tier_2_cummission_value = $record->productCommission?->west_tier_2_cummission_value;
        $this->west_tier_3_cummission_type = $record->productCommission?->west_tier_3_cummission_type;
        $this->west_tier_3_cummission_value = $record->productCommission?->west_tier_3_cummission_value;
        $this->east_bonus_1_cummission_type = $record->productCommission?->east_bonus_1_cummission_type;
        $this->east_bonus_2_cummission_type = $record->productCommission?->east_bonus_2_cummission_type;
        $this->east_bonus_3_cummission_type = $record->productCommission?->east_bonus_3_cummission_type;
        $this->east_bonus_1_cummission_value = $record->productCommission?->east_bonus_1_cummission_value;
        $this->east_bonus_2_cummission_value = $record->productCommission?->east_bonus_2_cummission_value;
        $this->east_bonus_3_cummission_value = $record->productCommission?->east_bonus_3_cummission_value;
        $this->west_bonus_1_cummission_type = $record->productCommission?->west_bonus_1_cummission_type;
        $this->west_bonus_2_cummission_type = $record->productCommission?->west_bonus_2_cummission_type;
        $this->west_bonus_3_cummission_type = $record->productCommission?->west_bonus_3_cummission_type;
        $this->west_bonus_1_cummission_value = $record->productCommission?->west_bonus_1_cummission_value;
        $this->west_bonus_2_cummission_value = $record->productCommission?->west_bonus_2_cummission_value;
        $this->west_bonus_3_cummission_value = $record->productCommission?->west_bonus_3_cummission_value;


        $this->calculateEastNetEarnings();
        $this->calculateWestNetEarnings();
    }

    protected function calculateNetEarnings($price, $commissionType, $commissionValue): string
    {
        if (empty($commissionType) || empty($commissionValue)) {
            return '0';
        }
        if ($commissionType === 'percentage') {
            $commission = (int)$price * (int)$commissionValue / 100;
        } else {
            $commission = $commissionValue;
        }

        return number_format((int)$commission, 2);
    }

    protected function calculateEastNetEarnings()
    {
        $east_zone_price = ProductRelationPrice::find($this->record->id)->east_zone_price ?? 0;
        $this->east_net_earnings = $this->calculateNetEarnings(
            $east_zone_price,
            $this->east_fixed_cummission_type,
            $this->east_fixed_cummission_value
        );
    }

    protected function calculateWestNetEarnings()
    {
        $this->west_net_earnings = $this->calculateNetEarnings(
            $this->record->west_zone_price,
            $this->west_fixed_cummission_type,
            $this->west_fixed_cummission_value
        );
    }

    public function updatedEastFixedCummissionType()
    {
        $this->calculateEastNetEarnings();
    }

    public function updatedEastFixedCummissionValue()
    {
        $this->calculateEastNetEarnings();
    }

    public function updatedWestFixedCummissionType()
    {
        $this->calculateWestNetEarnings();
    }

    public function updatedWestFixedCummissionValue()
    {
        $this->calculateWestNetEarnings();
    }

    public function form(Form $form): Form
    {
        return $form->schema(function () {
            $price = ProductRelationPrice::find($this->record->id);
            return [
                Group::make()
                    ->visible(fn() => $this->record?->price_type == 'fixed')
                    ->schema([
                        Group::make()
                            ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                            ->schema([
                                Placeholder::make('Region'),
                                Placeholder::make('Price')->label('Price'),
                                Placeholder::make('comission_type')
                                    ->label('Commission Type'),
                                Placeholder::make('commission_amount')->label('Commission Amount'),
                                Placeholder::make('admin_earnings')->label('Admin Earnings(RM)'),
                            ])
                            ->columns(5),
                        Group::make()
                            ->visible(fn() => $this->record?->price_type == 'fixed')
                            ->schema(function () use ($price) {
                                return [
                                    Placeholder::make('east_region')
                                        ->label('East Malaysia'),
                                    Placeholder::make('price')->label('')
                                        ->content(function () use ($price) {
                                            return "RM " . $price?->east_zone_price ?? null;
                                        }),
                                    Select::make('east_fixed_cummission_type')
                                        ->hiddenLabel()
                                        ->rules(fn() => ['required_with:east_fixed_cummission_value'])
                                        ->options([
                                            'percentage' => 'Percentage',
                                            'flat' => 'Fixed',
                                        ])
                                        ->default(fn() => $this->form_data['east_fixed_cummission_type'])
                                        ->extraAttributes(['wire:model.live' => 'east_fixed_cummission_type']),
                                    TextInput::make('east_fixed_cummission_value')
                                        ->numeric()
                                        ->rules(function (Get $get) use ($price) {
                                            return $get('east_fixed_cummission_type') == 'percentage'
                                                ? ['numeric', 'min:0', 'max:100', 'required_with:east_fixed_cummission_type']
                                                : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_zone_price), 'required_with:east_fixed_cummission_type'];
                                        })
                                        ->maxValue($this->record?->east_zone_price)
                                        ->hiddenLabel()
                                        ->default(fn() => $this->form_data['east_fixed_cummission_value'])
                                        ->extraAttributes(['wire:model.live' => 'east_fixed_cummission_value']),
                                    Placeholder::make('admin_earnings')
                                        ->extraAttributes([
                                            'id' => 'east_zone_fixed_price_admin_earnings'
                                        ])
                                        ->label("")
                                        ->content(function () use ($price) {
                                            return "RM " . $this->calculateNetEarnings(
                                                $price?->east_zone_price,
                                                $this->east_fixed_cummission_type,
                                                $this->east_fixed_cummission_value
                                            );
                                        }),
                                ];
                            })
                            ->columns(5),
                        Group::make()
                            ->schema([
                                Placeholder::make('west_region')
                                    ->label('West Malaysia'),
                                Placeholder::make('price')->label('')
                                    ->content(function () use ($price) {
                                        return "RM " . $price?->west_zone_price;
                                    }),
                                Select::make('west_fixed_cummission_type')
                                    ->rules(['required_with:west_fixed_cummission_value'])
                                    ->hiddenLabel()
                                    ->options([
                                        'percentage' => 'Percentage',
                                        'flat' => 'Fixed',
                                    ])
                                    ->default(fn() => $this->form_data['west_fixed_cummission_type'])
                                    ->extraAttributes(['wire:model.live' => 'west_fixed_cummission_type']),
                                TextInput::make('west_fixed_cummission_value')
                                    ->numeric()
                                    ->rules(function (Get $get) use ($price) {
                                        return $get('west_fixed_cummission_type') == 'percentage'
                                            ? ['numeric', 'min:0', 'max:100', 'required_with:west_fixed_cummission_type']
                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_zone_price), 'required_with:west_fixed_cummission_type'];
                                    })
                                    ->hiddenLabel()
                                    ->default(fn() => $this->form_data['west_fixed_cummission_value'])
                                    ->extraAttributes(['wire:model.live' => 'west_fixed_cummission_value']),
                                Placeholder::make('admin_earnings')
                                    ->extraAttributes([
                                        'id' => 'west_zone_fixed_price_admin_earnings'
                                    ])
                                    ->label("")
                                    ->content(function () use ($price) {
                                        return "RM " . $this->calculateNetEarnings(
                                            $price?->west_zone_price,
                                            $this->west_fixed_cummission_type,
                                            $this->west_fixed_cummission_value
                                        );
                                    })
                            ])
                            ->columns(5),
                        \Filament\Forms\Components\Actions::make([
                            \Filament\Forms\Components\Actions\Action::make('Update')
                                ->label('Update')
                                ->action(function (Get $get, Set $set) {
                                    $this->form->getState();
                                    $data = [
                                        'west_fixed_cummission_type' => $get('west_fixed_cummission_type') ?: null,
                                        'west_fixed_cummission_value' => $get('west_fixed_cummission_value') ?: null,
                                        'east_fixed_cummission_type' =>  $get('east_fixed_cummission_type') ?: null,
                                        'east_fixed_cummission_value' => $get('east_fixed_cummission_value') ?: null,
                                        'product_relation_id' => $this->productRelationId,
                                    ];
                                    $this->record->productCommission()->updateOrCreate(['product_id' => $this->record->product_id, 'user_id' => $this->record->user_id], $data);

                                    Notification::make()
                                        ->title('Commission Updated Successfully')
                                        ->success()
                                        ->send();
                                })
                        ]),


                    ]),

                //Tier Pricing
                Group::make()
                    ->visible(fn() => $this->record?->price_type == 'tier')
                    ->schema([
                        Section::make('East Malaysia')
                            ->schema([
                                Group::make()
                                    ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 0px;margin-right: 0px;'])
                                    ->schema([
                                        Placeholder::make('Type')->label('Type'),
                                        Placeholder::make('Quantity'),
                                        Placeholder::make('Price'),
                                        Placeholder::make('Commission Type'),
                                        Placeholder::make('Commission Amount'),
                                        Placeholder::make('admin earnings'),
                                    ])
                                    ->columns(6),

                                Group::make()
                                    ->schema([
                                        // Tier 1
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->east_tier_1_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 1'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->east_tier_1_min_quantity;
                                                        $max = $price?->east_tier_1_max_quantity;
                                                        $nextMin = $price?->east_tier_2_min_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        if ($min > 0 && ($max < 1 || ($nextMin < 1 && $nextMin < 1))) {
                                                            return "$min above $foamName";
                                                        }
                                                        if ($min > 0 && $max > 0) {
                                                            return "$min - $max $foamName";
                                                        }

                                                        return "";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $price?->east_tier_1_base_price;
                                                    }),
                                                Select::make('east_tier_1_cummission_type')
                                                    ->rules(['required_with:east_tier_1_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['east_tier_1_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_1_cummission_type']),
                                                TextInput::make('east_tier_1_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) {
                                                        return $get('east_tier_1_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:east_tier_1_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($this->record?->east_tier_1_base_price), 'required_with:east_tier_1_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_1_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $this->calculateNetEarnings(
                                                            $price?->east_tier_1_base_price,
                                                            $this->east_tier_1_cummission_type,
                                                            $this->east_tier_1_cummission_value
                                                        );
                                                    }),
                                            ]),

                                        // Tier 2
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->east_tier_2_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 2'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->east_tier_2_min_quantity;
                                                        $max = $price?->east_tier_2_max_quantity;
                                                        $nextMin = $price?->east_tier_3_min_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        if ($min > 0 && ($max < 1 || ($nextMin < 1 && $nextMin < 1))) {
                                                            return "$min above $foamName";
                                                        }
                                                        if ($min > 0 && $max > 0) {
                                                            return "$min - $max $foamName";
                                                        }

                                                        return "";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(
                                                        function () use ($price) {
                                                            return "RM " . $price?->east_tier_2_base_price;
                                                        }
                                                    ),
                                                Select::make('east_tier_2_cummission_type')
                                                    ->rules(['required_with:east_tier_2_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['east_tier_2_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_2_cummission_type']),
                                                TextInput::make('east_tier_2_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) use ($price) {
                                                        return $get('east_tier_2_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:east_tier_2_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_tier_2_base_price), 'required_with:east_tier_2_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_2_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return  "RM " . $this->calculateNetEarnings(
                                                            $price?->east_tier_2_base_price,
                                                            $this->east_tier_2_cummission_type,
                                                            $this->east_tier_2_cummission_value
                                                        );
                                                    }),
                                            ]),

                                        // Tier 3
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->east_tier_3_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 3'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->east_tier_3_min_quantity;
                                                        $max = $price?->east_tier_3_max_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        return "$min - above $foamName";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $price?->east_tier_3_base_price;
                                                    }),
                                                Select::make('east_tier_3_cummission_type')
                                                    ->rules(['required_with:east_tier_3_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['east_tier_3_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_3_cummission_type']),
                                                TextInput::make('east_tier_3_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) use ($price) {
                                                        return $get('east_tier_3_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:east_tier_3_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_tier_3_base_price), 'required_with:east_tier_3_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'east_tier_3_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $this->calculateNetEarnings(
                                                            $price?->east_tier_3_base_price,
                                                            $this->east_tier_3_cummission_type,
                                                            $this->east_tier_3_cummission_value
                                                        );
                                                    }),
                                            ]),
                                    ]),
                            ]),

                        Section::make('West Malaysia')
                            ->schema([
                                Group::make()
                                    ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 0px;margin-right: 0px;'])
                                    ->schema([
                                        Placeholder::make('Type')
                                            ->label('Type'),
                                        Placeholder::make('Quantity'),
                                        Placeholder::make('Price'),
                                        Placeholder::make('Commission Type'),
                                        Placeholder::make('Commission Amount'),
                                        Placeholder::make('admin earnings'),
                                    ])
                                    ->columns(6),
                                Group::make()
                                    ->schema([
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->west_tier_1_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 1'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->west_tier_1_min_quantity;
                                                        $max = $price?->west_tier_1_max_quantity;
                                                        $nextMin = $price?->west_tier_2_min_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        if ($min > 0 && ($max < 1 || ($nextMin < 1 && $nextMin < 1))) {
                                                            return "$min above $foamName";
                                                        }
                                                        if ($min > 0 && $max > 0) {
                                                            return "$min - $max $foamName";
                                                        }

                                                        return "";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $price?->west_tier_1_base_price;
                                                    }),
                                                Select::make('west_tier_1_cummission_type')
                                                    ->rules(['required_with:west_tier_1_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['west_tier_1_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_1_cummission_type']),
                                                TextInput::make('west_tier_1_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) use ($price) {
                                                        return $get('west_tier_1_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:west_tier_1_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_tier_1_base_price), 'required_with:west_tier_1_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_1_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $this->calculateNetEarnings(
                                                            $price?->west_tier_1_base_price,
                                                            $this->west_tier_1_cummission_type,
                                                            $this->west_tier_1_cummission_value
                                                        );
                                                    }),
                                            ]),

                                        //tier 2
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->west_tier_2_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 2'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->west_tier_2_min_quantity;
                                                        $max = $price?->west_tier_2_max_quantity;
                                                        $nextMin = $price?->west_tier_3_min_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        if ($min > 0 && ($max < 1 || ($nextMin < 1 && $nextMin < 1))) {
                                                            return "$min above $foamName";
                                                        }
                                                        if ($min > 0 && $max > 0) {
                                                            return "$min - $max $foamName";
                                                        }

                                                        return "";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $price?->west_tier_2_base_price;
                                                    }),
                                                Select::make('west_tier_2_cummission_type')
                                                    ->rules(['required_with:west_tier_2_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['west_tier_2_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_2_cummission_type']),
                                                TextInput::make('west_tier_2_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) use ($price) {
                                                        return $get('west_tier_2_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:west_tier_2_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_tier_2_base_price), 'required_with:west_tier_2_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_2_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $this->calculateNetEarnings(
                                                            $price?->west_tier_2_base_price,
                                                            $this->west_tier_2_cummission_type,
                                                            $this->west_tier_2_cummission_value
                                                        );
                                                    }),
                                            ]),
                                        //tier 3
                                        Group::make()
                                            ->columns(6)
                                            ->visible(function () use ($price) {
                                                return $price?->west_tier_3_min_quantity > 0;
                                            })
                                            ->schema([
                                                Placeholder::make('Tier 3'),
                                                Placeholder::make('Quantity')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        $min = $price?->west_tier_3_min_quantity;
                                                        $foamName = $this->record->products->foam->name;
                                                        return "$min - above $foamName";
                                                    }),
                                                Placeholder::make('Price')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $price?->west_tier_3_base_price;
                                                    }),
                                                Select::make('west_tier_3_cummission_type')
                                                    ->rules(['required_with:west_tier_3_cummission_value'])
                                                    ->hiddenLabel()
                                                    ->options([
                                                        'percentage' => 'Percentage',
                                                        'flat' => 'Fixed',
                                                    ])
                                                    ->default(fn() => $this->form_data['west_tier_3_cummission_type'])
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_3_cummission_type']),
                                                TextInput::make('west_tier_3_cummission_value')
                                                    ->numeric()
                                                    ->rules(function (Get $get) use ($price) {
                                                        return $get('west_tier_3_cummission_type') == 'percentage'
                                                            ? ['numeric', 'min:0', 'max:100', 'required_with:west_tier_3_cummission_type']
                                                            : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_tier_3_base_price), 'required_with:west_tier_3_cummission_type'];
                                                    })
                                                    ->extraAttributes(['wire:model.live' => 'west_tier_3_cummission_value'])
                                                    ->hiddenLabel(),
                                                Placeholder::make('admin earnings')
                                                    ->label("")
                                                    ->content(function () use ($price) {
                                                        return "RM " . $this->calculateNetEarnings(
                                                            $price?->west_tier_3_base_price,
                                                            $this->west_tier_3_cummission_type,
                                                            $this->west_tier_3_cummission_value
                                                        );
                                                    })
                                            ])

                                    ])
                                // ->columns(6),

                            ]),
                        \Filament\Forms\Components\Actions::make([
                            \Filament\Forms\Components\Actions\Action::make('Update')
                                ->label('Update')
                                ->action(function (Get $get, Set $set) {
                                    $this->form->getState();
                                    $data = [
                                        'west_tier_1_cummission_type' =>  $get('west_tier_1_cummission_type') ?: null,
                                        'west_tier_2_cummission_type' =>  $get('west_tier_2_cummission_type') ?: null,
                                        'west_tier_3_cummission_type' =>  $get('west_tier_3_cummission_type') ?: null,
                                        'west_tier_1_cummission_value' => $get('west_tier_1_cummission_value') ?: null,
                                        'west_tier_2_cummission_value' => $get('west_tier_2_cummission_value') ?: null,
                                        'west_tier_3_cummission_value' => $get('west_tier_3_cummission_value') ?: null,
                                        'east_tier_1_cummission_type' =>  $get('east_tier_1_cummission_type') ?: null,
                                        'east_tier_2_cummission_type' =>  $get('east_tier_2_cummission_type') ?: null,
                                        'east_tier_3_cummission_type' =>  $get('east_tier_3_cummission_type') ?: null,

                                        'east_tier_1_cummission_value' => $get('east_tier_1_cummission_value') ?: null,
                                        'east_tier_2_cummission_value' => $get('east_tier_2_cummission_value') ?: null,
                                        'east_tier_3_cummission_value' => $get('east_tier_3_cummission_value') ?: null,
                                        'product_relation_id' => $this->productRelationId,

                                    ];

                                    $this->record->productCommission()->updateOrCreate(['product_id' => $this->record->product_id, 'user_id' => $this->record->user_id], $data);

                                    Notification::make()
                                        ->title('Commission Updated Successfully')
                                        ->success()
                                        ->send();
                                })
                        ]),
                    ]),

                //bonus price
                Group::make()
                    ->visible(fn() => $this->record->price_type == 'bonus')
                    ->schema([
                        Section::make('East Malaysia')
                            ->schema([
                                Group::make()
                                    ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 0px;margin-right: 0px;'])
                                    ->columns(6)
                                    ->schema([
                                        Placeholder::make('quantity'),
                                        Placeholder::make('free quantity'),
                                        Placeholder::make('price'),
                                        Placeholder::make('comission_type')
                                            ->label('Commission Type'),
                                        Placeholder::make('commission_amount')->label('Commission Amount'),
                                        Placeholder::make('net earnings')
                                    ]),
                                Group::make()
                                    ->columns(6)
                                    ->schema([
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_1_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_1_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->east_zone_price;
                                            }),
                                        Select::make('east_bonus_1_cummission_type')
                                            ->rules(['required_with:east_bonus_1_cummission_value'])
                                            ->hiddenLabel()
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['east_bonus_1_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_1_cummission_type']),
                                        TextInput::make('east_bonus_1_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('east_bonus_1_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:east_bonus_1_cummission_type']
                                                    : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_zone_price), 'required_with:east_bonus_1_cummission_type'];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_1_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->east_zone_price,
                                                    $this->east_bonus_1_cummission_type,
                                                    $this->east_bonus_1_cummission_value
                                                );
                                            }),
                                        // bonus 2
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_2_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_2_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->east_zone_price;
                                            }),
                                        Select::make('east_bonus_2_cummission_type')
                                            ->rules(['required_with:east_bonus_2_cummission_value'])
                                            ->hiddenLabel()
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['east_bonus_2_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_2_cummission_type']),
                                        TextInput::make('east_bonus_2_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('east_bonus_2_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:east_bonus_2_cummission_type']
                                                    : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_zone_price), 'required_with:east_bonus_2_cummission_type'];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_2_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->east_zone_price,
                                                    $this->east_bonus_2_cummission_type,
                                                    $this->east_bonus_2_cummission_value
                                                );
                                            }),

                                        // bonus 3
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_3_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->east_bonus_3_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->east_zone_price;
                                            }),
                                        Select::make('east_bonus_3_cummission_type')
                                            ->rules(['required_with:east_bonus_3_cummission_value'])
                                            ->hiddenLabel()
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['east_bonus_3_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_3_cummission_type']),
                                        TextInput::make('east_bonus_3_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('east_bonus_3_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:east_bonus_3_cummission_type']
                                                    : ['numeric', 'min:0', 'max:' . (int)floor($price?->east_zone_price), 'required_with:east_bonus_3_cummission_type'];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'east_bonus_3_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->east_zone_price,
                                                    $this->east_bonus_3_cummission_type,
                                                    $this->east_bonus_3_cummission_value
                                                );
                                            }),

                                    ]),
                            ]),

                        Section::make('West Malaysia')
                            ->schema([
                                Group::make()
                                    ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 0px;margin-right: 0px;'])
                                    ->columns(6)
                                    ->schema([
                                        Placeholder::make('quantity'),
                                        Placeholder::make('free quantity'),
                                        Placeholder::make('price'),
                                        Placeholder::make('comission_type')
                                            ->label('Commission Type'),
                                        Placeholder::make('commission_amount')->label('Commission Amount'),
                                        Placeholder::make('net earnings')
                                    ]),
                                Group::make()
                                    ->columns(6)
                                    ->schema([
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_1_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_1_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->west_zone_price;
                                            }),
                                        Select::make('west_bonus_1_cummission_type')
                                            ->rules(['required_with:west_bonus_1_cummission_value'])
                                            ->hiddenLabel()
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['west_bonus_1_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_1_cummission_type']),
                                        TextInput::make('west_bonus_1_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('west_bonus_1_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:west_bonus_1_cummission_type']
                                                    : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_zone_price), 'required_with:west_bonus_1_cummission_type'];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_1_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->west_zone_price,
                                                    $this->west_bonus_1_cummission_type,
                                                    $this->west_bonus_1_cummission_value
                                                );
                                            }),
                                        // bonus 2
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_2_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_2_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->west_zone_price;
                                            }),
                                        Select::make('west_bonus_2_cummission_type')
                                            ->rules(['required_with:west_bonus_2_cummission_value'])
                                            ->hiddenLabel()
                                            ->default('flat')
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['west_bonus_2_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_2_cummission_type']),
                                        TextInput::make('west_bonus_2_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('west_bonus_2_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:west_bonus_2_cummission_type']
                                                    : ['numeric', 'min:0', 'required_with:west_bonus_2_cummission_type', 'max:' . (int)floor($price?->west_zone_price)];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_2_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->west_zone_price,
                                                    $this->west_bonus_2_cummission_type,
                                                    $this->west_bonus_2_cummission_value
                                                );
                                            }),

                                        // bonus 3
                                        Placeholder::make('quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_3_quantity;
                                            }),
                                        Placeholder::make('free quantity')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return $price?->west_bonus_3_quantity_value;
                                            }),
                                        Placeholder::make('price')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $price?->west_zone_price;
                                            }),
                                        Select::make('west_bonus_3_cummission_type')
                                            ->rules(['required_with:west_bonus_3_cummission_value'])
                                            ->hiddenLabel()
                                            ->options([
                                                'percentage' => 'Percentage',
                                                'flat' => 'Fixed',
                                            ])
                                            ->default(fn() => $this->form_data['west_bonus_3_cummission_type'])
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_3_cummission_type']),
                                        TextInput::make('west_bonus_3_cummission_value')
                                            ->numeric()
                                            ->rules(function (Get $get) use ($price) {
                                                return $get('west_bonus_3_cummission_type') == 'percentage'
                                                    ? ['numeric', 'min:0', 'max:100', 'required_with:west_bonus_3_cummission_type']
                                                    : ['numeric', 'min:0', 'max:' . (int)floor($price?->west_zone_price), 'required_with:west_bonus_3_cummission_type'];
                                            })
                                            ->extraAttributes(['wire:model.live' => 'west_bonus_3_cummission_value'])
                                            ->hiddenLabel(),
                                        Placeholder::make('net earnings')
                                            ->label("")
                                            ->content(function () use ($price) {
                                                return "RM " . $this->calculateNetEarnings(
                                                    $price?->west_zone_price,
                                                    $this->west_bonus_3_cummission_type,
                                                    $this->west_bonus_3_cummission_value
                                                );
                                            }),

                                    ]),
                            ]),
                        \Filament\Forms\Components\Actions::make([
                            \Filament\Forms\Components\Actions\Action::make('Update')
                                ->label('Update')
                                ->action(function (Get $get, Set $set) {
                                    $this->form->getState();
                                    $data = [
                                        'west_bonus_1_cummission_type' =>  $get('west_bonus_1_cummission_type') ?: null,
                                        'west_bonus_2_cummission_type' =>  $get('west_bonus_2_cummission_type') ?: null,
                                        'west_bonus_3_cummission_type' =>  $get('west_bonus_3_cummission_type') ?: null,
                                        'west_bonus_1_cummission_value' => $get('west_bonus_1_cummission_value') ?: null,
                                        'west_bonus_2_cummission_value' => $get('west_bonus_2_cummission_value') ?: null,
                                        'west_bonus_3_cummission_value' => $get('west_bonus_3_cummission_value') ?: null,

                                        'east_bonus_1_cummission_type' =>  $get('east_bonus_1_cummission_type') ?: null,
                                        'east_bonus_2_cummission_type' =>  $get('east_bonus_2_cummission_type') ?: null,
                                        'east_bonus_3_cummission_type' =>  $get('east_bonus_3_cummission_type') ?: null,
                                        'east_bonus_1_cummission_value' => $get('east_bonus_1_cummission_value') ?: null,
                                        'east_bonus_2_cummission_value' => $get('east_bonus_2_cummission_value') ?: null,
                                        'east_bonus_3_cummission_value' => $get('east_bonus_3_cummission_value') ?: null,
                                        'product_relation_id' => $this->productRelationId,

                                    ];
                                    $this->record->productCommission()->updateOrCreate(['product_id' => $this->record->product_id, 'user_id' => $this->record->user_id], $data);

                                    Notification::make()
                                        ->title('Commission Updated Successfully')
                                        ->success()
                                        ->send();
                                })
                        ]),
                    ])
            ];
        });
    }

    public function render()
    {
        return view(static::$view);
    }
}
