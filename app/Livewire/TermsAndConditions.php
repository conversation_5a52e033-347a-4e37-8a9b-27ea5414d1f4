<?php

namespace App\Livewire;

use App\Models\TermCondition;
use Livewire\Component;
use Indianic\CmsPages\Models\CmsPage;

class TermsAndConditions extends Component
{
    public $pageData;

    public function mount()
    {
        $page = TermCondition::where('status',true)->first();
        $this->pageData = $page;
    }

    public function render()
    {
        return view('livewire.terms-and-conditions');
    }
}
