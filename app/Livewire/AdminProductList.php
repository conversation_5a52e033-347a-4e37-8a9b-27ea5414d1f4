<?php

namespace App\Livewire;

use Exception;
use App\Models\User;
use App\Models\Product;
use Filament\Forms\Get;
use App\Enums\LabelEnum;
use App\Models\Category;
use Filament\Pages\Page;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Livewire\Attributes\On;
use App\Actions\AdminPriceAction;
use App\Models\ProductCommission;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Models\ProductRelationPrice;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Support\Exceptions\Halt;
use App\Actions\AdminTablePriceAction;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\SelectFilter;
use Filament\Forms\Components\Placeholder;
use Indianic\Settings\Models\GlobalSettings;
use Illuminate\Validation\ValidationException;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Awcodes\TableRepeater\Components\TableRepeater;
use App\Filament\Tables\Filters\CategorySubcategoryFilter;

class AdminProductList extends Page implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public static string $view = 'livewire.admin-product-list';

    public ?int $id = null;

    public ?string $name = null;

    public ?int $commissionFlat = 0;

    public ?int $commissionPercentage = 0;

    public function mount($id): void
    {
        $this->id = $id;
        $this->name = User::find($id)->name;
        $globalCommission = GlobalSettings::whereIn('name', ['commission_percentage', 'commission_flat'])->pluck('value', 'name')->toArray();
        $this->commissionPercentage = $globalCommission['commission_percentage'] ?? 0;
        $this->commissionFlat = $globalCommission['commission_flat'] ?? 0;
    }

    public function getHeading(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return "Products of {$this->name}";
    }

    public $category = null;

    #[On('category-changed')]
    public function updateCategories($value) {}

    public function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Action')
            ->filters([
                \App\Filters\CategorySubCategoryFilter::make('ts')
            ])
            ->query(function () {
                $products = Product::with(['productData', 'category', 'subcategory'])->whereHas('productData', function ($query) {
                    $query->where(['user_id' => $this->id, 'admin_approval' => true, 'is_rejected' => false]);
                });
                return $products;
            })
            ->columns([
                TextColumn::make('id')->label('Product ID')->searchable()->sortable(),
                TextColumn::make('name')->label('Product Name')->searchable()->sortable(),
                TextColumn::make('category.name')->label('Category')->searchable()->sortable(),
                TextColumn::make('subcategory.name')->label('Subcategory')->searchable()->sortable(),
            ])
            ->actions([
                Action::make('commission_update')
                    ->modalHeading(function () {
                        return new HtmlString('<div class="flex flex-col gap-y-1">
    <div class="flex items-center gap-x-2 text-lg font-bold">Update Commission</div>
    <div class="flex items-center gap-x-2 font-light">Minimum Flat Commission Rate: ' . $this->commissionFlat . '</div>
    <div class="flex items-center gap-x-2 font-light">Minimum Percentage Commission Rate: ' . $this->commissionPercentage . '%</div>
</div>');
                    })
                    ->modalSubmitActionLabel('Update')
                    ->iconButton()
                    ->icon('heroicon-s-currency-dollar')
                    ->tooltip(function ($record) {
                        $productData = $record->productDataForPC($this->id);

                        if (!$productData) {
                            return "Product data not found for this user";
                        }

                        $priceData = ProductRelationPrice::where('product_relation_id', $productData->id)->first();
                        if (is_null($priceData)) {
                            return "Price not set for this product. Please set price first before updating commission";
                        }

                        return "Update Commission";
                    })
                    ->color(function ($record) {
                        $productData = $record->productDataForPC($this->id);
                        if (!$productData) {
                            return 'gray';
                        }
                        $priceData = ProductRelationPrice::where('product_relation_id', $productData->id)->first();
                        return is_null($priceData) ? 'gray' : 'primary';
                    })
                    ->extraAttributes(function ($record) {
                        $productData = $record->productDataForPC($this->id);
                        $isDisabled = false;

                        if (!$productData) {
                            $isDisabled = true;
                        } else {
                            $priceData = ProductRelationPrice::where('product_relation_id', $productData->id)->first();
                            if (is_null($priceData)) {
                                $isDisabled = true;
                            }
                        }

                        if ($isDisabled) {
                            return [
                                'style' => 'cursor: not-allowed; opacity: 0.5;',
                            ];
                        }

                        return [];
                    })
                    ->outlined()
                    ->modalWidth('7xl')
                    ->form(function ($record) {
                        $productData = $record->productDataForPC($this->id);

                        if (!$productData) {
                            return [];
                        }

                        $productCommission = $productData?->productCommission;
                        $priceData = ProductRelationPrice::where('product_relation_id', $productData?->id)->first();
                        if (is_null($priceData)) {
                            return [];
                        }
                        $priceType = $productData->price_type;
                        if ($priceType == 'fixed') {
                            return $this->getFixedPriceSchema($record, $productCommission, $priceData);
                        } elseif ($priceType == 'bonus') {
                            return $this->getBonusPriceSchema($record, $productCommission, $priceData);
                        } elseif ($priceType == 'tier') {
                            return $this->getTierPriceSchema($record, $productCommission, $priceData);
                        }
                    })
                    ->action(function ($data, $record) {
                        $productData = $record->productDataForPC($this->id);

                        // Check if action should be disabled
                        if (!$productData) {
                            Notification::make()
                                ->danger()
                                ->title('Product data not found for this user')
                                ->send();
                            return;
                        }

                        $priceData = ProductRelationPrice::where('product_relation_id', $productData->id)->first();
                        if (is_null($priceData)) {
                            Notification::make()
                                ->warning()
                                ->title('Price not set for this product')
                                ->body('Please set price first before updating commission')
                                ->send();
                            return;
                        }

                        $userId = $productData->user_id;
                        $priceType = $productData->price_type;
                        if ($priceType == 'fixed') {
                            $this->storeFixedPriceCommission($data, $productData, $userId);
                        }
                        if ($priceType == 'bonus') {
                            $this->storeBonusPriceCommission($data, $productData, $userId);
                        }
                        if ($priceType == 'tier') {
                            $this->storeTierPriceCommission($data, $productData, $userId);
                        }
                    }),

            ]);
    }

    public function getFixedPriceSchema($record, $productCommission, $priceData)
    {
        return [
            TableRepeater::make('fix_price_commission')
                ->deletable(false)
                ->reorderable(false)
                ->addable(false)
                ->minItems(2)
                ->maxItems(2)

                ->formatStateUsing(function () use ($priceData, $productCommission) {

                    return [
                        [
                            'region' => 'East Malaysia',
                            'region_type' => 'east',
                            'commission_type' => $productCommission?->east_fixed_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_fixed_cummission_value ?? null,
                            'price' => $priceData->east_zone_price ?? null,
                            'net_earning' => $priceData->east_fixed_cummission_value
                        ],
                        [
                            'region' => 'West Malaysia',
                            'region_type' => 'west',
                            'commission_type' => $productCommission?->west_fixed_cummission_type ?? null,
                            'commission_amount' => $productCommission?->west_fixed_cummission_value ?? null,
                            'price' => $priceData->west_zone_price ?? null,
                            'net_earning' => $priceData->west_fixed_cummission_value
                        ],
                    ];
                })
                ->headers([
                    Header::make('region')->label('Region'),
                    Header::make('commission_type')->label('Commission Type'),

                    Header::make('commission_amount')->label('Commission Amount'),
                    Header::make('Price'),
                    Header::make('Admin Earning'),
                ])
                ->schema([
                    Placeholder::make('region')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Select::make('commission_type')
                        ->options([
                            'percentage' => 'Percentage',
                            'flat' => 'Flat',
                        ])
                        ->formatStateUsing(function ($state, $component) use ($productCommission) {
                            return $state;
                        })
                        ->validationAttribute('commission type')
                        ->live()
                        ->disabled(function () {
                            $user = \Auth::user();
                            if ($user->hasRole('Super Admin')) {
                                return false; // Enable for Super Admin
                            }
                            if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                return false;
                            }
                            return true;
                        })
                        ->rules(['required'])
                        ->label(''),
                    TextInput::make('commission_amount')
                        ->rules(function (Get $get) use ($priceData) {
                            $isSuperAdmin = Auth::user()->hasRole('Super Admin');
                            $minFlat = $isSuperAdmin ? 0 : $this->commissionFlat ?? 0;
                            $minPercentage = $isSuperAdmin ? 0 : $this->commissionPercentage ?? 1;
                            if ($get('commission_type') === 'percentage') {
                                return ['required', 'numeric', 'min:' . $minPercentage, 'max:100'];
                            } else {
                                $region = $get('region');
                                $price = $region == 'East Malaysia' ? $priceData->east_zone_price : $priceData->west_zone_price;
                                return ['required', 'numeric', 'min:' . $minFlat, "max:" . $price];
                            }
                        })
                        ->validationAttribute('commission amount')
                        ->formatStateUsing(function ($component) use ($productCommission) {
                            $iteration = explode('.', $component->getStatePath())[1];
                            if ($iteration == 0) {
                                return $productCommission?->west_fixed_cummission_value;
                            }
                            return $productCommission?->east_fixed_cummission_value;
                        })
                        ->disabled(function () {
                            $user = \Auth::user();
                            if ($user->hasRole('Super Admin')) {
                                return false; // Enable for Super Admin
                            }
                            if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                return false;
                            }
                            return true;
                        })
                        ->live(),
                    Placeholder::make('price')
                        ->content(function (Get $get, $state) use ($priceData) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('admin_earning')
                        ->content(function (Get $get) use ($priceData) {
                            $state = $get('region');
                            if ($state === 'East Malaysia') {
                                $commission = $this->calculateNetEarnings($priceData?->east_zone_price, $get('commission_type'), $get('commission_amount'));
                                return 'RM ' . $commission;
                            }

                            if ($state === 'West Malaysia') {
                                $commission = $this->calculateNetEarnings($priceData?->west_zone_price, $get('commission_type'), $get('commission_amount'));
                                return 'RM ' . $commission;
                            }
                        })
                        ->live()
                        ->label(''),
                ])
        ];
    }

    protected function earning($price, $commission)
    {
        return $price - $commission;
    }

    public function getBonusPriceSchema($record, $productCommission, $priceData)
    {
        return [
            TableRepeater::make('bonus_price_commission_east')
                ->deletable(false)
                ->reorderable(false)
                ->addable(false)
                ->formatStateUsing(function () use ($priceData, $productCommission) {
                    $rows = [];

                    if (!is_null($priceData->east_bonus_1_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->east_bonus_1_quantity,
                            'value' => $priceData->east_bonus_1_quantity_value,
                            'commission_type' => $productCommission?->east_bonus_1_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_bonus_1_cummission_value ?? null,
                            'price' => $priceData->east_zone_price,
                        ];
                    }

                    if (!is_null($priceData->east_bonus_2_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->east_bonus_2_quantity,
                            'value' => $priceData->east_bonus_2_quantity_value,
                            'commission_type' => $productCommission?->east_bonus_2_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_bonus_2_cummission_value ?? null,
                            'price' => $priceData->east_zone_price,
                        ];
                    }

                    if (!is_null($priceData->east_bonus_3_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->east_bonus_3_quantity,
                            'value' => $priceData->east_bonus_3_quantity_value,
                            'commission_type' => $productCommission?->east_bonus_3_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_bonus_3_cummission_value ?? null,
                            'price' => $priceData->east_zone_price,
                        ];
                    }

                    return $rows;
                })
                ->headers([
                    Header::make('region_text')->label('Quantity'),
                    Header::make('value')->label('Free Quantity'),
                    Header::make('commission_type')->label('Commission Type'),
                    Header::make('commission_amount')->label('Commission Amount'),
                    Header::make('Price'),
                    Header::make('Admin Earning'),
                ])
                ->schema([
                    Placeholder::make('quantity')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('value')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Select::make('commission_type')
                        ->options([
                            'percentage' => 'Percentage',
                            'flat' => 'Flat',
                        ])
                        ->formatStateUsing(function ($state, $component) use ($productCommission) {

                            $iteration = explode('.', $component->getStatePath())[1];
                            if ($iteration == 0) {
                                return $state;
                            }
                            return $state;
                        })
                        ->validationAttribute('commission type')
                        ->live()
                        ->rules(['required'])
                        ->label(''),
                    TextInput::make('commission_amount')
                        ->rules(function (Get $get) use ($priceData) {
                            $isSuperAdmin = Auth::user()->hasRole('Super Admin');
                            $minFlat = $isSuperAdmin ? 0 : $this->commissionFlat ?? 0;
                            $minPercentage = $isSuperAdmin ? 0 : $this->commissionPercentage ?? 1;
                            if ($get('commission_type') === 'percentage') {
                                return ['required', 'numeric', 'min:' . $minPercentage, 'max:100'];
                            } else {
                                $price = $get('price');
                                return ['required', 'numeric', 'min:' . $minFlat, "max:" . $price];
                            }
                        })
                        ->validationAttribute('commission amount')
                        ->formatStateUsing(function ($component) use ($productCommission) {
                            $iteration = explode('.', $component->getStatePath())[1];
                            if ($iteration == 0) {
                                // dd($productCommission?->west_fixed_cummission_value);
                                return $productCommission?->west_fixed_cummission_value;
                            }
                            return $productCommission?->east_fixed_cummission_value;
                        })
                        ->live(),
                    Placeholder::make('price')
                        ->content(function (Get $get, $state) use ($priceData) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('admin_earning')
                        ->content(function (Get $get) use ($priceData) {
                            $commission = $this->calculateNetEarnings($priceData?->east_zone_price, $get('commission_type'), $get('commission_amount'));
                            return 'RM ' . $commission;
                        })

                        ->label(''),
                ]),

            TableRepeater::make('bonus_price_commission_west')
                ->deletable(false)
                ->reorderable(false)
                ->addable(false)
                ->formatStateUsing(function () use ($priceData, $productCommission) {
                    $rows = [];

                    if (!is_null($priceData->west_bonus_1_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->west_bonus_1_quantity,
                            'value' => $priceData->west_bonus_1_quantity_value,
                            'commission_type' => $productCommission?->west_bonus_1_cummission_type ?? null,
                            'commission_amount' => $productCommission?->west_bonus_1_cummission_value ?? null,
                            'price' => $priceData->west_zone_price,
                        ];
                    }

                    if (!is_null($priceData->west_bonus_2_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->west_bonus_2_quantity,
                            'value' => $priceData->west_bonus_2_quantity_value,
                            'commission_type' => $productCommission?->west_bonus_2_cummission_type ?? null,
                            'percentage',
                            'commission_amount' => $productCommission?->west_bonus_2_cummission_value ?? null,
                            'price' => $priceData->west_zone_price,
                        ];
                    }

                    if (!is_null($priceData->east_bonus_3_quantity_value)) {
                        $rows[] = [
                            'quantity' => $priceData->east_bonus_3_quantity,
                            'value' => $priceData->east_bonus_3_quantity_value,
                            'commission_type' => $productCommission?->east_bonus_3_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_bonus_3_cummission_value ?? null,
                            'price' => $priceData->west_zone_price,
                        ];
                    }

                    return $rows;
                })
                ->headers([
                    Header::make('region_text')->label('Quantity'),
                    Header::make('value')->label('Free Quantity'),
                    Header::make('commission_type')->label('Commission Type'),
                    Header::make('commission_amount')->label('Commission Amount'),
                    Header::make('Price'),
                    Header::make('Admin Earning'),
                ])
                ->schema([
                    Placeholder::make('quantity')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('value')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Select::make('commission_type')
                        ->options([
                            'percentage' => 'Percentage',
                            'flat' => 'Flat',
                        ])
                        ->formatStateUsing(function ($state, $component) use ($productCommission) {

                            $iteration = explode('.', $component->getStatePath())[1];
                            if ($iteration == 0) {
                                return $state;
                            }
                            return $state;
                        })
                        ->validationAttribute('commission type')
                        ->live()
                        ->rules(['required'])
                        ->label(''),
                    TextInput::make('commission_amount')
                        ->rules(function (Get $get) use ($priceData) {
                            $isSuperAdmin = Auth::user()->hasRole('Super Admin');
                            $minFlat = $isSuperAdmin ? 0 : $this->commissionFlat ?? 0;
                            $minPercentage = $isSuperAdmin ? 0 : $this->commissionPercentage ?? 1;
                            if ($get('commission_type') === 'percentage') {
                                return ['required', 'numeric', 'min:' . $minPercentage, 'max:100'];
                            } else {
                                $region = $get('region');
                                $price = $region == 'East Malaysia' ? $priceData->east_zone_price : $priceData->west_zone_price;
                                return ['required', 'numeric', 'min:' . $minFlat, "max:" . $price];
                            }
                        })
                        ->validationAttribute('commission amount')
                        ->live(),
                    Placeholder::make('price')
                        ->content(function (Get $get, $state, $component) use ($priceData) {
                            return $priceData->west_zone_price;
                        })
                        ->label(''),
                    Placeholder::make('admin_earning')
                        ->content(function (Get $get) use ($priceData) {

                            $commission = $this->calculateNetEarnings($priceData?->east_zone_price, $get('commission_type'), $get('commission_amount'));
                            return 'RM ' . $commission;
                        })

                        ->label(''),
                ])
        ];
    }

    public function getTierPriceSchema($record, $productCommission, $priceData)
    {
        return [
            TableRepeater::make('tier_price_commission_east')
                ->label('Tier Price Commission East')
                ->deletable(false)
                ->reorderable(false)
                ->addable(false)
                ->formatStateUsing(function () use ($priceData, $productCommission) {
                    $rows = [];

                    if (!is_null($priceData->east_tier_1_min_quantity)) {
                        $maxQty = !empty($priceData->east_tier_1_max_quantity) || $priceData->east_tier_1_max_quantity !== 0 ? "- $priceData->east_tier_1_max_quantity" : 'and above';
                        $rows[] = [
                            'type' => 'Tier 1',
                            'value' => $priceData->east_tier_1_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->east_tier_1_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_tier_1_cummission_value ?? null,
                            'price' => $priceData->east_tier_1_base_price,
                        ];
                    }

                    if (!is_null($priceData->east_tier_2_min_quantity)) {
                        $maxQty = !empty($priceData->east_tier_2_max_quantity) || $priceData->east_tier_2_max_quantity !== 0 ? "- $priceData->east_tier_2_max_quantity" : ' and above';
                        $rows[] = [
                            'type' => "Tier 2",
                            'value' => $priceData->east_tier_2_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->east_tier_2_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_tier_2_cummission_value ?? null,
                            'price' => $priceData->east_tier_2_base_price,
                        ];
                    }

                    if (!is_null($priceData->east_tier_3_min_quantity)) {
                        $maxQty = !empty($priceData->east_tier_3_max_quantity) || $priceData->east_tier_3_max_quantity !== 0 ? "- $priceData->east_tier_3_max_quantity" : ' and above';
                        $rows[] = [
                            'type' => "Tier 3",
                            'value' => $priceData->east_tier_3_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->east_tier_3_cummission_type ?? null,
                            'commission_amount' => $productCommission?->east_tier_3_cummission_value ?? null,
                            'price' => $priceData->east_tier_3_base_price,
                        ];
                    }

                    return $rows;
                })
                ->headers([
                    Header::make('tier')->label('Tier'),
                    Header::make('value')->label('Free Quantity'),
                    Header::make('commission_type')->label('Commission Type'),
                    Header::make('commission_amount')->label('Commission Amount'),
                    Header::make('Price'),
                    Header::make('Admin Earning'),
                ])
                ->schema([
                    Placeholder::make('type')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('value')
                        ->content(function ($state) {
                            return $state;
                        })
                        ->label(''),
                    Select::make('commission_type')
                        ->options([
                            'percentage' => 'Percentage',
                            'flat' => 'Flat',
                        ])
                        ->default('select')
                        ->formatStateUsing(function ($state, $component) use ($productCommission) {

                            $iteration = explode('.', $component->getStatePath())[1];
                            if ($iteration == 0) {
                                return $state;
                            }
                            return $state;
                        })
                        ->validationAttribute('commission type')
                        ->live()
                        ->rules(['required'])
                        ->label(''),
                    TextInput::make('commission_amount')
                        ->rules(function (Get $get) use ($priceData) {
                            $isSuperAdmin = Auth::user()->hasRole('Super Admin');
                            $minFlat = $isSuperAdmin ? 0 : $this->commissionFlat ?? 0;
                            $minPercentage = $isSuperAdmin ? 0 : $this->commissionPercentage ?? 1;
                            if ($get('commission_type') === 'percentage') {
                                return ['required', 'numeric', 'min:' . $minPercentage, 'max:100'];
                            } else {
                                $region = $get('region');
                                $price = $get('price');
                                return ['required', 'numeric', 'min:' . $minFlat, "max:" . $price];
                            }
                        })
                        ->validationAttribute('commission amount')
                        ->live(),
                    Placeholder::make('price')
                        ->content(function (Get $get, $state) use ($priceData) {
                            return $state;
                        })
                        ->label(''),
                    Placeholder::make('admin_earning')
                        ->content(function (Get $get, $component) use ($priceData) {
                            $iteration = $iteration = explode('.', $component->getStatePath())[1];
                            $price = 0;
                            if ($iteration == 0) {
                                $price = $priceData?->east_tier_1_base_price;
                            }
                            if ($iteration == 1) {
                                $price = $priceData?->east_tier_2_base_price;
                            }
                            if ($iteration == 2) {
                                $price = $priceData?->east_tier_3_base_price;
                            }
                            $commission = $this->calculateNetEarnings($price, $get('commission_type'), $get('commission_amount'));
                            return 'RM ' . $commission;
                        })

                        ->label(''),
                ]),

            TableRepeater::make('tier_price_commission_west')
                ->deletable(false)
                ->reorderable(false)
                ->addable(false)
                ->formatStateUsing(function () use ($priceData, $productCommission) {
                    $rows = [];

                    if (!is_null($priceData->west_tier_1_min_quantity)) {
                        $maxQty = !empty($priceData->west_tier_1_max_quantity) || $priceData->west_tier_1_max_quantity !== 0 ? "- $priceData->west_tier_1_max_quantity" : 'and above';
                        $rows[] = [
                            'type' => 'Tier 1',
                            'value' => $priceData->west_tier_1_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->west_tier_1_cummission_type ?? null,
                            'commission_amount' => $productCommission?->west_tier_1_cummission_value ?? null,
                            'price' => $priceData->west_tier_1_base_price,
                        ];
                    }

                    if (!is_null($priceData->west_tier_2_min_quantity)) {
                        $maxQty = !empty($priceData->west_tier_2_max_quantity) || $priceData->west_tier_2_max_quantity !== 0 ? "- $priceData->west_tier_2_max_quantity" : 'and above';
                        // dd($maxQty);
                        $rows[] = [
                            'type' => "Tier 2",
                            'value' => $priceData->west_tier_2_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->west_tier_2_cummission_type ?? null,
                            'commission_amount' => $productCommission?->west_tier_2_cummission_value ?? null,
                            'price' => $priceData->west_tier_2_base_price,
                        ];
                    }

                    if (!is_null($priceData->west_tier_3_min_quantity)) {

                        $maxQty = !empty($priceData->west_tier_3_max_quantity) ? "- $priceData->west_tier_3_max_quantity" : ' and above';

                        $rows[] = [
                            'type' => "Tier 3",
                            'value' => $priceData->west_tier_3_min_quantity . $maxQty,
                            'commission_type' => $productCommission?->west_tier_3_cummission_type ?? null,
                            'commission_amount' => $productCommission?->west_tier_3_cummission_value ?? null,
                            'price' => $priceData->west_tier_3_base_price,
                        ];
                    }

                    return $rows;
                })
                ->headers([
                    Header::make('tier')->label('Tier'),
                    Header::make('value')->label('Free Quantity'),
                    Header::make('commission_type')->label('Commission Type'),
                    Header::make('commission_amount')->label('Commission Amount'),
                    Header::make('Price'),
                    Header::make('Admin Earning'),
                ])
                ->schema(function () use ($priceData, $productCommission) {

                    return [
                        Placeholder::make('type')
                            ->content(function ($state) {
                                return $state;
                            })
                            ->label(''),
                        Placeholder::make('value')
                            ->content(function ($state) {
                                return $state;
                            })
                            ->label(''),
                        Select::make('commission_type')
                            ->options([
                                'percentage' => 'Percentage',
                                'flat' => 'Flat',
                            ])
                            ->formatStateUsing(function ($state, $component) use ($productCommission) {

                                $iteration = explode('.', $component->getStatePath())[1];
                                if ($iteration == 0) {
                                    return $state;
                                }
                                return $state;
                            })
                            ->validationAttribute('commission type')
                            ->live()
                            ->rules(['required'])
                            ->label(''),
                        TextInput::make('commission_amount')
                            ->rules(function (Get $get) use ($priceData) {
                                $isSuperAdmin = Auth::user()->hasRole('Super Admin');
                                $minFlat = $isSuperAdmin ? 0 : $this->commissionFlat ?? 0;
                                $minPercentage = $isSuperAdmin ? 0 : $this->commissionPercentage ?? 1;
                                if ($get('commission_type') === 'percentage') {
                                    return ['required', 'numeric', 'min:' . $minPercentage, 'max:100'];
                                } else {
                                    $region = $get('region');
                                    $price = $get('price');
                                    return ['required', 'numeric', 'min:' . $minFlat, "max:" . $price];
                                }
                            })
                            ->validationAttribute('commission amount')
                            ->live(),
                        Placeholder::make('price')
                            ->content(function (Get $get, $state, $component) use ($priceData) {
                                return $state;
                            })
                            ->label(''),
                        Placeholder::make('admin_earning')
                            ->content(function (Get $get, $component) use ($priceData) {
                                $iteration = explode('.', $component->getStatePath())[1];
                                $price = 0;
                                if ($iteration == 0) {
                                    $price = $priceData?->west_tier_1_base_price;
                                }
                                if ($iteration == 1) {
                                    $price = $priceData?->west_tier_2_base_price;
                                }
                                if ($iteration == 2) {
                                    $price = $priceData?->west_tier_3_base_price;
                                }
                                $commission = $this->calculateNetEarnings($price, $get('commission_type'), $get('commission_amount'));
                                return 'RM ' . $commission;
                            })

                            ->label(''),
                    ];
                })
        ];
    }

    protected function calculateNetEarnings($price, $commissionType, $commissionValue): string
    {
        // dd($price, $commissionType, $commissionValue);
        if (empty($commissionType) || empty($commissionValue)) {
            return '0';
        }
        if ($commissionType === 'percentage') {
            $commission = (int)$price * (int)$commissionValue / 100;
        } else {
            $commission = $commissionValue;
        }

        return number_format((int)$commission, 2);
    }

    protected function storeFixedPriceCommission($data, $productData, $userId)
    {
        $eastData = $data['fix_price_commission'][0];
        $westData = $data['fix_price_commission'][1];
        $dataToStore = [];
        $dataToStore['east_fixed_cummission_type'] = $eastData['commission_type'];
        $dataToStore['east_fixed_cummission_value'] = $eastData['commission_amount'];
        $dataToStore['west_fixed_cummission_type'] = $westData['commission_type'];
        $dataToStore['west_fixed_cummission_value'] = $westData['commission_amount'];
        $dataToStore['product_relation_id'] = $productData->id;
        $dataToStore['product_id'] = $productData->product_id;
        $dataToStore['user_id'] = $userId;
        ProductCommission::updateOrCreate(['product_id' => $productData->product_id, 'user_id' => $userId], $dataToStore);
        $this->sendNotification();
    }

    protected function storeBonusPriceCommission($data, $productData, $userId)
    {
        $eastData = $data['bonus_price_commission_east'];
        $westData = $data['bonus_price_commission_west'];
        $dataToStore = [];
        foreach ($eastData as $key => $value) {
            $dataToStore['east_bonus_' . ($key + 1) . '_cummission_type'] = $value['commission_type'];
            $dataToStore['east_bonus_' . ($key + 1) . '_cummission_value'] = $value['commission_amount'];
        }
        foreach ($westData as $key => $value) {
            $dataToStore['west_bonus_' . ($key + 1) . '_cummission_type'] = $value['commission_type'];
            $dataToStore['west_bonus_' . ($key + 1) . '_cummission_value'] = $value['commission_amount'];
        }
        $dataToStore['product_relation_id'] = $productData->id;
        $dataToStore['product_id'] = $productData->product_id;
        $dataToStore['user_id'] = $userId;
        ProductCommission::updateOrCreate(['product_id' => $productData->product_id, 'user_id' => $userId], $dataToStore);
        $this->sendNotification();
    }

    protected function storeTierPriceCommission($data, $productData, $userId)
    {
        $eastData = $data['tier_price_commission_east'];
        $westData = $data['tier_price_commission_west'];
        $dataToStore = [];
        foreach ($eastData as $key => $value) {
            $dataToStore['east_tier_' . ($key + 1) . '_cummission_type'] = $value['commission_type'];
            $dataToStore['east_tier_' . ($key + 1) . '_cummission_value'] = $value['commission_amount'];
        }
        foreach ($westData as $key => $value) {
            $dataToStore['west_tier_' . ($key + 1) . '_cummission_type'] = $value['commission_type'];
            $dataToStore['west_tier_' . ($key + 1) . '_cummission_value'] = $value['commission_amount'];
        }
        $dataToStore['product_relation_id'] = $productData->id;
        $dataToStore['product_id'] = $productData->product_id;
        $dataToStore['user_id'] = $userId;
        ProductCommission::updateOrCreate(['product_id' => $productData->product_id, 'user_id' => $userId], $dataToStore);
        $this->sendNotification();
    }
    protected function sendNotification()
    {
        return Notification::make()
            ->title('Commission Updated')
            ->body('Commission has been updated successfully.')
            ->success()
            ->send();
    }
}
