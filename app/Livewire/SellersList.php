<?php

namespace App\Livewire;

use App\Actions\AdminPriceAction;
use App\Actions\AdminStockUpdateAction;
use App\Models\User;
use Filament\Forms\Get;
use Livewire\Component;
use App\Models\PcDetail;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\ProductRelation;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Models\ProductRelationPrice;
use Filament\Forms\Components\Group;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use App\Filament\Admin\Resources\ProductResource;

class SellersList extends Component implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    public $record;
    public function mount($record)
    {
        $this->record = $record;
    }
    public function render()
    {
        return view('livewire.sellers-list');
    }

    public function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Action')
            ->query(function () {
                $userIdsQuery = $this->record->productData() // Access the relationship query builder
                    ->select('user_id')
                    // ->where('product_id', $record->id) // Implicit in relationship
                    ->distinct();

                // Fetch user names using the subquery in whereIn
                return User::whereIn('id', $userIdsQuery);
            })
            ->columns([
                TextColumn::make(name: 'name')->label(label: 'Name')->searchable(),
                ViewColumn::make('price')
                    ->label('Price & Stock')
                    ->view('custom-view.table-price')

            ])
            ->filters([
                // ...  
            ])
            ->actions([
                AdminPriceAction::make(product: $this->record),
                AdminStockUpdateAction::make(product: $this->record),

            ])
            ->bulkActions([
                // ...  
            ]);
    }

    public function priceUpdateAction()
    {
        return Action::make('price_update')
            ->action(function ($record) {});
    }
}
