<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\ClinicPharmaSupplier;
use Filament\Notifications\Notification;

class SuppliersTable extends Component
{
    public $suppliers;

    public function mount()
    {
        $this->suppliers = ClinicPharmaSupplier::with('pcDetail.pcDetails')->get();
    }

    public function deleteSupplier($supplierId)
    {
        $supplier = ClinicPharmaSupplier::find($supplierId);

        if ($supplier) {
            $supplier->delete();
            
            Notification::make()
                ->title('Supplier deleted successfully')
                ->success()
                ->send();

            // Refresh the suppliers list
            $this->suppliers = ClinicPharmaSupplier::with('pcDetail.pcDetails')->get();
        }
    }

    public function render()
    {
        return view('clinic.suppliers-table', [
            'suppliers' => $this->suppliers
        ]);
    }
}
