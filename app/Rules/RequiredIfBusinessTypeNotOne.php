<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use App\Models\BusinessType;

class RequiredIfBusinessTypeNotOne implements Rule
{
    protected $businessTypeId;

    public function __construct($businessTypeId)
    {
        try {
            $this->businessTypeId = decryptParam($businessTypeId);
        } catch (\Exception $e) {
            $this->businessTypeId = null;
        }
    }

    public function passes($attribute, $value)
    {
        $isSoleProprietary = BusinessType::where('id', $this->businessTypeId)
            ->where('key', 'sole-proprietary')
            ->exists(); 

        if ($isSoleProprietary) {
            return true;
        }

        return !empty($value); 
    }

    public function message()
    {
        return 'The :attribute field is required.';
    }
}
