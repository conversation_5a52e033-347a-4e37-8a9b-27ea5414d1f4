<?php
namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use App\Models\User;

class ActiveUserEmail implements Rule
{
    public function passes($attribute, $value)
    {
        $user = User::withTrashed()->with('roles')->where('email', $value)->first();
        \Log::error(json_encode($user));
         if (!$user) {
            $this->message = __('validation.forgot_password.email_not_found');
            return false;
        }

        if ($user->trashed()) {
            $this->message = __('validation.forgot_password.user_deleted');
            return false;
        }

        if (!$user->status) {
            $this->message = __('validation.forgot_password.user_inactive');
            return false;
        }

        $clinicRoleId = 3;
        if (!$user->roles->contains('id', $clinicRoleId)) {
            $this->message = __('validation.forgot_password.user_facility_allow');
            return false;
        }

        return true;
    }

    public function message()
    {
        return $this->message ?? __('validation.forgot_password.email_not_found');
    }
}
