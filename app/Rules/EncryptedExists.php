<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class EncryptedExists implements Rule
{
    protected $table;

    protected $column;

    public function __construct($table, $column = 'id')
    {
        $this->table = $table;
        $this->column = $column;
    }

    public function passes($attribute, $value)
    {
        try {
            $decryptedId = decryptParam($value);

            return DB::table($this->table)->where($this->column, $decryptedId)->exists();
        } catch (\Illuminate\Contracts\Encryption\DecryptException $e) {
            return false;
        }
    }

    public function message()
    {
        return 'The :attribute is invalid or does not exist.';
    }
}
