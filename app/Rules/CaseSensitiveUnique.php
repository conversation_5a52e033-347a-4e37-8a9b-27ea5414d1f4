<?php
namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Database\Eloquent\Model;

class CaseSensitiveUnique implements Rule
{
    protected $model;
    protected $column;
    protected $ignoreId;

    public function __construct(string $model, string $column, $ignoreId = null)
    {
        $this->model = $model;
        $this->column = $column;
        $this->ignoreId = $ignoreId;
    }

    public function passes($attribute, $value)
    {
        $query = $this->model::where($this->column, 'ILIKE', $value);

        if ($this->ignoreId) {

            $query->where('id', '!=', $this->ignoreId);
        }
// dd($query->exists());
        return !$query->exists();
    }

    public function message()
    {
        // return 'The foam name has already been taken.';
    }
}
