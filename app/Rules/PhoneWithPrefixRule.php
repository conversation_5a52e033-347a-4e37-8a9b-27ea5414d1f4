<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Log;

class PhoneWithPrefixRule implements Rule
{
    protected $errorKey = 'invalid';

    public function passes($attribute, $value)
    {
        Log::info('PhoneWithPrefixRule passes called', [
            'attribute' => $attribute,
            'value' => $value,
        ]);
        if (!is_array($value)) {
            return true;
        }
        
        if (array_key_exists('prefix', $value) && empty($value['prefix'])) {
            return true;
        }
        
        if (!array_key_exists('number', $value) || empty($value['number'])) {
            return true;
        }
        // if (empty($value['prefix'])) {
        //     $this->errorKey = 'prefix';
        //     return false;
        // }
        // if (empty($value['number'])) {
        //     $this->errorKey = 'number';
        //     return false;
        // }
        /* if (!preg_match('/^\d{11}$/', $value['number'])) {
            $this->errorKey = 'digits';
            return false;
        } */

        $number = preg_replace('/\D/', '', $value['number']);

        // Check if the number has between 9 and 10 digits
        if (strlen($number) < 7 || strlen($number) > 10) {
            $this->errorKey = 'digits_between';
            return false;
        }
            
        return true;
    }

    public function message()
    {
        return match ($this->errorKey) {
            'prefix' => 'The Landline Prefix is required.',
            'number' => 'The Landline number is required.',
            default => 'The Landline number field must be between 7 and 10 digits.',
        };
    }
}
