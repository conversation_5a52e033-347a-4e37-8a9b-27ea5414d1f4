<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use App\Models\ZipCode; // Adjust this to your ZipCode model location

class CustomPostalCodeValidation implements Rule
{
    protected $cityId;
    protected $value; 

    public function __construct($cityId)
    {
        $this->cityId = $cityId;
    }

    public function passes($attribute, $value)
    {
        $this->value = $value;
        // Check if the postal code is valid for the given city_id
        $isValid = ZipCode::where('city_id', decryptParam($this->cityId))
                         ->where('code', $value)
                         ->exists();

        return $isValid;
    }

    public function message()
    {
        return __('api.clinic.postal_code_invalid',['code' => $this->value]);
    }
}
