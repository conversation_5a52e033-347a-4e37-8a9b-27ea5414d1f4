<?php

namespace App\Register;

use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Response\PcResponse;
use Filament\Actions\Action;
use App\Models\TermCondition;
use Filament\Facades\Filament;
use Filament\Pages\Auth\Register;
use Illuminate\Support\HtmlString;
use Filament\Events\Auth\Registered;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Cache;
use Indianic\CmsPages\Models\CmsPage;
use Filament\Forms\Components\Checkbox;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Components\Placeholder;
use Illuminate\Contracts\Support\Htmlable;
use DanHarrin\LivewireRateLimiting\Exceptions\TooManyRequestsException;

class PcRegister extends Register
{

    public bool $termsConditions = false;

    public function getTitle(): string
    {
        return 'Register as PS';
    }

    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        Group::make()->schema([
                            // $this->getNameFormComponent(),
                            $this->getEmailFormComponent(),
                            $this->getPasswordFormComponent(),
                            $this->getPasswordConfirmationFormComponent(),
                            Checkbox::make('terms_conditions')
                                ->label(function () {
                                    return new HtmlString(view('custom-view.auth.terms-conditions', [
                                        'pageData' => TermCondition::where('status', true)->first(),
                                        'policy' => CmsPage::where('slug', 'privacy-policy')->firstOrFail(),
                                    ])->render());
                                })
                                ->live()
                                ->afterStateUpdated(function ($state) {
                                    $this->termsConditions = $state;
                                }),
                            Group::make()->schema([
                                // $this->getRememberFormComponent(),
                                // $this->getResetPasswordFormComponent(),
                            ])
                                ->extraAttributes(['class' => 'justify-end'])
                                ->columns(2),
                        ])->extraAttributes(['class' => 'justify-end']),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    public function getHeading(): string | Htmlable
    {
        return new HtmlString('<div class="" style="font-size:1.5rem">Create New Account</div>');
    }

    public function loginAction(): Action
    {
        return Action::make('login')
            ->link()
            ->label('Login')
            ->url(filament()->getLoginUrl());
    }

    public function getRegisterFormAction(): Action
    {
        return Action::make('register')
            ->label('Register')
            ->disabled(fn() => !$this->termsConditions)
            ->submit('register');
    }

    protected function getTermsConditionsFormComponent(): Component
    {
        $pageData = TermCondition::where('status', true)->first();
        $policy = CmsPage::where('slug', 'privacy-policy')->firstOrFail();
        return Checkbox::make('terms_conditions')
            ->live()
            ->afterStateUpdated(function ($state) {
                $this->termsConditions = $state;
            })
            ->view('custom-view.auth.terms-conditions')
            ->viewData([
                'pageData' => $pageData,
                'policy' => $policy,
            ]);
    }

    protected function getNameFormComponent(): Component
    {
        return TextInput::make('name')
            ->label(new HtmlString('Name <span style="color: red;">*</span>'))
            ->validationAttribute('name')
            ->rules(['required', 'string', 'max:255']);
    }

    protected function getPasswordConfirmationFormComponent(): Component
    {
        return TextInput::make('password_confirmation')
            ->label(new HtmlString('Confirm Password <span style="color: red;">*</span>'))
            ->helperText('Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
            ->validationAttribute('password confirmation')
            ->password()
            ->revealable()
            ->rules(['required']);
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(new HtmlString('Email Address <span style="color: red;">*</span>'))
            ->validationAttribute('email')
            ->afterStateUpdated(
                function (Get $get, Set $set) {
                    $set('email', \Str::lower($get('email')));
                }
            )
            ->live()
            ->rules(['required', 'email', 'unique:users,email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/']);
    }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(new HtmlString('Password <span style="color: red;">*</span>'))
            ->helperText('Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
            ->validationAttribute('password')
            ->password()
            ->revealable()
            ->rules(['required', 'confirmed', Password::defaults()]);
    }

    protected function mutateFormDataBeforeRegister(array $data): array
    {
        unset($data['password_confirmation']);
        unset($data['terms_conditions']);
        return $data;
    }

    public function register(): ?PcResponse
    {
        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return null;
        }

        $user = $this->wrapInDatabaseTransaction(function () {
            $this->callHook('beforeValidate');

            $data = $this->form->getState();

            $this->callHook('afterValidate');

            $data = $this->mutateFormDataBeforeRegister($data);

            $this->callHook('beforeRegister');

            $user = $this->handleRegistration($data);

            $this->form->model($user)->saveRelationships();

            $this->callHook('afterRegister');

            $user->assignRole('Pharmaceutical Company');

            return $user;
        });

        event(new Registered($user));

        $this->sendEmailVerificationNotification($user);

        Filament::auth()->login($user);
        Filament::auth()->user()->send2FAEmail();
        session()->regenerate();

        return app(PcResponse::class);
    }
}
