<?php

namespace App\Forms\Components;

use Closure;
use Filament\Forms\Components\Concerns\HasAffixes;
use Filament\Forms\Components\Field;
use Filament\Support\RawJs;

class PhoneWithPrefix extends Field
{
    use HasAffixes;
    protected string $view = 'forms.components.phone-with-prefix';

    protected array|\Closure|null $prefixOptions = null;
    protected string | RawJs | Closure | null $mask = null;
    protected function setUp(): void
    {
        parent::setUp();
        $this->default([
            'prefix' => '',
            'number' => '',
        ]);
        
    }

    /**
     * Accepts an array or a closure for dynamic options.
     */
    public function prefixOptions(array|\Closure $options): static
    {
        $this->prefixOptions = $options;
        return $this;
    }

    /**
     * Returns the options, evaluating closure if needed.
     */
    public function getPrefixOptions()
    {
        if ($this->prefixOptions instanceof \Closure) {
            return $this->evaluate($this->prefixOptions);
            // dd("DD");
            // Pass the live-wire component to the closure for dynamic state access
            // return call_user_func($this->prefixOptions, $this->getLivewire());
        }
        return $this->prefixOptions ?? [];
    }
}
