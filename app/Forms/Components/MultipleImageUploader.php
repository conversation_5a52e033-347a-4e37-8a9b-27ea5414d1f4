<?php

namespace App\Forms\Components;

use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MultipleImageUploader extends SpatieMediaLibraryFileUpload
{
    protected bool $hasDefaultOption = true;

    public static function make(string $name): static
    {
        return parent::make($name)
            ->multiple()
            ->minFiles(1)
            ->maxFiles(10)
            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])
            ->disk('public')
            ->visibility('public')
            ->preserveFilenames()
            ->required();
    }

    public function enableDefaultOption(bool $condition = true): static
    {
        $this->hasDefaultOption = $condition;

        return $this;
    }

    public function hasDefaultOption(): bool
    {
        return $this->hasDefaultOption;
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Set the view for our component
        $this->viewIdentifier = 'forms.components.multiple-image-uploader';

        // Process default image selection after save
        $this->afterStateHydrated(function (SpatieMediaLibraryFileUpload $component, ?Model $record): void {
            if (!$record || !($record instanceof HasMedia)) {
                return;
            }

            // Get media items and add default flag
            $mediaItems = $record->getMedia($component->getCollection());

            // Initialize state with media items
            $state = [];
            foreach ($mediaItems as $media) {
                $state[] = [
                    'id' => $media->id,
                    'name' => $media->name,
                    'url' => $media->getUrl(),
                    'is_default' => (bool) $media->getCustomProperty('is_default', false),
                ];
            }

            // Set the state
            $component->state($state);
        });

        // Add custom validation handling
        $this->getUploadedFileUsing(function ($_statePath, $file) {
            // Extract the actual file from our custom format
            if (is_array($file) && isset($file['file'])) {
                return $file['file'];
            }

            return $file;
        });

        // Add custom processing for file uploads
        $this->dehydrateStateUsing(function ($state) {
            if (!is_array($state)) {
                return $state;
            }

            // Process each item to ensure it's in the correct format for upload
            return collect($state)
                ->filter()
                ->map(function ($item) {
                    // If it's already a file object, return it as is
                    if (is_object($item)) {
                        return $item;
                    }

                    // If it's an array with a file property, extract the file
                    if (is_array($item) && isset($item['file']) && is_object($item['file'])) {
                        return $item['file'];
                    }

                    // Otherwise, return the item as is
                    return $item;
                })
                ->toArray();
        });

        // Save the default image property
        $this->beforeStateDehydrated(function (SpatieMediaLibraryFileUpload $component, $state, ?Model $record): void {
            if (!$record || !($record instanceof HasMedia)) {
                return;
            }

            // Use the state that was passed to the function

            // Process existing media to update default status
            $record->getMedia($component->getCollection())
                ->each(function (Media $media) use ($state) {
                    // Check if this media is marked as default in the state
                    $isDefault = false;

                    if (is_array($state)) {
                        foreach ($state as $item) {
                            if (isset($item['id']) && $item['id'] == $media->id && isset($item['is_default']) && $item['is_default']) {
                                $isDefault = true;
                                break;
                            }
                        }
                    }

                    // Update the custom property
                    $media->setCustomProperty('is_default', $isDefault);
                    $media->save();
                });
        });
    }
}
