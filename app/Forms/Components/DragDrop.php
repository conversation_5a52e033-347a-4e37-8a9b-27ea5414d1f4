<?php

namespace App\Forms\Components;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class DragDrop extends SpatieMediaLibraryFileUpload
{
    /** @var view-string */
    protected string $view = 'forms.components.drag-drop';

    protected function setUp(): void
    {
        parent::setUp();

        $this->multiple()
            ->maxFiles(10)
            ->disk('s3')
            ->acceptedFileTypes(['image/jpeg', 'image/png'])
            ->rules([
                'image',
                // 'dimensions:width=1140,height=1140',
            ])
            ->validationMessages([
                'image' => 'The file must be an image.',
                'dimensions' => 'The image must be 1140px by 1140px.',
            ])
            ->maxSize(2048);

        $this->loadStateFromRelationshipsUsing(function (SpatieMediaLibraryFileUpload $component, ?Model $record): void {
            if (!$record) {
                $component->state([]);
                return;
            }

            if (!method_exists($record, 'media')) {
                $component->state([]);
                return;
            }

            /** @var \Spatie\MediaLibrary\HasMedia $record */
            $files = $record->getMedia($component->getCollection())
                ->map(fn(Media $media) => $media->id)
                ->toArray();

            $component->state($files);
        });

        $this->saveRelationshipsUsing(static function (SpatieMediaLibraryFileUpload $component): void {
            $record = $component->getRecord();
            $state = $component->getState();
            
            // Debug logging
            \Illuminate\Support\Facades\Log::info('DragDrop saveRelationshipsUsing called', [
                'record_id' => $record?->id,
                'record_class' => $record ? get_class($record) : 'null',
                'state_type' => gettype($state),
                'state_count' => is_array($state) ? count($state) : 'not_array',
                'state_content' => $state
            ]);
            
            if (!$record || !method_exists($record, 'addMediaFromStream')) {
                \Illuminate\Support\Facades\Log::warning('DragDrop: Invalid record or missing method');
                return;
            }

            // Don't process if state is empty or not an array
            if (empty($state) || !is_array($state)) {
                \Illuminate\Support\Facades\Log::info('DragDrop: Empty or invalid state, skipping');
                return;
            }

            // Get existing media collection before processing
            $existingMedia = $record->getMedia($component->getCollection());
            \Illuminate\Support\Facades\Log::info('DragDrop: Existing media count', [
                'collection' => $component->getCollection(),
                'existing_count' => $existingMedia->count(),
                'existing_ids' => $existingMedia->pluck('id')->toArray()
            ]);
            
            // Only process new temporary uploaded files from current state
            $newFiles = collect($state)->filter(function ($item) {
                $isTemporaryFile = $item instanceof TemporaryUploadedFile;
                \Illuminate\Support\Facades\Log::info('DragDrop: Checking state item', [
                    'item_type' => gettype($item),
                    'item_class' => is_object($item) ? get_class($item) : 'not_object',
                    'is_temporary_file' => $isTemporaryFile
                ]);
                return $isTemporaryFile;
            });

            \Illuminate\Support\Facades\Log::info('DragDrop: New files to process', [
                'new_files_count' => $newFiles->count()
            ]);

            // Add new uploaded files to the media collection
            foreach ($newFiles as $index => $file) {
                try {
                    \Illuminate\Support\Facades\Log::info('DragDrop: Processing file', [
                        'index' => $index,
                        'file_name' => $file->getClientOriginalName(),
                        'file_size' => $file->getSize()
                    ]);
                    
                    $mediaItem = $record
                        ->addMediaFromStream(fopen($file->getRealPath(), 'r'))
                        ->usingName($file->getClientOriginalName())
                        ->usingFileName($file->hashName())
                        ->toMediaCollection($component->getCollection(), $component->getDiskName());
                        
                    \Illuminate\Support\Facades\Log::info('DragDrop: File saved successfully', [
                        'media_id' => $mediaItem->id,
                        'file_name' => $mediaItem->file_name
                    ]);
                } catch (\Exception $e) {
                    // Log error but continue with other files
                    \Illuminate\Support\Facades\Log::error('Failed to save media file: ' . $e->getMessage(), [
                        'file_name' => $file->getClientOriginalName(),
                        'exception' => $e->getTraceAsString()
                    ]);
                }
            }

            // After saving new files, update component state with all media IDs
            // This includes both existing and newly added files
            $allMedia = $record->fresh()->getMedia($component->getCollection());
            $allMediaIds = $allMedia->pluck('id')->toArray();
            
            \Illuminate\Support\Facades\Log::info('DragDrop: Final state update', [
                'all_media_count' => $allMedia->count(),
                'all_media_ids' => $allMediaIds
            ]);
            
            // Update component state to reflect all files (existing + new)
            $component->state($allMediaIds);
        });

        $this->beforeStateDehydrated(function ($state) {
            if (!is_array($state)) {
                return $state;
            }

            return collect($state)
                ->filter()
                ->map(function ($item) {
                    if ($item instanceof TemporaryUploadedFile) {
                        return $item;
                    }
                    if (is_string($item) || is_numeric($item)) {
                        return $item; // Keep media IDs
                    }
                    if (is_array($item) && isset($item['file'])) {
                        return $item['file'];
                    }
                    return $item;
                })
                ->values()
                ->toArray();
        });
    }

    /**
     * Get all current media for the record including new uploads
     */
    public function getAllMedia(): array
    {
        $record = $this->getRecord();
        $state = $this->getState();
        
        if (!$record) {
            return [];
        }

        // Get existing media
        $existingMedia = $record->getMedia($this->getCollection());
        
        // Get new uploads from state
        $newUploads = collect($state)->filter(function ($item) {
            return $item instanceof TemporaryUploadedFile;
        });

        return [
            'existing' => $existingMedia->toArray(),
            'new' => $newUploads->toArray()
        ];
    }
}
