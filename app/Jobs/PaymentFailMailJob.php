<?php

namespace App\Jobs;

use App\Notifications\Api\PcNewOrderNotification;
use App\Mail\PaymentFailMail;
use App\Mail\PcOrderMail;
use App\Models\SubOrder;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;

class PaymentFailMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $order;
    protected $transaction;

    public function __construct($order,$transaction)
    {
        $this->order = $order;
        $this->transaction = $transaction;
    }

    public function handle()
    {
        try {
            Mail::to($this->order->user->email)->send(new PaymentFailMail($this->order,$this->transaction));
        
            
        }catch(Exception $e)
        {
            Log::error("facility order payment fail job issue ,  Error: " . $e->getMessage());
        }
    }
}

