<?php

namespace App\Jobs;

use App\Notifications\Api\PcNewOrderNotification;
use App\Mail\PaymentFailMail;
use App\Mail\PcOrderMail;
use App\Models\SubOrder;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;
use App\Notifications\Api\NewMessageNotification;
use App\Notifications\Api\SupportTicketNotification;
use App\Mail\SupportTicketMail;
use App\Models\User;
use App\Models\SupportTicket;


class SupplierNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $receiverId;
    protected $redirectUrl;
    protected $threadId;
    protected $from;
    protected $subOrderId;
    protected $main;

    public function __construct($receiverId,$redirectUrl,$threadId,$from,$subOrderId,$main=false)
    {
        $this->receiverId = $receiverId;
        $this->redirectUrl = $redirectUrl;
        $this->threadId = $threadId;
        $this->from = $from;
        $this->subOrderId = $subOrderId;
        $this->main = $main;
    }

    public function handle()
    {
        try {
            $subUsers = getSubUser($this->receiverId);
            if($this->receiverId && $this->threadId) {
                $receiver = User::find($this->receiverId);
                $ticket = SupportTicket::find($this->threadId);

            }
                foreach($subUsers as $subUser){
                    if($this->main && $subUser->can('all-orders_chat')) {
                        Mail::to($subUser->email)->send(new SupportTicketMail($ticket, $this->redirectUrl, $receiver));
                        $subUser->notify(new SupportTicketNotification($subUser->id,$this->redirectUrl,$this->threadId));
                 
                    } else{
                        if($subUser->can('all-orders_chat')) {
                            $subUser->notify(new NewMessageNotification(
                                $subUser->id,$this->redirectUrl,$this->threadId,$this->from,$this->subOrderId
                            ));
                        }
                    }
                }
            
        }catch(Exception $e)
        {
            Log::error($this->from . "getting error ,  Error: " . $e->getMessage());
        }
    }
}

