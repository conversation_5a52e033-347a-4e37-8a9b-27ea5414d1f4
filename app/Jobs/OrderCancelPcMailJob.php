<?php

namespace App\Jobs;

use App\Mail\OrderCancelPcMail;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;


class OrderCancelPcMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $subOrder;

    public function __construct($subOrder)
    {
        $this->subOrder = $subOrder;
    }

    public function handle()
    {
        try {
            Mail::to($this->subOrder->user->email)->send(new OrderCancelPcMail($this->subOrder)); 
        
        }catch(Exception $e)
        {
            Log::error("pc Order cancel issue. Error: " . $e->getMessage());
        }
    }
}

