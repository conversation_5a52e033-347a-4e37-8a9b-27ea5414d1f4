<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\ProductBatch;
use App\Models\ProductRelation;
use Illuminate\Queue\SerializesModels;
use Filament\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class BulkStockUpdateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $batchData;
    protected $normalData;
    protected $userId;

    /**
     * Create a new job instance.
     */
    public function __construct(array $batchData, array $normalData, int $userId)
    {
        $this->batchData = $batchData;
        $this->normalData = $normalData;
        $this->userId = $userId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (!empty($this->batchData)) {
            $productRelationIds = ProductRelation::whereIn('product_id', array_keys($this->batchData))
                ->pluck('id', 'product_id');

            $batchInsertData = [];

            foreach ($this->batchData as $productId => $batch) {
                $productRelationId = $productRelationIds[$productId] ?? null;
                if ($productRelationId) {
                    ProductBatch::where('product_id', $productId)
                        ->where('products_relation_id', $productRelationId)
                        ->delete();

                    foreach ($batch['batches'] as $batch) {
                        $batchInsertData[] = [
                            'product_id' => $productId,
                            'batch_name' => $batch['batch_name'],
                            'user_id' => $this->userId,
                            'products_relation_id' => $productRelationId,
                            'available_stock' => $batch['available_stock'],
                            'expiry_date' => $batch['expiry_date'],
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }
                }
            }

            if (!empty($batchInsertData)) {
                ProductBatch::insert($batchInsertData);
            }
        }

        if (!empty($this->normalData)) {
            foreach ($this->normalData as $productId => $productData) {
                ProductRelation::where('product_id', $productId)->update(['stock' => $productData]);
            }
        }
        
        // 🎯 Log bulk stock update activity
        $this->logBulkStockActivity();
        
        $user = User::find($this->userId);
        Notification::make()
            ->title('Bulk Stock Updated successfully')
            ->body('Bulk Stock Updated successfully')
            ->success()
            ->sendToDatabase($user);
    }

    /**
     * Log bulk stock update activity for audit trail
     */
    private function logBulkStockActivity(): void
    {
        $user = User::find($this->userId);
        $totalProductsUpdated = count($this->batchData) + count($this->normalData);
        $totalBatchesUpdated = 0;
        
        // Count total batches
        foreach ($this->batchData as $productData) {
            $totalBatchesUpdated += count($productData['batches'] ?? []);
        }
        
        // 🎯 Create comprehensive human-readable activity data
        $humanReadableData = [
            // Basic Context
            'Updated By' => $user->name ?? 'Unknown User',
            'Updated By ID' => $this->userId,
            'Update Type' => 'Bulk Stock Update',
            'Update Method' => 'Background Job',
            
            // Bulk Update Summary
            'Total Products Updated' => $totalProductsUpdated,
            'Batch-wise Products' => count($this->batchData),
            'Simple Stock Products' => count($this->normalData),
            'Total Batches Created' => $totalBatchesUpdated,
            
            // Detailed Breakdown
            'Batch-wise Updates' => $this->formatBatchUpdates(),
            'Simple Stock Updates' => $this->formatSimpleStockUpdates(),
            
            // Technical Details
            'Job Executed At' => now()->format('Y-m-d H:i:s'),
            'Processing Time' => 'Background Job',
        ];

        // Log activity for each product individually for better tracking
        foreach ($this->batchData as $productId => $productData) {
            $product = \App\Models\Product::find($productId);
            if ($product) {
                activity()
                    ->performedOn($product)
                    ->causedBy($user)
                    ->withProperties([
                        'old' => [], // No old state for bulk operations
                        'attributes' => [
                            'Product Name' => $product->name,
                            'Update Type' => 'Bulk Stock Update - Batch-wise',
                            'Updated By' => $user->name,
                            'Batches Added' => count($productData['batches'] ?? []),
                            'Batch Details' => $this->formatProductBatches($productData['batches'] ?? []),
                            'Updated At' => now()->format('Y-m-d H:i:s'),
                        ]
                    ])
                    ->log("Bulk update: {$user->name} updated '{$product->name}' with " . count($productData['batches'] ?? []) . " batches");
            }
        }

        foreach ($this->normalData as $productId => $stock) {
            $product = \App\Models\Product::find($productId);
            if ($product) {
                activity()
                    ->performedOn($product)
                    ->causedBy($user)
                    ->withProperties([
                        'old' => [], // No old state for bulk operations
                        'attributes' => [
                            'Product Name' => $product->name,
                            'Update Type' => 'Bulk Stock Update - Simple Stock',
                            'Updated By' => $user->name,
                            'New Stock Level' => $stock,
                            'Updated At' => now()->format('Y-m-d H:i:s'),
                        ]
                    ])
                    ->log("Bulk update: {$user->name} set '{$product->name}' stock to {$stock}");
            }
        }

        // Log summary activity
        activity()
            ->causedBy($user)
            ->withProperties([
                'old' => [],
                'attributes' => $humanReadableData
            ])
            ->log("Bulk stock update completed: {$totalProductsUpdated} products updated with {$totalBatchesUpdated} total batches by {$user->name}");
    }

    /**
     * Format batch updates for logging
     */
    private function formatBatchUpdates(): array
    {
        $formatted = [];
        foreach ($this->batchData as $productId => $productData) {
            $product = \App\Models\Product::find($productId);
            $formatted[$product?->name ?? "Product ID: {$productId}"] = [
                'Batches Count' => count($productData['batches'] ?? []),
                'Total Stock' => collect($productData['batches'] ?? [])->sum('available_stock'),
            ];
        }
        return $formatted;
    }

    /**
     * Format simple stock updates for logging
     */
    private function formatSimpleStockUpdates(): array
    {
        $formatted = [];
        foreach ($this->normalData as $productId => $stock) {
            $product = \App\Models\Product::find($productId);
            $formatted[$product?->name ?? "Product ID: {$productId}"] = $stock;
        }
        return $formatted;
    }

    /**
     * Format batch details for a single product
     */
    private function formatProductBatches(array $batches): array
    {
        $formatted = [];
        foreach ($batches as $index => $batch) {
            $batchNum = $index + 1;
            $formatted["Batch {$batchNum}"] = [
                'Batch Name' => $batch['batch_name'] ?? 'Not Set',
                'Available Stock' => $batch['available_stock'] ?? 0,
                'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
            ];
        }
        return $formatted;
    }
}
