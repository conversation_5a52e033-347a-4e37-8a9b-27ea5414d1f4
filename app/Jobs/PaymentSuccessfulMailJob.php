<?php

namespace App\Jobs;

use App\Notifications\Api\PcNewOrderNotification;
use App\Mail\PaymentSuccessfulMail;
use App\Mail\PcOrderMail;
use App\Models\SubOrder;
use App\Models\ZipCode;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;
use Carbon\Carbon;

class PaymentSuccessfulMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $order;
    protected $transactionStatus;
    protected $paymentType;

    public function __construct($order,$transactionStatus,$paymentType = null)
    {
        $this->order = $order;
        $this->transactionStatus = $transactionStatus;
        $this->paymentType = $paymentType;
    }

    public function handle()
    {

        try {
            Mail::to($this->order->user->email)->send(new PaymentSuccessfulMail($this->order,$this->transactionStatus,$this->paymentType));
            
            Log::error("PaymentSuccessfulMailJob call ");

            $formatter = new \NumberFormatter('en_IN', \NumberFormatter::SPELLOUT);

            $deliveryAdd = $this->deliveryAddess($this->order);
            $buyAdd = $this->buyAddess($this->order);
            foreach($this->order->subOrder as $subOrder) {
                $fileName = 'purchase-order-' . $subOrder->id . '.pdf';
                $s3Path = config('constants.api.order_invoices.supplier_invoice')  . $fileName;
                $amountInWords = $formatter->format($subOrder->total_sub_order_value);
                $facility = $this->order->user;
                $facilitySignatureFolderPath = config('constants.api.media.clinic_medias').$facility->clinicDetails->id.'/';
                $sellerAdd = $subOrder->user->addresses ? $this->billingAddess($subOrder->user->addresses[0]) : null;
                $poNumber = 'PO-'.Carbon::now()->year.'-'.$subOrder->id;
                $deliveryDays = $subOrder->delivery_days ? (int) $subOrder->delivery_days : null;
                $expectedDeliveryDate = $deliveryDays ? Carbon::now()->addDays($deliveryDays)->format('d F Y') : null;
                
                $html = view('pdf.pc-invoice', [
                    'order' => $this->order,
                    'subOrder' => $subOrder,
                    'pcDetails' => $subOrder->user->pcDetails,
                    'supAmt' => $subOrder->total_sub_order_value,
                    'shippingAmt' => $subOrder->total_shipping_amount,
                    'amountInWords' => $amountInWords,
                    'facility' => $facility,
                    'deliveryAdd' => $deliveryAdd,
                    'buyAdd' => $buyAdd,
                    'poNumber' => $poNumber,
                    'expectedDeliveryDate' => $expectedDeliveryDate,
                    'sellerAdd' => $sellerAdd,
                    'facilityClinicData' => $facility->clinicDetails,
                    "facilitySignature" =>  $facility->clinicDetails ? Storage::url($facilitySignatureFolderPath.$facility->clinicDetails->dc_signature) : '#',
                    'payment_method' => $this->paymentType == 'credit_line' ? 'Business Credit' : 'Online'
                ])->render();
                $pdfBinary = Browsershot::html($html)
                        ->setChromePath('/usr/bin/google-chrome')
                        ->format('A4')
                        ->noSandbox()
                        ->pdf();
                        
                Log::info("payment pc PO fileName: " . $fileName);
                $subOrder->update([
                    'invoice_path' => $fileName,
                    'receipt_number' => 'REC-'.$subOrder->order->order_number.'-'.$subOrder->id,
                    'po_number' => $poNumber,
                ]);

                Storage::disk(config(FILESYSTEM_DEFAULT))->put($s3Path, $pdfBinary);

                Mail::to($subOrder->user->email)->send(new PcOrderMail($subOrder,$pdfBinary));
                $subOrder->user->notify(new PcNewOrderNotification($subOrder->user_id,$subOrder->id));
                
            }
        }catch(Exception $e)
        {
            Log::error("payment PO issue. Error: " . $e->getMessage());
        }
    }

    public function billingAddess($address) {
        return $address->address_1 .','.$address->city->name .','. $address->postal_code;
    }

    public function deliveryAddess($order) {
        return $order->shipping_address_1 .','.$order->shippingCity->name .','. $order->shipping_postal_code;
    } 

    public function buyAddess($order) {
        return $order->billing_address_1 .','.$order->billingCity->name .','. $order->billing_postal_code;
    }

}

