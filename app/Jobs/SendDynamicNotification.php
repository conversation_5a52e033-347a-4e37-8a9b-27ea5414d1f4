<?php

namespace App\Jobs;

use App\Models\User;
use App\Notifications\BaseNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendDynamicNotification implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected $user;
    protected $emailTemplateData;
    protected $data;
    /**
     * Create a new job instance.
     */
    public function __construct(User $user, array $emailTemplateData, array $data)
    {
        $this->user = $user;
        $this->emailTemplateData = $emailTemplateData;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $this->user->notify(new BaseNotification($this->emailTemplateData, $this->data));
    }
}
