<?php

namespace App\Jobs;

use App\Mail\PaymentSuccessfulMail;
use App\Mail\ReceivedConfirmationMail;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ReceivedConfirmationMailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $record;

    public function __construct($record)
    {
        $this->record = $record;
    }

    public function handle()
    {
        try {
            Mail::to($this->record->user->email)->send(new ReceivedConfirmationMail($this->record));
            
        }catch(Exception $e)
        {
            Log::error("payment issue. Error: " . $e->getMessage());
        }
        
    }
}

