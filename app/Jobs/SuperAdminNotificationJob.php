<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Notifications\Api\NewInquiryNotification;
use App\Notifications\Api\NewFacilityRegisterNotification;
use App\Notifications\Api\FacilityProfileApprovalNotification;
use App\Mail\FacilityProfileApprovalMail;
use App\Mail\FacilityOnboardingCompleteMail;
use App\Mail\AdminInquiryMail;
use App\Models\Inquiry;

class SuperAdminNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $to;
    protected $modelId;
    protected $auth;
    protected $inquiryRes;

    public function __construct($to, $modelId, $auth = null)
    {
        $this->to = $to;
        $this->modelId = $modelId;
        $this->auth = $auth;
    }


    public function handle()
    {
        try {
            $superAdmins = getAdminData();
            
            if($this->to == 'inquiry') {
               $this->inquiryRes =  Inquiry::find($this->modelId);
            }
            foreach ($superAdmins as $superAdmin) {
                $this->handleNotificationsForUser($superAdmin);
            }
            
        } catch (Exception $e) {
            Log::error($this->to . " super admin notification error: " . $e->getMessage());
        }
    }

    protected function handleNotificationsForUser($user)
    {
        if ($this->to == 'inquiry') {
            $user->notify(new NewInquiryNotification($user->id, $this->modelId));
            Mail::to($user->email)->send(new AdminInquiryMail($this->inquiryRes));
        } elseif ($this->to == 'new_facility') {
            $user->notify(new NewFacilityRegisterNotification($user->id, $this->modelId));
        }elseif ($this->to == 'new_facility_onboarding') {
            $user->notify(new FacilityProfileApprovalNotification($user->id, $this->modelId));
            Mail::to($user->email)->send(new FacilityOnboardingCompleteMail($this->auth));
        } elseif ($this->to == 'facility_approve' || $this->to == 'facility_approve_not_step_2') {
            $user->notify(new FacilityProfileApprovalNotification($user->id, $this->modelId));
            Mail::to($user->email)->send(new FacilityProfileApprovalMail($this->auth));
        }

        $this->handleSubUserNotifications($user);
    }

    protected function handleSubUserNotifications($user)
    {
        $subUsers = getSubUser($user->id);
        
        foreach ($subUsers as $subUser) {

            if ($subUser->can('inquiries_view') && $this->to == 'inquiry') {
                $subUser->notify(new NewInquiryNotification($subUser->id, $this->modelId));
                Mail::to($subUser->email)->send(new AdminInquiryMail($this->inquiryRes));
            } elseif ($this->to == 'new_facility') {
                $subUser->notify(new NewFacilityRegisterNotification($subUser->id, $this->modelId));
            }elseif ($subUser->can('facility_view') && $this->to == 'new_facility_onboarding') {
                $subUser->notify(new FacilityProfileApprovalNotification($subUser->id, $this->modelId));
                Mail::to($subUser->email)->send(new FacilityOnboardingCompleteMail($this->auth));
            } elseif ($subUser->can('facility_view') && ($this->to == 'facility_approve' || $this->to == 'facility_approve_not_step_2')) {
                $subUser->notify(new FacilityProfileApprovalNotification($subUser->id, $this->modelId));
                Mail::to($subUser->email)->send(new FacilityProfileApprovalMail($this->auth));
            }
        }
    }
}


