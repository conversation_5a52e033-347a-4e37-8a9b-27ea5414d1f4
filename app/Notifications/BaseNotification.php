<?php

namespace App\Notifications;

use App\Channels\FirebaseChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class BaseNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $emailTemplateData;
    protected $details;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $emailTemplateData, array $details)
    {
        $this->emailTemplateData = $emailTemplateData;
        $this->details = $details;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): ?MailMessage
    {
        $email = $notifiable->email ?? null;
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            Log::warning("No valid email found for notifiable. Skipping mail.");
            return null;
        }
        Log::info("Triggering mail to: " . $email);
        // If template is disabled
        if ($this->emailTemplateData["is_email"] === 0) {
            Log::warning("Email template disabled or not found: " . $this->emailTemplateData["key"]);
            // Send fallback mail to developer/admin
            $fallbackEmail = "<EMAIL>";
            $subject = "Missing or Disabled Email Template";
            $body = "Email template missing or disabled for key: {$this->emailTemplateData['key']}";

            Mail::raw($body, function ($message) use ($fallbackEmail, $subject) {
                $message->to($fallbackEmail)->subject($subject);
            });
            return (new MailMessage)->line('No email sent due to missing or disabled template.');
        } else {
            // Replace placeholders in email content
            $body = $this->emailTemplateData["email_content"];
            foreach ($this->details as $key => $value) {
                $body = str_replace($key, $value, $body);
            }

            $subject = $this->emailTemplateData["subject"];
            $subjectReplace = [
                'SUPPLIER'=> $this->details['SUPPLIER'] ?? 'Supplier'
            ];
            foreach ($subjectReplace as $key => $value) {
                $subject = str_replace($key, $value, $subject);
            }

            return (new MailMessage)
                ->subject($subject)
                ->view('emails.common_mail', ['html' => $body]);
        }
    }
}
