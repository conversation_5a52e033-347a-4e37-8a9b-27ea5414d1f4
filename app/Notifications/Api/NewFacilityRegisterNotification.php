<?php

namespace App\Notifications\Api;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewFacilityRegisterNotification extends Notification
{
    use Queueable;

    protected $userId;
    protected $facilityId;

    /**
     * Create a new notification instance.
     */
    public function __construct($userId,$facilityId)
    {
        $this->userId = $userId;
        $this->facilityId = $facilityId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        return [ 

            "body" => __('api.notifications.new_facility_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.new_facility_title'),
            "format" => "filament",
            "status" => "success",
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => route('filament.admin.resources.clinics.view', ['record' => $this->facilityId]),
            "actions" => [
                [
                    'name' => 'viewFacility',
                    'label' => 'View Facility',
                    'url' => route('notifications.redirect', ['notification' => $this->id]), // <-- generic handler
                    'color' => 'primary'
                ]
            ],

        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
