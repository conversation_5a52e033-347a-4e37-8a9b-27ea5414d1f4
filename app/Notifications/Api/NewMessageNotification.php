<?php

namespace App\Notifications\Api;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewMessageNotification extends Notification
{
    use Queueable;

    protected $userId;
    protected $redirectUrl;
    protected $ticketId;
    protected $routeType;
    protected $subOrderId;

    /**
     * Create a new notification instance.
     */
    public function __construct($userId, $redirectUrl, $ticketId, $routeType = null, $subOrderId = null)
    {
        $this->userId = $userId;
        $this->redirectUrl = $redirectUrl;
        $this->ticketId = $ticketId;
        $this->routeType = $routeType;
        $this->subOrderId = $subOrderId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {

        if ($this->redirectUrl == 'pc') {
            if ($this->routeType == 'thread') {
                $url =  route('filament.pc.resources.orders.chat', ['record' => $this->subOrderId, 'thread_id' => $this->ticketId, 'type' => 'allorder']);
            } else {

                $url =  route('filament.pc.resources.support-ticket-receiveds.details', ['record' => $this->ticketId]);
            }
            // $url =  route('filament.pc.resources.support-ticket-receiveds.index');
        } else {
            $url =  route('filament.admin.resources.support-tickets.details', ['record' => $this->ticketId]);
        }
        return [

            "body" => __('api.notifications.new_message_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.new_message_title'),
            "format" => "filament",
            "status" => "success",
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => $url,
            "actions" => [
                [
                    'name' => 'NewMessage',
                    'label' => 'New Message',
                    //'url' => route('notifications.redirect', ['notification' => $this->id]),
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
