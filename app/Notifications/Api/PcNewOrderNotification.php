<?php

namespace App\Notifications\Api;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\DB;

class PcNewOrderNotification extends Notification
{
    use Queueable;

    protected $userId;
    protected $subOrderId;

    /**
     * Create a new notification instance.
     */
    public function __construct($userId,$subOrderId)
    {
        $this->userId = $userId;
        $this->subOrderId = $subOrderId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        return [ 

            "body" => __('api.notifications.new_order_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.new_order_title'),
            "format" => "filament",
            "status" => "success",
            "duration" => "persistent",
            "subOrderId" => $this->subOrderId,
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => route('filament.pc.resources.orders.view', ['record' => $this->subOrderId]),
            "actions" => [
                [
                    'name' => 'viewNewOrders',
                    'label' => 'View New Orders',
                    'url' => route('notifications.redirect', ['notification' => $this->id]), // <-- generic handler
                    'color' => 'primary'
                ]
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

}
