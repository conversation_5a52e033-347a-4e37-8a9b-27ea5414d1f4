<?php

namespace App\Notifications\Api;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class SupportTicketNotification extends Notification
{
    use Queueable;

    protected $userId;
    protected $redirectUrl;
    protected $ticketId;

    /**
     * Create a new notification instance.
     */
    public function __construct($userId, $redirectUrl, $ticketId)
    {
        $this->userId = $userId;
        $this->redirectUrl = $redirectUrl;
        $this->ticketId = $ticketId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        if ($this->redirectUrl == 'pc') {
            $url =  route('filament.pc.resources.support-ticket-receiveds.details', ['record' => $this->ticketId]);
        } else {
            //  $url =  route('filament.admin.resources.support-ticket-assigneds.index'); 
            $url =  route('filament.admin.resources.support-tickets.details', ['record' => $this->ticketId]);
        }

        return [

            "body" => __('api.notifications.new_support_ticket_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.new_support_ticket_title'),
            "format" => "filament",
            "status" => "success",
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            // "target_url" => $url,
            "actions" => [
                [
                    'name' => 'viewSupportTicket',
                    'label' => 'View SupportTicket',
                    //'url' => route('notifications.redirect', ['notification' => $this->id]), // <-- generic handler
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
