<?php

namespace App\Notifications\Api;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewInquiryNotification extends Notification
{
    use Queueable;

    protected $userId;
    protected $inquiryId;

    /**
     * Create a new notification instance.
     */
    public function __construct($userId, $inquiryId)
    {
        $this->userId = $userId;
        $this->inquiryId = $inquiryId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        return [

            "body" => __('api.notifications.new_inquiry_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.new_inquiry_title'),
            "format" => "filament",
            "status" => "success",
            "actions" => [],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => route('filament.admin.resources.inquiries.view', ['record' => $this->inquiryId]),
            "actions" => [
                [
                    'name' => 'viewInquiry',
                    'label' => 'View Inquiry',
                    'url' => route('notifications.redirect', ['notification' => $this->id]), // <-- generic handler
                    'color' => 'primary'
                ]
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
