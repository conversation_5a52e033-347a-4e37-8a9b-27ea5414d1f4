<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AssociateSupplierNotification extends Notification
{
    use Queueable;

    protected $pcId;

    /**
     * Create a new notification instance.
     */
    public function __construct($pcId)
    {
        $this->pcId = $pcId;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        return [

            "body" => "A new facility wants to associate with you.",
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "Facility Associate Request",
            "format" => "filament",
            "status" => "success",
            "target_url" => route('filament.pc.resources.facilities.index'),
            "actions" => [
                [
                    'name' => 'viewRequest',
                    'label' => 'View Request',
                    'url' => route('notifications.redirect', ['notification' => $this->id]), // <-- generic handler
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success"
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
