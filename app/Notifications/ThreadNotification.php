<?php

namespace App\Notifications;

use App\Filament\Admin\Resources\OrderChatResource;
use App\Filament\Admin\Resources\SupportTicketAssignedResource;
use App\Filament\Admin\Resources\threadResource;
use App\Models\Thread;
use App\Models\ThreadMessage;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;

class ThreadNotification extends Notification
{
    use Queueable;

    protected $message;
    protected $ticket;
    protected $ticketId;
    protected $type;
    protected $redirectUrl;
    protected $senderId;
    protected $subOrderId;
    protected $senderRole;
    protected $threadId; // Add thread ID property

    /**
     * Create a new notification instance.
     *
     * @param ThreadMessage|Thread $data
     * @param string|null $type
     * @param string $redirectUrl
     * @param string $senderRole - 'pc', 'admin', or 'clinic'
     */
    public function __construct($data, ?string $type = null, $redirectUrl = null, $senderRole = null)
    {
        if ($data instanceof ThreadMessage) {
            $this->message = $data;
            $this->ticket = $data->thread;
            $this->ticketId = $data->thread_id;
            $this->threadId = $data->thread_id; // Store thread ID separately
            $this->type = 'new_message';
            $this->subOrderId = $data->sub_order_id;
            $this->senderId = $data->from_id;
        } elseif ($data instanceof Thread) {
            $this->ticket = $data;
            $this->ticketId = $data->id;
            $this->threadId = $data->id; // For Thread objects, use the same ID
            $this->type = $type;
        }
        $this->redirectUrl = $redirectUrl;
        $this->senderRole = $senderRole;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param object $notifiable
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the database representation of the notification.
     *
     * @param object $notifiable
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable)
    {
        // Get the Thread if we don't have it as an object
        if (!$this->ticket instanceof Thread) {
            $thread = Thread::find($this->ticketId);
        } else {
            $thread = $this->ticket;
        }

        // Generate URL based on notification type and redirect preference
        $url = $this->generateUrl($thread, $notifiable);

        if ($this->type === 'new_message') {
            return $this->getNewMessageNotificationData($url, $notifiable);
        }

        // Add other notification types as needed
        return [];
    }

    /**
     * Generate the appropriate URL based on redirect preference and user role
     *
     * @param Thread $thread
     * @param object $notifiable
     * @return string
     */
    private function generateUrl(Thread $thread, $notifiable): string
    {
        // Get the sub order for proper routing
        $subOrder = \App\Models\SubOrder::where('order_id', $thread->order_id)
            ->where('user_id', $thread->receiver_id)
            ->first();
        
        $orderId = $subOrder ? $subOrder->id : $thread->order_id;
        $threadId = $this->threadId;

        // Route generation based on redirect URL preference
        switch ($this->redirectUrl) {
            case 'pc':
                return $this->generatePcUrl($orderId, $threadId, $thread);
                
            case 'admin':
                return $this->generateAdminUrl($orderId, $threadId);
                
            case 'clinic':
                return $this->generateClinicUrl($orderId, $threadId);
                
            default:
                // Default fallback based on user role
                $userRole = $this->getUserRole($notifiable);
                return $this->generateUrlByRole($userRole, $orderId, $threadId);
        }
    }

    /**
     * Generate PC panel URL
     */
    private function generatePcUrl(int $orderId, int $threadId, Thread $thread): string
    {
        $user = User::find($thread->sender_id);
        
        // Build the URL with proper parameters
        $baseUrl = route('filament.pc.resources.orders.chat', [$orderId]);
        
        // Add query parameters
        $queryParams = [
            'thread_id' => $threadId,
            'type' => 'allorder-list'
        ];
        
        return $baseUrl . '?' . http_build_query($queryParams);
    }

    /**
     * Generate Admin panel URL - CORRECTED
     */
    private function generateAdminUrl(): string
    {
        $url = OrderChatResource::getUrl('index', panel: 'admin');
        
        // Strip domain to get only the path
        $relativePath = ltrim(parse_url($url, PHP_URL_PATH), '/');
        $fullUrl = config('app.admin_url') . '/' . $relativePath;
        
        return $fullUrl;
    }

    /**
     * Generate Clinic panel URL
     */
    private function generateClinicUrl(int $orderId, int $threadId): string
    {
        $baseUrl = route('filament.clinic.resources.support-tickets.index');
        
        // Add parameters for filtering/navigation
        $queryParams = [
            'order_id' => $orderId,
            'thread_id' => $threadId
        ];
        
        return $baseUrl . '?' . http_build_query($queryParams);
    }

    /**
     * Generate URL based on user role
     */
    private function generateUrlByRole(string $role, int $orderId, int $threadId): string
    {
        switch ($role) {
            case 'admin':
                return $this->generateAdminUrl($orderId, $threadId);
            case 'clinic':
                return $this->generateClinicUrl($orderId, $threadId);
            case 'pc':
            default:
                // For PC role, we need to create a Thread object for the method
                $thread = Thread::find($this->ticketId);
                return $this->generatePcUrl($orderId, $threadId, $thread);
        }
    }

    /**
     * Determine user role based on the notifiable user
     *
     * @param object $notifiable
     * @return string
     */
    private function getUserRole($notifiable): string
    {
        // Check for Spatie roles or similar
        if (method_exists($notifiable, 'hasRole')) {
            if ($notifiable->hasRole('admin')) {
                return 'admin';
            } elseif ($notifiable->hasRole('clinic')) {
                return 'clinic';
            } elseif ($notifiable->hasRole('pc')) {
                return 'pc';
            }
        }
        
        // Check for roles relationship
        if (isset($notifiable->roles) && is_array($notifiable->roles) && !empty($notifiable->roles)) {
            $roleName = strtolower($notifiable->roles[0]['name'] ?? '');
            if (in_array($roleName, ['admin', 'clinic', 'pc'])) {
                return $roleName;
            }
        }
        
        // Alternative: check by user type or other properties
        if (isset($notifiable->user_type)) {
            return strtolower($notifiable->user_type);
        }
        
        // Default fallback
        return 'pc';
    }

    /**
     * Get notification data for new message
     *
     * @param string $url
     * @param object $notifiable
     * @return array
     */
    private function getNewMessageNotificationData(string $url, $notifiable): array
    {
        $sender = $this->message->from->name ?? 'User';
        $ticketId = $this->ticketId;
        $senderRole = $this->senderRole ?? 'User';

        return [
            "body" => "A new message was sent by {$sender} on order #{$ticketId}.",
            "icon" => "heroicon-o-chat-bubble-left-right",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "New Order Message",
            "format" => "filament",
            "status" => "success",
            "actions" => [
                [
                    'name' => 'new_message',
                    'label' => 'View Message',
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [
                'thread_id' => $this->threadId,
                'order_id' => $this->ticket->order_id ?? null,
                'sender_role' => $this->senderRole
            ],
            "iconColor" => "success"
        ];
    }

    /**
     * Get notification data for ticket closure
     *
     * @param Thread $thread
     * @param string $url
     * @return array
     */
    private function getTicketClosedNotificationData(Thread $thread, string $url): array
    {
        $ticketId = $thread->id;
        $closedBy = $thread->assignedUser->name ?? 'System';

        return [
            "body" => "Order #{$ticketId} has been closed by {$closedBy}.",
            "icon" => "heroicon-o-x-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "Order Closed",
            "format" => "filament",
            "status" => "warning",
            "actions" => [
                [
                    'name' => 'ticket_closed',
                    'label' => 'View Order',
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [
                'thread_id' => $this->threadId,
                'order_id' => $thread->order_id,
                'closed_by' => $closedBy
            ],
            "iconColor" => "warning"
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param object $notifiable
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticketId,
            'thread_id' => $this->threadId,
            'type' => $this->type,
            'sender_role' => $this->senderRole,
            'order_id' => $this->ticket->order_id ?? null,
        ];
    }
}