<?php

namespace App\Notifications;

use App\Filament\Admin\Resources\SupportTicketResource;
use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Auth;

class SupportNotification extends Notification
{
    use Queueable;

    protected $message;
    protected $ticket;
    protected $ticketId;
    protected $type;
    protected $redirectUrl;
    protected $senderId;

    /**
     * Create a new notification instance.
     *
     * @param SupportTicketMessage|SupportTicket $data
     * @param string|null $type
     * @param string $redirectUrl
     */
    public function __construct($data, ?string $type = null, $redirectUrl = null)
    {
        if ($data instanceof SupportTicketMessage) {
            $this->message = $data;
            $this->ticket = $data->supportTicket; // Get the full ticket relationship
            $this->ticketId = $data->support_ticket_id;
            $this->type = 'new_message';
        } elseif ($data instanceof SupportTicket) {
            $this->ticket = $data; // Store the full ticket object
            $this->ticketId = $data->id;
            $this->type = $type ?? 'ticket_closed';
        }

        $this->redirectUrl = $redirectUrl;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param object $notifiable
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the database representation of the notification.
     *
     * @param object $notifiable
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable)
    {
        // Get the support ticket if we don't have it as an object
        if (!$this->ticket instanceof SupportTicket) {
            $supportTicket = SupportTicket::find($this->ticketId);
        } else {
            $supportTicket = $this->ticket;
        }

        // Generate URL based on notification type and redirect preference
        $url = $this->generateUrl($supportTicket);

        if ($this->type === 'new_message') {
            return $this->getNewMessageNotificationData($url);
        }

        if ($this->type === 'ticket_created') {
            return $this->getTicketCreatedNotificationData($supportTicket, $url);
        }

        // Default: ticket closed notification
        return $this->getTicketClosedNotificationData($supportTicket, $url);
    }

    /**
     * Generate the appropriate URL based on redirect preference and user role
     *
     * @param SupportTicket $supportTicket
     * @return string
     */
    private function generateUrl(SupportTicket $supportTicket): string
    {
        $ticketId = $supportTicket->id;

        if ($this->redirectUrl === 'pc') {

            $user = User::find($supportTicket->sender_id);
            if ($user && isset($user->roles[0]['name']) && $user->roles[0]['name'] === 'Clinic') {
                return route('filament.pc.resources.support-ticket-receiveds.details', [$ticketId]);
            } else {
                return route('filament.pc.resources.support-ticket-assigneds.details', [$ticketId]);
            }
        } elseif ($this->redirectUrl === 'admin') {
            $url =  SupportTicketResource::getUrl(
                name: 'details',
                parameters: ['record' => $ticketId],
                panel: 'admin'
            );
            // Strip domain to get only the path
            $relativePath = ltrim(parse_url($url, PHP_URL_PATH), '/');
            return config('app.admin_url').'/'.$relativePath;
            // return config('app.admin_url')."/support-tickets/$ticketId/details";
            // return route('filament.admin.resources.support-tickets.details', [$ticketId]);
        } elseif ($this->redirectUrl === 'clinic') {
            return route('filament.clinic.resources.support-tickets.index');
        }

        // Default fallback
        return route('filament.admin.resources.support-tickets.details', [$ticketId]);
    }

    /**
     * Get notification data for new message
     *
     * @param string $url
     * @return array
     */
    private function getNewMessageNotificationData(string $url): array
    {
        $ticketId = $this->ticketId;
        $sender = getUser(Auth::user());
        if ($sender && $sender->pcDetails) {
            $companyName = $sender->pcDetails->company_name;
            if (!is_null($companyName) && trim($companyName) !== '') {
                $sender = $companyName;
            } else {
                $sender = $sender->pcDetails->business_name ?? $sender->name ?? 'User';
            }
        } else {
            $sender = $sender->name ?? 'User';
        }
        return [
            "body" => "A new message was sent by {$sender} on ticket #{$ticketId}.",
            "icon" => "heroicon-o-chat-bubble-left-right",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "New Support Ticket Message",
            "format" => "filament",
            "status" => "success",
            "actions" => [
                [
                    'name' => 'new_message',
                    'label' => 'View Message',
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success"
        ];
    }

    /**
     * Get notification data for ticket creation
     *
     * @param SupportTicket $supportTicket
     * @param string $url
     * @return array
     */
    private function getTicketCreatedNotificationData(SupportTicket $supportTicket, string $url): array
    {
        $ticketId = $supportTicket->id;
        $sender = $supportTicket->sender;
        if ($sender && $sender->pcDetails) {
            $companyName = $sender->pcDetails->company_name;
            if (!is_null($companyName) && trim($companyName) !== '') {
                $createdBy = $companyName;
            } else {
                $createdBy = $sender->pcDetails->business_name ?? $sender->name ?? 'User';
            }
        } else {
            $createdBy = $sender->name ?? 'User';
        }

        return [
            "body" => "New support ticket #{$ticketId} has been created by {$createdBy}.",
            "icon" => "heroicon-o-ticket",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "New Support Ticket Created",
            "format" => "filament",
            "status" => "info",
            "actions" => [
                [
                    'name' => 'ticket_created',
                    'label' => 'View Ticket',
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "info"
        ];
    }

    /**
     * Get notification data for ticket closure
     *
     * @param SupportTicket $supportTicket
     * @param string $url
     * @return array
     */
    private function getTicketClosedNotificationData(SupportTicket $supportTicket, string $url): array
    {
        $ticketId = $supportTicket->id;
        $sender = getUser(Auth::user());
        if ($sender && $sender->pcDetails) {
            $companyName = $sender->pcDetails->company_name;
            if (!is_null($companyName) && trim($companyName) !== '') {
                $closedBy = $companyName;
            } else {
                $closedBy = $sender->pcDetails->business_name ?? $sender->name ?? 'User';
            }
        } else {
            $closedBy = $sender->name ?? 'User';
        }

        return [
            "body" => "Support ticket #{$ticketId} has been closed by {$closedBy}.",
            "icon" => "heroicon-o-x-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => "Support Ticket Closed",
            "format" => "filament",
            "status" => "warning",
            "actions" => [
                [
                    'name' => 'ticket_closed',
                    'label' => 'View Ticket',
                    'url' => $url,
                    'color' => 'primary'
                ]
            ],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "warning"
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param object $notifiable
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->ticketId,
            'type' => $this->type,
        ];
    }
}
