<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class PcOnBoardingNotification extends Notification
{
    use Queueable;

    protected $userId;
    public $supplier;


    /**
     * Create a new notification instance.
     */
    public function __construct($userId, $supplier)
    {
        $this->userId = $userId;
        $this->supplier = $supplier;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toDatabase($notifiable)
    {
        Log::info([

            "body" => __('api.notifications.pc_onboarding_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.pc_onboarding_title', ["Supplier" => $this->supplier]),
            "format" => "filament",
            "status" => "success",
            "actions" => [],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => route('filament.admin.resources.users.view', ['record' => $this->userId]) . '?activeTab=Pending',
            "actions" => [
                [
                    'name' => 'pconboarding',
                    'label' => 'View',
                    'url' => config('app.admin_url') . '/users/' . $this->userId . '?activeTab=Pending',
                    'color' => 'primary'
                ]
            ],
        ]);
        return [

            "body" => __('api.notifications.pc_onboarding_body'),
            "icon" => "heroicon-o-check-circle",
            "view" => "filament-notifications::notification",
            "color" => null,
            "title" => __('api.notifications.pc_onboarding_title', ["Supplier" => $this->supplier]),
            "format" => "filament",
            "status" => "success",
            "actions" => [],
            "duration" => "persistent",
            "viewData" => [],
            "iconColor" => "success",
            "target_url" => config('app.admin_url') . '/' . ltrim(parse_url(route('filament.admin.resources.users.view', ['record' => $this->userId]), PHP_URL_PATH), '/') . '?activeTab=Pending',
            "actions" => [
                [
                    'name' => 'pconboarding',
                    'label' => 'View',
                    'url' => config('app.admin_url') . '/' . ltrim(parse_url(route('filament.admin.resources.users.view', ['record' => $this->userId]), PHP_URL_PATH), '/') . '?activeTab=Pending',

                    'color' => 'primary'
                ]
            ],
        ];
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
