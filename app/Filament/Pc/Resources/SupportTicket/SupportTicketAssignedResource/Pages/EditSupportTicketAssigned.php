<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Pc\Resources\SupportTicketAssignedResource;

class EditSupportTicketAssigned extends EditRecord
{
    protected static string $resource = SupportTicketAssignedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title(__('message_pc.support_ticket_assigned.updated_title'))
            ->body(__('message_pc.support_ticket_assigned.updated_body'));
    }
}
