<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Pc\Resources\SupportTicketAssignedResource;
use App\Models\SupportTicketMessage;
use App\Models\User;
use App\Models\Order;
use App\Models\SupportTicket;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;

class ListSupportTicketAssigneds extends ListRecords
{
    protected static string $resource = SupportTicketAssignedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Ticket')
                ->visible(function () {
                    $user = auth()->user();
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('dpharma-support_create');
                }),
        ];
    }
    public function getTitle(): string
    {
        return 'DPharma Support';
    }

    public function getBreadcrumbs(): array
    {
        return [
            // '1' => (__('message_pc.support_ticket_assigned.breadcrumb_support')),
            // $this->getResource()::getUrl("index") => (__('message_pc.support_ticket_assigned.breadcrumb_dpharma_support')),
            // '2' => (__('message_pc.support_ticket_assigned.breadcrumb_list')),
        ];
    }
}
