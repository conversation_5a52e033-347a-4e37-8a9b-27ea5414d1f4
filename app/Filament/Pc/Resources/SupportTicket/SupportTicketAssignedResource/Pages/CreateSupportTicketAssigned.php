<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages;

use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Pc\Resources\SupportTicketAssignedResource;
use App\Mail\SupportTicketCreated;
use App\Mail\SupportTicketMail;
use App\Models\User;
use App\Notifications\SupportNotification;
use Filament\Actions\Action;
use Google\Service\DatabaseMigrationService\EntityDdl;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class CreateSupportTicketAssigned extends CreateRecord
{
    protected static string $resource = SupportTicketAssignedResource::class;
    public $supportTicket;
    public $messages = [];
    public $ticket;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(SupportTicketAssignedResource::getUrl()),
        ];
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message_pc.support_ticket_assigned.created_title'))
            ->title(__('message_pc.support_ticket_assigned.created_body'));
    }
    public function getBreadcrumbs(): array
    {
        return [
            '1' => (__('message_pc.support_ticket_assigned.breadcrumb_support')),
            $this->getResource()::getUrl("index") => (__('message_pc.support_ticket_assigned.breadcrumb_dpharma_support')),
            3 => (__('message_pc.support_ticket_assigned.breadcrumb_add_ticket')),
        ];
    }
    public function getTitle(): string
    {
        return 'Add Ticket';
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Send'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function afterCreate(): void
    {
        $supportTicket = $this->record;

        try {
            $recipient = ($supportTicket->sender_id == Auth::id())
                ? User::find($supportTicket->receiver_id)
                : User::find($supportTicket->sender_id);

            $superAdmins = \App\Models\User::whereHas('roles', function ($query) {
                $query->where('name', 'Super Admin');
            })->get();

            $superAdminIds = $superAdmins->pluck('id');

            $subUsersWithPermission = \App\Models\User::whereIn('parent_id', $superAdminIds)->get()->filter(function ($user) {
                return $user->can('support-tickets_support ticket view');
            });

            $allRecipients = $superAdmins->merge($subUsersWithPermission)->unique('id');

            foreach ($allRecipients as $recipientUser) {
                if ($recipientUser->email) {
                    Mail::to($recipientUser->email)->send(new \App\Mail\SupportTicketMail($supportTicket, 'admin', $recipientUser));
                }
                if ($recipientUser->id !== Auth::id()) {
                    $recipientUser->notify(new \App\Notifications\SupportNotification($supportTicket, 'ticket_created', 'admin'));
                }
            }
        } catch (\Exception $e) {
            \Log::error("Failed to send support ticket creation email or notification: {$e->getMessage()}");
        }
    }
}
