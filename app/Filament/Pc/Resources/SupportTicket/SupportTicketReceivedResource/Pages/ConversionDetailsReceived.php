<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages;

use App\Filament\Pc\Resources\SupportTicketReceivedResource;
use App\Models\SubOrder;
use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use App\Models\Thread;
use App\Models\ThreadMessage;
use App\Models\User;
use App\Notifications\SupportNotification;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Filament\Actions\Action;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Livewire\Attributes\On;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\WithFileUploads;

class ConversionDetailsReceived extends Page implements HasForms
{
    use InteractsWithForms;
    use WithFileUploads;

    protected static string $resource = SupportTicketReceivedResource::class;
    protected static string $view = 'filament.pc.pages.conversion-details';
    protected static ?string $title = null;
    public $ticket;
    public $messageText = '';
    public $subOrder;
    public $messages = [];
    public $attachedFiles = [];
    public $perPage = 15;
    public $page = 1;
    public $hasMoreMessages = true;



    public function mount($record)
    {

        $this->ticket = SupportTicket::findOrFail($record);
        $this->subOrder = SubOrder::where('order_id', $this->ticket->order_id)->first();
        // $this->subOrder = SubOrder::where('order_id', $this->ticket->order_id)->where('user_id', Auth::user()->id)->first();
        auth()->user()->unreadNotifications
            ->filter(fn($n) => isset($n->data['actions'][0]['url']) && preg_match('/\/' . preg_quote($record, '/') . '(\/|$)/', $n->data['actions'][0]['url']))
            ->each->markAsRead();
        //auth()->user()->unreadNotifications()->where('data->body', 'like', '%#' . $record . '%')->update(['read_at' => now()]);
        $this->loadMessages();
        $this->markMessagesAsRead();
    }

    public function getTitle(): string
    {
        $orderNumber = $this->ticket->order?->order_number ?? '-';

        return 'TKT-' . $this->ticket->id . ' | Order ID: ' . '#' . $orderNumber;
    }

    public function getBreadcrumbs(): array
    {
        return [
            '1' => (__('message_pc.support_ticket_received.breadcrumb_support')),
            $this->getResource()::getUrl("index") => (__('message_pc.support_ticket_received.breadcrumb_facilities_support')),
            '2' => (__('message_pc.support_ticket_received.breadcrumb_ticket_details')),
        ];
    }

    public function markMessagesAsRead()
    {
        SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    public function ticket($ticketId)
    {
        $this->ticket = SupportTicket::find($ticketId);
        $this->page = 1;
        $this->messages = [];
        $this->loadMessages();

        $this->markMessagesAsRead();
    }

    public function loadMessages()
    {

        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);
    }

    public function loadMoreMessages()
    {
        if (!$this->hasMoreMessages) {
            return;
        }

        $this->page++;

        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        // Merge with existing messages in the right order
        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);

        $this->dispatch('messages-loaded');
    }

    public function sendMessage()
    {
        if ($this->ticket->status === 'closed') {
            Notification::make()
                // ->title(__('message_pc.support_ticket_assigned.closed_send_error_title'))
                ->title(__('message_pc.support_ticket_assigned.closed_send_error_body'))
                ->danger()
                ->send();
            return;
        }

        if (empty(trim($this->messageText)) && empty($this->attachedFiles)) {
            $this->addError('messageText', 'You cannot send an empty message. Please add some text or attach a file.');
            return;
        }

        $validationPassed = $this->validateUploads();

        if (empty($this->messageText) && !$validationPassed) {
            $this->addError('attachedFiles', 'Your message was not sent because the attached files are invalid.');
            return;
        }

        $message = SupportTicketMessage::create([
            'support_ticket_id' => $this->ticket->id,
            'from_id' => auth()->user()->id,
            'message' => $this->messageText ?? '',
        ]);

        if (!empty($this->attachedFiles)) {
            $folderPath = config('constants.api.media.support_ticket') . $this->ticket->id;
            $mediaData = [];

            foreach ($this->attachedFiles as $key => $file) {
                $extension = $file->getClientOriginalExtension();

                $fileName = hash('sha256', $file->getFilename() . '_' . now()) . '.' . $extension;

                getStorageDisk()->putFileAs($folderPath, $file, $fileName);

                $mediaData[] = [
                    "model_type"            => 'App\Models\SupportTicketMessage',
                    "model_id"              => $message->id,
                    "uuid"                  => Str::uuid()->toString(),
                    "collection_name"       => 'support-ticket-images',
                    "name"                  => $fileName,
                    "file_name"             => $fileName,
                    "mime_type"             => $file->getMimeType(),
                    "disk"                  => config('filesystems.default'),
                    "conversions_disk"      => config('filesystems.default'),
                    "size"                  => $file->getSize(),
                    "manipulations"         => json_encode([]),
                    "custom_properties"     => json_encode([]),
                    "generated_conversions" => json_encode([]),
                    "responsive_images"     => json_encode([]),
                    "order_column"          => $key + 1,
                    "created_at"            => now(),
                    "updated_at"            => now(),
                ];
            }

            if (!empty($mediaData)) {
                \App\Models\Media::insert($mediaData);
            }
        }

        // $recipient = ($this->ticket->sender_id == Auth::id())
        //     ? User::find($this->ticket->receiver_id)
        //     : User::find($this->ticket->sender_id);

        // $adminRecord = \DB::table('users')
        //     ->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
        //     ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
        //     ->where('roles.name', 'Super Admin')
        //     ->select('users.id')
        //     ->first();

        // $superAdmins = User::whereHas('roles', function ($query) {
        //     $query->where('name', 'Super Admin');
        // })->where('id', '!=', auth()->id())->get();

        // // Notify all Super Admin users
        // foreach ($superAdmins as $admin) {
        //     $admin->notify(new SupportNotification($message, 'new_message', 'admin'));
        // }

        // if (auth()->user()->hasRole('admin')) {
        //     $recipient->notify(new \App\Notifications\SupportNotification($message, 'new_message', 'admin'));
        // }
        // if ($recipient && $recipient->id !== auth()->user()->id) {
        //     // $recipient->notify(new SupportNotification($message));
        //     $recipient->notify(new \App\Notifications\SupportNotification($message, 'new_message', 'pc'));
        // }

        $this->messageText = '';
        $this->attachedFiles = [];
        $this->page = 1;
        $this->loadMessages();

        $this->dispatch('message-sent');
    }

    public function validateUploads()
    {
        if (empty($this->attachedFiles)) {
            return true;
        }

        if (count($this->attachedFiles) > 5) {
            $this->addError('attachedFiles', __('message_pc.support_ticket_received.file_upload_limit_error'));
            $this->attachedFiles = array_slice($this->attachedFiles, 0, 5);
            return false;
        }

        $hasInvalidFiles = false;
        $invalidFiles = [];

        foreach ($this->attachedFiles as $key => $file) {
            $mimeType = $file->getMimeType();
            $extension = strtolower($file->getClientOriginalExtension());
            $fileSizeKB = $file->getSize() / 1024;

            $isPdf = in_array($mimeType, ['application/pdf', 'application/x-pdf']) || $extension === 'pdf';
            $isImage = strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png', 'gif']);
            $sizeLimitKB = 2048; // 2MB

            if (!$isPdf && !$isImage) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            } elseif ($fileSizeKB > $sizeLimitKB) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            }
        }

        if ($hasInvalidFiles) {
            $errorMessage = __('message_pc.support_ticket_received.file_type_limit_error');
            if (!empty($invalidFiles)) {
                $errorMessage .= ' Invalid files: ' . implode(', ', $invalidFiles);
            }
            $this->addError('attachedFiles', $errorMessage);
            $this->attachedFiles = array_values($this->attachedFiles); // Reindex array
        } else {
            // Clear any previous errors if validation passes
            $this->resetErrorBag('attachedFiles');
        }

        return !$hasInvalidFiles;
    }

    public function updatedAttachedFiles()
    {
        if ($this->ticket->status === 'closed') {
            $this->attachedFiles = [];
            Notification::make()
                // ->title(__('message_pc.support_ticket_received.closed_file_attach_title'))
                ->title(__('message_pc.support_ticket_received.closed_file_attach_body'))
                ->danger()
                ->send();
            return;
        }

        $validationPassed = $this->validateUploads();

        if ($validationPassed && !empty($this->attachedFiles)) {
            $this->resetErrorBag('messageText');
        }
    }

    public function updatedMessageText($value)
    {
        if ($this->ticket->status === 'closed') {
            $this->messageText = '';
            Notification::make()
                // ->title(__('message_pc.support_ticket_received.closed_typing_title'))
                ->title(__('message_pc.support_ticket_received.closed_typing_body'))
                ->danger()
                ->send();
            return;
        }

        // Clear messageText error if the input is non-empty
        if (!empty(trim($value))) {
            $this->resetErrorBag('messageText');
        }
    }


    public function removeFile($index)
    {
        if (isset($this->attachedFiles[$index])) {
            unset($this->attachedFiles[$index]);
            $this->attachedFiles = array_values($this->attachedFiles);
        }
    }

    public function markAsClosed()
    {
        $this->ticket->update([
            'status' => 'closed',
            'closed_at' => now(),
        ]);

        try {
            $superAdmins = \App\Models\User::whereHas('roles', function ($query) {
                $query->where('name', 'Super Admin');
            })->get();

            $superAdminIds = $superAdmins->pluck('id');

            $subUsersWithPermission = \App\Models\User::whereIn('parent_id', $superAdminIds)
                ->get()
                ->filter(function ($user) {
                    return $user->can('support-tickets_support ticket view');
                });

            $allRecipients = $superAdmins->merge($subUsersWithPermission)->unique('id');

            foreach ($allRecipients as $recipient) {
                if ($recipient->email) {
                    Mail::to($recipient->email)->send(new \App\Mail\SupportTicketMail($this->ticket, 'admin', $recipient));
                }
                if ($recipient->id !== auth()->user()->id) {
                    $recipient->notify(new \App\Notifications\SupportNotification($this->ticket, 'ticket_closed', 'admin'));
                }
            }
            $sender = \App\Models\User::find($this->ticket->sender_id);
            if ($sender && $sender->email) {
                Mail::to($sender->email)->send(new \App\Mail\SupportTicketMail($this->ticket, 'sender', $sender));
                if ($sender->id !== auth()->user()->id) {
                    $sender->notify(new \App\Notifications\SupportNotification($this->ticket, 'ticket_closed', 'sender'));
                }
            }
        } catch (\Exception $e) {
            \Log::error("Failed to send support ticket closure email: {$e->getMessage()}");
        }

        Notification::make()
            ->title(__('message_pc.support_ticket_received.ticket_closed_title'))
            ->success()
            ->send();
    }

    public function notifyAlreadyClosed()
    {
        Notification::make()
            // ->title(__('message_pc.support_ticket_received.closed_ticket_title'))
            ->title(__('message_pc.support_ticket_received.closed_ticket_body'))
            ->danger()
            ->send();
    }

    public function markAsClosedAction()
    {
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        if ($isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('facilities-support_mark as closed') || auth()->user()->can('support-tickets_mark as closed')) {
            return Action::make('markAsClosed')
                ->action(function () {
                    // $this->ticket->refresh();
                    if ($this->ticket->status === 'closed') {
                        $this->notifyAlreadyClosed();
                    } else {
                        $this->markAsClosed();
                        $this->ticket->refresh();
                    }
                })
                ->requiresConfirmation(fn() => $this->ticket->status === 'open')
                ->label('Mark as Closed')
                ->color('primary')
                ->visible(fn() => $this->ticket->status !== 'closed');
        }
    }   
}
