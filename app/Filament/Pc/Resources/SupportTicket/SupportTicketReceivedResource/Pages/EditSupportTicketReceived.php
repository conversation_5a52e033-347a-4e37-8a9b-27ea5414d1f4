<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use App\Filament\Pc\Resources\SupportTicketReceivedResource;

class EditSupportTicketReceived extends EditRecord
{
    protected static string $resource = SupportTicketReceivedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
}
