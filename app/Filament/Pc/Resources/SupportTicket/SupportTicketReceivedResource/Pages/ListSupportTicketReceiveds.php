<?php

namespace App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Pc\Resources\SupportTicketReceivedResource;

class ListSupportTicketReceiveds extends ListRecords
{
    protected static string $resource = SupportTicketReceivedResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getTitle(): string
    {
        return __('message_pc.support_ticket_received.breadcrumb_facilities_support');
    }

    public function getBreadcrumbs(): array
    {
        return [
            // '1' => (__('message_pc.support_ticket_received.breadcrumb_support')),
            // $this->getResource()::getUrl("index") => (__('message_pc.support_ticket_received.breadcrumb_facilities_support')),
            // '2' => (__('message_pc.support_ticket_received.breadcrumb_list')),
        ];
    }
}
