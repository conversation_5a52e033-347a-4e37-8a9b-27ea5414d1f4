<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\ClinicDetailResource\Pages;
use App\Filament\Pc\Resources\ClinicDetailResource\Pages\ViewClinicDetail;
use App\Models\BusinessType;
use App\Models\ClinicCredit;
use App\Models\ClinicCreditHistory;
use App\Models\ClinicDetail;
use App\Models\ClinicPharmaSupplier;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class ClinicDetailResource extends Resource
{
    protected static ?string $model = ClinicDetail::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?string $navigationLabel = 'Facilities';

    protected static ?string $slug = 'facilities';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([]);
    }

    public static function canAccess(): bool
    {
        /** @var \App\Models\User $user */
        $user = Auth::user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facility_view')
            ||  $user->can('facility_open account request') || $user->can('facility_assign credit')
            || $user->can('facility_edit assign credit') || $user->can('facility_approve facility')
            || $user->can('facility_reject facility') || $user->can('facility_add facility');
    }

    public static function table(Table $table): Table
    {

        $user = getUser(Auth::user());
        $pcUserId = $user->parent_id ?? $user->id;
        return $table
            ->query(function (Builder $query) use ($pcUserId) {
                return ClinicDetail::query()
                    ->with('clinicAccountType', 'user', 'user.clinicCreditHistory')
                    ->whereHas('user')
                    ->where(function ($query) use ($pcUserId) {
                        // $query->whereHas('user.pharmaSuppliers', function ($q) use ($pcUserId) {
                        //     $q->where('pc_id', $pcUserId)
                        //         ->where('status', 'approved');
                        // })
                        //     ->orWhereHas('user.orders.subOrders', function ($q) use ($pcUserId) {
                        //         $q->where('user_id', $pcUserId);
                        //     });
                    });
            })
            ->emptyStateHeading('No Facilities records found')
            ->columns([
                TextColumn::make('id')
                    ->label('Facility ID')
                    ->sortable()
                    ->searchable(query: function ($query, $search) {
                        $numericValue = preg_replace('/[^0-9]/', '', $search); // strip everything except digits
                        $numericValue = $numericValue !== '' ? (int) $numericValue : null;

                        if (!is_null($numericValue) && filter_var($numericValue, FILTER_VALIDATE_INT) !== false) {
                            return $query->where('id', '=', $numericValue);
                        }
                    })
                    ->getStateUsing(function ($record) {
                        return $record->id;
                    })
                    ->formatStateUsing(fn($state): string => $state),
                TextColumn::make('clinic_name')
                    ->label('Facility Name')
                    ->sortable()->searchable(),
                TextColumn::make('clinicAccountType.name')->label('Facility Type')->sortable()->searchable(),
                TextColumn::make('user.clinicCreditHistory.total_credit_amount')
                    ->label('Credit')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        return $query->whereHas('user.clinicCreditHistory', function ($q) use ($user) {
                            $q->where('supplier_id', $user->id);
                        })->orderBy(function ($query) use ($user, $direction) {
                            $query->select('total_credit_amount')
                                ->from('clinic_credit_histories')
                                ->whereColumn('facility_id', 'clinic_details.user_id')
                                ->where('supplier_id', $user->id)
                                ->orderBy('id', 'desc')
                                ->limit(1);
                        }, $direction);
                    })
                    ->searchable(query: function ($query, $search) {
                        $user = getUser(Auth::user());
                        $numericValue = preg_replace('/[^0-9.]/', '', $search);
                        if (
                            $numericValue !== ''
                            && is_numeric($numericValue)
                            && preg_match('/^\d+(\.\d{1,2})?$/', $numericValue) // Only accept proper formats
                        ) {
                            // Cast to float to match DB numeric/decimal type
                            $numericValue = floatval($numericValue);

                            return $query->whereHas('user.clinicCreditHistory', function ($q) use ($numericValue, $user) {
                                $q->where('total_credit_amount', '=', $numericValue)
                                    ->where('supplier_id', $user->id);
                            });
                        }
                    })
                    ->getStateUsing(function ($record) {
                        $user = getUser(Auth::user());
                        $credit_amount = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id', $record->user_id)->orderBy('id', 'desc')->value('total_credit_amount');
                        return ($credit_amount) ? 'RM ' . $credit_amount : '-';
                    }),
                TextColumn::make('user.clinicCreditHistory.remaining_amount')
                    ->label('Credit Remaining')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        return $query->whereHas('user.clinicCreditHistory', function ($q) use ($user) {
                            $q->where('supplier_id', $user->id);
                        })->orderBy(function ($query) use ($user, $direction) {
                            $query->select(DB::raw('CASE WHEN remaining_amount < 0 THEN 0 ELSE remaining_amount END'))
                                ->from('clinic_credit_histories')
                                ->whereColumn('facility_id', 'clinic_details.user_id')
                                ->where('supplier_id', $user->id)
                                ->orderBy('id', 'desc')
                                ->limit(1);
                        }, $direction);
                    })
                    ->searchable(query: function ($query, $search) {
                        // Allow digits, decimal point, and optional minus sign
                        $numericValue = preg_replace('/[^0-9.\-]/', '', $search);
                        $user = getUser(Auth::user());

                        // Validate as numeric and allow up to 3 decimal places (with optional minus)
                        if (
                            $numericValue !== ''
                            && is_numeric($numericValue)
                            && preg_match('/^-?\d+(\.\d{1,3})?$/', $numericValue) // allow negative and up to 3 decimal places
                        ) {
                            $numericValue = floatval($numericValue);
                            $adjustedValue = $numericValue < 0 ? 0 : $numericValue;
                            // return $query->whereHas('user.clinicCredit', function ($q) use ($numericValue) {
                            //     $q->where('remaining_balance', '=', $numericValue)
                            //         ->where('supplier_id', auth()->id());
                            // });

                            return $query->whereHas('user.clinicCreditHistory', function ($q) use ($adjustedValue, $user) {

                                $q->whereRaw('CASE WHEN remaining_amount < 0 THEN 0 ELSE remaining_amount END = ?', [$adjustedValue])
                                    ->where('supplier_id', $user->id);
                            });
                        }

                        return $query;
                    })
                    ->getStateUsing(function ($record) {
                        $credit_amount = ClinicCreditHistory::where('supplier_id', getUser(Auth::user())->id)
                            ->where('facility_id', $record->user_id)
                            ->orderBy('id', 'desc')
                            ->value('remaining_amount');
                        if ($credit_amount < 0) {
                            $credit_amount = '0.00';
                        }

                        return isset($credit_amount) ?  'RM ' . $credit_amount : '-';
                    }),
                TextColumn::make('user.clinicCreditHistory.order_credit_used')
                    ->label('Unpaid Credit Used')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        return $query->whereHas('user.clinicCreditHistory', function ($q) use ($user) {
                            $q->where('supplier_id', $user->id);
                        })->orderBy(function ($query) use ($user, $direction) {
                            $query->select(DB::raw('CASE WHEN order_credit_used < 0 THEN 0 ELSE order_credit_used END'))
                                ->from('clinic_credit_histories')
                                ->whereColumn('facility_id', 'clinic_details.user_id')
                                ->where('supplier_id', $user->id)
                                ->orderBy('id', 'desc')
                                ->limit(1);
                        }, $direction);
                    })
                    ->searchable(query: function ($query, $search) {
                        // Allow digits, decimal point, and optional minus sign
                        $numericValue = preg_replace('/[^0-9.\-]/', '', $search);
                        $user = getUser(Auth::user());

                        // Validate as numeric and allow up to 3 decimal places (with optional minus)
                        if (
                            $numericValue !== ''
                            && is_numeric($numericValue)
                            && preg_match('/^-?\d+(\.\d{1,3})?$/', $numericValue) // allow negative and up to 3 decimal places
                        ) {
                            $numericValue = floatval($numericValue);
                            $adjustedValue = $numericValue < 0 ? 0 : $numericValue;
                            // return $query->whereHas('user.clinicCredit', function ($q) use ($numericValue) {
                            //     $q->where('remaining_balance', '=', $numericValue)
                            //         ->where('supplier_id', auth()->id());
                            // });

                            return $query->whereHas('user.clinicCreditHistory', function ($q) use ($adjustedValue, $user) {

                                $q->whereRaw('CASE WHEN order_credit_used < 0 THEN 0 ELSE order_credit_used END = ?', [$adjustedValue])
                                    ->where('supplier_id', $user->id);
                            });
                        }

                        return $query;
                    })
                    ->getStateUsing(function ($record) {
                        $credit_amount = ClinicCreditHistory::where('supplier_id', getUser(Auth::user())->id)
                            ->where('facility_id', $record->user_id)
                            ->orderBy('id', 'desc')
                            ->value('order_credit_used');
                        if ($credit_amount < 0) {
                            $credit_amount = '0.00';
                        }

                        return isset($credit_amount) ?  'RM ' . $credit_amount : '-';
                    }),


                // TextColumn::make('status')
                //     ->label('Status')
                //     ->formatStateUsing(fn ($record): string => $record->status ? 'Yes' : 'No'),

                TextColumn::make('status')
                    ->label('Associated Facility Status')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        return $query->orderBy(function ($query) use ($user, $direction) {
                            $query->select(DB::raw('CASE WHEN EXISTS (
                                SELECT 1 FROM clinic_pharma_suppliers
                                WHERE pc_id = ? AND clinic_id = clinic_details.user_id
                            ) THEN 1 ELSE 0 END'))
                                ->addBinding($user->id, 'select');
                        }, $direction);
                    })
                    ->searchable()
                    ->badge()
                    // ->formatStateUsing(fn ($state): string => $state == 'approved' ? 'Accepted' : ucfirst($state))
                    ->formatStateUsing(function ($record) {
                        $user = getUser(Auth::user());
                        $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                        return ($credit_amount) ? 'Yes' : 'No';
                    })
                    ->color(function ($record) {
                        $user = getUser(Auth::user());
                        $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                        $state =  ($credit_amount) ? 'Yes' : 'No';
                        $st = match ($state) {
                            'No' => 'warning',
                            'Yes' => 'success',
                            'rejected' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),
                TextColumn::make('user')
                    ->label('Assign Credit Status')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        return $query->orderBy(function ($query) use ($user, $direction) {
                            $query->select(DB::raw('CASE WHEN EXISTS (
                                SELECT 1 FROM clinic_credit_histories
                                WHERE supplier_id = ? AND facility_id = clinic_details.user_id
                            ) THEN 1 ELSE 0 END'))
                                ->addBinding($user->id, 'select');
                        }, $direction);
                    })
                    ->searchable(false)
                    ->badge()

                    // ->formatStateUsing(fn ($state): string => $state == 'approved' ? 'Accepted' : ucfirst($state))
                    ->formatStateUsing(function ($record) {
                        $user = getUser(auth()->user());
                        $credit_amount = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id', $record->user->id)->first();
                        return ($credit_amount) ? 'Yes' : 'No';
                    })
                    ->color(function ($record) {
                        $credit_amount = ClinicCreditHistory::where('supplier_id', Auth::id())->where('facility_id', $record->user->id)->first();
                        $state =  ($credit_amount) ? 'Yes' : 'No';
                        $st = match ($state) {
                            'No' => 'warning',
                            'Yes' => 'success',
                            'rejected' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),
            ])
            ->filters([
                SelectFilter::make('clinicAccountType.clinic_account_type_id')
                    ->label('Facility Type')
                    ->relationship('clinicAccountType', 'name')
                    ->searchable()
                    ->preload(),
                SelectFilter::make('clinic_name')->label('Facility Name')
                    ->searchable()
                    ->preload()
                    ->options(ClinicDetail::whereNotNull('clinic_name')
                        ->with('clinicAccountType', 'user', 'user.clinicCreditHistory')
                        ->where(function ($query) use ($pcUserId) {
                            $query->whereHas('user.pharmaSuppliers', function ($q) use ($pcUserId) {
                                $q->where('pc_id', $pcUserId)
                                    ->where('status', 'approved');
                            })
                                ->orWhereHas('user.orders.subOrders', function ($q) use ($pcUserId) {
                                    $q->where('user_id', $pcUserId);
                                });
                        })
                        ->pluck('clinic_name', 'clinic_name')),
                // SelectFilter::make('status')  // Custom name to avoid conflict
                //     ->label('Associated Facility Status')
                //     ->options(
                //         collect([
                //             'no' => 'No',
                //             'yes' => 'Yes',

                //         ])->filter()
                //     )
                //     ->query(function (Builder $query, array $state) {
                //         if (!empty($state['value'])) {
                //             $query->whereHas('user', function ($query) use ($state) {
                //                 $query->where('verification_status', $state['value']);
                //             });
                //         }
                //     })
                //     ->searchable()
                //     ->preload(),
                // New credit amount filter
                Filter::make('credit_amount')
                    ->label('Credit (RM)')
                    ->form([
                        TextInput::make('min_credit')
                            ->label('Credit Amount')
                            ->numeric()
                            ->inputMode('decimal'),
                    ])
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['min_credit']) {
                            return null;
                        }
                        return $data['min_credit'];
                    })
                    ->query(function (Builder $query, array $data) {
                        $user = getUser(Auth::user());
                        // dd($data['min_credit']);
                        return $query->when(
                            filled($data['min_credit'] ?? null),
                            fn($query) => $query->whereHas('user.clinicCreditHistory', function ($q) use ($data, $user) {
                                $q->where('total_credit_amount', '=', $data['min_credit'])
                                    ->where('supplier_id', $user->id);
                            })
                        );
                    })
            ])
            ->actions([
                // Action::make('credit-line-add')
                //     ->icon('iconoir-hand-card')
                //     ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                //     ->visible(function ($record) {
                //         $user = getUser(Auth::user());
                //         $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();

                //         $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($credit_amount);
                //         /** @var \App\Models\User $user */
                //         $user = Auth::user();

                //         return $approved  && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_assign credit'));
                //     })
                //     ->label(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                //     ->tooltip(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                //     ->color('success')
                //     ->iconButton()
                //     ->form([
                //         TextInput::make('assigned_credit_line_amount')
                //             ->rules(['required', 'numeric'])
                //             ->placeholder('Enter credit amount')
                //             ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                //             ->minValue(0)
                //             ->maxValue(1000000)
                //             ->validationMessages([
                //                 'required' => 'The Credit Amount is required.',
                //                 'max' => 'The Credit Amount must not exceed 1,000,000.',
                //             ])
                //             ->extraAttributes([
                //                 'inputmode' => 'numeric',
                //                 'pattern' => '[0-9]*',
                //                 'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                //                 'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                //             ])
                //             ->formatStateUsing(function ($record) {
                //                 $credit_amount = ClinicCreditHistory::where('supplier_id', Auth::id())->where('facility_id', $record->user_id)->pluck('credit_amount')->first();
                //                 return null;
                //             }),
                //     ])
                //     ->modalSubmitAction(false)
                //     ->extraModalFooterActions(fn (ClinicDetail $record): array => [
                //         \Filament\Tables\Actions\Action::make('save')
                //             ->label('Save')
                //             ->color('success')
                //             ->requiresConfirmation()
                //             ->action(function (Component $livewire) use ($record) {
                //                 $data = $livewire->mountedTableActionsData[0];
                //                 // Create a new credit line
                //                 $lastClinicCredit = ClinicCreditHistory::where([
                //                     'facility_id' => $record->user_id,
                //                     'supplier_id' => Auth::id(),
                //                 ])->orderBy('id', 'desc')->first();
                //                 if (empty($lastClinicCredit)) {
                //                     $clinicCredit = new ClinicCreditHistory();
                //                     $clinicCredit->facility_id = $record->user_id;
                //                     $clinicCredit->supplier_id = Auth::id();
                //                     $clinicCredit->credit_amount = $data['assigned_credit_line_amount'];
                //                     $clinicCredit->debit_amount = 0;
                //                     $clinicCredit->edit_credit = 0;
                //                     $clinicCredit->order_credit_used = 0;
                //                     $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                //                     $clinicCredit->remaining_amount = $data['assigned_credit_line_amount'];
                //                     $clinicCredit->reference_id = Auth::id();
                //                     $clinicCredit->reference_value = 'user';
                //                     $clinicCredit->action = 'PS Assign Credit';
                //                     $clinicCredit->save();
                //                 } else {
                //                     $remainingAmount = 0;
                //                     // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                //                     if ($lastClinicCredit->remaining_amount == 0) {
                //                         $remainingAmount = $data['assigned_credit_line_amount'] + $lastClinicCredit->total_credit_amount;
                //                         $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                //                     } else {
                //                         if ($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used) {
                //                             $remainingAmount = 0;
                //                         } else {
                //                             $remainingAmount = $data['assigned_credit_line_amount'] + $lastClinicCredit->remaining_amount;
                //                         }
                //                     }

                //                     // dd($remainingAmount);
                //                     $clinicCredit = new ClinicCreditHistory();
                //                     $clinicCredit->facility_id = $record->user_id;
                //                     $clinicCredit->supplier_id = Auth::id();
                //                     $clinicCredit->credit_amount = $data['assigned_credit_line_amount'];
                //                     $clinicCredit->debit_amount = 0;
                //                     $clinicCredit->edit_credit = 0;
                //                     $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used;
                //                     $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount + $data['assigned_credit_line_amount'];
                //                     $clinicCredit->remaining_amount = $remainingAmount;
                //                     $clinicCredit->reference_id = Auth::id();
                //                     $clinicCredit->reference_value = 'user';
                //                     $clinicCredit->action = 'PS Assign Credit';
                //                     $clinicCredit->save();
                //                 }

                //                 //Activitylog start
                //                 $newValues = [
                //                     'credit_amount' => $clinicCredit->credit_amount,
                //                     'total_credit_amount' => $clinicCredit->total_credit_amount,
                //                     'remaining_amount' => $clinicCredit->remaining_amount,
                //                 ];
                //                 //Activitylog end

                //                 $clinicCredit->save();
                //                 activity()
                //                 ->causedBy(Auth::user())
                //                 ->useLog('clinic_credit_assigned')
                //                 ->performedOn($clinicCredit)
                //                 ->withProperties([
                //                     // 'old' => $oldValues,
                //                     'attributes' => $newValues,
                //                 ])
                //                 ->log("{$record->clinic_name} Credit has been assigned successfully.");
                //             })
                //             ->cancelParentActions(),
                //     ]),
                Action::make('credit-line-add')
                    ->icon('iconoir-hand-card')
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->visible(function ($record) {
                        // return true;
                        $user = getUser(Auth::user());

                        $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();

                        $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($credit_amount);
                        /** @var \App\Models\User $user */
                        $user = Auth::user();

                        return $approved  && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || auth()->user()->can('credit-line-orders_assign credit'));
                    })
                    ->label(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                    ->tooltip(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                    ->color('success')
                    ->iconButton()
                    ->form([
                        TextInput::make('assigned_credit_line_amount')
                            ->rules(['required', 'numeric'])
                            ->placeholder('Enter credit amount')
                            ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                            ->minValue(0)
                            ->maxValue(1000000)
                            ->live()
                            ->afterStateUpdated(function ($livewire, $component) {
                                $livewire->validateOnly($component->getStatePath());
                            })
                            ->validationMessages([
                                'required' => 'The Credit Amount is required.',
                                'max' => 'The Credit Amount must not exceed 1,000,000.',
                            ])
                            ->validationAttribute('Credit Amount')
                            ->extraAttributes([
                                'inputmode' => 'numeric',
                                'pattern' => '[0-9]*',
                                'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                            ])
                            ->formatStateUsing(function ($record) {
                                $credit_amount = ClinicCreditHistory::where('supplier_id', Auth::id())->where('facility_id', $record->user_id)->pluck('credit_amount')->first();
                                return null;
                            }),
                    ])
                    ->modalSubmitAction(false)
                    ->extraModalFooterActions(function (ClinicDetail $record): array {
                        return [
                            Action::make('save')
                                ->label('Save')
                                ->color('success')
                                ->before(function (Component $livewire) {
                                    // dd($livewire);
                                    $data = $livewire->mountedTableActionsData[0] ?? [];
                                    if (
                                        empty($data['assigned_credit_line_amount']) ||
                                        !is_numeric($data['assigned_credit_line_amount']) ||
                                        $data['assigned_credit_line_amount'] < 0
                                    ) {
                                        // dd('here');
                                        $error = ValidationException::withMessages([
                                            'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount is required.',
                                        ]);
                                        // dd($error);
                                        throw $error;
                                    } else if ($data['assigned_credit_line_amount'] > 1000000) {
                                        $error = ValidationException::withMessages([
                                            'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount field must not be greater than 1000000.',
                                        ]);
                                        // dd($error);
                                        throw $error;
                                    }

                                    return false;
                                })

                                ->requiresConfirmation(function (Component $livewire) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];

                                    if (
                                        !empty($data['assigned_credit_line_amount']) &&
                                        is_numeric($data['assigned_credit_line_amount']) &&
                                        $data['assigned_credit_line_amount'] >= 0 &&
                                        $data['assigned_credit_line_amount'] <= 1000000
                                    ) {
                                        return true;
                                    }

                                    return false;
                                })
                                ->cancelParentActions(function (Component $livewire, $action) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];
                                    if (
                                        empty($data['assigned_credit_line_amount']) ||
                                        !is_numeric($data['assigned_credit_line_amount']) ||
                                        $data['assigned_credit_line_amount'] < 0 ||
                                        $data['assigned_credit_line_amount'] > 1000000
                                    ) {
                                        return false;
                                    }
                                    return true;
                                })

                                ->action(function (Component $livewire, $action) use ($record) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];

                                    if (empty($data['assigned_credit_line_amount'])) {
                                        Notification::make()
                                            ->title('Credit Amount is required.')
                                            ->danger()
                                            ->send();
                                        return;
                                    }

                                    $assignedAmount = (float) $data['assigned_credit_line_amount'];
                                    // $supplierId = Auth::id();
                                    $supplierId = getUser(Auth::user())->id;
                                    $facilityId = $record->user_id;

                                    $lastClinicCredit = ClinicCreditHistory::where([
                                        'facility_id' => $facilityId,
                                        'supplier_id' => $supplierId,
                                    ])->orderBy('id', 'desc')->first();
                                    $oldValues = [];
                                    if (empty($lastClinicCredit)) {
                                        $clinicCredit = new ClinicCreditHistory();
                                        $clinicCredit->facility_id = $facilityId;
                                        $clinicCredit->supplier_id = $supplierId;
                                        $clinicCredit->credit_amount = $assignedAmount;
                                        $clinicCredit->debit_amount = 0;
                                        $clinicCredit->edit_credit = 0;
                                        $clinicCredit->order_credit_used = 0;
                                        $clinicCredit->total_credit_amount = $assignedAmount;
                                        $clinicCredit->remaining_amount = $assignedAmount;
                                        $clinicCredit->reference_id = $supplierId;
                                        $clinicCredit->reference_value = 'user';
                                        $clinicCredit->action = 'PS Assign Credit';
                                        $clinicCredit->save();
                                    } else {
                                        $remainingAmount = 0;
                                        $oldValues = [
                                            // 'credit_amount' => $lastClinicCredit->credit_amount,
                                            'total_credit_amount' => $lastClinicCredit->total_credit_amount,
                                            'remaining_amount' => $lastClinicCredit->remaining_amount,
                                        ];

                                        if ($lastClinicCredit->remaining_amount == 0) {
                                            $remainingAmount = $assignedAmount + $lastClinicCredit->total_credit_amount;
                                            $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                        } else {

                                            // if ($assignedAmount < $lastClinicCredit->order_credit_used) {
                                            //     $remainingAmount = 0;
                                            // } else {
                                            $remainingAmount = $assignedAmount + $lastClinicCredit->remaining_amount;
                                            // }
                                            // dd($lastClinicCredit->order_credit_used);
                                        }

                                        $clinicCredit = new ClinicCreditHistory();
                                        $clinicCredit->facility_id = $facilityId;
                                        $clinicCredit->supplier_id = $supplierId;
                                        $clinicCredit->credit_amount = $assignedAmount;
                                        $clinicCredit->debit_amount = 0;
                                        $clinicCredit->edit_credit = 0;
                                        $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used;
                                        $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount + $assignedAmount;
                                        $clinicCredit->remaining_amount = $remainingAmount;
                                        $clinicCredit->reference_id = $supplierId;
                                        $clinicCredit->reference_value = 'user';
                                        $clinicCredit->action = 'PS Assign Credit';
                                        $clinicCredit->save();

                                        // Activitylog start
                                        $newValues = [
                                            // 'credit_amount' => $clinicCredit->credit_amount,
                                            'total_credit_amount' => $clinicCredit->total_credit_amount,
                                            'remaining_amount' => $clinicCredit->remaining_amount,
                                        ];
                                        //Activitylog end

                                        $clinicCredit->save();
                                        activity()
                                            ->causedBy(Auth::user())
                                            ->useLog('clinic_credit_assigned')
                                            ->performedOn($clinicCredit)
                                            ->withProperties([
                                                'old' => $oldValues,
                                                'attributes' => $newValues,
                                            ])
                                            ->log("{$record->clinic_name} Credit has been assigned successfully.");
                                    }




                                    $emailContentData = [
                                        "USER" => $record->clinic_name,
                                        "SUPPLIER" => pcCompanyName(getUser(Auth::user())->pcDetails),
                                        "ASSIGNCREDIT" => $assignedAmount,
                                        "TOTALCREDIT" => $clinicCredit->total_credit_amount,
                                    ];
                                    // dd($record->user->email);
                                    sendMailNotification($record->user, "ASSIGN_CREDIT", $emailContentData);

                                    Notification::make()
                                        ->title('Credit assigned successfully.')
                                        ->success()
                                        ->send();

                                    $action->cancel();
                                    $action->cancelParentActions();
                                }),
                        ];
                    }),

                Action::make('credit-line-edit')
                    ->icon('iconoir-wallet')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->visible(function ($record) {
                        /** @var \App\Models\User $user */
                        $user = getUser(Auth::user());
                        $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();

                        $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($credit_amount);
                        // $credit_amount = ClinicCredit::where('supplier_id', auth()->id())->where('facility_id', $record->user_id)->value('remaining_balance');
                        $credit_amount = ClinicCreditHistory::where([
                            'facility_id' => $record->user_id,
                            'supplier_id' => $user->id,
                        ])->orderBy('id', 'desc')->value('remaining_amount');
                        return $approved && ($user->hasRole('Super Admin') || (($user->hasRole('Pharmaceutical Company') && auth()->user()->parent_id == null) ||  (auth()->user()->parent_id != null &&  auth()->user()->can('credit-line-orders_edit assign credit')))) && $credit_amount > 0;
                    })
                    ->label(fn(ClinicDetail $record): string => "Edit Assign Credit to {$record->clinic_name}")
                    ->tooltip(fn(ClinicDetail $record): string => "Edit Assign Credit to {$record->clinic_name}")
                    ->iconButton()
                    ->form([
                        TextInput::make('assigned_credit_line_amount')
                            ->rules(['required', 'numeric'])
                            ->placeholder('Enter credit amount')
                            ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                            ->minValue(0)
                            ->maxValue(1000000)
                            ->validationMessages([
                                'required' => 'The Credit Amount is required.',
                                'max' => 'The Credit Amount must not exceed 1,000,000.',
                            ])
                            ->extraAttributes([
                                'inputmode' => 'numeric',
                                'pattern' => '[0-9]*',
                                'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                            ])
                            ->formatStateUsing(function ($record) {

                                $credit_amount = ClinicCreditHistory::where([
                                    'facility_id' => $record->user_id,
                                    'supplier_id' => getUser(Auth::user())->id,
                                ])->orderBy('id', 'desc')->value('total_credit_amount');


                                // $credit_amount = ClinicCredit::where('supplier_id', auth()->id())->where('facility_id', $record->user_id)->pluck('total_credit_amount')->first();
                                return $credit_amount;
                            }),
                    ])->modalSubmitActionLabel('Save')
                    // ->action(function ($record, Get $get, array $data) {
                    //     $record->update(['assigned_credit_line_amount' => $data['assigned_credit_line_amount'], 'assigned_credit_terms' => $data['assigned_credit_terms']]);
                    // }),
                    ->action(function ($record, Get $get, array $data) {
                        // $user = getUser(Auth::user());
                        // // Create a new credit line
                        // $lastClinicCredit = ClinicCreditHistory::where([
                        //     'facility_id' => $record->user_id,
                        //     'supplier_id' => Auth::id(),
                        // ])->orderBy('id', 'desc')->first();

                        // //Activitylog start
                        // $oldValues = $lastClinicCredit->exists ? [
                        //     'total_credit_amount' => $lastClinicCredit->total_credit_amount,
                        // ] : [];
                        // //Activitylog end

                        // $remainingAmount = 0;
                        // // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                        // if ($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used) {
                        //     $remainingAmount = 0;
                        // } else if ($data['assigned_credit_line_amount'] > $lastClinicCredit->order_credit_used) {
                        //     $remainingAmount = $data['assigned_credit_line_amount'] - $lastClinicCredit->order_credit_used;
                        // }

                        // // dd($remainingAmount);
                        // $clinicCredit = new ClinicCreditHistory();
                        // $clinicCredit->facility_id = $record->user_id;
                        // $clinicCredit->supplier_id = Auth::id();
                        // $clinicCredit->credit_amount = 0;
                        // $clinicCredit->debit_amount = 0;
                        // $clinicCredit->edit_credit = $data['assigned_credit_line_amount'];
                        // $clinicCredit->order_credit_used = $lastClinicCredit?->order_credit_used;
                        // $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                        // $clinicCredit->remaining_amount = $remainingAmount;
                        // $clinicCredit->reference_id = Auth::id();
                        // $clinicCredit->reference_value = 'user';
                        // $clinicCredit->action = 'PS Edit Credit';
                        // $clinicCredit->save();

                        // //Activitylog start
                        // $newValues = [
                        //     'total_credit_amount' => $data['assigned_credit_line_amount'],
                        // ];

                        // activity()
                        // ->causedBy(Auth::user())
                        // ->useLog('clinic_credit_update')
                        // ->performedOn($clinicCredit)
                        // ->withProperties([
                        //     'old' => $oldValues,
                        //     'attributes' => $newValues,
                        // ])
                        // ->log("{$record->clinic_name} Credit has been updated successfully.");
                        //Activitylog end

                        // Mail::to($record->user->email)->send(new AssignCreditClinicMail($record->order));

                        // // Optionally update the ClinicDetail record with the assigned credit amount
                        // $record->update([
                        //     'assigned_credit_line_amount' => $data['assigned_credit_line_amount'],
                        //     // 'assigned_credit_terms' => $data['assigned_credit_terms'],
                        // ]);
                    })->modalSubmitAction(false)
                    ->extraModalFooterActions(function (ClinicDetail $record): array {
                        return [
                            Action::make('save')
                                ->label('Save')
                                ->color('success')
                                ->before(function (Component $livewire) {
                                    // dd($livewire);
                                    $data = $livewire->mountedTableActionsData[0] ?? [];
                                    // dd(empty($data['assigned_credit_line_amount']));
                                    if (
                                        !is_numeric($data['assigned_credit_line_amount']) ||
                                        $data['assigned_credit_line_amount'] < 0
                                    ) {
                                        // dd('here');
                                        $error = ValidationException::withMessages([
                                            'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount is required.',
                                        ]);
                                        // dd($error);
                                        throw $error;
                                    } else if ($data['assigned_credit_line_amount'] > 1000000) {
                                        $error = ValidationException::withMessages([
                                            'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount field must not be greater than 1000000.',
                                        ]);
                                        // dd($error);
                                        throw $error;
                                    }

                                    return false;
                                })

                                ->requiresConfirmation(function (Component $livewire) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];

                                    if (
                                        !empty($data['assigned_credit_line_amount']) &&
                                        is_numeric($data['assigned_credit_line_amount']) &&
                                        $data['assigned_credit_line_amount'] >= 0 &&
                                        $data['assigned_credit_line_amount'] <= 1000000
                                    ) {
                                        return true;
                                    }

                                    return false;
                                })
                                ->cancelParentActions(function (Component $livewire, $action) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];
                                    if (

                                        !is_numeric($data['assigned_credit_line_amount']) ||
                                        $data['assigned_credit_line_amount'] < 0 ||
                                        $data['assigned_credit_line_amount'] > 1000000
                                    ) {
                                        return false;
                                    }
                                    return true;
                                })

                                ->action(function (Component $livewire, $action) use ($record) {
                                    $data = $livewire->mountedTableActionsData[0] ?? [];
                                    // dd($data);
                                    // if (empty($data['assigned_credit_line_amount'])) {
                                    //     Notification::make()
                                    //         ->title('Credit Amount is required.')
                                    //         ->danger()
                                    //         ->send();
                                    //     return;
                                    // }


                                    // Create a new credit line
                                    $lastClinicCredit = ClinicCreditHistory::where([
                                        'facility_id' => $record->user_id,
                                        'supplier_id' => getUser(Auth::user())->id,
                                    ])->orderBy('id', 'desc')->first();
                                    //Activitylog start
                                    $oldValues = [
                                        // 'credit_amount' => $lastClinicCredit->credit_amount,
                                        'total_credit_amount' => $lastClinicCredit->total_credit_amount,
                                        'remaining_amount' => $lastClinicCredit->remaining_amount,
                                    ];

                                    //Activitylog end

                                    $remainingAmount = 0;
                                    // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                                    if ($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used) {
                                        $remainingAmount = 0;
                                    } else if ($data['assigned_credit_line_amount'] > $lastClinicCredit->order_credit_used) {
                                        $remainingAmount = $data['assigned_credit_line_amount'] - $lastClinicCredit->order_credit_used;
                                    }

                                    // dd($remainingAmount);
                                    $clinicCredit = new ClinicCreditHistory();
                                    $clinicCredit->facility_id = $record->user_id;
                                    $clinicCredit->supplier_id = getUser(Auth::user())->id;
                                    $clinicCredit->credit_amount = 0;
                                    $clinicCredit->debit_amount = 0;
                                    $clinicCredit->edit_credit = $data['assigned_credit_line_amount'];
                                    $clinicCredit->order_credit_used = $lastClinicCredit?->order_credit_used;
                                    $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                                    $clinicCredit->remaining_amount = $remainingAmount;
                                    $clinicCredit->reference_id = Auth::id();
                                    $clinicCredit->reference_value = 'user';
                                    $clinicCredit->action = 'PS Edit Credit';
                                    $clinicCredit->save();

                                    //Activitylog start
                                    $newValues = [
                                        // 'credit_amount' => $clinicCredit->credit_amount,
                                        'total_credit_amount' => $clinicCredit->total_credit_amount,
                                        'remaining_amount' => $clinicCredit->remaining_amount,
                                    ];

                                    activity()
                                        ->causedBy(Auth::user())
                                        ->useLog('clinic_credit_update')
                                        ->performedOn($clinicCredit)
                                        ->withProperties([
                                            'old' => $oldValues,
                                            'attributes' => $newValues,
                                        ])
                                        ->log("{$record->clinic_name} Credit has been updated successfully.");


                                    $emailContentData = [
                                        "USER" => $record->clinic_name,
                                        "SUPPLIER" => pcCompanyName(getUser(Auth::user())->pcDetails),
                                        "ASSIGNCREDIT" => $data['assigned_credit_line_amount'],
                                        "TOTALCREDIT" => $clinicCredit->total_credit_amount,
                                    ];
                                    // dd($record->user->email);
                                    sendMailNotification($record->user, "UPDATE_CREDIT", $emailContentData);


                                    Notification::make()
                                        ->title('Credit assigned successfully.')
                                        ->success()
                                        ->send();

                                    $action->cancel();
                                    $action->cancelParentActions();
                                }),
                        ];
                    }),

                Action::make('associated-clinic')
                    ->icon('iconoir-plus')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->visible(function ($record) {
                        $user = getUser(Auth::user());
                        $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                        $credit =  (!empty($credit_amount)) ? false : true;
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        /** @var \App\Models\User $user */
                        $user = Auth::user();
                        return $credit && ($user->hasRole('Super Admin') || $isPharmaceuticalCompany || auth()->user()->can('facility_associate facility'));
                    })
                    ->label('Associated Facility')
                    ->tooltip('Associated Facility')
                    ->iconButton()
                    ->form([
                        TextInput::make('account_number')
                            ->label(new HtmlString('Account Number <span style="color:red">*</span>'))
                            ->validationMessages([
                                'required' => 'The Account Number field is required.',
                                'regex' => 'The Account Number must be a alphabetical.',
                                'max' => 'The Account Number may not be greater than 20 characters.',
                                'min' => 'The Account Number field must be at least 1 characters.',
                            ])
                            ->placeholder('Enter account number')
                            ->validationAttribute('Account Number')
                            ->rules(['required', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])
                    ])->modalSubmitActionLabel('Save')
                    // ->action(function ($record, Get $get, array $data) {
                    //     $record->update(['assigned_credit_line_amount' => $data['assigned_credit_line_amount'], 'assigned_credit_terms' => $data['assigned_credit_terms']]);
                    // }),
                    ->action(function ($record, Get $get, array $data) {
                        $user = getUser(Auth::user());
                        $clinicAssociate = ClinicPharmaSupplier::firstOrNew([
                            'clinic_id' => $record->user->id,
                            'pc_id' => $user->id,
                        ]);

                        //Activitylog start
                        $oldValues = $clinicAssociate->exists ? [
                            'account_number' => $clinicAssociate->account_number,
                            'status' => $clinicAssociate->status,
                            'approved_by' => $clinicAssociate->approved_by ? \App\Models\User::find($clinicAssociate->approved_by)?->name : null,
                        ] : [];
                        //Activitylog end
                        $user = getUser(Auth::user());
                        $clinicAssociate->account_number = $data['account_number'];
                        $clinicAssociate->approved_by = $user->id;
                        $clinicAssociate->status = 'approved';
                        $clinicAssociate->is_open_account = true;

                        //Activitylog start
                        $newValues = [
                            'account_number' => $clinicAssociate->account_number,
                            'status' => $clinicAssociate->status,
                            'approved_by' => $clinicAssociate->approved_by ? \App\Models\User::find($clinicAssociate->approved_by)?->name : null,
                        ];
                        //Activitylog end

                        $clinicAssociate->save();

                        //Activitylog start
                        activity()
                            ->causedBy(Auth::user())
                            ->useLog('clinic_association')
                            ->performedOn($clinicAssociate)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("{$record->clinic_name} has been associated with account number {$data['account_number']}");
                        //Activitylog end

                        Notification::make()
                            ->body('The clinic associated successfully.')
                            ->success()
                            ->send();
                    }),

                Action::make('view')
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->tooltip('View')
                    ->color('gray')
                    ->visible(function () {
                        /** @var \App\Models\User $user */
                        $user = Auth::user();
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || auth()->user()->can('credit-line-orders_view');
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->url(fn($record) => route(ViewClinicDetail::getRouteName(), ['record' => $record->user_id])),
            ])
            ->actionsColumnLabel('Action')
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClinicDetails::route('/'),
            'view' => Pages\ViewClinicDetail::route('/{record}/view'),
            'show' => Pages\ShowClinicDetail::route('/show/{user_id}'),
        ];
    }
}
