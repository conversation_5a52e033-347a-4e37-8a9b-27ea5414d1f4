<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\FacilityRequestResource\Pages;
use App\Filament\Pc\Resources\FacilityRequestResource\RelationManagers;
use App\Models\ClinicDetail;
use App\Models\ClinicPharmaSupplier;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;

class FacilityRequestResource extends Resource
{
    protected static ?string $model = ClinicPharmaSupplier::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    // protected static ?string $navigationGroup = 'Requests';
    // protected static ?string $navigationLabel = 'Associated Facility';
    protected static bool $shouldRegisterNavigation = false;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return auth()->user()->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facility_view')
            ||  $user->can('facility_open account request') || $user->can('facility_assign credit')
            || $user->can('facility_edit assign credit') || $user->can('facility_approve facility')
            || $user->can('facility_reject facility') || $user->can('facility_add facility');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            // ->query(ClinicPharmaSupplier::where('is_open_account', false)->where('status', 'pending')->where('pc_id', auth()->user()->id))
            ->emptyStateHeading('No Facilities Request records found')
            ->columns([
                TextColumn::make('clinicDetail.name')->searchable()->toggleable()
                    ->label('Facilities')
                    ->getStateUsing(function ($record) {
                        return $record->clinicDetail->name ?? 'N/A';
                    }),
                TextColumn::make('account_number')->searchable()->toggleable()
                    ->label('Account Number')->getStateUsing(function ($record) {
                        return $record->account_number;
                    }),
                TextColumn::make('approved_by')->searchable()->toggleable()
                    ->label('Verified By')
                    ->getStateUsing(function ($record) {
                        return $record->approvedBy->business_name ?? 'Admin';
                    })
                    ->hidden(fn ($livewire) => !$livewire->isVerified),
                TextColumn::make('created_at')
                    ->toggleable()
                    ->label('Requested Date')
                    ->formatStateUsing(function ($state): string {
                        if (empty($state)) {
                            return '-';
                        }
                        $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                        $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                        return $convertedDate->format('M d, Y | H:i');
                    })
                    ->sortable()
                    ->searchable(),
                    TextColumn::make('rejected_by')->toggleable()
                    ->label('Rejected By')
                    ->getStateUsing(function ($record) {
                        $rejectedByUser = User::find($record->reject_by);
                        return $rejectedByUser->name ?? 'Admin';
                    })
                    ->visible(fn ($livewire) => $livewire->isRejected),
                TextColumn::make('reject_reason')->searchable()->toggleable()
                    ->label('Reject Reason')
                    ->getStateUsing(function ($record) {
                        return $record->reject_reason ?? '-';
                    })
                    ->formatStateUsing(function ($state) {
                        if (strlen($state) <= 40) {
                            return $state;
                        }
                        return '<span title="'.e($state).'" style="cursor: default">'.e(substr($state, 0, 40)).'...</span>';
                    })
                    ->html()
                    ->visible(fn ($livewire) => $livewire->isRejected),
                TextColumn::make('status')
                    ->searchable()
                    ->visible(false)
                    ->label('Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'approved' => 'bi-patch-check-fill',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            'approved' => 'rgba(0, 70, 104, 1)',
                            default => 'secondary',
                        };
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light yellow for pending
                            'rejected' => ['style' => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;'], // light red for rejected
                            'approved' => ['style' => 'background-color:rgba(243, 251, 255, 1); border: 1px solid rgba(206, 237, 253, 1); border-radius: 6px; color:rgba(0, 70, 104, 1); padding: 4px 8px; width: fit-content;'], // light green for approved
                            default => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light gray for unknown
                        };
                    }),
                    // ->formatStateUsing(function ($state, $record) {
                    //     return $record ? ucwords(str_replace('_', ' ', $record->status == 'approved' ? 'accepted' : $record->status)) : 'Unknown';
                    // }),
            ])
            ->actionsColumnLabel('Action')
            ->filters([
                // SelectFilter::make('status')
                //     ->options([
                //         'pending' => 'Pending',
                //         'approved' => 'Accepted',
                //         'rejected' => 'Rejected',
                //     ])
                //     ->label('Status'),
                SelectFilter::make('clinic_id')
                    ->relationship('clinicDetail', 'name')
                    ->searchable()
                    ->options(ClinicPharmaSupplier::where('pc_id', auth()->user()->id)
                        ->get()
                        ->pluck('clinicDetail.name', 'clinic_id')
                        ->toArray())
                    ->label('Facility'),
                Filter::make('created_at')
                    ->label('Requested Date')
                    ->form([
                        DatePicker::make('created_from')->label('Requested From')->closeOnDateSelection()->reactive()->maxDate(now()),
                        DatePicker::make('created_until')->label('Requested Until')->closeOnDateSelection()->maxDate(now())->minDate(function ($get) {
                            return $get('created_from') ? Carbon::parse($get('created_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),


            ])
            ->actions([
                ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->tooltip('View')
                    ->extraAttributes([
                        'class' => 'border-2 rounded-lg text-gray-400 mr-1', // Added margin-right for spacing
                        'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'
                    ])
                    ->url(function ($record) {
                        return ClinicDetailResource::getUrl('view', ['record' => $record->clinic_id]);
                    }),
                Action::make('approve')
                    ->label('')
                    ->tooltip('Approve')
                    ->icon('heroicon-m-check')->size('sm')->iconButton()
                    ->color('success')
                    ->extraAttributes([
                        'class' => 'border-2 rounded-lg', // Added margin-right for spacing
                    ])
                    ->action(function ($record) {

                        $oldValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->user()->id,
                            'reject_by' => null
                        ]);

                        $newValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];

                        // Log activity for approving facility

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('facility_approval')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("The facility's account number of " . ($record->clinicDetail->name ?? 'N/A') . " has been verified");


                        Notification::make()
                            ->title('Facility Approved')
                            ->success()
                            ->send();
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to approve this facility?');
                        return $action;
                    })->visible(function ($record) {
                        return $record->status !== 'approved';
                    }),

                Action::make('reject')
                    ->label('')
                    ->tooltip('Reject')
                    ->icon('heroicon-m-x-mark')->size('sm')->iconButton()
                    ->color('danger')
                    ->extraAttributes([
                        'class' => 'border-2 rounded-lg',
                        'style' => 'margin-left: inherit;'
                    ])
                    ->modalSubmitAction(
                        fn ($action) =>
                        $action->label('Save')->color('danger')
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('')
                            ->validationMessages([
                                'required' => "The reason field is required.",
                            ])
                            ->rules(['required'])
                    ])
                    ->action(function ($data, $record) {

                        $oldValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];

                        $record->update([
                            'status' => 'rejected',
                            'reject_by' => auth()->user()->id,
                            'approved_by' => null,
                            'reject_reason' => $data['reason']

                        ]);

                        $newValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                            'reject_reason' => $record->reject_reason,
                        ];

                        // Log activity for rejecting

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('facility_rejection')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("The facility's account number of " . ($record->clinicDetail->name ?? 'N/A') . " has been rejected");

                        Notification::make()
                            ->title('Facility Rejected')
                            ->success()
                            ->send();
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to reject this facility?');
                        return $action;
                    })->visible(function ($record) {
                        return $record->status !== 'rejected';
                    }),
            ])
            // ->bulkActions([
            //     Tables\Actions\BulkAction::make('approve')
            //         ->label('Accept')
            //         ->icon('heroicon-o-check-circle')
            //         ->color('success')
            //         ->action(function ($records) {
            //             $records->each(function ($record) {
            //                 $oldValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                 ];
            //                 $record->update(['status' => 'approved', 'approved_by' => auth()->user()->id, 'reject_by' => null]);

            //                 $newValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by,
            //                     'reject_by' => $record->reject_by,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                 ];

            //                 // Log activity for approving facility

            //                 activity()
            //                     ->causedBy(auth()->user())
            //                     ->useLog('facility_approval')
            //                     ->performedOn($record)
            //                     ->withProperties([
            //                         'old' => $oldValues,
            //                         'attributes' => $newValues,
            //                     ])
            //                     ->log("The facility's account number of " . ($record->clinicDetail->name ?? 'N/A') . " have been verified");
            //             });
            //             Notification::make()
            //                 // ->title('Request Approved')
            //                 ->title('The selected request have been approved successfully.')
            //                 ->success()
            //                 ->send();
            //         })->after(function () {
            //             redirect(static::getUrl('index'));
            //         })
            //         ->requiresConfirmation(),
            //     Tables\Actions\BulkAction::make('reject')
            //         ->label('Reject')
            //         ->icon('heroicon-o-x-circle')
            //         ->color('danger')
            //         ->action(function ($records) {
            //             $records->each(function ($record) {
            //                 $oldValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                 ];
            //                 $record->update(['status' => 'rejected', 'reject_by' => auth()->user()->id, 'approved_by' => null]);

            //                 $newValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                     'reject_reason' => $record->reject_reason,
            //                 ];

            //                 // Log activity for rejecting facility

            //                 activity()
            //                     ->causedBy(auth()->user())
            //                     ->useLog('facility_rejection')
            //                     ->performedOn($record)
            //                     ->withProperties([
            //                         'old' => $oldValues,
            //                         'attributes' => $newValues,
            //                     ])
            //                     ->log("The facility's account number of " . ($record->clinicDetail->name ?? 'N/A') . " have been rejected");
            //             });
            //             Notification::make()
            //                 // ->title('Request Rejected')
            //                 ->title('The selected request have been rejected successfully.')
            //                 ->success()
            //                 ->send();
            //         })->after(function () {
            //             redirect(static::getUrl('index'));
            //         })
            //         ->requiresConfirmation(),
            // ])
        ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacilityRequests::route('/'),
            'create' => Pages\CreateFacilityRequest::route('/create'),
            // 'edit' => Pages\EditFacilityRequest::route('/{record}/edit'),
        ];
    }
}
