<?php

namespace App\Filament\Pc\Resources;

use Carbon\Carbon;
use App\Models\User;
use NumberFormatter;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use App\Models\ProductBatch;
use Illuminate\Http\Request;
use Illuminate\Support\Number;
use App\Mail\PcApprovedProduct;
use App\Mail\PcRejectedProduct;
use App\Models\ProductRelation;
use Illuminate\Validation\Rule;
use App\Actions\EditPriceAction;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Livewire\Attributes\Session;
use App\Models\ProductCommission;
use Awcodes\TableRepeater\Header;
use Filament\Support\Colors\Color;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use App\Livewire\ProductTableForPc;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Log;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Cache;
use App\Service\TierValidationService;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Section;
use App\Service\PriceManagementService;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\Tabs;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\CategorySubCategoryFilter;
use Filament\Forms\Components\Placeholder;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\ImageEntry;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Number as SupportNumber;
use App\Infolists\Components\DragDropImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Awcodes\TableRepeater\Components\TableRepeater;
use Filament\Infolists\Components\Grid as InfoGrid;
use App\Filament\Pc\Resources\ProductResource\Pages;
use Filament\Infolists\Components\Group as InfoGroup;
use Google\Service\DatabaseMigrationService\TableEntity;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Forms\Components\Livewire as ComponentsLivewire;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Illuminate\Contracts\Database\Query\Builder as QueryBuilder;
use App\Filament\Admin\Resources\ProductResource\Pages\ListProducts;
use App\Filament\Pc\Resources\ProductResource\Widgets\NormalProducts;
use App\Filament\Pc\Resources\ProductResource\Widgets\BatchWiseProducts;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'bi-box-seam';

    protected static bool $canCreateAnother = false;

    protected static ?string $navigationLabel = 'My Products';

    public $isAllProducts = false;

    public static ?string $search = null;

    // public static $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();

    public static function canView($record): bool
    {
        return Auth::user()->hasRole('Pharmaceutical Company') || Auth::user()->hasRole('Super Admin') || Auth::user()->can('products_view');
    }

    public $price;

    protected function applySearchToTableQuery(Builder $query): Builder
    {
        $this->applyColumnSearchesToTableQuery($query);

        if (filled($search = $this->getTableSearch())) {
            $query->where(function (Builder $query) use ($search) {
                $query->where('products.name', 'ILIKE', "%$search%")
                    ->orWhereHas('productData', function (Builder $query1) use ($search) {
                        $query1->where('sku', 'ILIKE', "%$search%");
                    })
                    ->orWhereHas('unit', function ($query2) use ($search) {
                        $query2->where('name', 'ILIKE', "%$search%");
                    });

                // Include is_batch_wise_stock search manually
                if (str_contains(strtolower($search), 'batch')) {
                    $query->orWhereHas('relationStocks', function ($query3) {
                        $query3->where('product_relation_stocks.is_batch_wise_stock', true);
                    });
                }

                if (str_contains(strtolower($search), 'product')) {
                    $query->orWhereHas('relationStocks', function ($query4) {
                        $query4->where('product_relation_stocks.is_batch_wise_stock', false);
                    });
                }
            });
        }

        return $query;
    }

    public static function getPriceData($key)
    {
        return ProductRelationPrice::where('product_relation_id', $key)->first();
    }

    public static function getPcData($record, $userId)
    {
        return $record->productDataForPc($userId);
    }

    public static function getStockData($key)
    {
        return ProductRelationStock::where('product_relation_id', $key)->first();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    Select::make('product_id')
                        ->label(new HtmlString('Product Name <span class="text-red-500" style="color:red;">*</span>'))
                        ->allowHtml(true)
                        ->live(onBlur: true)
                        ->native(false)
                        ->validationAttribute('product')
                        ->options(function () {
                            $products = Product::notAdded(getUser(Auth::id()))->where('status', 'approved')->withoutTrashed()->take(9)->pluck('name', 'id');
                            $createNew = collect(['create_new' => '<span class="text-blue-950" style="color: rgb(' . Color::Blue[950] . ')">+ Create New</span>']);
                            return $products->union($createNew)->toArray();
                        })
                        ->afterStateUpdated(function (Get $get) {
                            if ($get('product_id') == 'create_new') {
                                return redirect()->to(ProductResource::getUrl('create-new'));
                            }
                        })
                        ->rules(['required'])
                        ->getSearchResultsUsing(function (string $search): array {
                            session()->put('search_term_' . Auth::id(), $search);
                            $products = Product::where('name', 'ILIKE', "%$search%")
                                // "similarity(name, ?) > 0.2", [$search]) // Adjust threshold (0.3 - 1.0)
                                ->notAdded(getUser(Auth::id()))
                                // ->orderByRaw("similarity(name, ?) DESC", [$search])
                                ->where('status', 'approved')
                                ->withoutTrashed()
                                ->take(9)
                                ->pluck('name', 'id')
                                ->toArray();
                            if (count($products) === 0) {
                                Notification::make()
                                    ->title('No products found, please create new.')
                                    ->warning()
                                    ->send();
                            }
                            $createNew = ['create_new' => '<span class="text-blue-950" style="color: rgb(' . Color::Blue[950] . ')">+ Create New</span>'];
                            return  $products + $createNew;
                        })
                        ->optionsLimit(10)
                        ->searchable()
                        ->preload()
                ]),
                Actions::make([
                    \Filament\Forms\Components\Actions\Action::make('add')
                        ->action('create'),
                    \Filament\Forms\Components\Actions\Action::make('cancel')
                        ->label('Cancel')
                        ->color('gray')
                        ->outlined()
                        ->action(function (Get $get, Set $set) {
                            return redirect()->to(ProductResource::getUrl('index'));
                        })
                ]),
                ComponentsLivewire::make(ProductTableForPc::class)
                    ->key(1)
                    ->columnSpanFull()

            ]);
    }

    public static function table(Table $table): Table
    {
        $authId = getUser(Auth::user())->id;
        $pcCommission = PcDetail::where('user_id', $authId)->first();
        $finalCommissionType = $pcCommission?->commission_type ?: null;
        $finalCommission = $pcCommission?->commission_percentage ?: 0;

        return $table
            ->actionsColumnLabel('Action')
            ->defaultSort('id', 'desc')
            ->columns([

                TextColumn::make('id')
                    ->toggleable()
                    ->label('Product ID')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->toggleable()
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('generic.name')
                    ->toggleable()
                    ->label('Generic Name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('category.name')
                    ->toggleable()
                    ->label('Category')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('subcategory.name')
                    ->toggleable()
                    ->label('Subcategory')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('productData.updated_at')
                    ->toggleable()
                    ->label('Quantity')
                    ->formatStateUsing(function ($state, $record) {
                        // Cache the product data to avoid duplicate queries
                        if (!isset($record->_cached_product_data)) {
                            $user = getUser(Auth::user());
                            $userId = $user->id;
                            $productData = $record->productDataForPc($userId);
                            $productRelationData = ProductRelationStock::where('product_relation_id', $productData?->id)->first();

                            $record->_cached_product_data = [
                                'user' => $user,
                                'userId' => $userId,
                                'productData' => $productData,
                                'productRelationData' => $productRelationData
                            ];
                        }

                        $cachedData = $record->_cached_product_data;
                        $productRelationData = $cachedData['productRelationData'];
                        // dd($productRelationData);
                        $userId = $cachedData['userId'];


                        if ($productRelationData?->is_batch_wise_stock == true) {

                            $stock = ProductBatch::where('products_relation_id', $productRelationData?->product_relation_id)->sum('available_stock');
                        } else {
                            $stock = $productRelationData?->stock ?? $productRelationData?->total_stock ?? null;
                        }
                        return $stock ?? '-';
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        $userId = $user->id;

                        return $query->orderByRaw(
                            "
                            (SELECT CASE 
                                WHEN prs.is_batch_wise_stock = true THEN 
                                    (SELECT COALESCE(SUM(pb.available_stock), 0) 
                                     FROM products_batch pb 
                                     WHERE pb.product_id = products.id AND pb.user_id = ?)
                                ELSE 
                                    COALESCE(prs.stock, prs.total_stock, 0)
                             END
                             FROM products_relation pr 
                             JOIN product_relation_stocks prs ON pr.id = prs.product_relation_id 
                             WHERE pr.product_id = products.id 
                               AND pr.user_id = ? 
                               AND pr.is_rejected = false 
                               AND pr.deleted_at IS NULL 
                             LIMIT 1) {$direction}",
                            [$userId, $userId]
                        );
                    }),
                TextColumn::make('productData.created_at')
                    ->label('In Stock')
                    ->formatStateUsing(function ($state, $record) {

                        if ($state == true) {
                            // Use cached data if available
                            if (!isset($record->_cached_product_data)) {
                                $user = getUser(Auth::user());
                                $userId = $user->id;
                                if (!empty($user->parent_id)) {
                                    $userId = $user->parent_id;
                                }
                                $productData = $record->productDataForPc($userId);
                                $productRelationData = ProductRelationStock::where('product_relation_id', $productData?->id)->first();

                                $record->_cached_product_data = [
                                    'user' => $user,
                                    'userId' => $userId,
                                    'productData' => $productData,
                                    'productRelationData' => $productRelationData
                                ];
                            }

                            $cachedData = $record->_cached_product_data;
                            $productRelationData = $cachedData['productRelationData'];
                            $userId = $cachedData['userId'];

                            if ($productRelationData?->is_batch_wise_stock == true) {
                                $stock = $record->batches->where('product_id', $record->id)->where('user_id', $userId)->sum('available_stock');

                                return $stock > $productRelationData?->low_stock ? 'Yes' : 'No';
                            } else {
                                $stock = $productRelationData?->total_stock > $productRelationData?->low_stock ? 'Yes' : 'No';
                                return $stock;
                            }
                        }
                    })
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        $userId = $user->id;

                        return $query->orderByRaw(
                            "
                            (SELECT CASE 
                                WHEN prs.is_batch_wise_stock = true THEN 
                                    CASE WHEN (SELECT COALESCE(SUM(pb.available_stock), 0) 
                                              FROM products_batch pb 
                                              WHERE pb.product_id = products.id AND pb.user_id = ?) > COALESCE(prs.low_stock, 0)
                                         THEN 0 ELSE 1 END
                                WHEN prs.is_batch_wise_stock = false THEN 
                                    CASE WHEN COALESCE(prs.total_stock, 0) > COALESCE(prs.low_stock, 0) THEN 0 ELSE 1 END
                                ELSE 1
                             END
                             FROM products_relation pr 
                             JOIN product_relation_stocks prs ON pr.id = prs.product_relation_id 
                             WHERE pr.product_id = products.id 
                               AND pr.user_id = ? 
                               AND pr.is_rejected = false 
                               AND pr.deleted_at IS NULL 
                             LIMIT 1) {$direction}",
                            [$userId, $userId]
                        );
                    }),
                TextColumn::make('productData')
                    ->label('Expiry Date')
                    ->formatStateUsing(function ($state, $record) {
                        // Use cached data if available
                        if (!isset($record->_cached_product_data)) {
                            $user = getUser(Auth::user());
                            $userId = $user->id;
                            $productData = $record->productDataForPc($userId);
                            $productRelationData = ProductRelationStock::where('product_relation_id', $productData?->id)->first();

                            $record->_cached_product_data = [
                                'user' => $user,
                                'userId' => $userId,
                                'productData' => $productData,
                                'productRelationData' => $productRelationData
                            ];
                        }

                        $cachedData = $record->_cached_product_data;
                        $productRelationData = $cachedData['productRelationData'];
                        $userId = $cachedData['userId'];

                        if (!$productRelationData) {
                            return '-';
                        }
                        $format = getDateTimeFormatForPc(Auth::user());
                        if ($productRelationData?->is_batch_wise_stock == true) {
                            $latestExpiry = $record->batches->where('product_id', $record->id)->where('user_id', $userId)->max('expiry_date');

                            return getFormatedDate($latestExpiry, $format);
                        } elseif ($productRelationData?->is_batch_wise_stock == false) {
                            return getFormatedDate($productRelationData?->expiry_date, $format);
                        } else {
                            return '-';
                        }
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        $user = getUser(Auth::user());
                        $userId = $user->id;

                        return $query->orderByRaw(
                            "
                            (SELECT CASE 
                                WHEN prs.is_batch_wise_stock = true THEN 
                                    (SELECT MAX(pb.expiry_date) 
                                     FROM products_batch pb 
                                     WHERE pb.product_id = products.id AND pb.user_id = ?)
                                ELSE 
                                    prs.expiry_date
                             END
                             FROM products_relation pr 
                             JOIN product_relation_stocks prs ON pr.id = prs.product_relation_id 
                             WHERE pr.product_id = products.id 
                               AND pr.user_id = ? 
                               AND pr.is_rejected = false 
                               AND pr.deleted_at IS NULL 
                             LIMIT 1) {$direction}",
                            [$userId, $userId]
                        );
                    }),
                TextColumn::make('productData.rejected_reason')
                    ->formatStateUsing(function ($state) {
                        return Str::limit($state, 10) ?? '-';
                    })
                    ->label('Rejected Reason')
                    ->visible(function ($record, $livewire) {
                        return $livewire->isRejectedProducts;
                    })
                    ->alignCenter()
                    ->tooltip(function ($state, $record) {
                        return $record->productDataForPc($record->add_request_by)->rejected_reason ?? '-';
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->formatStateUsing(function ($state) {
                        $format = getDateTimeFormatForPc(Auth::user());
                        $timezone = Auth::user()->timezone ?? 'UTC';
                        return Carbon::parse($state)->timezone($timezone ?? 'UTC')->format($format);
                    })
                    ->sortable(),
            ])
            ->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->filtersFormColumns(3)
            ->filters([
                // Main Filters (3 columns)
                SelectFilter::make('stock')
                    ->label('Stock Status')
                    ->columnSpan(1)
                    ->options([
                        '' => 'All Stock Types',
                        'low_stock' => 'Low Stock',
                        'expired' => 'Expired',
                        'out_of_stock' => 'Out Of Stock',
                        'expiring_soon' => 'Expiring Soon',
                        'exclude_expired' => 'Exclude Expired',
                        'exclude_out_of_stock' => 'Exclude Out of Stock',
                    ])
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, array $data): Builder {
                        $user = Auth::user();
                        $userId = $user->parent_id ?? $user->id;

                        $value = $data['value'] ?? null;

                        if (!$value) {
                            // No filter applied, return original query
                            return $query;
                        }

                        return $query->whereHas('productData', function ($q) use ($userId, $value, $user) {
                            $q->where('user_id', $userId)
                                ->whereHas('productRelationStock', function ($q2) use ($value, $user) {
                                    $q2->where(function ($stockQuery) use ($value, $user) {
                                        if ($value === 'out_of_stock') {
                                            $stockQuery
                                                ->where(function ($subQuery) use ($user) {
                                                    $subQuery->where('is_batch_wise_stock', true)
                                                        ->whereRaw('(
                                            SELECT COALESCE(SUM(pb.available_stock), 0)
                                            FROM products_batch pb
                                            WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                                        ) <= 0');
                                                })
                                                ->orWhere(function ($subQuery) {
                                                    $subQuery->where('is_batch_wise_stock', false)
                                                        ->where('stock', '<=', 0);
                                                });
                                        } elseif ($value === 'low_stock') {
                                            $subQuerySum = '(
                                                SELECT COALESCE(SUM(pb.available_stock), 0)
                                                FROM products_batch pb
                                                WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                                            )';

                                            $stockQuery
                                                ->where(function ($subQuery) use ($subQuerySum) {
                                                    $subQuery->where('is_batch_wise_stock', true)
                                                        ->whereRaw("$subQuerySum < product_relation_stocks.low_stock")
                                                        ->whereRaw("$subQuerySum > 0");
                                                })
                                                ->orWhere(function ($subQuery) {
                                                    $subQuery->where('is_batch_wise_stock', false)
                                                        ->whereColumn('stock', '<', 'low_stock')
                                                        ->where('stock', '>', 0);
                                                });
                                        } elseif ($value === 'expired') {
                                            // $stockQuery->whereDate('expiry_date', '<', now());
                                            $stockQuery->whereNull('deleted_at')
                                                ->where('is_rejected', false)
                                                ->where(function ($q) {
                                                    $q->where(function ($subQuery) {
                                                        // For non-batch wise stock
                                                        $subQuery->where('is_batch_wise_stock', false)
                                                            ->where('expiry_date', '<', now());
                                                    })->orWhere(function ($subQuery) {
                                                        // For batch wise stock
                                                        $subQuery->where('is_batch_wise_stock', true)
                                                            ->whereDoesntHave('productsBatch', function ($batchQuery) {
                                                                $batchQuery->where('expiry_date', '>', now());
                                                            })
                                                            ->whereHas('productsBatch', function ($batchQuery) {
                                                                // Ensure at least one batch exists
                                                                $batchQuery->whereNotNull('expiry_date');
                                                            });
                                                    });
                                                });
                                        } elseif ($value === 'expiring_soon') {
                                            $from = now();
                                            $until = $user->pcDetails->expiry_soon ?? now()->addMonths(3)->startOfMonth();
                                            $stockQuery->where('is_batch_wise_stock', false)
                                                ->whereBetween('expiry_date', [$from, $until])
                                                ->orWhere(function ($q) use ($from, $until) {
                                                    $q->where('is_batch_wise_stock', true)
                                                        ->whereHas('productsBatch', function ($batchQuery) use ($from, $until) {
                                                            $batchQuery->whereBetween('expiry_date', [$from, $until]);
                                                        });
                                                });
                                        } elseif ($value === 'exclude_expired') {
                                            $stockQuery->where(function ($query) {
                                                $query->where(function ($q) {
                                                    $q->where('is_batch_wise_stock', false)
                                                        ->where('expiry_date', '>=', now()); // expiry on product_relation_stocks
                                                })->orWhere(function ($q) {
                                                    $q->where('is_batch_wise_stock', true)
                                                        ->whereHas('productsBatch', function ($batchQuery) {
                                                            $batchQuery->where('expiry_date', '>=', now()); // at least one unexpired batch
                                                        });
                                                });
                                            });
                                        } elseif ($value === 'exclude_out_of_stock') {
                                            $stockQuery->where(function ($query) {
                                                $query->where(function ($q) {
                                                    $q->where('is_batch_wise_stock', false)
                                                        ->where('stock', '>', 0);
                                                })->orWhere(function ($q) {
                                                    $q->where('is_batch_wise_stock', true)
                                                        ->whereHas('productsBatch', function ($batchQuery) {
                                                            $batchQuery->where('available_stock', '>', 0);
                                                        });
                                                });
                                            });
                                        }
                                    });
                                });
                        });
                    }),
                SelectFilter::make('generic_name_id')
                    ->label('Generic Name')
                    ->columnSpan(1)
                    ->relationship('generic', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('in_stock_status')
                    ->label('In Stock')
                    ->columnSpan(1)
                    ->options([
                        'yes' => 'Yes',
                        'no' => 'No',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $value = $data['value'] ?? null;
                        if (!$value) {
                            return $query;
                        }

                        $user = Auth::user();
                        $userId = $user->parent_id ?? $user->id;

                        return $query->whereHas('productData', function ($q) use ($userId, $value) {
                            $q->where('user_id', $userId)
                                ->whereHas('productRelationStock', function ($q2) use ($value) {
                                    $q2->where(function ($stockQuery) use ($value) {
                                        if ($value === 'yes') {
                                            $stockQuery->where(function ($subQuery) {
                                                $subQuery->where('is_batch_wise_stock', false)
                                                    ->whereColumn('total_stock', '>', 'low_stock');
                                            })->orWhere(function ($subQuery) {
                                                $subQuery->where('is_batch_wise_stock', true)
                                                    ->whereRaw('(SELECT COALESCE(SUM(pb.available_stock), 0) FROM products_batch pb WHERE pb.products_relation_id = product_relation_stocks.product_relation_id) > product_relation_stocks.low_stock');
                                            });
                                        } elseif ($value === 'no') {
                                            $stockQuery->where(function ($subQuery) {
                                                $subQuery->where('is_batch_wise_stock', false)
                                                    ->whereColumn('total_stock', '<=', 'low_stock');
                                            })->orWhere(function ($subQuery) {
                                                $subQuery->where('is_batch_wise_stock', true)
                                                    ->whereRaw('(SELECT COALESCE(SUM(pb.available_stock), 0) FROM products_batch pb WHERE pb.products_relation_id = product_relation_stocks.product_relation_id) <= product_relation_stocks.low_stock');
                                            });
                                        }
                                    });
                                });
                        });
                    }),
                Filter::make('pending approval')
                    ->label('Pending Approval')
                    ->toggle()
                    ->columnSpan(1)
                    ->modifyQueryUsing(function ($query) {
                        $products = $query->pendingApprovals();
                        // ->whereHas('productData', function ($q) {
                        //     $q->where('admin_approval', false);
                        // });
                        return $products;
                    }),

                CategorySubCategoryFilter::make('category_subcategory')
                    ->columnSpan(2),

                // Date Filters Section
                Filter::make('created_at')
                    ->label('')
                    ->columnSpan(3)
                    ->form([
                        \Filament\Forms\Components\Section::make('📅 Created Date Range')
                            ->schema([
                                \Filament\Forms\Components\Grid::make(2)
                                    ->schema([
                                        DatePicker::make('created_from')
                                            ->label('From')
                                            ->placeholder('Start date')
                                            ->live()
                                            ->maxDate(today()),
                                        DatePicker::make('created_until')
                                            ->label('Until')
                                            ->placeholder('End date')
                                            ->minDate(fn($get) => $get('created_from'))
                                            ->maxDate(today()),
                                    ])
                            ])
                            ->compact()
                    ])
                    ->query(function (Builder $query, array $data): Builder {

                        return $query
                            ->when(
                                $data['created_from'],
                                function (Builder $query, $date): Builder {
                                    return  $query->whereHas('productData', function ($q) use ($date) {
                                        $q->where('requested_by', Auth::id())->whereDate('created_at', '>=', $date);
                                    });
                                }
                            )
                            ->when(
                                $data['created_until'],
                                function (Builder $query, $date): Builder {
                                    return $query->whereHas('productData', function ($q) use ($date) {
                                        $q->where('requested_by', Auth::id())->whereDate('created_at', '<=', $date);
                                    });
                                }
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),
                Filter::make('expiry_date')
                    ->label('')
                    ->columnSpan(3)
                    ->form([
                        \Filament\Forms\Components\Section::make('📅 Expiry Date Range')
                            ->schema([
                                \Filament\Forms\Components\Grid::make(2)
                                    ->schema([
                                        DatePicker::make('expiry_from')
                                            ->label('From')
                                            ->placeholder('Start date')
                                            ->live()
                                            ->maxDate(today()),
                                        DatePicker::make('expiry_until')
                                            ->label('Until')
                                            ->placeholder('End date')
                                            ->minDate(fn($get) => $get('expiry_from'))
                                            ->maxDate(today()->addYears(10)), // Reasonable max date for expiry
                                    ])
                            ])
                            ->compact()
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $user = Auth::user();
                        $userId = $user->parent_id ?? $user->id;

                        return $query
                            ->when(
                                $data['expiry_from'] || $data['expiry_until'],
                                function (Builder $query) use ($data, $userId) {
                                    return $query->whereHas('productData', function ($q) use ($userId, $data) {
                                        $q->where('user_id', $userId)
                                            ->whereHas('productRelationStock', function ($q2) use ($data) {
                                                $q2->where(function ($stockQuery) use ($data) {
                                                    // Handle batch-wise stock
                                                    $stockQuery->where(function ($batchQuery) use ($data) {
                                                        $batchQuery->where('is_batch_wise_stock', true)
                                                            ->whereHas('productsBatch', function ($batchSubQuery) use ($data) {
                                                                if ($data['expiry_from']) {
                                                                    $batchSubQuery->whereDate('expiry_date', '>=', $data['expiry_from']);
                                                                }
                                                                if ($data['expiry_until']) {
                                                                    $batchSubQuery->whereDate('expiry_date', '<=', $data['expiry_until']);
                                                                }
                                                            });
                                                    })
                                                        // Handle non-batch-wise stock
                                                        ->orWhere(function ($nonBatchQuery) use ($data) {
                                                            $nonBatchQuery->where('is_batch_wise_stock', false);
                                                            if ($data['expiry_from']) {
                                                                $nonBatchQuery->whereDate('expiry_date', '>=', $data['expiry_from']);
                                                            }
                                                            if ($data['expiry_until']) {
                                                                $nonBatchQuery->whereDate('expiry_date', '<=', $data['expiry_until']);
                                                            }
                                                        });
                                                });
                                            });
                                    });
                                }
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (!$data['expiry_from'] && !$data['expiry_until']) {
                            return null;
                        }

                        $from = $data['expiry_from'] ? Carbon::parse($data['expiry_from'])->toFormattedDateString() : null;
                        $until = $data['expiry_until'] ? Carbon::parse($data['expiry_until'])->toFormattedDateString() : null;

                        if ($from && $until) {
                            return "Expiry: {$from} to {$until}";
                        } elseif ($from) {
                            return "Expiry from: {$from}";
                        } elseif ($until) {
                            return "Expiry until: {$until}";
                        }

                        return null;
                    })
            ])
            // ->filtersLayout(FiltersLayout::Modal)

            ->actions([
                ActionGroup::make([
                    EditAction::make()->label('Edit')->icon('heroicon-o-pencil-square')->color('gray')
                        ->visible(function ($record) {
                            $rejected = $record->status == 'rejected';
                            $user = Auth::user();
                            return $rejected && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_update'));
                        })
                        ->form([
                            Textarea::make('reason')->required()
                        ]),
                    ViewAction::make('view')
                        ->label('View')
                        ->icon('heroicon-o-eye')
                        ->visible(function ($record) {
                            $user = Auth::user();
                            return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_view');
                        })
                        ->action(fn($record) => redirect()->to(ProductResource::getUrl('view', ['record' => $record->id]))),

                    Action::make('Edit Price')
                        ->visible(function ($record) {
                            $approved = $record->status == 'approved';
                            $user = Auth::user();
                            return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_update price'));
                        })
                        ->modalSubmitActionLabel('Save')
                        ->icon('heroicon-o-pencil-square')
                        ->form(function ($record) use ($finalCommission, $finalCommissionType) {
                            $user = Auth::user();
                            $productData = $record->productDataForPc($user->id);

                            $productRelationData = ProductRelationPrice::where('product_relation_id', $productData?->id)->first();
                            // $productCommission = ProductCommission::where('product_id', $record->id)->where('user_id', $record->owner_id)->first();
                            $productCommission = null;
                            return [
                                Section::make()
                                    ->schema([
                                        Radio::make('price_type')
                                            ->label('Price Type')
                                            ->extraAttributes(['class' => 'rajen'])
                                            ->inline()
                                            ->live()
                                            ->formatStateUsing(function ($record) use ($productData) {
                                                return $productData?->price_type ?? 'fixed';
                                            })
                                            ->default('fixed')
                                            ->options(fn($record) => self::getEditPriceOptions($record)),
                                    ]),
                                Placeholder::make('discription')
                                    ->hiddenLabel()
                                    ->content(function ($record) {
                                        $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                                        if (empty($relationData?->id)) {
                                            return "-";
                                        }

                                        $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                                        $packSize = $stockData?->wholesale_pack_size;

                                        if (empty($packSize)) {
                                            return "-";
                                        }

                                        $text = "Pricing ";

                                        $rawQty = $record->quantity_per_unit;
                                        $wholeSalePackSize = $packSize ?? 1;
                                        $containerId = $record->container_id;
                                        $dosageFoamId = $record->dosage_foams_id;
                                        $qty = $rawQty ?? 0;

                                        if (!empty($rawQty)) {
                                            $text .= "{$wholeSalePackSize} ";
                                        }

                                        if (!empty($rawQty) && !empty($wholeSalePackSize) && $wholeSalePackSize > 0) {
                                            $qty = $rawQty * (int) $wholeSalePackSize;
                                        }

                                        $containerName = $record->container?->name;
                                        if (!empty($containerName)) {
                                            $text .= $containerName . ' of ';
                                        }

                                        $foamName = $record->foam?->name;
                                        $foam = $foamName ? Str::plural($foamName) : null;

                                        if (!empty($qty) && !empty($foam)) {
                                            $text .= "( {$qty} {$foam} )";
                                        }

                                        return new HtmlString("<div class='px-5 py-2 font-bold border-2 rounded-lg text-md'>{$text}</div>");
                                    }),
                                // ->extraAttributes(['class' => '!px-0', 'style' => 'border: none !important; box-shadow: none !important;margin:0px !important; padding:0px !important;'])
                                // ->schema([
                                Section::make()
                                    ->extraAttributes(['style' => 'padding:0px !important;',])
                                    ->visible(function (Get $get) {
                                        return $get('price_type') == 'fixed';
                                    })
                                    ->heading('Fixed')
                                    ->schema(fn($record) => self::getFixedPriceSchema($record, $finalCommission, $finalCommissionType, $productCommission)),
                                Group::make()
                                    ->hiddenLabel(false)
                                    ->label("abc")
                                    ->extraAttributes([
                                        'style' => 'max-height: 600px; overflow-y: auto;'
                                    ])
                                    ->visible(function (Get $get) {
                                        return $get('price_type') == 'bonus';
                                    })
                                    ->schema([
                                        Section::make()
                                            ->heading('East Malaysia')
                                            ->schema(fn($record) => self::getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)),
                                        Section::make()
                                            ->heading('West Malaysia')
                                            ->schema(fn($record) => self::getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)),
                                    ]),

                                Group::make()
                                    ->visible(function (Get $get) {
                                        return $get('price_type') == 'tier';
                                    })
                                    ->schema(fn($record) => self::tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)),
                                // ])
                            ];
                        })
                        ->action(function ($data, $record, PriceManagementService $service) {
                            // Capture original state for activity log
                            $user = getUser(Auth::user());
                            $userId = $user->id;
                            $relation = self::getPcData($record, $userId);
                            $originalPriceData = ProductRelationPrice::where('product_relation_id', $relation?->id)->first();
                            $originalState = [
                                'relation' => $relation ? $relation->toArray() : [],
                                'price' => $originalPriceData ? $originalPriceData->toArray() : []
                            ];

                            //Activity Log Start
                            // $user = getUser(Auth::user());

                            // $formatBonusData = function ($data, $region) {
                            //     $bonusStrings = [];
                            //     for ($i = 1; $i <= 3; $i++) {
                            //         $qty = $data["{$region}_bonus_{$i}_quantity"] ?? null;
                            //         $price = $data["{$region}_bonus_{$i}_price"] ?? null;
                            //         if ($qty || $price) {
                            //             $bonusStrings[] = "Qty: {$qty}, Price: {$price}";
                            //         }
                            //     }
                            //     return implode(' | ', $bonusStrings);
                            // };

                            // // Helper function to format tier data as string
                            // $formatTierData = function ($tierData) {
                            //     if (!$tierData || !is_array($tierData)) return null;

                            //     $tierStrings = [];
                            //     foreach ($tierData as $index => $tier) {
                            //         $minQty = $tier['min_quantity'] ?? 'N/A';
                            //         $maxQty = $tier['max_quantity'] ?? 'Unlimited';
                            //         $price = $tier['price'] ?? 'N/A';
                            //         $tierStrings[] = "Tier " . ($index + 1) . ": Min {$minQty} - Max {$maxQty}, Price: {$price}";
                            //     }
                            //     return implode(' | ', $tierStrings);
                            // };
                            // $oldLogData = array_filter([
                            //     'price_type' => $data['price_type'],
                            //     'user_name' => $user->name,
                            //     'product_id' => $record->name,

                            //     // Fixed
                            //     'fixed_price_east' => $data['fixed_price_east'] ?? null,
                            //     'fixed_price_west' => $data['fixed_price_west'] ?? null,

                            //     // Bonus
                            //     'east_bonus' => $formatBonusData($data, 'east'),
                            //     'west_bonus' => $formatBonusData($data, 'west'),

                            //     // Tier
                            //     'tier_price_east' => $formatTierData($data['pcInfo_east'] ?? null),
                            //     'tier_price_west' => $formatTierData($data['pcInfo_west'] ?? null),
                            // ]);
                            //Activity Log End

                            if ($data['price_type'] == 'bonus') {
                                $bonusEastQuantity = [];
                                for ($i = 1; $i < 4; $i++) {
                                    if (!empty($data["east_bonus_{$i}_quantity"])) {

                                        $bonusEastQuantity[] = $data["east_bonus_{$i}_quantity"];
                                    }
                                }
                                $eastUnique = array_unique($bonusEastQuantity);

                                if ($bonusEastQuantity != $eastUnique) {
                                    Notification::make()
                                        ->title("")
                                        ->body('Please make sure that all east quantities are unique.')
                                        ->danger()
                                        ->send();
                                    throw ValidationException::withMessages([
                                        '' => $eastUnique,
                                    ]);
                                }
                                $bonusWestQuantity = [];
                                for ($i = 1; $i < 4; $i++) {
                                    if (!empty($data["west_bonus_{$i}_quantity"])) {
                                        $bonusWestQuantity[] = $data["west_bonus_{$i}_quantity"];
                                    }
                                }
                                $westUnique = array_unique($bonusWestQuantity);

                                if ($bonusWestQuantity != $westUnique) {
                                    Notification::make()
                                        ->title("")
                                        ->body('Please make sure that all west quantities are unique.')
                                        ->danger()
                                        ->send();
                                    throw ValidationException::withMessages([
                                        '' => $eastUnique,
                                    ]);
                                }

                                // Validate bonus quantity and value sequence
                                $regions = ['east', 'west'];

                                foreach ($regions as $region) {
                                    $regionTitle = ucfirst($region);

                                    // Get all bonus data for this region
                                    $bonus1Qty = $data["{$region}_bonus_1_quantity"] ?? null;
                                    $bonus1Value = $data["{$region}_bonus_1_quantity_value"] ?? null;
                                    $bonus2Qty = $data["{$region}_bonus_2_quantity"] ?? null;
                                    $bonus2Value = $data["{$region}_bonus_2_quantity_value"] ?? null;
                                    $bonus3Qty = $data["{$region}_bonus_3_quantity"] ?? null;
                                    $bonus3Value = $data["{$region}_bonus_3_quantity_value"] ?? null;

                                    // Check if both quantity and value are provided together
                                    $bonus1Filled = !empty($bonus1Qty) && !empty($bonus1Value);
                                    $bonus2Filled = !empty($bonus2Qty) && !empty($bonus2Value);
                                    $bonus3Filled = !empty($bonus3Qty) && !empty($bonus3Value);

                                    // Check individual field pairing
                                    if ((!empty($bonus1Qty) && empty($bonus1Value)) || (empty($bonus1Qty) && !empty($bonus1Value))) {
                                        Notification::make()
                                            ->title('Bonus Field Error')
                                            ->body("{$regionTitle} Bonus 1: Both Quantity and Bonus Qty. must be entered together.")
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        throw \Illuminate\Validation\ValidationException::withMessages([
                                            "{$region}_bonus_1_quantity" => "Both quantity and bonus qty. must be entered together.",
                                            "{$region}_bonus_1_quantity_value" => "Both quantity and bonus qty. must be entered together."
                                        ]);
                                    }

                                    if ((!empty($bonus2Qty) && empty($bonus2Value)) || (empty($bonus2Qty) && !empty($bonus2Value))) {
                                        Notification::make()
                                            ->title('Bonus Field Error')
                                            ->body("{$regionTitle} Bonus 2: Both Quantity and Bonus Qty. must be entered together.")
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        throw \Illuminate\Validation\ValidationException::withMessages([
                                            "{$region}_bonus_2_quantity" => "Both quantity and bonus qty. must be entered together.",
                                            "{$region}_bonus_2_quantity_value" => "Both quantity and bonus qty. must be entered together."
                                        ]);
                                    }

                                    if ((!empty($bonus3Qty) && empty($bonus3Value)) || (empty($bonus3Qty) && !empty($bonus3Value))) {
                                        Notification::make()
                                            ->title('Bonus Field Error')
                                            ->body("{$regionTitle} Bonus 3: Both Quantity and Bonus Qty. must be entered together.")
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        throw \Illuminate\Validation\ValidationException::withMessages([
                                            "{$region}_bonus_3_quantity" => "Both quantity and bonus qty. must be entered together.",
                                            "{$region}_bonus_3_quantity_value" => "Both quantity and bonus qty. must be entered together."
                                        ]);
                                    }

                                    // Check sequential order
                                    if ($bonus2Filled && !$bonus1Filled) {
                                        Notification::make()
                                            ->title('Bonus Sequence Error')
                                            ->body("{$regionTitle} Bonus 1 must be completed before entering Bonus 2. Please fill Bonus 1 first.")
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        throw \Illuminate\Validation\ValidationException::withMessages([
                                            "{$region}_bonus_2_quantity" => "Please complete Bonus 1 before entering Bonus 2.",
                                            "{$region}_bonus_2_quantity_value" => "Please complete Bonus 1 before entering Bonus 2."
                                        ]);
                                    }

                                    if ($bonus3Filled && (!$bonus1Filled || !$bonus2Filled)) {
                                        $missingBonuses = [];
                                        if (!$bonus1Filled) $missingBonuses[] = "Bonus 1";
                                        if (!$bonus2Filled) $missingBonuses[] = "Bonus 2";

                                        $missingText = implode(' and ', $missingBonuses);
                                        Notification::make()
                                            ->title('Bonus Sequence Error')
                                            ->body("{$regionTitle} {$missingText} must be completed before entering Bonus 3. Please fill bonuses in sequence.")
                                            ->danger()
                                            ->persistent()
                                            ->send();
                                        throw \Illuminate\Validation\ValidationException::withMessages([
                                            "{$region}_bonus_3_quantity" => "Please complete {$missingText} before entering Bonus 3.",
                                            "{$region}_bonus_3_quantity_value" => "Please complete {$missingText} before entering Bonus 3."
                                        ]);
                                    }
                                }
                            }
                            if ($data['price_type'] == 'tier') {
                                $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
                                $westTierPriceInfo = $data['pcInfo_west'] ?? [];
                                $lastIndexForEast = count($eastTierPriceInfo) - 1;
                                $lastIndexForWest = count($westTierPriceInfo) - 1;

                                foreach ($eastTierPriceInfo as $index => $row) {

                                    if ($index !== $lastIndexForEast && empty($row['max_quantity'])) {
                                        Notification::make()
                                            ->title('Max quantity is required for all but the last row in East pricing.')
                                            ->danger()
                                            ->send();
                                        throw ValidationException::withMessages([
                                            'pcInfo_east.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in East pricing.',
                                        ]);
                                    }
                                }

                                foreach ($westTierPriceInfo as $index => $row) {
                                    if ($index !== $lastIndexForWest && empty($row['max_quantity'])) {
                                        Notification::make()
                                            ->title('Max quantity is required for all but the last row in East pricing.')
                                            ->danger()
                                            ->send();
                                        throw ValidationException::withMessages([
                                            'pcInfo_west.' . $index . '.max_quantity' => 'Max quantity is required for all but the last row in West pricing.',
                                        ]);
                                    }
                                }
                            }

                            $service->store($data, $record, $relation->id, $user);

                            // Clear cache after price update
                            \App\Services\ProductRelationCacheService::clearProductRelationPriceCache($relation->id);
                            \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                            // Capture final state for activity log
                            $finalPriceData = ProductRelationPrice::where('product_relation_id', $relation->id)->first();
                            $finalState = [
                                'relation' => $relation->fresh()->toArray(),
                                'price' => $finalPriceData ? $finalPriceData->toArray() : []
                            ];

                            // Extract only changed fields
                            $changedData = [];
                            $oldChangedData = [];

                            foreach ($finalState as $key => $newValues) {
                                $oldValues = $originalState[$key] ?? [];

                                // For arrays, compare individual keys
                                $changes = [];
                                $oldChanges = [];

                                foreach ($newValues as $field => $newValue) {
                                    $oldValue = $oldValues[$field] ?? null;

                                    // Convert both to same type for proper comparison
                                    $normalizedOld = is_numeric($oldValue) ? (float)$oldValue : $oldValue;
                                    $normalizedNew = is_numeric($newValue) ? (float)$newValue : $newValue;

                                    if ($normalizedOld !== $normalizedNew) {
                                        $changes[$field] = $newValue;
                                        $oldChanges[$field] = $oldValue;
                                    }
                                }
                                // dd($oldChanges);
                                if (!empty($changes)) {
                                    $changedData[$key] = $changes;
                                    $oldChangedData[$key] = $oldChanges;
                                }

                                // 🎯 Debug: Check what data we're working with
                                // if ($key === 'price') {
                                //     dd([
                                //         'Step 1 - Raw oldChanges' => $oldChanges,
                                //         'Step 2 - Previous price type' => $originalState['price']['price_type'] ?? 'NOT_FOUND',
                                //         'Step 3 - Original price data' => $originalState['price'] ?? 'NOT_FOUND',
                                //         'Step 4 - Final price data' => $finalState['price'] ?? 'NOT_FOUND',
                                //         'Step 5 - All original state' => $originalState
                                //     ]);
                                // }

                                // 🎯 Filter price data based on previous price type to show only relevant fields
                                if ($key === 'price' && !empty($oldChanges)) {
                                    // ✅ FIX: Get price_type from relation data, not price data

                                    $previousPriceType = $originalState['relation']['price_type'] ?? null;
                                    $filteredOldChanges = [];

                                    foreach ($oldChanges as $field => $value) {
                                        $shouldInclude = false;

                                        // Always include core fields regardless of price type
                                        if (in_array($field, ['price_type', 'updated_at'])) {
                                            $shouldInclude = true;
                                        }
                                        // Handle based on previous price type
                                        elseif ($previousPriceType === 'fixed') {
                                            // Fixed: only show zone prices (simple pricing)
                                            if (in_array($field, ['east_zone_price', 'west_zone_price'])) {
                                                $shouldInclude = true;
                                            }
                                        } elseif ($previousPriceType === 'bonus') {
                                            // Bonus: show bonus fields and zone prices
                                            if (str_contains($field, 'bonus') || in_array($field, ['east_zone_price', 'west_zone_price'])) {
                                                $shouldInclude = true;
                                            }
                                        } elseif ($previousPriceType === 'tier') {
                                            // Tier: show tier fields and zone prices  
                                            if (str_contains($field, 'tier') || in_array($field, ['east_zone_price', 'west_zone_price'])) {
                                                $shouldInclude = true;
                                            }
                                        }

                                        if ($shouldInclude) {
                                            $filteredOldChanges[$field] = $value;
                                        }
                                    }

                                    // Update with filtered data
                                    $oldChangedData[$key] = $filteredOldChanges;
                                }
                            }

                            if (!empty($changedData)) {
                                // 🎯 Create comprehensive human-readable activity data
                                $humanReadableData = [
                                    // Basic Context
                                    'Product Name' => $record->name,
                                    'SKU' => $relation->sku ?? 'Not Set',
                                    'Category' => $record->category?->name ?? 'Unknown',
                                    'Sub Category' => $record->subCategory?->name ?? 'Unknown',
                                    'Brand' => $record->brand?->name ?? 'Unknown',
                                    'Pricing Type' => ucfirst($data['price_type']),
                                ];

                                // Add detailed pricing information based on type
                                if ($data['price_type'] === 'fixed') {
                                    $humanReadableData['Fixed Pricing'] = [
                                        'East Malaysia' => 'RM ' . number_format($data['east_zone_price_1'] ?? $data['fixed_price_east'] ?? 0, 2),
                                        'West Malaysia' => 'RM ' . number_format($data['west_zone_price_1'] ?? $data['fixed_price_west'] ?? 0, 2),
                                    ];
                                } elseif ($data['price_type'] === 'bonus') {
                                    $humanReadableData['Bonus Pricing'] = [
                                        'East Malaysia' => [
                                            'Base Price' => 'RM ' . number_format($data['east_zone_price_bonus'] ?? $data['east_base_price'] ?? 0, 2),
                                            'Bonus Structure' => self::formatBonusStructureForLog($data, 'east')
                                        ],
                                        'West Malaysia' => [
                                            'Base Price' => 'RM ' . number_format($data['west_zone_price_bonus'] ?? $data['west_base_price'] ?? 0, 2),
                                            'Bonus Structure' => self::formatBonusStructureForLog($data, 'west')
                                        ]
                                    ];
                                } elseif ($data['price_type'] === 'tier') {
                                    $humanReadableData['Tier Pricing'] = [
                                        'East Malaysia' => self::formatTierStructureForLog($data['pcInfo_east'] ?? []),
                                        'West Malaysia' => self::formatTierStructureForLog($data['pcInfo_west'] ?? [])
                                    ];
                                }


                                // dd($oldChangedData);
                                // Create clean, user-friendly old data structure
                                $humanReadableOldData = [
                                    'Product Name' => $record->name,
                                    'SKU' => $relation->sku ?? 'Not Set',
                                    'Category' => $record->category?->name ?? 'Unknown',
                                    'Sub Category' => $record->subCategory?->name ?? 'Unknown',
                                    'Brand' => $record->brand?->name ?? 'Unknown',
                                    'Previous Price Type' => $originalState['relation']['price_type'] ?? 'Unknown',
                                ];

                                // Add detailed old pricing information based on previous price type
                                $previousPriceType = $originalState['relation']['price_type'] ?? null;

                                // Get the original price data directly from the original state
                                $originalPriceData = $originalState['price'] ?? [];

                                if ($previousPriceType === 'fixed') {
                                    $humanReadableOldData['Previous Fixed Pricing'] = [
                                        'East Malaysia' => 'RM ' . number_format($originalPriceData['east_zone_price'] ?? 0, 2),
                                        'West Malaysia' => 'RM ' . number_format($originalPriceData['west_zone_price'] ?? 0, 2),
                                    ];
                                } elseif ($previousPriceType === 'bonus') {
                                    $humanReadableOldData['Previous Bonus Pricing'] = [
                                        'East Malaysia' => [
                                            'Base Price' => 'RM ' . number_format($originalPriceData['east_zone_price'] ?? 0, 2),
                                            'Bonus Structure' => self::formatOldBonusStructureForLog($originalPriceData, 'east')
                                        ],
                                        'West Malaysia' => [
                                            'Base Price' => 'RM ' . number_format($originalPriceData['west_zone_price'] ?? 0, 2),
                                            'Bonus Structure' => self::formatOldBonusStructureForLog($originalPriceData, 'west')
                                        ]
                                    ];
                                } elseif ($previousPriceType === 'tier') {
                                    $humanReadableOldData['Previous Tier Pricing'] = [
                                        'East Malaysia' => self::formatOldTierStructureForLog($originalPriceData, 'east'),
                                        'West Malaysia' => self::formatOldTierStructureForLog($originalPriceData, 'west')
                                    ];
                                }
                                // dd($oldChangedData);
                                activity()
                                    ->performedOn($record)
                                    ->causedBy(Auth::user())
                                    ->withProperties([
                                        'old' => $humanReadableOldData,
                                        'attributes' => $humanReadableData
                                    ])
                                    ->log("PC updated pricing for '{$record->name}' - {$humanReadableData['Pricing Type']} pricing structure applied");
                            }

                            // $record->relationPrices->toArray();
                            // $oldData = ['old'=>$oldData];
                            // $newData = ['new'=>$data];
                            // $activityData = array_merge($oldData, $newData);
                            // activity()
                            //     ->performedOn($record)
                            //     ->causedBy(Auth::user())
                            //     ->withProperties($activityData)
                            //     ->log('Product Price Updated by PC');
                            //Activity Log Start
                            // $newLogData = array_filter([
                            //     'price_type' => $data['price_type'],
                            //     'user_name' => $user->name,
                            //     'product_id' => $record->name,

                            //     // Fixed
                            //     'fixed_price_east' => $data['fixed_price_east'] ?? null,
                            //     'fixed_price_west' => $data['fixed_price_west'] ?? null,

                            //     // Bonus
                            //     'east_bonus' => $formatBonusData($data, 'east'),
                            //     'west_bonus' => $formatBonusData($data, 'west'),

                            //     // Tier - Convert to strings
                            //     'tier_price_east' => $formatTierData($data['pcInfo_east'] ?? null),
                            //     'tier_price_west' => $formatTierData($data['pcInfo_west'] ?? null),
                            // ]);

                            // activity()
                            //     ->causedBy(Auth::user()))
                            //     ->useLog('product_price_update')
                            //     ->performedOn($record)
                            //     ->withProperties(['old' => $oldLogData, 'attributes' => $newLogData])
                            //     ->log("Price for product {$record->name} has been updated");
                            //Activity Log End

                            Notification::make()
                                ->title('Product Price Updated')
                                ->success()
                                ->send();
                        }),
                    Action::make('update_stock')
                        ->visible(function ($record) {
                            $approved = $record->status == 'approved';
                            $user = Auth::user();
                            return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_update stock'));
                        })
                        ->modalSubmitActionLabel('Save')
                        ->label('Update Stock')
                        ->icon('heroicon-o-pencil')
                        ->action(function (array $data, $record) {
                            $data['wholesale_pack_size'] = $data['stock_type'] == 'wps' ? $data['wholesale_pack_size'] : 1;

                            $user = getUser(Auth::user());
                            $userId = $user->id;
                            $relation = $record->productDataForPc($userId);
                            $originalStockData = ProductRelationStock::where('product_relation_id', $relation?->id)->first();
                            $originalBatches = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();

                            // 🎯 Capture clean original state with only essential data
                            $originalStockArray = $originalStockData ? [
                                'stock' => $originalStockData->stock,
                                'total_stock' => $originalStockData->total_stock,
                                'is_batch_wise_stock' => $originalStockData->is_batch_wise_stock,
                                'low_stock' => $originalStockData->low_stock,
                                'wholesale_pack_size' => $data['wholesale_pack_size'],
                                'stock_type' => $originalStockData->stock_type,
                            ] : [];

                            $insertedBatchIds = [];

                            if ($data['stock_status'] == 'out_of_stock') {
                                $userId = getUser(Auth::user())->id;
                                $relation = $record->productDataForPc($userId);
                                ProductRelationStock::updateOrCreate(['product_relation_id' => $relation->id], Arr::only($data, ['low_stock', 'wholesale_pack_size', 'stock_type']));

                                $productStock = ProductRelationStock::where('product_relation_id', $relation->id)->first();
                                if ($productStock?->is_batch_wise_stock) {
                                    ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                                }
                                $productStock->update(['stock' => 0, 'total_stock' => 0, 'is_batch_wise_stock' => false]);

                                // Clear cache after stock update
                                \App\Services\ProductRelationCacheService::clearProductRelationStockCache($relation->id);
                                \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                                // Clear additional caches
                                \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                                \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                                \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                                // Log activity for out of stock update - only changed fields
                                $changedFields = [];
                                $oldFields = [];

                                if (($originalStockArray['stock'] ?? 0) != 0) {
                                    $changedFields['stock'] = 0;
                                    $oldFields['stock'] = $originalStockArray['stock'] ?? 0;
                                }
                                if (($originalStockArray['total_stock'] ?? 0) != 0) {
                                    $changedFields['total_stock'] = 0;
                                    $oldFields['total_stock'] = $originalStockArray['total_stock'] ?? 0;
                                }
                                if (($originalStockArray['is_batch_wise_stock'] ?? false) != false) {
                                    $changedFields['is_batch_wise_stock'] = false;
                                    $oldFields['is_batch_wise_stock'] = $originalStockArray['is_batch_wise_stock'] ?? false;
                                }

                                // Add non-stock fields that might have changed
                                if (($originalStockArray['low_stock'] ?? null) != $data['low_stock']) {
                                    $changedFields['low_stock'] = $data['low_stock'];
                                    $oldFields['low_stock'] = $originalStockArray['low_stock'] ?? null;
                                }
                                if (($originalStockArray['wholesale_pack_size'] ?? null) != $data['wholesale_pack_size']) {
                                    $changedFields['wholesale_pack_size'] = $data['wholesale_pack_size'];
                                    $oldFields['wholesale_pack_size'] = $originalStockArray['wholesale_pack_size'] ?? null;
                                }
                                if (($originalStockArray['stock_type'] ?? null) != $data['stock_type']) {
                                    $changedFields['stock_type'] = $data['stock_type'];
                                    $oldFields['stock_type'] = $originalStockArray['stock_type'] ?? null;
                                }

                                if (!empty($changedFields)) {
                                    $user = getUser(Auth::user());
                                    $stockData = ProductRelationStock::where('product_relation_id', $relation->id)->first();
                                    $batches = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();
                                    $totalBatchStock = $batches->sum('available_stock');

                                    // 🎯 Create human-readable old data
                                    $humanReadableOldData = self::formatOldDataForStockLog($oldFields);

                                    // 🎯 Create comprehensive human-readable activity data
                                    $humanReadableData = [
                                        // Basic Context
                                        'Product Name' => $record->name,
                                        'SKU' => $relation->sku ?? 'Not Set',
                                        'Updated By PC' => $user->name,
                                        'Updated By ID' => $user->id,
                                        'Update Type' => $data['stock_status'] === 'out_of_stock' ? 'PC Stock Update - Out of Stock' : 'PC Stock Update - In Stock',

                                        // Product Details
                                        'Category' => $record->category?->name ?? 'Unknown',
                                        'Brand' => $record->brand?->name ?? 'Unknown',

                                        // Stock Configuration
                                        'Stock Status' => $data['stock_status'] === 'out_of_stock' ? 'Out of Stock' : 'In Stock',
                                        'Stock Management' => $stockData?->is_batch_wise_stock ? 'By Batch' : 'Simple Stock',
                                        'Stock Type' => $data['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                                        'Wholesale Pack Size' => $data['wholesale_pack_size'] ?? 'Not Set',
                                        'Low Stock Trigger' => $data['low_stock'] ?? 'Not Set',

                                        // Stock Details
                                        'Current Stock' => $data['stock_status'] === 'out_of_stock'
                                            ? '0'
                                            : ($stockData?->is_batch_wise_stock
                                                ? $totalBatchStock . ' (from batches)'
                                                : ($data['stock'] ?? 'Not Set')),
                                        'Total Stock' => $data['total_stock'] ?? $stockData?->total_stock ?? 'Not Set',
                                        'Batch Count' => $batches->count(),

                                        // Technical Changes
                                        'Changed Fields' => array_keys($changedFields),
                                        'Updated At' => now()->format('Y-m-d H:i:s'),
                                    ];

                                    // Add batch details if applicable
                                    if ($stockData?->is_batch_wise_stock && $batches->count() > 0) {
                                        $humanReadableData['Batch Details'] = [];
                                        foreach ($batches as $index => $batch) {
                                            $batchNum = $index + 1;
                                            $humanReadableData['Batch Details']["Batch {$batchNum}"] = [
                                                'Batch Number' => $batch->batch_number ?? 'Not Set',
                                                'Available Stock' => $batch->available_stock ?? 0,
                                                'Manufacturing Date' => $batch->manufacturing_date ?? 'Not Set',
                                                'Expiry Date' => $batch->expiry_date ?? 'Not Set',
                                            ];
                                        }
                                    }

                                    activity()
                                        ->performedOn($record)
                                        ->causedBy(Auth::user())
                                        ->withProperties([
                                            'old' => $humanReadableOldData,
                                            'attributes' => $humanReadableData
                                        ])
                                        ->log("PC '{$user->name}' updated stock for '{$record->name}' - {$humanReadableData['Stock Status']} with {$humanReadableData['Stock Management']}");
                                }

                                //Activity Log Start
                                // $newLogData = array_filter([
                                //     'stock_status' => 'out_of_stock',
                                //     'sku' => $data['sku'] ?? $relation?->sku,
                                //     'low_stock' => $data['low_stock'],
                                //     'wholesale_pack_size' => $data['wholesale_pack_size'],
                                //     'is_batch_wise_stock' => false,
                                //     'stock' => $data['stock'],
                                //     'total_stock' => $data['total_stock'],
                                // ]);

                                // activity()
                                //     ->causedBy(Auth::user()))
                                //     ->useLog('product_stock_update')
                                //     ->performedOn($record)
                                //     ->withProperties(['old' => $oldLogData, 'attributes' => $newLogData])
                                //     ->log("Stock for product {$record->name} set to out of stock");
                                //Activity Log End

                                // Send success notification for out of stock
                                Notification::make()
                                    ->title('Stock Updated Successfully')
                                    ->body("{$record->name} has been marked as out of stock.")
                                    ->success()
                                    ->send();

                                return;
                            }
                            $productData = $record->productDataForPc(getUser(Auth::user())->id);
                            //convert below code to create or update

                            $productRelationData = ProductRelationStock::updateOrCreate(
                                ['product_relation_id' => $productData?->id],
                                [
                                    'low_stock' => $data['low_stock'],
                                    'wholesale_pack_size' => $data['wholesale_pack_size'],
                                    'stock_type' => $data['stock_type'],
                                ]
                            );

                            $batches = $data['batches'] ?? [];
                            $batchNames = array_column($batches, 'batch_name');
                            $batchNames = array_filter($batchNames, fn($name) => is_string($name) && trim($name) !== '');
                            $batchStock = 0;
                            $duplicates = array_filter(array_count_values($batchNames), fn($count) => $count > 1);
                            $isDuplicateName = count($duplicates) > 0;
                            if ($isDuplicateName) {
                                Notification::make()
                                    // ->title('Duplicate Batch Name')
                                    ->title('Duplicate batch name found. Please check the batch names.')
                                    ->danger()
                                    ->send();
                                return;
                            }
                            if (!empty($data['batches'])) {
                                foreach ($data['batches'] as $batch) {

                                    $batchStock += $batch['available_stock'];
                                    $data['productData']['total_stock'] = $batchStock;
                                }
                            } else {
                                $data['productData']['total_stock'] = $data['productData']['stock'];
                            }
                            $userId = getUser(Auth::user())->id;
                            // if (empty($data['batches'])) {
                            //     $userId = getUser(Auth::user())->id;
                            //     ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                            // }

                            $relation = ProductRelation::where(['product_id' => $record->id, 'user_id' => $userId]);
                            $relation?->update(['sku' => $data['sku']]);
                            $productRelationId = $relation?->first()?->id;
                            $data['productData']['low_stock'] = $data['low_stock'];
                            $data['productData']['stock_type'] = $data['stock_type'];
                            // dd($data['productData']);
                            $data['productData']['expiry_date'] = !empty($data['productData']['expiry_date']) ? Carbon::parse($data['productData']['expiry_date'])->setTime(23, 59, 0) : null;
                            // dd($data['productData']);
                            $stock = ProductRelationStock::updateOrCreate(['product_relation_id' => $productRelationId], $data['productData']);

                            $batchesData = [];
                            $relation = $relation?->first();
                            if (!empty($data['batches'])) {

                                $batchesData = [];
                                foreach ($data['batches'] as $batch) {
                                    $batchesData[] = [
                                        'product_id' => $record?->id,
                                        'user_id' => $userId,
                                        'products_relation_id' => $relation?->id,
                                        'available_stock' => $batch['available_stock'],
                                        'batch_name' => $batch['batch_name'],
                                        'expiry_date' => $batch['expiry_date'],
                                    ];
                                }
                                ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                                $stock->update(['is_batch_wise_stock' => true]);
                                //here I want to get the id of the last inserted batch
                                // For inserting multiple batches and getting their IDs in PostgreSQL, use insert() and then fetch the inserted rows.
                                ProductBatch::insert($batchesData);

                                // Fetch the IDs of the newly inserted batches for this product/user/relation
                                $insertedBatchIds = ProductBatch::where([
                                    'product_id' => $record?->id,
                                    'user_id' => $userId,
                                    'products_relation_id' => $relation?->id,
                                ])
                                    ->orderByDesc('id')
                                    ->limit(count($batchesData))
                                    ->pluck('id')
                                    ->toArray();

                                // dd($insertedBatchIds); // For debugging, remove or comment out in production
                                // add activity log with old data and new data

                                //Activity Log Start
                                // $newLogData = array_filter([
                                //     'stock_status' => $data['stock_status'],
                                //     'sku' => $data['sku'],
                                //     'low_stock' => $data['low_stock'],
                                //     'wholesale_pack_size' => $data['wholesale_pack_size'],
                                //     'is_batch_wise_stock' => $data['productData']['is_batch_wise_stock'] ?? false,
                                //     'stock' => $data['productData']['stock'] ?? null,
                                //     'total_stock' => $data['productData']['total_stock'] ?? 0,
                                //     'available_stock' => $batch['available_stock'],
                                //     'batch_name' => $batch['batch_name'],
                                //     'expiry_date' => $batch['expiry_date'],
                                // ]);

                                // // Log the update
                                // activity()
                                //     ->causedBy(Auth::user()))
                                //     ->useLog('product_stock_update')
                                //     ->performedOn($record)
                                //     ->withProperties([
                                //         'old' => $oldLogData,
                                //         'attributes' => $newLogData
                                //     ])
                                //     ->log("Stock for product {$record->name} updated");
                                //Activity Log End
                            }

                            // Clear cache after stock update
                            \App\Services\ProductRelationCacheService::clearProductRelationStockCache($productRelationId);
                            \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                            // Clear additional caches
                            \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                            \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                            \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                            // Clear static caches that might be used in table columns
                            unset($record->_cached_product_data);

                            // 🎯 Compare only essential stock fields for clean activity logging
                            $finalStockData = ProductRelationStock::where('product_relation_id', $productRelationId)->first();
                            $finalBatches = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();

                            // Compare and get only changed fields
                            $changedData = [];
                            $oldChangedData = [];

                            // Compare stock data with meaningful field names only
                            $stockChanges = [];
                            $oldStockChanges = [];

                            // Check key stock fields
                            $stockFields = ['stock', 'total_stock', 'is_batch_wise_stock', 'low_stock', 'wholesale_pack_size', 'stock_type'];
                            foreach ($stockFields as $field) {
                                $oldValue = $originalStockArray[$field] ?? null;
                                $newValue = $finalStockData?->$field ?? null;

                                if ($oldValue != $newValue) {
                                    $stockChanges[$field] = $newValue;
                                    $oldStockChanges[$field] = $oldValue;
                                }
                            }

                            if (!empty($stockChanges)) {
                                $changedData['stock'] = $stockChanges;
                                $oldChangedData['stock'] = $oldStockChanges;
                            }

                            // 🎯 Enhanced batch comparison logic similar to ViewProduct.php
                            $originalIsBatchWise = $originalStockArray['is_batch_wise_stock'] ?? false;
                            $finalIsBatchWise = $finalStockData?->is_batch_wise_stock ?? false;

                            // Calculate original total stock (either from batches or direct stock)
                            $originalTotalStock = $originalIsBatchWise
                                ? $originalBatches->sum('available_stock')
                                : ($originalStockArray['stock'] ?? 0);

                            // Calculate final total stock
                            $finalTotalStock = $finalIsBatchWise
                                ? $finalBatches->sum('available_stock')
                                : ($finalStockData?->stock ?? 0);

                            // Check for stock type transition (batch to normal or normal to batch)
                            if ($originalIsBatchWise !== $finalIsBatchWise) {
                                // Stock type changed - capture comprehensive before/after state
                                $stockChanges['is_batch_wise_stock'] = $finalIsBatchWise;
                                $oldStockChanges['is_batch_wise_stock'] = $originalIsBatchWise;

                                $stockChanges['stock'] = $finalStockData?->stock ?? 0;
                                $oldStockChanges['stock'] = $originalStockArray['stock'] ?? 0;

                                $stockChanges['total_stock'] = $finalStockData?->total_stock ?? 0;
                                $oldStockChanges['total_stock'] = $originalTotalStock; // Use calculated original total

                                // Always capture batch transition
                                $originalBatchData = $originalBatches->map(function ($batch) {
                                    return [
                                        'batch_number' => $batch->batch_name ?? 'Not Set', // ✅ Fixed field name
                                        'available_stock' => $batch->available_stock ?? 0,
                                        'expiry_date' => $batch->expiry_date ?? 'Not Set',
                                    ];
                                })->toArray();

                                $finalBatchData = $finalBatches->map(function ($batch) {
                                    return [
                                        'batch_number' => $batch->batch_name ?? 'Not Set', // ✅ Fixed field name
                                        'available_stock' => $batch->available_stock ?? 0,
                                        'expiry_date' => $batch->expiry_date ?? 'Not Set',
                                    ];
                                })->toArray();

                                if ($originalIsBatchWise && !$finalIsBatchWise) {
                                    // Batch to Normal: Capture all original batches
                                    $oldChangedData['batches'] = $originalBatchData;
                                    $changedData['batches'] = []; // No batches in normal stock
                                } elseif (!$originalIsBatchWise && $finalIsBatchWise) {
                                    // Normal to Batch: Show new batch structure
                                    $oldChangedData['batches'] = []; // No batches before
                                    $changedData['batches'] = $finalBatchData;
                                }
                            } else {
                                // Same stock type - compare batch data only if both are batch-wise
                                if ($originalIsBatchWise && $finalIsBatchWise) {
                                    $originalBatchData = $originalBatches->map(function ($batch) {
                                        return [
                                            'batch_number' => $batch->batch_name ?? 'Not Set', // ✅ Fixed field name
                                            'available_stock' => $batch->available_stock ?? 0,
                                            'expiry_date' => $batch->expiry_date ?? 'Not Set',
                                        ];
                                    })->toArray();

                                    $finalBatchData = $finalBatches->map(function ($batch) {
                                        return [
                                            'batch_number' => $batch->batch_name ?? 'Not Set', // ✅ Fixed field name
                                            'available_stock' => $batch->available_stock ?? 0,
                                            'expiry_date' => $batch->expiry_date ?? 'Not Set',
                                        ];
                                    })->toArray();

                                    if (json_encode($originalBatchData) !== json_encode($finalBatchData)) {
                                        $changedData['batches'] = $finalBatchData;
                                        $oldChangedData['batches'] = $originalBatchData;
                                    }
                                }
                            }

                            if (!empty($changedData)) {
                                $user = getUser(Auth::user());
                                $finalStockDataForLog = ProductRelationStock::where('product_relation_id', $productRelationId)->first();
                                $finalBatchesForLog = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();
                                $totalBatchStock = $finalBatchesForLog->sum('available_stock');

                                // 🎯 Create human-readable old data
                                $humanReadableOldData = self::formatCompleteOldDataForStockLog($oldChangedData);

                                // 🎯 Create comprehensive human-readable activity data
                                $humanReadableData = [
                                    // Basic Context
                                    'Product Name' => $record->name,
                                    'SKU' => $relation->sku ?? 'Not Set',
                                    'Updated By PC' => $user->name,
                                    'Updated By ID' => $user->id,
                                    'Update Type' => 'PC Stock Update - In Stock',

                                    // Product Details
                                    'Category' => $record->category?->name ?? 'Unknown',
                                    'Brand' => $record->brand?->name ?? 'Unknown',

                                    // Stock Configuration
                                    'Stock Status' => 'In Stock',
                                    'Stock Management' => $finalStockDataForLog?->is_batch_wise_stock ? 'By Batch' : 'Simple Stock',
                                    'Stock Type' => $finalStockDataForLog?->stock_type === 'wps' ? 'Wholesale Pack' : 'Unit',
                                    'Wholesale Pack Size' => $finalStockDataForLog?->wholesale_pack_size ?? 'Not Set',
                                    'Low Stock Trigger' => $finalStockDataForLog?->low_stock ?? 'Not Set',

                                    // Stock Details
                                    'Current Stock' => $finalStockDataForLog?->is_batch_wise_stock
                                        ? $totalBatchStock . ' (from batches)'
                                        : ($finalStockDataForLog?->stock ?? 'Not Set'),
                                    'Total Stock' => $finalStockDataForLog?->total_stock ?? 'Not Set',
                                    'Batch Count' => $finalBatchesForLog->count(),

                                    // Technical Changes
                                    'Changed Fields' => array_keys($changedData),
                                    'Updated At' => now()->format('Y-m-d H:i:s'),
                                ];

                                // Add batch details if applicable
                                if ($finalStockDataForLog?->is_batch_wise_stock && $finalBatchesForLog->count() > 0) {
                                    $humanReadableData['Batch Details'] = [];
                                    foreach ($finalBatchesForLog as $index => $batch) {
                                        $batchNum = $index + 1;
                                        $humanReadableData['Batch Details']["Batch {$batchNum}"] = [
                                            'Batch Number' => $batch->batch_name ?? 'Not Set', // ✅ Fixed field name
                                            'Available Stock' => $batch->available_stock ?? 0,
                                            'Expiry Date' => $batch->expiry_date ?? 'Not Set',
                                            // ✅ Removed Manufacturing Date as requested
                                        ];
                                    }
                                }

                                // 🎯 Debug: Log comprehensive stock transition data
                                Log::info('Activity Log Debug - ProductResource In Stock', [
                                    'original_is_batch_wise' => $originalIsBatchWise,
                                    'final_is_batch_wise' => $finalIsBatchWise,
                                    'stock_type_changed' => $originalIsBatchWise !== $finalIsBatchWise,
                                    'original_batch_count' => $originalBatches->count(),
                                    'final_batch_count' => $finalBatches->count(),
                                    'original_total_stock' => $originalTotalStock,
                                    'final_total_stock' => $finalTotalStock,
                                    'changedData_keys' => array_keys($changedData),
                                    'oldChangedData_keys' => array_keys($oldChangedData),
                                    'has_old_batch_details' => isset($humanReadableOldData['Batch Details']),
                                    'has_new_batch_details' => isset($humanReadableData['Batch Details']),
                                ]);

                                activity()
                                    ->performedOn($record)
                                    ->causedBy(Auth::user())
                                    ->withProperties([
                                        'old' => $humanReadableOldData,
                                        'attributes' => $humanReadableData
                                    ])
                                    ->log("PC '{$user->name}' updated stock for '{$record->name}' - {$humanReadableData['Stock Status']} with {$humanReadableData['Stock Management']}");
                            }

                            // Send success notification
                            Notification::make()
                                ->title('Stock Updated Successfully')
                                ->body("Stock for {$record->name} has been updated successfully.")
                                ->success()
                                ->send();
                        })
                        ->modalWidth('7xl')
                        ->form(function ($record) {
                            $user = Auth::user();
                            $userId = $user->id;
                            if (!empty($user->parent_id)) {
                                $userId = $user->parent_id;
                            }

                            $productData = $record?->productDataForPc($userId);
                            $productRelationData = ProductRelationPrice::where('product_relation_id', $productData?->id)->first();
                            $batchDetail = ProductRelationStock::where('product_relation_id', $productData?->id)->first();

                            return [
                                Section::make()->schema([
                                    Group::make()->schema([
                                        Placeholder::make('name')
                                            ->label('Product Name')
                                            ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent'])
                                            ->content(fn($record) => $record?->name),
                                        Placeholder::make('unit')
                                            ->label('Volume Unit')
                                            ->content(function ($record) {
                                                return $record?->unit?->name ?? "";
                                            })
                                            ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent']),
                                        TextInput::make('sku')
                                            ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                                            ->validationAttribute('SKU')
                                            ->rules([
                                                'required',
                                                'string',
                                            ])
                                            ->formatStateUsing(function ($record) {
                                                $userId = getUser(Auth::user())->id;
                                                return $record?->productDataForPc($userId)->sku ?? "";
                                            }),
                                        TextInput::make('low_stock')
                                            ->label(function ($record) {
                                                $containerName = $record->container->name ?? "";
                                                return new HtmlString("<div class='text-sm'>Low Stock trigger value by $containerName<span class='text-red-500' style='color:red;'>*</span> </div>");
                                            })
                                            ->numeric()
                                            ->validationAttribute('low stock')
                                            ->rules(rules: ['integer', 'required', 'gt:0'])
                                            ->default('-')
                                            ->formatStateUsing(function ($record) {
                                                $productRelation = $record?->productDataForPc(getUser(Auth::user())->id);
                                                return ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->low_stock;
                                            }),

                                        Toggle::make('productData.is_batch_wise_stock')
                                            ->label('By Batch')
                                            ->inline(false)
                                            ->extraAttributes(['style' => 'marign-top: 30px; !important'])
                                            ->live(onBlur: true)
                                            // ->disabled(function() use ($batchDetail) {
                                            //     return $batchDetail?->stock > 0;
                                            // })
                                            // ->helperText(function() use ($batchDetail) {
                                            //     return $batchDetail?->stock > 0 
                                            //         ? 'Cannot change the stock type when there is existing stock. Please update the stock status to out of stock first.'
                                            //         : null;
                                            // })
                                            ->formatStateUsing(function ($record) use ($batchDetail) {
                                                return $batchDetail?->is_batch_wise_stock ?? true;
                                            }),
                                        Select::make('stock_status')
                                            ->options([
                                                'in_stock' => 'In Stock',
                                                'out_of_stock' => 'Out of Stock',
                                            ])
                                            ->afterStateUpdated(function (Get $get) {
                                                if ($get('stock_status') == 'out_of_stock') {
                                                    Notification::make()
                                                        ->body('This will remove all the current stock data !')
                                                        ->info()
                                                        ->send();
                                                }
                                            })
                                            ->formatStateUsing(function ($record) {
                                                $userId = getUser(Auth::user())->id;
                                                $productRelation = $record?->productDataForPc($userId);
                                                $stockData = ProductRelationStock::where(['product_relation_id' => $productRelation?->id])->first();
                                                if ($stockData?->is_batch_wise_stock) {
                                                    $currentStock = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId, 'products_relation_id' => $stockData?->product_relation_id])->sum('available_stock');

                                                    if ($currentStock > 0) {
                                                        return 'in_stock';
                                                    } else {
                                                        return 'out_of_stock';
                                                    }
                                                } else {
                                                    $currentStock = $stockData?->stock;
                                                    if ($currentStock > 0) {
                                                        return 'in_stock';
                                                    } else {
                                                        return 'out_of_stock';
                                                    }
                                                }
                                            })
                                            ->live(),
                                        TextInput::make('wholesale_pack_size')
                                            ->columnSpan(2)
                                            ->visible(fn(Get $get) => $get('stock_type') == 'wps')
                                            ->label(new HtmlString('Wholesale Pack Size<span class="text-red-500" style="color:red;">*</span>'))
                                            ->numeric()
                                            ->live()
                                            ->validationAttribute('Wholesale Pack Size')
                                            ->rules(function (Get $get) {
                                                if ($get('stock_type') == 'wps') {
                                                    return ['integer', 'required', 'gt:1'];
                                                }
                                                return ['integer', 'required', 'gt:0'];
                                            })
                                            ->default('-')
                                            ->prefix(function ($record) {
                                                return new HtmlString("<div class='text-sm font-semibold text-gray-600'>" . Str::plural($record->container?->name ?? '') . " of</div>");
                                            })
                                            ->suffix(function ($record, Get $get) {
                                                $qty = $record?->quantity_per_unit;

                                                if ($get('wholesale_pack_size') && $get('wholesale_pack_size') > 0) {
                                                    $qty = $qty * (int) $get('wholesale_pack_size');
                                                }
                                                if (!empty($record?->wholesale_pack_size) && $record->wholesale_pack_size > 0) {
                                                    $qty = $qty * (int) $record->wholesale_pack_size;
                                                }
                                                return new HtmlString("<div class='text-sm font-semibold text-gray-600'>{$qty} " . Str::plural($record->foam->name ?? '') . "</div>");
                                            })
                                            ->formatStateUsing(function ($record) {
                                                $productRelation = $record->productDataForPc(getUser(Auth::user())->id);
                                                return ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->wholesale_pack_size ?? null;
                                            }),
                                        Radio::make('stock_type')
                                            ->label('Stock Type')
                                            ->options([
                                                'unit' => 'Unit',
                                                'wps' => new HtmlString('Wholesale Pack<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Indicates the quantity of items included in one wholesale unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                            </svg>'),
                                            ])
                                            ->inline()
                                            ->live()
                                            ->rules(['required'])
                                            ->columnSpanFull()
                                            ->validationAttribute('stock type')
                                            ->formatStateUsing(function ($record) {
                                                $productRelation = $record?->productDataForPc(getUser(Auth::user())->id);
                                                return ProductRelationStock::where('product_relation_id', $productRelation?->id)->first()?->stock_type ?? 'unit';
                                            }),
                                    ])->columns(4),
                                ]),
                                Section::make()
                                    ->heading(fn(Get $get) => new HtmlString(
                                        '<div class="flex items-center justify-between w-full">
                                            <span class="text-base font-bold text-gray-900">Manage Batch</span>
                                            <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                                            collect($get('batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                                        ) . '</span>
                                        </div>'
                                    ))
                                    ->visible(fn(Get $get) => $get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                                    ->schema(function (Get $get) {
                                        return [
                                            TableRepeater::make('batches')
                                                ->live()
                                                ->defaultItems(1)
                                                ->default(function ($record) {

                                                    $user = getUser(Auth::user());
                                                    $userId = $user->id;
                                                    $productRelationId = ProductRelation::where('product_id', $record->id)->where('user_id', $userId)->first()->id;
                                                    return $record?->batches?->where('user_id', $userId)->where('products_relation_id', $productRelationId)->map(fn($batch) => [
                                                        'batch_name' => $batch?->batch_name,
                                                        'available_stock' => $batch?->available_stock,
                                                        'expiry_date' => $batch?->expiry_date,
                                                    ])->toArray() ?? [['batch_name' => '', 'available_stock' => '', 'expiry_date' => '']];
                                                })
                                                ->minItems(fn(Get $get) => $get('productData.is_batch_wise_stock') ? 1 : 0)
                                                ->addActionAlignment(Alignment::End)
                                                ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                    return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                                        ->extraAttributes([
                                                            'style' => 'border: none !important; box-shadow: none !important;'
                                                        ]);
                                                })
                                                ->reorderable(false)
                                                ->headers([
                                                    Header::make('batch_name')->label('Batch Number'),
                                                    Header::make('available_stock')->label('Stock by Packaging'),
                                                    Header::make('expiry_date')->label('Expiry Date'),
                                                ])
                                                ->schema(function (Get $get, $record) {
                                                    return [
                                                        TextInput::make('batch_name')
                                                            ->label('Batch Name')
                                                            ->rules(function ($record) use ($get) {
                                                                return $get('productData.is_batch_wise_stock')
                                                                    ? ['required', 'string', 'max:255', Rule::unique('products_batch', 'batch_name')->ignore($record?->id, 'product_id')]
                                                                    : ['nullable', 'string', 'max:255', Rule::unique('products_batch', 'batch_name')->ignore($record?->id, 'product_id')];
                                                            })
                                                            ->validationAttribute('Batch Name'),
                                                        TextInput::make('available_stock')
                                                            ->label('Available Stock')
                                                            ->live(onBlur: true)
                                                            ->rules(function () use ($get) {
                                                                return $get('productData.is_batch_wise_stock')
                                                                    ? ['required', 'numeric', 'max:999999', 'gt:0']
                                                                    : ['nullable', 'numeric', 'max:999999', 'gt:0'];
                                                            })
                                                            ->numeric(),
                                                        DatePicker::make('expiry_date')
                                                            ->placeholder('Select the expiry date')
                                                            ->label('Expiry Date')
                                                            ->minDate(today())
                                                            ->rules(function () use ($get) {
                                                                return $get('productData.is_batch_wise_stock')
                                                                    ? ['required', 'date', 'after_or_equal:today']
                                                                    : ['nullable', 'date', 'after_or_equal:today'];
                                                            })
                                                            ->validationMessages([
                                                                'after_or_equal' => 'The expiry date must be after or equal to today.',
                                                            ])
                                                            ->validationAttribute('Expiry Date')
                                                    ];
                                                })
                                                ->reactive()

                                        ];
                                    }),
                                Group::make()
                                    ->visible(fn(Get $get) => !$get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                                    ->schema([
                                        Group::make()->schema([
                                            TextInput::make('productData.stock')
                                                ->label(function ($record) {
                                                    return new HtmlString("Stock by {$record?->container?->name}<span class='text-red-500' style='color:red;'>*</span>");
                                                })
                                                ->validationAttribute('Stock')

                                                ->rules(function ($record, Get $get) {
                                                    $lowStock = $record?->productData?->low_stock ?? 0;
                                                    if (!$get('productData.is_batch_wise_stock')) {
                                                        return ['required', 'numeric', 'max:999999', 'gt:' . $lowStock];
                                                    }
                                                    return [];
                                                })
                                                ->formatStateUsing(function ($record) use ($productRelationData, $batchDetail) {
                                                    // dd($batchDetail);
                                                    return $batchDetail->stock ?? null;
                                                }),
                                            DatePicker::make('productData.expiry_date')
                                                ->placeholder('Select the expiry date')
                                                ->label(new HtmlString('Expiry Date<span class="font-bold text-red-500">*</span>'))
                                                ->minDate(today())
                                                ->rules(function (Get $get) {
                                                    if (!$get('productData.is_batch_wise_stock')) {
                                                        return ['required', 'date', 'after_or_equal:today'];
                                                    }
                                                    return [];
                                                })
                                                ->validationAttribute('Expiry Date')
                                                ->rules(function (Get $get) {
                                                    if (!$get('productData.is_batch_wise_stock')) {
                                                        return ['required', 'date', 'after_or_equal:today'];
                                                    }
                                                    return [];
                                                })
                                                ->validationMessages([
                                                    'after_or_equal' => 'The expiry date must be after or equal to today.',
                                                ])
                                                ->default(function ($record) use ($productRelationData, $batchDetail) {
                                                    return $batchDetail->expiry_date ?? null;
                                                })
                                        ])->columns(2),

                                        // Expired Stock Removal Action
                                        // Group::make()
                                        //     ->visible(function ($record) use ($batchDetail) {
                                        //         // Check if stock type is non-batch-wise, has stock, and expiry date has passed
                                        //         if (!$batchDetail || $batchDetail->is_batch_wise_stock) {
                                        //             return false;
                                        //         }

                                        //         $hasStock = ($batchDetail->stock ?? 0) > 0;
                                        //         $isExpired = $batchDetail->expiry_date && Carbon::parse($batchDetail->expiry_date)->isPast();

                                        //         return $hasStock && $isExpired;
                                        //     })
                                        //     ->schema([
                                        //         \Filament\Forms\Components\Actions::make([
                                        //             \Filament\Forms\Components\Actions\Action::make('remove_expired_stock')
                                        //                 ->label('Remove Expired Stock')
                                        //                 ->icon('heroicon-o-exclamation-triangle')
                                        //                 ->color('danger')
                                        //                 ->outlined()
                                        //                 ->requiresConfirmation()
                                        //                 ->modalHeading('Remove Expired Stock')
                                        //                 ->modalDescription(function ($record) use ($batchDetail) {
                                        //                     $expiryDate = $batchDetail->expiry_date ? Carbon::parse($batchDetail->expiry_date)->format('d M Y') : 'Unknown';
                                        //                     $currentStock = $batchDetail->stock ?? 0;
                                        //                     return "This product has expired on {$expiryDate} and currently has {$currentStock} units in stock. Are you sure you want to remove all expired stock?";
                                        //                 })
                                        //                 ->modalSubmitActionLabel('Yes, Remove Stock')
                                        //                 ->action(function ($record, $action, $livewire) {
                                        //                     // dd($livewire->mountedTableActions);
                                        //     $userId = getUser(Auth::user())->id;
                                        //     $productData = $record->productDataForPc($userId);

                                        //     if ($productData) {
                                        //         // Update stock to 0 and total_stock to 0
                                        //         ProductRelationStock::where('product_relation_id', $productData->id)
                                        //             ->update([
                                        //                 'stock' => 0,
                                        //                 'total_stock' => 0
                                        //             ]);

                                        //         Notification::make()
                                        //             ->title('Expired Stock Removed')
                                        //             ->body('All expired stock has been successfully removed.')
                                        //             ->success()
                                        //             ->send();

                                        //         // Close the parent table action modal
                                        //         $livewire->mountedTableActions = null;
                                        //         $livewire->mountedTableActionData = [];
                                        //         $livewire->mountedTableActionRecord = null;
                                        //     }
                                        // })
                                        //         ])
                                        //     ])
                                    ]),

                            ];
                        }),
                    Action::make('approve')
                        ->icon('heroicon-c-wrench-screwdriver')
                        ->visible(function ($record) {
                            $user = getUser(Auth::user());
                            $approved = $record?->productDataForPc($user->id)?->pc_approval == false && empty($record?->productDataForPc($user->id)?->pc_rejection_reason);
                            return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') ||  auth()->user()->can('products_approve'));
                        })
                        ->requiresConfirmation()
                        ->action(function ($record) {
                            $record?->productData->update(['pc_approval' => true]);
                            $admins = User::role('Super Admin')->get();
                            foreach ($admins as $admin) {
                                Mail::to($admin->email)->send(new PcApprovedProduct($record, $admin, getUser(Auth::user())->name));
                            }
                        }),
                    Action::make('reject')
                        ->icon('heroicon-c-x-circle')
                        ->visible(function ($record) {
                            $user = getUser(Auth::user());
                            $approved = $record?->productDataForPc($user->id)?->pc_approval == false && empty($record?->productDataForPc($user->id)?->pc_rejection_reason);
                            return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || auth()->user()->can('products_reject'));
                        })
                        ->requiresConfirmation()
                        ->form([
                            Textarea::make('reason')
                                ->label('Reason')
                                ->required()
                                ->maxLength(255)
                                ->validationAttribute('Reason')
                                ->rules(['required', 'string', 'max:255']),
                        ])
                        ->action(function ($record, $data) {
                            $record?->productData->update(['pc_approval' => false, 'pc_rejection_reason' => $data['reason']]);
                            $admins = User::role('Super Admin')->get();
                            foreach ($admins as $admin) {
                                Mail::to($admin->email)->send(new PcRejectedProduct($record, $admin, getUser(Auth::user())->name, $data['reason']));
                            }
                        }),
                    DeleteAction::make()
                        ->label('Delete')
                        ->icon('heroicon-o-trash')
                        ->visible(function ($record) {
                            $approved = $record?->status == 'approved';
                            $user = Auth::user();
                            return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('products_delete'));
                        })
                        ->action(function ($record) {
                            // 🎯 Comprehensive data capture BEFORE deletion
                            $user = getUser(Auth::user());
                            $userId = $user->id;

                            // Load all relationships for complete data capture
                            $record->load(['category', 'brand', 'unit', 'container', 'foam', 'generic']);

                            // Get the ProductRelation that will be deleted
                            $productRelation = ProductRelation::where(['product_id' => $record->id, 'user_id' => $userId])->first();

                            if (!$productRelation) {
                                Notification::make()
                                    ->title('Product relation not found')
                                    ->body("No product relation found for {$record->name}")
                                    ->warning()
                                    ->send();
                                return;
                            }

                            // Capture all related data before deletion
                            $stockData = ProductRelationStock::where('product_relation_id', $productRelation->id)->first();
                            $priceData = ProductRelationPrice::where('product_relation_id', $productRelation->id)->first();
                            $batchData = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();
                            $productCommission = ProductCommission::where('product_relation_id', $productRelation->id)->first();

                            // 🎯 Create comprehensive human-readable activity data for deletion
                            $humanReadableData = [
                                // Basic Product Information
                                'Product Name' => $record->name ?? 'Unknown Product',
                                'Product ID' => $record->id ?? 'Unknown',
                                'SKU' => $productRelation->sku ?? 'Not Set',
                                'Deleted By PC' => $user->name ?? 'Unknown User',
                                'Action Type' => 'Product Deletion',
                                'Deletion Reason' => 'PC removed product from their inventory',

                                // Product Details
                                'Category' => $record->category?->name ?? 'Unknown',
                                'Sub Category' => $record->subcategory?->name ?? 'Unknown',
                                'Brand' => $record->brand?->name ?? 'Unknown',
                                'Unit' => $record->unit?->name ?? 'Unknown',
                                'Container' => $record->container?->name ?? 'Unknown',
                                'Dosage Form' => $record->foam?->name ?? 'Unknown',
                                'Generic Name' => $record->generic?->name ?? 'Unknown',
                                'Description' => $record->description ?? 'No description',
                                'Quantity Per Unit' => $record->quantity_per_unit ?? 'Not Set',
                                'Wholesale Pack Size' => $record->wholesale_pack_size ?? 'Not Set',

                                // Stock Information (if exists)
                                'Stock Status' => $stockData ? ($stockData->stock > 0 ? 'In Stock' : 'Out of Stock') : 'No Stock Data',
                                'Stock Management' => $stockData?->is_batch_wise_stock ? 'By Batch' : 'Simple Stock',
                                'Stock Type' => $stockData?->stock_type === 'wps' ? 'Wholesale Pack' : 'Unit',
                                'Current Stock' => $stockData?->stock ?? 'Not Set',
                                'Total Stock' => $stockData?->total_stock ?? 'Not Set',
                                'Low Stock Trigger' => $stockData?->low_stock ?? 'Not Set',
                                'Wholesale Pack Size (Stock)' => $stockData?->wholesale_pack_size ?? 'Not Set',

                                // Pricing Information (if exists)
                                'Pricing Type' => $priceData ? ($priceData->pricing_type ?? 'Not Set') : 'No Pricing Data',
                                'East Zone Fixed Price' => $priceData ? ($priceData->east_zone_price_1 ? 'RM ' . number_format($priceData->east_zone_price_1, 2) : 'Not Set') : 'No Price Data',
                                'West Zone Fixed Price' => $priceData ? ($priceData->west_zone_price_1 ? 'RM ' . number_format($priceData->west_zone_price_1, 2) : 'Not Set') : 'No Price Data',

                                // Batch Information
                                'Batch Count' => $batchData->count(),

                                // Commission Information
                                'Commission Type' => $productCommission?->commission_type ?? 'Not Set',
                                'Commission Percentage' => $productCommission?->commission_percentage ? $productCommission->commission_percentage . '%' : 'Not Set',

                                // Technical Information
                                'Product Relation ID' => $productRelation->id,
                                'User ID' => $userId,
                                'Deleted At' => now()->format('Y-m-d H:i:s'),
                            ];

                            // Add batch details if any
                            if ($batchData->count() > 0) {
                                $humanReadableData['Batch Details'] = [];
                                foreach ($batchData as $index => $batch) {
                                    $batchNum = $index + 1;
                                    $humanReadableData['Batch Details']["Batch {$batchNum}"] = [
                                        'Batch Number' => $batch->batch_name ?? 'Not Set',
                                        'Available Stock' => $batch->available_stock ?? 0,
                                        'Expiry Date' => $batch->expiry_date ?? 'Not Set',
                                    ];
                                }
                            }

                            // Add pricing details if tier pricing exists
                            if ($priceData && !empty($priceData->east_tier_price)) {
                                $humanReadableData['East Tier Pricing'] = self::formatTierStructureForLog($priceData->east_tier_price);
                            }
                            if ($priceData && !empty($priceData->west_tier_price)) {
                                $humanReadableData['West Tier Pricing'] = self::formatTierStructureForLog($priceData->west_tier_price);
                            }

                            // Add bonus pricing details if exists
                            if ($priceData && $priceData->pricing_type === 'bonus') {
                                $humanReadableData['East Bonus Pricing'] = [
                                    'Base Price' => $priceData->east_zone_price_bonus ? 'RM ' . number_format($priceData->east_zone_price_bonus, 2) : 'Not Set',
                                    'Bonus Structure' => $priceData->east_bonus_structure ?? 'Not Set',
                                ];
                                $humanReadableData['West Bonus Pricing'] = [
                                    'Base Price' => $priceData->west_zone_price_bonus ? 'RM ' . number_format($priceData->west_zone_price_bonus, 2) : 'Not Set',
                                    'Bonus Structure' => $priceData->west_bonus_structure ?? 'Not Set',
                                ];
                            }

                            // 🎯 Debug: Log deletion data
                            Log::info('Activity Log Debug - Product Deletion', [
                                'product_name' => $record->name,
                                'product_id' => $record->id,
                                'user_name' => $user->name,
                                'stock_count' => $stockData ? 1 : 0,
                                'price_count' => $priceData ? 1 : 0,
                                'batch_count' => $batchData->count(),
                                'has_stock_data' => !is_null($stockData),
                                'has_price_data' => !is_null($priceData),
                                'has_batch_data' => $batchData->count() > 0,
                            ]);

                            // 🎯 Perform the actual deletion
                            $productRelation->delete();

                            // Clear all related caches
                            \App\Services\ProductRelationCacheService::clearProductRelationStockCache($productRelation->id);
                            \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);
                            \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                            \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                            \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                            // 🎯 Log the deletion activity (no 'old' data since it's a deletion)
                            activity()
                                ->performedOn($record)
                                ->causedBy(Auth::user())
                                ->withProperties([
                                    'old' => [], // No old data for deletions
                                    'attributes' => $humanReadableData
                                ])
                                ->log("PC '{$user->name}' deleted product '" . ($record->name ?? 'Unknown Product') . "' from their inventory - " . ($stockData ? '1' : '0') . " stock records, {$humanReadableData['Batch Count']} batches removed");

                            // Send success notification
                            Notification::make()
                                ->title('Product Deleted Successfully')
                                ->body("Product '{$record->name}' has been removed from your inventory.")
                                ->success()
                                ->send();
                        })
                        ->color('gray'),

                ]),
            ])

            ->bulkActions([
                // ...
            ]);
    }

    public static function handleTierPrice($data, $record)
    {
        $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
        $westTierPriceInfo = $data['pcInfo_west'] ?? [];
        $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();
        $eastTierPrice = [];
        $westTierPrice = [];
        foreach ($eastTierPriceInfo as $key => $value) {
            $count = $key + 1;
            $eastTierPrice["east_tier_{$count}_min_quantity"] = $value['min_quantity'];
            $eastTierPrice["east_tier_{$count}_max_quantity"] = $value['max_quantity'];
            $eastTierPrice["east_tier_{$count}_base_price"] = $value['price'];
        }
        foreach ($westTierPriceInfo as $key => $value) {
            $count = $key + 1;
            $westTierPrice["west_tier_{$count}_min_quantity"] = $value['min_quantity'];
            $westTierPrice["west_tier_{$count}_max_quantity"] = $value['max_quantity'];
            $westTierPrice["west_tier_{$count}_base_price"] = $value['price'];
        }
        $productData = array_merge($data, $eastTierPrice, $westTierPrice);
        unset($productData['pcInfo_east']);
        unset($productData['pcInfo_west']);

        return $productData ?? [];
    }

    public static function getBatchesAttribute($record)
    {
        $userId = getUser(Auth::user())->id;
        $productRelationId = $record->productDataForPc($userId)->id;
        return $record->batches->where('user_id', $userId)->where('products_relation_id', $productRelationId)->map(function ($batch) {
            return [
                'batch_name' => $batch->batch_name,
                'available_stock' => $batch->available_stock,
                'expiry_date' => $batch->expiry_date,
            ];
        })->toArray();
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $userId = Auth::id();
        if (!empty(Auth::user()->parent_id)) {
            $userId = Auth::user()->parent_id;
        }
        $pcCommission = PcDetail::where('user_id', $userId)->first();
        $finalCommissionType = '';
        $finalCommission = 0;
        $finalCommissionType = $pcCommission?->commission_type ?: null;
        $finalCommission = $pcCommission?->commission_percentage ?: 0;
        return $infolist
            ->extraAttributes(['x-data' => '{ showDetails: false }'])
            ->schema(function () use ($finalCommission, $finalCommissionType, $userId) {
                return [
                    InfoGrid::make(3)->schema([
                        InfoSection::make('Product Details')
                            ->schema([
                                InfoGrid::make(2)->schema([
                                    InfoGroup::make()->schema([
                                        TextEntry::make('name')->label('Product Name'),
                                        TextEntry::make('category.name')
                                            // ->formatStateUsing(function ($state) {
                                            //     return new HtmlString("<span class='px-2 py-2 font-semibold text-green-600 border-2 border-green-300 rounded-lg shadow-sm text-md bg-green-50'>$state</span>");
                                            // })
                                            ->label('Category'),
                                        TextEntry::make('subcategory.name')
                                            ->label('Sub Category'),
                                        TextEntry::make('productData.created_at')
                                            ->formatStateUsing(function ($state, $record) {
                                                $userId = getUser(Auth::user())->id;

                                                return $record->productDataForPc($userId)->sku ?? '';
                                            })
                                            ->label('Stock Keeping Unit (SKU)'),
                                        TextEntry::make('productData')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->generic?->name ?? '';
                                            })
                                            ->label('Generic Name'),
                                        TextEntry::make('productData.created_at')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->brand?->name ?? '';
                                            })
                                            ->label('Brand'),

                                        TextEntry::make('productData')
                                            ->formatStateUsing(function ($record) {
                                                $distributors = $record->distributors
                                                    ? $record->distributors->pluck('name')->join(' | ')
                                                    : '—';
                                                return $distributors;
                                            })
                                            ->label('Distributors'),
                                        TextEntry::make('is_prescription_required')
                                            ->formatStateUsing(function ($state) {
                                                return $state ? 'Yes' : 'No';
                                            })
                                            ->label('Prescription Required'),
                                        TextEntry::make('status')
                                            ->label(new HtmlString('<span class="text-white font-bold bg-red-500 p-2 rounded-md shadow-md">Reason for Rejection</span>'))
                                            ->formatStateUsing(function ($record) {
                                                $userId = $record->owner_id;
                                                $reason = $record->productDataForPC($userId)->rejected_reason ?? '';
                                                return new HtmlString('<span class="font-bold text-red-500">' . $reason . '</span>');
                                                // return $reason;
                                            })
                                            ->visible(function ($record) {
                                                return $record->status == 'rejected';
                                            })
                                            ->columnSpanFull(),
                                        SpatieMediaLibraryImageEntry::make('mda_document')
                                            ->collection('mda-documents')
                                            ->disk('s3')
                                            ->visible(function ($record) {
                                                return (bool) $record->subcategory?->is_mda;
                                            })
                                            ->label('MDA Document'),
                                        TextEntry::make('created_at')
                                            ->formatStateUsing(function ($state) {
                                                $format = getDateTimeFormatForPc(Auth::user());
                                                $timezone = Auth::user()->timezone ?? 'UTC';
                                                return Carbon::parse($state)->timezone($timezone ?? 'UTC')->format($format);
                                            })
                                            ->label('Created At'),
                                        TextEntry::make('productData.rejected_on')
                                            ->formatStateUsing(function ($record, $state) {
                                                $rejectedOn = $record->productDataForPC($record->owner_id)?->rejected_on;
                                                $format = getDateTimeFormatForPc(Auth::user());
                                                $timezone = Auth::user()->timezone ?? 'UTC';
                                                return Carbon::parse($rejectedOn)
                                                    ->timezone($timezone ?? 'UTC')
                                                    ->format($format);
                                            })
                                            ->visible(function ($record) {
                                                return $record->status == 'rejected';
                                            })
                                            ->label('Rejected On'),

                                        InfoGroup::make()
                                            ->schema([
                                                ViewEntry::make('view_more')
                                                    ->label('')
                                                    ->view('custom-view.show-hide-button'),
                                            ])->columnSpanFull(),
                                    ])->columns(3)->columnSpanFull(),
                                ]),
                            ])->columnSpan(2),
                        InfoSection::make('Product Images')
                            ->extraAttributes(['class' => 'min-h-full'])
                            ->schema([
                                DragDropImageEntry::make('images')
                                    ->collection('product-images')
                            ])->columnSpan(1),
                    ])->columnSpanFull(),
                    InfoSection::make()

                        ->heading('Product Description')
                        ->extraAttributes(['x-show' => 'showDetails', 'x-cloak' => true])
                        ->schema([
                            InfoGroup::make()
                                ->label('Product Details')
                                ->schema([
                                    Tabs::make(label: 'Product Details')
                                        ->schema([
                                            Tab::make('Product Description')
                                                ->schema([
                                                    TextEntry::make('product_description')
                                                        ->html()
                                                        ->formatStateUsing(fn($state) => $state)
                                                        ->label(''),
                                                ]),
                                            Tab::make('Key Ingredients')
                                                ->schema([
                                                    TextEntry::make('description_ingredients')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Storage Instructions')
                                                ->schema([
                                                    TextEntry::make('description_storage_instructions')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Usage/Indication')
                                                ->schema([
                                                    TextEntry::make('description_indications')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Contradiction')
                                                ->schema([
                                                    TextEntry::make('description_contradictions')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('How to Use')
                                                ->schema([
                                                    TextEntry::make('description_how_to_use')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Safety Information/Pregnancy')
                                                ->schema([
                                                    TextEntry::make('description_safety_information')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Dosage Information')
                                                ->schema([
                                                    TextEntry::make('description_dosage')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Side Effects')
                                                ->schema([
                                                    TextEntry::make('description_side_effects')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                        ]),
                                ]),
                        ])->columnSpanFull(),

                    InfoSection::make('Stock Details')
                        ->schema([
                            TextEntry::make('productData')
                                ->formatStateUsing(function ($record) {
                                    $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();

                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);

                                    if ($stockData?->is_batch_wise_stock) {
                                        $userId = getUser(Auth::user())->id;
                                        $currentStock = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId, 'products_relation_id' => $productRelation?->id])->sum('available_stock');

                                        $lowStock = $stockData?->low_stock;
                                        if ($currentStock < $lowStock) {
                                            return new HtmlString('<span class="px-1 py-1 text-white bg-red-500 rounded-md">Low Stock</span>');
                                        }
                                        if ($currentStock < 1) {
                                            return new HtmlString('<span class="px-1 py-1 text-white bg-red-500 rounded-md">Out of Stock</span>');
                                        }
                                        return new HtmlString('<span class="px-1 py-1 text-white bg-green-500 rounded-md x-1">In Stock</span>');
                                    } else {
                                        $currentStock = $stockData?->total_stock;
                                        $lowStock = $stockData?->low_stock;
                                        // Default to "In Stock"
                                        $badgeClass = 'bg-green-500';
                                        $badgeText = 'In Stock';

                                        // Check if current stock is null or 0
                                        if (is_null($currentStock) || $currentStock == 0) {
                                            $badgeClass = 'bg-red-500';
                                            $badgeText = 'Out of Stock';
                                        } elseif (!is_null($lowStock) && $currentStock < $lowStock) {
                                            // Check if stock is below low stock threshold
                                            $badgeClass = 'bg-red-500'; // Optional: use yellow to differentiate
                                            $badgeText = 'Low Stock';
                                        } else {
                                            $badgeClass = 'bg-green-500';
                                            $badgeText = 'In Stock';
                                        }
                                        return new HtmlString('<span class="px-1 py-1 text-white ' . $badgeClass . ' rounded-md">' . $badgeText . '</span>');
                                    }
                                })
                                ->label('Stock Status'),
                            TextEntry::make('created_at')
                                ->formatStateUsing(function ($record) {
                                    $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();

                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    if ($stockData?->is_batch_wise_stock) {
                                        $userId = getUser(Auth::user())->id;
                                        $stock = $record->batches->where('user_id', $userId)->where('products_relation_id', $productRelation?->id)->sum('available_stock') ?? '-';
                                        return $stock ?? '-';
                                    } else {
                                        return ProductRelationStock::where(['product_relation_id' => $productRelation?->id])->first()?->stock ?? '-';
                                    }
                                })
                                ->default('-')
                                ->label(function ($record) {
                                    $container = $record->container->name ?? '';
                                    return "Stock By $container";
                                }),

                            TextEntry::make('unit.name')
                                ->label('Volume Unit'),
                            TextEntry::make('quantity_per_unit')
                                ->label(function ($record) {
                                    if (!empty($record->container->name)) {
                                        return "Volume by {$record->container->name}";
                                    }
                                    return 'Volume';
                                }),

                            TextEntry::make('productData')
                                ->formatStateUsing(function ($record) {
                                    $userId = getUser(Auth::user())->id;
                                    $productRelation = ($record->productDataForPc($userId));
                                    return ProductRelationStock::where(['product_relation_id' => $productRelation?->id])->first()?->low_stock ?? '-';
                                })
                                ->label(function ($record) {
                                    $container = $record->container->name ?? "";
                                    return "Low Stock Trigger Value By {$container}";
                                }),
                            TextEntry::make('productData.is_tier_pricing')
                                ->formatStateUsing(function ($record) {
                                    $user = Auth::user();
                                    $userId = $user->id;
                                    if (!empty($user->parent_id)) {
                                        $userId = $user->parent_id;
                                    }
                                    // Get user-specific date format or default
                                    $format = PcDetail::where('user_id', $userId)->value('date_format') ?? 'M d, Y';

                                    $productRelation = ($record->productDataForPc($userId));
                                    $date = ProductRelationStock::where(['product_relation_id' => $productRelation?->id])->first()?->expiry_date;
                                    return !empty($date) ? Carbon::parse($date)->format($format) : '-';
                                })
                                ->visible(function ($record) {
                                    $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();
                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    return !$stockData?->is_batch_wise_stock;
                                })
                                ->default('-')
                                ->label('Expiry Date'),
                            TextEntry::make('container.name')
                                ->label(PackagingToolTip::tooltip()),
                            TextEntry::make('foam.name')->label('Product Form'),
                            TextEntry::make('productData')
                                ->formatStateUsing(function ($state, $record) {
                                    return $record->weight ?? '';
                                })
                                ->label('Weight (gms)'),
                            TextEntry::make('productData')
                                ->visible(function ($record) {
                                    $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();
                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    return $stockData?->stock_type == 'wps';
                                })
                                ->formatStateUsing(function ($record) {
                                    $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                                    if (empty($relationData?->id)) {
                                        return "-";
                                    }

                                    $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                                    $packSize = $stockData?->wholesale_pack_size;

                                    if (empty($packSize)) {
                                        return "-";
                                    }

                                    $text = '';

                                    $text .= $packSize . ' ';

                                    $containerName = $record->container?->name;
                                    if (!empty($containerName)) {
                                        $text .= Str::plural($containerName) . ' of ';
                                    }

                                    $quantityPerUnit = $record->quantity_per_unit;
                                    $foamName = $record->foam?->name;

                                    if (!empty($quantityPerUnit) && !empty($foamName)) {
                                        $qty = $quantityPerUnit * (int) $packSize;
                                        $doses = Str::plural($foamName);
                                        $text .= "( {$qty} {$doses} ) ";
                                    }
                                    // dd($text);
                                    return $text;
                                })
                                ->label('Wholesale Pack Size'),
                            TextEntry::make('productData')
                                ->label('Stock Type')
                                ->formatStateUsing(function ($record) {
                                    $relationData = $record->productDataForPC(getUser(Auth::user())?->id);
                                    $stockData = ProductRelationStock::where('product_relation_id', $relationData?->id)->first();
                                    $text = '';
                                    if ($stockData?->stock_type == 'wps') {
                                        $text = 'Wholesale Pack';
                                    } else {
                                        $text = 'Unit';
                                    }
                                    return $text;
                                })

                        ])->columns(4),
                    InfoSection::make()
                        ->heading('By Batch')
                        ->visible(function ($record) {
                            $userId = !empty(Auth::user()->parent_id) ? Auth::user()->parent_id : Auth::id();
                            $productRelation = self::getPcData($record, $userId);
                            $stockData = self::getStockData($productRelation?->id);
                            return  $stockData?->is_batch_wise_stock == true;
                        })

                        ->schema(function ($record) {
                            $data = $record;
                            return [
                                ViewEntry::make('batches')
                                    ->view('custom-view.batch-repeatable-entry')
                                    ->viewData(['batches' => self::getBatchesAttribute($record)])

                            ];
                        }),

                    // Price Sections Container (visible only when price data exists)
                    InfoSection::make()
                        ->visible(function ($record) use ($userId) {
                            // return true;
                            return $record->productDataForPc($userId)?->price_type == 'fixed';
                        })
                        ->columnSpanFull()
                        ->schema([
                            InfoSection::make()
                                ->heading(function ($record) use ($userId) {
                                    $relationData = $record->productDataForPC(getUser(Auth::user())?->id);
                                    // dd($relationData);
                                    if (empty($relationData?->id)) {
                                        return "Fixed - ";
                                    }

                                    $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                                    $packSize = $stockData?->wholesale_pack_size;

                                    if (empty($packSize)) {
                                        return "Fixed -";
                                    }

                                    $text = '';
                                    $text .= $packSize . ' ';

                                    $containerName = $record->container?->name;
                                    if (!empty($containerName)) {
                                        $text .= $containerName . ' of ';
                                    }

                                    $quantityPerUnit = $record->quantity_per_unit;
                                    $foamName = $record->foam?->name;

                                    if (!empty($quantityPerUnit) && !empty($foamName)) {
                                        $qty = $quantityPerUnit * (int) $packSize;
                                        $doses = Str::plural($foamName);
                                        $text .= "( {$qty} {$doses} ) ";
                                    }
                                    return "Fixed - $text";
                                })
                                ->visible(function ($record) use ($userId) {
                                    return $record->productDataForPc($userId)?->price_type == 'fixed';
                                })
                                ->schema([
                                    InfoGroup::make()
                                        ->columns(4)
                                        ->schema([
                                            TextEntry::make('region')->label('Region'),
                                            TextEntry::make('price')
                                                ->label('Price by'),
                                            TextEntry::make('admin_fees')
                                                ->label('Admin Fees'),
                                            TextEntry::make('net_earnings')->label('Net Earnings'),
                                        ]),
                                    InfoGroup::make()
                                        ->columns(4)
                                        ->schema([
                                            TextEntry::make('east_malasiya')->label('East Malaysia'),
                                            TextEntry::make('productData.admin_approval')
                                                ->formatStateUsing(function ($record) use ($userId) {
                                                    $userId = getUser(Auth::user())->id;
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    return "RM " . ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->east_zone_price;
                                                })
                                                ->label(''),
                                            TextEntry::make('admin_fees')
                                                ->label(function () use ($finalCommission, $finalCommissionType) {
                                                    if ($finalCommissionType == 'percentage') {
                                                        return $finalCommission . '%';
                                                    } else {
                                                        return 'RM ' . $finalCommission;
                                                    }
                                                }),
                                            TextEntry::make('net_earnings')
                                                ->label(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    $eastZonePrice = ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->east_zone_price;
                                                    if ($finalCommissionType == 'percentage') {
                                                        $earnings = $eastZonePrice - ($eastZonePrice * $finalCommission / 100);
                                                        return "RM " . number_format($earnings, 2);
                                                    } else {
                                                        return 'RM ' . $eastZonePrice - $finalCommission;
                                                    }
                                                }),

                                            TextEntry::make('west_malasiya')->label('West Malaysia'),
                                            TextEntry::make('productData.admin_approval')
                                                ->formatStateUsing(function ($record) use ($userId) {
                                                    $userId = getUser(Auth::user())->id;
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    return "RM " . ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->west_zone_price;
                                                    // return "RM " . $record->productDataForPc($userId)?->west_zone_price;
                                                })
                                                ->label(''),
                                            TextEntry::make('west_admin_fees')
                                                ->label(function () use ($finalCommission, $finalCommissionType) {
                                                    if ($finalCommissionType == 'percentage') {
                                                        return $finalCommission . '%';
                                                    } else {
                                                        return 'RM ' . $finalCommission;
                                                    }
                                                }),
                                            TextEntry::make('west_net_earnings')
                                                ->label(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    $westZonePrice = ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->west_zone_price;
                                                    if ($finalCommissionType == 'percentage') {
                                                        $earnings = $westZonePrice - ($westZonePrice * $finalCommission / 100);
                                                        return "RM " . number_format($earnings, 2);
                                                    } else {
                                                        return 'RM ' . $westZonePrice - $finalCommission;
                                                    }
                                                }),
                                        ]),
                                ]),
                        ]),

                    // Bonus Price Section  
                    InfoSection::make()
                        ->heading(function ($record) use ($userId) {
                            $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                            if (empty($relationData?->id)) {
                                return "Bonus :";
                            }

                            $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                            $packSize = $stockData?->wholesale_pack_size;

                            if (empty($packSize)) {
                                return "Bonus: ";
                            }

                            $text = '';
                            $text .= $packSize . ' ';

                            $containerName = $record->container?->name;
                            if (!empty($containerName)) {
                                $text .= Str::plural($containerName) . ' of ';
                            }

                            $quantityPerUnit = $record->quantity_per_unit;
                            $foamName = $record->foam?->name;

                            if (!empty($quantityPerUnit) && !empty($foamName)) {
                                $qty = $quantityPerUnit * (int) $packSize;
                                $doses = Str::plural($foamName);
                                $text .= "( {$qty} {$doses} ) ";
                            }
                            return "Bonus - $text";
                        })
                        ->visible(function ($record) use ($userId) {
                            return $record->productDataForPc($userId)?->price_type == 'bonus';
                        })
                        ->schema([
                            InfoGrid::make(2) // Change from 3 to 2 to ensure two sections side by side
                                ->extraAttributes(['class' => 'm-0'])

                                ->schema([
                                    InfoSection::make()
                                        ->heading('East Malaysia')
                                        ->schema([
                                            InfoGroup::make()->schema([
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($userId) {
                                                        $productRelation = self::getPcData($record, $userId);
                                                        $eastZonePrice = self::getPriceData($productRelation?->id)?->east_zone_price;
                                                        return "RM " . number_format($eastZonePrice, 2);
                                                    })
                                                    ->label('Base Price'),
                                                TextEntry::make('productData')
                                                    ->label('Admin Fees')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return  $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('Net Earnings')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                        $productRelation = ($record->productDataForPc($userId));
                                                        $eastZonePrice = self::getPriceData($productRelation?->id)?->east_zone_price;
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $eastZonePrice - ($eastZonePrice * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $eastZonePrice - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                            ])->columns(3),
                                            InfoGroup::make()->schema([
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'bg-gray-200 p-3 pt-4 rounded-md'])
                                                    ->schema([
                                                        TextEntry::make('quantity')
                                                            ->label('Quantity'),
                                                        TextEntry::make('bonus_quantity')
                                                            ->label('Bonus Quantity'),
                                                    ])->columns(2),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'ml-4'])
                                                    ->schema([
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_1_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_1_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_2_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_2_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_3_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_3_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                    ])->columns(2)
                                            ])->columnSpanFull(3),
                                        ])->columnSpan(1),

                                    InfoSection::make()
                                        ->heading('West Malaysia')
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            return [
                                                InfoGroup::make()->schema([
                                                    TextEntry::make('productData')
                                                        ->formatStateUsing(function ($record) use ($userId) {
                                                            $productRelation = self::getPcData($record, $userId);
                                                            $westZonePrice = self::getPriceData($productRelation?->id)?->west_zone_price;
                                                            return 'RM ' . number_format($westZonePrice, 2);
                                                        })
                                                        ->label('Base Price'),
                                                    TextEntry::make('productData')
                                                        ->label('Admin Fees')
                                                        ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                            return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                        }),
                                                    TextEntry::make('productData')
                                                        ->label('Net Earnings')
                                                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                            $productRelation = self::getPcData($record, $userId);
                                                            $westZonePrice = self::getPriceData($productRelation?->id)?->west_zone_price;
                                                            if ($finalCommissionType == 'percentage') {
                                                                $earnings = $westZonePrice - ($westZonePrice * $finalCommission / 100);
                                                            } else {
                                                                $earnings = $westZonePrice - $finalCommission;
                                                            }
                                                            return "RM " . number_format($earnings, 2);
                                                        }),
                                                ])->columns(3),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'bg-gray-200 p-3 pt-4 rounded-md'])
                                                    ->schema([
                                                        TextEntry::make('quantity')
                                                            ->label('Quantity'),
                                                        TextEntry::make('bonus_quantity')
                                                            ->label('Bonus Quantity'),
                                                    ])->columns(2),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'ml-4'])
                                                    ->schema([
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_1_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_1_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_2_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_2_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_3_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_3_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                    ])->columns(2)
                                            ];
                                        })->columnSpan(1),
                                ])->columnSpanFull(),
                        ]),
                    InfoSection::make()
                        ->heading(function ($record) use ($userId) {
                            $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                            if (empty($relationData?->id)) {
                                return "Tier - ";
                            }

                            $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                            $packSize = $stockData?->wholesale_pack_size;

                            if (empty($packSize)) {
                                return "Tier - ";
                            }

                            $text = '';
                            $text .= $packSize . ' ';

                            $containerName = $record->container?->name;
                            if (!empty($containerName)) {
                                $text .= Str::plural($containerName) . ' of ';
                            }

                            $quantityPerUnit = $record->quantity_per_unit;
                            $foamName = $record->foam?->name;

                            if (!empty($quantityPerUnit) && !empty($foamName)) {
                                $qty = $quantityPerUnit * (int) $packSize;
                                $doses = Str::plural($foamName);
                                $text .= "( {$qty} {$doses} ) ";
                            }
                            return "Tier - $text";
                        })
                        ->visible(function ($record) use ($userId) {
                            return $record->productDataForPc($userId)?->price_type == 'tier';
                        })
                        ->schema([
                            InfoSection::make()
                                ->heading(function ($record) use ($userId) {
                                    return new HtmlString("
                                    <div class='flex flex-col justify-center h-full'>
                                        <div class='flex flex-row items-center justify-between w-full'>
                                            <span class='font-bold'>East Malaysia</span>
                                            <span class='font-normal text-right text-gray-400'>{$record?->quantity_per_unit} {$record?->foam?->name} in {$record?->unit?->name}</span>
                                        </div>
                                    </div>
                                ");
                                })
                                ->schema([
                                    InfoGroup::make([
                                        TextEntry::make('type')->label('Type'),
                                        TextEntry::make('quantity')->label('Quantity'),
                                        TextEntry::make('price')->label('Price per Unit'),
                                        TextEntry::make('admin_fees')->label('Admin Fees'),
                                        TextEntry::make('net_earnings')->label('Net Earnings'),
                                    ])->columns(5),
                                    InfoGroup::make()
                                        ->columns(5)
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            $productRelation = self::getPcData($record, $userId);
                                            $tierData = self::getPriceData($productRelation?->id);
                                            if (empty($tierData)) {
                                                return [];
                                            }
                                            return [
                                                TextEntry::make('tier_1')->label('Tier 1'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->east_tier_1_max_quantity) || $tierData?->east_tier_1_max_quantity !== 0 ? $tierData->east_tier_1_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData?->east_tier_1_min_quantity . ' - ' . $tierData?->east_tier_1_max_quantity
                                                            : $tierData?->east_tier_1_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_1_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_1_base_price - ($tierData?->east_tier_1_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_1_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_2')->label('Tier 2')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->east_tier_2_max_quantity) || $tierData?->east_tier_2_max_quantity !== 0 ? $tierData?->east_tier_2_max_quantity : null;

                                                        return $tierData && $maxQty !== null
                                                            ? $tierData?->east_tier_2_min_quantity . ' - ' . $tierData?->east_tier_2_max_quantity
                                                            : $tierData?->east_tier_2_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_2_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_2_base_price - ($tierData?->east_tier_2_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_2_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_3')
                                                    ->visible(function () use ($tierData) {

                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->label('Tier 3'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {

                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity .  " and above";
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_3_base_price;
                                                    })
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType, $tierData) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_3_base_price - ($tierData?->east_tier_3_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_3_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    })
                                            ];
                                        })
                                ]),
                            InfoSection::make()
                                ->heading(function ($record) {
                                    return new HtmlString("
                                    <div class='flex flex-col justify-center h-full'>
                                        <div class='flex flex-row items-center justify-between w-full'>
                                            <span class='font-bold'>West Malaysia</span>
                                            <span class='font-normal text-right text-gray-400'>{$record->quantity_per_unit} {$record->foam?->name} in {$record->unit?->name}</span>
                                        </div>
                                    </div>
                                ");
                                })
                                ->schema([
                                    InfoGroup::make([
                                        TextEntry::make('type')->label('Type'),
                                        TextEntry::make('quantity')->label('Quantity'),
                                        TextEntry::make('price')->label('Price per Unit'),
                                        TextEntry::make('admin_fees')->label('Admin Fees'),
                                        TextEntry::make('net_earnings')->label('Net Earnings'),
                                    ])->columns(5),
                                    InfoGroup::make()
                                        ->columns(5)
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            $productRelation = self::getPcData($record, $userId);
                                            $tierData = self::getPriceData($productRelation->id);
                                            if (empty($tierData)) {
                                                return [];
                                            }
                                            return [
                                                TextEntry::make('tier_1')->label('Tier 1'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->west_tier_1_max_quantity) || $tierData?->west_tier_1_max_quantity !== 0 ? $tierData->west_tier_1_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData->west_tier_1_min_quantity . ' - ' . $tierData->west_tier_1_max_quantity
                                                            : $tierData->west_tier_1_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_1_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_1_base_price - ($tierData?->east_tier_1_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_1_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_2')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->label('Tier 2'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->west_tier_2_max_quantity) || $tierData?->west_tier_2_max_quantity !== 0 ? $tierData->west_tier_2_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData->west_tier_2_min_quantity . ' - ' . $tierData->west_tier_2_max_quantity
                                                            : $tierData->west_tier_2_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_2_base_price;
                                                    })
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_2_base_price - ($tierData?->west_tier_2_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_2_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_3')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('Tier 3'),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity . " and above";
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_3_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_3_base_price - ($tierData?->west_tier_3_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_3_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    })
                                            ];
                                        })
                                ])
                        ])
                ];
            });
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            NormalProducts::class,
            BatchWiseProducts::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit-product'),
            'create-new' => Pages\CreateNewProduct::route('/create-product/{search?}'),
            'create-existing' => Pages\CreateNewProductFromExistingProduct::route('/create-existing/{record}/'),
            'product-bulk-stock-update' => Pages\ProductBulkStockUpdate::route('/product-bulk-stock-update'),
            'review' => Pages\ProductReview::route('/review'),
            'view' => Pages\ViewProduct::route('/{record}/view'),
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(url()->previous()),
            $this->addProductAction(),
            $this->bulkStockUpdateAction(),
        ];
    }

    public function addProductAction(): Action
    {
        return Action::make('add_product')
            ->icon('heroicon-o-plus')

            ->outlined()
            ->label('Pending Approval');
    }

    public function bulkStockUpdateAction(): Action
    {
        return Action::make('bulk_stock_update')
            ->label('Bulk Stock Update');
    }

    public static function getEditPriceOptions($record)
    {
        return [
            'fixed' => new HtmlString('Fixed &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sets a fixed price that does not vary with quantity or duration.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            '),

            'bonus' => new HtmlString('Bonus &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            
            
            '),

            'tier' => new HtmlString('Tier &nbsp;
            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Applies different prices based on quantity purchased — more units may cost less per unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                            </svg>
            '),
        ];
    }

    public static function getFixedPriceSchema($record, $finalCommission, $finalCommissionType, $productCommission)
    {

        return [
            Group::make()
                ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                ->schema([
                    Placeholder::make('Region'),
                    Placeholder::make('Price')->label('Price by'),
                    Placeholder::make('Admin Fees'),
                    Placeholder::make('Net Earnings'),
                ])->columns(4),
            Group::make()->schema([
                Placeholder::make('East Malaysia')
                    ->label("")
                    ->content("East Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('east_zone_price_1')
                    ->validationAttribute('east zone price')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric', 'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->formatStateUsing(function ($record) {

                        return ProductRelationPrice::where('product_relation_id', $record->productDataForPc(getUser(Auth::id()))?->id)->first()?->east_zone_price;
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission, $productCommission) {
                        if ($productCommission) {
                            $finalCommissionType = $productCommission?->east_fixed_cummission_type;
                            $finalCommission = $productCommission?->east_fixed_cummission_value;
                        }
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission, $productCommission) {
                        if ($productCommission) {
                            $finalCommissionType = $productCommission?->east_fixed_cummission_type;
                            $finalCommission = $productCommission?->east_fixed_cummission_value;
                        }
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('east_zone_price_1') / 100;
                            $earning = (float)$get('east_zone_price_1') - (float)$commission;
                            return 'RM ' . number_format($earning, 2);
                        } else {
                            $earning = (float)$get('east_zone_price_1') - (float)$finalCommission;
                            return 'RM ' . number_format($earning, 2);
                        }

                        return 'RM15';
                    }),

                Placeholder::make('West Malaysia')
                    ->label("")
                    ->content("West Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('west_zone_price_1')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric',  'gt:0', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->validationMessages([
                        'gt' => 'The west zone price must be greater than 0.',
                    ])
                    ->validationAttribute('west zone price')
                    ->formatStateUsing(function ($record) {
                        return (ProductRelationPrice::where('product_relation_id', $record->productDataForPc(getUser(Auth::id()))?->id)->first()?->west_zone_price);
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission, $productCommission) {
                        if ($productCommission) {
                            $finalCommissionType = $productCommission?->west_fixed_cummission_type;
                            $finalCommission = $productCommission?->west_fixed_cummission_value;
                        }
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission, $productCommission) {
                        if ($productCommission) {
                            $finalCommissionType = $productCommission?->west_fixed_cummission_type;
                            $finalCommission = $productCommission?->west_fixed_cummission_value;
                        }
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('west_zone_price_1') / 100;
                            $earning = (float)$get('west_zone_price_1') - (float)$commission;
                            return 'RM ' . number_format((float)$earning, 2);
                        } else {
                            $earning = (float)$get('west_zone_price_1') - $finalCommission;

                            return 'RM ' . number_format((float)$earning, 2);
                        }

                        return 'RM15';
                    }),
            ])->columns(4),
        ];
    }

    public static function getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)
    {
        return [
            Group::make()
                ->schema([
                    Group::make()
                        ->schema([
                            TextInput::make('east_zone_price_bonus')
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_zone_price;
                                })
                                ->rules(function (Get $get) {
                                    $priceType = $get('price_type');
                                    if ($priceType == 'bonus') {
                                        return ['required', 'numeric', 'gt:0', 'price'];
                                    }
                                    return ['numeric', 'price'];
                                })
                                ->placeholder('Enter Price')
                                ->validationAttribute('east bonus price')
                                ->calculateNetEarnings(
                                    commission: $finalCommission,
                                    commissionType: $finalCommissionType,
                                    fieldId: 'mountedTableActionsData.0.east_bonus_net_earnings',
                                    currentField: 'mountedTableActionsData.0.east_bonus_base_price',
                                )
                                ->prefix('RM')
                                ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_admin_fees')
                                ->label('Admin Fees')
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productCommission) {

                                    if ($productCommission) {
                                        $finalCommissionType = $productCommission?->east_bonus_1_cummission_type;
                                        $finalCommission = $productCommission?->east_bonus_1_cummission_value;
                                    }
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission;
                                    }

                                    return $finalCommission;
                                })
                                ->disabled()
                                ->prefix(function () use ($finalCommissionType, $productCommission) {
                                    if ($productCommission) {
                                        $finalCommissionType = $productCommission?->east_bonus_1_cummission_type;
                                    }
                                    if ($finalCommissionType == 'percentage') {
                                        return '%';
                                    }

                                    return 'RM';
                                }),
                            TextInput::make('east_bonus_net_earnings')
                                ->disabled()
                                ->prefix('RM')
                                ->reactive()
                                ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData, $productCommission) {
                                    if ($productCommission) {
                                        $finalCommissionType = $productCommission?->east_bonus_1_cummission_type;
                                        $finalCommission = $productCommission?->east_bonus_1_cummission_value;
                                    }
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $productRelationData?->east_zone_price / 100;
                                        $earning = $productRelationData?->east_zone_price - $commission;
                                    } else {
                                        $earning = $productRelationData?->east_zone_price - $finalCommission;
                                    }

                                    return $earning;
                                })

                                ->label('Net Earnings'),
                        ])
                        ->columns(3),
                    Group::make()
                        ->columns(2)
                        ->schema([
                            TextInput::make('east_bonus_1_quantity')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity')
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', 'max:999999', 'integer', 'gt:0'];
                                    }
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity;
                                })
                                ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_1_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 1 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_1_quantity') ?: 0;
                                    if ($get('price_type') == 'bonus') {
                                        return ['required', "max:$max", 'integer', 'gt:0'];
                                    }
                                    return ['nullable', "max:$max", 'integer', 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_1_quantity_value;
                                })
                                ->label(new HtmlString('Bonus Qty. <span style="color:red;">*</span>')),
                            TextInput::make('east_bonus_2_quantity')
                                ->reactive()
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity')
                                ->rules(['max:999999', 'integer', 'gt:0'])
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_2_quantity_value')
                                ->numeric()
                                ->validationAttribute('east bonus 2 quantity value')
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_2_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_2_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                            TextInput::make('east_bonus_3_quantity')
                                ->validationAttribute('east bonus 3 quantity')
                                ->rules(['integer', 'max:999999', 'gt:0'])
                                ->numeric()
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity;
                                })
                                ->label('Quantity'),
                            TextInput::make('east_bonus_3_quantity_value')
                                ->validationAttribute('east bonus 3 quantity value')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $max = $get('east_bonus_3_quantity') ?: 0;
                                    return ['integer', "max:$max", 'gt:0'];
                                })
                                ->formatStateUsing(function ($record) use ($productRelationData) {
                                    return $productRelationData?->east_bonus_3_quantity_value;
                                })
                                ->label('Bonus Qty.'),
                        ]),
                ]),

        ];
    }

    public static function getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)
    {
        return [
            Group::make()

                ->schema([
                    TextInput::make('west_zone_price_bonus')
                        ->placeholder('Enter Price')
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_zone_price;
                        })
                        ->validationAttribute('west zone price')
                        ->calculateNetEarnings(
                            commission: $finalCommission,
                            commissionType: $finalCommissionType,
                            fieldId: 'mountedTableActionsData.0.west_bonus_net_earnings',
                            currentField: 'mountedTableActionsData.0.west_bonus_base_price',
                        )
                        ->extraAttributes(fn() => self::numericValueValidationRule())
                        ->prefix('RM')
                        ->rules(function (Get $get) {
                            if ($get('price_type') === 'bonus') {
                                return ['required', 'numeric', 'gt:0', 'price'];
                            }
                            return ['nullable', 'numeric', 'gt:0', 'price'];
                        })
                        ->validationAttribute('west zone bonus price')
                        ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_admin_fees')
                        ->label('Admin Fees')
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productCommission) {
                            if ($productCommission) {
                                $finalCommissionType = $productCommission?->west_bonus_1_cummission_type;
                                $finalCommission = $productCommission?->west_bonus_1_cummission_value;
                            }
                            if ($finalCommissionType == 'percentage') {
                                return $finalCommission;
                            }

                            return $finalCommission;
                        })
                        ->disabled()
                        ->prefix(function () use ($finalCommissionType, $productCommission) {
                            if ($productCommission) {
                                $finalCommissionType = $productCommission?->west_bonus_1_cummission_type;
                            }
                            if ($finalCommissionType == 'percentage') {
                                return '%';
                            }

                            return 'RM';
                        }),
                    TextInput::make('west_bonus_net_earnings')
                        ->disabled()
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $productRelationData, $productCommission) {
                            if ($productCommission) {
                                $finalCommissionType = $productCommission?->west_bonus_1_cummission_type;
                                $finalCommission = $productCommission?->west_bonus_1_cummission_value;
                            }
                            if ($finalCommissionType == 'percentage') {
                                $commission = $finalCommission * $productRelationData?->west_zone_price / 100;
                                $earning = $productRelationData?->west_zone_price - $commission;
                            } else {
                                $earning = $productRelationData?->west_zone_price - $finalCommission;
                            }

                            return $earning;
                        })
                        ->prefix('RM')
                        ->reactive()
                        ->label('Net Earnings'),
                ])
                ->columns(3),
            Group::make()
                ->columns(2)
                ->schema([
                    TextInput::make('west_bonus_1_quantity')
                        ->validationAttribute('west bonus 1 quantity')
                        ->numeric()
                        ->rules(['required', 'max:999999', 'integer', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity;
                        })
                        ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_1_quantity_value')
                        ->validationAttribute('west bonus 1 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_1_quantity') ?: 0;
                            return ['required', 'integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_1_quantity_value;
                        })
                        ->label(new HtmlString('Quantity Qty. <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_2_quantity')
                        ->validationAttribute('west bonus 2 quantity')
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_2_quantity_value')
                        ->validationAttribute('west bonus 2 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_2_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_2_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                    TextInput::make('west_bonus_3_quantity')
                        ->validationAttribute('west bonus 3 quantity')
                        ->numeric()
                        ->rules(['integer', 'max:999999', 'gt:0'])
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_3_quantity_value')
                        ->validationAttribute('west bonus 3 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_3_quantity') ?: 0;
                            return ['integer', "max:$max", 'gt:0'];
                        })
                        ->formatStateUsing(function ($record) use ($productRelationData) {
                            return $productRelationData?->west_bonus_3_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                ]),
        ];
    }

    public static function getEastMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType, $productCommission): array
    {
        return [
            Section::make()
                ->heading('East Malaysia')
                ->schema([
                    Group::make()
                        ->schema([
                            Placeholder::make('type'),
                            Placeholder::make('min_qty'),
                            Placeholder::make('max_qty'),
                            Placeholder::make('price')->label('Price per Unit'),
                            Placeholder::make('admin_fees'),
                            Placeholder::make('net_earnings'),
                        ])->columns(6),
                    Group::make()
                        ->schema([
                            Placeholder::make('tier_1')
                                ->label('')
                                ->content(fn() => new HtmlString('<div style="display: flex; align-items: center; margin-top: 10px;">Tier 1</div>')),

                            TextInput::make('east_tier_1_min_quantity')
                                // ->extraAttributes(['class' => 'mt-4'])
                                // ->label(new HtmlString('<span style="margin-top:30px;">Min. Qty. </span>'))
                                ->numeric()
                                ->validationAttribute('east tier 1 min quantity')
                                ->readOnly()
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'numeric', 'max:999999'];
                                    }
                                    return [];
                                })
                                ->formatStateUsing(function ($record) {
                                    return !empty($record->productData?->east_tier_1_min_quantity) ? $record->productData?->east_tier_1_min_quantity : 1;
                                })
                                ->label(''),
                            TextInput::make('east_tier_1_max_quantity')
                                // ->live(debounce: 500)
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $nextqty = (int) $get('east_tier_1_max_quantity') + 1;
                                    $set('east_tier_2_min_quantity', $nextqty);
                                })
                                ->numeric()
                                ->validationAttribute('east tier 1 max quntity')

                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_1_min_quantity');
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'numeric', "gt:$max"];
                                    }
                                    return ['numeric', "get:$max"];
                                })
                                ->formatStateUsing(function ($record) {
                                    return $record->productData?->east_tier_1_max_quantity;
                                })
                                ->label(''),
                            TextInput::make('east_tier_1_base_price')->label('')
                                ->numeric()
                                ->validationAttribute('east tier 1 base price')
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'price', 'numeric'];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_1_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_1_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_1_net_earnings', number_format($netEarnings, 2));
                                    $set('east_tier_1_base_price', $get('east_tier_1_base_price'));
                                })
                                ->live(debounce: 500),
                            Placeholder::make('east_tier_1_admin_fees')
                                ->reactive()
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_1_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($record, $finalCommission, $finalCommissionType) {
                                    $basePrice = $get('east_tier_1_base_price') ?? $record->productData?->east_tier_1_base_price;

                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $earning = $basePrice - $finalCommission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),

                            Placeholder::make('tier_2')
                                ->content(fn() => "Tier 2")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->label(""),
                            TextInput::make('east_tier_2_min_quantity')
                                ->numeric()
                                ->validationAttribute('east tier 2 min quantity')
                                ->rules(['numeric', 'max:999999'])
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_min_quantity ?? null;
                                })
                                ->label(''),
                            TextInput::make('east_tier_2_max_quantity')
                                ->numeric()
                                ->validationAttribute('east tier 2 max quantity')
                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_2_min_quantity');
                                    return ['numeric', "gt:$max"];
                                })
                                ->live(onBlur: true)
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $nextVal = $get('east_tier_2_max_quantity') + 1;
                                    $set('east_tier_3_min_quantity', $nextVal);
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_max_quantity ?? null;
                                })
                                ->label(''),
                            TextInput::make('east_tier_2_base_price')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $min = $get('east_tier_2_min_quantity');
                                    $max = $get('east_tier_2_max_quantity');
                                    if ($get('price_type') == 'tier' && (isset($min) || isset($max))) {
                                        return ['numeric', "required", "price"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_2_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_2_net_earnings', number_format($netEarnings, 2));
                                })
                                ->label('')
                                ->live(onBlur: true),
                            Placeholder::make('east_tier_2_admin_fees')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_2_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                    $basePrice = $record->productData?->east_tier_2_base_price ?? $get('east_tier_2_base_price');
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $earning = $basePrice - $finalCommission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),

                            Placeholder::make('tier_3')
                                ->content(fn() => "Tier 3")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->label(""),
                            TextInput::make('east_tier_3_min_quantity')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_2_max_quantity');
                                    if ($get('price_type') == 'tier') {
                                        return ['numeric', "required", "gt:$max"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_3_min_quantity ?? null;
                                })
                                ->label(''),
                            Placeholder::make('east_tier_3_max_quantity')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content('and above'),
                            TextInput::make('east_tier_3_base_price')
                                ->numeric()
                                ->validationAttribute('east tier 3 base price')
                                ->rules(function (Get $get) {
                                    $min = $get('east_tier_3_min_quantity');
                                    $max = $get('east_tier_3_max_quantity');
                                    if ($get('price_type') == 'tier' && (isset($min) || isset($max))) {
                                        return ['numeric', "required", "price"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_3_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_3_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_3_net_earnings', $netEarnings);
                                })
                                ->label('')
                                ->live()
                                ->debounce('500ms'),
                            Placeholder::make('east_tier_3_admin_fees')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_3_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                    $basePrice = $record->productData?->east_tier_3_base_price ?? $get('east_tier_3_base_price');
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $earning = $basePrice - $finalCommission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),
                        ])->columns(6),
                ]),

            self::getWestMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType),
        ];
    }

    public static function getWestMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType): Component
    {
        return
            Section::make()
            ->heading('West Malaysia')
            ->schema([
                Group::make()
                    ->schema([
                        Placeholder::make('type'),
                        Placeholder::make('min_qty'),
                        Placeholder::make('max_qty'),
                        Placeholder::make('price')->label('Price per Unit'),
                        Placeholder::make('admin_fees'),
                        Placeholder::make('net_earnings'),
                    ])->columns(6),
                Group::make()
                    ->schema([
                        Placeholder::make('tier_1')
                            ->content(fn() => "Tier 1")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_1_min_quantity')
                            ->numeric()
                            ->validationAttribute('west tier 1 min quantity')
                            ->readOnly()
                            ->rules(['numeric', 'max:999999'])
                            ->formatStateUsing(function () use ($record) {
                                return !empty($record->productData?->west_tier_1_min_quantity) ? $record->productData?->west_tier_1_min_quantity : 1;
                            })
                            ->label(''),
                        TextInput::make('west_tier_1_max_quantity')
                            ->numeric()
                            ->validationAttribute('west tier 1 max quantity')
                            ->rules(function (Get $get) {
                                if ($get('price_type') == 'tier' && $get('west_tier_1_min_quantity') > 0) {
                                    return ['numeric', 'max:999999', 'min:' . $get('west_tier_1_min_quantity')];
                                } elseif ($get('price_type') == 'tier') {
                                    return ['numeric', 'max:999999', 'required'];
                                }
                                return ['numeric', 'max:999999'];
                            })
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set) {
                                $nextVal = $get('west_tier_1_max_quantity') + 1;
                                $set('west_tier_2_min_quantity', $nextVal);
                            })
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_1_max_quantity ?? null;
                            })
                            ->label(''),
                        TextInput::make('west_tier_1_base_price')
                            ->numeric()
                            ->validationAttribute('west tier 1 base price')
                            ->rules(function (Get $get) {
                                if ($get('price_type') == 'tier') {
                                    return ['numeric', 'max:999999', 'price', 'required'];
                                }
                                return ['numeric', 'max:999999'];
                            })
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_1_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }

                                $set('west_tier_1_admin_fees', $adminFees);

                                $commission = ($finalCommissionType == 'percentage')
                                    ? ($finalCommission * $state / 100)
                                    : ($finalCommission * $state);

                                $netEarnings = $state - $commission;
                                $set('west_tier_1_net_earnings', number_format($netEarnings, 2));
                            })
                            ->label('')
                            ->live(onBlur: true),
                        Placeholder::make('west_tier_1_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('east_tier_1_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_1_base_price ?? $get('west_tier_1_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $earning = $basePrice - $finalCommission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),

                        Placeholder::make('tier_2')
                            ->content(fn() => "Tier 2")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_2_min_quantity')
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_min_quantity ?? null;
                            })
                            ->label(''),
                        TextInput::make('west_tier_2_max_quantity')
                            ->live(onBlur: true)
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_max_quantity ?? null;
                            })
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set) {
                                $nextVal = !is_null($get('west_tier_2_max_quantity')) ? $get('west_tier_2_max_quantity') + 1 : null;
                                $set('west_tier_3_min_quantity', $nextVal);
                            })
                            ->label(''),
                        TextInput::make('west_tier_2_base_price')->label('')
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }
                            })
                            ->live(onBlur: true),
                        Placeholder::make('west_tier_2_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('west_tier_2_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_2_base_price ?? $get('west_tier_2_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $earning = $basePrice - $finalCommission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),

                        Placeholder::make('tier_3')
                            ->content(fn() => "Tier 3")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_3_min_quantity')
                            ->formatStateUsing(function (Get $get) use ($record) {

                                return !is_null($get('west_tier_2_max_quantity')) ? $get('west_tier_2_max_quantity') + 1 : null ?? $record->productData?->west_tier_3_min_quantity  ?? null;
                            })

                            ->label(''),
                        Placeholder::make('west_tier_3_max_quantity')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content('and above'),
                        TextInput::make('west_tier_3_base_price')->label('')
                            ->reactive()
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_3_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }
                            })
                            ->live(),
                        Placeholder::make('west_tier_3_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('west_tier_3_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_3_base_price ?? $get('west_tier_3_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $earning = $basePrice - $finalCommission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),
                    ])->columns(6),
            ]);
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }

    public static function handleOtherPriceTypes($priceType, $record)
    {
        // $relation = ProductRelation::where(['product_id' => $record->id, 'user_id' => Auth::id()])->first();

        $nullBonusArr = [
            // 'east_zone_price' => null,
            // 'west_zone_price' => null,
            'east_bonus_1_quantity' => null,
            'east_bonus_1_quantity_value' => null,
            'east_bonus_2_quantity' => null,
            'east_bonus_2_quantity_value' => null,
            'east_bonus_3_quantity' => null,
            'east_bonus_3_quantity_value' => null,

            'west_bonus_1_quantity' => null,
            'west_bonus_1_quantity_value' => null,
            'west_bonus_2_quantity' => null,
            'west_bonus_2_quantity_value' => null,
            'west_bonus_3_quantity' => null,
            'west_bonus_3_quantity_value' => null,
        ];

        $nullTierArr = [
            'east_tier_1_min_quantity' => null,
            'east_tier_1_max_quantity' => null,
            'east_tier_1_base_price' => null,
            'east_tier_2_min_quantity' => null,
            'east_tier_2_max_quantity' => null,
            'east_tier_2_base_price' => null,
            'east_tier_3_min_quantity' => null,
            'east_tier_3_max_quantity' => null,
            'east_tier_3_base_price' => null,
            'west_tier_1_min_quantity' => null,
            'west_tier_1_max_quantity' => null,
            'west_tier_1_base_price' => null,
            'west_tier_2_min_quantity' => null,
            'west_tier_2_max_quantity' => null,
            'west_tier_2_base_price' => null,
            'west_tier_3_min_quantity' => null,
            'west_tier_3_max_quantity' => null,
            'west_tier_3_base_price' => null,
        ];

        $nullFixedArray = [
            'east_zone_price' => null,
            'west_zone_price' => null,
        ];
        $record->productData->update(array_merge($nullBonusArr, $nullTierArr, $nullFixedArray));
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {
        if ($finalCommissionType == 'percentage') {
            $commission = $finalCommission * $price / 100;
            $earning = $price - $commission;
        } else {
            $earning = $price - $finalCommission;
        }

        return number_format($earning, 2);
    }

    public static function tableTierPrice($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission)
    {
        // dd($productCommission);
        return [
            Group::make()
                // ->visible(fn(Get $get) => $get('productData.price_type') === 'tier')
                ->schema(function ($get) use ($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission) {
                    return [
                        Section::make('East Malaysia')->schema([
                            TableRepeater::make('pcInfo_east')
                                ->live()
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The east zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_east')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->maxItems(3)
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData, $productCommission) {
                                    Cache::forget('pcInfo_east');
                                    $tiers = [];
                                    if ($productRelationData?->east_tier_1_min_quantity !== null) {
                                        $tier1MaxQty = $productRelationData->east_tier_1_max_quantity == 0 ? null : $productRelationData->east_tier_1_max_quantity;
                                        $adminFees1 = !empty($productCommission->east_tier_1_cummission_value) ? $productCommission?->east_tier_1_cummission_value : $finalCommission;
                                        $finalCommissionType1 = !empty($productCommission->east_tier_1_cummission_type) ? $productCommission?->east_tier_1_cummission_type : $finalCommissionType;
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->east_tier_1_min_quantity ?? null,
                                            'max_quantity' => $tier1MaxQty,
                                            'price' => $productRelationData?->east_tier_1_base_price ?? null,
                                            'admin_fees' => $adminFees1,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_1_base_price, $adminFees1, $finalCommissionType1),
                                        ];
                                    }

                                    $adminFees2 = !empty($productCommission->east_tier_2_cummission_value) ? $productCommission?->east_tier_2_cummission_value : $finalCommission;
                                    if ($productRelationData?->east_tier_2_min_quantity != null) {
                                        $tier2Max = $productRelationData->east_tier_2_max_quantity == 0 ? null : $productRelationData->east_tier_2_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->east_tier_2_min_quantity ?? null,
                                            'max_quantity' => $tier2Max,
                                            'price' => $productRelationData?->east_tier_2_base_price ?? null,
                                            'admin_fees' => $adminFees2,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }
                                    $adminFees3 = !empty($productCommission->east_tier_3_cummission_value) ? $productCommission?->east_tier_3_cummission_value : $finalCommission;
                                    if ($productRelationData?->east_tier_3_min_quantity != null) {
                                        $tier3MaxQty = $productRelationData->east_tier_3_max_quantity == 0 ? null : $productRelationData->east_tier_3_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->east_tier_3_min_quantity ?? null,
                                            'max_quantity' => $tier3MaxQty,
                                            'price' => $productRelationData?->east_tier_3_base_price ?? null,
                                            'admin_fees' => $adminFees3,
                                            'net_earnings' => self::netEarnings($productRelationData?->east_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }
                                    // dd($tiers);
                                    return $tiers;
                                })
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();

                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            $set('pcInfo_east', $updatedTiers);
                                        }
                                    });
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->placeholder('and above')
                                        ->numeric()
                                        ->step(1) // Ensure whole numbers only
                                        ->live(onBlur: true) // Capture on blur to ensure complete value
                                        ->dehydrateStateUsing(fn($state) => (int)$state) // Ensure integer when saving
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->afterStateUpdated(function ($state, Set $set, Get $get, $component) use ($finalCommissionType) {
                                            $maxQty = (int)$state;

                                            // Get the row path and extract the correct key
                                            $rowPath = $component->getStatePath();
                                            // Extract the UUID of the repeater item
                                            $pathParts = explode('.', $rowPath);
                                            $rowKey = $pathParts[count($pathParts) - 2]; // Get the parent UUID

                                            $tiers = $get('../../pcInfo_east');

                                            // Find the next tier to update its min_quantity
                                            $tierKeys = array_keys($tiers);
                                            $currentKeyIndex = array_search($rowKey, $tierKeys);

                                            // If there's a next tier, update ONLY its min_quantity
                                            if ($currentKeyIndex !== false && isset($tierKeys[$currentKeyIndex + 1])) {

                                                $nextKey = $tierKeys[$currentKeyIndex + 1];
                                                // Direct path to the next tier's min_quantity
                                                $set("../../pcInfo_east.{$nextKey}.min_quantity", $maxQty + 1);
                                                if (!empty($tiers[1])) {
                                                    $price = (float)$tiers[1]['price'];
                                                    $adminFees = (float)$tiers[1]['admin_fees'];

                                                    if ($finalCommissionType == 'percentage') {
                                                        $commission = $adminFees * $price / 100;
                                                        $earning = $price - $commission;
                                                    } else {
                                                        $earning = $price - $adminFees;
                                                    }

                                                    $set("../../pcInfo_east.1.net_earnings", number_format($earning, 2));
                                                }
                                                if (!empty($tiers[2])) {
                                                    $price = (float)$tiers[2]['price'];
                                                    $adminFees = (float)$tiers[2]['admin_fees'];

                                                    if ($finalCommissionType == 'percentage') {
                                                        $commission = $adminFees * $price / 100;
                                                        $earning = $price - $commission;
                                                    } else {
                                                        $earning = $price - $adminFees;
                                                    }

                                                    $set("../../pcInfo_east.2.net_earnings", number_format($earning, 2));
                                                }
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, $state, Component $component) {
                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ]),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->numeric()
                                        ->prefix('RM')
                                        ->live()
                                        ->formatStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->dehydrateStateUsing(function ($state) {
                                            return number_format((float)($state ?? 0), 2);
                                        })
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        ),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state) use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        ->reactive()
                                        ->readOnly()
                                        ->prefix('RM')
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_east');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;
                                            $adminFees = number_format($price - $adminFees, 2);

                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();

                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }

                                            $set('pcInfo_east', $updatedTiers);
                                        }
                                    });
                                })
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {
                                            $tiers = $get('pcInfo_east');
                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    // ->title('Invalid Tier Addition')
                                                    ->title('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $tiers);
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {

                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ]),

                        Section::make('West Malaysia')->schema([
                            TableRepeater::make('pcInfo_west')
                                ->maxItems(3)
                                ->minItems(function (Get $get) {
                                    return $get('price_type') === 'tier' ? 1 : 0;
                                })
                                ->required(function (Get $get) {
                                    return $get('price_type') === 'tier';
                                })
                                ->validationMessages([
                                    'required' => 'The west zone tier price is required.',
                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                ])
                                ->deletable(function (Get $get) {
                                    if (count($get('pcInfo_west')) > 1) {
                                        return true;
                                    }
                                    return false;
                                })
                                ->formatStateUsing(function () use ($record, $finalCommission, $finalCommissionType, $productRelationData) {
                                    $tiers = [];

                                    if ($productRelationData?->west_tier_1_min_quantity != null) {
                                        $tier1MaxQty = $productRelationData->west_tier_1_max_quantity == 0 ? null : $productRelationData->west_tier_1_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => $productRelationData?->west_tier_1_min_quantity,
                                            'max_quantity' => $tier1MaxQty,
                                            'price' => $productRelationData?->west_tier_1_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_1_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_2_min_quantity != null) {
                                        $tier2MaxQty = $productRelationData->west_tier_2_max_quantity == 0 ? null : $productRelationData->west_tier_2_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 2',
                                            'min_quantity' => $productRelationData?->west_tier_2_min_quantity,
                                            'max_quantity' => $tier2MaxQty,
                                            'price' => $productRelationData?->west_tier_2_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_2_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if ($productRelationData?->west_tier_3_min_quantity != null) {
                                        $tier3MaxQty = $productRelationData->west_tier_3_max_quantity == 0 ? null : $productRelationData->west_tier_3_max_quantity;
                                        $tiers[] = [
                                            'type' => 'Tier 3',
                                            'min_quantity' => $productRelationData?->west_tier_3_min_quantity,
                                            'max_quantity' => $tier3MaxQty,
                                            'price' => $productRelationData?->west_tier_3_base_price,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => self::netEarnings($productRelationData?->west_tier_3_base_price, $finalCommission, $finalCommissionType),
                                        ];
                                    }

                                    if (empty($tiers)) {
                                        $tiers[] = [
                                            'type' => 'Tier 1',
                                            'min_quantity' => 1,
                                            'max_quantity' => null,
                                            'price' => null,
                                            'admin_fees' => $finalCommission,
                                            'net_earnings' => 0,
                                        ];
                                    }
                                    // dd($tiers);
                                    return $tiers;
                                })
                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) use ($finalCommissionType) {
                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) use ($finalCommissionType) {

                                        $tiers = $component->getState();
                                        $eastTiers = $get('pcInfo_east');

                                        if (!empty($tiers)) {
                                            unset($tiers[$arguments['item']]);
                                            foreach ($tiers as $key => $tier) {
                                                if (isset($tier['max_quantity'])) {
                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                }

                                                // Do the same for min_quantity and price
                                                if (isset($tier['min_quantity'])) {
                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                }

                                                if (isset($tier['price'])) {
                                                    $priceStr = (string)$tier['price'];
                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                }
                                            }

                                            $tierKeys = array_keys($tiers);
                                            $tiersArray = array_values($tiers);

                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                $tierNumber = $i + 1;
                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $finalCommissionType);

                                                if ($i == 0) {
                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                } else {
                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                    if ($previousMax > 0) {
                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                    }
                                                }
                                            }

                                            $updatedTiers = [];
                                            foreach ($tierKeys as $index => $key) {
                                                if (isset($tiersArray[$index])) {
                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                }
                                            }
                                            foreach ($eastTiers as $key => $tier) {
                                                $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $eastTiers);
                                            $set('pcInfo_west', $updatedTiers);
                                        }
                                    });
                                })
                                ->reorderable(false)
                                ->headers([
                                    Header::make('Type'),
                                    Header::make('min_quantity')->label("Min Qty"),
                                    Header::make('max_qty')->label("Max Qty"),
                                    Header::make('price')->label("Price per Unit"),
                                    Header::make('admin_fees')->label("Admin Fees"),
                                    Header::make('net_earnings')->label("Net Earnings")
                                ])
                                ->schema([
                                    TextInput::make('type')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            // Find this item's position in the repeater
                                            $rowUuid = $component->getStatePath();
                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (empty($tiers)) {
                                                return "Tier 1";
                                            } else {
                                                return "Tier " . count($tiers) + 1;
                                            }

                                            $keys = array_keys($tiers);
                                            $position = array_search($rowUuid, $keys);

                                            if ($position === false) return "Tier ?";
                                            return "Tier " . ($position + 1);
                                        }),

                                    TextInput::make('min_quantity')
                                        ->label('')
                                        ->formatStateUsing(fn() => 1)
                                        ->placeholder('Min quantity')
                                        ->numeric()
                                        ->disabled()
                                        ->dehydrated(true)  // Ensure this value is saved
                                        ->live(),

                                    TextInput::make('max_quantity')
                                        ->label('')
                                        ->placeholder('and above')
                                        ->numeric()
                                        ->dehydrated(true)
                                        ->readOnly(function (Get $get, Component $component) {
                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);

                                            $index = $pathParts[count($pathParts) - 2];

                                            return $index == 2;
                                        })
                                        ->live('blur')
                                        ->rules(function (Get $get) {
                                            $minQty = $get('min_quantity');
                                            if (!empty($minQty)) {
                                                return ['numeric', 'gt:' . $minQty];
                                            }
                                        })
                                        ->validationAttribute('Max quantity')
                                        ->validationMessages([
                                            'gt' => 'Max quantity must be greater than min quantity',
                                        ])
                                        ->afterStateUpdated(function ($state, Set $set, Get $get, $component) use ($finalCommissionType) {
                                            $maxQty = (int)$state;

                                            $rowPath = $component->getStatePath();
                                            $pathParts = explode('.', $rowPath);
                                            $rowKey = $pathParts[count($pathParts) - 2];

                                            $tiers = $get('../../pcInfo_west');

                                            $tierKeys = array_keys($tiers);
                                            $currentKeyIndex = array_search($rowKey, $tierKeys);

                                            if ($currentKeyIndex !== false && isset($tierKeys[$currentKeyIndex + 1])) {

                                                $nextKey = $tierKeys[$currentKeyIndex + 1];
                                                $set("../../pcInfo_west.{$nextKey}.min_quantity", $maxQty + 1);
                                                if (!empty($tiers[1])) {
                                                    $price = (float)$tiers[1]['price'];
                                                    $adminFees = (float)$tiers[1]['admin_fees'];

                                                    if ($finalCommissionType == 'percentage') {
                                                        $commission = $adminFees * $price / 100;
                                                        $earning = $price - $commission;
                                                    } else {
                                                        $earning = $price - $adminFees;
                                                    }

                                                    $set("../../pcInfo_west.1.net_earnings", number_format($earning, 2));
                                                }
                                                if (!empty($tiers[2])) {
                                                    $price = (float)$tiers[2]['price'];
                                                    $adminFees = (float)$tiers[2]['admin_fees'];

                                                    if ($finalCommissionType == 'percentage') {
                                                        $commission = $adminFees * $price / 100;
                                                        $earning = $price - $commission;
                                                    } else {
                                                        $earning = $price - $adminFees;
                                                    }
                                                    $set("../../pcInfo_west.2.net_earnings", number_format($earning, 2));
                                                }
                                            }
                                        })
                                        ->validationAttribute('Maximum quantity'),

                                    TextInput::make('price')
                                        ->label('')
                                        ->placeholder('Price')
                                        ->validationAttribute('Price')
                                        ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                                        ->afterStateUpdated(function (Set $set, Get $get) use ($finalCommissionType) {
                                            $price = (int)$get('price');
                                            $adminFees = (int)$get('admin_fees');
                                            $commissionType = $finalCommissionType;

                                            if ($commissionType === 'percentage') {
                                                $commission = $price * $adminFees / 100;
                                            } else {
                                                $commission = $adminFees;
                                            }

                                            $netEarnings = $price - $commission;
                                            $set('net_earnings', number_format($netEarnings, 2, '.', ''));
                                        })
                                        ->calculateNetEarningsForRepeater(
                                            $commission = $finalCommission,
                                            $commissionType = $finalCommissionType,
                                        )
                                        ->numeric()
                                        ->prefix('RM'),

                                    TextInput::make('admin_fees')
                                        ->label('')
                                        ->disabled()
                                        ->formatStateUsing(function () use ($get) {
                                            return (int)$get('admin_fees');
                                        })
                                        ->placeholder('Admin fees')
                                        ->numeric()
                                        ->prefix(function () use ($finalCommissionType) {
                                            if ($finalCommissionType === 'percentage') {
                                                return '%';
                                            }
                                            return 'RM';
                                        })
                                        ->live(),

                                    TextInput::make('net_earnings')
                                        ->label('')
                                        ->placeholder('Net earnings')
                                        ->disabled()
                                        ->prefix('RM')
                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                            $rowUuid = $component->getStatePath();
                                            $lastDotPos = strrpos($rowUuid, '.');
                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                            $tiers = $get('pcInfo_west');
                                            if (!isset($tiers[$rowUuid])) return 0;

                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;
                                            // dd($price, $adminFees);
                                            return number_format($price - $adminFees, 2);
                                        })
                                ])
                                ->defaultItems(1)
                                ->label("")
                                ->addActionAlignment(Alignment::End)
                                ->addActionLabel("")
                                ->addAction(
                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                        ->label('+ Add Tier')
                                        ->extraAttributes([
                                            'style' => 'border: none !important; box-shadow: none !important;'
                                        ])
                                        ->action(function (Get $get, Set $set, TierValidationService $validation) use ($finalCommissionType, $finalCommission) {

                                            $tiers = $get('pcInfo_west');
                                            $eastTiers = $get('pcInfo_east');
                                            $isValid = $validation::validateTierCompletion($tiers);

                                            if (!$isValid) {
                                                // Show notification and prevent adding a new tier
                                                \Filament\Notifications\Notification::make()
                                                    ->danger()
                                                    // ->title('Invalid Tier Addition')
                                                    ->title('Please complete the current tier before adding a new one.')
                                                    ->send();
                                                return;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            if (empty($tiers)) {
                                                // First tier starts at 1
                                                $minQty = 1;
                                            } else {
                                                // Find the max quantity of the last tier
                                                $lastTier = end($tiers);
                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                            }
                                            foreach ($tiers as $key => $tier) {
                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            foreach ($eastTiers as $key => $tier) {
                                                $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $finalCommissionType);
                                            }
                                            $set('pcInfo_east', $eastTiers);
                                            $set('pcInfo_west', [
                                                ...$tiers,
                                                [
                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                    'min_quantity' => $minQty ?? 1,
                                                    'max_quantity' => null,
                                                    'price' => null,
                                                    'admin_fees' => $finalCommission,
                                                ]
                                            ]);
                                        })
                                ),

                        ])
                    ];
                })
        ];
    }

    /**
     * Format bonus structure for activity logging
     */
    private static function formatBonusStructureForLog($data, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 3; $i++) {
            $qty = $data["{$region}_bonus_{$i}_quantity"] ?? null;
            $bonusQty = $data["{$region}_bonus_{$i}_quantity_value"] ?? null;

            if (!empty($qty) && !empty($bonusQty)) {
                $structure["Level {$i}"] = "Buy {$qty}, Get {$bonusQty} Free";
            }
        }
        return empty($structure) ? ['No bonus structure set'] : $structure;
    }

    /**
     * Format tier structure for activity logging
     */
    private static function formatTierStructureForLog($tierData): array
    {
        if (empty($tierData)) {
            return ['No tier structure set'];
        }

        $structure = [];
        foreach ($tierData as $index => $tier) {
            $tierNum = $index + 1;
            $minQty = $tier['min_quantity'] ?? 'N/A';
            $maxQty = $tier['max_quantity'] ?? 'Above';
            $price = isset($tier['price']) ? 'RM ' . number_format($tier['price'], 2) : 'Not Set';

            if ($maxQty === 'Above' || empty($maxQty)) {
                $structure["Tier {$tierNum}"] = "{$minQty}+ units = {$price} each";
            } else {
                $structure["Tier {$tierNum}"] = "{$minQty}-{$maxQty} units = {$price} each";
            }
        }
        return $structure;
    }

    /**
     * Format old bonus structure for activity logging
     */
    private static function formatOldBonusStructureForLog($oldPriceData, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 3; $i++) {
            $qty = $oldPriceData["{$region}_bonus_{$i}_quantity"] ?? null;
            $bonusQty = $oldPriceData["{$region}_bonus_{$i}_quantity_value"] ?? null;

            if (!empty($qty) && !empty($bonusQty)) {
                $structure["Level {$i}"] = "Buy {$qty}, Get {$bonusQty} Free";
            }
        }
        return empty($structure) ? ['No bonus structure set'] : $structure;
    }

    /**
     * Format old tier structure for activity logging
     */
    private static function formatOldTierStructureForLog($oldPriceData, $region): array
    {
        $structure = [];
        for ($i = 1; $i <= 5; $i++) { // Assuming max 5 tiers
            $minQty = $oldPriceData["{$region}_tier_{$i}_min_quantity"] ?? null;
            $maxQty = $oldPriceData["{$region}_tier_{$i}_max_quantity"] ?? null;
            $price = $oldPriceData["{$region}_tier_{$i}_base_price"] ?? null;

            if (!empty($minQty) && !empty($price)) {
                $formattedPrice = 'RM ' . number_format($price, 2);
                if (empty($maxQty) || $maxQty === 'Above') {
                    $structure["Tier {$i}"] = "{$minQty}+ units = {$formattedPrice} each";
                } else {
                    $structure["Tier {$i}"] = "{$minQty}-{$maxQty} units = {$formattedPrice} each";
                }
            }
        }
        return empty($structure) ? ['No tier structure set'] : $structure;
    }

    /**
     * Format old data for human-readable stock activity logging
     */
    private static function formatOldDataForStockLog(array $oldFields): array
    {
        $humanReadableOldData = [];

        foreach ($oldFields as $field => $oldValue) {
            $humanReadableKey = self::getHumanReadableStockFieldName($field);
            $humanReadableValue = self::formatStockFieldValueForLog($field, $oldValue);
            $humanReadableOldData[$humanReadableKey] = $humanReadableValue;
        }

        return $humanReadableOldData;
    }

    /**
     * Format complete old data for complex stock activity logging
     */
    private static function formatCompleteOldDataForStockLog(array $oldChangedData): array
    {
        $humanReadableOldData = [];

        foreach ($oldChangedData as $section => $changes) {
            if ($section === 'stock') {
                $humanReadableSection = [];
                foreach ($changes as $field => $oldValue) {
                    $humanReadableKey = self::getHumanReadableStockFieldName($field);
                    $humanReadableValue = self::formatStockFieldValueForLog($field, $oldValue);
                    $humanReadableSection[$humanReadableKey] = $humanReadableValue;
                }
                if (!empty($humanReadableSection)) {
                    $humanReadableOldData['Stock Configuration'] = $humanReadableSection;
                }
            } elseif ($section === 'batches') {
                if (!empty($changes)) {
                    $humanReadableOldData['Batch Details'] = [];
                    foreach ($changes as $index => $batch) {
                        $batchNum = $index + 1;
                        $humanReadableOldData['Batch Details']["Batch {$batchNum}"] = [
                            'Batch Number' => $batch['batch_number'] ?? 'Not Set',
                            'Available Stock' => $batch['available_stock'] ?? 0,
                            'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
                        ];
                    }
                }
            }
        }

        return $humanReadableOldData;
    }

    /**
     * Get human-readable field name for stock fields
     */
    private static function getHumanReadableStockFieldName(string $field): string
    {
        $fieldMap = [
            'stock' => 'Current Stock',
            'total_stock' => 'Total Stock',
            'is_batch_wise_stock' => 'Batch Management',
            'low_stock' => 'Low Stock Trigger',
            'wholesale_pack_size' => 'Wholesale Pack Size',
            'stock_type' => 'Stock Type',
            'available_stock' => 'Available Stock',
            'batch_number' => 'Batch Number',
            'expiry_date' => 'Expiry Date',
            'manufacturing_date' => 'Manufacturing Date',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'product_id' => 'Product ID',
            'user_id' => 'User ID',
            'products_relation_id' => 'Product Relation ID',
        ];

        return $fieldMap[$field] ?? ucwords(str_replace('_', ' ', $field));
    }

    /**
     * Format stock field value for human-readable display
     */
    private static function formatStockFieldValueForLog(string $field, $value)
    {
        if ($value === null) {
            return 'Not Set';
        }

        switch ($field) {
            case 'is_batch_wise_stock':
                return $value ? 'By Batch' : 'Simple Stock';
            case 'stock_type':
                return $value === 'wps' ? 'Wholesale Pack' : 'Unit';
            case 'created_at':
            case 'updated_at':
                return \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s');
            case 'expiry_date':
            case 'manufacturing_date':
                return $value ? \Carbon\Carbon::parse($value)->format('Y-m-d') : 'Not Set';
            default:
                return (string) $value;
        }
    }
}
