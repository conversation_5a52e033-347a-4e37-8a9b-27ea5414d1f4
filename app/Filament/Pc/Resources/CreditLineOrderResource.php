<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Exports\OrderExporter;
use App\Filament\Pc\Resources\CreditLineOrderResource\Pages;
use App\Filament\Pc\Resources\CreditLineOrderResource\RelationManagers;
use App\Mail\PCActionMail\PaidCreditLineOrderMail;
use App\Models\ClinicDetail;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\User;
use App\Models\Order;
use App\Models\PcDetail;
use App\Models\ClinicCreditHistory;
use App\Models\SubOrder;
use Filament\Facades\Filament;
use Illuminate\Support\Arr;
use Filament\Infolists\Infolist;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\DB;
use App\Models\ClinicCredit;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Filament\Tables\Filters\Filter;
use Filament\Notifications\Notification;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\Grid;
use Filament\Tables\Actions\ViewAction;
use Filament\Infolists\Components\Group;
use Illuminate\Support\Facades\Cookie;

class CreditLineOrderResource extends Resource
{
    protected static ?string $model = SubOrder::class;

    //protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Orders Management';
    protected static ?string $navigationLabel = 'Credit Line Orders';

    public static function canAccess(): bool
    {
        $user = getUser(auth()->user());
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('credit-line-orders_view');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $user = Filament::auth()->user()->id;

        return $infolist->schema(function ($record) {
            return [
                Grid::make()
                    ->schema([
                        Section::make(function ($record) {
                            return ucfirst($record->order->user->clinicData->clinic_name);
                        })
                            ->schema([
                                Group::make()
                                    ->schema([
                                        TextEntry::make('order.user.clinicData.clinic_number')->label('Facility ID')
                                        ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()

                                        ->url(fn(SubOrder $record): string => route('filament.pc.resources.facilities.show', ['user_id' => $record->order->user_id ?? null])),
                                        TextEntry::make('order.user.clinicData.clinic_name')->label('Facility Name'),
                                        TextEntry::make('order.user.clinicData.mobile_number')
                                            ->label('Phone Number')
                                            ->default('-')
                                            ->formatStateUsing(function ($state, $record) {
                                                $clinicData = optional($record->order->user->clinicData); // Use optional() helper

                                                return $clinicData->mobile_code && $clinicData->mobile_number ? '(' . $clinicData->mobile_code . ') ' . $clinicData->mobile_number : '-';
                                            }),

                                        TextEntry::make('order.user.email')->label('Email')
                                            ->extraAttributes([
                                                'class' => 'max-w-full overflow-hidden text-ellipsis',
                                                'style' => 'word-break: break-all; min-width: 0;'
                                            ]),
                                    ])

                                    ->columns(4),
                            ])
                            ->columnSpan(2),
                        Group::make()
                            ->schema([
                                Section::make('Billing Address')->schema([
                                    TextEntry::make('')
                                        ->default(function ($record) {
                                            $billingAddress = [$record->order->billing_first_name, $record->order->billing_last_name, $record->order->billing_address_1, $record->order->billing_address_2, $record->order->billingCity->name, $record->order->billingState->name, $record->order->billing_postal_code, $record->order->billingCountry->name];

                                            $phone = '';
                                            if (!empty($record->order->billing_phone_code)) {
                                                $phone = '(' . $record->order->billing_phone_code . ') ' . $record->order->billing_phone_number;
                                            } else {
                                                $phone = $record->order->billing_phone_number;
                                            }

                                            $billingAddress[] = $phone;
                                            return implode(', ', array_filter($billingAddress));
                                        })
                                        ->columnSpan(1),
                                ]),
                            ])
                            ->columns(1),
                    ])
                    ->columns(3),

                Grid::make()
                    ->schema([
                        Section::make('Order Summary')
                            ->schema([
                                Group::make()
                                    ->schema([
                                        TextEntry::make('receipt_number')
                                            ->label('Receipt ID')
                                            ->getStateUsing(function ($record) {
                                                $state = $record->receipt_number ?? null;
                                                return filled(trim((string)$state)) ? "#{$state}" : '-';
                                            })
                                            ->columns(1),
                                        TextEntry::make('invoice_po_number')
                                        ->label('Invoice ID')
                                        ->getStateUsing(function ($record) {
                                            $state = $record->invoice_po_number ?? null;
                                            return filled(trim((string)$state)) ? "#{$state}" : '-';
                                        }),
                                        TextEntry::make('invoice_po_number')
                                            ->label('PS Invoice ID')
                                            ->getStateUsing(function ($record) {
                                                $state = $record->invoice_po_number ?? null;
                                                return '-';//filled(trim((string)$state)) ? "#{$state}" : '-';
                                            }),
                                        TextEntry::make('order.order_number')->label('Order ID')->default('-'),
                                        TextEntry::make('created_at')
                                            ->label('Order Date')
                                            ->formatStateUsing(function ($state) {
                                                $user = Filament::auth()->user()->id;
                                                $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                                                return getFormatedDate($state, $format);
                                            }),
                                        TextEntry::make('id')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->orderProducts()->count();
                                            })
                                            ->label('Total Items'),
                                        TextEntry::make('shipping_by')->label('Shipping By')->default('-')
                                            ->formatStateUsing(function ($state, $record) {
                                                return ($record->warehouse_type == 'owned') ? 'Own Logistic' : 'Dpharma Logistic';
                                            }),
                                        TextEntry::make('warehouse_type')
                                            ->label('ETA')
                                            ->formatStateUsing(function ($state, $record) {
                                                if ($record->warehouse_type == 'owned') {
                                                    if(empty($record->delivery_days)){
                                                        return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->pcDetail->delivery_days)->format('M d, Y') : '-';
                                                    }
                                                    return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->delivery_days)->format('M d, Y') : '-';
                                                }
                                                return '-';
                                            }),
                                        TextEntry::make('total_sub_order_value')
                                            ->formatStateUsing(function ($state) {
                                                return 'RM ' . number_format($state, 2);
                                            })
                                            ->label('Order Total'),
                                        TextEntry::make('status')
                                            ->label('Order Status')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                                            })
                                            ->icon(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';

                                                return match ($status) {
                                                    'pending' => 'bi-clock-fill',
                                                    'rejected' => 'bi-x-circle-fill',
                                                    'accepted' => 'bi-patch-check-fill',
                                                    'cancelled' => 'bi-patch-check-fill',
                                                    'delivered' => 'bi-patch-check-fill',
                                                    'in_transit'  => 'heroicon-o-truck',
                                                    'ready_for_pickup' => 'heroicon-o-archive-box',
                                                    default => 'heroicon-o-question-mark-circle',
                                                };
                                            })
                                            ->color(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';

                                                $color = config("constants.order_status.color.{$status}", '#424242');
                                                return $color;
                                            })
                                            ->extraAttributes(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';
                                                $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                                                $color = config("constants.order_status.color.{$status}", '#424242');
                                                $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                                                return [
                                                    'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                                                ];
                                            }),
                                        TextEntry::make('payment_type')
                                            ->label('Payment Type')
                                            ->formatStateUsing(function ($state) {
                                                return $state ? ucwords(str_replace('_', ' ', $state)) : '-';
                                            }),
                                        TextEntry::make('credit_line_status')
                                            ->label('Payment Status')
                                            ->visible(function ($record) {
                                                return $record ? $record->payment_type === 'credit_line' : false;
                                            })
                                            ->formatStateUsing(function ($state) {
                                                return $state ? ucwords(str_replace('_', ' ', $state)) : '-';
                                            }),
                                        TextEntry::make('rejected_note')
                                            ->label('Reason for Cancellation')
                                            ->visible(function ($record) {
                                                return $record ? $record->status === 'rejected' : false;
                                            }),
                                    ])
                                    ->columns(3),
                            ])
                            ->columnSpan(2),
                        Group::make()
                            ->schema([
                                Section::make('Shipping Address')
                                    ->label('Order Status')
                                    ->schema([
                                        TextEntry::make('')
                                            ->default(function ($record) {
                                                $shippingAddress = [$record->order->shipping_first_name, $record->order->shipping_last_name, $record->order->shipping_address_1, $record->order->shipping_address_2, $record->order->shippingCity->name, $record->order->shippingState->name, $record->order->shipping_postal_code ?? ''];
                                                $phone = '';
                                                if (!empty($record->order->shipping_phone_code)) {
                                                    $phone = '(' . $record->order->shipping_phone_code . ') ' . $record->order->shipping_phone_number;
                                                } else {
                                                    $phone = $record->order->shipping_phone_number;
                                                }

                                                $shippingAddress[] = $phone;
                                                return implode(', ', array_filter($shippingAddress));
                                            })
                                            ->columnSpan(1),
                                    ]),
                                Section::make('Purchase Order')
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.purchase-order-invoice')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                                Section::make('PS Invoice')
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.invoice-viewer')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                                Section::make('DPharma Invoice')
                                    ->visible(function ($record) {
                                        return !empty($record->payout_path) ? true : false;
                                    })
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.dpharma-invoice-viewer')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                            ])
                            ->columns(1),
                    ])
                    ->columns(3),
                Group::make()
                    ->columnSpanFull()
                    ->extraAttributes(['style' =>  'margin: 0px !important; padding: 0px !important;'])
                    // ->heading(function ($record) {
                    //     return 'Items';
                    // })
                    ->schema([
                        Group::make()->schema(function ($record) {
                            return [
                                ViewEntry::make('my')
                                    ->view('filament.pc.infolist.components.sub-order-detail')
                                    ->viewData(['order' => $record]),
                            ];
                        })->extraAttributes(['style' => 'margin: 0px !important; padding: 0px !important;']),
                    ]),
            ];
        });
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table->query(
            SubOrder::with(['order', 'user', 'orderProducts', 'orderProducts.product'])
                ->where('user_id', $user)
                ->where('payment_type', 'credit_line') // Filter subOrders by logged-in user
                ->withCount([
                    'orderProducts as order_products_count' => function (Builder $query) {
                        $query->select(DB::raw("COALESCE(COUNT(*), 0)"));
                    }
                ]),
        )
            // ->recordUrl(null)
            ->emptyStateHeading('No Credit Line Order records found')
            ->columns([
                TextColumn::make('order.order_number')
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.credit-line-orders.view', ['record' => $record->id, 'type' => 'credit_line']))
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('order.user.clinicData.clinic_number')
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.facilities.show', ['user_id' => $record->order->user_id ?? null]))
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Facility ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('order.user.clinicData.clinic_name')
                    ->label('Facility Name')
                    ->sortable()
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('order.user.clinicData.clinic_name')->label('Facility Name')->sortable()->searchable()->toggleable(),
                TextColumn::make('created_at')->label('Order Date')
                    ->formatStateUsing(function ($state) {
                        $user = Filament::auth()->user()->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    })->sortable()->toggleable(),
                TextColumn::make('order_products_count')
                    ->label('Items')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('total_sub_order_value')->label('Order Total')->prefix('RM ')->formatStateUsing(function ($state) {
                    return number_format($state, 2);
                })->sortable()->toggleable(),
                TextColumn::make('status')->searchable()->label('Order Status')->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'accepted' => 'bi-patch-check-fill',
                            'cancelled' => 'heroicon-o-x-mark',
                            'delivered' => 'heroicon-o-truck',
                            'in_transit' => 'heroicon-o-truck',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color = config("constants.order_status.color.{$status}", '#424242');

                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        // Add inline styling to the status text (badge)
                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),
                TextColumn::make('credit_line_status')->searchable()->toggleable()->label('Payment Status')
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->credit_line_status)) : 'pending';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->credit_line_status) : 'pending';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'paid' => 'bxs-dollar-circle',
                            default => 'bi-clock-fill',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->credit_line_status) : 'pending';

                        return match ($status) {
                            'pending' => 'warning',
                            'paid' => 'success',
                            default => 'warning',
                        };
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->credit_line_status) : 'pending';

                        // Add inline styling to the status text (badge)
                        return match ($status) {
                            'pending' => ['style' => 'background-color:#FFE4CC; border: 1px solid #FFB366; border-radius: 6px; color:#CC5500; padding: 4px 8px; width: fit-content; font-weight: 500;'], // orange for pending
                            'paid' => ['style' => 'background-color:#CCE5CC; border: 1px solid #99CC99; border-radius: 6px; color:#006600; padding: 4px 8px; width: fit-content; font-weight: 500;'],  // light green for approved
                            default =>  ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'],
                        };
                    }),

            ])

            ->filters([
                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('order.user.clinicData', 'clinic_name')->options(fn() => ClinicDetail::whereNotNull('clinic_name')->pluck('clinic_name', 'id')->toArray()),
                SelectFilter::make('status')
                    ->label('Order Status')
                    ->options([
                        'pending' => 'Pending',
                        'rejected' => 'Rejected',
                        'accepted' => 'Accepted',
                        'cancelled' => 'Cancelled',
                        'in_transit' => 'In Transit',
                        'delivered' => 'Delivered',
                    ]),
                SelectFilter::make('credit_line_status')
                    ->label('Credit Status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                    ]),
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('From')
                            ->maxDate(fn($get) => $get('to_date') ?? now())
                            ->closeOnDateSelection()
                            ->placeholder('Select from date'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('To')
                            ->minDate(fn($get) => $get('from_date'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->placeholder('Select to date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '>=', $date))
                            )
                            ->when(
                                $data['to_date'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '<=', $date))
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from_date'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from_date'])->toFormattedDateString();
                        }
                        if ($data['to_date'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['to_date'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),

            ])
            ->defaultSort('id', 'desc')
            ->actionsColumnLabel('Action')
            ->actions([
                Action::make('chat_with_facility')
                    ->label(false)
                    ->tooltip('Chat With Facility')
                    ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')->size('sm')->iconButton()
                    ->color(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();
                        $unreadCount = $thread
                            ? \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count()
                            : 0;
                        return $unreadCount > 0 ? 'success' : 'primary';
                    })
                    ->extraAttributes(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        $unreadCount = $thread
                            ? \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count()
                            : 0;

                        $iconColor = $unreadCount > 0 ? 'rgb(5, 161, 5)' : 'rgb(0, 70, 104)';
                        $borderColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';
                        $textColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';

                        return [
                            'class' => "border-2 rounded-lg {$textColor}",
                            'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                        ];
                    })
                    ->visible(function ($record) {

                        return $record->order->status !== 'cancelled' && (auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->can('credit-line-orders_chat'));
                    })
                    ->badge(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        if (! $thread) {
                            return null;
                        }

                        $unreadCount = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count();

                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->badgeColor(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        if (! $thread) {
                            return 'gray';
                        }

                        $hasUnread = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->exists();

                        return $hasUnread ? 'success' : 'primary';
                    })
                    ->action(function ($record) {
                        $user = getUser(auth()->user());

                        $orderUserId = $record->order->user_id;

                        $thread = \App\Models\Thread::firstOrCreate(
                            [
                                'order_id' => $record->order_id,
                                'receiver_id' => $user->id,
                            ],
                            [
                                'receiver_id' => $user->id,
                                'sender_id' => $orderUserId,
                            ]
                        );
                        Cookie::queue('type', 'credit_line');
                        Cookie::queue('source', 'credit_line');
                        return redirect()->route('filament.pc.resources.orders.chat', [
                            'thread_id' => $thread->id,
                            'record' => $record->id,
                            'type' => request()->query('type') ?? 'credit_line',
                        ]);
                    }),
                Action::make('approve')
                    ->label(false)
                    ->icon('bxs-dollar-circle')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->requiresConfirmation()
                    ->modalHeading('Do you want to Mark as Paid?')
                    ->modalDescription(fn(SubOrder $record) => 'Are you sure about marking order #' . $record->order->order_number . ' as paid?')
                    ->modalSubmitActionLabel('Mark as Paid')
                    ->color('success')
                    ->modalIconColor('primary')
                    ->action(
                        function (SubOrder $record) {
                            $lastClinicCredit = ClinicCreditHistory::where('supplier_id', auth()->id())->where('facility_id', (int)$record->order->user_id)->orderBy('id', 'desc')->first();
                            if (!empty($lastClinicCredit)) {

                                $remainingAmount = 0;
                                if ($lastClinicCredit->remaining_amount == 0) {
                                    $remainingAmount = $record->total_amount + $lastClinicCredit->total_credit_amount;
                                    $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                } else {
                                    $remainingAmount = $lastClinicCredit->remaining_amount + $record->total_amount;
                                }

                                $clinicCredit = new ClinicCreditHistory();
                                $clinicCredit->facility_id = $record->order->user_id;
                                $clinicCredit->supplier_id = auth()->id();
                                $clinicCredit->credit_amount = $record->total_amount;
                                $clinicCredit->debit_amount = 0;
                                $clinicCredit->edit_credit = 0;
                                $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used - $record->total_amount;
                                $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount;
                                $clinicCredit->remaining_amount = $remainingAmount;
                                $clinicCredit->reference_id = $record->id;
                                $clinicCredit->reference_value = 'suborder';
                                $clinicCredit->action = 'Order Paid';
                                // $clinicCredit->save();
                            }

                            $previousStatus = $record->credit_line_status;
                            $record->update(['credit_line_status' => 'paid']);
                            Mail::to($record->order->user->email)->send(new PaidCreditLineOrderMail($record));

                            activity()
                                ->causedBy(auth()->user())
                                ->useLog('order_payment')
                                ->performedOn($record)
                                ->withProperties([
                                    'old' => [
                                        'credit_line_status' => $previousStatus,
                                    ],
                                    'attributes' => [
                                        'credit_line_status' => 'paid',
                                    ],
                                ])
                                ->log("Order #{$record->order->order_number}  has been marked as paid, including payment against the credit line.");
                        }
                    )
                    // ->visible(fn(SubOrder $record) => $record->credit_line_status !== 'paid')
                    ->visible(
                        fn(SubOrder $record) => $record->credit_line_status !== 'paid' &&
                            in_array($record->status, ['accepted', 'in_transit', 'delivered']) &&
                            (auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->can('credit-line-orders_mark as paid'))
                    )
                    ->tooltip(fn(SubOrder $record) => $record->credit_line_status === 'paid'
                        ? 'Already marked as paid'
                        : 'Mark as Paid'),
                Action::make('view')
                    // ->url(fn(SubOrder $record): string => route('filament.pc.resources.credit-line-orders.view', ['record' => $record->id])) // Change order_id to record
                    ->url(function (SubOrder $record) {
                        Cookie::queue('source', 'credit_line');
                        return route('filament.pc.resources.credit-line-orders.view', ['record' => $record->id]);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Order Details')
                    ->color('gray')
                    ->visible(function ($record) {
                        return $record->order->status !== 'cancelled' || (auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->can('credit-line-orders_view'));
                    })
                    ->label(false),

                Action::make('inTransit')
                    ->tooltip('In-Transit')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-truck')->size('sm')->iconButton()
                    ->label(false)
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'owned' &&
                            $record->status === 'accepted'
                        );
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') ||  $user->can('credit-line-orders_in transit'));
                    })
                    ->action(function ($record) {
                        $record->status = 'in_transit';
                        $record->in_transit_at = now();
                        $record->save();

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'in_transit',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status has been updated to in transit");

                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    })
                    ->color('gray'),
                Action::make('readyForPickup')
                    ->tooltip('Ready For Pickup')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-archive-box')->size('sm')->iconButton()
                    ->label(false)
                    ->color('gray')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'dpharma' &&
                            $record->status === 'accepted'
                        );
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_ready for pickup'));
                    })
                    ->action(function ($record) {
                        $record->status = 'ready_for_pickup';
                        $record->ready_for_pickup_at = now();
                        $record->save();

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'ready_for_pickup',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status has been updated to ready for pickup");

                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    }),
                Action::make('delivered')
                    ->icon('heroicon-o-check')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('Delivered')
                    ->label(false)
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = ($record->warehouse_type == 'owned' && $record->status == 'in_transit') ? true : false;
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') ||  $user->can('credit-line-orders_delivered'));
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')

                    ->action(function ($record) {
                        $record->status = 'delivered';
                        $record->deliver_at = now();
                        $record->save();

                        $mainOrder = Order::with('subOrders')->where('id', $record->order_id)->first();
                        $allDelivered = $mainOrder && $mainOrder->subOrders->isNotEmpty() &&
                            $mainOrder->subOrders->every(fn($subOrder) => $subOrder->status === 'delivered');
                        if ($allDelivered) {
                            DB::table('orders')
                                ->where('id', $record->order_id)
                                ->update(['status' => 'delivered']);
                        }

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'in_transit',
                                ],
                                'attributes' => [
                                    'status' => 'delivered',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status has been updated to delivered");

                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    })
                    ->color('success'),


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditLineOrders::route('/'),
            'create' => Pages\CreateCreditLineOrder::route('/create'),
            'edit' => Pages\EditCreditLineOrder::route('/{record}/edit'),
            'view' => Pages\ViewCreditLineOrder::route('/{record}/detail'),
        ];
    }
}
