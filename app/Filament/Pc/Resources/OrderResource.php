<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\OrderResource\Pages;
use App\Filament\Pc\Resources\OrderResource\RelationManagers;
use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\Models\Order;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\View;
use App\Filament\Exports\OrderExporter;
use App\Models\ClinicDetail;
use App\Models\OrderProduct;
use App\Models\PcDetail;
use App\Models\SubOrder;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Card;
use Filament\Infolists\Components\Grid;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Infolists\Components\Group;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Cookie;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Infolists\Components\Actions\Action as InfoAction;
use Filament\Navigation\NavigationItem;
use App\Service\ShippingService;

class OrderResource extends Resource
{
    protected static ?string $model = SubOrder::class;

    protected static ?string $navigationGroup = 'Orders Management';
    protected static ?string $navigationLabel = 'All Orders';

    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();

        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || $user->can('all-orders_view') ||
            $user->can('all-orders_view details') || $user->can('all-orders_review order') || $user->can('all-orders_reject order') ||
            $user->can('all-orders_chat') || $user->can('all-orders_approve order') || $user->can('all-orders_download po') ||
            $user->can('all-orders_upload invoice') || $user->can('all-orders_export') || $user->can('all-orders_confirm order');
    }

    public static function getNavigationItems(): array
    {
        // Check if the query string contains 'type=credit_line'
        $isCreditLineActive = request()->query('type') === 'credit_line';

        // Define default navigation items
        $navigationItems = [
            NavigationItem::make('All Orders')
                ->url(route('filament.pc.resources.orders.index'))
                ->isActiveWhen(
                    fn(): bool =>
                    request()->routeIs('filament.pc.resources.orders.*') && !$isCreditLineActive
                )
                ->group('Orders Management'),
        ];

        // Add 'Credit Line Orders' navigation item
        $navigationItems[] = NavigationItem::make('Credit Line Orders')
            ->url(route('filament.pc.resources.credit-line-orders.index', ['type' => 'credit_line']))
            ->isActiveWhen(fn(): bool => $isCreditLineActive)
            ->group('Orders Management');

        return $navigationItems;
    }
    public static function form(Form $form): Form
    {
        return $form->schema([
            //
        ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $user = Filament::auth()->user()->id;

        return $infolist->schema(function ($record) {
            return [
                Grid::make()
                    ->schema([
                        Section::make(function ($record) {
                            return ucfirst($record->order->user->clinicData->clinic_name);
                        })
                            ->schema([
                                Group::make()
                                    ->schema([
                                        TextEntry::make('order.user.clinicData.clinic_number')
                                        ->label('Facility ID')
                                        ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()

                                        ->url(fn(SubOrder $record): string => route('filament.pc.resources.facilities.show', ['user_id' => $record->order->user_id ?? null])),
                                        TextEntry::make('order.user.clinicData.clinic_name')->label('Facility Name'),
                                        TextEntry::make('order.user.clinicData.mobile_number')
                                            ->label('Phone Number')
                                            // ->prefix('+')
                                            ->default('-')
                                            ->formatStateUsing(function ($state, $record) {
                                                $clinicData = optional($record->order->user->clinicData); // Use optional() helper
                                                // dd($clinicData);
                                                return  $clinicData->mobile_code && $clinicData->mobile_number ? '+(' . $clinicData->mobile_code . ') ' . $clinicData->mobile_number : '-';
                                            }),

                                        TextEntry::make('order.user.email')->label('Email')
                                            ->extraAttributes([
                                                'class' => 'max-w-full overflow-hidden text-ellipsis',
                                                'style' => 'word-break: break-all; min-width: 0;'
                                            ]),
                                    ])

                                    ->columns(4),
                            ])
                            ->columnSpan(2),
                        Group::make()
                            ->schema([
                                Section::make('Billing Address')->schema([
                                    TextEntry::make('')
                                        ->default(function ($record) {
                                            $billingAddress = [$record->order->billing_first_name, $record->order->billing_last_name, $record->order->billing_address_1, $record->order->billing_address_2, $record->order->billingCity->name, $record->order->billingState->name, $record->order->billing_postal_code, $record->order->billingCountry->name];

                                            $phone = '';
                                            if (!empty($record->order->billing_phone_code)) {
                                                $phone = '(' . $record->order->billing_phone_code . ') ' . $record->order->billing_phone_number;
                                            } else {
                                                $phone = $record->order->billing_phone_number;
                                            }

                                            $billingAddress[] = $phone;
                                            return implode(', ', array_filter($billingAddress));
                                        })
                                        ->columnSpan(1),
                                ]),
                            ])
                            ->columns(1),
                    ])
                    ->columns(3),

                Grid::make()
                    ->schema([
                        Section::make('Order Summary')
                            ->schema([
                                Group::make()
                                    ->schema([
                                        TextEntry::make('receipt_number')
                                            ->label('Receipt ID')
                                            ->getStateUsing(function ($record) {
                                                $state = $record->receipt_number ?? null;
                                                return filled(trim((string)$state)) ? "#{$state}" : '-';
                                            })
                                            ->columns(1),
                                        TextEntry::make('invoice_po_number')
                                            ->label('Invoice ID')
                                            ->getStateUsing(function ($record) {
                                                $state = $record->invoice_po_number ?? null;
                                                return filled(trim((string)$state)) ? "#{$state}" : '-';
                                            }),
                                        TextEntry::make('invoice_po_number')
                                            ->label('PS Invoice ID')
                                            ->getStateUsing(function ($record) {
                                                $state = $record->invoice_po_number ?? null;
                                                return '-'; //filled(trim((string)$state)) ? "#{$state}" : '-';
                                            }),
                                        TextEntry::make('order.order_number')->label('Order ID')->default('-'),
                                        TextEntry::make('created_at')
                                            ->label('Order Date')
                                            ->formatStateUsing(function ($state) {
                                                $user = Filament::auth()->user()->id;
                                                $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                                                return getFormatedDate($state, $format);
                                            }),
                                        TextEntry::make('id')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->orderProducts()->count();
                                            })
                                            ->label('Total Items'),
                                        TextEntry::make('shipping_by')->label('Shipping By')->default('-')
                                            ->formatStateUsing(function ($state, $record) {
                                                return ($record->warehouse_type == 'owned') ? 'Own Logistic' : 'Dpharma Logistic';
                                            }),
                                        TextEntry::make('warehouse_type')
                                            ->label('ETA')
                                            ->formatStateUsing(function ($state, $record) {
                                                if ($record->warehouse_type == 'owned') {
                                                    if (empty($record->delivery_days)) {
                                                        return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->pcDetail->delivery_days)->format('M d, Y') : '-';
                                                    }
                                                    return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->delivery_days)->format('M d, Y') : '-';
                                                }
                                                return '-';
                                            }),
                                        TextEntry::make('total_sub_order_value')
                                            ->formatStateUsing(function ($state) {
                                                return 'RM ' . number_format($state, 2);
                                            })
                                            ->label('Order Total'),
                                        TextEntry::make('status')
                                            ->label('Order Status')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                                            })
                                            ->icon(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';

                                                return match ($status) {
                                                    'pending' => 'bi-clock-fill',
                                                    'rejected' => 'bi-x-circle-fill',
                                                    'accepted' => 'bi-patch-check-fill',
                                                    'cancelled' => 'bi-patch-check-fill',
                                                    'delivered' => 'bi-patch-check-fill',
                                                    'in_transit'  => 'heroicon-o-truck',
                                                    'ready_for_pickup' => 'heroicon-o-archive-box',
                                                    default => 'heroicon-o-question-mark-circle',
                                                };
                                            })
                                            ->color(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';

                                                $color = config("constants.order_status.color.{$status}", '#424242');
                                                return $color;
                                            })
                                            ->extraAttributes(function ($state, $record) {
                                                $status = $record ? strtolower($record->status) : 'unknown';
                                                $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                                                $color = config("constants.order_status.color.{$status}", '#424242');
                                                $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                                                return [
                                                    'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                                                ];
                                            }),
                                        TextEntry::make('payment_type')
                                            ->label('Payment Type')
                                            ->formatStateUsing(function ($state) {
                                                return $state ? ucwords(str_replace('_', ' ', $state)) : '-';
                                            }),

                                        TextEntry::make('payment_status')
                                            ->label('Payment Status')
                                            ->visible(function ($record) {
                                                return $record ? $record->payment_type != 'credit_line' : false;
                                            })
                                            ->tooltip("You cannot accept or reject an order if its payment status is not 'Paid'.")
                                            ->getStateUsing(function ($record) {
                                                return $record->order->payment_status ? ucwords(str_replace('_', ' ', $record->order->payment_status)) : '-';
                                            }),
                                        TextEntry::make('credit_line_status')
                                            ->label('Payment Status')
                                            ->visible(function ($record) {
                                                return $record ? $record->payment_type === 'credit_line' : false;
                                            })
                                            ->formatStateUsing(function ($state) {
                                                return $state ? ucwords(str_replace('_', ' ', $state)) : '-';
                                            }),
                                        TextEntry::make('rejected_note')
                                            ->label('Reason for Cancellation')
                                            ->visible(function ($record) {
                                                return $record ? $record->status === 'rejected' : false;
                                            }),
                                    ])
                                    ->columns(3),
                            ])
                            ->columnSpan(2),
                        Group::make()
                            ->schema([
                                Section::make('Shipping Address')
                                    ->label('Order Status')
                                    ->schema([
                                        TextEntry::make('')
                                            ->default(function ($record) {
                                                $shippingAddress = [$record->order->shipping_first_name, $record->order->shipping_last_name, $record->order->shipping_address_1, $record->order->shipping_address_2, $record->order->shippingCity->name, $record->order->shippingState->name, $record->order->shipping_postal_code ?? ''];
                                                $phone = '';
                                                if (!empty($record->order->shipping_phone_code)) {
                                                    $phone = '(' . $record->order->shipping_phone_code . ') ' . $record->order->shipping_phone_number;
                                                } else {
                                                    $phone = $record->order->shipping_phone_number;
                                                }

                                                $shippingAddress[] = $phone;
                                                return implode(', ', array_filter($shippingAddress));
                                            })
                                            ->columnSpan(1),
                                    ]),
                                Section::make('Purchase Order')
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.purchase-order-invoice')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                                Section::make('PS Invoice')
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.invoice-viewer')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                                Section::make('DPharma Invoice')
                                    ->visible(function ($record) {
                                        return !empty($record->payout_path) ? true : false;
                                    })
                                    ->schema([
                                        ViewEntry::make('media')
                                            ->view('filament.pc.infolist.components.dpharma-invoice-viewer')
                                            ->extraAttributes(['class' => 'flex flex-col gap-2']),
                                    ])
                                    ->columnSpan(1),
                            ])
                            ->columns(1),
                    ])
                    ->columns(3),
                Group::make()
                    ->columnSpanFull()
                    ->extraAttributes(['style' =>  'margin: 0px !important; padding: 0px !important;'])
                    // ->heading(function ($record) {
                    //     return 'Items';
                    // })
                    ->schema([
                        Group::make()->schema(function ($record) {
                            return [
                                ViewEntry::make('my')
                                    ->view('filament.pc.infolist.components.sub-order-detail')
                                    ->viewData(['order' => $record]),
                            ];
                        })->extraAttributes(['style' => 'margin: 0px !important; padding: 0px !important;']),
                    ]),
            ];
        });
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table
            ->query(
                SubOrder::with(['order', 'order.subOrders', 'user', 'orderProducts', 'orderProducts.product'])
                    ->where('user_id', $user)
                    ->withCount([
                        'orderProducts as order_products_count' => function (Builder $query) {
                            $query->select(DB::raw('COALESCE(COUNT(*), 0)'));
                        },
                    ]),
            )

            ->defaultSort('id', 'desc')
            // ->recordUrl(null)
            ->emptyStateHeading('No Orders records found')
            ->columns([
                TextColumn::make('order.order_number')
                    ->url(function (SubOrder $record): ?string {
                        return route('filament.pc.resources.orders.view', ['record' => $record->id]);
                    })
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('order.user.clinicData.clinic_number')
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.facilities.show', ['user_id' => $record->order->user_id ?? null]))
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Facility ID')->sortable()->toggleable()->searchable(),

                TextColumn::make('order.user.clinicData.clinic_name')->label('Facility Name')->sortable()->searchable()->toggleable(),
                TextColumn::make('order.created_at')->label('Order Date')
                    ->formatStateUsing(function ($state) {
                        $user = Filament::auth()->user()->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    })
                    ->sortable()->toggleable(),
                TextColumn::make('order_products_count')->label('Items')->sortable()->toggleable(),
                TextColumn::make('total_sub_order_value')
                    ->label('Order Total')
                    ->prefix('RM ')
                    ->formatStateUsing(function ($state) {
                        return number_format($state, 2);
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('payment_type')
                    ->label('Credit Line')
                    ->getStateUsing(function (SubOrder $record) {
                        return $record->payment_type === 'credit_line' ? 'Yes' : 'No';
                    })
                    ->color(function (string $state) {
                        return $state === 'Yes' ? 'success' : 'danger';
                    })
                    ->icon(function (string $state) {
                        return $state === 'Yes' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('status')
                    ->searchable()
                    ->label('Order Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'accepted' => 'bi-patch-check-fill',
                            'cancelled' => 'bi-patch-check-fill',
                            'delivered' => 'bi-patch-check-fill',
                            'in_transit'  => 'heroicon-o-truck',
                            'ready_for_pickup' => 'heroicon-o-archive-box',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color = config("constants.order_status.color.{$status}", '#424242');

                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';
                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),
            ])
            ->defaultSort('id', 'desc')
            ->actionsColumnLabel('Action')
            // ->headerActions([ExportAction::make()->exporter(OrderExporter::class)->label('Export Orders')])
            ->filters([
                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('order.user.clinicData', 'clinic_name')->options(fn() => ClinicDetail::whereNotNull('clinic_name')->pluck('clinic_name', 'id')->toArray()),
                SelectFilter::make('status')
                    ->label('Order Status')
                    ->multiple()
                    ->options([
                        'pending' => 'Pending',
                        'rejected' => 'Rejected',
                        'accepted' => 'Accepted',
                        'cancelled' => 'Cancelled',
                        'in_transit' => 'In Transit',
                        'delivered' => 'Delivered',

                    ]),
                SelectFilter::make('payment_type')
                    ->label('Payment Type')
                    ->options([
                        'credit_line' => 'Credit Line',
                        'pay_now' => 'Online',
                    ])
                    ->attribute('payment_type'),
                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('From')
                            ->maxDate(fn($get) => $get('to_date') ?? now())
                            ->closeOnDateSelection()
                            ->placeholder('Select from date'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('To')
                            ->minDate(fn($get) => $get('from_date'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->placeholder('Select to date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '>=', $date))
                            )
                            ->when(
                                $data['to_date'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '<=', $date))
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from_date'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from_date'])->toFormattedDateString();
                        }
                        if ($data['to_date'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['to_date'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ])

            ->actions([
                Action::make('chat_with_facility')
                    ->label(false)
                    ->tooltip('Chat With Facility')
                    ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')->size('sm')->iconButton()
                    ->color(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();
                        $unreadCount = $thread
                            ? \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count()
                            : 0;
                        return $unreadCount > 0 ? 'success' : 'primary';
                    })

                    ->extraAttributes(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        $unreadCount = $thread
                            ? \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count()
                            : 0;

                        $iconColor = $unreadCount > 0 ? 'rgb(5, 161, 5)' : 'rgb(0, 70, 104)';
                        $borderColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';
                        $textColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';
                        // dd($textColor);
                        return [
                            'class' => "border-2 rounded-lg {$textColor}",
                            'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                        ];
                    })
                    ->visible(function ($record) {
                        $cancelled = $record->status == 'cancelled';
                        $user = getUser(auth()->user());
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        $visible = true;
                        if ($record->status == 'rejected') {
                            $fromTime = $record->rejected_at;
                            $currentTime = now();
                            $diffInHours = Carbon::parse($fromTime)->diffInHours($currentTime);
                            if ($diffInHours < 24) {
                                $visible = true;
                            } else {
                                $visible = false;
                            }
                        }
                        return ($record->status != 'cancelled' && ($visible)) && (auth()->user()->hasRole('Super Admin') || $isPharmaceuticalCompany || auth()->user()->can('all-orders_chat'));
                    })
                    ->badge(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        if (! $thread) {
                            return null;
                        }

                        $unreadCount = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->count();

                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->badgeColor(function ($record) {
                        $user = Filament::auth()->user();

                        $thread = \App\Models\Thread::where('order_id', $record->order_id)
                            ->where(function ($query) use ($user) {
                                $query->where('receiver_id', $user->id)
                                    ->orWhere('sender_id', $user->id);
                            })
                            ->first();

                        if (! $thread) {
                            return 'gray';
                        }

                        $hasUnread = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                            ->where('from_id', '!=', $user->id)
                            ->where('is_read', false)
                            ->exists();

                        return $hasUnread ? 'success' : 'primary';
                    })
                    ->action(function ($record) {
                        $user = getUser(auth()->user());
                        $orderUserId = $record->order->user_id;
                        $thread = \App\Models\Thread::firstOrCreate(
                            [
                                'order_id' => $record->order_id,
                                'receiver_id' => $user->id,
                            ],
                            [
                                'receiver_id' => $user->id,
                                'sender_id' => $orderUserId,
                            ]
                        );
                        Cookie::queue('type', 'allorder-list', 60); // minutes
                        return redirect()->route('filament.pc.resources.orders.chat', [
                            'thread_id' => $thread->id,
                            'record' => $record->id,
                            'type' => request()->query('type') ?? 'allorder-list'
                        ]);
                    }),
                Action::make('inTransit')
                    ->tooltip('In-Transit')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-truck')->size('sm')->iconButton()
                    ->label(false)
                    ->color('gray')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'owned' &&
                            $record->status === 'accepted'
                        );

                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_in transit'));
                    })
                    ->action(function ($record) {
                        $user = auth()->user();
                        $record->status = 'in_transit';
                        $record->in_transit_at = now();
                        $record->save();
                        $record->orderProducts()->update(['status' => 'in_transit']);
                        //Activtylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'in_transit',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status has been updated to in transit");  //by {$user->name}
                        //Activtylog end
                        //sending email to facility
                        $paymentType = match ($record->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };
                        if (!empty($record->order?->user->timezone)) {
                            $timezone = $record->order?->user->timezone;
                            $orderDate = Carbon::parse($record->order?->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        } else {
                            $orderDate = Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s');
                        }

                        $orderDetails = [
                            'Order Number'      => "#" . $record->order->order_number,
                            'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                            'Total Items'   => $record->orderProducts->count(),
                            'Supplier'   => $record->user?->name ?? 'N/A',
                            'Order Total'   => "RM " . number_format($record->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];
                        $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';

                        foreach ($orderDetails as $key => $value) {
                            $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                        }
                        $orderDetailsHtml .= '</table>';
                        $emailContentData = [
                            "USER" => $record->order->user->name,
                            "ORDERNUMBER" => $record->order->order_number,
                            "ORDERDETAIL" => $orderDetailsHtml
                        ];
                        sendMailNotification($record->order->user, "PC_ORDER_INTRANSIT", $emailContentData);
                        //$superAdminEmails  = getAdminData();
                        // foreach ($superAdminEmails as $key => $adminData) {
                        //     $adminEmailContentData = [
                        //         "USER" => $adminData->name,
                        //         "ORDERNUMBER" => $record->order->order_number
                        //     ];
                        //     sendMailNotification($adminData, "ADMIN_ORDER_INTRANSIT", $adminEmailContentData);
                        // }

                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    }),
                Action::make('readyForPickup')
                    ->tooltip('Ready For Pickup')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-archive-box')->size('sm')->iconButton()
                    ->label(false)
                    ->color('gray')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'dpharma' &&
                            $record->status === 'accepted'
                        );
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_ready for pickup'));
                    })
                    ->action(function ($record) {
                        $user = auth()->user();
                        $record->status = 'ready_for_pickup';
                        $record->ready_for_pickup_at = now();
                        $record->save();
                        $record->orderProducts()->update(['status' => 'ready_for_pickup']);
                        $response = (new ShippingService())->createOrderRequest($record);

                        //Activtylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'ready_for_pickup',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status updated to ready for pickup"); //by {$user->name}
                        //Activtylog end
                        //Send Email to Facility
                        $paymentType = match ($record->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };
                        if ($record->order?->user->timezone) {
                            $timezone = $record->order?->user->timezone;
                            $orderDate = Carbon::parse($record->order->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        } else {
                            $orderDate = Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s');
                        }

                        $orderDetails = [
                            'Order Number'      => "#" . $record->order->order_number,
                            'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                            'Total Items'   => $record->orderProducts->count(),
                            'Supplier'   => $record->user?->name ?? 'N/A',
                            'Order Total'   => "RM " . number_format($record->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];
                        $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';

                        foreach ($orderDetails as $key => $value) {
                            $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                        }
                        $orderDetailsHtml .= '</table>';
                        $emailContentData = [
                            "USER" => $record->order->user->name,
                            "ORDERNUMBER" => $record->order->order_number,
                            "ORDERDETAIL" => $orderDetailsHtml
                        ];
                        sendMailNotification($record->order->user, "PC_ORDER_READY_FOR_PICKUP", $emailContentData);

                        // $superAdminEmails  = getAdminData();
                        // foreach ($superAdminEmails as $key => $adminData) {
                        //     $adminEmailContentData = [
                        //         "USER" => $adminData->name,
                        //         "ORDERNUMBER" => $record->order->order_number
                        //     ];
                        //     sendMailNotification($adminData, "ADMIN_ORDER_READY_FOR_PICKUP", $adminEmailContentData);
                        // }
                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    }),
                Action::make('view')
                    // ->url(fn(SubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->id, 'type' => 'allorder'])) // Change order_id to record
                    ->url(function (SubOrder $record) {
                        Cookie::queue('source', 'allorder');
                        return route('filament.pc.resources.orders.view', ['record' => $record->id, 'type' => 'allorder']);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->color('gray')
                    ->visible(function ($record) {
                        $user = auth()->user();

                        return ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_view details'));
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('Order Details')
                    ->label(false),


                Action::make('delivered')
                    ->icon('heroicon-o-check')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('Delivered')
                    ->label(false)
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = ($record->status == 'in_transit' || $record->status == 'ready_for_pickup') ? true : false;
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_delivered'));
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->action(function ($record) {
                        $paymentType = match ($record->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };
                        if ($record->order?->user->timezone) {
                            $timezone = $record->order?->user->timezone;
                            $orderDate = Carbon::parse($record->order->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        } else {
                            $orderDate = Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s');
                        }

                        $orderDetails = [
                            'Order Number'      => "#" . $record->order->order_number,
                            'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                            'Total Items'   => $record->orderProducts->count(),
                            'Supplier'   => $record->user?->name ?? 'N/A',
                            'Order Total'   => "RM " . number_format($record->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];
                        $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';
                        foreach ($orderDetails as $key => $value) {
                            $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                        }
                        $orderDetailsHtml .= '</table>';
                        $emailContentData = [
                            "USER" => $record->order->user->name,
                            "ORDERNUMBER" => $record->order->order_number,
                            "ORDERDETAIL" => $orderDetailsHtml
                        ];
                        sendMailNotification($record->order->user, "PC_ORDER_DELIVERED", $emailContentData);

                        $oldStatus = $record->status;
                        $record->status = 'delivered';
                        $record->deliver_at = now();
                        $record->save();
                        $record->orderProducts()->update(['status' => 'delivered']);
                        $mainOrder = Order::with('subOrders')->where('id', $record->order_id)->first();
                        $allDelivered = $mainOrder && $mainOrder->subOrders->isNotEmpty() &&
                            $mainOrder->subOrders->every(fn($subOrder) => $subOrder->status === 'delivered');
                        if ($allDelivered) {
                            DB::table('orders')
                                ->where('id', $record->order_id)
                                ->update(['status' => 'delivered']);
                        }

                        //Activtylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => $oldStatus,
                                ],
                                'attributes' => [
                                    'status' => 'delivered',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status updated to delivered"); //{$user->name}
                        //Activtylog end
                        //send mail to admin and facility


                        // $superAdminEmails  = getAdminData();
                        // foreach ($superAdminEmails as $key => $adminData) {
                        //     $adminEmailContentData = [
                        //         "USER" => $adminData->name,
                        //         "ORDERNUMBER" => $record->order->order_number
                        //     ];
                        //     sendMailNotification($adminData, "ADMIN_ORDER_DELIVERED", $adminEmailContentData);
                        // }
                        //email sending ends

                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    })
                    ->color('success'),


                // ]),
            ])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([])]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
            'review' => Pages\ReviewOrder::route('/{record}/review'),
            'chat' => Pages\ChatOrder::route('/{record}/chat'),
        ];
    }
}
