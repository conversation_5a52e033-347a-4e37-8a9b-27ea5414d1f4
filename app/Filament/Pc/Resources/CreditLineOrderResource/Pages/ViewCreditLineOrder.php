<?php

namespace App\Filament\Pc\Resources\CreditLineOrderResource\Pages;

use App\Filament\Pc\Resources\CreditLineOrderResource;
use Filament\Actions;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Textarea;
use Filament\Navigation\NavigationItem;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components\FileUpload;
use App\Mail\PCActionMail\RejectOrderMail;
use App\Filament\Pc\Resources\OrderResource;
use Illuminate\Support\Carbon;
use Filament\Actions\Action;
use App\Models\Order;
use App\Models\ClinicCreditHistory;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\Cookie;
use App\Service\ShippingService;

class ViewCreditLineOrder extends ViewRecord
{
    protected static string $resource = CreditLineOrderResource::class;


    public function getTitle(): string
    {
        return '#' . $this->getRecord()->order->order_number;
    }

    public function getBreadcrumbs(): array
    {
        $orderType = request()->query('type');
        $routeName = 'filament.pc.resources.credit-line-orders.index';
        $title = 'Credit Line Orders';

        return [
            1 => 'Orders Management',
            route($routeName) => $title,
            3 => 'Order Details',
        ];
    }



    protected function getHeaderActions(): array
    {
        $user = Filament::auth()->user()->id;
        return [
            Actions\Action::make('upload_invoice')
                ->label(false)
                ->tooltip('Upload Invoice')
                ->icon('heroicon-o-document-text')->size('sm')->iconButton()
                ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                ->form([
                    TextInput::make('invoice_po_number')
                        ->label(new HtmlString("Invoice Number<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->validationMessages([
                            'required' => "The invoice number field is required.",
                        ])
                        ->default($this->getRecord()->invoice_po_number)
                        ->rules(['required']),
                    FileUpload::make('invoice')
                        ->label('Upload Invoice')
                        ->required()
                        // ->disk('s3')
                        ->directory('invoices')
                        ->acceptedFileTypes(['application/pdf', 'image/*'])
                        ->maxSize(5120)
                ])
                ->action(function ($data, $record) use ($user) {
                    if ($record) {

                        $oldInvoicePoNumber = $record->invoice_po_number;
                        // Remove the dd() for production
                        // dd($data);

                        $record->invoice_po_number = $data['invoice_po_number'];
                        $record->save();

                        // Delete old media
                        \App\Models\Media::where([
                            'model_type' => get_class($record),
                            'model_id' => $record->id,
                            'collection_name' => 'invoices'
                        ])->delete();

                        //Activitylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('invoice_po')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => ['invoice_po_number' => $oldInvoicePoNumber], // Use the captured old value
                                'attributes' => ['invoice_po_number' => $data['invoice_po_number']]
                            ])
                            ->log("Invoice has been uploaded for Order #{$record->order->order_number}"); //by {$user->name}
                        //Activitylog end

                        if (!empty($data['invoice'])) {
                            $record->addMediaFromUrl(Storage::disk('s3')->url($data['invoice']))
                                ->preservingOriginal()
                                ->toMediaCollection('invoices', 's3');

                            Notification::make()
                                ->title('Invoice Uploaded')
                                ->body('Invoice has been uploaded successfully.')
                                ->success()
                                ->send();
                        }
                    }
                })
                ->visible(function ($record) {
                    $user = auth()->user();
                    return $record->status == 'accepted' && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_upload invoice'));
                })
                ->color('primary'),

            Actions\Action::make('downloadInvoice')
                ->tooltip('Download Invoice')
                ->icon('heroicon-o-arrow-down-tray')
                ->size('sm')
                ->iconButton()
                ->extraAttributes([
                    'class' => 'border-2 border-success rounded-lg text-blue-900',
                    'style' => 'margin-left: inherit; border-color: rgb(0, 70, 104);'
                ])
                ->action(function ($record) {
                    $media = $record->getMedia('invoices')->last();

                    if ($media) {
                        try {
                            $url = $media->getTemporaryUrl(now()->addMinutes(5));
                            $fileName = $media->file_name;

                            //Activitylog start
                            activity()
                                ->causedBy(auth()->user())
                                ->useLog('invoice_download')
                                ->performedOn($record)
                                ->log("Invoice has been downloaded for Order #{$record->order->order_number}");
                            //Activitylog end

                            return response()->streamDownload(function () use ($url) {
                                echo file_get_contents($url);
                            }, $fileName);
                        } catch (\Exception $e) {
                            \Log::error('Error creating invoice download URL', [
                                'media_id' => $media->id,
                                'error' => $e->getMessage(),
                            ]);
                            Notification::make()
                                ->title('Error')
                                ->body('Unable to download invoice. Please try again later.')
                                ->danger()
                                ->send();
                            return null;
                        }
                    }

                    Notification::make()
                        ->title('Error')
                        ->body('No invoice found.')
                        ->danger()
                        ->send();
                    return null;
                })
                ->visible(function ($record) {
                    $user = auth()->user();
                    $conditionStatus = $record->status === 'accepted' && $record->getMedia('invoices')->isNotEmpty();
                    return $conditionStatus && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download invoice'));
                })
                ->label(false)
                ->color('primary'),

            Actions\Action::make('review_order')
                ->label('Confirm')

                // ->visible(fn() => $this->getRecord()->status == 'pending' || $this->getRecord()->status == null)
                ->visible(function () {
                    $record = $this->getRecord();
                    $user = auth()->user();
                    return ($record->status == 'pending' || $record->status == null)
                        && $record->order->orderProducts()->exists()
                        && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_confirm order')); // Or count() > 0 if you prefer
                })
                ->url(fn() => route('filament.pc.resources.orders.review', [
                    'record' => $this->getRecord()->id,
                    'type' => request()->query('type') ?? 'allorder'
                ]))

                ->color('primary'),
            Actions\Action::make('reject')
                ->label('Reject Order')
                ->modalHeading(fn($record) => 'Reason for Rejecting Order #' . $record->order->order_number)
                ->visible(function () {
                    $user = auth()->user();
                    return ($this->getRecord()->status === 'pending'  || $this->getRecord()->status == null) && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_reject order'));
                })
                ->modalSubmitAction(
                    fn($action) =>
                    $action->label('Save')->color('primary') // Change button text & color
                )
                ->form([
                    Textarea::make('reason')
                        ->label('')
                        ->validationMessages([
                            'required' => "The reason field is required.",
                        ])
                        // ->label(new HtmlString("Reason<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->rules(['required'])
                ])
                ->color('danger')
                ->action(function ($data, $record) use ($user) {
                    DB::beginTransaction();

                    try {
                        $record->update(['status' => 'rejected', 'rejected_at' => now(), 'rejected_note' => $data['reason']]);
                        $record->orderProducts()->update([
                            'status' => 'rejected'
                        ]);


                        self::updateOrderStatus($record->order_id);
                        if ($record->total_dpharma_points_used > 0) {
                            DB::table('dpharma_points')->insert([
                                'user_id'          => $record->order->user_id,
                                'description'      => 'Order Rejected - Points Successfully Returned',
                                'points'           => $record->total_dpharma_points_used, // Adjust points logic as needed
                                'redeem'           => null,
                                'balance'          => DB::raw("(
                                    COALESCE(
                                        (SELECT balance FROM dpharma_points
                                        WHERE user_id = {$record->order->user_id}
                                        ORDER BY created_at DESC LIMIT 1),
                                        0
                                    ) + {$record->total_dpharma_points_used}
                                )"),
                                'created_at'       => now(),
                                'updated_at'       => now(),
                                'reference_id'     => $record->id,
                                'reference_value'  => 'suborder',
                            ]);
                        }
                        if ($record->payment_type == 'credit_line') {
                            $lastClinicCredit = ClinicCreditHistory::where('supplier_id', getUser(auth()->user())->id)->where('facility_id', (int)$record->order->user_id)->orderBy('id', 'desc')->first();

                            if (!empty($lastClinicCredit)) {

                                $remainingAmount = 0;
                                if ($lastClinicCredit->remaining_amount == 0) {
                                    $remainingAmount = $record->total_amount + $lastClinicCredit->total_credit_amount;
                                    $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                } else {
                                    $remainingAmount = $lastClinicCredit->remaining_amount + $record->total_amount;
                                }

                                $clinicCredit = new ClinicCreditHistory();
                                $clinicCredit->facility_id = $record->order->user_id;
                                $clinicCredit->supplier_id = getUser(auth()->user())->id;
                                $clinicCredit->credit_amount = $record->total_amount;
                                $clinicCredit->debit_amount = 0;
                                $clinicCredit->edit_credit = 0;
                                $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used - $record->total_amount;
                                $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount;
                                $clinicCredit->remaining_amount = $remainingAmount;
                                $clinicCredit->reference_id = $record->id;
                                $clinicCredit->reference_value = 'suborder';
                                $clinicCredit->action = 'Order Rejected';
                                $clinicCredit->save();
                            }
                        }


                        DB::commit();
                        Notification::make()
                            ->title('Order Rejected')
                            ->body('The rejected reason has been saved successfully.')
                            ->danger()
                            ->send();

                        // Mail::to($record->order->user->email)->send(new RejectOrderMail($record));
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Notification::make()
                            ->title('Rejection Failed')
                            ->body('An error occurred while approving the order. Please try again.')
                            ->danger()
                            ->send();
                    }
                }),
            Actions\Action::make('chat_with_facility')
                ->label(false)
                ->tooltip('Chat With Facility')
                ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')->size('sm')->iconButton()
                ->extraAttributes(function ($record) {
                    $user = Filament::auth()->user();

                    $thread = \App\Models\Thread::where('order_id', $record->order_id)
                        ->where(function ($query) use ($user) {
                            $query->where('receiver_id', $user->id)
                                ->orWhere('sender_id', $user->id);
                        })
                        ->first();

                    $unreadCount = $thread
                        ? \App\Models\ThreadMessage::where('thread_id', $thread->id)
                        ->where('from_id', '!=', $user->id)
                        ->where('is_read', false)
                        ->count()
                        : 0;

                    $iconColor = $unreadCount > 0 ? 'rgb(5, 161, 5)' : 'rgb(0, 70, 104)';
                    $borderColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';
                    $textColor = $unreadCount ? 'rgb(0, 168, 89)' : 'rgb(0, 70, 104)';

                    return [
                        'class' => "border-2 rounded-lg {$textColor}",
                        'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                    ];
                })
                ->visible(function ($record) {
                    $user = auth()->user();
                    return $record->order->status !== 'cancelled' && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_chat'));
                })
                ->badge(function ($record) {
                    $user = Filament::auth()->user();

                    $thread = \App\Models\Thread::where('order_id', $record->order_id)
                        ->where(function ($query) use ($user) {
                            $query->where('receiver_id', $user->id)
                                ->orWhere('sender_id', $user->id);
                        })
                        ->first();

                    if (! $thread) {
                        return null;
                    }

                    $unreadCount = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                        ->where('from_id', '!=', $user->id)
                        ->where('is_read', false)
                        ->count();

                    return $unreadCount > 0 ? $unreadCount : null;
                })
                ->badgeColor(function ($record) {
                    $user = Filament::auth()->user();

                    $thread = \App\Models\Thread::where('order_id', $record->order_id)
                        ->where(function ($query) use ($user) {
                            $query->where('receiver_id', $user->id)
                                ->orWhere('sender_id', $user->id);
                        })
                        ->first();

                    if (! $thread) {
                        return 'gray';
                    }

                    $hasUnread = \App\Models\ThreadMessage::where('thread_id', $thread->id)
                        ->where('from_id', '!=', $user->id)
                        ->where('is_read', false)
                        ->exists();

                    return $hasUnread ? 'success' : 'primary';
                })
                ->action(function ($record) {
                    $user = Filament::auth()->user();

                    $orderUserId = $record->order->user_id;

                    $thread = \App\Models\Thread::firstOrCreate(
                        [
                            'order_id' => $record->order_id,
                            'receiver_id' => $user->id,
                        ],
                        [
                            'receiver_id' => $user->id,
                            'sender_id' => $orderUserId,
                        ]
                    );
                    Cookie::queue('type', 'credit_line_detail', 60); // minutes
                    Cookie::queue('orderId', $record->id, 60); // minutes
                    return redirect()->route('filament.pc.resources.orders.chat', [
                        'thread_id' => $thread->id,
                        'record' => $record->id,
                        'type' => request()->query('type') ?? 'credit_line_detail'
                    ]);
                }),
            // ->color('primary') // disables default Filament color styling
            Actions\Action::make('readyForPickup')
                ->tooltip('Ready For Pickup')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                ->icon('heroicon-o-archive-box')->size('sm')->iconButton()
                ->label(false)
                ->color('primary')
                ->requiresConfirmation()
                ->modalHeading('Confirm')
                ->extraAttributes(function ($record) {

                    $iconColor = 'rgba(96, 165, 250, 1)';
                    $borderColor = 'rgba(96, 165, 250, 1)';
                    $textColor = 'rgba(96, 165, 250, 1)';


                    return [
                        'class' => "border-2 rounded-lg {$textColor}",
                        'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                    ];
                })
                ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                ->modalSubmitActionLabel('Confirm')
                ->visible(function ($record) {
                    $user = auth()->user();
                    $conditionWareHouse = (
                        $record->warehouse_type === 'dpharma' &&
                        $record->status === 'accepted'
                    );
                    return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_in transit'));
                })
                ->action(function ($record) {
                    $paymentType = match ($record->payment_type) {
                        'pay_now'    => 'Pay Now',
                        'pay_later'  => 'Pay Later',
                        'credit_line' => 'Credit Line',
                        default      => 'Unknown',
                    };

                    if ($record->order?->user->timezone) {
                        $timezone = $record->order?->user->timezone;
                        $orderDate = Carbon::parse($record->order?->created_at)->timezone($timezone)->format('d M, Y h:i A');
                    } else {
                        $orderDate = Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s');
                    }
                    $orderDetails = [
                        'Order Number'      => "#" . $record->order->order_number,
                        'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                        'Total Items'   => $record->orderProducts->count(),
                        'Supplier'   => $record->user?->name ?? 'N/A',
                        'Order Total'   => "RM " . number_format($record->total_amount, 2),
                        'Payment Type'  => $paymentType ?? 'N/A',
                    ];
                    $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';

                    foreach ($orderDetails as $key => $value) {
                        $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                    }
                    $orderDetailsHtml .= '</table>';
                    $emailContentData = [
                        "USER" => $record->order->user->name,
                        "ORDERNUMBER" => $record->order->order_number,
                        "ORDERDETAIL" => $orderDetailsHtml
                    ];
                    sendMailNotification($record->order->user, "PC_ORDER_READY_FOR_PICKUP", $emailContentData);

                    $user = auth()->user();
                    $record->status = 'ready_for_pickup';
                    $record->ready_for_pickup_at = now();
                    $record->save();
                    $record->orderProducts()->update(['status' => 'ready_for_pickup']);
                    $response = (new ShippingService())->createOrderRequest($record);

                    //Activtylog start
                    activity()
                        ->causedBy(auth()->user())
                        ->useLog('order_status_update')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'status' => 'accepted',
                            ],
                            'attributes' => [
                                'status' => 'ready_for_pickup',
                            ],
                        ])
                        ->log("Order #{$record->order->order_number} status updated to ready for pickup"); //by {$user->name}
                    //Activtylog end
                    ///same like Intransit

                    // $superAdminEmails  = getAdminData();
                    // foreach ($superAdminEmails as $key => $adminData) {
                    //     $adminEmailContentData = [
                    //         "USER" => $adminData->name,
                    //         "ORDERNUMBER" => $record->order->order_number
                    //     ];
                    //     sendMailNotification($adminData, "ADMIN_ORDER_READY_FOR_PICKUP", $adminEmailContentData);
                    // }

                    Notification::make()
                        ->body('Order status updated successfully.')
                        ->success()
                        ->send();

                    return null;
                }),
            Actions\Action::make('inTransit')
                ->tooltip('In-Transit')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                ->icon('heroicon-o-truck')->size('sm')->iconButton()
                ->label(false)
                ->color('gray')
                ->extraAttributes(function ($record) {

                    $iconColor = 'rgba(96, 165, 250, 1)';
                    $borderColor = 'rgba(96, 165, 250, 1)';
                    $textColor = 'rgba(96, 165, 250, 1)';


                    return [
                        'class' => "border-2 rounded-lg {$textColor}",
                        'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                    ];
                })
                ->requiresConfirmation()
                ->modalHeading('Confirm')
                ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                ->modalSubmitActionLabel('Confirm')
                ->visible(function ($record) {
                    $user = auth()->user();
                    $conditionAccepted = (
                        $record->warehouse_type === 'owned' &&
                        $record->status === 'accepted'
                    );
                    // return $conditionAccepted;
                    return $conditionAccepted && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_in transit'));
                })
                ->action(function ($record) {
                    $user = auth()->user();
                    $record->status = 'in_transit';
                    $record->in_transit_at = now();
                    $record->save();

                    //Activitylog start
                    activity()
                        ->causedBy(auth()->user())
                        ->useLog('order_status_update')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'status' => 'accepted',
                            ],
                            'attributes' => [
                                'status' => 'in_transit',
                            ],
                        ])
                        ->log("Order #{$record->order->order_number} status has been updated to in transit");
                    //Activitylog end

                    Notification::make()
                        ->body('Order status updated successfully.')
                        ->success()
                        ->send();

                    return null;
                })
                ->color('primary'),
            Actions\Action::make('delivered')
                ->tooltip('Delivered')
                ->label('')
                ->icon('heroicon-o-check')->size('sm')->iconButton()
                // ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                ->extraAttributes(function ($record) {

                    $iconColor = 'rgb(5, 161, 5)';
                    $borderColor = 'rgb(0, 168, 89)';
                    $textColor = 'rgb(0, 168, 89)';


                    return [
                        'class' => "border-2 rounded-lg {$textColor}",
                        'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                    ];
                })
                ->visible(function ($record) {
                    $user = auth()->user();
                    $conditionWareHouse = ($record->status == 'in_transit' || $record->status == 'ready_for_pickup') ? true : false;
                    return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_delivered'));
                })
                ->requiresConfirmation()
                ->modalHeading('Confirm')
                ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                ->modalSubmitActionLabel('Confirm')

                ->action(function ($record) {
                    $user = auth()->user();

                    $record->status = 'delivered';
                    $record->deliver_at = now();
                    $record->save();

                    //Activitylog start
                    activity()
                        ->causedBy(auth()->user())
                        ->useLog('order_status_update')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'status' => 'in_transit',
                            ],
                            'attributes' => [
                                'status' => 'delivered',
                            ],
                        ])
                        ->log("Order #{$record->order->order_number} status has been updated to delivered");
                    //Activitylog end

                    Notification::make()
                        ->body('Order status updated successfully.')
                        ->success()
                        ->send();

                    return null;
                })
                ->color('success'),

            Actions\Action::make('downloadPo')
                ->visible(function () {
                    $user = auth()->user();
                    return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download po');
                })
                ->tooltip('Download PO')
                ->label('')
                ->icon('heroicon-o-document-arrow-down')->size('sm')->iconButton()
                // ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                ->extraAttributes(function ($record) {

                    $iconColor =  'rgb(0, 70, 104)';
                    $borderColor = 'rgb(0, 70, 104)';
                    $textColor =  'rgb(0, 70, 104)';

                    return [
                        'class' => "border-2 rounded-lg {$textColor}",
                        'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};"

                    ];
                })
                // ->icon('heroicon-o-arrow-down-tray')
                ->action(function ($record) {
                    $user = auth()->user();
                    if (!($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download po'))) {
                        abort(403);
                    }
                    $fileName = $record->invoice_path;
                    $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                    $disk = Storage::disk('s3');
                    $filePath = $invoicePath . $fileName;
                    \Log::info('image url: ' . $filePath);
                    \Log::info('file exists: ' . $disk->exists($filePath));

                    if ($fileName && $disk->exists($filePath)) {

                        //Activitylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_po_download')
                            ->performedOn($record)
                            ->log("PO downloaded for Order #{$record->order->order_number}");
                        //Activitylog end

                        return new StreamedResponse(function () use ($disk, $filePath) {
                            echo $disk->get($filePath);
                        }, 200, [
                            'Content-Type' => 'application/pdf',
                            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                        ]);
                    }
                    Notification::make()
                        ->title('File not found')
                        ->body('The requested invoice file is unavailable or could not be located.')
                        ->danger()
                        ->send();

                    return null;
                })
                ->color('primary'),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(CreditLineOrderResource::getUrl('index')),


        ];
    }

    public static function updateOrderStatus($orderId)
    {
        try {
            $statuses = DB::table('sub_orders')
                ->where('order_id', $orderId)
                ->pluck('status')
                ->toArray();


            if (empty($statuses)) {
                return;
            }
            $allStatusesUpdated = !in_array('pending', $statuses);

            if ($allStatusesUpdated) {
                $orderStatus = 'delivered'; // All statuses updated
            } elseif (count($statuses) === 1 && (in_array('rejected', $statuses) || in_array('accepted', $statuses))) {
                $orderStatus = 'in_transit'; // Only one status is accepted or rejected
            } elseif (in_array('rejected', $statuses) || in_array('accepted', $statuses)) {
                $orderStatus = 'in_transit';
            } else {
                $orderStatus = 'pending'; // Fallback status
            }
            $mainOrder = Order::with('subOrders')->where('id', $orderId)->first();
            $allDelivered = $mainOrder && $mainOrder->subOrders->isNotEmpty() &&
                $mainOrder->subOrders->every(fn($subOrder) => $subOrder->status === 'delivered');

            if ($allDelivered) {
                $orderStatus = 'delivered';
            } else {
                if (count($statuses) === 1 && in_array('rejected', $statuses)) {
                    $orderStatus = 'cancelled'; // Only one status is accepted or rejected
                } else {
                    $orderStatus = 'in_transit'; // Only one status is accepted or rejected
                }
                // $orderStatus = 'in_transit';
            }
            // $orderStatus = 'in_transit';

            DB::table('orders')
                ->where('id', $orderId)
                ->update(['status' => $orderStatus]);
        } catch (\exception $th) {
            return 0;
        }
    }

    protected static function rejectReason($data, $records)
    {
        foreach ($records as $record) {
            $record->reject_reason = $data['reject_reason'];
            $record->status = 'rejected';
            $record->save();
        }

        Notification::make()->title('Order Rejected')->success()->send();
    }
}
