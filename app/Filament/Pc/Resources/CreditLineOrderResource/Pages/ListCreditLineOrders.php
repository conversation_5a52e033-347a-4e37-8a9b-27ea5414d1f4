<?php

namespace App\Filament\Pc\Resources\CreditLineOrderResource\Pages;

use App\Filament\Pc\Resources\CreditLineOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\ExportAction;
use App\Filament\Exports\OrderClinicExporter;

class ListCreditLineOrders extends ListRecords
{
    protected static string $resource = CreditLineOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->exporter(OrderClinicExporter::class)
                ->color('primary')
                ->visible(function () {
                    $user = auth()->user();

                    return $user->hasRole('Super Admint') || $user->hasRole('Pharmaceutical Company') ||  $user->can('credit-line-orders_export');
                })
                ->label('Export Orders')
        ];
    }
    public function getTitle(): string
    {
        return 'Credit Line Orders';
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Orders Management",
            // $this->getResource()::getUrl('index') => "Credit Line Orders",
            // 2 => "List",

        ];
    }
}
