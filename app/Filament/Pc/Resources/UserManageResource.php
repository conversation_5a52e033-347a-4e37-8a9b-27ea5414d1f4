<?php

namespace App\Filament\Pc\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\AccountType;
use Filament\Resources\Resource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Spatie\Permission\Models\Role;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Facades\Http;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\TextInput;
use Filament\Navigation\NavigationGroup;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\Pages\CreateRecord;
use Filament\Tables\Actions\ExportBulkAction;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use App\Filament\Pc\Resources\UserManageResource\Pages;
use App\Filament\Pc\Resources\UserManageResource\RelationManagers;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\Unique;

class UserManageResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';


    public const STATUS_ACTIVE = 1;

    public const STATUS_INACTIVE = 0;


    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || $user->can('users_view')
            || $user->can('users_create') || $user->can('users_update') || $user->can('users_delete');
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || $user->can('users_create');
    }

    public static function canEdit($record): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || $user->can('users_update');
    }

    public static function canDelete($record): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || $user->can('users_delete');
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('')
                    ->schema([
                        Grid::make(5)
                            ->schema([
                                FileUpload::make('photo')
                                    ->label('')
                                    ->image()
                                    ->imageEditor()
                                    ->circleCropper()
                                    ->directory('users')
                                    ->avatar()
                                    ->columnSpanFull()
                                    ->alignCenter()
                                    ->rules(['required', 'image', 'mimes:jpg,jpeg,png', 'max:2048']) // Image validation
                                    ->validationMessages([
                                        'required' => 'The avatar field is required.',
                                        'image' => 'The file must be an image.',
                                        'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                        'max' => 'The image must not exceed 2MB.'
                                    ]),

                                TextInput::make('name')
                                    ->label(new HtmlString("Full Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->placeholder("Enter Name")
                                    ->rules(['required', 'max:50', 'regex:/^[A-Za-z0-9\s]+$/'])
                                    ->validationMessages([
                                        'required' => 'The first name field is required.',
                                        'max' => 'The first name must not exceed 50 characters.',
                                        'regex' => 'The name must contain only letters, numbers, and spaces.',
                                    ]),
                                TextInput::make('email')
                                    ->label(new HtmlString("Email <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->placeholder("Enter Email")
                                    ->rules(['required', 'email'])
                                    ->unique(
                                        table: User::class,
                                        column: 'email',
                                        ignoreRecord: true,
                                        modifyRuleUsing: function (Unique $rule) {
                                            return $rule->whereNull('deleted_at');
                                        }
                                    )
                                    ->validationMessages([
                                        'required' => 'Please enter an email address.',
                                        'email' => 'Please enter a valid email address.',
                                        'unique' => 'This email is already registered.'
                                    ])->afterStateUpdated(
                                        function (Get $get, Set $set) {
                                            $set('email', \Str::lower($get('email')));
                                        }
                                    )
                                    ->disabled(fn() => $form->getOperation() === 'edit')
                                    ->live(),


                                TextInput::make('phone')
                                    ->numeric()
                                    ->prefix('+60')
                                    ->live()
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->validationMessages([
                                        'digits_between' => 'The phone number must be between :min and :max characters.',
                                        'required' => 'The phone number field is required.',
                                    ])
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">Enter phone number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Phone Number')
                                    ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->rules(['required', 'digits_between:8,12']),


                                Select::make('role')
                                    ->label(new HtmlString("Role <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->placeholder("Select Role")
                                    // ->options(
                                    //     \Spatie\Permission\Models\Role::where('created_by', function ($query) {
                                    //         $query->select('id')
                                    //             ->from('roles')
                                    //             ->where('name', 'Pharmaceutical Company');
                                    //     })->where('panel', 'pc')->where('is_active', true)->pluck('name', 'name')
                                    // )
                                    // ->options(
                                    //     \Spatie\Permission\Models\Role::where('created_by', auth()->id())
                                    //         ->where('panel', 'pc')
                                    //         ->where('is_active', true)   
                                    //         ->pluck('name', 'name')
                                    // )
                                    ->options(
                                        function () {
                                            $userId = getUser(auth()->user())->id;
                                            return  Role::where('is_active', true)->where('panel', 'pc')->where('created_by', $userId)->pluck('name', 'name')->map(function ($label, $value) {
                                                return preg_replace('/^\d+-\s*/', '', $label); // Display cleaned label
                                            });
                                        }
                                        // \Spatie\Permission\Models\Role::where('created_by', function ($query) {
                                        //     $query->select('id')
                                        //         ->from('roles')
                                        //         ->where('name', 'Pharmaceutical Company');
                                        // })->where('panel', 'pc')->where('is_active', true)->pluck('name', 'name')
                                    )

                                    ->rules('required')
                                    ->validationMessages([
                                        'required' => 'Please select a role.',
                                    ])
                                    ->default(function ($record) {
                                        return $record?->roles->first()?->name;
                                    })
                                    ->dehydrated(false),
                            ])
                            ->columns(2),
                    ])

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            // ->query(
            //     User::whereIn('parent_id', function ($query) {
            //         $query->select('id')
            //             ->from('users')
            //             ->whereExists(function ($roleQuery) {
            //                 $roleQuery->select(DB::raw(1))
            //                     ->from('model_has_roles')
            //                     ->whereColumn('model_has_roles.model_id', 'users.id')
            //                     ->whereIn('model_has_roles.role_id', function ($subQuery) {
            //                         $subQuery->select('id')
            //                             ->from('roles')
            //                             ->where('name', 'Pharmaceutical Company');
            //                     });
            //             });
            //     })
            // )
            ->query(function () {
                $user = auth()->user();
                $query = User::query()
                    ->with(['roles', 'createdBy']);

                if (!$user->hasRole('Pharmaceutical Company')) {
                    return $query->where('created_by', $user->id);
                }
                return $query->where('parent_id', getUser($user)->id);
                //return dd(User::where('parent_id', getUser(auth()->user())->id)->where('created_by', auth()->user()->id)->with(['roles', 'createdBy']));
            })->emptyStateHeading('No User records found')
            ->columns([
                TextColumn::make('id')
                    ->label('User ID')
                    ->formatStateUsing(fn(string $state): string => "#{$state}")
                    ->toggleable()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Name')
                    ->toggleable()
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        // Check if the record has a photo stored in S3
                        $avatar = $record->photo
                            ? '<img src="' . Storage::disk('s3')->url($record->photo) . '" alt="Avatar" class="inline-block w-8 h-8 mr-2 rounded-full">'
                            : '<img src="' . asset('images/default-avatar.png') . '" class="inline-block w-8 h-8 mr-2 rounded-full">';

                        return new HtmlString($avatar . e($state));
                    }),

                TextColumn::make('email')->sortable()->searchable()->toggleable(),
                TextColumn::make('roles.name')->label('Role')->default('-')
                    ->sortable(query: function ($query, $direction) {
                        return $query->join('model_has_roles', 'users.id', '=', 'model_has_roles.model_id')
                            ->join('roles', 'model_has_roles.role_id', '=', 'roles.id')
                            ->orderBy('roles.name', $direction)
                            ->select('users.*');
                    })->searchable()->toggleable()
                    ->formatStateUsing(function ($state) {
                        return collect(explode(',', $state))
                            ->map(fn($name) => preg_replace('/^\s*\d+-\s*/', '', trim($name)))
                            ->implode(', ');
                    }),
                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Created By')
                    ->formatStateUsing(function ($record) {
                        return $record->createdBy ? "{$record->createdBy->name}" : 'N/A';
                    })->searchable()->toggleable()->sortable(),
                Tables\Columns\TextColumn::make('created_at')->searchable()->toggleable()->sortable()->dateTime('M d, Y | h:i A'),
                ToggleColumn::make('is_active')->label('Status')
                    ->visible(function () {
                        $user = getUser(auth()->user());
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('users_change status');
                    })
                    ->toggleable()
                    ->sortable()
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company']))
                    ->afterStateUpdated(function ($record) {
                        if(!$record->is_active) {
                            \App\Filament\Admin\Resources\UserManageResource::logoutUserById($record->id);
                            Notification::make()
                                ->success()
                                ->title('User has been deactivated and logged out successfully.')
                                ->send();
                        } else {
                            Notification::make()
                                ->success()
                                ->title('User has been activated successfully.')
                                ->send();
                        }
                    }),

            ])
            ->defaultSort('id', 'desc')
            ->filters([

                SelectFilter::make('role')
                    ->label('Role')
                    ->options(function () {
                        return \Spatie\Permission\Models\Role::query()
                            ->whereNotIn('name', ['Super Admin', 'Clinic'])
                            ->where('panel', 'pc')
                            ->where('created_by', auth()->id())
                            ->pluck('name', 'name')
                            ->mapWithKeys(function ($name, $key) {
                                $displayName = preg_replace('/^\s*\d+-\s*/', '', trim($name));
                                return [$key => $displayName];
                            });
                    })
                    ->searchable()
                    ->preload()
                    ->query(function ($query, $data) {
                        if ($data['value']) {
                            $query->whereHas('roles', function ($q) use ($data) {
                                $q->where('name', $data['value']);
                            });
                        }
                    }),
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Action')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company'])),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',]),
                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->tooltip(function (Model $record) {
                        return "Delete {$record->parent_name}";
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title('User Deleted')
                            ->title('The users have been deleted successfully.'),
                    ),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $records->each->delete();
                        Notification::make()
                            ->success()
                            // ->title('Users Deleted')
                            ->title('The users have been deleted successfully.')
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('active')
                    ->label('Active')
                    ->color('success')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_ACTIVE; // Use is_active instead of status
                                $record->save();
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been activated successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to activate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactive')
                    ->label('Inactive')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_INACTIVE; // Use is_active instead of status
                                $record->save();
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been deactivated successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to deactivate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserManages::route('/'),
            'create' => Pages\CreateUserManage::route('/create'),
            'view' => Pages\ViewUserManage::route('/{record}'),
            'edit' => Pages\EditUserManage::route('/{record}/edit'),
        ];
    }
}
