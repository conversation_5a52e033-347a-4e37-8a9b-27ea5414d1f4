<?php

namespace App\Filament\Pc\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\SupportTicket;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Pc\Resources\SupportTicketReceivedResource\Pages;
use App\Filament\Pc\Resources\SupportTicketReceivedResource\RelationManagers;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages\ConversionDetailsReceived;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages\EditSupportTicketReceived;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages\ListSupportTicketReceiveds;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketReceivedResource\Pages\CreateSupportTicketReceived;
use App\Models\ClinicDetail;
use App\Models\PcDetail;
use App\Models\SubOrder;
use App\Models\SupportTicketMessage;
use App\Models\Thread;
use App\Models\User;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;

class SupportTicketReceivedResource extends Resource
{
    protected static ?string $model = SupportTicket::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Facilities Support';

    protected static ?string $breadcrumb = 'Support';

    protected static ?string $navigationGroup = 'Support';

    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        //auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facilities-support_view')
            || $user->can('facilities-support_mark as closed');
    }

    // public static function canCreate(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-receiveds_create');
    // }

    // public static function canUpdate(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-receiveds_update');
    // }

    // public static function canDelete(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-receiveds_delete');
    // }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }


    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->query(function (Builder $query) {
                $user = auth()->user();
                $baseQuery = SupportTicket::withUnreadCount();

                if ($user->parent_id === null) {
                    return $baseQuery->where('receiver_id', $user->id);
                } else {
                    return $baseQuery->where(function ($query) use ($user) {
                        $query->where('receiver_id', $user->id)
                            ->orWhere('receiver_id', $user->parent_id);
                    });
                }
            })->emptyStateHeading('No facilities support ticket records found')
            ->recordUrl(function (SupportTicket $record): string {
                return self::getUrl('details', ['record' => $record]);
            })
            ->columns([
                TextColumn::make('id')
                    ->label('Ticket ID')
                    ->sortable()
                    ->prefix('TKT-')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('unread_messages_count')
                    ->label('Unread Messages')
                    ->sortable()
                    ->toggleable()
                    ->formatStateUsing(fn($state) => $state > 0 ? "$state Unread" : 'All Read'),

                TextColumn::make('order.order_number')
                    ->url(function (SupportTicket $record): ?string {
                        if (
                            $record->order_id &&
                            (
                                auth()->user()->hasRole('Pharmaceutical Company') ||
                                auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            $subOrder = SubOrder::where('order_id', $record->order_id)->first();
                            if ($subOrder) {
                                return route('filament.pc.resources.orders.view', ['record' => $subOrder->id]);
                            }
                        }
                        return null;
                    })
                    ->tooltip(function (SupportTicket $record) {
                        if (
                            !$record->order_id ||
                            (
                                !auth()->user()->hasRole('Pharmaceutical Company') &&
                                !auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            return 'You do not have permission to view this order.';
                        }
                        return null;
                    })
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('sender.name')
                    ->label('Raised From')                                                    
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->label('Created Date')
                    ->sortable()->toggleable()->searchable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),
                TextColumn::make('email')->searchable()->sortable()
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('category.name')->searchable()
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('subject')->searchable()
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->limit(10)
                    ->tooltip(fn($record) => $record->subject)
                    ->suffix('...'),
                TextColumn::make('closed_at')
                    ->label('Closed Date')
                    ->sortable()->toggleable()->searchable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        if (is_null($state) || $state === '' || $state === '-') {
                            return '-';
                        }
                        try {
                            return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                                ->setTimezone($timeZone)
                                ->format('M d, Y | h:i A');
                        } catch (\Exception $e) {
                            return '-';
                        }
                    })
                    ->default('-'), 
                TextColumn::make('status')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'open' => 'success',
                        'closed' => 'danger',
                    })->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('unread_status')
                    ->label('Unread Messages')
                    ->options([
                        'read' => 'All Read',
                        'unread' => 'Has Unread',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!isset($data['value'])) {
                            return $query;
                        }

                        return match ($data['value']) {
                            'read' => $query->whereNotExists(function ($query) {
                                $query->select(DB::raw(1))
                                    ->from('support_ticket_messages')
                                    ->whereColumn('support_ticket_messages.support_ticket_id', 'support_tickets.id')
                                    ->where('is_read', false);
                            }),
                            'unread' => $query->whereExists(function ($query) {
                                $query->select(DB::raw(1))
                                    ->from('support_ticket_messages')
                                    ->whereColumn('support_ticket_messages.support_ticket_id', 'support_tickets.id')
                                    ->where('is_read', false);
                            }),
                            default => $query,
                        };
                    }),
                // SelectFilter::make('account_type')
                //     ->label('Account Type')
                //     ->searchable()
                //     ->multiple()
                //     ->options(function () {
                //         return \Spatie\Permission\Models\Role::pluck('name', 'name')->toArray();
                //     })
                //     ->query(function (Builder $query, array $data): Builder {
                //         if (empty($data['values'])) {
                //             return $query;
                //         }

                //         return $query->where(function ($query) use ($data) {
                //             $query->whereHas('sender', function ($q) use ($data) {
                //                 $q->whereNull('parent_id')
                //                     ->whereHas('roles', function ($roleQuery) use ($data) {
                //                         $roleQuery->whereIn('name', $data['values']);
                //                     });
                //             });

                //             $query->orWhereHas('sender', function ($q) use ($data) {
                //                 $q->whereNotNull('parent_id')
                //                     ->whereHas('parent.roles', function ($parentRoleQuery) use ($data) {
                //                         $parentRoleQuery->whereIn('name', $data['values']);
                //                     });
                //             });
                //         });
                //     }),

                SelectFilter::make('sender_id')
                    ->label('Raised From')
                    ->multiple()
                    ->preload()
                    ->options(function () {
                        $authUser = auth()->user();
                        $authId = $authUser->id;
                        $parentId = $authUser->parent_id ?? null;

                        // Collect all tickets where receiver is the user or their parent
                        $tickets = \App\Models\SupportTicket::with('sender')
                            ->where(function ($query) use ($authId, $parentId) {
                                $query->where('receiver_id', $authId);
                                if ($parentId) {
                                    $query->orWhere('receiver_id', $parentId);
                                }
                            })
                            ->get();

                        return $tickets
                            ->unique('sender_id')
                            ->mapWithKeys(function ($ticket) {
                                if ($ticket->sender) {
                                    return [$ticket->sender->id => $ticket->sender->name];
                                }
                                return [];
                            })
                            ->toArray();
                    }),
                SelectFilter::make('category_id')
                    ->label('Category')
                    ->multiple()
                    ->relationship('category', 'name')
                    ->preload(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->multiple()
                    ->options([
                        'open' => 'Open',
                        'closed' => 'Closed',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereIn('status', $data['values']);
                    }),
                Filter::make('created_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Created Date')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['date'], fn($q) => $q->whereDate('created_at', $data['date']));
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' . Carbon::parse($data['date'])->toFormattedDateString();
                    }),
                Filter::make('closed_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Closed Date')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['date'], fn($q) => $q->whereDate('closed_at', $data['date']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' .  Carbon::parse($data['date'])->toFormattedDateString();
                    }),

            ])
            ->actionsColumnLabel('Action')
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->label('')
                    ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record])),
                Action::make('Chat')
                    ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')
                    ->size('sm')->tooltip('Chat')
                    ->iconButton()
                    ->visible(function () {
                        $user = auth()->user();
                        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('facilities-support_chat');
                    })
                    ->color(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    })
                    ->extraAttributes(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return [
                            'class' => 'border-2 rounded-lg ' . ($unreadCount > 0 ? 'text-green-500' : 'text-blue-500'),
                            'style' => 'margin-left: inherit; border-color: ' . ($unreadCount > 0 ? 'rgb(34, 197, 94)' : 'rgb(0, 70, 104)'),
                        ];
                    })
                    ->label('')
                    ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record]) . '#conversation-header')
                    ->badge(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->badgeColor(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSupportTicketReceiveds::route('/'),
            'create' => CreateSupportTicketReceived::route('/create'),
            // 'edit' => EditSupportTicketReceived::route('/{record}/edit'),
            'details' => ConversionDetailsReceived::route('assigned/{record}/details'),


        ];
    }
}
