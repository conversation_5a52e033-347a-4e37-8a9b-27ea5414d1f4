<?php

namespace App\Filament\Pc\Resources\ProductResource\Widgets;

use Carbon\Carbon;
use App\Models\Product;
use Filament\Forms\Get;
use App\Models\PcDetail;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use App\Models\ProductRelation;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use App\Models\ProductRelationStock;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Placeholder;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Concerns\InteractsWithTable;
use Awcodes\TableRepeater\Components\TableRepeater;
use Illuminate\Support\Facades\Auth;

class BatchWiseProducts extends BaseWidget
{
    use InteractsWithTable;
    protected int|string|array $columnSpan = 'full';

    protected static string $view = 'filament.pc.resources.product-resource.widgets.batch-wise-widget';

    #[On('refresh')]
    public function refreshTable(): void
    {
        $this->resetTable();
    }

    public function viewBatches($record = null)
    {
        // dd($this);
        // If record is passed directly, use it
        // Otherwise, try to get it from the mountAction arguments
        // if (!$record) {
           
        //     $record = $this->mountedTableActionRecord ?? null;
            
        //     if (!$record && isset($this->mountedTableActionData['record'])) {
        //         $recordId = $this->mountedTableActionData['record'];
        //         $userId = getUser(Auth::user())->id;
        //         $productRelationId = ProductRelation::where('product_id', $recordId)->where('user_id', $userId)->first()->id;
               
        //         $record = Product::with([
        //             'productData', 
        //             'batches' => function($query) use ($userId, $productRelationId) {
        //                 $query->where('user_id', $userId)->where('product_relation_id', $productRelationId);
        //             }, 
        //             'unit'
        //         ])->find($recordId);
        //     }
        // }

        // if (!$record) {
        //     info("No record found in viewBatches action");
        //     return;
        // }

        return Action::make('viewBatches')
            ->modalHeading(function () use ($record) {
                if ($record->productData->is_batch_wise_stock) {
                    return "Batch Wise" . " - " . $record->name;
                }
                return "Stock Wise" . " - " . $record->name;
            })
            ->form(function () use ($record) {
                dd($record);
                if ($record->productDataForPc(getUser(Auth::user()))->is_batch_wise_stock) {
                    return [
                        Section::make()->schema([
                            TextInput::make('name')
                                ->default($record->name)
                                ->disabled(),
                            TextInput::make('sku')
                                ->label('Stock Keeping Unit (SKU)')
                                ->default($record->sku)
                                ->disabled(),
                            Toggle::make('productData.is_batch_wise_stock')
                                ->inline(false)
                                ->live()
                                ->disabled()
                                ->default($record->productData->is_batch_wise_stock)
                                ->reactive(),
                        ])->columns(3),
                        Section::make('Batches')->schema([
                            TableRepeater::make('batches')
                                ->schema([
                                    TextInput::make('batch_name')
                                        ->label('Batch Name')
                                        ->required(),
                                    TextInput::make('available_stock')
                                        ->label('Available Stock')
                                        ->numeric()
                                        ->required(),
                                    DatePicker::make('expiry_date')
                                    ->placeholder('Enter Expiry Date')
                                        ->label('Expiry Date')
                                        ->required(),
                                ])
                                ->columnSpanFull()
                                ->defaultItems(1)
                                ->headers([
                                    Header::make('batch_name')
                                        ->label('Batch Name'),
                                    Header::make('available_stock')
                                        ->label('Available Stock'),
                                    Header::make('expiry_date')
                                        ->label('Expiry Date'),
                                ])
                        ])
                    ];
                }
            })
            ->action(function (array $data, $record) {
                // Handle the form submission

            });
    }

    public function openBatchDetails($productId)
    {
        $userId = getUser(Auth::user())->id;
        $productRelationId = ProductRelation::where('product_id', $productId)->where('user_id', $userId)->first()->id;
        $product = Product::with([
            'productData', 
            'batches' => function($query) use ($userId, $productId, $productRelationId) {
                $query->where('user_id', $userId)->where('product_id', $productId)->where('products_relation_id', $productRelationId);
            }, 
            'unit'
        ])->find($productId);
        // dd($product);
        if (!$product) {
            info("Product not found: " . $productId);
            return;
        }

        // Call viewBatches directly with the product
        $this->viewBatches($product);
    }

    public function updateNormalStock($productId, $newStock)
    {
        $userId = Auth::id(); // Optional: to avoid key collision between users
        $cacheKey = 'data';

        $bulkStockData = cache()->get($cacheKey, []);

        $bulkStockData['normal'][$productId]['stock'] = $newStock;

        cache()->put($cacheKey, $bulkStockData, now()->addMinutes(30));
        session()->put('data.normal.' . $productId, ['stock' => $newStock]);
    }
    protected function applySearchToTableQuery(Builder $query): Builder
    {
        $this->applyColumnSearchesToTableQuery($query);

        if (filled($search = $this->getTableSearch())) {
            $query->where(function (Builder $query) use ($search) {
                $query->where('products.name', 'ILIKE', "%$search%")
                    ->orWhereHas('productData', function (Builder $query1) use ($search) {
                        $query1->where('sku', 'ILIKE', "%$search%");
                    })
                    ->orWhereHas('unit', function ($query2) use ($search) {
                        $query2->where('name', 'ILIKE', "%$search%");
                    });

                // Include is_batch_wise_stock search manually
                if (str_contains(strtolower($search), 'batch')) {
                    $query->orWhereHas('relationStocks', function ($query3) {
                        $query3->where('product_relation_stocks.is_batch_wise_stock', true);
                    });
                }

                if (str_contains(strtolower($search), 'product')) {
                    $query->orWhereHas('relationStocks', function ($query4) {
                        $query4->where('product_relation_stocks.is_batch_wise_stock', false);
                    });
                }
            });
        }

        return $query;
    }

    public function table(Table $table): Table
    {
        return $table
            ->header(new HtmlString(""))
            //->poll('10s')
            ->paginated(false)

            ->query(function () {

                $userId = getUser(Auth::user())->id;

                $productIds = ProductRelation::query()
                    ->whereUserId($userId)
                    ->pluck('product_id')
                    ->toArray();

                $products = Product::query()
                    ->where('status', 'approved')
                    ->whereIn('id', $productIds)
                    ->with('productData', 'relationStocks');

                return $products;
            })
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->label('Product Name'),
                TextColumn::make('sku')
                    ->formatStateUsing(fn($record) => $record->productData?->sku ?? '')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->WhereHas('productData', function (Builder $q) use ($search) {
                            $q->where('sku', 'ILIKE', "%$search%");
                        });
                    })
                    ->label('Stock Keeping Unit (SKU)'),
                TextColumn::make('unit.name')
                    ->searchable()
                    ->visible()
                    ->formatStateUsing(function ($record) {
                        return $record->unit->name;
                    }),
                TextColumn::make('productData')
                    ->label('Stock')
                    ->formatStateUsing(function ($record) {
                        $user = getUser(Auth::user());
                        $userId = $user->id;
                        $productRelation = $record->productDataForPc($userId);
                        $productRelationId = $productRelation?->id;
                        $stockData = $record->relationStocks;
                        // dd($stockData);
                        $isBatchWise = $stockData?->where('product_relation_id', $productRelationId)->first()?->is_batch_wise_stock;

                        // Check for cached/session values first
                        if ($isBatchWise) {
                            // For batch-wise products, check session data
                            $sessionData = session()->get('data.batch_data_.' . $record->id);
                            $cacheData = cache()->get('data', [])['batch_data_'][$record->id] ?? null;
                           
                            if (!empty($sessionData['batches']) || !empty($cacheData['batches'])) {
                                // Use cached batch data
                                $batches = $sessionData['batches'] ?? $cacheData['batches'] ?? [];
                                $stock = collect($batches)->sum('available_stock');
                            } else {
                                // Fall back to database values - filter by user_id and product_id
                                $productRelationId = ProductRelation::where('product_id', $record->id)->where('user_id', $userId)->first()->id; 
                                $filteredBatches = $record->batches->filter(function ($batch) use ($userId, $record, $productRelationId) {
                                    return $batch->user_id == $userId && $batch->product_id == $record->id && $batch->products_relation_id == $productRelationId;
                                });
                                
                                $stock = $filteredBatches->sum('available_stock');
                            }
                        } else {
                            // For normal products, check session data
                            $sessionData = session()->get('data.normal.' . $record->id);
                            $cacheData = cache()->get('data', [])['normal'][$record->id] ?? null;
                            
                            if (isset($sessionData['stock']) || isset($cacheData['stock'])) {
                                // Use cached stock value
                                $stock = $sessionData['stock'] ?? $cacheData['stock'];
                                
                            } else {
                                // Fall back to database values
                                $stock = ProductRelationStock::where('product_relation_id', $productRelationId)->first()?->stock;
                               
                            }
                        }

                        if ($isBatchWise) {
                            return new HtmlString('<div class="inline-flex items-center justify-between w-32 h-10 px-2 py-1 bg-white border border-gray-300 rounded-md">
                                <input
                                    type="text"
                                    value="' . ($stock ?? 0) . '"
                                    class="w-full text-sm text-gray-800 truncate bg-transparent border-none cursor-default focus:outline-none focus:ring-0"
                                    readonly
                                />
                                <a href="#"
                                   wire:click="openBatchDetails(' . $record->id . ')"
                                   class="p-1 transition rounded shrink-0 hover:bg-gray-200">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-gray-500 cursor-pointer"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536M9 11l6-6 3.536 3.536-6 6H9v-3.536z"
                                        />
                                    </svg>
                                </a>
                            </div>');
                        } else {
                            return new HtmlString('<div class="inline-flex items-center justify-between w-32 h-10 px-2 py-1 bg-white border border-gray-300 rounded-md">
                                <input
                                    type="number"
                                    value="' . ($stock ?? 0) . '"
                                    wire:change="updateNormalStock(' . $record->productDataForPc(Auth::id())->id . ', $event.target.value)"
                                    class="w-full text-sm text-gray-800 truncate bg-transparent border-none focus:outline-none focus:ring-0"
                                />
                            </div>');
                        }

                        // Display inline editable input for non-batch products

                    })
                    ->action(
                        Action::make('viewBatches')
                            ->label('View Batches')
                            ->visible(function ($record) {
                                $user = getUser(Auth::user());
                                $userId = $user->id;
                                $productRelation = $record->relationStocks->where('product_relation_id', $record->productDataForPc($userId)->id);
                                return $productRelation?->first()?->is_batch_wise_stock;
                            })
                            ->form(function ($record) {
                                $user = getUser(Auth::user());
                                $userId = $user->id;
                                $productRelation = $record->relationStocks->where('product_relation_id', $record->productDataForPc($userId)->id);
                                if ($productRelation?->first()->is_batch_wise_stock) {
                                    return [
                                        Section::make()->schema([
                                            TextInput::make('name')->disabled(),
                                            TextInput::make('sku')
                                                ->formatStateUsing(function ($record) {
                                                    return $record->productDataForPc(Auth::id())->sku ?? "";
                                                })
                                                ->disabled(),
                                            Toggle::make('productData')
                                                ->formatStateUsing(function ($record) {
                                                    return true;
                                                })
                                                ->inline(false)
                                                ->disabled()
                                                ->extraAttributes(['style' => 'margin-top: 40px !important;', 'class' => 'mt-4']),
                                        ])
                                            ->columns(3),
                                        Section::make()->schema([
                                            TextInput::make('available_stock')
                                                ->formatStateUsing(function ($record) use ($productRelation) {
                                                    return $productRelation?->first()?->total_stock;
                                                })
                                                ->disabled(),
                                        ])
                                            ->columnSpanFull()
                                            ->visible(function ($record, Get $get) {
                                                return ! $get('productData.is_batch_wise_stock');
                                            }),
                                        TableRepeater::make('batches')
                                            ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                // dd('jere');
                                                return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                                    ->extraAttributes([
                                                        'style' => 'border: none !important; box-shadow: none !important;'
                                                    ]);
                                            })
                                            ->reorderable(false)
                                            ->addActionAlignment(Alignment::End)
                                            ->headers([
                                                Header::make('Batch Name'),
                                                Header::make('Available Stock'),
                                                Header::make('Low Stock Trigger Value'),
                                                Header::make('Action'),
                                            ])
                                            ->schema([
                                                TextInput::make('batch_name'),
                                                TextInput::make('available_stock')
                                                    ->rules(['numeric', 'gt:0', 'integer']),
                                                DatePicker::make('expiry_date')
                                                    ->displayFormat('M d,Y')
                                                    ->native(false)
                                                    ->minDate(today())
                                                    ->placeholder('Select the expiry date')
                                                    ->label('Expiry Date'),
                                            ])
                                            ->columns(3)
                                            ->visible(function ($record, Get $get) use ($productRelation) {
                                                return $productRelation?->first()?->is_batch_wise_stock;
                                            }),
                                    ];
                                }
                            })
                            ->mountUsing(function ($form, $record) {
                                if (!$form) {
                                    info("Form is null for record ID: " . ($record->id ?? 'unknown'));
                                    return;
                                }

                                $sessionData = session()->get('data.batch_data_.' . $record->id);
                                $normalSessionData = session()->get('data.normal.' . $record->id, []);

                                $productData = $sessionData['productData'] ?? [];
                                $availableStock = $sessionData['available_stock'] ?? '';
                                $expiryDate = $sessionData['expiry_date'] ?? '';
                                $sessionBatchData = $sessionData['batches'] ?? [];

                                // Check if record has batches
                                $batches = [];
                                if (!empty($sessionBatchData)) {
                                    $batches = $sessionBatchData;
                                } elseif ($record->batches && $record->batches->count() > 0) {
                                    $userId = getUser(Auth::user())->id;
                                    $productRelationId = ProductRelation::where('product_id', $record->id)->where('user_id', $userId)->first()->id;
                                   
                                    $filteredBatches = $record->batches->filter(function ($batch) use ($userId, $record, $productRelationId) {
                                        return $batch->user_id == $userId && $batch->product_id == $record->id && $batch->products_relation_id == $productRelationId;
                                    });
                                    
                                    $batches = $filteredBatches->map(fn($batch) => [
                                        'batch_name' => $batch->batch_name,
                                        'available_stock' => $batch->available_stock,
                                        'expiry_date' => $batch->expiry_date,
                                    ])->toArray();
                                }

                                try {
                                    $form->fill([
                                        'name' => $record->name,
                                        'sku' => $record->sku,
                                        'productData' => $productData,
                                        'batches' => $batches,
                                        'available_stock' => $availableStock ?? 0,
                                        'expiry_date' => $expiryDate ?? null,
                                        'stock' => $normalSessionData['stock'] ?? $record->productData->stock ?? null,
                                        'expires_on_after' => $normalSessionData['expires_on_after'] ?? $record->productData->expires_on_after ?? null,
                                    ]);
                                } catch (\Exception $e) {
                                    info("Error filling form: " . $e->getMessage());
                                }
                            })
                            ->action(function (array $data, $record) {

                                session()->forget('data.batch_data_.' . $record->id);
                                $cacheKey = 'data';
                                $bulkStockData = cache()->get($cacheKey, []);

                                $bulkStockData['batch_data_'][$record->id] = $data;

                                cache()->put($cacheKey, $bulkStockData, now()->addMinutes(30));
                                // cache()->put('data.batch_data_.' . $record->id, $data);
                                session()->put('data.batch_data_.' . $record->id, $data);
                            })
                            ->modalWidth(function ($record) {
                                return MaxWidth::Full;
                            }),


                    ),
                TextColumn::make('productData.created_at')
                    ->label('')
                    ->formatStateUsing(function ($record) {
                        $relationData = $record->productDataForPC(getUser(Auth::user())?->id);

                        if (empty($relationData?->id)) {
                            return "-";
                        }

                        $stockData = ProductRelationStock::where('product_relation_id', $relationData->id)->first();
                        $packSize = $stockData?->wholesale_pack_size;

                        if (empty($packSize)) {
                            return "-";
                        }

                        $text = '';

                        $text .= $packSize . ' ';

                        $containerName = $record->container?->name;
                        if (!empty($containerName)) {
                            $text .= Str::plural($containerName) . ' of ';
                        }

                        $quantityPerUnit = $record->quantity_per_unit;
                        $foamName = $record->foam?->name;

                        if (!empty($quantityPerUnit) && !empty($foamName)) {
                            $qty = $quantityPerUnit * (int) $packSize;
                            $doses = Str::plural($foamName);
                            $text .= "( {$qty} {$doses} ) ";
                        }

                        return $text;
                    }),
                TextColumn::make('productData.is_batch_wise_stock')
                    ->label('Stock Type')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('relationStocks', function (Builder $query) use ($search) {
                            if (str_contains(strtolower($search), 'batch')) {
                                $query->where('product_relation_stocks.is_batch_wise_stock', true);
                            } elseif (str_contains(strtolower($search), 'product')) {
                                $query->where('product_relation_stocks.is_batch_wise_stock', false);
                            }
                        });
                    })
                    ->formatStateUsing(function ($record) {
                        $user = getUser(Auth::user());
                        $userId = $user->id;
                        $productRelation = $record->productDataForPc($userId);
                        $productRelationId = $productRelation?->id;
                        $stockData = $record->relationStocks;
                        return $stockData->where('product_relation_id', $productRelationId)->first()?->is_batch_wise_stock ? 'By Batch' : 'By Stock';
                    }),
            ]);
    }

    public function updateExpiryDate($productId, $newExpiryDate)
    {
        $existingData = session()->get('data.normal.' . $productId, []);
        $existingData['expires_on_after'] = $newExpiryDate;
        cache()->put('data.normal.' . $productId, $existingData);
        session()->put('data.normal.' . $productId, $existingData);
    }

    // public function updateStock($productId, $newStock)
    // {
    //     $existingData = session()->get('data.normal.' . $productId, []);
    //     $existingData['stock'] = $newStock;
    //     session()->put('data.normal.' . $productId, $existingData);
    // }

    public function updateStock($productId, $newStock)
    {
        $userId = Auth::id(); // Optional: to avoid key collision between users
        $cacheKey = 'data';

        $bulkStockData = cache()->get($cacheKey, []);

        $bulkStockData['normal'][$productId]['stock'] = $newStock;

        cache()->put($cacheKey, $bulkStockData, now()->addMinutes(30));
    }
}
