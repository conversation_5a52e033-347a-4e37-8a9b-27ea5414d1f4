<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Closure;
use App\Models\Unit;
use App\Models\User;
use App\Models\Brand;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Distributor;
use App\Models\GenericName;
use Illuminate\Support\Arr;
use Livewire\Attributes\On;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use App\Forms\Components\DragDrop;
use App\Rules\CaseSensitiveUnique;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Tabs;
use App\Mail\PcCreatedMasterProduct;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use App\Mail\PcCreatedExistingProduct;
use App\Service\TierValidationService;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use phpseclib3\File\ASN1\Maps\FieldID;
use App\Service\PriceManagementService;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Placeholder;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Pc\Resources\ProductResource;
use Filament\Forms\Concerns\InteractsWithForms;
use Awcodes\TableRepeater\Components\TableRepeater;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use DefStudio\SearchableInput\Forms\Components\SearchableInput;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CreateNewProduct extends CreateRecord
{

    protected ?string $heading = 'Add Product';

    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.create-new-product';

    public ?array $data = [];

    public  $commission;

    public $pcCommission;

    public $globalCommission;

    public $finalCommissionType;

    public $finalCommission;

    public $productData = null;

    public $batchData = null;

    public $uploadedImages = null;

    public ?array $images = [];

    public ?array $stockData = [];

    public ?array $priceData = [];

    public ?string $defaultFileName = "";

    public ?string $searchTerm = "";
    public ?string $tempSku = "";

    public $defaultIndex = 0;
    public $defaultImage = "";

    public bool $isBatchWise = false;

    public $activityData = [];

    protected $listeners = ['updateDefaultImage', 'handleChangeDefaultImage'];


    protected function getRedirectUrl(): string
    {
        return ProductResource::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.pc.resources.products.index')) => 'Products',
            "#" => 'Add Product',
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('create')),
        ];
    }



    public function refresh($uuid): void
    {
        $this->form->fill(["pcInfo_east.{$uuid}.min_quantity"]);

        // $this->fillForm();
    }

    public function handleChangeDefaultImage($fileName)
    {
        $this->defaultFileName = $fileName;
    }

    public function setDefaultImage($index)
    {
        $this->defaultIndex = $index;
    }

    public function removeMedia($mediaId)
    {
        if (!$this->record) {
            return false;
        }

        $media = Media::find($mediaId);
        if ($media && $media->model_id == $this->record->id) {
            $media->delete();

            // Update the form state
            $currentState = $this->form->getState();
            if (isset($currentState['images'])) {
                $currentState['images'] = array_filter($currentState['images'], function ($id) use ($mediaId) {
                    return $id !== $mediaId;
                });
                $this->form->fill($currentState);
            }

            return true;
        }
        return false;
    }

    public function clearLivewireTmpFiles()
    {
        $tmpPath = storage_path('app/livewire-tmp');
        if (File::exists($tmpPath)) {
            File::cleanDirectory($tmpPath);
        }
    }

    public function mount(): void
    {
        parent::mount();
        $userId = getUser(auth()->user())->id;
        $data = [
            'productData' => [
                'price_type' => 'fixed',
                'stock_type' => 'normal',
            ],
        ];

        $pcCommission = PcDetail::where('user_id', $userId)->first();
        $productCommission = !empty($this->data['product_data']['commission_amount']) ? $this->data['product_data']['commission_amount'] : 0;
        $this->commission = $pcCommission?->commission_percentage ?? 0;

        $this->finalCommissionType = $pcCommission->commission_type;
        $this->finalCommission = $this->commission;
        $this->searchTerm = session()->get('search_term_' . auth()->id()) ?? null;
        $this->tempSku = !empty($this->searchTerm) ? mb_substr($this->searchTerm, 0, 4) . "_"  . rand(100000, 999999) : null;
        $data['sku'] = $this->tempSku;
        $data['name'] = $this->searchTerm;
        session()->forget('search_term_' . auth()->id());

        $data['admin_fees'] = $this->finalCommission;
        $data['commission_type'] = $this->finalCommissionType;
        $data['pcInfo_east'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);

        $data['pcInfo_west'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);
        $this->form->fill($data);
    }

    public function form(Form $form): Form
    {
        return  $form->schema([
            TextInput::make('admin_fees')->hidden()->live(),
            TextInput::make('commission_type')->hidden(true)->live(),
            Section::make()->schema([
                TextInput::make('name')
                    ->placeholder('Product Name')
                    ->label('Product Name')
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Get $get, Set $set) {
                        if (empty($get('name')) || $get('name') == null || $get('name') == '') {
                            $set('sku', null);
                            $this->searchTerm = $get('name');
                            return;
                        }
                        $this->searchTerm = $get('name');
                        $set('sku', mb_substr($get('name'), 0, 4) . "_"  . rand(100000, 999999));
                    })
                    ->validationAttribute('product name')
                    ->label(new HtmlString('Product Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->rules([
                        'required',
                        'max:255',
                        new CaseSensitiveUnique(Product::class, 'name'),
                    ])
                    ->validationMessages([
                        'required' => 'product name is required',
                        'max' => 'product name must not exceed 255 characters',
                        'App\\Rules\\CaseSensitiveUnique' => 'prodcut name already exists',
                    ]),
                Select::make('brand_id')
                    ->native(false)
                    ->relationship('brand', 'name')
                    // ->extraAttributes(['style' => 'z-index: 65; position:relative'])
                    ->createOptionForm([
                        TextInput::make('name')
                            ->label(new HtmlString('Name<span class="text-red-500" style="color:red;">*</span>'))
                            ->rules([
                                'required',
                                'max:100',
                                // 'regex:/^[\w\s\p{P}]+$/u',
                                fn(Get $get) => new CaseSensitiveUnique(Brand::class, 'name', $get('id'))
                            ])
                            ->maxLength(100)
                            // ->unique(table: 'brands', column:'name', ignoreRecord: true)
                            ->validationMessages([
                                'required' => __('message.brand.required'),
                                // 'regex' => __('message.brand.regex'),
                                'max' => __('message.brand.max'),
                                'App\\Rules\\CaseSensitiveUnique' => __('message.brand.case_sensitive_unique'),
                            ]),
                        FileUpload::make('logo')->image()->directory('brand')->imageEditor()
                            ->markAsRequired(false)
                            ->label(new HtmlString('Logo<span class="text-red-500" style="color:red;">*</span>'))
                            ->maxSize(2048)
                            ->visibility('public')
                            ->hint('Supported formats: JPEG, JPG, PNG (Max 2MB), dimension: 580px x 580px')
                            ->disk('s3')
                            ->rules([
                                'required',
                                'image',
                                'mimes:jpeg,png,jpg',
                                // 'dimensions:width=580,height=580',
                            ])
                            ->validationMessages([
                                'required' => 'logo is required',
                                'image' => 'The file must be an image.',
                                'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                // 'dimensions' => 'The image must be 580px x 580px.',
                            ])
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])->required(),
                    ])

                    ->searchable(true)
                    ->preload()
                    ->label(new HtmlString('Brand<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('brand')
                    ->options(fn() => Brand::where('status', true)->pluck('name', 'id'))
                    ->rules(['required']),
                Select::make('category_id')
                    ->searchable(true)
                    ->preload()
                    ->label(new HtmlString('Category<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('category')
                    ->options(fn() => Category::whereNull('parent_id')->where('status', true)->pluck('name', 'id'))
                    ->reactive()
                    ->rules(['required']),
                Select::make('sub_category_id')
                    ->searchable()
                    ->preload()
                    ->label('Sub category')
                    ->live()
                    ->options(
                        fn(Get $get) => $get('category_id')
                            ? Category::where([
                                'parent_id' => $get('category_id'),
                                'status'    => true,
                            ])->pluck('name', 'id')
                            : []
                    ),

                SpatieMediaLibraryFileUpload::make('mda_document')
                    ->collection('mda-documents')
                    ->disk('s3')
                    ->visibility('public')
                    ->columnSpanFull()
                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                    ->maxSize(2048)
                    ->visible(function (Get $get) {
                        return (bool) Category::find($get('sub_category_id'))?->is_mda;
                    })
                    ->reactive(),
                Select::make('generic_name_id')
                    ->relationship('generic', 'name')
                    // ->extraAttributes(['style' => 'z-index: 48; position:relative;'])
                    ->createOptionForm([
                        Group::make()->schema([
                            TextInput::make('name')
                                ->label(new HtmlString('Name<span class="text-red-500" style="color:red;">*</span>'))
                                ->rules([
                                    'required',
                                    // 'regex:/^[\w\s\p{P}]+$/u', 
                                    'max:100',
                                    fn(Get $get) => new CaseSensitiveUnique(GenericName::class, 'name', $get('id'))
                                ])
                                ->validationMessages([
                                    'required' => __('message.generic_name.required'),
                                    // 'regex' => __('message.generic_name.regex'),
                                    'max' => __('message.generic_name.max'),
                                    'App\\Rules\\CaseSensitiveUnique' => __('message.generic_name.case_sensitive_unique'),
                                ]),
                        ])
                        // ->extraAttributes(['style' => 'z-index:150; position:relative;']),
                    ])
                    ->searchable(true)
                    ->preload()
                    ->label(new HtmlString('Generic Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('generic name')
                    ->rules(['required'])
                    ->options(fn() => GenericName::where('status', true)->pluck('name', 'id')),
                TextInput::make('sku')
                    ->placeholder('SKU')
                    ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('SKU')
                    ->rules(['required', 'max:255', 'unique:products,sku']),
                Select::make('distributor_id')
                    ->relationship('distributors', 'name')
                    ->createOptionForm([
                        Section::make()->schema([
                            TextInput::make('name')
                                ->label(new HtmlString("Distributor Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                ->maxLength(100)
                                ->placeholder('Enter Distributor Name')
                                ->rules([
                                    'required',
                                    // 'regex:/^[\w\s\p{P}]+$/u',
                                    'max:100',
                                    fn(Get $get) => new CaseSensitiveUnique(Distributor::class, 'name', $get('id'))
                                ])
                                ->validationMessages([
                                    'required' => __('message.distributor.required'),
                                    // 'regex' => __('message.distributor.regex'),
                                    'max' => __('message.distributor.max'),
                                    'App\\Rules\\CaseSensitiveUnique' => __('message.distributor.case_sensitive_unique'),
                                ]),

                        ]),
                    ])
                    ->searchable(true)
                    ->preload()
                    ->multiple()
                    ->label(new HtmlString('Distributor<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('distributor')
                    ->rules(['required'])
                    ->options(fn() => Distributor::where('status', true)->pluck('name', 'id')),
                Radio::make('is_prescription_required')
                    ->label('Prescription Required')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ])
                    ->inline()
                    ->formatStateUsing(fn() => 0)
                    ->default(0)

            ])->columns(2),
            Section::make()->schema([
                DragDrop::make('images')
                    ->multiple()
                    ->markAsRequired(false)
                    ->validationAttribute('images')
                    ->visibility('public')
                    ->label(new HtmlString('Images'))
                    ->collection('product-images')
                    ->dehydrated(true)
                    ->afterStateUpdated(function ($state) {
                        $this->images = array_filter((array) $state);
                    })
            ])->extraAttributes(['style' => 'z-index: 1 !important;position:relative;']),
            Section::make()
                ->heading('Product Description')
                ->schema([
                    Tabs::make('Product Details')
                        ->schema([
                            Tab::make('Product Description')
                                ->schema([
                                    RichEditor::make('product_description') // need to add migration
                                ]),
                            Tab::make('Key Ingredients')
                                ->schema([
                                    RichEditor::make('description_ingredients') // need to add migration
                                ]),
                            Tab::make('Storage Instructions')
                                ->schema([
                                    RichEditor::make('description_storage_instructions')  // need to add migration
                                ]),
                            Tab::make('Usage/Indication')
                                ->schema([
                                    RichEditor::make('description_indications')
                                ]),
                            Tab::make('Contradiction')
                                ->schema([
                                    RichEditor::make('description_contradictions')
                                ]),
                            Tab::make('How to Use')
                                ->schema([
                                    RichEditor::make('description_how_to_use') // need to add migration
                                ]),
                            Tab::make('Safety Information/Pregnancy')
                                ->schema([
                                    RichEditor::make('description_safety_information') // need to add migration
                                ]),
                            Tab::make('Dosage Information')
                                ->schema([
                                    RichEditor::make('description_dosage')
                                ]),
                            Tab::make('Side Effects')
                                ->schema([
                                    RichEditor::make('description_side_effects')
                                ]),

                        ])
                ]),
            Section::make('Stock Details')
                ->schema([

                    Select::make('container_id')
                        ->searchable(true)
                        ->preload()
                        ->label(fn() => PackagingToolTip::tooltip())
                        ->live()
                        ->rules(['required'])
                        ->validationAttribute('packaging')
                        ->options(fn() =>  Container::all()->pluck('name', 'id')),
                    Select::make('dosage_foams_id')
                        ->searchable(true)
                        ->preload()
                        ->label(new HtmlString('Product Form<span class="text-red-500" style="color:red;">*</span>'))
                        ->validationAttribute('Product Form')
                        ->rules(['required'])
                        ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
                    TextInput::make('productData.stock')
                        ->placeholder('Stock')
                        ->label(new HtmlString('Stock<span class="text-red-500" style="color:red;">*</span>'))
                        ->rules(['numeric', 'gt:0'])
                        ->visible(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal')
                        ->numeric()
                        ->maxValue(99999999)
                        ->requiredIf(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal', true)
                        ->rules(function (Get $get) {
                            $lowStock = $get('productData.low_stock');
                            if ($get('in_stock') == 'yes') {
                                return ['required', 'max:99999999', 'gte:' . $lowStock];
                            }
                            return ['sometimes', 'max:99999999', 'gte:' . $lowStock];
                        })
                        ->validationAttribute('stock'),
                    TextInput::make('quantity_per_unit')
                        ->placeholder('Volume')
                        ->validationAttribute('volume')
                        ->rules(['required', 'max:99999999', 'gt:0'])
                        ->numeric()
                        ->maxValue(99999999)
                        ->rules(['required', 'max:99999999'])
                        ->label(function (Get $get) {
                            if (!empty($get('container_id'))) {
                                $containerName = Container::find($get('container_id'))?->name;
                                return new HtmlString("Volume by {$containerName}<span class='text-red-500' style='color:red;'>*</span>");
                            }
                            return new HtmlString('Volume<span class="text-red-500" style="color:red;">*</span>');
                        }),
                    Select::make('unit_id')
                        ->searchable(true)
                        ->preload()
                        ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>'))
                        ->rules(['required', 'max:99999999'])
                        ->validationAttribute('volume unit')
                        ->options(fn() => Unit::all()->pluck('name', 'id')),
                    
                    TextInput::make('productData.low_stock')
                        ->placeholder('Low stock trigger value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return ['required', 'numeric', 'max:99999999', 'gt:0'];
                            }
                            return ['numeric', 'max:99999999', 'gt:0'];
                        })
                        ->numeric()
                        ->maxValue(99999999)
                        ->validationAttribute('low stock trigger value')
                        ->visible(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return true;
                            }
                            return false;
                        })
                        ->label(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return new HtmlString('Low stock trigger value<span class="text-red-500" style="color:red;">*</span>');
                            }
                            return 'Low stock trigger value';
                        }),
                    DatePicker::make('productData.expires_on_after')
                        ->format(getFormatedDateForPc())
                        ->label(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return new HtmlString('Expiry Date<span class="text-red-500" style="color:red;">*</span>');
                            }
                            return 'Expiry Date 416';
                        })
                        ->visible(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return true;
                            }
                            return false;
                        })
                        ->minDate(today())
                        ->displayFormat(function () {
                            $pcDetail = PcDetail::where('user_id', auth()->user()->id)->first();
                            return $pcDetail->date_format ?? 'd/m/Y';
                        })
                        ->validationAttribute('Expiry Date')
                        ->rules(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return ['required', 'date', 'after_or_equal:today'];
                            }
                            return ['sometimes', 'date', 'after_or_equal:today'];
                        }),
                    TextInput::make('weight')
                        ->label(new HtmlString('Weight (gms)<span class="text-red-500" style="color:red;">*</span>'))
                        ->numeric()
                        ->validationAttribute('weight')
                        ->placeholder('Weight (gms)')
                        ->rules(['required', 'gt:0', 'numeric'])
                ])->columns(5),


        ])->statePath('data');
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {
        if ($finalCommissionType == 'percentage') {
            $commission = $finalCommission * $price / 100;
            $earning = $price - $commission;
        } else {
            $earning =  $price - $finalCommission;
        }

        return number_format($earning, 2);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $userId = getUser(Auth::user())->id;
        $this->activityData = $data;
        unset($data['images']);
        $data['owner_id'] = $userId;
        $data['add_request_by'] = $userId;
        $this->productData['sku'] = $data['sku'];
        return $data;
    }

    protected function afterCreate(): void
    {
        $userId = getUser(Auth::user())->id;
        $product = $this->record;
        $defaultImage = $this->defaultIndex;
        $this->productData['user_id'] = $userId;
        $this->productData['requested_by'] = $userId;
        $this->productData['pc_approval'] = true;
        $this->productData['submitted_on'] = now();
        DB::transaction(function () use ($product, $defaultImage,) {
            if (!empty($defaultImage)) {
                $defaultImage = DB::table('media')->where(['model_id' => $this->record->id])->get()?->toArray()[$defaultImage]->id;
                $this->record->update(['default_image_id' => $defaultImage ?? null]);
            }

            if (!empty($this->productData) && is_array($this->productData)) {
                $productRelationId = $product->productData()->create($this->productData);
            }

            

            $user = getUser(Auth::user());
            $this->activityData['name'] = $product->name;
            $this->activityData['category_id'] = $product->category_id;
            $this->activityData['sub_category_id'] = $product->sub_category_id;
            $this->activityData['brand_id'] = $product->brand_id;
            $this->activityData['unit_id'] = $product->unit_id;
            $this->activityData['dosage_foams_id'] = $product->dosage_foams_id;
            $this->activityData['quantity_per_unit'] = $product->quantity_per_unit;
            activity()
                ->performedOn($product)
                ->causedBy(Auth::user())
                ->withProperties(['attributes' => $this->activityData])
                ->log("Master Product created By $user->name ($product->name)");

            // Notification::make()
            //     // ->title('Product Created')
            //     ->title('Product has been created successfully')
            //     ->success()
            //     ->send();

            Notification::make()
                ->title('Product Created')
                ->body("$product->name has been created by $user->name.")
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view')
                        ->label('View Product')
                        ->url(url(config('app.admin_url') . '/products/' . $product->id . "/view-admin"))
                ])
                ->info()
                ->sendToDatabase(User::role('Super Admin')->get());

            $superAdmins = User::role('Super Admin')->get();
            foreach ($superAdmins as $superAdmin) {
                Mail::to($superAdmin->email)->send(new PcCreatedMasterProduct($product, $user));
            }

            return redirect()->to(ProductResource::getUrl('index'));
        });
    }

    public function saveAction(): Action
    {
        return Action::make('save')
            ->label('Add')
            ->action('create');
    }

    public function reviewAction(): Action
    {
        return Action::make(name: 'review')
            ->label(label: 'Review')
            ->action(action: 'reviewPage');
    }

    public function reviewPage()
    {
        $data = $this->form->getState();
        // session()->put('product_data_for_save_' . auth()->id(), $data);
        foreach ($data['images'] as $index => $image) {
            if ($image instanceof TemporaryUploadedFile) {
                $data['images'][$index] = $image->temporaryUrl();
            }
        }
        session()->put('product_data_' . auth()->id(), $data);
        return redirect()->to(ProductResource::getUrl('review'));
    }

    public function reAction(): Action
    {
        return Action::make('cancel')
            ->label('Cancel')
            ->action('resetForm');
    }

    public function saveData()
    {
        $this->validate();
        $productId = DB::table('products')->insertGetId([
            'name' => $this->data['name'],
            'category_id' => $this->data['category_id'],
            'sub_category_id' => $this->data['sub_category_id'],
            'unit_id' => $this->data['unit_id'],
            'sku' => $this->data['sku'],
            'dosage_foams_id' => $this->data['dosage_foams_id'],
            'quantity_per_unit' => $this->data['quantity_per_unit'],
            'add_request_by' => auth()->user()->id,
            'status' => 'pending',
        ]);
        $product = Product::find($productId);
        if (isset($this->data['images']) && is_array($this->data['images'])) {
            foreach ($this->data['images'] as $image) {
                if ($image instanceof TemporaryUploadedFile) {
                    $product->addMedia($image->getRealPath())
                        ->usingFileName($image->getClientOriginalName())
                        ->toMediaCollection('product-images');
                }
            }
        }

        $prductRelationData = $this->data['productData'];
        $productRelationId = DB::table('products_relation')->insertGetId([
            'product_id' => $productId,
            'user_id' => auth()->user()->id,
            'requested_by' => auth()->user()->id,
            'price_type' => $prductRelationData['price_type'],
            'low_stock' => $prductRelationData['low_stock'],
            // 'quantity_per_unit' => $prductRelationData['quantity_per_unit'],
            'west_bonus_3_base_price' => $prductRelationData['west_bonus_1_base_price'] ?? null,
            'west_bonus_2_base_price' => $prductRelationData['east_bonus_1_base_price'] ?? null,
            'east_bonus_1_quantity' => $prductRelationData['east_bonus_1_quantity'] ?? null,
            'west_bonus_1_quantity' => $prductRelationData['west_bonus_1_quantity'] ?? null,
            'east_bonus_1_quantity_value' => $prductRelationData['east_bonus_1_quantity_value'] ?? null,
            'west_bonus_1_quantity_value' => $prductRelationData['west_bonus_1_quantity_value'] ?? null,
            'is_batch_wise_stock' => $prductRelationData['stock_type'] == 'batch',
            'west_zone_price' => $prductRelationData['west_zone_price'] ?? null,
            'east_zone_price' => $prductRelationData['east_zone_price'] ?? null,
            'stock' => $prductRelationData['stock'] ?? null,
            'expires_on_after' => $prductRelationData['expires_on_after'] ?? null,
            'west_tier_3_base_price' => $prductRelationData['west_tier_3_base_price'] ?? null,
            'west_tier_2_base_price' => $prductRelationData['west_tier_2_base_price'] ?? null,
            'west_tier_1_base_price' => $prductRelationData['west_tier_1_base_price'] ?? null,

            'east_tier_3_base_price' => $prductRelationData['east_tier_3_base_price'] ?? null,
            'east_tier_2_base_price' => $prductRelationData['east_tier_2_base_price'] ?? null,
            'east_tier_1_base_price' => $prductRelationData['east_tier_1_base_price'] ?? null,

            'west_tier_3_max_quantity' => $prductRelationData['west_tier_3_max_qty'] ?? null,
            'west_tier_2_max_quantity' => $prductRelationData['west_tier_2_max_qty'] ?? null,
            'west_tier_1_max_quantity' => $prductRelationData['west_tier_1_max_qty'] ?? null,
            'west_tier_3_min_quantity' => $prductRelationData['west_tier_3_min_qty'] ?? null,
            'west_tier_2_min_quantity' => $prductRelationData['west_tier_2_min_qty'] ?? null,
            'west_tier_1_min_quantity' => $prductRelationData['west_tier_1_min_qty'] ?? null,

            'west_tier_3_max_quantity' => $prductRelationData['west_tier_3_max_qty'] ?? null,
            'west_tier_2_max_quantity' => $prductRelationData['west_tier_2_max_qty'] ?? null,
            'west_tier_1_max_quantity' => $prductRelationData['west_tier_1_max_qty'] ?? null,
            'west_tier_3_min_quantity' => $prductRelationData['west_tier_3_min_qty'] ?? null,
            'west_tier_2_min_quantity' => $prductRelationData['west_tier_2_min_qty'] ?? null,
            'west_tier_1_min_quantity' => $prductRelationData['west_tier_1_min_qty'] ?? null,

        ]);
        $batchData = $this->data['batches'];
        $batchDataFinal = [];
        foreach ($batchData as $key => $batch) {
            $batchDataFinal[$key]['batch_name'] = $batch['batch_name'];
            $batchDataFinal[$key]['products_relation_id'] = $productRelationId;
            $batchDataFinal[$key]['user_id'] = auth()->user()->id;
            $batchDataFinal[$key]['product_id'] = $productId;
        }
        $productBatchId = DB::table('products_batch')->insert($batchDataFinal);

        // Log activity start
        // $product = $product->load(['category', 'subCategory']);
        // activity()
        //    ->causedBy(auth()->user())
        //     ->useLog('product_creation')
        //     ->performedOn($product)
        //     ->withProperties([
        //         'attributes' => array_filter([
        //             'name' => $product->name,
        //             'sku' => $product->sku,
        //             'category' => $product->category?->name,
        //             'sub_category' => $product->subCategory?->name,
        //             'stock' => $prductRelationData['stock'] ?? null,
        //         ], fn($value) => !is_null($value)),
        //     ])
        //     ->log("Product " . ($product->name ?? ' ') . " has been created");
        // Log activity end

    }

    public function resetForm()
    {
        $this->data = [
            'price_type' => 'fixed',
        ];
        $this->form->fill($this->data);
        return redirect()->to(ProductResource::getUrl('index'));
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }
}
