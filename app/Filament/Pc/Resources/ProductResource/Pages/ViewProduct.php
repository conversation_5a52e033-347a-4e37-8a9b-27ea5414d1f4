<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Product;
use Filament\Forms\Get;
use App\Models\PcDetail;
use Illuminate\Support\Str;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Models\ProductRelation;
use Illuminate\Validation\Rule;
use App\Actions\EditPriceAction;
use App\Mail\ProductResubmitted;
use Filament\Infolists\Infolist;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Contracts\View\View;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Pc\Resources\ProductResource;
use Awcodes\TableRepeater\Components\TableRepeater;
use Illuminate\Support\Facades\Route as LaravelRoute;
use Filament\Infolists\Concerns\InteractsWithInfolists;

class ViewProduct extends ViewRecord
{
    public $showModal = false;


    public function getBreadcrumbs(): array
    {

        return [
            url(route('filament.pc.resources.products.index')) => 'My Products',
            "#" => 'Product Details',
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return $this->getRecord()->name;
    }

    public function getImages()
    {

        $images = $this->record->getMedia('product-images');
        return $images;
    }

    public function showAllImages()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        $record = $this->getRecord();

        $authId = Auth::id();
        $userId = getUser(Auth::user())->id;
        if (!empty(Auth::user()->parent_id)) {
            $userId = Auth::user()->parent_id;
        }

        $relation = \App\Services\ProductRelationCacheService::getProductRelation($record->id, $userId);
        $stockData = \App\Services\ProductRelationCacheService::getProductRelationStock($relation?->id);
        $priceData = \App\Services\ProductRelationCacheService::getProductRelationPrice($relation?->id);
        $pcCommission = PcDetail::where('user_id', $authId)->first();
        $finalCommissionType = $pcCommission?->commission_type ?: null;
        $finalCommission = $pcCommission?->commission_percentage ?: 0;
        return [
            Action::make('edit_product_pc')
                ->label('Edit')
                ->url(fn() => $this->getResource()::getUrl('edit', ['record' => $this->record->getKey()]))
                ->visible(
                    fn($record) => $record->status == 'rejected' || (auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('products_update'))
                )
                ->button()
                ->color('primary'),
            Action::make('submit_approval')
                ->label('Submit for Approval')
                ->icon('heroicon-o-check')
                ->requiresConfirmation()

                ->visible(function ($record) {
                    return $record->status === 'rejected' && session()->get('is_resubmit');
                })
                ->action(function ($record) {

                    $record->update(['status' => 'pending']);
                    $record->productDataForPc(getUser(Auth::user())->id)->update([
                        'is_rejected' => false,
                        'submitted_on' => now(),
                    ]);
                    Notification::make()
                        ->title('Product submitted for approval')
                        ->success()
                        ->send();

                    Notification::make()
                        ->title('Product Resubmitted')
                        ->body("$record->name has been resubmitted for approval.")
                        ->actions([
                            \Filament\Notifications\Actions\Action::make('view')
                                ->label('View Product')
                                ->url(url(config('app.admin_url') . '/products/' . $record->id . "/view-admin"))
                        ])
                        ->sendToDatabase(User::role('Super Admin')->get());


                    Mail::to(User::find(1)->email)->send(
                        new ProductResubmitted($record, getUser(Auth::user()))
                    );
                    session()->forget('is_resubmit');
                    return redirect()->to(ProductResource::getUrl('index'));
                }),
            EditPriceAction::make($finalCommission, $finalCommissionType)
                ->visible(function ($record) {
                    $isApproved = $record->status == 'approved';
                    $isPharmacecuticalCompany = isPharmaceuticalCompany();
                    return $isApproved && ($isPharmacecuticalCompany || Auth::user()->hasRole('Super Admin') || Auth::user()->can('products_update price'));
                }),
            Action::make('update_stock')
                ->visible(function ($record) {
                    $isApproved = $record->status == 'approved';
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $isApproved && ($isPharmaceuticalCompany || Auth::user()->hasRole('Super Admin') || Auth::user()->can('products_update stock'));
                })
                ->outlined()
                ->label('Update Stock')
                ->icon('heroicon-o-pencil')
                ->action(function (array $data, $record) use ($stockData, $relation) {
                    $data['wholesale_pack_size'] = $data['stock_type'] == 'wps' ? $data['wholesale_pack_size'] : 1;
                    // 🎯 Ensure we have fresh product data with relationships
                    $record = $record->load(['category', 'brand', 'unit', 'container', 'foam']);

                    // 🎯 CRITICAL: Capture original state BEFORE any database operations
                    $userId = getUser(Auth::user())->id;

                    // Get original batch data from database BEFORE any changes
                    $originalBatchesFromDB = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();
                    $originalStockFromDB = ProductRelationStock::where('product_relation_id', $relation->id)->first();

                    $originalState = [
                        'stock' => $originalStockFromDB ? [
                            'stock' => $originalStockFromDB->stock,
                            'total_stock' => $originalStockFromDB->total_stock,
                            'is_batch_wise_stock' => $originalStockFromDB->is_batch_wise_stock,
                            'low_stock' => $originalStockFromDB->low_stock,
                            'wholesale_pack_size' => $originalStockFromDB->wholesale_pack_size,
                            'stock_type' => $originalStockFromDB->stock_type,
                        ] : [],
                        'batches' => $originalBatchesFromDB->map(function ($batch) {
                            return [
                                'batch_number' => $batch->batch_name ?? null,
                                'available_stock' => $batch->available_stock ?? null,
                                'expiry_date' => $batch->expiry_date ?? null,
                            ];
                        })->toArray()
                    ];

                    if ($data['stock_status'] == 'out_of_stock') {
                        $userId = getUser(Auth::user())->id;
                        $lowStock = $data['low_stock'] ?? null;
                        ProductRelationStock::updateOrCreate(['product_relation_id' => $relation->id], ['stock' => 0, 'total_stock' => 0, 'is_batch_wise_stock' => false, 'low_stock' => $lowStock, 'wholesale_pack_size' => $data['wholesale_pack_size'], 'stock_type' => $data['stock_type']]);

                        // Clear cache after stock update
                        \App\Services\ProductRelationCacheService::clearProductRelationStockCache($relation->id);
                        \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                        // Clear additional caches
                        \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                        \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                        \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                        $productStock = ProductRelationStock::where('product_relation_id', $relation->id)->first();
                        if ($productStock?->is_batch_wise_stock) {
                            ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                        }
                        $productStock?->update(['stock' => 0, 'total_stock' => 0, 'is_batch_wise_stock' => false]);

                        // 🎯 Create human-readable activity log for out of stock
                        $user = getUser(Auth::user());
                        $changedFields = [];
                        $oldFields = [];

                        if (($originalState['stock']['stock'] ?? 0) != 0) {
                            $changedFields['stock'] = 0;
                            $oldFields['stock'] = $originalState['stock']['stock'] ?? 0;
                        }
                        if (($originalState['stock']['total_stock'] ?? 0) != 0) {
                            $changedFields['total_stock'] = 0;
                            $oldFields['total_stock'] = $originalState['stock']['total_stock'] ?? 0;
                        }
                        if (($originalState['stock']['is_batch_wise_stock'] ?? false) != false) {
                            $changedFields['is_batch_wise_stock'] = false;
                            $oldFields['is_batch_wise_stock'] = $originalState['stock']['is_batch_wise_stock'] ?? false;
                        }

                        if (!empty($changedFields)) {
                            // Transform to human-readable format
                            $humanReadableOldData = [];
                            $humanReadableData = [];

                            // Create old data section using helper functions
                            if (!empty($oldFields)) {
                                $humanReadableOldData['Stock Configuration'] = [];
                                foreach ($oldFields as $field => $value) {
                                    $readableKey = self::getHumanReadableStockFieldName($field);
                                    $readableValue = self::formatStockFieldValueForLog($field, $value);
                                    $humanReadableOldData['Stock Configuration'][$readableKey] = $readableValue;
                                }
                            }

                            // Create comprehensive current data
                            $humanReadableData = [
                                'Product Name' => $record->name ?? 'Unknown Product',
                                'Product ID' => $record->id ?? 'Unknown',
                                'SKU' => $relation->sku ?? 'Not Set',
                                'Updated By PC' => $user->name ?? 'Unknown User',
                                'Update Type' => 'PC Stock Update - Out of Stock',
                                'Category' => $record->category?->name ?? 'Unknown',
                                'Brand' => $record->brand?->name ?? 'Unknown',
                                'Stock Status' => 'Out of Stock',
                                'Stock Management' => 'Simple Stock (Batches Cleared)',
                                'Stock Type' => $data['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                                'Wholesale Pack Size' => $data['wholesale_pack_size'] ?? 'Not Set',
                                'Current Stock' => '0',
                                'Total Stock' => '0',
                                'Low Stock Trigger' => $data['low_stock'] ?? 'Not Set',
                                'Batches Cleared' => !empty($originalState['batches']) ? 'Yes' : 'No',
                                'Previous Batch Count' => count($originalState['batches']),
                                'Updated At' => now()->format('Y-m-d H:i:s'),
                            ];

                            // 🎯 Debug: Log what we're about to save
                            Log::info('Activity Log Debug - Out of Stock', [
                                'product_name' => $record->name,
                                'product_id' => $record->id,
                                'user_name' => $user->name,
                                'original_batch_count' => count($originalState['batches']),
                                'original_batches' => $originalState['batches'],
                                'humanReadableOldData_has_batches' => isset($humanReadableOldData['Batch Details']),
                                'humanReadableData_keys' => array_keys($humanReadableData),
                                'has_product_name' => isset($humanReadableData['Product Name']),
                                'product_name_value' => $humanReadableData['Product Name'] ?? 'NOT SET'
                            ]);

                            activity()
                                ->performedOn($record)
                                ->causedBy(Auth::user())
                                ->withProperties([
                                    'old' => $humanReadableOldData,
                                    'attributes' => $humanReadableData
                                ])
                                ->log("PC '{$user->name}' set '" . ($record->name ?? 'Unknown Product') . "' to out of stock - {$humanReadableData['Previous Batch Count']} batches cleared");
                        }

                        // Send success notification for out of stock
                        Notification::make()
                            ->title('Stock Updated Successfully')
                            ->body("{$record->name} has been marked as out of stock.")
                            ->success()
                            ->send();

                        return;
                    }
                    $totalStock = 0;
                    if (!empty($data['batches'])) {
                        $batchesData = [];
                        foreach ($data['batches'] as $batch) {
                            $totalStock += $batch['available_stock'];
                        }
                    } else {
                        $totalStock = $data['productData']['stock'];
                    }
                    $batches = $data['batches'] ?? [];
                    $batchNames = array_column($batches, 'batch_name');
                    $batchNames = array_filter($batchNames, fn($name) => is_string($name) && trim($name) !== '');
                    $batchStock = 0;
                    $duplicates = array_filter(array_count_values($batchNames), fn($count) => $count > 1);
                    $isDuplicateName = count($duplicates) > 0;
                    if ($isDuplicateName) {
                        Notification::make()
                            ->title('Duplicate batch name found. Please check the batch names.')
                            ->danger()
                            ->send();
                        return;
                    }
                    if (!empty($data['batches'])) {
                        foreach ($data['batches'] as $batch) {
                            $batchStock += $batch['available_stock'];
                            $data['productData']['total_stock'] = $batchStock;
                        }
                    } else {
                        $data['productData']['total_stock'] = $data['productData']['stock'] ?? 0;
                    }
                    $userId = getUser(Auth::user())->id;
                    // if (empty($data['batches'])) {
                    //     $userId = getUser(auth()->user())->id;
                    //     ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                    // }

                    $relation = \App\Services\ProductRelationCacheService::getProductRelation($record->id, $userId);
                    $relation->update(['sku' => $data['sku']]);
                    $productRelationId = $relation->id;

                    ProductRelationStock::updateOrCreate(
                        ['product_relation_id' => $productRelationId],
                        [
                            'low_stock' => $data['low_stock'],
                            'wholesale_pack_size' => $data['wholesale_pack_size'],
                            'stock_type' => $data['stock_type'],
                        ]
                    );

                    $data['productData']['low_stock'] = $data['low_stock'];
                    $data['productData']['wholesale_pack_size'] = $data['wholesale_pack_size'];
                    $data['productData']['stock_type'] = $data['stock_type'];
                    $data['productData']['expiry_date'] = !empty($data['productData']['expiry_date']) ? Carbon::parse($data['productData']['expiry_date'])->setTime(23, 59, 0) : null;
                    $stock = ProductRelationStock::updateOrCreate(['product_relation_id' => $productRelationId], $data['productData']);

                    // Clear cache after stock update
                    \App\Services\ProductRelationCacheService::clearProductRelationStockCache($productRelationId);
                    \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                    // Clear additional caches
                    \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                    \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                    \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                    $batchesData = [];

                    if (!empty($data['batches'])) {
                        $batchesData = [];
                        foreach ($data['batches'] as $batch) {
                            $batchesData[] = [
                                'product_id' => $record->id,
                                'user_id' => $userId,
                                'products_relation_id' => $relation?->id,
                                'available_stock' => $batch['available_stock'],
                                'batch_name' => $batch['batch_name'],
                                'expiry_date' => $batch['expiry_date'],
                            ];
                        }

                        ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->delete();
                        $stock->update(['is_batch_wise_stock' => true]);

                        ProductBatch::insert($batchesData);

                        // Clear cache after batch update
                        \App\Services\ProductRelationCacheService::clearProductRelationStockCache($productRelationId);
                        \App\Services\ProductRelationCacheService::clearProductRelationCache($record->id, $userId);

                        // Clear additional caches for batches
                        \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                        \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                        \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");
                    }

                    // 🎯 Create clean activity logging for in-stock updates
                    $finalStockData = $stock->fresh();
                    $finalBatches = $record->batches?->where('user_id', $userId) ?? collect([]);

                    // 🎯 Enhanced comparison logic to handle stock type transitions
                    $changedData = [];
                    $oldChangedData = [];

                    // Get original and final states for comparison
                    $originalIsBatchWise = $originalState['stock']['is_batch_wise_stock'] ?? false;
                    $finalIsBatchWise = $finalStockData?->is_batch_wise_stock ?? false;
                    $originalBatches = $originalState['batches'] ?? [];

                    // Calculate original total stock (either from batches or direct stock)
                    $originalTotalStock = $originalIsBatchWise
                        ? collect($originalBatches)->sum('available_stock')
                        : ($originalState['stock']['stock'] ?? 0);

                    // Calculate final total stock
                    $finalTotalStock = $finalIsBatchWise
                        ? $finalBatches->sum('available_stock')
                        : ($finalStockData?->stock ?? 0);

                    // Always capture stock changes if there are any meaningful differences
                    $stockChanges = [];
                    $oldStockChanges = [];

                    // Check for stock type transition (batch to normal or normal to batch)
                    if ($originalIsBatchWise !== $finalIsBatchWise) {
                        // Stock type changed - capture comprehensive before/after state
                        $stockChanges['is_batch_wise_stock'] = $finalIsBatchWise;
                        $oldStockChanges['is_batch_wise_stock'] = $originalIsBatchWise;

                        $stockChanges['stock'] = $finalStockData?->stock ?? 0;
                        $oldStockChanges['stock'] = $originalState['stock']['stock'] ?? 0;

                        $stockChanges['total_stock'] = $finalStockData?->total_stock ?? 0;
                        $oldStockChanges['total_stock'] = $originalTotalStock; // Use calculated original total

                        // Always capture batch transition
                        if ($originalIsBatchWise && !$finalIsBatchWise) {
                            // Batch to Normal: Capture all original batches
                            $oldChangedData['batches'] = $originalBatches;
                            $changedData['batches'] = []; // No batches in normal stock
                        } elseif (!$originalIsBatchWise && $finalIsBatchWise) {
                            // Normal to Batch: Show new batch structure
                            $oldChangedData['batches'] = []; // No batches before
                            $changedData['batches'] = $finalBatches->map(function ($batch) {
                                return [
                                    'batch_number' => $batch->batch_name,
                                    'available_stock' => $batch->available_stock,
                                    'expiry_date' => $batch->expiry_date,
                                ];
                            })->toArray();
                        }
                    } else {
                        // Same stock type - check individual fields
                        $stockFields = ['stock', 'total_stock', 'is_batch_wise_stock', 'low_stock', 'wholesale_pack_size', 'stock_type'];
                        foreach ($stockFields as $field) {
                            $oldValue = $originalState['stock'][$field] ?? null;
                            $newValue = $finalStockData?->$field ?? null;

                            if ($oldValue != $newValue) {
                                $stockChanges[$field] = $newValue;
                                $oldStockChanges[$field] = $oldValue;
                            }
                        }

                        // Compare batch data only if both are batch-wise
                        if ($originalIsBatchWise && $finalIsBatchWise) {
                            $finalBatchData = $finalBatches->map(function ($batch) {
                                return [
                                    'batch_number' => $batch->batch_name,
                                    'available_stock' => $batch->available_stock,
                                    'expiry_date' => $batch->expiry_date,
                                ];
                            })->toArray();

                            if (json_encode($originalBatches) !== json_encode($finalBatchData)) {
                                $changedData['batches'] = $finalBatchData;
                                $oldChangedData['batches'] = $originalBatches;
                            }
                        }
                    }

                    // Check other fields that might have changed
                    $otherFields = ['low_stock', 'wholesale_pack_size', 'stock_type'];
                    foreach ($otherFields as $field) {
                        $oldValue = $originalState['stock'][$field] ?? null;
                        $newValue = $finalStockData?->$field ?? null;

                        if ($oldValue != $newValue) {
                            $stockChanges[$field] = $newValue;
                            $oldStockChanges[$field] = $oldValue;
                        }
                    }

                    if (!empty($stockChanges)) {
                        $changedData['stock'] = $stockChanges;
                        $oldChangedData['stock'] = $oldStockChanges;
                    }

                    if (!empty($changedData)) {
                        $user = getUser(Auth::user());
                        $totalBatchStock = $finalBatches->sum('available_stock');

                        // 🎯 Create human-readable old data using helper function
                        $humanReadableOldData = self::formatCompleteOldDataForStockLog($oldChangedData);

                        // 🎯 Create comprehensive human-readable activity data
                        $humanReadableData = [
                            'Product Name' => $record->name ?? 'Unknown Product',
                            'Product ID' => $record->id ?? 'Unknown',
                            'SKU' => $relation->sku ?? 'Not Set',
                            'Updated By PC' => $user->name ?? 'Unknown User',
                            'Update Type' => 'PC Stock Update - In Stock',
                            'Category' => $record->category?->name ?? 'Unknown',
                            'Brand' => $record->brand?->name ?? 'Unknown',
                            'Stock Status' => 'In Stock',
                            'Stock Management' => $finalStockData?->is_batch_wise_stock ? 'By Batch' : 'Simple Stock',
                            'Stock Type' => $finalStockData?->stock_type === 'wps' ? 'Wholesale Pack' : 'Unit',
                            'Wholesale Pack Size' => $finalStockData?->wholesale_pack_size ?? 'Not Set',
                            'Low Stock Trigger' => $finalStockData?->low_stock ?? 'Not Set',
                            'Current Stock' => $finalStockData?->is_batch_wise_stock
                                ? $totalBatchStock . ' (from batches)'
                                : ($finalStockData?->stock ?? 'Not Set'),
                            'Total Stock' => $finalStockData?->total_stock ?? 'Not Set',
                            'Batch Count' => $finalBatches->count(),
                            'Updated At' => now()->format('Y-m-d H:i:s'),
                        ];

                        // Add batch details if applicable
                        if ($finalStockData?->is_batch_wise_stock && $finalBatches->count() > 0) {
                            $humanReadableData['Batch Details'] = [];
                            foreach ($finalBatches as $index => $batch) {
                                $batchNum = $index + 1;
                                $humanReadableData['Batch Details']["Batch {$batchNum}"] = [
                                    'Batch Number' => $batch->batch_name ?? 'Not Set',
                                    'Available Stock' => $batch->available_stock ?? 0,
                                    'Expiry Date' => $batch->expiry_date ?? 'Not Set',
                                ];
                            }
                        }

                        // 🎯 Debug: Log comprehensive stock transition data
                        Log::info('Activity Log Debug - In Stock', [
                            'original_is_batch_wise' => $originalIsBatchWise,
                            'final_is_batch_wise' => $finalIsBatchWise,
                            'stock_type_changed' => $originalIsBatchWise !== $finalIsBatchWise,
                            'original_batch_count' => count($originalState['batches']),
                            'final_batch_count' => $finalBatches->count(),
                            'original_total_stock' => $originalTotalStock,
                            'final_total_stock' => $finalTotalStock,
                            'original_batches' => $originalState['batches'],
                            'changedData_keys' => array_keys($changedData),
                            'oldChangedData_keys' => array_keys($oldChangedData),
                            'has_old_batch_details' => isset($humanReadableOldData['Batch Details']),
                            'has_new_batch_details' => isset($humanReadableData['Batch Details']),
                        ]);

                        activity()
                            ->performedOn($record)
                            ->causedBy(Auth::user())
                            ->withProperties([
                                'old' => $humanReadableOldData,
                                'attributes' => $humanReadableData
                            ])
                            ->log("PC '{$user->name}' updated stock for '" . ($record->name ?? 'Unknown Product') . "' - {$humanReadableData['Stock Status']} with {$humanReadableData['Stock Management']}");
                    }

                    // Final cache clearing to ensure all caches are cleared
                    \Illuminate\Support\Facades\Cache::forget("product_stock_{$record->id}_{$userId}");
                    \Illuminate\Support\Facades\Cache::forget("product_batches_{$record->id}_{$userId}");
                    \Illuminate\Support\Facades\Cache::forget("product_data_{$record->id}_{$userId}");

                    // Send success notification
                    Notification::make()
                        ->title('Stock Updated Successfully')
                        ->body("Stock for {$record->name} has been updated successfully.")
                        ->success()
                        ->send();
                })
                ->modalWidth('7xl')
                ->form(function ($record) use ($stockData) {
                    return [
                        Section::make()->schema([
                            Group::make()->schema([
                                TextInput::make('name')
                                    ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent'])
                                    ->disabled()
                                    ->formatStateUsing(fn($record) => $record->name),
                                TextInput::make('unit')
                                    ->label('Volume Unit')
                                    ->formatStateUsing(function ($record) {
                                        return $record->unit?->name ?? "";
                                    })
                                    ->extraAttributes(['class' => 'border-none border-0 focus:ring-0 p-0 bg-transparent'])
                                    ->disabled(),
                                TextInput::make('sku')
                                    ->label('Stock Keeping Unit (SKU)')
                                    ->rules(['required'])
                                    ->formatStateUsing(function ($record) {
                                        return $record->productDataForPc(Auth::id())->sku ?? "";
                                    }),
                                TextInput::make('low_stock')
                                    ->label(function ($record) {
                                        $containerName = $record->container->name ?? "";
                                        return new HtmlString("<div class='text-sm'>Low Stock trigger value by $containerName<span class='text-red-500' style='color:red;'>*</span> </div>");
                                    })
                                    ->validationAttribute('Low Stock trigger value')
                                    ->rules(['required', 'numeric', 'gt:0'])
                                    ->formatStateUsing(function ($record) use ($stockData) {
                                        return $stockData?->low_stock;
                                    })
                                    ->numeric(),
                                Toggle::make('productData.is_batch_wise_stock')
                                    ->label('By Batch')
                                    ->inline(false)
                                    ->extraAttributes(['style' => 'marign-top: 30px; !important'])
                                    ->live()

                                    ->formatStateUsing(function ($record) use ($stockData) {
                                        return $stockData?->is_batch_wise_stock ?? true;
                                    }),
                                Select::make('stock_status')
                                    ->options([
                                        'in_stock' => 'In Stock',
                                        'out_of_stock' => 'Out of Stock',
                                    ])
                                    ->afterStateUpdated(function (Get $get) {
                                        if ($get('stock_status') == 'out_of_stock') {
                                            Notification::make()
                                                ->body('This will remove all the current stock data !')
                                                ->info()
                                                ->send();
                                        }
                                    })
                                    ->formatStateUsing(function ($record) use ($stockData) {
                                        $userId = getUser(Auth::user())->id;
                                        if ($stockData?->is_batch_wise_stock) {
                                            $currentStock = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId, 'products_relation_id' => $stockData?->product_relation_id])->sum('available_stock');

                                            if ($currentStock > 0) {
                                                return 'in_stock';
                                            } else {
                                                return 'out_of_stock';
                                            }
                                        } else {
                                            $currentStock = $stockData?->stock;
                                            if ($currentStock > 0) {
                                                return 'in_stock';
                                            } else {
                                                return 'out_of_stock';
                                            }
                                        }
                                    })
                                    ->live(),
                                TextInput::make('wholesale_pack_size')
                                    ->columnSpan(2)
                                    ->visible(fn(Get $get) => $get('stock_type') == 'wps')
                                    ->label(new HtmlString('Wholesale Pack Size<span class="text-red-500" style="color:red;">*</span>'))
                                    ->numeric()
                                    ->live()
                                    ->validationAttribute('Wholesale Pack Size')
                                    ->rules(function (Get $get) {
                                        if ($get('stock_type') == 'wps') {
                                            return ['integer', 'required', 'gt:1'];
                                        }
                                        return ['integer', 'required', 'gt:0'];
                                    })
                                    ->default('-')
                                    ->prefix(function ($record) {
                                        return new HtmlString("<div class='text-sm font-semibold text-gray-600'>" . Str::plural($record->container?->name ?? '') . " of</div>");
                                    })
                                    ->suffix(function ($record, Get $get) {
                                        $qty = $record->quantity_per_unit;
                                        if ($get('wholesale_pack_size') && $get('wholesale_pack_size') > 0) {

                                            $qty = $qty * (int) $get('wholesale_pack_size');
                                        }
                                        if (!empty($record->wholesale_pack_size) && $record->wholesale_pack_size > 0) {
                                            $qty = $qty * (int) $record->wholesale_pack_size;
                                        }
                                        return new HtmlString("<div class='text-sm font-semibold text-gray-600'>{$qty} " . Str::plural($record->foam?->name) . "</div>");
                                    })
                                    ->formatStateUsing(function ($record) {
                                        $productRelation = $record->productDataForPc(getUser(Auth::user())->id);
                                        return \App\Services\ProductRelationCacheService::getProductRelationStock($productRelation?->id)?->wholesale_pack_size ?? null;
                                    }),
                                Radio::make('stock_type')
                                    ->label('Stock Type')
                                    ->live()
                                    ->options([
                                        'unit' => 'Unit',
                                        'wps' => new HtmlString('Wholesale Pack<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Indicates the quantity of items included in one wholesale unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>'),
                                    ])
                                    ->inline()
                                    ->rules(['required'])
                                    ->columnSpanFull()
                                    ->validationAttribute('stock type')
                                    ->formatStateUsing(function ($record) {
                                        $productRelation = $record->productDataForPc(getUser(Auth::user())->id);
                                        return \App\Services\ProductRelationCacheService::getProductRelationStock($productRelation?->id)?->stock_type ?? 'unit';
                                    }),

                            ])->columns(4),
                        ]),
                        Section::make()
                            ->heading(fn(Get $get) => new HtmlString(
                                '<div class="flex items-center justify-between w-full">
        <span class="text-base font-bold text-gray-900">Manage Batch</span>
        <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                                    collect($get('batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                                ) . '</span>
    </div>'
                            ))
                            ->visible(fn(Get $get) => $get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                            ->schema([
                                TableRepeater::make('batches')
                                    // ->state(function ($record) {
                                    //     $userId = getUser(auth()->user())->id;
                                    //     return ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->get();
                                    // })
                                    ->minItems(fn(Get $get) => $get('productData.is_batch_wise_stock') ? 1 : 0)
                                    ->default(function ($record) use ($stockData) {


                                        return $record->batches?->where('user_id', getUser(Auth::user())->id)->where('products_relation_id', $stockData?->product_relation_id)->map(fn($batch) => [
                                            'id' => $batch->id,
                                            'batch_name' => $batch->batch_name,
                                            'available_stock' => $batch->available_stock,
                                            'expiry_date' => $batch->expiry_date,
                                        ])->toArray();
                                    })
                                    ->addActionAlignment(Alignment::End)
                                    ->addActionLabel('+ Add Batch')
                                    ->reorderable(false)
                                    ->headers([
                                        Header::make('batch_name')->label('Batch Number'),
                                        Header::make('available_stock')->label('Stock by Packaging'),
                                        Header::make('expiry_date')->label('Expiry Date'),
                                    ])
                                    ->schema([

                                        TextInput::make('id')
                                            ->hidden(),
                                        TextInput::make('batch_name')
                                            ->rules(function (Get $get) use ($stockData) {
                                                $batchId = $get('id');
                                                return ['required', 'string', 'max:255', Rule::unique('products_batch', 'batch_name')->ignore($batchId)];
                                            }),
                                        TextInput::make('available_stock')
                                            ->live(onBlur: true)
                                            ->rules(['gt:0', 'numeric', 'required']),
                                        DatePicker::make('expiry_date')
                                            ->placeholder('Select the expiry date')
                                            ->rules(['required', 'date', 'after_or_equal:today'])
                                            ->minDate(today()),
                                    ])
                            ]),
                        Group::make()
                            ->visible(fn(Get $get) => !$get('productData.is_batch_wise_stock') && $get('stock_status') == 'in_stock')
                            ->schema([
                                Group::make()->schema([
                                    TextInput::make('productData.stock')
                                        ->rules(function (Get $get) use ($stockData) {
                                            if (!$get('productData.is_batch_wise_stock')) {
                                                return ['required', 'numeric', 'gt:0'];
                                            }
                                            return ['sometimes', 'numeric', 'gt:0'];
                                        })
                                        ->validationAttribute('Stock')
                                        ->label(function (Get $get, $record) {
                                            $containerName = $record->container->name ?? "";
                                            return new HtmlString("Stock by $containerName<span class='text-red-500' style='color:red;'>*</span>");
                                        })
                                        ->formatStateUsing(function ($record) use ($stockData) {
                                            return $stockData->stock ?? null;
                                        })
                                        ->columnSpan(5),
                                    DatePicker::make('productData.expiry_date')
                                        ->placeholder('Select the expiry date')
                                        ->validationAttribute('Expiry Date')
                                        ->label(function (Get $get) {
                                            if (!$get('productData.is_batch_wise_stock')) {
                                                return new HtmlString('Expiry Date<span class="text-red-500" style="color:red;">*</span>');
                                            }
                                            return 'Expiry Date';
                                        })
                                        // ->format(getFormatedDateForPc()))
                                        ->minDate(today())

                                        ->rules(function (Get $get) use ($stockData) {
                                            if (!$get('productData.is_batch_wise_stock')) {
                                                return ['required', 'date', 'after_or_equal:today'];
                                            }
                                            return ['sometimes', 'date', 'after_or_equal:today'];
                                        })
                                        ->default(fn($record) => $stockData?->expiry_date)
                                        ->columnSpan(5)
                                ])->columns(12)
                            ]),

                    ];
                }),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('index')),
        ];
    }

    /**
     * Format complete old data for complex stock activity logging
     */
    private static function formatCompleteOldDataForStockLog(array $oldChangedData): array
    {
        $humanReadableOldData = [];

        foreach ($oldChangedData as $section => $changes) {
            if ($section === 'stock') {
                $humanReadableSection = [];
                foreach ($changes as $field => $oldValue) {
                    $humanReadableKey = self::getHumanReadableStockFieldName($field);
                    $humanReadableValue = self::formatStockFieldValueForLog($field, $oldValue);
                    $humanReadableSection[$humanReadableKey] = $humanReadableValue;
                }
                if (!empty($humanReadableSection)) {
                    $humanReadableOldData['Stock Configuration'] = $humanReadableSection;
                }
            } elseif ($section === 'batches') {
                if (!empty($changes)) {
                    $humanReadableOldData['Batch Details'] = [];
                    foreach ($changes as $index => $batch) {
                        $batchNum = $index + 1;
                        $humanReadableOldData['Batch Details']["Batch {$batchNum}"] = [
                            'Batch Number' => $batch['batch_number'] ?? 'Not Set',
                            'Available Stock' => $batch['available_stock'] ?? 0,
                            'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
                        ];
                    }
                }
            }
        }

        return $humanReadableOldData;
    }

    /**
     * Get human-readable field name for stock fields
     */
    private static function getHumanReadableStockFieldName(string $field): string
    {
        $fieldMap = [
            'stock' => 'Current Stock',
            'total_stock' => 'Total Stock',
            'is_batch_wise_stock' => 'Batch Management',
            'low_stock' => 'Low Stock Trigger',
            'wholesale_pack_size' => 'Wholesale Pack Size',
            'stock_type' => 'Stock Type',
            'available_stock' => 'Available Stock',
            'batch_number' => 'Batch Number',
            'expiry_date' => 'Expiry Date',
            'manufacturing_date' => 'Manufacturing Date',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
            'product_id' => 'Product ID',
            'user_id' => 'User ID',
            'products_relation_id' => 'Product Relation ID',
        ];

        return $fieldMap[$field] ?? ucwords(str_replace('_', ' ', $field));
    }

    /**
     * Format stock field value for human-readable display
     */
    private static function formatStockFieldValueForLog(string $field, $value)
    {
        if ($value === null) {
            return 'Not Set';
        }

        switch ($field) {
            case 'is_batch_wise_stock':
                return $value ? 'By Batch' : 'Simple Stock';
            case 'stock_type':
                return $value === 'wps' ? 'Wholesale Pack' : 'Unit';
            case 'created_at':
            case 'updated_at':
                return \Carbon\Carbon::parse($value)->format('Y-m-d H:i:s');
            case 'expiry_date':
            case 'manufacturing_date':
                return $value ? \Carbon\Carbon::parse($value)->format('Y-m-d') : 'Not Set';
            default:
                return (string) $value;
        }
    }
}
