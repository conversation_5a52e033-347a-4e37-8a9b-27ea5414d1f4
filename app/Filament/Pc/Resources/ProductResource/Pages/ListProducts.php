<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Filament\Actions;
use App\Models\Product;
use Illuminate\View\View;
use Filament\Actions\Action;
use App\Models\ProductRelation;
use Livewire\Attributes\Session;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Pc\Resources\ProductResource;
use Filament\Actions\ActionGroup;
use Illuminate\Support\Facades\Cache;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;



    #[Session]
    public bool $isPending = false;

    #[Session]
    public bool $isAllProducts = true;

    #[Session]
    public bool $isRejectedProducts = false;

    public function mount(): void
    {
        if (request()->get('tab') == 'all-products') {
            $this->isPending = false;
            $this->isAllProducts = true;
            $this->isRejectedProducts = false;
        } elseif (request()->get('tab') == 'pending-approval') {
            $this->isPending = true;
            $this->isAllProducts = false;
            $this->isRejectedProducts = false;
        } elseif (request()->get('tab') == 'rejected') {
            $this->isPending = false;
            $this->isAllProducts = false;
            $this->isRejectedProducts = true;
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            $this->bulkStockUpdateAction(),
            $this->addProductAction(),
        ];
    }

    public function getHeader(): ?View
    {
        return $this->getHeaderHeading();
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "My Products",
            // 2 => "List",
        ];
    }

    public function pendingApprovals()
    {
        $this->isPending = true;
        $this->isAllProducts = false;
        $this->isRejectedProducts = false;
        $this->dispatch('refreshTable');
    }

    public function allProducts()
    {
        $this->isPending = false;
        $this->isAllProducts = true;
        $this->isRejectedProducts = false;
        $this->dispatch('refreshTable');
    }

    public function rejectedProducts()
    {
        $this->isPending = false;
        $this->isAllProducts = false;
        $this->isRejectedProducts = true;
        $this->dispatch('refreshTable');
    }

    private function getHeaderHeading()
    {

        return view('custom-view.header-action', [
            'title' => 'My Products',
            'headerActions' => $this->getHeaderActions(),
            'isAllProducts' => $this->isAllProducts,
            'isPending' => $this->isPending,
            'isRejectedProducts' => $this->isRejectedProducts,
            'pending' => Product::query()->whereHas('productData', function ($query) {
                $query->where('user_id', auth()->user()->id);
            })->pendingApprovals()->count(),
        ]);
    }

    protected function getTableQuery(): ?Builder
    {
        $userId = auth()->user()->parent_id ?? auth()->user()->id;
        $query = Product::query()->whereHas('productData', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        });
        if (!$this->isAllProducts) {
            $query->pendingApprovals();
        }
        if ($this->isRejectedProducts) {
            $query = Product::query()->where('status', 'rejected')->whereHas('productData', function ($query) use ($userId) {
                $query->where('requested_by', $userId)
                    ->where('is_rejected', operator: true);
            });
        }
        if ($this->isAllProducts) {
            $query = Product::query()->whereHas('productData', function ($query) use ($userId) {
                $query->where('user_id', $userId)->where('is_rejected', false);
            });
        }
        $data =  $query->with(['category', 'subcategory', 'productData', 'batches']);

        return $data;
    }

    public function bulkStockUpdateAction(): Action
    {
        return Action::make('bulk_stock_update')
            ->outlined()
            ->visible(fn($record) => auth()->user()->can('products_bulk stock update') || auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company'))
            ->url(ProductResource::getUrl('product-bulk-stock-update'))
            ->label('Bulk Stock Update');
    }



    public function addProductAction(): Action
    {
        return Action::make('add_product')
            ->icon('heroicon-o-plus')
            ->url(ProductResource::getUrl('create'))
            ->visible(fn() => auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('products_create'))
            ->label('Add Product');
    }
}
