<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use App\Models\ProductRelation;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Pc\Resources\ProductResource;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected static bool $canCreateAnother = false;

    public ?string $heading = 'Add Product';

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.pc.resources.products.index')) => 'My Products',
            "#" => 'Add Product',
        ];
    }

    public function getFormActions(): array
    {
        return []; // ← This removes all form footer buttons
    }

    public function create(bool $another = false): void
    {
        $this->navigate($this->form->getState());
    }

    public function navigate($data)
    {
        if ($data['product_id'] == 'create_new') {

            return redirect()->to(ProductResource::getUrl('create-new'));
        } else {
            $exists = \App\Services\ProductRelationCacheService::hasProductRelation($this->data['product_id'], auth()->user()->id);
            if ($exists) {
                Notification::make()
                    ->title('Product already exists')
                    ->danger()
                    ->send();
                return;
            }
            return redirect()->to(ProductResource::getUrl('create-existing', ['record' => $data['product_id']]));
        }
    }

    protected function getCreateFormAction(): Action
    {
        return Action::make('create')
            ->label('Add')
            ->submit('create')
            ->keyBindings(['mod+s']);
    }

    protected function getHeaderActions(): array
    {
        return [

            Actions\Action::make('add-new-product')
                ->label(label: '+ Add New Product')
                ->view('custom-view.action-hint-text')
                ->viewData(['hint' => 'If the product is not available in the master list, you can create it manually from scratch'])
                ->url(fn() => $this->getResource()::getUrl('create-new')),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->extraAttributes(['style' => 'margin-bottom: 22px;'])
                ->url(ProductResource::getUrl('index')),
        ];
    }
}
