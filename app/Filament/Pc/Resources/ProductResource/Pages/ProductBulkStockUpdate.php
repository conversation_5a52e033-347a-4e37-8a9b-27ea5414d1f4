<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Carbon\Carbon;
use App\Models\Product;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Models\ProductRelation;
use App\Jobs\BulkStockUpdateJob;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Models\ProductRelationStock;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Pc\Resources\ProductResource;
use App\Filament\Pc\Resources\ProductResource\Widgets\NormalProducts;
use App\Filament\Pc\Resources\ProductResource\Widgets\BatchWiseProducts;

class ProductBulkStockUpdate extends Page
{
    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.product-bulk-stock-update';

    protected static ?string $header = 'Bulk Stock Update';

    protected static ?string $widgetSpace = 'full';

    public function getTitle(): string|Htmlable
    {
        return 'Bulk Stock Update';
    }


    protected function getHeaderActions(): array
    {
        return [

            Action::make('cancel')
                ->action(function () {
                    session()->forget('data');
                    return redirect()->to(ProductResource::getUrl('index'));
                })
                ->label(fn() => new HtmlString("<div class='font-medium text-gray-700'>Cancel</div>"))

                ->color('white'),
            Action::make('save')
                ->action(function (array $data) {
                    $batchData = cache()->get('data')['batch_data_'] ?? [];
                    $normalData = cache()->get('data')['normal'] ?? [];

                    cache()->forget('data');
                    $userId = getUser(auth()->user())->id;
                    $totalStock = 0;

                    if (!empty($batchData)) {
                        $productIds = array_keys($batchData);

                        // Get product relations for current user only
                        $productRelations = ProductRelation::whereIn('product_id', $productIds)
                            ->where('user_id', $userId)
                            ->get(['id', 'product_id'])
                            ->keyBy('product_id');

                        $productRelationIds = $productRelations->pluck('id')->toArray();
                        
                        // Delete existing batches for current user and these products only
                        ProductBatch::whereIn('product_id', $productIds)
                            ->where('user_id', $userId)
                            ->whereIn('products_relation_id', $productRelationIds)
                            ->delete();


                        $batchesToInsert = [];
                        foreach ($batchData as $productId => $data) {
                            $productRelationId = $productRelations[$productId]->id ?? null;
                            if ($productRelationId && isset($data['batches'])) {
                                foreach ($data['batches'] as $batch) {
                                    $totalStock += $batch['available_stock'];
                                    $batchesToInsert[] = [
                                        'product_id' => $productId,
                                        'batch_name' => $batch['batch_name'],
                                        'user_id' => $userId,
                                        'products_relation_id' => $productRelationId,
                                        'available_stock' => $batch['available_stock'],
                                        'expiry_date' => $batch['expiry_date'],
                                        'created_at' => now(),
                                        'updated_at' => now(),
                                    ];
                                }
                            }
                        }


                        if (!empty($batchesToInsert)) {
                            ProductBatch::insert($batchesToInsert);
                            ProductRelationStock::whereIn('product_relation_id', $productRelationIds)
                                ->update(['total_stock' => $totalStock, 'stock' => $totalStock, 'is_batch_wise_stock' => true]);
                        }
                    }

                    if (!empty($normalData)) {
                        $updates = [];
                        foreach ($normalData as $productId => $data) {
                            $update = ['product_id' => $productId];

                            if (isset($data['stock'])) {
                                $update['stock'] = $data['stock'];
                                $update['total_stock'] = $data['stock'];
                            }

                            if (isset($data['expires_on_after'])) {
                                $update['expires_on_after'] = $data['expires_on_after'];
                            }

                            if (count($update) > 1) {
                                $updates[$productId] = $update;
                            }
                        }

                        if (!empty($updates)) {

                            $casesStock = [];
                            $casesExpiresOnAfter = [];
                            $casesTotalStock = [];
                            $productIds = [];

                            foreach ($updates as $update) {
                                $productId = $update['product_id'];

                                if (isset($update['stock']) && !is_null($productId)) {
                                    $casesStock[] = "WHEN product_relation_id = {$productId} THEN {$update['stock']}";
                                    $casesTotalStock[] = "WHEN product_relation_id = {$productId} THEN {$update['stock']}";
                                }
                                if (isset($update['expires_on_after'])) {
                                    $casesExpiresOnAfter[] = "WHEN product_relation_id = {$productId} THEN '{$update['expires_on_after']}'::timestamp";
                                }

                                $productIds[] = $productId;
                            }

                            $productIds = array_filter($productIds);
                            if (!empty($productIds)) {
                                $productIdsList = implode(',', $productIds);
                                $updateStatements = [];

                                if (!empty($casesStock)) {
                                    $updateStatements[] = "stock = CASE " . implode(' ', $casesStock) . " ELSE stock END";
                                }

                                if (!empty($casesTotalStock)) {
                                    $updateStatements[] = "total_stock = CASE " . implode(' ', $casesTotalStock) . " ELSE total_stock END";
                                }

                                if (!empty($casesExpiresOnAfter)) {
                                    $updateStatements[] = "expires_on_after = CASE " . implode(' ', $casesExpiresOnAfter) . " ELSE expires_on_after END";
                                }
                                if (!empty($updateStatements)) {
                                    // Get the product IDs as an array
                                    $productIds = explode(',', str_replace("'", "", $productIdsList));

                                    // For each product ID
                                    foreach ($productIds as $productId) {
                                        // Check if the record exists
                                        $exists = DB::table('product_relation_stocks')
                                            ->where('product_relation_id', $productId)
                                            ->exists();

                                        if ($exists) {
                                            // Update existing record
                                            $individualUpdateQuery = "UPDATE product_relation_stocks SET " . implode(', ', $updateStatements) . " WHERE product_relation_id = {$productId}";
                                            DB::update($individualUpdateQuery);
                                        } else {
                                            // Insert new record
                                            // You'll need to extract the values from your update statements
                                            // This is a simplified example - adjust according to your actual data
                                            $insertData = [
                                                'product_relation_id' => $productId,
                                                'updated_at' => now(),
                                                'stock' => $updates[$productId]['stock'] ?? null,
                                                'total_stock' => $updates[$productId]['stock'] ?? null,
                                            ];
                                            DB::table('product_relation_stocks')->insert($insertData);
                                        }
                                    }

                                    cache()->forget(key: 'data');
                                    session()->forget('data');
                                }
                            }
                        }
                    }
                    cache()->forget(key: 'data');
                    session()->forget('data');
                    $this->dispatch('refresh');
                    Notification::make()
                        ->title('Products updated successfully')
                        ->success()
                        ->send();
                    return redirect()->to(ProductResource::getUrl('index'));
                }),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(url()->previous()),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            BatchWiseProducts::class,
        ];
    }
}
