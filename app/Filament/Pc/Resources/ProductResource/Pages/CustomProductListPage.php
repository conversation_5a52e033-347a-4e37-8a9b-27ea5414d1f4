<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use App\Models\Product;
use Livewire\Component;
use Filament\Tables\Table;
use Filament\Actions\Action;
use Filament\Resources\Pages\Page;
use Illuminate\Support\HtmlString;
use Filament\Tables\Filters\Filter;
use Illuminate\Contracts\View\View;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Enums\FiltersLayout;
use App\Filament\Pc\Resources\ProductResource;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;

class CustomProductListPage extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;


    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.custom-product-list-page';

    // protected static ?string $title = "";

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(),
            'active' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('active', true)),
            'inactive' => Tab::make()
                ->modifyQueryUsing(fn(Builder $query) => $query->where('active', false)),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function () {
                $data = Product::query()->with(['category', 'subcategory', 'productData', 'batches']);
                return $data;
            })
            ->columns([
                TextColumn::make('id')->label('Product ID'),
                TextColumn::make('name')->label('Product Name'),
                TextColumn::make('category.name')->label('Category'),
                TextColumn::make('subcategory.name')->label('Subcategory'),
                TextColumn::make('productData.is_batch_wise_stock')->label('Stock')
                    ->formatStateUsing(function ($state, $record) {
                        if ($state == true) {
                            // dd($record->batches);
                            $stock = $record->batches->where('product_id', $record->id)->first()->available_stock;
                            return $stock;
                        } else {
                            $stock = $record->productData->stock;
                            return $stock;
                        }
                    }),
                TextColumn::make('productData')->label('Out of Stock')
                    ->formatStateUsing(function ($state, $record) {
                        if ($state == true) {
                            if ($record->productData->is_batch_wise_stock == true) {
                                $stock = $record->batches->where('product_id', $record->id)->first()->available_stock > 0 ? 'In Stock' : 'Out of Stock';
                                return $stock;
                            } else {
                                $stock = $record->productData->stock > 0 ? 'In Stock' : 'Out of Stock';
                                return $stock;
                            }
                        }
                    })
            ])
            ->filters([
                Filter::make('pending approval'),
                Filter::make('my_products'),
            ])
            ->filtersLayout(FiltersLayout::Modal)

            ->actions([
                // ...
            ])

            ->bulkActions([
                // ...
            ]);
    }

    public function getHeaderActions(): array
    {
        return [
            $this->addProductAction(),
            $this->bulkStockUpdateAction(),
        ];
    }

    public function addProductAction(): Action
    {
        return Action::make('add_product')
            ->outlined()
            // ->action(fn()=>dd('action'))
            ->label('Pending Approval');
    }

    public function bulkStockUpdateAction(): Action
    {
        return Action::make('bulk_stock_update')
            ->label('Bulk Stock Update');
    }
}
