<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use App\Models\Brand;
use App\Models\Product;
use App\Models\Category;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use App\Filament\Pc\Resources\ProductResource;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;

class ProductReview extends Page implements HasInfolists
{
    use InteractsWithInfolists;
    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.product-review';

    public $data;

    public function mount()
    {
        $this->data = session()->get('product_data_' . auth()->id());
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record(new Product($this->data))
            ->schema([
                Section::make('Product Details')
                    ->schema([
                        TextEntry::make('name')
                            ->formatStateUsing(function () {
                                return $this->data['name'];
                            })
                            ->label('Product Name'),
                        TextEntry::make('brand')
                            ->formatStateUsing(function () {
                                return Brand::find($this->data['brand_id'])->name;
                            })
                            ->label('Brand'),
                        TextEntry::make(name: 'category')
                            ->formatStateUsing(function () {
                                return Category::find($this->data['category_id'])->name;
                            })
                            ->label('Category'),
                        TextEntry::make('sub_category_id')
                            ->formatStateUsing(function () {
                                return Category::find($this->data['sub_category_id'])->name;
                            })
                            ->label(label: 'Sub Category'),
                        TextEntry::make('weight')
                            ->formatStateUsing(function () {
                                return $this->data['weight'];
                            })
                            ->label('Weight (gms)'),
                        Group::make([
                            ...collect($this->data['images'] ?? [])->map(
                                fn($imgPath) =>
                                ImageEntry::make('images')
                                    ->label('Image')
                                    ->url(function () use ($imgPath) {

                                        return $imgPath;
                                    })
                                    ->hiddenLabel()
                                    ->extraAttributes(['class' => 'w-48 h-auto rounded shadow'])
                            )->toArray(),
                        ])->columns(count($this->data['images'] ?? []) ?: 1),
                    ])
            ]);
    }

    public function save() {}
}
