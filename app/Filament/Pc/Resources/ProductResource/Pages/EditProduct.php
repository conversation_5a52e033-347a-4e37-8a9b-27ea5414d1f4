<?php

namespace App\Filament\Pc\Resources\ProductResource\Pages;

use Carbon\Carbon;
use App\Models\Unit;
use App\Models\User;
use App\Models\Brand;
use Filament\Actions;
use App\Models\Product;
use Filament\Forms\Get;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Distributor;
use App\Models\GenericName;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Illuminate\Validation\Rule;
use App\Mail\ProductResubmitted;
use App\Forms\Components\DragDrop;
use App\Rules\CaseSensitiveUnique;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\RichEditor;
use App\Filament\Pc\Resources\ProductResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class EditProduct extends EditRecord
{
    use HasBackButton;
    protected static string $resource = ProductResource::class;

    public $defaultIndex;

    public $images;

    public $oldData = [];

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.pc.resources.products.index')) => 'Product Catalog',
            "#" => 'Product Edit',
        ];
    }
    protected function getRedirectUrl(): ?string
    {
        session()->put('is_resubmit', true);
        return ProductResource::getUrl('view', ['record' => $this->record]);
    }

    protected function getHeaderActions(): array
    {

        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('view', ['record' => $this->record->id])),
        ];
    }

    public function setDefaultImage($index)
    {
        $this->defaultIndex = $index;
        
        // Update the form state to reflect the default image selection
        $currentState = $this->form->getState();
        if (isset($currentState['images']) && is_array($currentState['images'])) {
            // Store the default index for processing in afterSave
            $this->form->fill(array_merge($currentState, ['_default_image_index' => $index]));
        }
    }

    public function removeMedia($mediaId)
    {
        $currentState = $this->form->getState();
        if (isset($currentState['images'])) {
            // First remove the media from database
            $media = Media::find($mediaId);
            if ($media) {
                $media->delete();
            }

            // Convert mediaId to string for comparison since UUIDs are strings
            $mediaIdString = (string) $mediaId;

            // Filter out the removed media ID from form state
            $currentState['images'] = array_values(array_filter($currentState['images'], function ($uuid) use ($mediaIdString) {
                return $uuid !== $mediaIdString;
            }));

            // Update the form state
            $this->form->fill($currentState);
        }

        // Remove from media library if record exists
        if ($this->record instanceof \App\Models\Product) {
            $media = $this->record->getMedia('product-images')->find($mediaId);
            if ($media) {
                $media->delete();
            }
        }
    }

    public function clearLivewireTmpFiles()
    {
        $tmpPath = storage_path('app/private/livewire-tmp');
        File::cleanDirectory($tmpPath); // Removes all temporary files
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make()->schema([
                TextInput::make('name')
                    ->label('Product Name')
                    ->validationAttribute('product name')
                    ->label(new HtmlString('Product Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->rules(fn($record) => ['required', 'max:255',  Rule::unique('products', 'name')->ignore($record->id)]),
                Select::make('brand_id')
                    ->label(new HtmlString('Brand<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('brand')
                    ->options(fn() => Brand::pluck('name', 'id'))
                    ->rules(['required']),
                Select::make('category_id')
                    ->label(new HtmlString('Category<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('category')
                    ->options(fn() => Category::whereNull('parent_id')->pluck('name', 'id'))
                    ->reactive()
                    ->rules(['required']),
                Select::make('sub_category_id')
                    ->label('Sub Category')
                    ->live()
                    ->options(function (Get $get) {
                        if (!empty($get('category_id'))) {
                            $subCategories = Category::where('parent_id', $get('category_id'))->pluck('name', 'id');
                            return $subCategories;
                        }
                        return [];
                    }),
                SpatieMediaLibraryFileUpload::make('mda_document')
                    ->collection('mda-documents')
                    ->disk('s3')
                    ->visibility(visibility: 'public')
                    ->columnSpanFull()
                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                    ->maxSize(2048)
                    ->visible(function (Get $get) {
                        return (bool) Category::find($get('sub_category_id'))?->is_mda;
                    })
                    ->reactive(),
                Select::make('generic_name_id')
                    ->label(new HtmlString('Generic Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->rules(['required'])
                    ->options(fn() => GenericName::pluck('name', 'id')),
                TextInput::make('sku')
                    ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('SKU')
                    ->rules(fn($record) => ['required', 'max:255', Rule::unique('products', 'sku')->ignore($record->id)]),
                Select::make('distributor_ids')
                    ->relationship('distributors', 'name')
                    ->createOptionForm([
                        Section::make()->schema([
                            TextInput::make('name')
                                ->label(new HtmlString("Distributor Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                ->maxLength(100)
                                ->placeholder('Enter Distributor Name')
                                ->rules([
                                    'required',
                                    // 'regex:/^[\w\s\p{P}]+$/u',
                                    'max:100',
                                    fn(Get $get) => new CaseSensitiveUnique(Distributor::class, 'name', $get('id'))
                                ])
                                ->validationMessages([
                                    'required' => __('message.distributor.required'),
                                    // 'regex' => __('message.distributor.regex'),
                                    'max' => __('message.distributor.max'),
                                    'App\\Rules\\CaseSensitiveUnique' => __('message.distributor.case_sensitive_unique'),
                                ]),

                        ]),
                    ])
                    ->multiple()
                    ->label(new HtmlString('Distributor<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('distributor')
                    ->rules(['required']),
                Radio::make('is_prescription_required')
                    ->label('Prescription Required')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ])
                    ->inline()
                    ->default(0)

            ])->columns(2),
            Section::make()->schema([
                DragDrop::make('images')
                    // ->markAsRequired(false)
                    ->rules(['required'])
                    // ->required()
                    ->multiple()
                    ->validationAttribute('images')
                    ->label(new HtmlString('Images'))
                    ->validationMessages([
                        'image' => 'The :attribute must be an image.',
                    ])
                    ->collection('product-images')
                    ->dehydrated(true)
                    ->beforeStateDehydrated(function ($state) {
                        return array_filter((array) $state);
                    })
            ]),
            Section::make()
                ->heading('Product Description')
                ->schema([
                    Tabs::make('Product Details')
                        ->schema([
                            Tab::make('Product Description')
                                ->schema([
                                    RichEditor::make('product_description') // need to add migration
                                ]),
                            Tab::make('Key Ingredients')
                                ->schema([
                                    RichEditor::make('description_ingredients') // need to add migration
                                ]),
                            Tab::make('Storage Instructions')
                                ->schema([
                                    RichEditor::make('description_storage_instructions')  // need to add migration
                                ]),
                            Tab::make('Usage/Indication')
                                ->schema([
                                    RichEditor::make('description_indications')
                                ]),
                            Tab::make('Contradiction')
                                ->schema([
                                    RichEditor::make('description_contradictions')
                                ]),
                            Tab::make('How to Use')
                                ->schema([
                                    RichEditor::make('description_how_to_use') // need to add migration
                                ]),
                            Tab::make('Safety Information/Pregnancy')
                                ->schema([
                                    RichEditor::make('description_safety_information') // need to add migration
                                ]),
                            Tab::make('Dosage Information')
                                ->schema([
                                    RichEditor::make('description_dosage')
                                ]),
                            Tab::make('Side Effects')
                                ->schema([
                                    RichEditor::make('description_side_effects')
                                ]),
                        ])
                ]),
            //this code is not updated
            Section::make('Stock Details')
                ->schema([

                    Select::make('container_id')
                        ->searchable(true)
                        ->preload()
                        ->label(fn()=> PackagingToolTip::tooltip())
                        ->rules(['required'])
                        ->live()

                        ->validationAttribute('packaging')
                        ->options(fn() =>  Container::all()->pluck('name', 'id')),
                    Select::make('dosage_foams_id')
                        ->searchable(true)
                        ->preload()
                        ->label(new HtmlString('Product Form<span class="text-red-500" style="color:red;">*</span>'))
                        ->validationAttribute('Product Form')
                        ->rules(['required'])
                        ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
                    TextInput::make('productData.stock')
                        ->placeholder('Stock')
                        ->label(new HtmlString('Stock<span class="text-red-500" style="color:red;">*</span>'))
                        ->rules(['numeric', 'gt:0'])
                        ->visible(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal')
                        ->numeric()
                        ->maxValue(99999999)
                        ->requiredIf(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal', true)
                        ->rules(function (Get $get) {
                            $lowStock = $get('productData.low_stock');
                            if ($get('in_stock') == 'yes') {
                                return ['required', 'max:99999999', 'gte:' . $lowStock];
                            }
                            return ['sometimes', 'max:99999999', 'gte:' . $lowStock];
                        })
                        ->validationAttribute('stock'),
                    TextInput::make('quantity_per_unit')
                        ->placeholder('Volume')
                        ->validationAttribute('volume')
                        ->rules(['required', 'max:99999999', 'gt:0'])
                        ->numeric()
                        ->maxValue(99999999)
                        ->rules(['required', 'max:99999999'])
                        ->label(function ($record, Get $get) {
                            if (!empty($get('container_id'))) {
                                $container = Container::find($get('container_id'))?->name;
                                return new HtmlString("Volume by $container<span class='text-red-500' style='color:red;'>*</span>");
                            }
                            $container = $record->container?->name;
                            return new HtmlString("Volume by $container<span class='text-red-500' style='color:red;'>*</span>");
                        }),
                    Select::make('unit_id')
                        ->searchable(true)
                        ->preload()
                        ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>'))
                        ->rules(['required', 'max:99999999'])
                        ->validationAttribute('volume unit')
                        ->options(fn() => Unit::all()->pluck('name', 'id')),
                    
                    TextInput::make('productData.low_stock')
                        ->placeholder('Low stock trigger value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return ['required', 'numeric', 'max:99999999', 'gt:0'];
                            }
                            return ['numeric', 'max:99999999', 'gt:0'];
                        })
                        ->numeric()
                        ->maxValue(99999999)
                        ->validationAttribute('low stock trigger value')
                        ->visible(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return true;
                            }
                            return false;
                        })
                        ->label(function (Get $get) {
                            if ($get('in_stock') == 'yes') {
                                return new HtmlString('Low stock trigger value<span class="text-red-500" style="color:red;">*</span>');
                            }
                            return 'Low stock trigger value';
                        }),
                    DatePicker::make('productData.expires_on_after')
                        ->format(getFormatedDateForPc())
                        ->label(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return new HtmlString('Expiry Date<span class="text-red-500" style="color:red;">*</span>');
                            }
                            return 'Expiry Date 416';
                        })
                        ->visible(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return true;
                            }
                            return false;
                        })
                        ->minDate(today())
                        ->displayFormat(function () {
                            $pcDetail = PcDetail::where('user_id', auth()->user()->id)->first();
                            return $pcDetail->date_format ?? 'd/m/Y';
                        })
                        ->validationAttribute('Expiry Date')
                        ->rules(function (Get $get) {
                            if ($get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal') {
                                return ['required', 'date', 'after_or_equal:today'];
                            }
                            return ['sometimes', 'date', 'after_or_equal:today'];
                        }),
                    TextInput::make('weight')
                        ->label(new HtmlString('Weight (gms)<span class="text-red-500" style="color:red;">*</span>'))
                ])->columns(5),
        ]);
    }

    public function mutateFormDataBeforeSave(array $data): array
    {
        $this->oldData = Product::find($this->record->id)->toArray();
        // Store images and default index for processing after save
        if (isset($data['images'])) {
            $this->images = $data['images'];
            unset($data['images']); // Remove from data to prevent form processing conflicts
        }
        
        // Store default image index if set
        if (isset($data['_default_image_index'])) {
            $this->defaultIndex = $data['_default_image_index'];
            unset($data['_default_image_index']);
        }
        
        return $data;
    }

    public function afterSave(): void
    {
        $product = $this->record;
        $changes = $product->getChanges();
        $ignoreFields = ['updated_at', 'created_at', 'id', 'default_image_id'];
        $changes = array_diff_key($changes, array_flip($ignoreFields));
        $changeKeys = array_keys($changes);
        $oldFinal = collect($this->oldData)->only($changeKeys)->toArray();
        
        // Handle default image setting
        if (isset($this->defaultIndex) && $this->defaultIndex !== null) {
            $mediaCollection = $product->getMedia('product-images');
            
            // Reset all media as non-default first
            $product->update(['default_image_id' => null]);
            
            // Set the selected image as default
            if ($mediaCollection->count() > $this->defaultIndex) {
                $media = $mediaCollection->get($this->defaultIndex);
                if ($media) {
                    $product->update(['default_image_id' => $media->id]);
                }
            } elseif ($mediaCollection->count() > 0) {
                // Fallback to first image if index is out of bounds
                $product->update(['default_image_id' => $mediaCollection->first()->id]);
            }
        } elseif (!$product->default_image_id && $product->getMedia('product-images')->count() > 0) {
            // Set first image as default if no default is set
            $product->update(['default_image_id' => null]);
        } else {
            $product->update(['default_image_id' => null]);
        }
        $name = getUser(Auth::user())->name;
        $oldData = ['old'=>$oldFinal];
        $newData = ['attributes'=>$changes];
        $activityData = array_merge($oldData, $newData);
        activity()
            ->performedOn($product)
            ->causedBy(Auth::user())
            ->withProperties($activityData)
            ->log("Master Product updated By PC $name ($product->name)");
        
        // Clear temporary variables
        $this->images = null;
        $this->defaultIndex = null;
    }

    protected function getProductDataForLogging($product): array
    {
        $productData = $product->productDataForPc(getUser(auth()->user())->id)?->first();
        $expDate =  $productData?->expires_on_after ? Carbon::parse($productData?->expires_on_after)->format('Y-m-d') : null;
        return array_filter([
            'name' => $product->name,
            'sku' => $product->sku,
            'category' => $product->category?->name,
            'sub_category' => $product->subCategory?->name,
            'brand' => $product->brand?->name,
            'generic_name' => $product->genericName?->name,
            'distributors' => $product->distributors?->pluck('name')->implode(', '),
            'weight' => $product->weight,
            'container' => $product->container?->name,
            'unit' => $product->unit?->name,
            'dosage_form' => $product->dosageForm?->name,
            'quantity_per_unit' => $product->quantity_per_unit,
            'stock' => $productData?->stock,
            'low_stock' => $productData?->low_stock,
            'expires_on_after' => $expDate,
            'product_description' => $product->product_description,
            'description_ingredients' => $product->description_ingredients,
            'description_storage_instructions' => $product->description_storage_instructions,
            'description_indications' => $product->description_indications,
            'description_contradictions' => $product->description_contradictions,
            'description_how_to_use' => $product->description_how_to_use,
            'description_safety_information' => $product->description_safety_information,
            'description_dosage' => $product->description_dosage,
            'description_side_effects' => $product->description_side_effects,
        ], fn($value) => !is_null($value) && $value !== '');
    }
}
