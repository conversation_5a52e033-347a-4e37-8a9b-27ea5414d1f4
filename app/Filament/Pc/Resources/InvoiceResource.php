<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\InvoiceResource\Pages;
use App\Filament\Pc\Resources\InvoiceResource\RelationManagers;
use App\Models\ClinicDetail;
use App\Models\SubOrder;
use Filament\Tables\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Support\Facades\Cookie;

class InvoiceResource extends Resource
{
    protected static ?string $model = SubOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
    //protected static ?string $navigationGroup = 'Orders Management';
    protected static ?string $navigationLabel = 'Invoices';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table
            ->query(
                SubOrder::with(['order', 'user', 'orderProducts', 'orderProducts.product', 'payoutSubOrder.Payout'])
                    ->selectRaw('*, (total_sub_order_value - (
                    SELECT COALESCE(SUM(total_commission), 0)
                    FROM order_products
                    WHERE sub_order_id = sub_orders.id
                )) AS net_earnings')
                    ->where('user_id', $user)
                    ->where(function ($query) {
                        $query->where('status', 'delivered')
                            ->orWhere('status', 'accepted');
                    })
                    ->withCount([
                        'orderProducts as order_products_count' => function (Builder $query) {
                            $query->select(DB::raw('COALESCE(COUNT(*), 0)'));
                        },
                    ])
                    ->withSum('orderProducts', 'total_commission')
            )
            ->defaultSort('id', 'desc')
            ->recordUrl(fn(SubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->id]))
            ->emptyStateHeading('No Invoice records found')
            ->columns([
                TextColumn::make('order.order_number')
                    // ->url(fn(SubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->id])) // Change order_id to record
                    // ->formatStateUsing(fn(string $state): string => !empty($state) ? '#' . $state  : '-')->html()
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()

                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('invoice_po_number')->label('PS Invoice ID')->sortable()->searchable()->toggleable(),
                TextColumn::make('total_sub_order_value')
                    ->label('Order Amount')
                    ->formatStateUsing(fn($state) => (!empty($state)) ? 'RM ' .  number_format($state ?? 0, 2) : '-')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('order_products_sum_total_commission')
                    ->label('Admin Fees')
                    ->sortable()
                    ->toggleable()
                    ->default('-')
                    ->formatStateUsing(fn($state) => (is_numeric($state) || is_float($state)) ? 'RM ' .  number_format($state ?? 0, 2) : '-'),
                TextColumn::make('commision_invoice')
                    ->label('Dpharma Invoice')
                    ->toggleable()
                    ->view('components.table-columns.view-dpharma-invoice-icon')

                    //  ->getStateUsing(function ($record) {
                    //     return $record->invoice_path ? 'View' : 'No Receipt';
                    // })
                    // ->url(function ($record) {
                    //     $fileName = $record->invoice_path;
                    //     if (!$fileName) {
                    //         return null;
                    //     }

                    //     $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                    //     $filePath = $invoicePath . $fileName;

                    //     return Storage::disk('s3')->temporaryUrl(
                    //         $filePath,
                    //         now()->addMinutes(15) // URL will be valid for 15 minutes
                    //     );
                    // })
                    ->getStateUsing(function ($record) {
                        return '-';
                    })
                    ->icon(function () {
                        return '';
                    })
                    ->color(function ($record) {
                        return  'gray';
                    })
                    ->sortable(false)
                    ->toggleable(),
                TextColumn::make('net_earnings')
                    ->label('Net Earnings')
                    ->prefix('RM ')
                    ->getStateUsing(fn($record) => number_format($record->net_earnings, 2))
                    ->sortable()
                    ->toggleable(),
                ViewColumn::make('purchase_order_download')
                    ->label('Purchase Order')
                    ->toggleable()
                    ->view('components.table-columns.view-invoice-icon'),

                ViewColumn::make('invoice_download')
                    ->label('PS Invoice')
                    ->toggleable()
                    ->view('components.table-columns.view-ps-invoice-icon'),
                // TextColumn::make('invoice_download')
                // ->label('PS Invoice')
                // ->getStateUsing(function ($record) {
                //     return $record->getFirstMedia('invoices') ? 'View' : '-';
                // })
                // ->url(function ($record) {
                //     $media = $record->getFirstMedia('invoices');
                //     return $media ? $media->getFullUrl() : null;
                // })
                // ->openUrlInNewTab()
                // ->icon(function($record){
                //     return $record->getFirstMedia('invoices') ? 'heroicon-o-eye' : '';
                // })
                // ->color(function ($record) {
                //     return $record->getFirstMedia('invoices') ? 'primary' : 'gray';
                // })
                // ->sortable(false)
                // ->toggleable(),
                // TextColumn::make('commision_download')
                // ->label('Order Commission')
                // ->getStateUsing(function ($record) {
                //     return 'No Receipt';
                // }),
                TextColumn::make('payment_type')
                    ->label('Credit Line')
                    ->sortable()
                    ->getStateUsing(function (SubOrder $record) {
                        return $record->payment_type === 'credit_line' ? 'Yes' : 'No';
                    })
                    ->color(function (string $state) {
                        return $state === 'Yes' ? 'success' : 'danger';
                    })
                    ->icon(function (string $state) {
                        return $state === 'Yes' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
                    })
                    ->toggleable(),
                TextColumn::make('payoutSubOrder.Payout.payout_status')
                    ->label('Payout Status')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->badge()
                    ->default('pending')
                    ->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
                    ->color(function ($state) {
                        $st = match ($state) {
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),



            ])
            ->actionsColumnLabel('Action')
            ->filters([])
            ->actions([
                Action::make('view')
                    ->url(function (SubOrder $record) {
                        Cookie::queue('source', 'invoice');
                        return route('filament.pc.resources.orders.view', ['record' => $record->id]);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Order Details')
                    ->color('gray')
                    ->label(false),

                // ]),
            ])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([])]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/'),
            'create' => Pages\CreateInvoice::route('/create'),
            'edit' => Pages\EditInvoice::route('/{record}/edit'),
        ];
    }
}
