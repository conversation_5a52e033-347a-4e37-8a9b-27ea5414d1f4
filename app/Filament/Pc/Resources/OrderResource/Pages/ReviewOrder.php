<?php

namespace App\Filament\Pc\Resources\OrderResource\Pages;

use App\Filament\Pc\Resources\InvoiceResource;
use Filament\Resources\Pages\Page;
use App\Models\Order;
use App\Models\SubOrder;
use App\Filament\Pc\Resources\OrderResource;
use App\Filament\Pc\Resources\OutStandingPaymentResource;
use App\Filament\Pc\Resources\ShipmentResource;
use App\Mail\PCActionMail\ApprovedOrderMail;
use App\Mail\PCActionMail\RejectOrderMail;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithFileUploads;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Models\ClinicCreditHistory;
use Filament\Actions\Action;
use App\Models\Transaction;
use App\Models\User;
use App\Service\EcommerceService;
use Illuminate\Support\Facades\Log;

class ReviewOrder extends Page
{
    use WithFileUploads;

    protected static string $resource = OrderResource::class;
    protected static string $view = 'filament.pc.resources.order-resource.pages.review-order';

    public $order;
    public $batchQuantities = [];
    public $selectedBatches = [];
    public $productIds = [];
    public $productBatchIds = [];
    public $assignedQuantities = [];
    public $validationErrors = [];
    public $showInvoiceModal = false;
    public $invoiceNumber = '';
    public $invoiceFile;
    public $productsData = [];

    protected $listeners = ['batchSelectionChanged' => 'handleBatchSelection'];

    public static function canAccess(array $parameters = []): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()?->can('all-orders_review order');
    }
    public function getTitle(): string
    {
        return 'Review Order #' . $this->order->order->order_number;
    }

    public function getBreadcrumbs(): array
    {
        $orderType = request()->query('type');
        $routeName = $orderType === 'credit_line' ? 'filament.pc.resources.credit-line-orders.index' : 'filament.pc.resources.orders.index';
        $title = $orderType === 'credit_line' ? 'Credit Line Orders' : 'All Orders';

        if ($orderType === 'credit_line') {
            $detailRoute = route('filament.pc.resources.orders.view', [
                'record' => $this->order->id,
                'type' => request()->query('type') ?? 'allorder'
            ]);
        } else {
            $detailRoute =  route('filament.pc.resources.orders.view', ['record' => $this->order->id, 'type' => request()->query('type') ?? 'allorder']);
        }


        return [
            1 => 'Orders Management',
            route($routeName) => $title,
            $detailRoute => 'Order Details',
            4 => 'Review Order'
        ];
    }

    public function mount($record)
    {

        $this->order = SubOrder::with(['order', 'user', 'user.warehouseInfo'])->where('id', $record)->first();
        $this->initializeArrays();
        $this->loadProductsData();
        $this->invoiceNumber = 'PS-' . $this->order->order->user_id . $this->order->order->order_number;
        session(['orderType' => request()->get('type')]);
    }

    protected function initializeArrays()
    {
        foreach ($this->order->orderProducts as $index => $product) {
            $this->batchQuantities[$index] = [];
            $this->selectedBatches[$index] = [];
            $this->productIds[$index] = $product->product_id;
        }
    }

    public function updateBatchQuantity($productIndex, $batchIndex, $quantity)
    {
        $this->batchQuantities[$productIndex][$batchIndex] = (int)$quantity;

        // Store current quantities before validation
        $currentQuantities = $this->batchQuantities[$productIndex];

        $this->validateBatchQuantity($productIndex);

        // Restore quantities that were not specifically invalid
        foreach ($currentQuantities as $bIndex => $qty) {
            if ($bIndex !== $batchIndex && !isset($this->validationErrors["product.{$productIndex}.batch.{$bIndex}"])) {
                $this->batchQuantities[$productIndex][$bIndex] = $qty;
            }
        }
    }

    protected function validateBatchQuantity($productIndex)
    {
        // Clear previous errors for this product
        foreach ($this->validationErrors as $key => $value) {
            if (strpos($key, "product.{$productIndex}") === 0) {
                unset($this->validationErrors[$key]);
            }
        }

        $product = $this->order->orderProducts[$productIndex];
        $productRelation = \App\Models\ProductRelation::where('product_id', $product->product_id)
            ->where('user_id', getUser(auth()->user())->id)
            ->first();


        $productRelation = \App\Models\ProductRelationStock::where('product_relation_id', $productRelation->id)
            ->first();
        if (!$productRelation) {
            $this->validationErrors["product.{$productIndex}"] = "Product relation not found";
            return false;
        }

        $isBatchWiseStock = $productRelation->is_batch_wise_stock ?? false;

        $totalStock = $productRelation->stock ?? 0;
        // if ($product->quantity > $totalStock) {
        //     $this->validationErrors["product.{$productIndex}.stock"] = "Order quantity ({$product->quantity}) cannot exceed total available stock ({$totalStock})";
        //     return false;
        // }
        $finalQuantity = $product->quantity + $product->bonus_final_qty;
        if ($isBatchWiseStock) {
            $batches = \App\Models\ProductBatch::where('product_id', $product->product_id)
                ->where('user_id', getUser(auth()->user())->id)
                ->get();
            //dd($this->batchQuantities);
            //$totalAssigned = array_sum($this->batchQuantities[$productIndex] ?? []);
            $totalAssigned = array_sum(array_map('intval', $this->batchQuantities[$productIndex] ?? []));

            // Store validation state for each batch
            $batchValidationState = [];
            foreach (($this->batchQuantities[$productIndex] ?? []) as $batchIndex => $quantity) {
                $batch = $batches[$batchIndex] ?? null;
                if ($batch && $quantity > $batch->available_stock) {
                    $this->validationErrors["product.{$productIndex}.batch.{$batchIndex}"] = "Assigned quantity ({$quantity}) cannot exceed available stock ({$batch->available_stock})";
                    $batchValidationState[$batchIndex] = false;
                } else {
                    $batchValidationState[$batchIndex] = true;
                }
            }



            // Only validate total if all individual batch quantities are valid
            if (!in_array(false, $batchValidationState, true) && $totalAssigned !== $finalQuantity) {
                $this->validationErrors["product.{$productIndex}.total"] = "Total assigned quantity must match order quantity ({$finalQuantity})";
                return false;
            }

            return !in_array(false, $batchValidationState, true);
        } else {
            if ($product->quantity > $totalStock) {
                $this->validationErrors["product.{$productIndex}.stock"] = "Order quantity ({$finalQuantity}) cannot exceed total available stock ({$totalStock})";
                return false;
            }
        }

        return true;
    }

    public function validateAndShowInvoiceForm()
    {
        $this->validationErrors = [];
        $hasErrors = false;

        foreach ($this->order->orderProducts as $index => $product) {
            if (!$this->validateBatchQuantity($index)) {
                $hasErrors = true;
            }
        }

        if (!$hasErrors) {
            $this->showInvoiceModal = true;
        }
    }

    public function verifyInvoiceAndSubmit()
    {
        // Verify invoice number
        $isValid = $this->order->subOrder()
            ->where('user_id', getUser(auth()->user())->id)
            ->where('invoice_po_number', $this->invoiceNumber)
            ->exists();

        if (!$isValid) {
            $this->validationErrors['invoice'] = 'Invalid invoice number';
            return;
        }

        $this->submitOrder();
    }

    public function submitOrder()
    {
        try {
            DB::beginTransaction();

            foreach ($this->order->orderProducts as $index => $product) {
                $productRelation = \App\Models\ProductRelation::where('product_id', $product->product_id)
                    ->where('user_id', getUser(auth()->user())->id)
                    ->first();

                $productRelation = \App\Models\ProductRelationStock::whereIn('product_relation_id1', $productRelation->pluck('id'))
                    ->first();
                $isBatchWiseStock = $productRelation->is_batch_wise_stock ?? false;

                if ($isBatchWiseStock) {
                    $batches = \App\Models\ProductBatch::where('product_id', $product->product_id)
                        ->where('user_id', getUser(auth()->user())->id)
                        ->get();

                    foreach (($this->batchQuantities[$index] ?? []) as $batchIndex => $quantity) {
                        if ($quantity > 0) {
                            $batch = $batches[$batchIndex];

                            // Create order product batch record
                            \App\Models\OrderProductBatch::create([
                                'product_id' => $product->product_id,
                                'order_id' => $this->order->order->id,
                                'suborder_id' => $this->order->id,
                                'product_batch_id' => $batch->id,
                                'available_batch_stock' => $batch->available_stock,
                                'assign_stock' => $quantity,
                                'order_product_id' => $product->id
                            ]);

                            // Update batch stock
                            DB::table('products_batch')
                                ->where('id', $batch->id)
                                ->update([
                                    'available_stock' => DB::raw("available_stock - $quantity")
                                ]);
                        }
                    }
                } else {
                    // For non-batch products, create a single record
                    \App\Models\OrderProductBatch::create([
                        'product_id' => $product->product_id,
                        'order_id' => $this->order->order->id,
                        'suborder_id' => $this->order->id,
                        'product_batch_id' => null,
                        'available_batch_stock' => $productRelation->stock,
                        'assign_stock' => $product->quantity,
                        'order_product_id' => $product->id
                    ]);

                    // Update product stock directly
                    DB::table('products_relation')
                        ->where('id', $productRelation->id)
                        ->update([
                            'stock' => DB::raw("stock - {$product->quantity}")
                        ]);
                }
                // Update order status
                $this->order->update([
                    'status' => 'accepted'
                ]);
                $this->order->orderProducts()->update([
                    'status' => 'accepted'
                ]);
            }

            DB::commit();
            $this->showInvoiceModal = false;
            Notification::make()
                ->title('Success')
                ->body('Order reviewed successfully')
                ->success()
                ->send();

            //Mail::to($this->order->user->email)->send(new ApprovedOrderMail($this->order));
            //sending mail here to facility and admins
            $order = Order::with(['orderProductsGrouped' => function ($query) {
                $query->select('order_id', 'status', DB::raw('COUNT(*) as count'))
                    ->groupBy('order_id', 'status');
            }, 'subOrder', 'subOrder.user', 'orderProducts', 'orderProducts.product', 'user'])->find($this->order->order_id);

            $userMail = $this->order?->user;
            $facilityName = $userMail->name;
            if ($userMail && !empty($order->subOrder[0]?->user->timezone)) {
                $timezone = $order->subOrder[0]?->user->timezone;
                $orderDate = Carbon::parse($order->created_at)->timezone($timezone)->format('d M, Y h:i A');
            } else {
                $orderDate = Carbon::parse($order->created_at)->format('d-m-Y H:i:s');
            }
            $paymentType = match ($this->order?->payment_type) {
                'pay_now'    => 'Pay Now',
                'pay_later'  => 'Pay Later',
                'credit_line' => 'Credit Line',
                default      => 'Unknown',
            };

            $orderDetails = [
                'Order Number'  => "#" . $this->order->order_number,
                'Order Date'    => $orderDate ?? Carbon::parse($order->created_at)->format('d-m-Y H:i:s'),
                'Total Items'   => $this->order->orderProducts->count(),
                'Supplier'      => pcCompanyName($this->order->user->pcDetails) ?? 'N/A',
                'Order Total'   => "RM " . number_format($order->subOrder[0]?->total_amount, 2),
                'Payment Type'  => $paymentType ?? 'N/A',
            ];
            $facilityName = $order->user->name;
            //Send Email Approval to admin and facility
            Mail::to($order->user->email)->send(new ApprovedOrderMail($this->order, $facilityName, $orderDetails));
            // $superAdminEmails  = getAdminData();
            // foreach ($superAdminEmails as $key => $adminData) {
            //     Mail::to($adminData->email)->send(new ApprovedOrderMail($this->order, $adminData->name, $orderDetails));
            // }

            //email sending ends

            return redirect()->to($this->getResource()::getUrl('index'));
        } catch (\Exception $e) {
            DB::rollBack();
            $this->validationErrors['system'] = 'Failed to process order: ' . $e->getMessage();
        }
    }

    // public function saveInvoice()
    // {
    //     // Get the first sub order since we're dealing with a collection
    //     $subOrder = $this->order;

    //     if (!$subOrder) {
    //         Notification::make()
    //             ->title('Error')
    //             ->body('No sub-order found for this order.')
    //             ->danger()
    //             ->send();
    //         return;
    //     }

    //     $this->validate([
    //         'invoiceNumber' => [
    //             'required',
    //             'string',
    //             'max:255',
    //             function ($attribute, $value, $fail) use ($subOrder) {
    //                 // Update the entered invoice number in the sub_order table
    //                 $subOrder->invoice_po_number = $value;
    //                 $subOrder->save();
    //             },
    //         ],
    //         'invoiceFile' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
    //     ]);

    //     try {
    //         DB::beginTransaction();

    //         \App\Models\Media::where([
    //             'model_type' => get_class($subOrder),
    //             'model_id' => $subOrder->id,
    //             'collection_name' => 'invoices'
    //         ])->delete();

    //         // Store the file
    //         if ($this->invoiceFile) {
    //             $path = $this->invoiceFile->store('invoices', 's3');

    //             $fileName = basename($path);

    //             // Generate UUID
    //             $uuid = (string) \Illuminate\Support\Str::uuid();

    //             // Create media record
    //             $media = new \App\Models\Media();
    //             $media->model_type = get_class($subOrder);
    //             $media->model_id = $subOrder->id;
    //             $media->uuid = $uuid;
    //             $media->collection_name = 'invoices';
    //             $media->name = $this->invoiceNumber;
    //             $media->file_name = $fileName;
    //             $media->mime_type = $this->invoiceFile->getMimeType();
    //             $media->disk = 's3';
    //             $media->conversions_disk = 's3';
    //             $media->size = $this->invoiceFile->getSize();
    //             $media->manipulations = '[]';
    //             $media->custom_properties = json_encode(['invoice_po_number' => $this->invoiceNumber]);
    //             $media->generated_conversions = '[]';
    //             $media->responsive_images = '[]';
    //             $media->order_column = 1;

    //             $media->save();
    //         }
    //         // Update sub_order with invoice number
    //         $subOrder->update([
    //             'invoice_po_number' => $this->invoiceNumber,
    //             'status' => 'accepted'
    //         ]);
    //         $subOrder->orderProducts()->update([
    //             'status' => 'accepted'
    //         ]);

    //         // Save batch information for each product
    //         foreach ($this->order->orderProducts as $index => $product) {
    //             $productRelation = \App\Models\ProductRelation::where('product_id', $product->product_id)
    //                 ->where('user_id', auth()->user()->id)
    //                 ->first();
    //             $productRelation = \App\Models\ProductRelationStock::where('product_relation_id', $productRelation->id)
    //                 ->first();
    //             $isBatchWiseStock = $productRelation->is_batch_wise_stock ?? false;

    //             if ($isBatchWiseStock) {
    //                 $batches = \App\Models\ProductBatch::where('product_id', $product->product_id)
    //                     ->where('user_id', auth()->user()->id)
    //                     ->get();

    //                 foreach (($this->batchQuantities[$index] ?? []) as $batchIndex => $quantity) {
    //                     if ($quantity > 0) {
    //                         $batch = $batches[$batchIndex];

    //                         // Create order product batch record
    //                         \App\Models\OrderProductBatch::create([
    //                             'product_id' => $product->product_id,
    //                             'order_id' => $this->order->order->id,
    //                             'suborder_id' => $this->order->id,
    //                             'product_batch_id' => $batch->id,
    //                             'available_batch_stock' => $batch->available_stock,
    //                             'assign_stock' => $quantity,
    //                             'created_at' => now(),
    //                             'updated_at' => now(),
    //                             'order_product_id' => $product->id
    //                         ]);

    //                         // Update batch stock
    //                         DB::table('products_batch')
    //                             ->where('id', $batch->id)
    //                             ->where('products_relation_id', $productRelation->product_relation_id)
    //                             ->update([
    //                                 'available_stock' => DB::raw("available_stock - $quantity")
    //                             ]);
    //                     }
    //                 }
    //             } else {

    //                 // For non-batch products, create a single record
    //                 \App\Models\OrderProductBatch::create([
    //                     'product_id' => $product->product_id,
    //                     'order_id' => $this->order->order->id,
    //                     'suborder_id' => $this->order->id,
    //                     'product_batch_id' => null,
    //                     'available_batch_stock' => $productRelation->stock,
    //                     'assign_stock' => $product->quantity,
    //                     'created_at' => now(),
    //                     'updated_at' => now(),
    //                     'order_product_id' => $product->id
    //                 ]);

    //                 // Update product stock directly
    //                 // DB::table('products_relation')
    //                 //     ->where('id', $productRelation->id)
    //                 //     ->update([
    //                 //         'stock' => DB::raw("stock - {$product->quantity}")
    //                 //     ]);

    //                 DB::table('product_relation_stocks')
    //                     ->where('product_relation_id', $productRelation->product_relation_id)
    //                     ->update([
    //                         'stock' => DB::raw("stock - {$product->quantity}")
    //                     ]);
    //             }
    //         }

    //         ReviewOrder::updateOrderStatus($subOrder->order_id);
    //         ReviewOrder::getApprovedOrderTotal($subOrder);
    //         ReviewOrder::payoutOrder($subOrder);
    //         $earnPoint = $this->order->earn_points ?? 0;
    //         DB::table('dpharma_points')->insert([
    //             'user_id'          => $this->order->order->user_id,
    //             'description'      => 'Order Approved - Points Successfully Rewarded',
    //             'points'           => $earnPoint, // Adjust points logic as needed
    //             'redeem'           => null,
    //             'balance'          => DB::raw("(
    //                                                     COALESCE(
    //                                                         (SELECT balance FROM dpharma_points
    //                                                          WHERE user_id = {$this->order->order->user_id}
    //                                                          ORDER BY created_at DESC LIMIT 1),
    //                                                         0
    //                                                     ) + {$earnPoint}
    //                                                 )"),
    //             'created_at'       => now(),
    //             'updated_at'       => now(),
    //             'reference_id'     => $this->order->id,
    //             'reference_value'  => 'suborder',
    //         ]);

    //         DB::commit();
    //         // Reset form and close modal
    //         $this->reset(['invoiceNumber', 'invoiceFile', 'showInvoiceModal']);
    //         Notification::make()
    //             ->title('Invoice uploaded successfully')
    //             ->success()
    //             ->send();

    //         $routeName = match (session('orderType')) {
    //             'credit_line' => route('filament.pc.resources.credit-line-orders.index', ['type' => 'credit_line']),
    //             default => route('filament.pc.resources.orders.index', []),
    //         };
    //         return redirect($routeName);
    //     } catch (\Exception $e) {
    //         DB::rollBack();
    //         Notification::make()
    //             ->title('Error uploading invoice')
    //             ->body($e->getMessage())
    //             ->danger()
    //             ->send();
    //     }
    // }

    public function saveInvoice()
    {
        $subOrder = $this->order;

        if (!$subOrder) {
            Notification::make()
                ->title('Error')
                ->body('No sub-order found for this order.')
                ->danger()
                ->send();
            return;
        }

        $this->validate([
            'invoiceNumber' => [
                'required',
                'string',
                'max:255',
            ],
            'invoiceFile' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:10240',
        ]);


        try {



            DB::beginTransaction();

            // Update invoice PO number
            // dd($subOrder);
            $status = 'accepted';
            // if($subOrder->user->warehouseInfo->warehouse_type != 'owned'){
            //     $status = 'delivered';
            //     $subOrder->deliver_at = now();
            // }

            // dd($status);
            $subOrder->invoice_po_number = $this->invoiceNumber;
            $subOrder->status = $status;
            $subOrder->approved_at = now();
            $subOrder->save();

            //Activity Log Start
            activity()
                ->causedBy(auth()->user())
                ->useLog('order_approval')
                ->performedOn($subOrder)
                ->withProperties([
                    'old' => ['status' => 'pending'],
                    'attributes' => [
                        'status' => $status,
                        'invoice_po_number' => $this->invoiceNumber,
                    ],
                ])
                ->log("Order #{$subOrder->order->order_number} status updated to accepted");
            //Activity Log End

            // Clear existing media
            $subOrder->clearMediaCollection('invoices');

            // Store the file
            if ($this->invoiceFile) {
                // Clean the filename to remove quotes and special characters
                $originalName = $this->invoiceFile->getClientOriginalName();
                $cleanName = $this->cleanFileName($originalName);

                $subOrder->addMedia($this->invoiceFile)
                    ->preservingOriginal()
                    ->usingName($cleanName)
                    ->usingFileName($cleanName)
                    ->withCustomProperties(['invoice_po_number' => $this->invoiceNumber])
                    ->toMediaCollection('invoices', 's3');

                //Activity Log Start
                activity()
                    ->causedBy(auth()->user())
                    ->useLog('invoice_upload')
                    ->performedOn($subOrder)
                    ->log("Invoice #{$this->invoiceNumber} uploaded for Order #{$subOrder->order->order_number}");
                //Activity Log End
            }

            // Update order products
            $subOrder->orderProducts()->update(['status' => 'accepted']);

            // dd('test');
            // Handle batch information
            foreach ($this->order->orderProducts as $index => $product) {

                $productRelation = \App\Models\ProductRelation::where('product_id', $product->product_id)
                    ->where('user_id', getUser(auth()->user())->id)
                    ->first();

                $productRelation = \App\Models\ProductRelationStock::where('product_relation_id', $productRelation->id)
                    ->first();
                $isBatchWiseStock = $productRelation->is_batch_wise_stock;

                if ($isBatchWiseStock) {
                    $batches = \App\Models\ProductBatch::where('product_id', $product->product_id)
                        ->where('user_id', getUser(auth()->user())->id)
                        ->get();
                    // dd($batches);
                    foreach (($this->batchQuantities[$index] ?? []) as $batchIndex => $quantity) {
                        if ($quantity > 0) {
                            $batch = $batches[$batchIndex];
                            $oldBatchStock = $batch->available_stock;
                            // dd($this->order->order->id);
                            \App\Models\OrderProductBatch::create([
                                'product_id' => $product->product_id,
                                'order_id' => $this->order->order->id,
                                'suborder_id' => $this->order->id,
                                'product_batch_id' => $batch->id,
                                'available_batch_stock' => $batch->available_stock,
                                'assign_stock' => $quantity,
                                'created_at' => now(),
                                'updated_at' => now(),
                                'order_product_id' => $product->id,
                            ]);

                            $productQuantity = $quantity;
                            // if ($product->stock_type == 'unit') {
                            //     $productQuantity = $productQuantity * 1;//(!empty($product->wholesale_pack_size) ? $product->wholesale_pack_size : 1);
                            // }

                            DB::table('products_batch')
                                ->where('id', $batch->id)
                                ->where('products_relation_id', $productRelation->product_relation_id)
                                ->update([
                                    'available_stock' => DB::raw("available_stock - $productQuantity"),
                                ]);

                            //ActivityLog start
                            activity()
                                ->causedBy(auth()->user())
                                ->useLog('batch_assignment')
                                ->withProperties([
                                    'old' => [
                                        'available_stock' => $oldBatchStock,
                                        'batch_name' => $batch->batch_name,
                                    ],
                                    'attributes' => [
                                        'available_stock' => $batch->available_stock - $quantity,
                                        'assigned_quantity' => $quantity,
                                        'batch_name' => $batch->batch_name,
                                    ],
                                ])
                                ->log("Batch {$batch->batch_name} has been assigned {$quantity} units for Order #{$subOrder->order->order_number}");
                            //ActivityLog end
                        }
                    }
                } else {
                    $oldStock = $productRelation->stock;
                    $productQuantity = $product->quantity;
                    if (!empty($product->bonus_final_qty)) {
                        $productQuantity += $product->bonus_final_qty;
                    }
                    // if ($product->stock_type == 'unit') {
                    //     $productQuantity = $productQuantity * 1; //!empty($product->wholesale_pack_size) ? $product->wholesale_pack_size : 1;
                    // }

                    \App\Models\OrderProductBatch::create([
                        'product_id' => $product->product_id,
                        'order_id' => $this->order->order->id,
                        'suborder_id' => $this->order->id,
                        'product_batch_id' => null,
                        'available_batch_stock' => $productRelation->stock,
                        'assign_stock' => $product->quantity,
                        'created_at' => now(),
                        'updated_at' => now(),
                        'order_product_id' => $product->id,
                    ]);

                    DB::table('product_relation_stocks')
                        ->where('product_relation_id', $productRelation->product_relation_id)
                        ->update([
                            'stock' => DB::raw("stock - {$productQuantity}"),
                        ]);

                    //ActivityLog start
                    activity()
                        ->causedBy(auth()->user())
                        ->useLog('stock_assignment')
                        ->withProperties([
                            'old' => [
                                'stock' => $oldStock,
                            ],
                            'attributes' => [
                                'stock' => $oldStock - $product->quantity,
                                'assigned_quantity' => $product->quantity,
                            ],
                        ])
                        ->log("Assigned {$product->quantity} units from stock for Order #{$subOrder->order->order_number}");
                    //ActivityLog end
                }
            }
            // dd('test');
            // Update order status and points
            ReviewOrder::updateOrderStatus($subOrder->order_id);
            ReviewOrder::getApprovedOrderTotal($subOrder);
            ReviewOrder::payoutOrder($subOrder);

            $earnPoint = $this->order->earn_points ?? 0;
            DB::table('dpharma_points')->insert([
                'user_id' => $this->order->order->user_id,
                'description' => 'Order ' . $this->order->order_number . 'Approved - Points Successfully Rewarded',
                //'description' => 'Order <a style="color:blue;" href="' . route('filament.admin.resources.orders.view', ['record' => $subOrder->order_id]) . '">' . $subOrder->order->order_number . '</a> Approved - Points Successfully Rewarded',
                'points' => $earnPoint,
                'redeem' => null,
                'balance' => DB::raw("(
                COALESCE(
                    (SELECT balance FROM dpharma_points
                     WHERE user_id = {$this->order->order->user_id}
                     ORDER BY created_at DESC LIMIT 1),
                    0
                ) + {$earnPoint}
            )"),
                'created_at' => now(),
                'updated_at' => now(),
                'reference_id' => $this->order->id,
                'reference_value' => 'suborder',
            ]);

            DB::commit();

            // Reset form and redirect
            $this->reset(['invoiceNumber', 'invoiceFile', 'showInvoiceModal']);

            //sending Email to admin and faclity for approval
            $order = Order::with(['orderProductsGrouped' => function ($query) {
                $query->select('order_id', 'status', DB::raw('COUNT(*) as count'))
                    ->groupBy('order_id', 'status');
            }, 'subOrder', 'subOrder.user', 'orderProducts', 'orderProducts.product', 'user'])->find($this->order->order_id);

            $userMail = $this->order?->user;
            $facilityName = $userMail->name;

            if ($userMail && !empty($order->subOrder[0]?->user->timezone)) {
                $timezone = $order->subOrder[0]?->user->timezone;
                $orderDate = Carbon::parse($order->created_at)->timezone($timezone)->format('d M, Y h:i A');
            } else {
                $orderDate = Carbon::parse($order->created_at)->format('d-m-Y H:i:s');
            }
            $paymentType = match ($this->order?->payment_type) {
                'pay_now'    => 'Pay Now',
                'pay_later'  => 'Pay Later',
                'credit_line' => 'Credit Line',
                default      => 'Unknown',
            };

            $orderDetails = [
                'Order Number'      => "#" . $subOrder->order->order_number,
                'Order Date'    => $orderDate ?? Carbon::parse($order->created_at)->format('d-m-Y H:i:s'),
                'Total Items'   => $this->order->orderProducts->count(),
                'Supplier'   => pcCompanyName($this->order->user->pcDetails) ?? 'N/A',
                'Order Total'   => "RM " . number_format($order->subOrder[0]?->total_amount, 2),
                'Payment Type'  => $paymentType ?? 'N/A',
            ];
            $facilityName = $order->user->name;
            //Send Email Approval to admin and facility
            Mail::to($order->user->email)->send(new ApprovedOrderMail($this->order, $facilityName, $orderDetails));

            // $superAdminEmails  = getAdminData();
            // foreach ($superAdminEmails as $key => $adminData) {
            //     Mail::to($adminData->email)->send(new ApprovedOrderMail($this->order, $adminData->name, $orderDetails));
            // }

            //email sending ends here

            Notification::make()
                ->title('Order accepted successfully')
                ->success()
                ->send();

            $routeName = match (session('orderType')) {
                'credit_line' => route('filament.pc.resources.credit-line-orders.index', ['type' => 'credit_line']),
                default => route('filament.pc.resources.orders.index', []),
            };
            return redirect($routeName);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error uploading invoice in saveInvoice', [
                'suborder_id' => $subOrder->id,
                'error' => $e->getMessage(),
            ]);
            Notification::make()
                ->title('Error uploading invoice')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function handleBatchSelection($index)
    {

        // Clear quantities for unselected batches
        $this->batchQuantities[$index] = array_intersect_key(
            $this->batchQuantities[$index] ?? [],
            array_flip($this->selectedBatches[$index] ?? [])
        );

        // Initialize quantities for newly selected batches
        foreach ($this->selectedBatches[$index] ?? [] as $batchIndex) {
            if (!isset($this->batchQuantities[$index][$batchIndex])) {
                $this->batchQuantities[$index][$batchIndex] = 0;
            }
        }

        // Clear validation errors for this product
        unset($this->validationErrors["product.{$index}"]);
        unset($this->validationErrors["product.{$index}.total"]);
        foreach (($this->batchQuantities[$index] ?? []) as $batchIndex => $_) {
            unset($this->validationErrors["product.{$index}.batch.{$batchIndex}"]);
        }
    }

    public function removeInvoiceFile()
    {
        $this->invoiceFile = null;
    }

    protected function getHeaderActions(): array
    {
        $user = getUser(auth()->user())->id;
        // dd($this->order->status);
        return [
            Actions\Action::make('review_order')
                ->label('Accept')
                ->color('success')
                ->visible(function () {
                    $pending = $this->order->status === 'pending'  || $this->order->status == null;
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $pending && ($isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('all-orders_confirm order'));
                })
                // ->hidden(fn() => \App\Models\OrderProductBatch::where('order_id', $this->order->id)->exists())
                ->action(function () {
                    $this->validateAndShowInvoiceForm();
                }),

            Actions\Action::make('reject')
                ->label('Reject')
                ->modalHeading('Reason for Reject Order #' . $this->order->order->order_number)
                ->visible(function () {
                    $pending = $this->order->status === 'pending'  || $this->order->status == null;
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $pending && ($isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('all-orders_reject order'));
                })
                ->modalSubmitAction(
                    fn($action) =>
                    $action->label('Save')->color('primary') // Change button text & color
                )
                ->form([
                    Textarea::make('reason')
                        ->label(false)
                        ->validationMessages([
                            'required' => "The reason field is required.",
                        ])
                        ->rules(['required'])
                        ->columnSpanFull(),
                ])
                ->action(function ($data, $record) use ($user) {



                    DB::beginTransaction();
                    try {

                        $this->order->update(['status' => 'rejected', 'rejected_at' => now(), 'rejected_note' => $data['reason']]);
                        $this->order->orderProducts()->update([
                            'status' => 'rejected',
                        ]);
                        $returnPoints = 0;
                        $totalRefundAmt = 0;
                        if ($this->order->payment_type != 'credit_line') {
                            $returnPoints += $this->order->total_dpharma_points_used;
                            $totalRefundAmt += $this->order->total_amount;
                        }
                        if ((count($this->order->order->subOrder) == 1) && $totalRefundAmt > 0 && $this->order->order->ecommerce_tran_id && $this->order->order->payment_status == 'paid') {
                            $refundRes = (new EcommerceService())->refundPayment($this->order->order->ecommerce_tran_id);
                            Log::error(
                                'refund process response from controller',
                                [
                                    'refundRes' => $refundRes,
                                    'order_id' => $this->order->id,
                                ]
                            );
                            if (isset($refundRes['result'])) {
                                $refundResult =  $refundRes['result'];
                                $refundStatus = $refundResult['refundStatus'];


                                if ($refundStatus == 11) {

                                    Transaction::create([
                                        "transaction_id" => $refundResult['transactionNumber'],
                                        "order_id" => $this->order->order->id,
                                        "sender_id" => 1, //for admin
                                        "sub_order_id" => $this->order->id,
                                        "payment_method" => 'CREDIT',
                                        "amount" => $refundResult['amount'],
                                        "status" => 'success',
                                        "order_status" => 'cancelled',
                                        "ecommerce_status" => (int) $refundStatus,
                                        "meta_data" => json_encode($refundResult),
                                    ]);
                                }
                            }
                            // else {
                            //     $isRefund = false;
                            //     throw new \Exception('Failed to get refund process.');
                            // }
                        }

                        //Activitylog start
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn(auth()->user())
                            ->withProperties([
                                'old' => [
                                    'status' => 'pending',
                                ],
                                'attributes' => [
                                    'status' => 'rejected',
                                ],
                            ])
                            ->log("Order #{$this->order->order->order_number} status has been Rejected");
                        //Activitylog end

                        ReviewOrder::updateOrderStatus($this->order->order_id);
                        if ($this->order->total_dpharma_points_used > 0) {
                            DB::table('dpharma_points')->insert([
                                'user_id'          => $this->order->order->user_id,
                                'description'      => 'Order #' . $this->order->order_number . 'Rejected - Points Successfully Returned',
                                //'description' => 'Order <a style="color:blue;" href="' . route('filament.admin.resources.orders.view', ['record' => $this->order->id]) . '">' . $this->order->order_number . '</a> Rejected - Points Successfully Returned',
                                'points'           => $this->order->total_dpharma_points_used, // Adjust points logic as needed
                                'redeem'           => null,
                                'balance'          => DB::raw("(
                                                        COALESCE(
                                                            (SELECT balance FROM dpharma_points
                                                             WHERE user_id = {$this->order->order->user_id}
                                                             ORDER BY created_at DESC LIMIT 1),
                                                            0
                                                        ) + {$this->order->total_dpharma_points_used}
                                                    )"),
                                'created_at'       => now(),
                                'updated_at'       => now(),
                                'reference_id'     => $this->order->id,
                                'reference_value'  => 'suborder',
                            ]);
                        }

                        if ($this->order->payment_type == 'credit_line') {
                            $lastClinicCredit = ClinicCreditHistory::where('supplier_id', getUser(auth()->user())->id)->where('facility_id', (int)$this->order->order->user_id)->orderBy('id', 'desc')->first();

                            if (!empty($lastClinicCredit)) {

                                $remainingAmount = 0;
                                if ($lastClinicCredit->remaining_amount == 0) {
                                    $remainingAmount = $this->order->total_amount + $lastClinicCredit->total_credit_amount;
                                    $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                } else {
                                    $remainingAmount = $lastClinicCredit->remaining_amount + $this->order->total_amount;
                                }

                                $clinicCredit = new ClinicCreditHistory();
                                $clinicCredit->facility_id = $this->order->order->user_id;
                                $clinicCredit->supplier_id = getUser(auth()->user())->id;
                                $clinicCredit->credit_amount = $this->order->total_amount;
                                $clinicCredit->debit_amount = 0;
                                $clinicCredit->edit_credit = 0;
                                $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used - $this->order->total_amount;
                                $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount;
                                $clinicCredit->remaining_amount = $remainingAmount;
                                $clinicCredit->reference_id = $this->order->id;
                                $clinicCredit->reference_value = 'suborder';
                                $clinicCredit->action = 'Order Rejected';
                                $clinicCredit->save();
                            }
                        }
                        //Send Email to Admin And Facility
                        $order = Order::with(['orderProductsGrouped' => function ($query) {
                            $query->select('order_id', 'status', DB::raw('COUNT(*) as count'))
                                ->groupBy('order_id', 'status');
                        }, 'subOrder', 'subOrder.user', 'orderProducts', 'orderProducts.product', 'user'])->find($this->order->order->id);


                        $userMail = $this->order?->user;
                        $facilityName = $userMail->name;

                        if ($userMail && !empty($order->subOrder[0]?->user->timezone)) {
                            $timezone = $order->subOrder[0]?->user->timezone;
                            $orderDate = Carbon::parse($order->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        } else {
                            $orderDate = Carbon::parse($order->created_at)->format('d-m-Y H:i:s');
                        }
                        $paymentType = match ($this->order?->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };

                        $orderDetails = [
                            'Order Number'      => "#" . $order->order_number,
                            'Order Date'    => $orderDate ?? '',
                            'Total Items'   => $this->order->orderProducts->count(),
                            'Supplier'   => pcCompanyName($this->order->user->pcDetails) ?? 'N/A',
                            'Order Total'   => "RM " . number_format($this->order?->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];

                        // $superAdminEmails  = getAdminData();
                        // foreach ($superAdminEmails as $key => $adminData) {

                        //     Mail::to($adminData->email)->send(new RejectOrderMail($order->subOrder[0], $adminData->name, $orderDetails));
                        // }

                        $user = User::find($order->user_id);
                        Mail::to($user->email)->send(new RejectOrderMail($order->subOrder[0], $user->name, $orderDetails));
                        //sending email ends

                        DB::commit();
                        Notification::make()
                            ->title('Order Rejected')
                            ->body('The rejected reason has been saved successfully.')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {

                        DB::rollBack();
                        Notification::make()
                            ->title('Approval Failed')
                            ->body('An error occurred while approving the order. Please try again.')
                            ->danger()
                            ->send();
                    }
                })->color('danger'),

            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(function () {
                    // $type = request()->get('type');
                    $type = request()->cookie('source');
                    if ($type == 'accepted') {
                        return ShipmentResource::getUrl('index');
                    } else if ($type == 'allorder') {
                        return OrderResource::getUrl('index');
                    } else if ($type == 'payment-earning') {
                        $id = request()->cookie('payoutId');
                        return route('filament.pc.resources.payment-earning-records.view', ['record' => $id]);
                    } else if ($type == 'invoice') {
                        return InvoiceResource::getUrl('index');
                    } else if ($type == 'outstanding') {
                        return OutStandingPaymentResource::getUrl('index');
                    }
                }),
        ];
    }

    protected function loadProductsData()
    {
        $productIds = $this->order->orderProducts->pluck('product_id')->unique();

        // Fetch all product relations in one query
        $productRelations = \App\Models\ProductRelation::whereIn('product_id', $productIds)
            ->where('user_id', getUser(auth()->user())->id)
            ->get()
            ->keyBy('id');

        // $productRelationStocks = \App\Models\ProductRelationStock::whereIn('product_relation_id', $productRelations->pluck('id'))
        //     ->first();
        // ->groupBy(function ($stock) use ($productRelations) {
        //     return $productRelations->where('id', $stock->product_relation_id)->first()->product_id;
        // });
        // dd($productRelationStocks);
        // Fetch all batches in one query
        $batches = \App\Models\ProductBatch::whereIn('product_id', $productIds)
            ->where('user_id', getUser(auth()->user())->id)
            ->get()
            ->groupBy('product_id');

        // Prepare data for each product
        foreach ($productIds as $productId) {
            // $productRelation = $productRelations->get($productId);
            $productRelations = \App\Models\ProductRelation::where('product_id', $productId)
                ->where('user_id', getUser(auth()->user())->id)
                ->first();
            $productRelationStocks = \App\Models\ProductRelationStock::where('product_relation_id', $productRelations->id)
                ->first();

            // dd($productRelationStocks);

            $isBatchWiseStock = $productRelationStocks->is_batch_wise_stock ?? false;
            $productStock = $productRelationStocks->stock ?? 0;

            $this->productsData[$productId] = [
                'productRelation' => $productRelations,
                'isBatchWiseStock' => $isBatchWiseStock,
                'productStock' => $productStock,
                'batches' => $batches->get($productId, collect([])),
            ];
        }
    }
    public static function getApprovedOrderTotal($order)
    {
        try {
            $referredValue = DB::table('clinic_referred_codes')
                ->join('users', 'clinic_referred_codes.to_user_id', '=', 'users.id')
                ->where('clinic_referred_codes.to_user_id', $order->order->user_id)->where('clinic_referred_codes.status', 'pending')->select('clinic_referred_codes.order_value', 'clinic_referred_codes.points', 'clinic_referred_codes.id', 'clinic_referred_codes.to_user_id', 'users.name', 'clinic_referred_codes.from_user_id')->first();
            if ($referredValue) {
                $userId =  $order->order->user_id;
                $userTotalAmount = Order::where('user_id', $userId)
                    ->with(['subOrder' => function ($query) {
                        $query->where('status', 'accepted');
                    }])
                    ->get()
                    ->sum(function ($order) {
                        return $order->subOrder->sum('total_amount');
                    });

                $totalAmountReferred = $userTotalAmount + $order->total_amount;

                if ($totalAmountReferred >= $referredValue->order_value) {

                    DB::table('dpharma_points')->insert([
                        'user_id'          => $referredValue->from_user_id,
                        'description'      => 'Cheer up! Earn rewards by referring to the ' . $referredValue->name,
                        'points'           => $referredValue->points,
                        'redeem'           => null,
                        'balance' => DB::raw("(
                            COALESCE(
                                (SELECT balance FROM dpharma_points
                                 WHERE user_id = {$referredValue->from_user_id}
                                 ORDER BY created_at DESC LIMIT 1),
                                0
                            ) + {$referredValue->points}
                        )"),
                        'created_at'       => now(),
                        'updated_at'       => now(),
                        'reference_id'     => $referredValue->id,
                        'reference_value'  => 'clinic_referred_codes',
                    ]);
                    DB::table('clinic_referred_codes')
                        ->where('id', $referredValue->id)
                        ->update(['status' => 'approved']);
                    return 1;
                }
            }
            return 0;
        } catch (\exception $th) {
            return 0;
        }
    }
    public static function updateOrderStatus($orderId)
    {
        try {
            $statuses = DB::table('sub_orders')
                ->where('order_id', $orderId)
                ->pluck('status')
                ->toArray();


            if (empty($statuses)) {
                return;
            }
            $allStatusesUpdated = !in_array('pending', $statuses);

            if ($allStatusesUpdated) {
                $orderStatus = 'delivered'; // All statuses updated
            } elseif (count($statuses) === 1 && (in_array('rejected', $statuses) || in_array('accepted', $statuses))) {
                $orderStatus = 'in_transit'; // Only one status is accepted or rejected
            } elseif (in_array('rejected', $statuses) || in_array('accepted', $statuses)) {
                $orderStatus = 'in_transit';
            } else {
                $orderStatus = 'pending'; // Fallback status
            }
            $mainOrder = Order::with('subOrders')->where('id', $orderId)->first();
            $allDelivered = $mainOrder && $mainOrder->subOrders->isNotEmpty() &&
                $mainOrder->subOrders->every(fn($subOrder) => $subOrder->status === 'delivered');

            if ($allDelivered) {
                $orderStatus = 'delivered';
            } else {
                if (count($statuses) === 1 && in_array('rejected', $statuses)) {
                    $orderStatus = 'cancelled'; // Only one status is accepted or rejected
                } else {
                    $orderStatus = 'in_transit'; // Only one status is accepted or rejected
                }
                // $orderStatus = 'in_transit';

            }
            // $orderStatus = 'in_transit';

            DB::table('orders')
                ->where('id', $orderId)
                ->update(['status' => $orderStatus]);
        } catch (\exception $th) {
            return 0;
        }
    }
    public static function payoutOrder($order)
    {
        try {
            $pcId = $order->user_id;
            $today = Carbon::now()->startOfDay();
            $payoutType = $order->payout_type ?? null;
            $cycleType = $order->cycle_type ?? null;

            if ($cycleType === 'monthly') {
                $startDate = Carbon::now()->startOfMonth()->startOfDay();
                $endDate = Carbon::now()->endOfMonth()->endOfDay();
            } else {
                if ($today->day < 15) {
                    // 1st to 14th cycle
                    $startDate = $today->copy()->startOfMonth()->startOfDay();
                    $endDate = $today->copy()->startOfMonth()->addDays(13)->endOfDay(); // 14th day
                } else {
                    // 15th to end of month cycle
                    $startDate = $today->copy()->startOfMonth()->addDays(14)->startOfDay(); // 15th day
                    $endDate = $today->copy()->endOfMonth()->endOfDay(); // last day of month
                }
                // $startDate = Carbon::now()->startOfMonth()->startOfDay();
                // $endDate = Carbon::now()->startOfMonth()->addDays(13)->endOfDay();
            }

            $payout = DB::table('payouts')
                ->where('user_id', $pcId)
                ->whereDate('start_date', '<=', $today)
                ->whereDate('end_date', '>=', $today)
                ->first();

            if (!$payout) {
                $payoutId = DB::table('payouts')->insertGetId([
                    'user_id' => $pcId,
                    'payout_type' => $payoutType,
                    'cycle_type' => $cycleType,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'outstanding_commission_status' => 'pending',
                    'mechant_payment_type' => (auth()->user()->pcDetails->payment_method) ? auth()->user()->pcDetails->payment_method : 'manual'
                ]);
            } else {
                $payoutId = $payout->id;
            }
            if ($payoutId) {
                DB::table('payout_sub_orders')->insert([
                    'payout_id' => $payoutId,
                    'sub_order_id' => $order->id,
                    'order_id' => $order->order_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        } catch (\exception $th) {
            return 0;
        }
    }

    /**
     * Clean filename by removing quotes and special characters
     */
    private function cleanFileName($filename)
    {
        // Remove quotes (single and double)
        $filename = str_replace(['"', "'"], '', $filename);

        // Remove other potentially problematic characters
        $filename = preg_replace('/[^\w\-_\.]/', '_', $filename);

        // Remove multiple consecutive underscores
        $filename = preg_replace('/_+/', '_', $filename);

        // Remove leading/trailing underscores and dots
        $filename = trim($filename, '_.');

        // Ensure we have a valid filename
        if (empty($filename)) {
            $filename = 'invoice_' . time();
        }

        return $filename;
    }
}
