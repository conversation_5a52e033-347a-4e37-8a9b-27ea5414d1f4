<?php

namespace App\Filament\Pc\Resources\OrderResource\Pages;

use App\Filament\Pc\Resources\OrderResource;
use App\Filament\Pc\Resources\CreditLineOrderResource;
use Filament\Resources\Pages\Page;
use App\Models\SubOrder;
use App\Models\Message;
use Livewire\Component;
use App\Models\Messages;
use App\Models\SupportTicket;
use Illuminate\Contracts\View\View;
use App\Models\SupportTicketMessage;
use App\Filament\Pc\Resources\SupportTicketReceivedResource;
use App\Models\Thread;
use App\Models\ThreadMessage;
use App\Models\User;
use App\Models\Order;
use App\Notifications\NewMessageNotification;
use Filament\Actions\Action;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Str;


class ChatOrder extends Page
{
    public $order;
    public $suborder;
    public $ticket;
    public $message;
    public $file;
    public $groupedMessages;
    protected static string $resource = OrderResource::class;
    public $selectedThread = null;
    public $messageText = '';
    public $messages = [];
    public $perPage = 15;
    public $page = 1;
    public $hasMoreMessages = true;
    public $attachedFiles = [];
    protected static string $view = 'filament.pc.resources.order-resource.pages.chat-order';

    public static function canAccess(array $parameters = []): bool
    {
        $user = auth()->user();
        if (!$user) {
            return false;
        }
        if ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company')) {
            return true;
        }
        $type = request()->query('type') ?? request()->cookie('type');
        if (in_array($type, ['allorder-detail', 'allorder-list'])) {
            return $user->can('all-orders_chat');
        }
        if (in_array($type, ['credit_line_detail', 'credit_line'])) {
            return $user->can('credit-line-orders_chat');
        }
        return $user->can('all-orders_chat') || $user->can('credit-line-orders_chat');
    }

    public function mount($record)
    {
        // $this->suborder = SubOrder::find($record);
        $this->order = SubOrder::find($record);
        $this->ticket = Thread::where('order_id', $this->order->order_id)->get()->first();

        auth()->user()->unreadNotifications
            ->filter(fn($n) => isset($n->data['actions'][0]['url']) && preg_match('/\/' . preg_quote($record, '/') . '(\/|$)/', $n->data['actions'][0]['url']))
            ->each->markAsRead();
         
       $this->markMessagesAsRead();
        if (request()->has('thread_id')) {
            $threadId = request()->get('thread_id');
            $this->selectThread($threadId);
        }
    }
    public function getTitle(): string
    {
        if (!$this->selectedThread) {
            return 'Chat';
        }

        $orderNumber = $this->selectedThread->order?->order_number;

        $participantName = $this->selectedThread->sender_id == auth()->id()
            ? ($this->selectedThread->receiver->name ?? 'Unknown User')
            : ($this->selectedThread->sender->name ?? 'Unknown User');

        return 'Chat with ' . $participantName . ' | Order #' . $orderNumber;
    }

    public function markMessagesAsRead()
    {
        ThreadMessage::where('thread_id', $this->ticket->id)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(function(){
                    $type = request()->cookie('type');
                    $orderId = request()->cookie('orderId');
                    // $type = request()->get('type');
                    if($type == 'allorder-list'){
                     return OrderResource::getUrl('index');
                    }else if($type == 'allorder-detail'){
                        return route('filament.pc.resources.orders.view', ['record' => $orderId, 'type' => 'allorder']);
                    }else if($type == 'credit_line'){
                        return CreditLineOrderResource::getUrl('index');
                    }else if($type == 'credit_line_detail'){
                        return route('filament.pc.resources.credit-line-orders.view', ['record' => $orderId]);
                    }
                    return url()->previous();
                }),
        ];
    }


    public function getBreadcrumbs(): array
    {
        $orderType = request()->query('type');
        $routeName = $orderType === 'credit_line' ? 'filament.pc.resources.credit-line-orders.index' : 'filament.pc.resources.orders.index';
        $title = $orderType === 'credit_line' ? 'Credit Line Orders' : 'All Orders';

        if ($orderType === 'credit_line') {
            $detailRoute = route('filament.pc.resources.orders.view', [
                'record' => $this->order->id,
                'type' => request()->query('type') ?? 'allorder'
            ]);
        } else {
            $detailRoute =  route('filament.pc.resources.orders.view', ['record' => $this->order->id, 'type' => request()->query('type') ?? 'allorder']);
        }


        return [
            1 => 'Orders Management',
            route($routeName) => $title,
            $detailRoute => 'Order Details',
            4 => 'Chat with Facility'
        ];
    }

    public function selectThread($threadId)
    {
        $this->selectedThread = Thread::find($threadId);
        $this->page = 1;
        $this->messages = [];
        $this->loadMessages();

        ThreadMessage::where('thread_id', $threadId)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    public function loadMessages()
    {
        if (!$this->selectedThread) {
            $this->messages = [];
            $this->hasMoreMessages = false;
            return;
        }

        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = ThreadMessage::where('thread_id', $this->selectedThread->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = ThreadMessage::where('thread_id', $this->selectedThread->id)->count();

        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);
    }

    public function easyLoadAllMessages()
    {
        $this->loadMessages(true);
    }

    // Livewire event listener for loading more messages
    #[On('load-more-messages')]
    public function loadMoreMessages()
    {
        if (!$this->hasMoreMessages || !$this->selectedThread) {
            return;
        }

        $this->page++;
        $this->loadMessages();
    }

    public function sendMessage()
    {
        if (empty(trim($this->messageText)) && empty($this->attachedFiles)) {
            $this->addError('messageText', 'You cannot send an empty message. Please add some text or attach a file.');
            return;
        }

        $validationPassed = $this->validateUploads();

        if (empty($this->messageText) && !$validationPassed) {
            $this->addError('attachedFiles', 'Your message was not sent because the attached files are invalid.');
            return;
        }

        $message = ThreadMessage::create([
            'thread_id' => $this->selectedThread->id,
            'from_id' => Auth::id(),
            'message' => $this->messageText,
            'is_read' => false,
        ]);

        if (!empty($this->attachedFiles)) {
            $folderPath = config('constants.api.media.thread') . $this->ticket->id;
            $mediaData = [];

            foreach ($this->attachedFiles as $key => $file) {
                $extension = $file->getClientOriginalExtension();

                $fileName = hash('sha256', $file->getFilename() . '_' . now()) . '.' . $extension;

                getStorageDisk()->putFileAs($folderPath, $file, $fileName);
                $mediaData[] = [
                    "model_type"            => 'App\Models\ThreadMessage',
                    "model_id"              => $message->id,
                    "uuid"                  => Str::uuid()->toString(),
                    "collection_name"       => 'thread-chat-images',
                    "name"                  => $fileName,
                    "file_name"             => $fileName,
                    "mime_type"             => $file->getMimeType(),
                    "disk"                  => config('filesystems.default'),
                    "conversions_disk"      => config('filesystems.default'),
                    "size"                  => $file->getSize(),
                    "manipulations"         => json_encode([]),
                    "custom_properties"     => json_encode([]),
                    "generated_conversions" => json_encode([]),
                    "responsive_images"     => json_encode([]),
                    "order_column"          => $key + 1,
                    "created_at"            => now(),
                    "updated_at"            => now(),
                ];
            }

            if (!empty($mediaData)) {
                \App\Models\Media::insert($mediaData);
            }
        }

        // $superAdmins = \App\Models\User::whereHas('roles', function ($query) {
        //     $query->where('name', 'Super Admin');
        // })->get();

        // foreach ($superAdmins as $superAdmin) {
        //     $recipient = User::find($superAdmin->id);

        //     if ($recipient && $recipient->id !== auth()->user()->id) {
        //         $recipient->notify(new \App\Notifications\ThreadNotification($message, 'new_message', 'admin'));
        //     }           
        // }
        $this->messageText = '';
        $this->attachedFiles = [];
        $this->page = 1;
        $this->loadMessages();

        $this->dispatch('message-sent');
    }

    public function validateUploads()
    {
        if (empty($this->attachedFiles)) {
            return true;
        }

        if (count($this->attachedFiles) > 5) {
            $this->addError('attachedFiles', 'You can only upload up to 5 files at a time.');
            $this->attachedFiles = array_slice($this->attachedFiles, 0, 5);
            return false;
        }

        $hasInvalidFiles = false;
        $invalidFiles = [];

        foreach ($this->attachedFiles as $key => $file) {
            $mimeType = $file->getMimeType();
            $extension = strtolower($file->getClientOriginalExtension());
            $fileSizeKB = $file->getSize() / 1024;

            $isPdf = in_array($mimeType, ['application/pdf', 'application/x-pdf']) || $extension === 'pdf';
            $isImage = strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png', 'gif']);
            $sizeLimitKB = 2048; // 2MB

            if (!$isPdf && !$isImage) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            } elseif ($fileSizeKB > $sizeLimitKB) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            }
        }

        if ($hasInvalidFiles) {
            $errorMessage = 'Only images (JPG, PNG, GIF) and PDFs up to 2MB each are allowed.';
            if (!empty($invalidFiles)) {
                $errorMessage .= ' Invalid files: ' . implode(', ', $invalidFiles);
            }
            $this->addError('attachedFiles', $errorMessage);
            $this->attachedFiles = array_values($this->attachedFiles); // Reindex array
        } else {
            // Clear any previous errors if validation passes
            $this->resetErrorBag('attachedFiles');
        }

        return !$hasInvalidFiles;
    }


    public function updatedAttachedFiles()
    {
        $validationPassed = $this->validateUploads();

        if ($validationPassed && !empty($this->attachedFiles)) {
            $this->resetErrorBag('messageText');
        }
    }

    public function removeFile($index)
    {
        if (isset($this->attachedFiles[$index])) {
            unset($this->attachedFiles[$index]);
            $this->attachedFiles = array_values($this->attachedFiles);
        }
    }
}
