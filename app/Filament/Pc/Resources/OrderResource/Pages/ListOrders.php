<?php

namespace App\Filament\Pc\Resources\OrderResource\Pages;

use App\Filament\Exports\OrderExporter;
use App\Filament\Pc\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Actions\ExportAction;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            ExportAction::make()
                ->exporter(OrderExporter::class)
                ->color('primary')
                ->visible(function () {
                    $user = auth()->user();
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $user->hasRole('Super Admint') || $isPharmaceuticalCompany || $user->can('all-orders_export');
                })
                ->label('Export Orders')
                ->after(function () {
                    //Activtylog start
                    activity()
                        ->causedBy(auth()->user())
                        ->useLog('order_export')
                        ->performedOn(new \App\Models\Order)
                        ->withProperties([
                            'attributes' => ['exported_date' => now()->format('M d,Y')],
                        ])
                        ->log("Order details have been exported");
                    //Activtylog end
                })
        ];
    }
    public function getTitle(): string
    {
        return 'All Orders';
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Orders Management",
            // $this->getResource()::getUrl('index') => "All Orders",
            // 2 => "List",
        ];
    }
}
