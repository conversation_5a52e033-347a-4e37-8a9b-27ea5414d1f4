<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\OutStandingPaymentResource\Pages;
use App\Filament\Pc\Resources\OutStandingPaymentResource\RelationManagers;
use App\Models\ClinicDetail;
use App\Models\OutStandingPayment;
use App\Models\Payout;
use App\Models\PayoutSubOrder;
use App\Models\PcDetail;
use App\Models\SubOrder;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\Cookie;

class OutStandingPaymentResource extends Resource
{
    protected static ?string $model = PayoutSubOrder::class;

    protected static ?string $navigationGroup = 'Payment Records';
    protected static ?string $navigationLabel = 'Payments';
    protected static ?string $slug = 'outstanding-payments';
    // public static function canAccess(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('orders_view');
    // }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table->query(
            PayoutSubOrder::with([
                'Payout',
                'order.user',
                'order.clinicDetail',
                'order.subOrder.orderProducts',
                'subOrder'
            ])

                ->whereHas('payout', function ($query) use ($user) {
                    $query->where('user_id', $user);
                        // ->where('is_payout', false);
                })
                ->whereHas('subOrder', function ($query) use ($user) {
                    $query->whereNot('payment_type', 'credit_line');
                })
                ->select('payout_sub_orders.*')
                ->selectSub(function ($query) use ($user) {
                    $query->selectRaw('COALESCE(SUM(order_products.total_commission), 0)')
                        ->from('sub_orders')
                        ->join('order_products', 'order_products.sub_order_id', '=', 'sub_orders.id')
                        ->whereColumn('sub_orders.order_id', 'payout_sub_orders.order_id')
                        ->where('sub_orders.user_id', $user);
                }, 'due_amount')
        )->emptyStateHeading('No Outstanding Payment records found')
        ->defaultSort('id','desc')
        ->description("Payments: This page displays a detailed records of the payment's you are received and yet to receive from your orders, over selected timeframes")
        ->columns([
                TextColumn::make('order.order_number')
                    ->label('Order Id')
                    ->toggleable()->sortable()
                    ->searchable()
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()

                    // ->formatStateUsing(fn($record) => '#' . ($record->order->order_number ?? ''))
                    ->url(function ($record) {
                        $params = ['record' => $record->sub_order_id];

                        if ($record->subOrder->payment_type === 'credit_line') {
                            $params['type'] = 'credit_line';
                        }

                        return route('filament.pc.resources.orders.view', $params);
                    }),

                TextColumn::make('order.user.name')
                    ->label('Facility Name')
                    ->searchable()->sortable()
                    ->toggleable()
                    ->formatStateUsing(fn($record) => ucfirst($record->order?->user?->name ?? '')),

                TextColumn::make('order.created_at')
                    ->label('Order Date')
                    ->sortable()
                    ->toggleable()
                    ->formatStateUsing(function ($record) {
                        $user = getUser(auth()->user())->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return getFormatedDate($record->order?->created_at, $format);
                    }),

                TextColumn::make('sub_order_id')
                    ->label('Items')
                    ->toggleable()->sortable()
                    ->formatStateUsing(function ($record) {
                        // dd($record->order->subOrder);
                       return $record->subOrder->orderProducts()->count();
                    }),
                TextColumn::make('order_id')
                    ->label('Order Totals')
                    ->toggleable()->sortable()
                    ->formatStateUsing(function ($record) use ($user) {
                        $subOrder = $record->order?->subOrder?->firstWhere('user_id', $user);
                        return 'RM ' . number_format($subOrder->total_sub_order_value ?? 0, 2);
                    }),
                TextColumn::make('due_amount')
                    ->label('Admin Fee')
                    ->toggleable()
                    ->sortable()
                    ->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),
                TextColumn::make('Payout.payout_status')
                    ->label('Payout Status')
                    ->searchable()
                    ->badge()
                    ->toggleable()
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
                     ->color(function ($state) {
                        $st = match ($state) {
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),
            ])
            ->filters([

                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('order.user', 'name')->options(fn() => User::whereNotNull('name')->pluck('name', 'id')->toArray()),
                SelectFilter::make('payout_status')
                ->label('Payout Status')
                ->multiple()
                ->options([
                    'pending' => 'Pending',
                    'paid' => 'Paid',
                    'failed' => 'Failed',
                ])
                ->query(function ($query, $data) {
                    if($data['values']){
                        return $query->whereHas('Payout', function ($q) use ($data) {
                            $q->whereIn('payout_status', $data['values'] ?? []);
                        });
                    }

                }),
            ])
            ->actionsColumnLabel('Action')
            ->actions([
                Action::make('view')
                    ->url(function(PayoutSubOrder $record){
                        Cookie::queue('source', 'outstanding');
                        return route('filament.pc.resources.orders.view', ['record' => $record->sub_order_id]);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Order Details')
                    ->color('gray')
                    ->label(false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOutStandingPayments::route('/'),
            //'create' => Pages\CreateOutStandingPayment::route('/create'),
            //'edit' => Pages\EditOutStandingPayment::route('/{record}/edit'),
        ];
    }
}
