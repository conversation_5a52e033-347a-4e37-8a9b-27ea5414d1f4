<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\ShipmentResource\Pages;
use App\Filament\Pc\Resources\ShipmentResource\RelationManagers;
use Filament\Tables;
use App\Models\Order;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\View;
use App\Filament\Exports\OrderExporter;
use App\Models\ClinicDetail;
use App\Models\OrderProduct;
use App\Models\PcDetail;
use App\Models\SubOrder;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Card;
use Filament\Infolists\Components\Grid;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Infolists\Components\Group;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\ActionGroup;
use Illuminate\Support\Facades\Cookie;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Filament\Infolists\Components\Actions\Action as InfoAction;
use Filament\Navigation\NavigationItem;
use Filament\Forms\Components\DatePicker;

class ShipmentResource extends Resource
{
    protected static ?string $model = SubOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';


    public static function canAccess(array $parameters = []): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company')  || auth()->user()?->can('accept-orders_view');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table
            ->query(
                SubOrder::with(['order', 'user', 'orderProducts', 'orderProducts.product'])
                    ->where('user_id', $user)
                    ->whereNotIn('status', ['cancelled', 'rejected', 'ready_for_pickup', 'pending', 'delivered', 'in_transit'])
                    ->withCount([
                        'orderProducts as order_products_count' => function (Builder $query) {
                            $query->select(DB::raw('COALESCE(COUNT(*), 0)'));
                        },
                    ]),
            )
            ->emptyStateHeading('No Orders records found')
            ->recordUrl(function ($record) {
                $params = ['record' => $record->id];

                if ($record->payment_type === 'shipment') {
                    $params['type'] = 'shipment';
                }

                return route('filament.pc.resources.orders.view', $params);
            })

            ->columns([
                TextColumn::make('order.order_number')
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                    TextColumn::make('order.user.clinicData.clinic_number')
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.facilities.show', ['user_id' => $record->order->user_id ?? null]))
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Facility ID')->sortable()->toggleable()->searchable(),

                    TextColumn::make('order.user.clinicData.clinic_name')->label('Facility Name')->sortable()->searchable()->toggleable(),
                TextColumn::make('order.created_at')->label('Order Date')
                    ->formatStateUsing(function ($state) {
                        $user = Filament::auth()->user()->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    })
                    ->sortable()->toggleable(),
                TextColumn::make('order_products_count')->label('Items')->sortable()->toggleable(),
                TextColumn::make('total_sub_order_value')
                    ->label('Order Total')
                    ->prefix('RM ')
                    ->formatStateUsing(function ($state) {
                        return number_format($state, 2);
                    })
                    ->sortable()
                    ->toggleable(),
                // TextColumn::make('status')
                //     ->searchable()
                //     ->label('Order Status')
                //     ->toggleable()
                //     ->formatStateUsing(function ($state, $record) {
                //         return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                //     })
                //     ->icon(function ($state, $record) {
                //         $status = $record ? strtolower($record->status) : 'unknown';

                //         return match ($status) {
                //             'pending' => 'bi-clock-fill',
                //             'rejected' => 'bi-x-circle-fill',
                //             'accepted' => 'bi-patch-check-fill',
                //             'cancelled' => 'bi-patch-check-fill',
                //             'delivered' => 'bi-patch-check-fill',
                //             default => 'heroicon-o-question-mark-circle',
                //         };
                //     })
                //     ->color(function ($state, $record) {
                //         $status = $record ? strtolower($record->status) : 'unknown';

                //         $color = config("constants.order_status.color.{$status}", '#424242');

                //         return $color;
                //     })
                //     ->extraAttributes(function ($state, $record) {
                //         $status = $record ? strtolower($record->status) : 'unknown';

                //         $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                //         $color = config("constants.order_status.color.{$status}", '#424242');
                //         $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                //         return [
                //             'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                //         ];
                //     }),
            ])
            ->defaultSort('id', 'desc')
            ->actionsColumnLabel('Action')
            ->filters([
                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('order.user.clinicData', 'clinic_name')->options(fn() => ClinicDetail::whereNotNull('clinic_name')->pluck('clinic_name', 'id')->toArray()),
                // SelectFilter::make('status')
                //     ->label('Order Status')
                //     ->multiple()
                //     ->options([
                //         'accepted' => 'Accepted'
                //     ]),
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('from_date')
                            ->label('From')
                            ->maxDate(fn($get) => $get('to_date') ?? now())
                            ->closeOnDateSelection()
                            ->placeholder('Select from date'),
                        DatePicker::make('to_date')
                            ->label('To')
                            ->minDate(fn($get) => $get('from_date'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->placeholder('Select to date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'] ?? null,
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date)
                            )
                            ->when(
                                $data['to_date'] ?? null,
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date)
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from_date'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from_date'])->toFormattedDateString();
                        }
                        if ($data['to_date'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['to_date'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ])

            ->actions([
                Action::make('view')
                    ->url(function (SubOrder $record) {
                        Cookie::queue('source', 'accepted');
                        return route('filament.pc.resources.orders.view', ['record' => $record->id, 'type' => 'accepted']);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit;'])
                    ->visible(fn(SubOrder $record): bool => $record->payment_type != 'credit_line' && auth()->user()->can('accept-orders_view'))
                    // ->visible(function ($record) {
                    //     if ($record->id == 52) {
                    //         dd($record->payment_type != 'credit_line', auth()->user()->can('accept-orders_view'));
                    //     }
                    // })
                    ->tooltip('Order Details')
                    ->color('gray')
                    ->label(false),
                Action::make('credit_line')
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit;'])
                    ->label(false)
                    ->color('gray')
                    ->tooltip('View Credit Line Order')
                    ->visible(fn(SubOrder $record): bool => $record->payment_type === 'credit_line')
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->id, 'type' => 'accepted'])),

                Action::make('inTransit')
                    ->tooltip('In-Transit')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-truck')->size('sm')->iconButton()
                    ->label(false)
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'owned' &&
                            $record->status === 'accepted'
                        );
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('accept-orders_in transit'));
                    })
                    ->action(function ($record) {
                        $record->status = 'in_transit';
                        $record->in_transit_at = now();
                        $record->save();

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'in_transit',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status updated to in transit");
                        $paymentType = match ($record->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };
                        if (!empty($record->order?->user->timezone)) {
                            $timezone = $record->order?->user->timezone;
                            $orderDate = Carbon::parse($record->order->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        } else {
                            $orderDate = Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s');
                        }

                        $orderDetails = [
                            'Order Number'      => "#" . $record->order->order_number,
                            'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                            'Total Items'   => $record->orderProducts->count(),
                            'Supplier'   => $record->user?->name ?? 'N/A',
                            'Order Total'   => "RM " . number_format($record->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];
                        $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';

                        foreach ($orderDetails as $key => $value) {
                            $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                        }
                        $orderDetailsHtml .= '</table>';
                        $emailContentData = [
                            "USER" => $record->order->user->name,
                            "ORDERNUMBER" => $record->order->order_number,
                            "ORDERDETAIL" => $orderDetailsHtml
                        ];
                        sendMailNotification($record->order->user, "PC_ORDER_INTRANSIT", $emailContentData);
                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    })
                    ->color('gray'),
                Action::make('readyForPickup')
                    ->tooltip('Ready For Pickup')->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->icon('heroicon-o-archive-box')->size('sm')->iconButton()
                    ->label(false)
                    ->color('gray')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm')
                    ->modalDescription('Are you sure you want to update these status? This action cannot be undone.')
                    ->modalSubmitActionLabel('Confirm')
                    ->visible(function ($record) {
                        $user = auth()->user();
                        $conditionWareHouse = (
                            $record->warehouse_type === 'dpharma' &&
                            $record->status === 'accepted'
                        );
                        return $conditionWareHouse && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('accept-orders_ready for pickup'));
                    })
                    ->action(function ($record) {
                        $record->status = 'ready_for_pickup';
                        $record->ready_for_pickup_at = now();
                        $record->save();

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('order_status_update')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => [
                                    'status' => 'accepted',
                                ],
                                'attributes' => [
                                    'status' => 'ready_for_pickup',
                                ],
                            ])
                            ->log("Order #{$record->order->order_number} status updated to ready for pickup");

                        $paymentType = match ($record->payment_type) {
                            'pay_now'    => 'Pay Now',
                            'pay_later'  => 'Pay Later',
                            'credit_line' => 'Credit Line',
                            default      => 'Unknown',
                        };
                        $timezone = $record->order?->user->timezone;
                        $orderDate = Carbon::parse($record->order?->user->created_at)->timezone($timezone)->format('d M, Y h:i A');
                        $orderDetails = [
                            'Order Number'      => "#" . $record->order->order_number,
                            'Order Date'    => $orderDate ?? Carbon::parse($record->order->created_at)->format('d-m-Y H:i:s'),
                            'Total Items'   => $record->orderProducts->count(),
                            'Supplier'   => $record->user?->name ?? 'N/A',
                            'Order Total'   => "RM " . number_format($record->total_amount, 2),
                            'Payment Type'  => $paymentType ?? 'N/A',
                        ];
                        $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';

                        foreach ($orderDetails as $key => $value) {
                            $orderDetailsHtml .= "<tr><th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th><td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td></tr>";
                        }
                        $orderDetailsHtml .= '</table>';
                        $emailContentData = [
                            "USER" => $record->order->user->name,
                            "ORDERNUMBER" => $record->order->order_number,
                            "ORDERDETAIL" => $orderDetailsHtml
                        ];
                        sendMailNotification($record->order->user, "PC_ORDER_READY_FOR_PICKUP", $emailContentData);
                        Notification::make()
                            ->body('Order status updated successfully.')
                            ->success()
                            ->send();

                        return null;
                    }),

            ])
            ->bulkActions([Tables\Actions\BulkActionGroup::make([])]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipments::route('/'),
            'create' => Pages\CreateShipment::route('/create'),
            'edit' => Pages\EditShipment::route('/{record}/edit'),
        ];
    }
}
