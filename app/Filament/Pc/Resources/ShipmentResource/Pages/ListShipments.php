<?php

namespace App\Filament\Pc\Resources\ShipmentResource\Pages;

use App\Filament\Pc\Resources\ShipmentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListShipments extends ListRecords
{
    protected static string $resource = ShipmentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //  Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [];
    }
    public function getTitle(): string
    {
        return "Accepted Orders";
    }
}
