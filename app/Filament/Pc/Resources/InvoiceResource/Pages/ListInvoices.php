<?php

namespace App\Filament\Pc\Resources\InvoiceResource\Pages;

use App\Filament\Pc\Resources\InvoiceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInvoices extends ListRecords
{
    protected static string $resource = InvoiceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getTitle(): string
    {
        return 'Invoices';
    }
    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Invoice Management",
            // 2 => "List",
        ];
    }
}
