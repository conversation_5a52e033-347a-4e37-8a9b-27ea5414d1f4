<?php

namespace App\Filament\Pc\Resources\ClinicDetailResource\Widgets;

use App\Models\ClinicCredit;
use App\Models\ClinicDetail;
use App\Models\Order;
use App\Models\PayoutSubOrder;
use App\Models\PcDetail;
use App\Models\SubOrder;
use Filament\Facades\Filament;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;

class CreditlineHistoryTable extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';
    public $record = null;
    protected static ?string $heading = 'Credit Used History';

    public function table(Table $table): Table
    {
        $facilityId = $this->record->user_id ?? null;
        $user = getUser(auth()->user());
        $supplierId = $user->id;

        return $table
            ->query(
                SubOrder::query()
                    ->join('orders', 'sub_orders.order_id', '=', 'orders.id')
                    ->where('sub_orders.payment_type', 'credit_line')
                    ->where('orders.user_id', $facilityId)
                    ->where('sub_orders.user_id', $supplierId)
                    ->with(['order'])
                    ->select('sub_orders.*')
            )
            ->recordUrl(function (SubOrder $record): ?string {
                return route('filament.pc.resources.orders.view', ['record' => $record->id]);
            })
            ->defaultSort('id', 'desc')
            ->emptyStateHeading('No credit usage records found')
            ->columns([
                TextColumn::make('order.order_number')
                    ->label('Order ID')
                    ->url(function (SubOrder $record): ?string {
                        return route('filament.pc.resources.orders.view', ['record' => $record->id]);
                    })
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()

                    ->toggleable()
                    ->searchable()
                    ->sortable(),
                // ->formatStateUsing(fn(?string $state): string => !empty($state) ? '#' . $state : '-'),

                TextColumn::make('order.created_at')
                    ->label('Order Date')
                    ->toggleable()
                    ->sortable()
                    ->searchable()
                    ->dateTime('M d, Y'),
                // ->formatStateUsing(function ($state) use ($supplierId) {
                //     $format = PcDetail::where('user_id', $supplierId)->value('date_format') ?? 'M d, Y';
                //     return getFormatedDate($state, $format);
                // }),

                TextColumn::make('total_amount') // Direct access since we're using SubOrder model
                    ->label('Credit Used')
                    ->sortable()
                    ->searchable()
                    ->toggleable()
                    ->prefix('RM ')
                    ->formatStateUsing(fn($state) => number_format($state, 2)),
            ])
            ->defaultSort('order.created_at', 'desc')
            ->actionsColumnLabel('Action')
            ->filters([
                Filter::make('total_sub_order_value')
                    ->label('Credit Used')
                    ->form([
                        TextInput::make('min_credit')
                            ->label('Credit Used')
                            ->numeric()
                            ->inputMode('decimal'),
                    ])
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['min_credit']) {
                            return null;
                        }
                        return $data['min_credit'];
                    })
                    ->query(function (Builder $query, array $data) {
                        return $query->when(
                            filled($data['min_credit'] ?? null),
                            fn($query) => $query->where('total_sub_order_value', '=', $data['min_credit'])
                        );
                    }),
                Filter::make('created_at')
                    ->form([
                        DatePicker::make('from')
                            ->label('From Date')
                            // ->visible(fn($get) => $get('range') === 'custom')
                            ->maxDate(fn($get) => $get('until') ?? now())
                            ->closeOnDateSelection(),

                        DatePicker::make('until')
                            ->label('To Date')
                            // ->visible(fn($get) => $get('range') === 'custom')
                            ->minDate(fn($get) => $get('from'))
                            ->maxDate(now())
                            ->closeOnDateSelection(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '>=', $date))
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereHas('order', fn(Builder $q) => $q->whereDate('created_at', '<=', $date))
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from'])->toFormattedDateString();
                        }
                        if ($data['until'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['until'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn(SubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->id]))
                    ->icon('heroicon-o-eye')
                    ->size('sm')
                    ->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Order Details')
                    ->label(false),
            ]);
    }
}
