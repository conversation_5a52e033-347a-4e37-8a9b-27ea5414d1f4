<?php

namespace App\Filament\Pc\Resources\ClinicDetailResource\Pages;

use App\Filament\Admin\Resources\ClinicResource;
use App\Filament\Pc\Resources\ClinicDetailResource;
use App\Models\Approval;
use App\Models\ClinicAccountType;
use App\Models\ClinicDetail;
use App\Models\DpharmaPoint;
use App\Models\PcDetail;
use App\Models\User;
use App\Models\UserAddress;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Notifications\Notification;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Components\ViewEntry;
use Illuminate\Validation\Rule;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;
use Filament\Facades\Filament;

class ShowClinicDetail extends Page implements HasForms
{
    protected static string $resource = ClinicDetailResource::class;
    protected static string $view = 'filament.pc.resources.clinic-detail-resource.pages.show-clinic-detail';
    protected static ?string $model = ClinicDetail::class;

    public  $record;

    public function mount($user_id): void
    {
        $this->record = ClinicDetail::where('user_id', $user_id)->with(['user', 'shippingAddress', 'userAddress'])->firstOrFail();
        // dd($this->record);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->record)
            ->schema([
                // Facility Details Section
                Section::make('Facility Details')
                    ->headerActions([])
                    ->schema([
                        Grid::make(5)
                            ->schema([
                                ImageEntry::make('company_image')
                                    ->columnSpan(1)
                                    ->label('')
                                    ->default(asset('/images/user-avatar.png'))
                                    ->circular(),

                                \Filament\Infolists\Components\Grid::make([
                                    'default' => 4,
                                    'sm' => 4,
                                ])
                                    ->columnSpan(4)
                                    ->schema([
                                        TextEntry::make('clinic_name')
                                            ->label('Facility Name'),
                                        TextEntry::make('clinic_owner')
                                            ->label('Owner'),
                                        TextEntry::make('user.email')
                                            ->label('Email')
                                            ->icon('heroicon-m-envelope'),
                                        TextEntry::make('mobile_number')
                                            ->label('Phone Number')
                                            ->prefix('+60 ')
                                            ->formatStateUsing(fn($state): string => substr($state, 0, 2) . '-' . substr($state, 2))
                                            ->icon('heroicon-m-phone'),
                                        TextEntry::make('created_at')
                                            ->label('Created On')
                                            ->formatStateUsing(function ($state) {
                                                $user = Filament::auth()->user()->id;
                                                $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                                                $time = PcDetail::where('user_id', $user)->value('time_format') ?? 'H:i';
                                                if ($time == '12-hour') {
                                                    $time = 'H:i A';
                                                } elseif ($time == '24-hour') {
                                                    $time = 'h:i';
                                                }
                                                return getFormatedDate($state, $format . ' | ' . $time);
                                            }),
                                        // TextEntry::make('status')
                                        //     ->label('Status')
                                        //     ->badge()
                                        //     ->formatStateUsing(fn (bool $state): string =>  $state ? 'Active' : 'Inactive')
                                        //     ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                                        TextEntry::make('user.verification_status')
                                            ->label('Status')
                                            ->badge()
                                            ->icon(fn($state): string => match ($state) {
                                                'approved' => 'heroicon-s-check-circle',
                                                'rejected' => 'heroicon-s-x-circle',
                                                'pending' => 'heroicon-s-clock',
                                                default => 'heroicon-s-check-circle',
                                            })
                                            ->formatStateUsing(fn($state): string => $state == 'approved' ? 'Accepted' : ucfirst($state))
                                            ->color(function ($state) {
                                                $st = match ($state) {
                                                    'pending' => 'warning',
                                                    'approved' => 'success',
                                                    'rejected' => 'danger',
                                                    default => 'warning',
                                                };
                                                return $st;
                                            }),
                                        TextEntry::make('tier')
                                            ->label('Tier')
                                            ->badge()
                                            ->formatStateUsing(fn(string $state): string =>  !empty($state) ? ucfirst($state) : '-')
                                            ->color(fn(string $state): string => match ($state) {
                                                'gold' => 'warning',
                                                'silver' => 'primary',
                                                'bronze' => 'danger',
                                                default => 'default',
                                            })
                                            ->icon(fn(string $state): string => match ($state) {
                                                'gold' => 'heroicon-s-currency-dollar',
                                                'silver' => 'heroicon-s-currency-dollar',
                                                'bronze' => 'heroicon-s-currency-dollar',
                                                default => 'heroicon-s-currency-dollar',
                                            }),

                                    ])
                            ])
                    ]),

                // Tabs Section
                Tabs::make()
                    ->tabs([
                        Tab::make('General Details')
                            ->schema([
                                Section::make('Basic Details')
                                    ->schema([
                                        \Filament\Infolists\Components\Grid::make([
                                            'default' => 4,
                                            'sm' => 4,
                                        ])
                                            ->columnSpan('full')
                                            ->extraAttributes(['class' => 'gap-0'])
                                            ->schema([
                                                TextEntry::make('clinic_number')->label('Facility Registration Number'),
                                                TextEntry::make('mobile_number')
                                                    ->prefix('+60 ')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 2) . '-' . substr($state, 2))
                                                    ->label('Facility Phone Number'),
                                                TextEntry::make('landline_number')
                                                    ->prefix('+03 ')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 1) . '-' . substr($state, 1))
                                                    ->label('Facility Landline Number'),
                                                TextEntry::make('company_name')
                                                    ->label('Company Name'),
                                                TextEntry::make('company_number')
                                                    ->label('Company Registration Number'),
                                                TextEntry::make('tin_number')
                                                    ->label('TIN Number'),
                                                TextEntry::make('clinic_owner')
                                                    ->label('Facility Owner'),
                                                TextEntry::make('clinic_year')
                                                    ->label('Commencement Year'),
                                                TextEntry::make('clinicAccountType.name')
                                                    ->label('Type of Facility'),
                                                TextEntry::make('sst_number')
                                                    ->label('SST Registration Number'),
                                            ])
                                    ])->columns(2),

                                Section::make('Billing Address')
                                    ->schema([
                                        \Filament\Infolists\Components\Grid::make([
                                            'default' => 4,
                                            'sm' => 4,
                                        ])
                                            ->columnSpan('full')
                                            ->extraAttributes(['class' => 'gap-0'])
                                            ->schema([
                                                TextEntry::make('billingAddress.address_1')
                                                    ->label('Street 1'),
                                                TextEntry::make('billingAddress.address_2')
                                                    ->label('Street 2'),
                                                TextEntry::make('billingAddress.city.name')
                                                    ->label('City'),
                                                TextEntry::make('billingAddress.state.name')
                                                    ->label('State'),
                                                TextEntry::make('billingAddress.country.name')
                                                    ->label('Country'),
                                                TextEntry::make('billingAddress.postal_code')
                                                    ->label('Postal Code'),
                                            ])
                                    ])->columns(2),
                                Section::make('Shipping Address')
                                    ->schema([
                                        RepeatableEntry::make('shippingAddresses')
                                            ->label('')
                                            ->schema([
                                                Grid::make(4) // Using 6 columns for all fields
                                                    ->schema([
                                                        TextEntry::make('address_1')
                                                            ->label('Street 1')
                                                            ->columnSpan(1),
                                                        TextEntry::make('address_2')
                                                            ->label('Street 2')
                                                            ->columnSpan(1),
                                                        TextEntry::make('city.name')
                                                            ->label('City')
                                                            ->columnSpan(1),
                                                        TextEntry::make('state.name')
                                                            ->label('State')
                                                            ->columnSpan(1),
                                                        TextEntry::make('country.name')
                                                            ->label('Country')
                                                            ->columnSpan(1),
                                                        TextEntry::make('postal_code')
                                                            ->label('Postal Code')
                                                            ->columnSpan(1),
                                                    ])
                                            ])
                                    ]),
                                Section::make('Doctor In Charge')
                                    ->schema([
                                        \Filament\Infolists\Components\Grid::make([
                                            'default' => 4,
                                            'sm' => 4,
                                        ])
                                            ->columnSpan('full')
                                            ->extraAttributes(['class' => 'gap-0'])
                                            ->schema([
                                                TextEntry::make('dc_name')
                                                    ->label('Full Name'),
                                                TextEntry::make('dc_nric')
                                                    ->label('NRIC'),
                                                TextEntry::make('dc_mmc_number')
                                                    ->label('MMC Registration No'),
                                                TextEntry::make('dc_apc_number')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 4) . '/' . substr($state, 4))
                                                    ->label('Current APC No'),
                                                TextEntry::make('dc_phone_number')
                                                    ->prefix('+60 ')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 2) . '-' . substr($state, 2))
                                                    ->label('Phone Number'),
                                                TextEntry::make('dc_landline_number')
                                                    ->prefix('+03 ')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 1) . '-' . substr($state, 1))
                                                    ->label('Landline Number'),
                                                TextEntry::make('dc_signature')
                                                    ->label('Signature')
                                                    ->formatStateUsing(function ($state, $record) {
                                                        return view('filament.pc.resources.clinic-detail-resource.pages.view-signature', [
                                                            //    'signatureData' => asset('storage/images/clinic/'.$record->id.'/'.$record->dc_signature),

                                                            'signatureData' => getImage($record->dc_signature, '/images/clinic/' . $record->id),

                                                            'altText' => 'Doctor Signature'
                                                        ]);
                                                    })
                                                    ->html(),
                                            ])
                                    ])->columns(2),
                                Section::make('Admin In Charge')
                                    ->schema([
                                        \Filament\Infolists\Components\Grid::make([
                                            'default' => 4,
                                            'sm' => 4,
                                        ])
                                            ->columnSpan('full')
                                            ->extraAttributes(['class' => 'gap-0'])
                                            ->schema([
                                                TextEntry::make('ac_name')
                                                    ->label('Full Name'),
                                                TextEntry::make('ac_nric')
                                                    ->label('NRIC'),
                                                TextEntry::make('ac_phone_number')
                                                    ->label('NRIC')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 2) . '-' . substr($state, 2))
                                                    ->prefix('+60 ')->label('Phone Number'),
                                                TextEntry::make('ac_landline_number')
                                                    ->prefix('+03 ')
                                                    ->icon('heroicon-m-phone')
                                                    ->formatStateUsing(fn($state): string => substr($state, 0, 1) . '-' . substr($state, 1))
                                                    ->label('Landline Number'),

                                            ])
                                    ])->columns(2),
                                Section::make('Uploaded Documents')
                                    ->schema([
                                        \Filament\Infolists\Components\Grid::make([
                                            'default' => 4,
                                            'sm' => 4,
                                        ])
                                            ->columnSpan('full')
                                            ->extraAttributes(['class' => 'gap-0'])
                                            ->schema(function ($record) {
                                                // dd($record);
                                                return [
                                                    ViewEntry::make('clinicData.borang_certificate')
                                                        ->view('filament.pc.resources.clinic-detail-resource.pages.clinic-pdf-image')
                                                        ->viewData([
                                                            'filePaths' => $record->borangCertificates->pluck('name')->all(),
                                                            'name' => 'borang_certificate',
                                                            'record' => $record,
                                                        ])
                                                        ->label('Upload Certification of Registration(Borang B Borang F)'),

                                                    ViewEntry::make('clinicData.mmc_certificate')
                                                        ->view('filament.pc.resources.clinic-detail-resource.pages.clinic-pdf-image')
                                                        ->viewData([
                                                            'filePaths' => $record->mmcCertificates->pluck('name')->all(),
                                                            'name' => 'mmc_certificate',
                                                            'record' => $record,
                                                        ])
                                                        ->label('MMC Full Registration Certificate for a Person In-Charge'),

                                                    ViewEntry::make('clinicData.apc_certificate')
                                                        ->view('filament.pc.resources.clinic-detail-resource.pages.clinic-pdf-image')
                                                        ->viewData([
                                                            'filePaths' => $record->apcCertificates->pluck('name')->all(),
                                                            'name' => 'apc_certificate',
                                                            'record' => $record,
                                                        ])
                                                        ->label('Current Annual Practicing Certificate (APC)'),
                                                    ViewEntry::make('clinicData.arc_certificate')
                                                        ->view('filament.pc.resources.clinic-detail-resource.pages.clinic-pdf-image')
                                                        ->viewData([
                                                            'filePaths' => $record->arcCertificates->pluck('name')->all(),
                                                            'name' => 'arc_certificate',
                                                            'record' => $record,
                                                        ])
                                                        ->label('Current Annual Practicing Certificate (APC)'),
                                                    ViewEntry::make('clinicData.poison_license')
                                                        ->view('filament.pc.resources.clinic-detail-resource.pages.clinic-pdf-image')
                                                        ->viewData([
                                                            'filePaths' => $record->licenseCertificates->pluck('name')->all(),
                                                            'name' => 'poison_license',
                                                            'record' => $record,
                                                        ])
                                                        ->label('Current Annual Practicing Certificate (APC)'),
                                                ];
                                            })
                                    ])->columns(2),
                            ]),
                    ])
                    ->columnSpanFull()
            ]);
    }


    public function getTitle(): string|Htmlable
    {
        return $this->record->clinic_name;
    }

    public function getBreadcrumbs(): array
    {
        return [
            ClinicDetailResource::getUrl() => 'Facilities',
            route('filament.pc.resources.facilities.view', ['record' => $this->record->user_id]) => 'Facility Details',
            $this->getTitle(),

        ];
    }
}
