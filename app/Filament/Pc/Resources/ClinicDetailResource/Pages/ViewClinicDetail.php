<?php

namespace App\Filament\Pc\Resources\ClinicDetailResource\Pages;

use App\Filament\Pc\Resources\ClinicDetailResource;
use App\Filament\Pc\Resources\ClinicDetailResource\Widgets\CreditlineHistoryTable;
use App\Models\ClinicCredit;
use App\Models\ClinicCreditHistory;
use App\Models\ClinicDetail;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Group as InfoGroup;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use Filament\Forms\Get;
use App\Models\ClinicPharmaSupplier;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Infolists\Components\HtmlEntry;

use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;
use Livewire\Component;

class ViewClinicDetail extends Page
{
    protected static string $resource = ClinicDetailResource::class;

    protected static string $view = 'filament.pc.resources.clinic-detail-resource.pages.view-clinic-detail';

    public $data;

    public $creditAmount;
    public $remainingBalance;
    public $unpaidCreditUsed;
    public int $id;
    public function mount($record)
    {
        $this->id = $record;
        $user = getUser(auth()->user());
        $this->data = ClinicDetail::where('user_id', (int) $record)->with('clinicAccountType', 'businessName')->first();
        $credit = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id',  (int) $record)->orderBy('id', 'desc')->first();
        $this->creditAmount = $credit->total_credit_amount ?? 0;
        $this->remainingBalance =  $credit->remaining_amount ?? 0;
        $this->unpaidCreditUsed = $credit->order_credit_used ?? 0;
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicDetailResource::getUrl('index')),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->data)
            ->schema([
                // Facility Profile Section
                InfoSection::make('Facility Details')
                    ->columnSpan('full')
                    ->extraAttributes(['class' => 'gap-0'])
                    ->schema([
                        TextEntry::make('id')
                            // ->prefix('#')
                            ->label('Facility Id'),
                        TextEntry::make('clinic_name')
                            ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">' . ucfirst($state) . '</span>' : '<span style="color: blue;">-</span>')->html()

                            ->url(route('filament.pc.resources.facilities.show', ['user_id' => $this->data->user_id]))
                            ->label('Facility Name'),
                        TextEntry::make('clinicAccountType.name')
                            ->label('Facility Type'),
                        TextEntry::make('admin_verified_on')
                            ->label('Approved On')
                            ->getStateUsing(
                                fn(ClinicDetail $record): ?string => $record->user?->admin_verified_on
                            )
                            ->formatStateUsing(function ($state): string {
                                if (empty($state)) {
                                    return '-';
                                }

                                $userTimezone = auth()->user()->timezone;
                                $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                                return $convertedDate->format('M d, Y | H:i');
                            }),

                    ])->columns(4),

                // Credit Info Section
                InfoSection::make('Credit Info')
                    ->columnSpan('full')
                    ->extraAttributes(['class' => 'gap-0 mt-6']) // Add spacing between sections
                    ->headerActions([
                        //                 \Filament\Infolists\Components\Actions\Action::make('credit-line-add')
                        //                     ->icon('iconoir-hand-card')->size('sm')
                        //                     ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                        //                     ->visible(function ($record) {
                        //                         $user = getUser(auth()->user());
                        //                         $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();

                        //                         $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($credit_amount);
                        //                         $user = auth()->user();
                        //                         return $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_assign credit'));
                        //                     })
                        //                     ->label(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                        //                     ->iconButton()
                        //                     ->form([
                        //                         TextInput::make('assigned_credit_line_amount')
                        //                             ->rules(['required', 'numeric'])
                        //                             ->placeholder('Enter credit amount')
                        //                             ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                        //                             ->minValue(0)
                        //                             ->maxValue(1000000)
                        //                             ->validationMessages([
                        //                                 'required' => 'The Credit Amount is required.',
                        //                                 'max' => 'The Credit Amount must not exceed 1,000,000.',
                        //                             ])
                        //                             ->extraAttributes([
                        //                                 'inputmode' => 'numeric',
                        //                                 'pattern' => '[0-9]*',
                        //                                 'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                        //                                 'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                        //                             ])
                        //                             ->formatStateUsing(function ($record) {
                        //                                 $user = getUser(auth()->user());
                        //                                 $credit_amount = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id', $record->user_id)->orderBy('id', 'desc')->pluck('credit_amount')->first();
                        //                                 return null;
                        //                             }),
                        //                     ])->modalSubmitActionLabel('Save')
                        //                     // ->action(function ($record, Get $get, array $data) {
                        //                     //     $record->update(['assigned_credit_line_amount' => $data['assigned_credit_line_amount'], 'assigned_credit_terms' => $data['assigned_credit_terms']]);
                        //                     // }),
                        //                     ->action(function ($record, Get $get, array $data) {
                        //                         // Create a new credit line
                        //                         $user = getUser(auth()->user());
                        //                         $lastClinicCredit = ClinicCreditHistory::where([
                        //                             'facility_id' => $record->user_id,
                        //                             'supplier_id' => $user->id,
                        //                         ])->orderBy('id', 'desc')->first();
                        //                         if(empty($lastClinicCredit)){
                        //                             $clinicCredit = new ClinicCreditHistory();
                        //                             $clinicCredit->facility_id = $record->user_id;
                        //                             $clinicCredit->supplier_id = $user->id;
                        //                             $clinicCredit->credit_amount = $data['assigned_credit_line_amount'];
                        //                             $clinicCredit->debit_amount = 0;
                        //                             $clinicCredit->edit_credit = 0;
                        //                             $clinicCredit->order_credit_used = 0;
                        //                             $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                        //                             $clinicCredit->remaining_amount = $data['assigned_credit_line_amount'];
                        //                             $clinicCredit->reference_id = $user->id;
                        //                             $clinicCredit->reference_value = 'user';
                        //                             $clinicCredit->action = 'PS Assign Credit';
                        //                             $clinicCredit->save();

                        //                         }else{

                        //                             $remainingAmount = 0;
                        //                             // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                        //                             if($lastClinicCredit->remaining_amount == 0){
                        //                                 $remainingAmount = $data['assigned_credit_line_amount']+$lastClinicCredit->total_credit_amount;
                        //                                  $remainingAmount = $remainingAmount-$lastClinicCredit->order_credit_used;
                        //                             }else{
                        //                                 if($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used){
                        //                                     $remainingAmount = 0;
                        //                                 }else{
                        //                                     $remainingAmount = $data['assigned_credit_line_amount']+$lastClinicCredit->remaining_amount;
                        //                                 }
                        //                             }

                        // // dd($remainingAmount);
                        //                             $clinicCredit = new ClinicCreditHistory();
                        //                             $clinicCredit->facility_id = $record->user_id;
                        //                             $clinicCredit->supplier_id = $user->id;
                        //                             $clinicCredit->credit_amount = $data['assigned_credit_line_amount'];
                        //                             $clinicCredit->debit_amount = 0;
                        //                             $clinicCredit->edit_credit = 0;
                        //                             $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used;
                        //                             $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount+$data['assigned_credit_line_amount'];
                        //                             $clinicCredit->remaining_amount = $remainingAmount;
                        //                             $clinicCredit->reference_id = $user->id;
                        //                             $clinicCredit->reference_value = 'user';
                        //                             $clinicCredit->action = 'PS Assign Credit';
                        //                             $clinicCredit->save();
                        //                         }

                        //                         // activity()
                        //                         //     ->causedBy(auth()->user())
                        //                         //     ->useLog('credit_line_add')
                        //                         //     ->performedOn($clinicCredit)
                        //                         //     ->withProperties([
                        //                         //         'old' => $oldValues,
                        //                         //         'attributes' => $newValues,
                        //                         //     ])
                        //                         //     ->log(($record->clinic_name ?? 'N/A') . " credit has been assigned");
                        //                         Notification::make()->title("Facility credit added successfully")->success()->send();
                        //                         return redirect(request()->header('Referer'));
                        //                         // // Optionally update the ClinicDetail record with the assigned credit amount
                        //                         // $record->update([
                        //                         //     'assigned_credit_line_amount' => $data['assigned_credit_line_amount'],
                        //                         //     // 'assigned_credit_terms' => $data['assigned_credit_terms'],
                        //                         // ]);
                        //                     }),

                        \Filament\Infolists\Components\Actions\Action::make('credit-line-add')
                            // ->icon('iconoir-hand-card')
                            // ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                            ->icon('iconoir-hand-card')->size('sm')
                            ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                            ->visible(function ($record) {
                                $user = getUser(Auth::user());
                                $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();

                                $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($credit_amount);
                                /** @var \App\Models\User $user */
                                $user = Auth::user();

                                return $approved  && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_assign credit'));
                            })
                            ->label(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                            ->tooltip(fn(ClinicDetail $record): string => "Assign Credit to {$record->clinic_name}")
                            ->color('success')
                            ->iconButton()
                            ->form([
                                TextInput::make('assigned_credit_line_amount')
                                    ->rules(['required', 'numeric'])
                                    ->placeholder('Enter credit amount')
                                    ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                                    ->minValue(0)
                                    ->maxValue(1000000)
                                    ->live()
                                    ->afterStateUpdated(function ($livewire, $component) {
                                        $livewire->validateOnly($component->getStatePath());
                                    })
                                    ->validationMessages([
                                        'required' => 'The Credit Amount is required.',
                                        'max' => 'The Credit Amount must not exceed 1,000,000.',
                                    ])
                                    ->validationAttribute('Credit Amount')
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                    ])
                                    ->formatStateUsing(function ($record) {
                                        $credit_amount = ClinicCreditHistory::where('supplier_id', getUser(Auth::user())->id)->where('facility_id', $record->user_id)->pluck('credit_amount')->first();
                                        return null;
                                    }),
                            ])
                            ->modalSubmitAction(false)
                            ->extraModalFooterActions(function (ClinicDetail $record): array {
                                return [
                                    \Filament\Infolists\Components\Actions\Action::make('save')
                                        ->label('Save')
                                        ->color('success')
                                        ->before(function (Component $livewire) {
                                            // dd($livewire);
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];
                                            // dd($data);
                                            if (
                                                empty($data['assigned_credit_line_amount']) ||
                                                !is_numeric($data['assigned_credit_line_amount']) ||
                                                $data['assigned_credit_line_amount'] < 0
                                            ) {
                                                // dd('here');
                                                $error = ValidationException::withMessages([
                                                    'mountedInfolistActionsData.0.assigned_credit_line_amount' => 'The Credit Amount is required.',
                                                ]);
                                                // dd($error);
                                                throw $error;
                                            } else if ($data['assigned_credit_line_amount'] > 1000000) {
                                                $error = ValidationException::withMessages([
                                                    'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount field must not be greater than 1000000.',
                                                ]);
                                                // dd($error);
                                                throw $error;
                                            }

                                            return false;
                                        })

                                        ->requiresConfirmation(function (Component $livewire) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];

                                            if (
                                                !empty($data['assigned_credit_line_amount']) &&
                                                is_numeric($data['assigned_credit_line_amount']) &&
                                                $data['assigned_credit_line_amount'] >= 0 &&
                                                $data['assigned_credit_line_amount'] <= 1000000
                                            ) {
                                                return true;
                                            }

                                            return false;
                                        })
                                        ->cancelParentActions(function (Component $livewire, $action) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];
                                            if (
                                                empty($data['assigned_credit_line_amount']) ||
                                                !is_numeric($data['assigned_credit_line_amount']) ||
                                                $data['assigned_credit_line_amount'] < 0 ||
                                                $data['assigned_credit_line_amount'] > 1000000
                                            ) {
                                                return false;
                                            }
                                            return true;
                                        })

                                        ->action(function (Component $livewire, $action) use ($record) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];

                                            if (empty($data['assigned_credit_line_amount'])) {
                                                Notification::make()
                                                    ->title('Credit Amount is required.')
                                                    ->danger()
                                                    ->send();
                                                return;
                                            }

                                            $assignedAmount = (float) $data['assigned_credit_line_amount'];
                                            $supplierId = getUser(Auth::user())->id;
                                            $facilityId = $record->user_id;

                                            $lastClinicCredit = ClinicCreditHistory::where([
                                                'facility_id' => $facilityId,
                                                'supplier_id' => $supplierId,
                                            ])->orderBy('id', 'desc')->first();
                                            $oldValues = [];
                                            if (empty($lastClinicCredit)) {
                                                $clinicCredit = new ClinicCreditHistory();
                                                $clinicCredit->facility_id = $facilityId;
                                                $clinicCredit->supplier_id = $supplierId;
                                                $clinicCredit->credit_amount = $assignedAmount;
                                                $clinicCredit->debit_amount = 0;
                                                $clinicCredit->edit_credit = 0;
                                                $clinicCredit->order_credit_used = 0;
                                                $clinicCredit->total_credit_amount = $assignedAmount;
                                                $clinicCredit->remaining_amount = $assignedAmount;
                                                $clinicCredit->reference_id = $supplierId;
                                                $clinicCredit->reference_value = 'user';
                                                $clinicCredit->action = 'PS Assign Credit';
                                                $clinicCredit->save();
                                            } else {
                                                $remainingAmount = 0;
                                                $oldValues = [
                                                    // 'credit_amount' => $lastClinicCredit->credit_amount,
                                                    'total_credit_amount' => $lastClinicCredit->total_credit_amount,
                                                    'remaining_amount' => $lastClinicCredit->remaining_amount,
                                                ];
                                                if ($lastClinicCredit->remaining_amount == 0) {
                                                    $remainingAmount = $assignedAmount + $lastClinicCredit->total_credit_amount;
                                                    $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                                } else {
                                                    // if ($assignedAmount < $lastClinicCredit->order_credit_used) {
                                                    //     $remainingAmount = 0;
                                                    // } else {
                                                    $remainingAmount = $assignedAmount + $lastClinicCredit->remaining_amount;
                                                    // }
                                                }

                                                $clinicCredit = new ClinicCreditHistory();
                                                $clinicCredit->facility_id = $facilityId;
                                                $clinicCredit->supplier_id = $supplierId;
                                                $clinicCredit->credit_amount = $assignedAmount;
                                                $clinicCredit->debit_amount = 0;
                                                $clinicCredit->edit_credit = 0;
                                                $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used;
                                                $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount + $assignedAmount;
                                                $clinicCredit->remaining_amount = $remainingAmount;
                                                $clinicCredit->reference_id = $supplierId;
                                                $clinicCredit->reference_value = 'user';
                                                $clinicCredit->action = 'PS Assign Credit';
                                                $clinicCredit->save();
                                            }

                                            Notification::make()
                                                ->title('Credit assigned successfully.')
                                                ->success()
                                                ->send();
                                            // Activitylog start
                                            $newValues = [
                                                // 'credit_amount' => $clinicCredit->credit_amount,
                                                'total_credit_amount' => $clinicCredit->total_credit_amount,
                                                'remaining_amount' => $clinicCredit->remaining_amount,
                                            ];
                                            //Activitylog end

                                            $clinicCredit->save();
                                            $user = getUser(Auth::user());
                                            $this->data = ClinicDetail::where('user_id', $this->id)->with('clinicAccountType', 'businessName')->first();
                                            $credit = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id',  $this->id)->orderBy('id', 'desc')->first();
                                            $this->creditAmount = $credit->total_credit_amount ?? 0;
                                            $this->remainingBalance =  $credit->remaining_amount ?? 0;
                                            $this->unpaidCreditUsed = $credit->order_credit_used ?? 0;

                                            activity()
                                                ->causedBy(Auth::user())
                                                ->useLog('clinic_credit_assigned')
                                                ->performedOn($clinicCredit)
                                                ->withProperties([
                                                    'old' => $oldValues,
                                                    'attributes' => $newValues,
                                                ])
                                                ->log("{$record->clinic_name} Credit has been assigned successfully.");


                                            $emailContentData = [
                                                "USER" => $record->clinic_name,
                                                "SUPPLIER" => pcCompanyName(getUser(Auth::user())->pcDetails),
                                                "ASSIGNCREDIT" => $assignedAmount,
                                                "TOTALCREDIT" => $clinicCredit->total_credit_amount,
                                            ];
                                            // dd($record->user->email);
                                            sendMailNotification($record->user, "ASSIGN_CREDIT", $emailContentData);


                                            $action->cancel();
                                            $action->cancelParentActions();
                                            // dd();// $this->record->refresh();
                                        }),

                                ];
                            }),
                        \Filament\Infolists\Components\Actions\Action::make('credit-line-edit')
                            ->icon('iconoir-wallet')->size('sm')->iconButton()
                            ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                            ->visible(function ($record) {
                                $user = getUser(auth()->user());
                                $credit_amount = ClinicCredit::where('supplier_id', $user->id)->where('facility_id', $record->user_id)->orderBy('created_at', 'desc')->value('remaining_balance');
                                $check = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                                // dd( $credit_amount);
                                $approved = $record->user !== null && $record->user->verification_status == 'approved' && !empty($check);
                                $user = auth()->user();
                                return ($this->remainingBalance > 0) && $approved && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('credit-line-orders_edit assign credit'));
                            })
                            // ->visible(fn(ClinicDetail $record): bool => $record->user !== null && $record->user->verification_status == 'approved')
                            ->label(fn(ClinicDetail $record): string => "Edit Credit to {$record->clinic_name}")
                            ->tooltip(fn(ClinicDetail $record): string => "Edit Assign Credit to {$record->clinic_name}")
                            ->color('success')
                            ->iconButton()

                            ->form([
                                TextInput::make('assigned_credit_line_amount')
                                    ->rules(['required', 'numeric'])
                                    ->placeholder('Enter credit amount')
                                    ->label(fn() => new HtmlString('Credit Amount <span style="color: red;">*</span>'))
                                    ->minValue(0)
                                    ->maxValue(1000000)
                                    ->validationMessages([
                                        'required' => 'The Credit Amount is required.',
                                        'max' => 'The Credit Amount must not exceed 1,000,000.',
                                    ])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                    ])
                                    ->formatStateUsing(function ($record) {
                                        $user = getUser(auth()->user());
                                        $credit_amount = ClinicCreditHistory::where([
                                            'facility_id' => $record->user_id,
                                            'supplier_id' => $user->id,
                                        ])->orderBy('id', 'desc')->value('total_credit_amount');
                                        return $credit_amount;
                                    }),
                            ])->modalSubmitActionLabel('Save')
                            // ->action(function ($record, Get $get, array $data) {
                            //     $record->update(['assigned_credit_line_amount' => $data['assigned_credit_line_amount'], 'assigned_credit_terms' => $data['assigned_credit_terms']]);
                            // }),
                            // ->action(function ($record, Get $get, array $data) {
                            //     // Create a new credit line
                            //     $user = getUser(auth()->user());
                            //     // Create a new credit line
                            //     $lastClinicCredit = ClinicCreditHistory::where([
                            //         'facility_id' => $record->user_id,
                            //         'supplier_id' => $user->id,
                            //     ])->orderBy('id', 'desc')->first();
                            //     $remainingAmount = 0;
                            //     // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                            //     if($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used){
                            //         $remainingAmount = 0;
                            //     }else if($data['assigned_credit_line_amount'] > $lastClinicCredit->order_credit_used){
                            //         $remainingAmount = $data['assigned_credit_line_amount']-$lastClinicCredit->order_credit_used;
                            //     }

                            //     // dd($remainingAmount);
                            //     $clinicCredit = new ClinicCreditHistory();
                            //     $clinicCredit->facility_id = $record->user_id;
                            //     $clinicCredit->supplier_id = $user->id;
                            //     $clinicCredit->credit_amount = 0;
                            //     $clinicCredit->debit_amount = 0;
                            //     $clinicCredit->edit_credit = $data['assigned_credit_line_amount'];
                            //     $clinicCredit->order_credit_used = $lastClinicCredit?->order_credit_used;
                            //     $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                            //     $clinicCredit->remaining_amount = $remainingAmount;
                            //     $clinicCredit->reference_id = $user->id;
                            //     $clinicCredit->reference_value = 'user';
                            //     $clinicCredit->action = 'PS Edit Credit';
                            //     $clinicCredit->save();


                            //     // activity()
                            //     //     ->causedBy(auth()->user())
                            //     //     ->useLog('credit_line_edit')
                            //     //     ->performedOn($clinicCredit)
                            //     //     ->withProperties([
                            //     //         'old' => $oldValues,
                            //     //         'attributes' => $newValues,
                            //     //     ])
                            //     //     ->log(($record->clinic_name ?? 'N/A') . " credit has been updated");
                            //     Notification::make()->title("Facility credit updated successfully")->success()->send();
                            //     return redirect(request()->header('Referer'));
                            // }),
                            ->modalSubmitAction(false)
                            ->extraModalFooterActions(function (ClinicDetail $record): array {
                                return [
                                    \Filament\Infolists\Components\Actions\Action::make('save')
                                        ->label('Save')
                                        ->color('success')
                                        ->before(function (Component $livewire) {
                                            // dd($livewire);
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];
                                            // dd($data);
                                            if (

                                                !is_numeric($data['assigned_credit_line_amount']) ||
                                                $data['assigned_credit_line_amount'] < 0
                                            ) {
                                                // dd('here');
                                                $error = ValidationException::withMessages([
                                                    'mountedInfolistActionsData.0.assigned_credit_line_amount' => 'The Credit Amount is required.',
                                                ]);
                                                // dd($error);
                                                throw $error;
                                            } else if ($data['assigned_credit_line_amount'] > 1000000) {
                                                $error = ValidationException::withMessages([
                                                    'mountedTableActionsData.0.assigned_credit_line_amount' => 'The Credit Amount field must not be greater than 1000000.',
                                                ]);
                                                // dd($error);
                                                throw $error;
                                            }

                                            return false;
                                        })

                                        ->requiresConfirmation(function (Component $livewire) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];

                                            if (
                                                !empty($data['assigned_credit_line_amount']) &&
                                                is_numeric($data['assigned_credit_line_amount']) &&
                                                $data['assigned_credit_line_amount'] >= 0 &&
                                                $data['assigned_credit_line_amount'] <= 1000000
                                            ) {
                                                return true;
                                            }

                                            return false;
                                        })
                                        ->cancelParentActions(function (Component $livewire, $action) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];
                                            if (
                                                !is_numeric($data['assigned_credit_line_amount']) ||
                                                $data['assigned_credit_line_amount'] < 0 ||
                                                $data['assigned_credit_line_amount'] > 1000000
                                            ) {
                                                return false;
                                            }
                                            return true;
                                        })

                                        ->action(function (Component $livewire, $action) use ($record) {
                                            $data = $livewire->mountedInfolistActionsData[0] ?? [];

                                            // if (empty($data['assigned_credit_line_amount'])) {
                                            //     Notification::make()
                                            //         ->title('Credit Amount is required.')
                                            //         ->danger()
                                            //         ->send();
                                            //     return;
                                            // }

                                            // Create a new credit line
                                            $user = getUser(auth()->user());
                                            // Create a new credit line
                                            $lastClinicCredit = ClinicCreditHistory::where([
                                                'facility_id' => $record->user_id,
                                                'supplier_id' => $user->id,
                                            ])->orderBy('id', 'desc')->first();
                                            //Activitylog start
                                            $oldValues = [
                                                // 'credit_amount' => $lastClinicCredit->credit_amount,
                                                'total_credit_amount' => $lastClinicCredit->total_credit_amount,
                                                'remaining_amount' => $lastClinicCredit->remaining_amount,
                                            ];

                                            //Activitylog end
                                            $remainingAmount = 0;
                                            // dd($data['assigned_credit_line_amount'] , $lastClinicCredit->order_credit_used);
                                            if ($data['assigned_credit_line_amount'] < $lastClinicCredit->order_credit_used) {
                                                $remainingAmount = 0;
                                            } else if ($data['assigned_credit_line_amount'] > $lastClinicCredit->order_credit_used) {
                                                $remainingAmount = $data['assigned_credit_line_amount'] - $lastClinicCredit->order_credit_used;
                                            }

                                            // dd($remainingAmount);
                                            $clinicCredit = new ClinicCreditHistory();
                                            $clinicCredit->facility_id = $record->user_id;
                                            $clinicCredit->supplier_id = $user->id;
                                            $clinicCredit->credit_amount = 0;
                                            $clinicCredit->debit_amount = 0;
                                            $clinicCredit->edit_credit = $data['assigned_credit_line_amount'];
                                            $clinicCredit->order_credit_used = $lastClinicCredit?->order_credit_used;
                                            $clinicCredit->total_credit_amount = $data['assigned_credit_line_amount'];
                                            $clinicCredit->remaining_amount = $remainingAmount;
                                            $clinicCredit->reference_id = $user->id;
                                            $clinicCredit->reference_value = 'user';
                                            $clinicCredit->action = 'PS Edit Credit';
                                            $clinicCredit->save();

                                            $user = getUser(Auth::user());
                                            $this->data = ClinicDetail::where('user_id', $this->id)->with('clinicAccountType', 'businessName')->first();
                                            $credit = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id',  $this->id)->orderBy('id', 'desc')->first();
                                            $this->creditAmount = $credit->total_credit_amount ?? 0;
                                            $this->remainingBalance =  $credit->remaining_amount ?? 0;
                                            $this->unpaidCreditUsed = $credit->order_credit_used ?? 0;
                                            //Activitylog start
                                            $newValues = [
                                                // 'credit_amount' => $clinicCredit->credit_amount,
                                                'total_credit_amount' => $clinicCredit->total_credit_amount,
                                                'remaining_amount' => $clinicCredit->remaining_amount,
                                            ];
                                            activity()
                                                ->causedBy(Auth::user())
                                                ->useLog('clinic_credit_update')
                                                ->performedOn($clinicCredit)
                                                ->withProperties([
                                                    'old' => $oldValues,
                                                    'attributes' => $newValues,
                                                ])
                                                ->log("{$record->clinic_name} Credit has been updated successfully.");

                                            $emailContentData = [
                                                "USER" => $record->clinic_name,
                                                "SUPPLIER" => pcCompanyName(getUser(Auth::user())->pcDetails),
                                                "ASSIGNCREDIT" => $data['assigned_credit_line_amount'],
                                                "TOTALCREDIT" => $clinicCredit->total_credit_amount,
                                            ];
                                            // dd($record->user->email);
                                            sendMailNotification($record->user, "UPDATE_CREDIT", $emailContentData);

                                            Notification::make()
                                                ->title('Credit updated successfully.')
                                                ->success()
                                                ->send();

                                            $action->cancel();
                                            $action->cancelParentActions();
                                        }),
                                ];
                            })
                        //                     \Filament\Infolists\Components\Actions\Action::make('reject')
                        //                                                 ->color('danger')
                        //                                                 ->outlined()
                        //                                                 ->label('Inactive Credit')
                        //                                                 ->button()
                        //                                                 ->requiresConfirmation()
                        //                                                 ->modalHeading('Confirm Inactive Credit')
                        //                                                 ->modalDescription('Are you sure you want to inactive these changes? This action cannot be undone.')
                        //                                                 ->modalSubmitActionLabel('Confirm')
                        //                                                 ->action(function ($record) {
                        //                                                     $clinicCredit = ClinicCredit::firstOrNew([
                        //                                                         'facility_id' => $record->user_id,
                        //                                                         'supplier_id' => auth()->id(),
                        //                                                     ]);
                        //                                                     if ($clinicCredit->exists) {
                        //                                                         $newRemaing = 0;
                        //                                                     if($clinicCredit->remaining_balance>0){
                        //                                                         $remaining_balance = 0;
                        //                                                         $newRemaing = 0-$clinicCredit->remaining_balance;
                        //                                                     }else{
                        //                                                         $newRemaing = $clinicCredit->remaining_balance;
                        //                                                     }
                        //                         // dd($newRemaing);
                        //                                                     $clinicCredit->credit_amount = 0;
                        //                                                     $clinicCredit->remaining_balance = $newRemaing;
                        //                                                     $clinicCredit->total_credit_amount = 0;
                        //                                                     $clinicCredit->save();
                        //                                                     Notification::make()
                        //                                                         ->title('Clinic Credit Inactve Successfully')
                        //                                                         ->success()
                        //                                                         ->send();
                        //                                                 }
                        //                                                 }),
                    ])
                    ->schema([
                        TextEntry::make('creditAmount')
                            ->label('Credit')
                            ->formatStateUsing(function ($state) {
                                return ($this->creditAmount) ? 'RM ' . $this->creditAmount : '-';
                            })->default('-'),
                        TextEntry::make('remainingBalance')
                            ->label('Credit Remaining')
                            // ->placeholder('The facility was initially assigned 1000 credits, out of which 200 were used. Although the admin has now changed the assigned credit to 0, the pending receivable from the facility remains 200. ')
                            ->formatStateUsing(function ($state) {

                                $value = $this->remainingBalance;
                                if ($value < 0) {
                                    $value = '0.00';
                                }
                                return ($value) ? 'RM ' .  $value : '-';
                            })->default('-'),

                        TextEntry::make('unpaidCreditUsed')
                            ->label('Unpaid Credit Used')
                            // ->placeholder('The facility was initially assigned 1000 credits, out of which 200 were used. Although the admin has now changed the assigned credit to 0, the pending receivable from the facility remains 200. ')
                            ->formatStateUsing(function ($state) {

                                $value = $this->unpaidCreditUsed;
                                return ($value) ? 'RM ' .  $value : '-';
                            })->default('-'),

                        TextEntry::make('user.verification_status')
                            ->label('Associated Facility Status')
                            ->badge()
                            ->formatStateUsing(function ($record) {
                                $user = getUser(auth()->user());
                                $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                                return ($credit_amount) ? 'Yes' : 'No';
                            })->default('No')
                            ->color(function ($record) {
                                $user = getUser(auth()->user());
                                $credit_amount = ClinicPharmaSupplier::where('pc_id', $user->id)->where('clinic_id', $record->user->id)->first();
                                $state =  ($credit_amount) ? 'Yes' : 'No';
                                $st = match ($state) {
                                    'No' => 'warning',
                                    'Yes' => 'success',
                                    'rejected' => 'danger',
                                    default => 'warning',
                                };
                                return $st;
                            }),
                        TextEntry::make('user')
                            ->label('Assign Credit Status')
                            ->badge()
                            // ->formatStateUsing(fn ($state): string => $state == 'approved' ? 'Accepted' : ucfirst($state))
                            ->formatStateUsing(function ($record) {
                                $user = getUser(auth()->user());
                                $credit_amount = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id', $record->user->id)->first();
                                return ($credit_amount) ? 'Yes' : 'No';
                            })
                            ->color(function ($record) {
                                $user = getUser(auth()->user());
                                $credit_amount = ClinicCreditHistory::where('supplier_id', $user->id)->where('facility_id', $record->user->id)->first();
                                $state =  ($credit_amount) ? 'Yes' : 'No';
                                $st = match ($state) {
                                    'No' => 'warning',
                                    'Yes' => 'success',
                                    'rejected' => 'danger',
                                    default => 'warning',
                                };
                                return $st;
                            }),
                        TextEntry::make('info_note')
                            ->label('') // Hide label
                            ->visible(($this->remainingBalance < 0) ? true : false)
                            ->default(function ($record) {
                                return 'The facility used ' . $this->remainingBalance . ' credits. Although the admin has now changed the assigned credit to ' . $this->creditAmount . ', the pending receivable from the facility remains ' . $this->remainingBalance;
                            })
                            ->columnSpanFull(),
                        // Placeholder::make('quantity')
                    ])->columns(4),
            ]);
    }

    protected function getFooterWidgets(): array
    {
        if (auth()->user()->can('facility_credit history') || auth()->user()->hasRole('Super Admin') || auth()->user()->hasRole('Pharmaceutical Company')) {
            return [
                CreditlineHistoryTable::make([
                    'record' => $this->data,
                ]),
            ];
        }
        return [];
    }

    public function getBreadcrumbs(): array
    {
        $routeName = 'filament.pc.resources.facilities.index';
        $title = 'Facility Details';
        return [
            route($routeName) => 'Facilities',
            2 => $title,
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return $this->data->clinic_name;
    }
}
