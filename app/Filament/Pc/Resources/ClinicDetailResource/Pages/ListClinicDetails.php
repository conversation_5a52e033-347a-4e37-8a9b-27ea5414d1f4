<?php

namespace App\Filament\Pc\Resources\ClinicDetailResource\Pages;

use App\Filament\Pc\Resources\ClinicDetailResource;
use App\Filament\Pc\Resources\FacilityOpenRequestResource\Pages\ListFacilityOpenRequests;
use App\Filament\Pc\Resources\FacilityRequestResource\Pages\ListFacilityRequests;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListClinicDetails extends ListRecords
{
    protected static string $resource = ClinicDetailResource::class;


    public function getTitle(): string
    {
        return 'Facilities';  // Custom title for the page
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Facilities",
            // 2 => "List",
        ];
    }

    // protected function applySearchToTableQuery(Builder $query): Builder
    // {
    //     $user = getUser(auth()->user());
    //     $pcUserId = $user->parent_id ?? $user->id;

    //     if (filled($search = $this->getTableSearch())) {
    //         $numericValue = preg_replace('/[^0-9.]/', '', $search);
    //         if (!empty($numericValue) && is_numeric($numericValue)) {
    //             $query->when(
    //                 filled($numericValue ?? null),
    //                 fn ($query) => $query->whereHas('user.clinicCredit', function ($q) use ($numericValue) {
    //                     $q->where('total_credit_amount', '=', $numericValue)
    //                       ->where('supplier_id', auth()->id());
    //                 })
    //             );
    //         } else{
    //             $query->where('clinic_name', 'ILIKE', "%{$search}%")
    //             ->orWhereHas('clinicAccountType', function ($q) use ($search) {
    //                 $q->where('name', 'ILIKE', "%{$search}%");
    //             });

    //         }
    //     }

    //     // }
    //     return $query;
    // }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
            Action::make('Verify Associated Facilities')
                ->label('Verify Credit Line')
                ->color('gray')
                ->visible(function () {
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return  $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('facility_verify associate facility');
                })
                ->tooltip('Facility wants to associate with Requested Pharmaceutical Suppliers')
                ->url(fn () => ListFacilityRequests::getUrl()),
            Action::make('Facility Open Requests')
                ->label('Open Account Requests')
                ->visible(function () {
                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                    return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('facility_open account request');
                })
                ->tooltip('Facility requests to open an account with the requested Pharmaceutical Suppliers')
                ->url(fn () => ListFacilityOpenRequests::getUrl()),
        ];
    }
}
