<?php

namespace App\Filament\Pc\Resources\FacilityRequestResource\Pages;

use App\Filament\Pc\Resources\ClinicDetailResource;
use App\Filament\Pc\Resources\FacilityRequestResource;
use App\Models\ClinicPharmaSupplier;
use Filament\Actions;
use Filament\Actions\Action;
use Livewire\Attributes\Session;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\View\View;

class ListFacilityRequests extends ListRecords
{
    protected static string $resource = FacilityRequestResource::class;
    #[Session]
    public bool $isVerified = true;

    #[Session]
    public bool $isPending = false;

    #[Session]
    public bool $isRejected = false;

    public function getTitle(): string
    {
        return 'Verify Associated Facilities';  // Custom title for the page
    }


    public function getBreadcrumbs(): array
    {
        return [
            // route('filament.pc.resources.facilities.index') => 'Facilities',
            // $this->getResource()::getUrl('index') => "Verify Associated Facilities",
        ];
    }

    public function getHeader(): ?View
    {
        return $this->getHeaderHeading();
    }


    private function getHeaderHeading()
    {
        return view('custom-view.header-action-request', [
            'title' => 'Verify Credit Line',
            'headerActions' => $this->getHeaderActions(),
            'isVerified' => $this->isVerified,
            'isRejected' => $this->isRejected,
            'isPending' => $this->isPending,
            // 'pending' => ClinicPharmaSupplier::query()->pendingApprovals()->count(),
        ]);
    }

    public function pendingApprovals()
    {
        $this->isPending = true;
        $this->isVerified = false;
        $this->isRejected = false;
        $this->resetTable();
    }

    public function allVerified()
    {
        $this->isPending = false;
        $this->isVerified = true;
        $this->isRejected = false;
        $this->resetTable();
    }

    public function rejectedApprovals()
    {
        $this->isPending = false;
        $this->isVerified = false;
        $this->isRejected = true;
        $this->resetTable();
    }

    protected function getTableQuery(): ?Builder
    {
        if ($this->isVerified) {
            return ClinicPharmaSupplier::query()->where('status', 'approved')->where('is_open_account', false)->where('pc_id', auth()->user()->id);
        } elseif ($this->isPending) {
            return ClinicPharmaSupplier::query()->where('status', 'pending')->where('is_open_account', false)->where('pc_id', auth()->user()->id);
        } else {
            return ClinicPharmaSupplier::query()->where('status', 'rejected')->where('is_open_account', false)->where('pc_id', auth()->user()->id);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicDetailResource::getUrl('index')),

        ];
    }
}
