<?php

namespace App\Filament\Pc\Resources\UserManageResource\Pages;

use App\Filament\Pc\Resources\UserManageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Imports\UserManageImporter;

class ListUserManages extends ListRecords
{
    protected static string $resource = UserManageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add User'),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Users",
            // 2 => "List",
        ];
    }
}
