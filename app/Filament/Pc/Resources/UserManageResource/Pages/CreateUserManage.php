<?php

namespace App\Filament\Pc\Resources\UserManageResource\Pages;

use App\Filament\Pc\Resources\UserManageResource;
use App\Mail\SendPCUserPasswordMail;
use App\Mail\UserCreatedMail;
use App\Models\User;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Spatie\Permission\Models\Role;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;


class CreateUserManage extends CreateRecord
{
    protected static string $resource = UserManageResource::class;

    protected string $plainPassword;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
          Action::make('back')
                ->label('Back')
                ->color('gray')
                 ->url(UserManageResource::getUrl()),
        ];
    }

    public function getTitle(): string
    {
        return 'Add User';
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Users',
            1 => "Add User",
        ];
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Add'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['parent_id'] = auth()->user()->parent_id ?? auth()->id();
        $data['created_by'] = auth()->id();
        unset($data['role']);

        return $data;
    }

    protected function afterCreate(): void
    {
        $user = $this->record;
        $role = $this->data['role'];

        if ($role) {
            $user->assignRole($role);
        }

        // Mail::to($this->record->email)->send(new UserCreatedMail($this->record, 'pc'));
        $this->sendCredential($this->record);

        try {
            Notification::make()
                ->success()
                // ->title('User has been Created Successfully')
                ->title("User has been created successfully.")
                // ->persistent()
                ->send();
            //Mail::to($user->email)->send(new UserCreatedMail($user->username, $this->plainPassword));
        } catch (\Exception $e) {
            Notification::make()
                ->warning()
                // ->title('User has been Created Successfully')
                ->title("User created successfully with username '{$user->username}' but email notification failed to send.")
                ->persistent()
                ->send();
        }
    }


    protected function getCreatedNotification(): ?Notification
    {
        return null;
    }

    public function sendCredential($data)
    {
        $userEmail = $data['email'];
        $plainPassword = generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['id'])->update(['password' => $hashedPassword, 'is_temp_password' => true]);
        $loginUrl = config('app.pc_url') . '/home';
        Mail::to($userEmail)->send(new SendPCUserPasswordMail($plainPassword, $userEmail, $loginUrl, $data));
    }
}
