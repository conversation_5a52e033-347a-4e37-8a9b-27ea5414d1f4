<?php

namespace App\Filament\Pc\Resources\UserManageResource\Pages;

use App\Filament\Pc\Resources\UserManageResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Notifications\Notification;
use Filament\Pages\Actions\Action;
use Indianic\FilamentShield\Resources\RoleResource\Pages\ViewRole;

class ViewUserManage extends ViewRecord
{
    protected static string $resource = UserManageResource::class;

    public function getHeaderActions(): array
    {

        return [
            Actions\EditAction::make(),

            Action::make('back')
                ->label('Back')
                ->url(UserManageResource::getUrl())
                ->color('gray'),
        ];
    }

    public function getTitle(): string
    {
        return $this->record->name;
    }

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Details')
                    ->schema([
                        Grid::make()
                            ->schema([
                                // Left column with large avatar
                                ImageEntry::make('photo')
                                    ->circular()
                                    ->columnSpan(1)
                                    ->default(asset('/images/user-avatar.png')),
                                // Right column with user details in a grid
                                Grid::make()
                                    ->schema([
                                        TextEntry::make('id')
                                            ->label('User ID')
                                            ->formatStateUsing(fn(string $state): string => "#{$state}"),
                                        TextEntry::make('name')
                                            ->label('Full Name'),
                                        TextEntry::make('email')
                                            ->label('Email'),
                                        TextEntry::make('phone')
                                            ->label('Phone Number')
                                            ->formatStateUsing(fn(string $state): string => preg_replace('/(\+\d{1,3})(\d{6,})/', '$1 $2', $state)),
                                        TextEntry::make('roles.name')
                                            ->label('Role')
                                            ->default('-')
                                            ->url(fn($record) => $record->roles->first() ? ViewRole::getUrl(['record' => $record->roles->first()->id]) : null)
                                            ->formatStateUsing(
                                                fn($state) =>
                                                $state
                                                    ? '<span style="color: blue;">' . preg_replace('/^\d+-/', '', $state) . '</span>'
                                                    : '<span style="color: blue;">-</span>'
                                            )
                                            ->html(),
                                        ViewEntry::make('is_active')
                                            ->label('Status')
                                            ->visible(function () {
                                                $user = getUser(auth()->user());
                                                $isPharmaceuticalCompany = isPharmaceuticalCompany();
                                                return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('users_change status');
                                            })
                                            ->view('components.user-toggle-switch')


                                    ])
                                    ->columns(3)
                                    ->columnSpan(2),
                            ])
                            ->columns(3),
                    ]),
            ]);
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Users',
            1 => 'User Details',
        ];
    }
    public function toggleStatus($userId, $status)
    {
        $user = \App\Models\User::find($userId);

        if ($user) {
            $user->is_active = $status;
            $user->save();

            // If user is being deactivated, logout from all sessions
            if (!$status) {
                \App\Filament\Admin\Resources\UserManageResource::logoutUserById($userId);
                Notification::make()
                    ->success()
                    ->title('Status Updated')
                    ->body("User has been deactivated and logged out from all sessions.")
                    ->send();
            } else {
                Notification::make()
                    ->success()
                    ->title('Status Updated')
                    ->body("User has been activated successfully.")
                    ->send();
            }
        }
    }
}
