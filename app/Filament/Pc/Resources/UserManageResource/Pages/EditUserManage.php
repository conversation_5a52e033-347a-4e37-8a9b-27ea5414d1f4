<?php

namespace App\Filament\Pc\Resources\UserManageResource\Pages;

use App\Filament\Pc\Resources\UserManageResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditUserManage extends EditRecord
{
    protected static string $resource = UserManageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserManageResource::getUrl()),
        ];
    }


    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Users',
            1 => 'Edit User',
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        unset($data['role']);

        return $data;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['role'] = $this->record->roles->first()?->name;

        return $data;
    }

    // protected function afterSave(): void
    // {
    //     $user = $this->record;
    //     $roleName = $this->data['role'];

    //     if ($roleName) {
    //         $user->syncRoles([$roleName]);
    //     }
    // }

    protected function afterSave(): void
    {
        $user = $this->record;
        $newRoleName = $this->data['role'];
        $oldRoleName = $user->roles->first()?->name;

        if ($newRoleName && $newRoleName !== $oldRoleName) {
            $user->syncRoles([$newRoleName]);

            activity()
                ->causedBy(auth()->user())
                ->useLog('users')
                ->performedOn($user)
                ->withProperties([
                    'old' => [
                        'role' => $oldRoleName,
                    ],
                    'attributes' => [
                        'role' => $newRoleName,
                    ],
                ])
                ->log("PS User has been updated");
        }
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title('User Updated')
            ->title('The user has been updated successfully.');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
