<?php

namespace App\Filament\Pc\Resources;

use Carbon\Carbon;
use App\Models\User;
use Filament\Tables;
use App\Models\Order;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\SupportTicket;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\MarkdownEditor;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages\ConversionDetailsAssigned;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages\ListSupportTicketAssigneds;
use App\Filament\Pc\Resources\SupportTicket\SupportTicketAssignedResource\Pages\CreateSupportTicketAssigned;
use App\Models\Media;
use App\Models\PcDetail;
use App\Models\SubOrder;
use App\Models\SupportTicketMessage;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Get;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Filament\Facades\Filament;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Set;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class SupportTicketAssignedResource extends Resource
{
    protected static ?string $model = SupportTicket::class;

    protected static ?string $navigationLabel = 'DPharma Support';

    protected static ?string $navigationGroup = 'Support';
    public $ticket = null;

    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        //auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('dpharma-support_view') || $user->can('dpharma-support_create');
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('dpharma-support_create');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Add New Ticket')
                    ->schema([
                        Select::make('order_id')
                            ->label(new HtmlString("Order ID <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->options(function () {
                                $authUser = auth()->user();
                                $authId = $authUser->id;
                                $parentId = $authUser->parent_id;

                                $senderIds = [$authId];
                                if ($parentId) {
                                    $senderIds[] = $parentId;
                                }

                                $usedOrderIds = SupportTicket::whereIn('sender_id', $senderIds)
                                    ->pluck('order_id')
                                    ->toArray();

                                return Order::query()
                                    ->whereNotIn('id', $usedOrderIds)
                                    ->whereHas('subOrders', function ($query) use ($authId, $parentId) {
                                        $query->where(function ($q) use ($authId, $parentId) {
                                            $q->where('user_id', $authId);
                                            if ($parentId) {
                                                $q->orWhere('user_id', $parentId);
                                            }
                                        });
                                    })
                                    ->withCount([
                                        'orderProducts as order_products_count' => function (Builder $query) {
                                            $query->select(DB::raw('COALESCE(COUNT(*), 0)'));
                                        },
                                    ])
                                    ->orderBy('order_number', 'asc')
                                    ->pluck('order_number', 'id');
                            })
                            ->rules(['required'])
                            ->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.order_required'),
                            ])
                            ->searchable()
                            ->placeholder(__('message_pc.support_ticket_assigned.order_placeholder')),

                        TextInput::make('name')
                            ->label(new HtmlString("Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder(__('message_pc.support_ticket_assigned.name_placeholder'))
                            ->rules(['required', 'regex:/^[A-Za-z\s]+$/', 'max:50'])
                            ->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.name_required'),
                                'max' => __('message_pc.support_ticket_assigned.name_max'),
                                'regex' => __('message_pc.support_ticket_assigned.name_regex'),
                            ]),

                        Hidden::make('sender_id')
                            ->default(function () {
                                $user = auth()->user();
                                return $user->parent_id ?: $user->id;
                            })
                            ->rules(['required', 'exists:users,id']),

                        Hidden::make('receiver_id')
                            ->default(function () {
                                $superAdmin = \App\Models\User::whereHas('roles', function ($query) {
                                    $query->where('name', 'Super Admin');
                                })->first();

                                return $superAdmin ? $superAdmin->id : null;
                            })
                            ->rules(['required', 'exists:users,id']),

                        TextInput::make('receiver_name')
                            ->label('Raised To')
                            ->placeholder(__('message_pc.support_ticket_assigned.receiver_placeholder'))
                            ->default(function () {
                                $superAdmin = \App\Models\User::find(1);

                                return $superAdmin && $superAdmin->hasRole('Super Admin')
                                    ? $superAdmin->name
                                    : 'Super Admin';
                                
                            })
                            ->disabled(),

                        TextInput::make('email')
                            ->label(new HtmlString("Email <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder(__('message_pc.support_ticket_assigned.email_placeholder'))
                            ->maxLength(50)
                            ->default(Filament::auth()->user()->email ?? '')
                            ->rules([
                                'required',
                                'email',
                                'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                'max:50',
                                // Rule::unique('users', 'email')->ignore(Auth::user()->id ?? null)
                            ])
                            ->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.email_required'),
                                'regex' => __('message_pc.support_ticket_assigned.email_regex'),
                                'email' => __('message_pc.support_ticket_assigned.email_email'),
                                'max' => __('message_pc.support_ticket_assigned.email_max'),
                                // 'unique' => __('message_pc.support_ticket_assigned.email_unique'),
                            ])
                            ->afterStateUpdated(
                                function (Get $get, Set $set) {
                                    $set('email', \Str::lower($get('email')));
                                }
                            )
                            ->live(),

                        Select::make('category_id')
                            ->label(new HtmlString("Category <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder(__('message_pc.support_ticket_assigned.category_placeholder'))
                            ->relationship('SupportCategory', 'name')
                            ->searchable()
                            ->preload()
                            ->rules(['required', 'exists:support_categories,id'])
                            ->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.category_required'),
                            ]),
                        TextInput::make('subject')
                            ->label(new HtmlString("Subject <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder(__('message_pc.support_ticket_assigned.subject_placeholder'))
                            ->rules(['required', 'string', 'max:100'])->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.subject_required'),
                                'max' => __('message_pc.support_ticket_assigned.subject_max'),
                            ]),

                        MarkdownEditor::make('description')
                            ->label(new HtmlString("Description <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder(__('message_pc.support_ticket_assigned.description_placeholder'))
                            ->rules(['required', 'string', 'max:500'])
                            ->disableToolbarButtons(['attachFiles'])
                            ->columnSpanFull()->validationMessages([
                                'required' => __('message_pc.support_ticket_assigned.description_required'),
                                'max' => __('message_pc.support_ticket_assigned.description_max'),
                            ]),
                        SpatieMediaLibraryFileUpload::make('images')
                            ->collection('support-ticket-images')
                            ->multiple()
                            ->openable()
                            ->maxFiles(3)
                            ->label('Attachments (optional)')
                            ->rules(['nullable', 'mimes:jpg,jpeg,png,pdf', 'max:2048'])
                            ->helperText('Maximum 3 images allowed. Each image or pdf must be under 2MB. Only image files (JPEG, PNG, JPG and PDF) are accepted.')
                            ->validationMessages([
                                'mimes' => 'Only JPG, JPEG, PNG and PDF formats are allowed.',
                                'max' => 'The image and PDF must not exceed 2MB.'
                            ])                        
                            ->columnSpanFull(),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
         $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->query(function (Builder $query) {
                $user = auth()->user();
                $baseQuery = SupportTicket::query();
                if ($user->parent_id === null) {
                    return $baseQuery->where('sender_id', $user->id);
                } else {
                    return $baseQuery->where(function ($query) use ($user) {
                        $query->where('sender_id', $user->id)
                            ->orWhere('sender_id', $user->parent_id);
                    });
                }
            })->emptyStateHeading('No facilities support ticket records found')
            ->recordUrl(function (SupportTicket $record): string {
                return self::getUrl('details', ['record' => $record]);
            })
            ->columns([
                TextColumn::make('id')
                    ->label('Ticket ID')
                    ->sortable()
                    ->prefix('TKT-')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('order.order_number')
                    ->url(function (SupportTicket $record): ?string {
                        if (
                            $record->order_id &&
                            (
                                auth()->user()->hasRole('Pharmaceutical Company') ||
                                auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            $subOrder = SubOrder::where('order_id', $record->order_id)->first();
                            if ($subOrder) {
                                return route('filament.pc.resources.orders.view', ['record' => $subOrder->id]);
                            }
                        }
                        return null;
                    })
                    ->tooltip(function (SupportTicket $record) {
                        if (
                            !$record->order_id ||
                            (
                                !auth()->user()->hasRole('Pharmaceutical Company') &&
                                !auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            return 'You do not have permission to view this order.';
                        }
                        return null;
                    })
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('receiver.name')
                    ->label('Raised To')
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->receiver && $record->receiver->hasRole('Super Admin')) {
                            return 'DPharma';
                        }
                        return ucfirst($state);
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('receiver', function (Builder $query) use ($search) {
                            $query->where(function (Builder $query) use ($search) {
                                $query->where('name', 'like', "%{$search}%")
                                    ->orWhere(function (Builder $query) use ($search) {
                                        if (stripos('DPharma', $search) !== false) {
                                            $query->where('id', 1);
                                        }
                                    });
                            });
                        });
                    })
                    ->toggleable(),
                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
               TextColumn::make('created_at')
                    ->label('Created Date')
                    ->sortable()->toggleable()->searchable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),
                TextColumn::make('email')->searchable()->sortable()
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('category.name')->searchable()
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('subject')->searchable()
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->limit(10)
                    ->tooltip(fn($record) => $record->subject)
                    ->suffix('...'),
                TextColumn::make('closed_at')
                    ->label('Closed Date')
                    ->sortable()->toggleable()->searchable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        if (is_null($state) || $state === '' || $state === '-') {
                            return '-';
                        }
                        try {
                            return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                                ->setTimezone($timeZone)
                                ->format('M d, Y | h:i A');
                        } catch (\Exception $e) {
                            return '-';
                        }
                    })->default('-'),
                TextColumn::make('status')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'open' => 'success',
                        'closed' => 'danger',
                    })->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                // SelectFilter::make('account_type')
                //     ->label('Account Type')
                //     ->searchable()
                //     ->multiple()
                //     ->options([
                //         'Clinic' => 'Clinic',
                //         'Pharmaceutical Company' => 'Pharmaceutical Company',
                //     ])
                //     ->query(function (Builder $query, array $data): Builder {
                //         if (empty($data['values'])) {
                //             return $query;
                //         }

                //         return $query->where(function ($query) use ($data) {
                //             $query->whereHas('sender', function ($q) use ($data) {
                //                 $q->whereNull('parent_id')
                //                     ->whereHas('roles', function ($roleQuery) use ($data) {
                //                         $roleQuery->whereIn('name', $data['values']);
                //                     });
                //             });

                //             $query->orWhereHas('sender', function ($q) use ($data) {
                //                 $q->whereNotNull('parent_id')
                //                     ->whereHas('parent.roles', function ($parentRoleQuery) use ($data) {
                //                         $parentRoleQuery->whereIn('name', $data['values']);
                //                     });
                //             });
                //         });
                // //     }),
                // SelectFilter::make('sender_id')
                //     ->label('Received From')
                //     ->multiple()
                //     ->relationship('sender', 'name'),
                SelectFilter::make('category_id')
                    ->label('Category')
                    ->multiple()
                    ->relationship('category', 'name')
                    ->preload(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->multiple()
                    ->options([
                        'open' => 'Open',
                        'closed' => 'Closed',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereIn('status', $data['values']);
                    }),
                Filter::make('created_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Created Date')->format('Y-m-d')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['date']) {
                            \Log::info('Filtering by date: ' . $data['date']);
                            $query->whereDate('created_at', $data['date']);
                        }
                        return $query;
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' . Carbon::parse($data['date'])->toFormattedDateString();
                    }),
                Filter::make('closed_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Closed Date')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['date'], fn($q) => $q->whereDate('closed_at', $data['date']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' .  Carbon::parse($data['date'])->toFormattedDateString();
                    }),
            ])
            ->actionsColumnLabel('Action')
            ->actions([
                // Action::make('delete')
                //     ->icon('heroicon-o-trash')->size('xs')->iconButton()
                // ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit;'])
                //     ->label('')
                //     ->color('danger')
                //     ->modalHeading('Confirm Deletion')
                //     ->action(fn(SupportTicket $record) => $record->delete())
                //     ->requiresConfirmation(),
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->label('')
                    ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record])),
                Action::make('Chat')
                    ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')
                    ->size('sm')->tooltip('Chat')
                    ->iconButton()
                    ->visible(function () {
                        $user = auth()->user();
                        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('dpharma-support_chat');
                    })
                    ->color(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    })
                    ->extraAttributes(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return [
                            'class' => 'border-2 rounded-lg ' . ($unreadCount > 0 ? 'text-green-500' : 'text-blue-500'),
                            'style' => 'margin-left: inherit; border-color: ' . ($unreadCount > 0 ? 'rgb(34, 197, 94)' : 'rgb(0, 70, 104)'),
                        ];
                    })
                    ->label('')
                    ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record]) . '#conversation-header')
                    ->badge(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->badgeColor(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }


    public static function getPages(): array
    {

        return [
            'index' => ListSupportTicketAssigneds::route('/'),
            'create' => CreateSupportTicketAssigned::route('/create'),
            // 'edit' => Pages\EditSupportTicketAssigned::route('/{record}/edit'),
            'details' => ConversionDetailsAssigned::route('assigned/{record}/details'),
        ];
    }
}
