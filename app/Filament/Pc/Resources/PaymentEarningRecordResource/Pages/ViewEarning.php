<?php

namespace App\Filament\Pc\Resources\PaymentEarningRecordResource\Pages;

use App\Filament\Pc\Resources\PaymentEarningRecordResource;
use App\Filament\Pc\Resources\PaymentEarningRecordResource\Widgets\PaymentEarningItemsTable;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\View\View;
use Filament\Actions\Action;

    class ViewEarning extends ViewRecord
    {
        protected static string $resource = PaymentEarningRecordResource::class;

        public function getTitle(): string
        {
            return '#' . $this->getRecord()->id;
        }

        public function getBreadcrumbs(): array
        {
            return [
                1 => 'Payment Records',
                route('filament.pc.resources.payment-earning-records.index') => 'Income Stream',
                2 => 'Payment Record Details',
            ];
        }

        protected function getHeaderActions(): array
        {
            return [
                Action::make('back')
                    ->label('Back')
                    ->color('gray')
                    ->url(PaymentEarningRecordResource::getUrl('index')),
            ];
        }

        protected function getFooterWidgets(): array
        {
            return [
                PaymentEarningItemsTable::make([
                    'record' => $this->getRecord(), // Pass the current payout record
                ]),
            ];
        }

    }
