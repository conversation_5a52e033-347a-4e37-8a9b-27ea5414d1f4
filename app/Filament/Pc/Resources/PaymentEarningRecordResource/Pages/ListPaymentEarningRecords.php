<?php

namespace App\Filament\Pc\Resources\PaymentEarningRecordResource\Pages;

use App\Filament\Pc\Resources\PaymentEarningRecordResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Concerns\InteractsWithTable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListPaymentEarningRecords extends ListRecords
{
    protected static string $resource = PaymentEarningRecordResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Payment Records',
            // $this->getResource()::getUrl('index') => "Earnings",
            // 2 => "List",
        ];
    }

    protected function applySearchToTableQuery(Builder $query): Builder
    {
        $user = getUser(auth()->user());

        if (filled($search = $this->getTableSearch())) {
            $numericValue = preg_replace('/[^0-9.]/', '', $search); //preg_replace('/[^0-9.]/', '', $search);
            // dd($numericValue);
            if (!empty($numericValue) && is_numeric($numericValue)) {
                $query->whereHas('payoutSubOrders.subOrder', function ($subQuery) use ($search) {
                    $subQuery->where('payment_type', '!=', 'credit_line')
                        ->where(function ($q) use ($search) {
                            // Filter for 'schedule' payout type
                            $q->whereHas('orderProducts', function ($orderQuery) use ($search) {
                                $orderQuery->select('sub_order_id')
                                    ->groupBy('sub_order_id')
                                    ->havingRaw('SUM(total_sub_order_value - COALESCE(total_commission, 0)) = ?', [$search]);
                            }, '>=', 1) // Ensure at least one order product matches
                                ->orWhere(function ($q) use ($search) {
                                    // Filter for 'full' payout type
                                    $q->where('payout_type', 'full')
                                        ->whereRaw('total_sub_order_value = ?', [$search]);
                                });
                        });
                });
                // $query->where(function ($mainQuery) use ($numericValue) {
                //     $mainQuery->whereHas('payoutSubOrders.subOrder', function ($subOrderQuery) use ($numericValue) {
                //         $subOrderQuery->where('payment_type', '!=', 'credit_line')
                //             ->where(function ($innerQuery) use ($numericValue) {
                //                 $innerQuery->where(function ($fullQuery) use ($numericValue) {
                //                     $fullQuery->where('payout_type', 'full')
                //                         ->where('total_sub_order_value', $numericValue);
                //                 })
                //                     ->orWhere(function ($scheduleQuery) use ($numericValue) {
                //                         $scheduleQuery->where('payout_type', 'schedule')
                //                             ->whereRaw('(total_sub_order_value - (
                //                             SELECT COALESCE(SUM(total_commission), 0)
                //                             FROM order_products
                //                             WHERE order_id = sub_orders.order_id
                //                         )) = ?', [$numericValue]);
                //                     });
                //             });
                //     });
                // });
            } else {
                // Text search handling
                $query->where('payout_invoice_id', 'ilike', "%{$search}%");
            }
        }

        return $query;
    }



    public function getTitle(): string
    {
        return 'Income Stream';
    }
}
