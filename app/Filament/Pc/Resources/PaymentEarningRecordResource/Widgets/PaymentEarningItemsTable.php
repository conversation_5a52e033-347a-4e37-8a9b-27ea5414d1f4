<?php

namespace App\Filament\Pc\Resources\PaymentEarningRecordResource\Widgets;

use App\Models\Payout;
use App\Models\PayoutSubOrder;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\PcDetail;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Cookie;

class PaymentEarningItemsTable extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';
    public $record = null;
    protected static string $view = 'filament.pc.resources.payment-earning-record-resource.widgets.payment-earning-items-table';
    protected static ?string $heading  = 'Order Details';
    // public function table(Table $table): Table
    // {
    //     $record = 1;

    //     return $table
    //         ->query(Payout::where('id', $record)->with(['user','payoutSubOrders.order'])
    //         )
    //         ->columns([
    //             TextColumn::make('payoutSubOrders.order.order_number')->label('Order ID'),
    //             TextColumn::make('payoutSubOrders.order.created_at')->label('Order Date'),
    //             TextColumn::make('payoutSubOrders.order.amount')->label('Order Total')



    //             // ->hidden(
    //             //     fn(TextColumn $column): bool => dd($column->getRecord())
    //             //     // $column->getRecord() &&
    //             //     //     $column->getRecord()->productData &&
    //             //     //     $column->getRecord()->productData->is_batch_wise_stock
    //             // )
    //         ]);
    // }

    public function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table
            ->query(function (Builder $query) use ($user) {
                return PayoutSubOrder::where('payout_id', $this->record->id)
                    ->with(['Payout', 'subOrder', 'order', 'order.subOrder.orderProducts'])
                    ->whereHas('subOrder', function ($query) use ($user) {
                        $query->where('user_id', $user);
                    })
                    ->join('sub_orders', 'payout_sub_orders.sub_order_id', '=', 'sub_orders.id')
                    ->join('orders', 'sub_orders.order_id', '=', 'orders.id')
                    ->select('payout_sub_orders.*')
                    // ->select('payout_sub_orders.*')
->addSelect([
    'admin_fee' => OrderProduct::selectRaw('SUM(total_commission)')
        ->whereColumn('sub_orders.id', 'order_products.sub_order_id')
]);
            })
            ->recordUrl(function ($record) {
                $params = ['record' => $record->sub_order_id];

                if ($record->subOrder->payment_type === 'credit_line') {
                    $params['type'] = 'credit_line';
                }

                return route('filament.pc.resources.orders.view', $params);
            })
            ->columns([
                TextColumn::make('order.order_number')->label('Order ID')->toggleable()
                    ->formatStateUsing(fn(string $state): string =>  !empty($state) ? '#' . $state : '-')
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('orders.order_number', $direction);
                    }),
                TextColumn::make('order.created_at')->label('Order Date')
                    ->formatStateUsing(function ($state) {
                        $user = getUser(auth()->user())->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    })->toggleable()
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('orders.created_at', $direction);
                    }),
                TextColumn::make('subOrder.total_sub_order_value')->label('Order Total')->prefix('RM ')->toggleable()
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy('sub_orders.total_sub_order_value', $direction);
                    }),
                TextColumn::make('admin_fee')
                    ->label('Admin Fee')
                    ->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2))
                    ->sortable(), // Admin Fee is calculated, so we can't sort by it directly
                TextColumn::make('subOrder.total_amount')->label('Payable Amount')->toggleable()
                    ->formatStateUsing(function ($record) use ($user) {
                        $subOrder = $record->order?->subOrder?->firstWhere('user_id', $user);

                        if ($subOrder && ($subOrder->payment_type != 'credit_line')) {

                            $commision = $subOrder->orderProducts->sum('total_commission');

                            if ($subOrder->payout_type == 'schedule') {
                                $finalAmt = $subOrder->total_sub_order_value - $commision;
                            } else {
                                $finalAmt = $subOrder->total_sub_order_value;
                            }

                            return 'RM ' . number_format($finalAmt, 2);
                        }
                        return 'RM 0.00';
                    })
                    ->sortable(false), // Payable Amount is calculated, so we can't sort by it directly
            ])
            ->defaultSort('order.created_at', 'desc')
            ->filters([])
            ->actionsColumnLabel('Actions')
            ->actions([
                Action::make('view')
                    ->url(function(PayoutSubOrder $record){
                         Cookie::queue('source', 'payment-earning');
                         Cookie::queue('payoutId', $this->record->id);
                        return route('filament.pc.resources.orders.view', ['record' => $record->sub_order_id]);
                    } ) // Change order_id to record
                    ->icon('heroicon-o-eye')->iconButton()->size('sm')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->visible(fn(PayoutSubOrder $record): bool => $record->subOrder->payment_type != 'credit_line')
                    ->color('gray')
                    ->tooltip('Order Details')
                    ->label(''),

                Action::make('credit_line')
                    ->icon('heroicon-o-eye')
                    ->label('')
                    ->color('gray')->iconButton()->size('sm')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Order Details')
                    ->visible(fn(PayoutSubOrder $record): bool => $record->subOrder->payment_type === 'credit_line')
                    ->url(fn(PayoutSubOrder $record): string => route('filament.pc.resources.orders.view', ['record' => $record->sub_order_id, 'type' => 'credit_line'])),

            ]);
    }
}
