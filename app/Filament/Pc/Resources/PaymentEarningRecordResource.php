<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\PaymentEarningRecordResource\Pages;
use App\Models\Payout;
use App\Models\PcDetail;
use Carbon\Carbon;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Facades\Filament;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Tables\Concerns\InteractsWithTable;
use Illuminate\Contracts\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\DB;
use Filament\View\PanelsRenderHook;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;

class PaymentEarningRecordResource extends Resource
{
    use InteractsWithTable;
    protected static ?string $model = Payout::class;

    protected static ?string $navigationGroup = 'Payment Records';
    protected static ?string $navigationLabel = 'Income Stream';

    // public static function canAccess(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('orders_view');
    // }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            // Removed extraAttributes for AlpineJS tab handling
            ->schema(function ($record) {
                $record = $record->load('user', 'payoutSubOrders');
                $start = Carbon::parse($record->start_date);
                $end = Carbon::parse($record->end_date);

                $monthYear = $start->format('M');
                $startFormatted = $start->format('jS M');
                $endFormatted = $end->format('jS M');

                $headerTitle =  "{$monthYear} ({$startFormatted} - {$endFormatted})";
                return [
                    InfoSection::make($headerTitle)
                        ->schema([
                            \Filament\Infolists\Components\Grid::make([
                                'default' => 5,
                                'sm' => 5,
                            ])
                                ->columnSpan('full')
                                ->extraAttributes(['class' => 'gap-0'])
                                ->schema([


                                    \Filament\Infolists\Components\Grid::make([
                                        'default' => 3,
                                        'sm' => 3,
                                    ])
                                        ->columnSpan(3)
                                        ->schema([

                                            TextEntry::make('id')
                                                ->label('ID'),
                                            TextEntry::make('payout_on')
                                                ->label('Date')->formatStateUsing(function ($state) {
                                                    $user = Filament::auth()->user()->id;
                                                    $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                                                    return getFormatedDate($state, $format);
                                                }),


                                            TextEntry::make('payoutSubOrders')
                                                ->label('Total Orders')
                                                ->formatStateUsing(function ($record) {
                                                    $count = $record->payoutSubOrders->count();
                                                    return $count;
                                                }),
                                            TextEntry::make('transaction_id')
                                                ->label('Transaction ID'),
                                            TextEntry::make('payoutSubOrders')
                                                ->label('Payable Amount')
                                                ->formatStateUsing(function ($record) {
                                                    $amount = $record->payoutSubOrders->sum(function ($payoutSubOrder) {
                                                        $subOrder = $payoutSubOrder->subOrder;
                                                        if ($subOrder->payment_type != 'credit_line') {

                                                            if ($subOrder->payout_type === 'schedule') {
                                                                // For schedule type, subtract total_commission from total_sub_order_value
                                                                $commision = $subOrder->orderProducts->sum('total_commission');
                                                                return ($subOrder->total_sub_order_value ?? 0) - ($commision ?? 0);
                                                            } else if ($subOrder->payout_type === 'full') {
                                                                // For full type, use total_sub_order_value as is
                                                                return $subOrder->total_sub_order_value ?? 0;
                                                            }
                                                        }

                                                        return 0; // Default case
                                                    });
                                                    return 'RM ' . number_format($amount, 2);
                                                }),

                                            TextEntry::make('payout_status')
                                                ->label('Status')
                                                ->formatStateUsing(function ($state) {
                                                    switch ($state) {
                                                        case 'pending':
                                                            return '<span class="px-2 py-1 text-xs font-semibold text-yellow-600 bg-yellow-100 rounded-full">Pending</span>';
                                                        case 'paid':
                                                            return '<span class="px-2 py-1 text-xs font-semibold text-green-600 bg-green-100 rounded-full">Paid</span>';
                                                        case 'failed':
                                                            return '<span class="px-2 py-1 text-xs font-semibold text-red-600 bg-red-100 rounded-full">Failed</span>';
                                                        default:
                                                            return '<span class="px-2 py-1 text-xs font-semibold text-gray-600 bg-gray-100 rounded-full">-</span>';
                                                    }
                                                })
                                                ->html(),
                                        ])
                                ])
                        ]),


                ];
            });
    }

    public static function table(Table $table): Table
    {
        $user = getUser(auth()->user())->id;
        return $table->query(
            Payout::with(['payoutSubOrders', 'payoutSubOrders.order'])
                ->where('user_id', $user)
                ->where('is_payout', true)
        )
            // ->recordUrl(null)
            ->description('Earnings/Income Stream: This page displays a detailed record of the payments you’ve received from your orders, over selected timeframes. (Next Payout: ' . self::getNextPayCycle() . ')')
            ->emptyStateHeading('No Income Stream records found')
            ->columns([
                TextColumn::make('payout_on')
                    ->formatStateUsing(function ($state) {
                        $user = getUser(auth()->user())->id;
                        $format = PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                        return ($state != '-') ? getFormatedDate($state, $format) : '-';
                    })
                    ->default('-')
                    ->label('Payout Date')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('payout_invoice_id')
                    ->toggleable()
                    ->default('-')
                    ->label('Payout Invoice Id'),

                TextColumn::make('start_date')
                    ->toggleable()
                    ->label('Earning Cycle')
                    ->formatStateUsing(function ($record) {
                        if (!$record->start_date || !$record->end_date) {
                            return '—'; // Or return null to leave it blank
                        }
                        $start = Carbon::parse($record->start_date);
                        $end = Carbon::parse($record->end_date);

                        $monthYear = $start->format('M Y');
                        $startFormatted = $start->format('M jS');
                        $endFormatted = $end->format('M jS');

                        return "{$monthYear} ({$startFormatted} to {$endFormatted})";
                    }),

                TextColumn::make('is_payout')
                    ->label('Amount')
                    ->searchable()
                    ->toggleable()
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        $amount = $record->payoutSubOrders->sum(function ($payoutSubOrder) {
                            $subOrder = $payoutSubOrder->subOrder;
                            if ($subOrder->payment_type != 'credit_line') {

                                if ($subOrder->payout_type === 'schedule') {
                                    // For schedule type, subtract total_commission from total_sub_order_value
                                    $commision = $subOrder->orderProducts->sum('total_commission');
                                    return ($subOrder->total_sub_order_value ?? 0) - ($commision ?? 0);
                                } else if ($subOrder->payout_type === 'full') {
                                    // For full type, use total_sub_order_value as is
                                    return $subOrder->total_sub_order_value ?? 0;
                                }
                            }

                            return 0; // Default case
                        });
                        return 'RM ' . number_format($amount, 2);
                    }),
                TextColumn::make('payout_status')
                    ->label('Status')
                    ->searchable()
                    ->badge()
                    ->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
                    ->sortable()
                    ->color(function ($state) {
                        $st = match ($state) {
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),
            ])

            ->filters([

                SelectFilter::make('payout_status')
                ->label('Status')
                ->multiple()
                ->options([
                    'pending' => 'Pending',
                    'paid' => 'Paid',
                    'failed' => 'Failed',
                ])
                ->query(function ($query, $data) {
                    if($data['values']){
                        return $query->whereIn('payout_status', $data['values'] ?? []);

                    }

                }),
                Filter::make('payout_on')
                    ->label('Payout On')
                    ->form([
                        DatePicker::make('payout_on_from')->label('Payout From')->closeOnDateSelection()->reactive(),
                        DatePicker::make('payout_on_until')->label('Payout Until')->closeOnDateSelection()->minDate(function ($get) {
                            return $get('payout_on_from') ? Carbon::parse($get('payout_on_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payout_on_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payout_on', '>=', $date),
                            )
                            ->when(
                                $data['payout_on_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payout_on', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['payout_on_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['payout_on_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['payout_on_until'])->toFormattedDateString();
                    }),
            ])
            ->actionsColumnLabel('Action')
            ->actions([
                Action::make('view')
                    ->url(fn(Payout $record): string => route('filament.pc.resources.payment-earning-records.view', [
                        'record' => $record,
                    ]))
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip('Earning Details')
                    ->color('gray')
                    ->label(false),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }




    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentEarningRecords::route('/'),
            'create' => Pages\CreatePaymentEarningRecord::route('/create'),
            'edit' => Pages\EditPaymentEarningRecord::route('/{record}/edit'),
            'view' => Pages\ViewEarning::route('/{record}'),
        ];
    }

    public static function getNextPayCycle(): string
    {
        $today = Carbon::now()->startOfDay();
        // $today = Carbon::create(2025, 7, 15)->startOfDay();

        $cycleType = 'weekly'; //getUser(auth()->user())->pcDetails->cycle_type ?? null;

        if ($cycleType === 'monthly') {
            // Next month's start and end dates
            $startDate = $today->copy()->addMonthNoOverflow()->startOfMonth();
            $endDate = $today->copy()->addMonthNoOverflow()->endOfMonth();
        
        } else {
            if ($today->day >= 15) {
                // Next cycle start from 1st of next month
                $startDate = $today->copy()->addMonthNoOverflow()->startOfMonth(); // Next month's 1st
                $endDate = $today->copy()->addMonthNoOverflow()->day(14); // 14th day of next month
            } else {
                // Next cycle start from 15th of current month
                $startDate = $today->copy()->day(15); // 15th of current month
                $endDate = $today->copy()->endOfMonth(); // Last day of current month
            }
        }
        
        // ✅ Define startDay and endDay with ordinal suffix
        $startDay = $startDate->format('jS'); // e.g., 1st
        $endDay = $endDate->format('jS');     // e.g., 30th
        
        // ✅ Create display text
        $display = "{$startDate->format('M Y')} ({$startDate->format('M')} {$startDay} to {$endDate->format('M')} {$endDay})";

        return $display;
    }
}
