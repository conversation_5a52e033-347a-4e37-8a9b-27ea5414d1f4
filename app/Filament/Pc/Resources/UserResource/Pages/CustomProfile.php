<?php

namespace App\Filament\Pc\Resources\UserResource\Pages;

use App\Filament\Pc\Resources\UserResource;
use App\Models\User;
use Filament\Facades\Filament;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CustomProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static string $resource = UserResource::class;

    protected static string $view = 'filament.pc.resources.user-resource.pages.custom-profile';

    public $company_registration_certificate = null;

    public User $user;

    public function mount(): void
    {
        $this->user = Filament::auth()->user();
        $this->company_registration_certificate = [$this->user->company_registration_certificate];
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            FileUpload::make('company_registration_certificate')
                ->afterStateUpdated(function (Get $get, $state) {
                    if ($state) {
                        // Get the current user
                        $user = $this->user;

                        // Generate a unique filename
                        $uniqueName = Str::random(40);

                        // Extract the file extension
                        $fileExtension = $state->getClientOriginalExtension();

                        // Combine the unique filename and file extension
                        $filename = $uniqueName.'.'.$fileExtension;

                        // Define the storage path
                        $storagePath = 'public/company-registration-certificate'.$user->id.'/';

                        // Ensure the directory exists
                        Storage::makeDirectory($storagePath);

                        // Store the file in the storage path with the new name
                        $filePath = $state->storeAs($storagePath, $filename);

                        // Save the file path to the user's record
                        $user->company_registration_certificate = $filePath;
                        $user->save();

                        // Update the local property
                        $this->company_registration_certificate = [$filePath];
                    }
                }),
        ]);
    }
}
