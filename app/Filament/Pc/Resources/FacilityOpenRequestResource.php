<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\FacilityOpenRequestResource\Pages;
use App\Filament\Pc\Resources\FacilityOpenRequestResource\RelationManagers;
use App\Models\ClinicPharmaSupplier;
use App\Models\User;
use Filament\Tables\Actions\Action;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Validator;

class FacilityOpenRequestResource extends Resource
{
    protected static ?string $model = ClinicPharmaSupplier::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    // protected static ?string $navigationGroup = 'Requests';
    // protected static ?string $navigationLabel = 'Open Account Requests';
    protected static bool $shouldRegisterNavigation = false;



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(ClinicPharmaSupplier::where('is_open_account', true)->where('pc_id', getUser(auth()->user())->id))
            ->emptyStateHeading('No Open Account Request records found')
            ->columns([
                TextColumn::make('clinicDetail.name')->searchable()->toggleable()
                    ->label('Facilities')
                    ->getStateUsing(function ($record) {
                        return $record->clinicDetail->name ?? 'N/A';
                    }),
                TextInputColumn::make('account_number')->searchable()->toggleable()
                    ->label('Account Number')->placeholder('Account Number')
                    ->disabled(fn($record) => $record->status === 'approved')
                    ->getStateUsing(function ($record) {
                        return $record->account_number;
                    }),
                TextColumn::make('status')
                    ->searchable()
                    ->label('Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'approved' => 'bi-patch-check-fill',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            'approved' => 'rgba(0, 70, 104, 1)',
                            default => 'secondary',
                        };
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light yellow for pending
                            'rejected' => ['style' => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;'], // light red for rejected
                            'approved' => ['style' => 'background-color:rgba(243, 251, 255, 1); border: 1px solid rgba(206, 237, 253, 1); border-radius: 6px; color:rgba(0, 70, 104, 1); padding: 4px 8px; width: fit-content;'], // light green for approved
                            default => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light gray for unknown
                        };
                    }),
            ])->actionsColumnLabel('Action')
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ])
                    ->label('Status'),
                SelectFilter::make('clinic_id')
                    ->relationship('clinicDetail', 'name')
                    ->searchable()
                    ->options(ClinicPharmaSupplier::where('pc_id', auth()->user()->id)
                        ->get()
                        ->pluck('clinicDetail.name', 'clinic_id')
                        ->toArray())
                    ->label('Facility'),
            ])
            ->actions([
                Action::make('approve')
                    ->label('')
                    ->tooltip('Approve')
                    ->icon('heroicon-m-check')->size('sm')->iconButton()
                    ->color('success')
                    ->extraAttributes(['class' => 'border-2 rounded-lg'])
                    ->action(function ($record) {
                        $validator = Validator::make(['account_number' => $record->account_number], [
                            'account_number' => ['max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/']
                        ]);

                        if ($validator->fails()) {
                            Notification::make()
                                // ->title('Validation Error')
                                ->title($validator->errors()->first('account_number'))
                                ->danger()
                                ->send();
                            return;
                        }

                        $oldValues = [
                            'status' => $record->status,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];

                        $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->user()->id,
                            'reject_by' => null
                        ]);

                        $newValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                        ];

                        // Log activity for approving facility
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('facility_approval')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("The facility's open account request of " . ($record->clinicDetail->name ?? '') . " has been approved");

                        Notification::make()
                            ->title('Facility Approved')
                            ->success()
                            ->send();
                    })
                    ->visible(function ($record) {
                        return $record->status !== 'approved';
                    }),
                // ->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                //     $action->modalHeading('Are you sure you want to approve this facility?');
                //     return $action;
                // }) ,
                Action::make('reject')
                    ->label('')
                    ->tooltip('Reject')
                    ->icon('heroicon-m-x-mark')->size('sm')->iconButton()
                    ->color('danger')
                    ->extraAttributes(['class' => 'border-2 border-danger rounded-lg', 'style' => 'margin-left: inherit;'])
                    ->modalSubmitAction(
                        fn($action) =>
                        $action->label('Save')->color('danger')
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('')
                            ->validationMessages([
                                'required' => "The reason field is required.",
                            ])
                            ->rules(['required'])
                    ])
                    ->action(function ($data, $record) {
                        $oldValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                        ];


                        $record->update([
                            'status' => 'rejected',
                            'reject_by' => auth()->user()->id,
                            'approved_by' => null,
                            'reject_reason' => $data['reason']

                        ]);

                        $newValues = [
                            'status' => $record->status,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                            'reject_reason' => $record->reject_reason,
                        ];

                        // Log activity for rejecting facility
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('facility_rejection')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("The facility's open account request of " . ($record->clinicDetail->name ?? 'N/A') . " has been rejected");


                        Notification::make()
                            ->title('Facility Rejected')
                            ->success()
                            ->send();
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to reject this facility?');
                        return $action;
                    })->visible(function ($record) {
                        return $record->status !== 'rejected';
                    }),


            ])
            // ->bulkActions([
            //     Tables\Actions\BulkAction::make('approve')
            //         ->label('Accept')
            //         ->tooltip('Accept')
            //         ->icon('heroicon-o-check-circle')
            //         ->color('success')
            //         ->action(function ($records) {
            //             $records->each(function ($record) {
            //                 if (empty($record->account_number)) {
            //                     Notification::make()
            //                         // ->title('Account Number Required')
            //                         ->title('Please enter the account number before approving the facility.')
            //                         ->danger()
            //                         ->send();
            //                     return;
            //                 }

            //                 $oldValues = [
            //                     'status' => $record->status,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                 ];

            //                 $record->update(['status' => 'approved', 'approved_by' => auth()->user()->id, 'reject_by' => null]);

            //                 $newValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                 ];

            //                 // Log activity for each approved facility
            //                 activity()
            //                     ->causedBy(auth()->user())
            //                     ->useLog('facility_bulk_approval')
            //                     ->performedOn($record)
            //                     ->withProperties([
            //                         'old' => $oldValues,
            //                         'attributes' => $newValues,
            //                     ])
            //                     ->log("The facility's open account request of " . ($record->clinicDetail->name ?? 'N/A') . " have been approved");
            //             });
            //             Notification::make()
            //                 // ->title('Request Approved')
            //                 ->title('The selected request have been approved successfully.')
            //                 ->success()
            //                 ->send();
            //         })->after(function () {
            //             redirect(static::getUrl('index'));
            //         })
            //         ->requiresConfirmation(),
            //     Tables\Actions\BulkAction::make('reject')
            //         ->label('Reject')
            //         ->tooltip('Reject')
            //         ->icon('heroicon-o-x-circle')
            //         ->color('danger')
            //         ->action(function ($records) {
            //             $records->each(function ($record) {

            //                 $oldValues = [
            //                     'status' => $record->status,
            //                     'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
            //                     'reject_reason' => $record->reject_reason,
            //                 ];
            //                 $record->update(['status' => 'rejected', 'reject_by' => auth()->user()->id, 'approved_by' => null]);

            //                 $newValues = [
            //                     'status' => $record->status,
            //                     'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
            //                     'reject_reason' => $record->reject_reason,
            //                 ];
            //                 // Log activity for each rejected facility
            //                 activity()
            //                     ->causedBy(auth()->user())
            //                     ->useLog('facility_bulk_rejection')
            //                     ->performedOn($record)
            //                     ->withProperties([
            //                         'old' => $oldValues,
            //                         'attributes' => $newValues,
            //                     ])
            //                     ->log("The facility's open account request of " . ($record->clinicDetail->name ?? 'N/A') . " have been rejected");
            //             });
            //             Notification::make()
            //                 // ->title('Request Rejected')
            //                 ->title('The selected request have been rejected successfully.')
            //                 ->success()
            //                 ->send();
            //         })->after(function () {
            //             redirect(static::getUrl('index'));
            //         })
            //         ->requiresConfirmation(),
            // ])
            ;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFacilityOpenRequests::route('/'),
            'create' => Pages\CreateFacilityOpenRequest::route('/create'),
            // 'edit' => Pages\EditFacilityOpenRequest::route('/{record}/edit'),
        ];
    }
}
