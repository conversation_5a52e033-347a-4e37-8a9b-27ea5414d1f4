<?php

namespace App\Filament\Pc\Resources\OutStandingPaymentResource\Pages;

use App\Filament\Pc\Resources\OutStandingPaymentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOutStandingPayments extends ListRecords
{
    protected static string $resource = OutStandingPaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Payment Records',
            // OutStandingPaymentResource::getUrl() => 'Outstanding Payments',
            // 2 => 'List',
        ];
    }

    public function getTitle(): string
    {
        return 'Payments';
    }
}
