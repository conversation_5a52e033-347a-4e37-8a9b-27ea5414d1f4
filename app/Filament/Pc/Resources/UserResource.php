<?php

namespace App\Filament\Pc\Resources;

use App\Filament\Pc\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static bool $shouldRegisterNavigation = false;

    public function mount(): void
    {
        $this->record = auth()->user();
        info(config('app.url'));
    }

    public static function form(Form $form): Form
    {
        info(config('app.url'));

        return $form
            ->schema([
                FileUpload::make('company_registration_certificate'),
            ]);
    }

    // public static function table(Table $table): Table
    // {
    //     return $table
    //         ->columns([
    //             //
    //         ])
    //         ->filters([
    //             //
    //         ])
    //         ->actions([
    //             Tables\Actions\EditAction::make(),
    //         ])
    //         ->bulkActions([
    //             Tables\Actions\BulkActionGroup::make([
    //                 Tables\Actions\DeleteBulkAction::make(),
    //             ]),
    //         ]);
    // }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            // 'index' => Pages\ListUsers::route('/'),
            // 'create' => Pages\CreateUser::route('/create'),
            'index' => Pages\EditUser::route('/{record}/profile'),
            // 'profile' => Pages\CustomProfile::route('/{record}/profile'),
        ];
    }

    // public static function canViewAny(): bool
    // {
    //     return false;
    // }
}
