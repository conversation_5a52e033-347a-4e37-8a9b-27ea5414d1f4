<?php

namespace App\Filament\Pc\Resources\FacilityOpenRequestResource\Pages;

use App\Filament\Pc\Resources\FacilityOpenRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditFacilityOpenRequest extends EditRecord
{
    protected static string $resource = FacilityOpenRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
