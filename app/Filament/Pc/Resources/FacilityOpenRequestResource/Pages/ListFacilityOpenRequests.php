<?php

namespace App\Filament\Pc\Resources\FacilityOpenRequestResource\Pages;

use App\Filament\Pc\Resources\ClinicDetailResource;
use App\Filament\Pc\Resources\FacilityOpenRequestResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;

class ListFacilityOpenRequests extends ListRecords
{
    protected static string $resource = FacilityOpenRequestResource::class;

    public static function canAccess(array $parameters = []): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return auth()->user()->hasRole('Super Admin') || $isPharmaceuticalCompany ||  $user->can('facility_open account request');
    }
    public function getTitle(): string
    {
        return 'Open Account Request';  // Custom title for the page
    }

    public function getBreadcrumbs(): array
    {
        return [
            route('filament.pc.resources.facilities.index') => 'Facilities',
            $this->getResource()::getUrl('index') => "Open Account Request",
        ];
    }
     protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicDetailResource::getUrl('index')),

        ];
    }
}
