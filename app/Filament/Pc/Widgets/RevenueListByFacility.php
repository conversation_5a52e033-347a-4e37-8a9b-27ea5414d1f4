<?php

namespace App\Filament\Pc\Widgets;

use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Carbon;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use Filament\Forms\Components\ToggleButtons;

class RevenueListByFacility extends ApexChartWidget
{
    protected static ?string $chartId = 'RevenueListByFacility';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Revenue';
    protected static ?string $pollingInterval = '10s';
    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('facility_id')
                ->label('By Facility')
                ->options(
                    fn() => User::query()
                        ->whereHas('clinicData')
                        ->with('clinicData:id,user_id,clinic_name')
                        ->get()
                        ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                        ->mapWithKeys(fn($user) => [
                            $user->id => $user->clinicData->clinic_name,
                        ])
                        ->sortBy(fn($name) => $name)
                        ->toArray()
                )
                ->searchable()
                ->preload()
                ->placeholder('By Facility')
                ->reactive(),

            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                // ->reactive(),
                ->required(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->reactive()
                ->required(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->reactive()
                ->required(),

        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] ?? 'year';
        $facilityId = $filters['facility_id'] ?? null;

        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                break;
            default:
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                break;
        }

        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;
        
        $data = DB::table('sub_orders as so')
            ->join('orders', 'orders.id', '=', 'so.order_id')
            ->join('clinic_details', 'clinic_details.user_id', '=', 'orders.user_id')
            ->join(DB::raw('(
                 SELECT count(id) as products, sub_order_id, SUM(total_commission) AS total_commission
                 FROM order_products
                 GROUP BY sub_order_id
             ) as op_comm'), 'op_comm.sub_order_id', '=', 'so.id')
            ->when($facilityId, fn($query) => $query->where('orders.user_id', $facilityId))
            ->whereBetween('so.created_at', [$start, $end])
            ->where('so.user_id',$pcUserId)
            ->whereIn('so.status', ['accepted', 'delivered'])
            ->selectRaw("
                 orders.user_id as facility_id,
                 clinic_details.clinic_name as facility_name,
                 COUNT(DISTINCT so.id) AS total_sub_orders,
                 SUM(op_comm.products) AS total_order_products,
                 COALESCE(SUM(so.total_amount), 0) AS total_amount,
                 COALESCE(SUM(op_comm.total_commission), 0) AS total_commission,
                 COALESCE(SUM(so.total_amount), 0) - COALESCE(SUM(op_comm.total_commission), 0) AS total_earning
             ")
            ->groupBy('facility_id', 'facility_name')
            ->orderBy('facility_name')
            ->pluck('total_earning', 'facility_name');

        $categories = $data->keys()->toArray(); // Facility names on X-axis
        $seriesData = $data->values()->map(fn($value) => (float) $value)->toArray(); // Earnings on Y-axis
        return [
            'chart' => [
                'type' => 'bar',
                'height' => 300,
                'zoom' => [
                    'enabled' => false,
                ],
                'toolbar' => [
                    'show' => false,
                ],
            ],
            'series' => [
                [
                    'name' => 'Earning',
                    'data' => $seriesData,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                //  'title' => [
                //      'text' => 'Earning',
                //  ],
            ],
            'colors' => ['#004668'],
            'stroke' => [
                'curve' => 'straight',
                'width' => 2,
            ],
            'plotOptions' => [
                'bar' => [
                    'columnWidth' => '10%', // Control stick width
                    'borderRadius' => 2,
                ],
            ],
            // 'noData' => [
            //     'text' => 'No data available',
            //     'align' => 'center',
            //     'verticalAlign' => 'middle',
            //     'style' => [
            //         'color' => '#999',
            //         'fontSize' => '16px',
            //     ],
            // ],
        ];
    }


    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return 'RM ' + val.toLocaleString();
                        }
                    }
                },
            }
        JS);
    }
}
