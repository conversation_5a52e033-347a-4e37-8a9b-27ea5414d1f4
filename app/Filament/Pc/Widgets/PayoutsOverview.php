<?php

namespace App\Filament\Pc\Widgets;

use App\Models\Payout;
use App\Models\Product;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;


class PayoutsOverview extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static ?string $navigationIcon = null;
    
    protected static ?string $label = 'Payouts';

    protected int | string | array $rowSpan = 'full';
    
    protected static ?string $pollingInterval = '10s';

    protected function getHeaderActions(): array
    {
        return [
            FilterAction::make()
                ->form([
                    DatePicker::make('startDate'),
                    DatePicker::make('endDate'),
                ])
                ->label('Filter by Date')
                ->submitActionLabel('Apply'),
        ];
    }

    protected function getStats(): array
    {

        $startDate = $this->filters['startDate'] ?? null;
        $endDate = $this->filters['endDate'] ?? null;

        $query = Payout::query()
        ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
        ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id');

     if ($startDate && $endDate) {
            $query->whereBetween('payouts.created_at', [$startDate, $endDate]);
        }
        $payout = $query->selectRaw('
                SUM(sub_orders.total_amount) AS total_payout,
                SUM(CASE WHEN payouts.is_payout = true THEN sub_orders.total_amount ELSE 0 END) AS processed_payout,
                SUM(CASE WHEN payouts.is_payout = false THEN sub_orders.total_amount ELSE 0 END) AS pending_payout
            ')
            ->first();
        return [
            Stat::make('Total Revenue', 'RM'.number_format($payout->total_payout, 2))
                ->icon('heroicon-o-currency-dollar')
                ->extraAttributes([
                    'style' => 'background-color: #E5F7FF;',
                ])
                ->iconPosition('start')
                ->iconBackgroundColor('info')
                ->iconColor('light'),

            Stat::make('Payout Pending',  'RM'.number_format($payout->pending_payout, 2))
                ->icon('heroicon-o-clock')
                ->extraAttributes([
                    'style' => 'background-color: #EDE5FF;',
                ])
                ->iconPosition('start')
                ->iconBackgroundColor('warning')
                ->iconColor('#ffffff'),

            Stat::make('Payout Processed', 'RM'.number_format($payout->processed_payout, 2))
                ->icon('heroicon-o-square-3-stack-3d')
                ->extraAttributes([
                    'style' => 'background-color: #E5FFEF;',
                ])
                ->iconPosition('start')
                ->iconBackgroundColor('info')
                ->iconColor('#ffffff'),

        ];
    }
}
