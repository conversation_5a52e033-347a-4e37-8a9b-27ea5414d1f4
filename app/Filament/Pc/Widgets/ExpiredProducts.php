<?php

namespace App\Filament\Pc\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductRelation;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;

class ExpiredProducts extends BaseWidget
{
    protected static ?string $model = ProductRelation::class;
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Expired Products (Latest 10)';


    public function table(Table $table): Table
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;
        return $table
            ->query(
                // Product::whereHas('productData', function ($query) use ($pcUserId) {
                //         $query->where('user_id', $pcUserId);
                //     })
                //     ->whereHas('productData.productRelationStock', function ($stockQuery) {
                //         $stockQuery->where(function ($q) {
                //             $q->where('is_batch_wise_stock', false)
                //                 ->where('expiry_date', '<', now());
                //         })
                //         ->orWhere(function ($q) {
                //             $q->where('is_batch_wise_stock', true)
                //                 ->whereDoesntHave('productsBatch', function ($batchQuery) {
                //                     $batchQuery->where('expiry_date', '>=', now());
                //                 });
                //         });
                //     })
                //     ->limit(10)

                Product::with([
                    'productData.productRelationStock.productsBatch',
                ])
                ->whereNull('deleted_at') // Products not deleted
                ->whereHas('productData', function ($query) use ($pcUserId) {
                    $query->where('user_id', $pcUserId)
                        ->whereNull('deleted_at')
                        ->where('is_rejected', false)
                        ->whereHas('productRelationStock', function ($stockQuery) {
                            $stockQuery->where(function ($condition) {
                                $condition->where(function ($nonBatch) {
                                    // Case 1: Non-batch stock expired
                                    $nonBatch->where('is_batch_wise_stock', false)
                                             ->where('expiry_date', '<', now());
                                })
                                ->orWhere(function ($batchWise) {
                                    // Case 2: Batch-wise stock
                                    $batchWise->where('is_batch_wise_stock', true)
                                              ->whereDoesntHave('productsBatch', function ($batchQuery) {
                                                  // No batch with future expiry
                                                  $batchQuery->where('expiry_date', '>', now());
                                              })
                                              ->whereHas('productsBatch', function ($batchQuery) {
                                                  // Ensure at least one batch exists
                                                  $batchQuery->whereNotNull('expiry_date');
                                              });
                                });
                            });
                        });
                })
                ->orderBy('id', 'asc')
                ->limit(10)
            )
            // ->filters([
            //     Filter::make('expiry_filter')
            //         ->form([
            //             DatePicker::make('from')
            //                 ->label('Start Date')
            //                 ->default(now()->subMonths(3)->startOfMonth()),
            //             DatePicker::make('until')
            //                 ->label('End Date')
            //                 ->default(now()),
            //         ])
            //         ->query(function ($query, array $data) {
            //             $from = $data['from'] ?? null;
            //             $until = $data['until'] ?? null;

            //             if ($from && $until) {
            //                 $query->whereHas('productData.productRelationStock', function ($stockQuery) use ($from, $until) {
            //                     $stockQuery->where(function ($q) use ($from, $until) {
            //                         // Non batch-wise stock with expiry_date in range
            //                         $q->where('is_batch_wise_stock', false)
            //                             ->whereBetween('expiry_date', [$from, $until]);
            //                     })->orWhere(function ($q) use ($from, $until) {
            //                         // Batch-wise stock with any batch expiry_date in range
            //                         $q->where('is_batch_wise_stock', true)
            //                             ->whereHas('productsBatch', function ($batchQuery) use ($from, $until) {
            //                                 $batchQuery->whereBetween('expiry_date', [$from, $until]);
            //                             });
            //                     });
            //                 });
            //             }
            //         })
            //         ->indicateUsing(function (array $data): ?string {
            //             $from = $data['from'] ?? null;
            //             // dd(date($from );
            //             $until = $data['until'] ?? null;

            //             if ($from && $until) {
            //                 return "Range : From {$from} to {$until}";
            //             }

            //             if ($from) {
            //                 return "Range : From {$from}";
            //             }

            //             if ($until) {
            //                 return "Until {$until}";
            //             }

            //             return '';
            //         }),
            // ])
            ->columns([
                TextColumn::make('id')->prefix('#')
                    ->label('Product ID')
                    ->color('primary')
                    ->url(fn($record): string => route('filament.pc.resources.products.view', ['record' => $record->id])),

                TextColumn::make('name')
                    ->label('Product Name'),

                // TextColumn::make('is_batch_wise_stock')
                //     ->label('By Batch')
                //     ->getStateUsing(function ($record) use ($pcUserId) {

                //         $stock = $record->productDataForPc($pcUserId)?->productRelationStock;

                //         if (!$stock->is_batch_wise_stock) {
                //             return "No";
                //         }
                //         return "Yes";
                //     }),

                TextColumn::make('expiry_date')
                    ->label('Expiry Date')
                    ->getStateUsing(function ($record) use ($pcUserId) {

                        $stock = $record->productDataForPc($pcUserId)?->productRelationStock;

                        if (!$stock) {
                            return '';
                        }

                        if (!$stock->is_batch_wise_stock) {
                            return $stock->expiry_date ? date('Y-m-d', strtotime($stock->expiry_date)) : '';
                        }

                        $batches = $stock->productsBatch;

                        if ($batches->isEmpty()) {
                            return '';
                        }

                        $latestExpiry = $batches->max('expiry_date');
                        return $latestExpiry ? date('Y-m-d', strtotime($latestExpiry)) : '';
                    })->formatStateUsing(function ($state) use ($authUser) {
                        $format = $authUser->pcDetails->date_format ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    }),
            ])

            ->paginated(false)
            ->emptyStateHeading('No Products Available')
            ->emptyStateDescription('Expired products will appear here.');
    }
}
