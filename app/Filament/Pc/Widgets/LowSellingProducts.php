<?php

namespace App\Filament\Pc\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductRelation;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;


class LowSellingProducts extends BaseWidget
{
    protected static ?string $model = ClinicPharmaSupplier::class;
    protected static ?string $heading = 'Low/Out of Stock Products';
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                OrderProduct::query()
                    ->select(
                        'products.id',
                        'products.name as product_name',
                        DB::raw('COUNT(order_products.id) as orders_count'),
                    )
                    ->join('products', 'products.id', '=', 'order_products.product_id')
                    ->groupBy('products.id', 'products.name')
                    ->orderByDesc(DB::raw('SUM(order_products.quantity)'))
                    ->limit(10)
                    
            )
            ->columns([
                TextColumn::make('id')->prefix('#')
                    ->label('Product ID'),

                TextColumn::make('product_name')
                    ->label('Product Name'),

                // TextColumn::make('price')
                //     ->label('Price')
                //     ->searchable(),

                // TextColumn::make('stock')
                //     ->label('Stock')
                //     ->searchable(),

                TextColumn::make('orders_count')
                    ->label('Order Count'),

            ])
            ->filters([
                Filter::make('date_filter')
                    ->label('Date Filter')
                    ->form([
                        Select::make('range')
                            ->label('Select Range')
                            ->options([
                                'this_month' => 'This Month',
                                'this_year' => 'This Year',
                                'custom' => 'Custom Range',
                            ])
                            ->required()
                            ->default('this_month'),
    
                        DatePicker::make('from')
                            ->label('From')
                            ->visible(fn ($get) => $get('range') === 'custom'),
    
                        DatePicker::make('until')
                            ->label('Until')
                            ->visible(fn ($get) => $get('range') === 'custom'),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['range'] === 'this_month', function ($query) {
                                $query->whereBetween('order_products.created_at', [
                                    now()->startOfMonth()->toDateString(),
                                    now()->endOfMonth()->toDateString(),
                                ]);
                            })
                            ->when($data['range'] === 'this_year', function ($query) {
                                $query->whereBetween('order_products.created_at', [
                                    now()->startOfYear()->toDateString(),
                                    now()->endOfYear()->toDateString(),
                                ]);
                            })
                            ->when($data['range'] === 'custom', function ($query) use ($data) {
                                $query
                                    ->when($data['from'], fn ($q) => $q->whereDate('order_products.created_at', '>=', $data['from']))
                                    ->when($data['until'], fn ($q) => $q->whereDate('order_products.created_at', '<=', $data['until']));
                            });
                    }),
            ])
            ->paginated(false)
            ->emptyStateHeading('No accounts pending verification')
            ->emptyStateDescription('When clinics or pharmacy suppliers register new account numbers, they will appear here for verification.');
    }
}