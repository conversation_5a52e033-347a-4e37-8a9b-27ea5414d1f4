<?php

namespace App\Filament\Pc\Widgets;

use Filament\Forms;
use App\Models\User;
use App\Models\Order;
use App\Models\PcDetail;
use App\Models\SubOrder;
use Filament\Tables\Table;
use App\Models\ClinicDetail;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\View;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\ButtonAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Pc\Resources\OrderResource;
use Filament\Widgets\TableWidget as BaseWidget;

class RecentOrders extends BaseWidget
{
    protected static ?string $label = 'Recent Orders';
    protected static ?string $model = SubOrder::class;
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;
        return $table

            // ->headerActions([
            //     // Add custom action to the header
            //     ButtonAction::make('view_all_orders')
            //         ->label('View Recent Orders')
            //         ->icon('heroicon-o-arrow-right')
            //         ->url(OrderResource::getUrl() . '?tableSortColumn=order.created_at&tableSortDirection=desc')
            //         ->color('primary'),
            //     // ->extraAttributes(['target' => '_blank']),
            // ])
            ->headerActions([
                // Add custom action to the header
                ButtonAction::make('view_all_orders')
                    ->label('View Recent Orders')
                    ->icon('heroicon-o-arrow-right')
                    ->url(OrderResource::getUrl() . '?tableSortColumn=order.created_at&tableSortDirection=desc')
                    ->color('primary'),
                // ->extraAttributes(['target' => '_blank']),
            ])
            ->query(
                SubOrder::with(['order', 'user', 'orderProducts', 'orderProducts.product'])
                    ->where('sub_orders.user_id', $pcUserId)
                    ->withCount([
                        'orderProducts as order_products_count' => function (Builder $query) {
                            $query->select(DB::raw('COALESCE(COUNT(*), 0)'));
                        },
                    ])
                    // ->latest()
                    ->join('orders', 'orders.id', '=', 'sub_orders.order_id')
                    ->orderBy('orders.created_at', 'desc')
                    ->limit(10),
            )
            ->recordUrl(fn($record): string => route('filament.pc.resources.orders.view', ['record' => $record->id]))
            ->columns([
                TextColumn::make('order.order_number')->prefix('#')->label('Order ID')
                    ->searchable()
                    ->color('primary')
                    ->url(fn($record): string => route('filament.pc.resources.orders.view', ['record' => $record->id])),
                TextColumn::make('invoice_po_number')
                    ->label('Invoice ID')
                    ->searchable()
                    ->getStateUsing(function ($record) {
                        $state = $record->invoice_po_number ?? null;
                        return filled(trim((string)$state)) ? "#{$state}" : '-';
                    }),
                TextColumn::make('order.user.clinicData.clinic_name')->label('Facility Name')->searchable(),
                TextColumn::make('order.created_at')->label('Order Date')
                    ->formatStateUsing(function ($state) {
                        $user = Filament::auth()->user();
                        $userId = $user->parent_id ?? $user->id;
                        $format = PcDetail::where('user_id', $userId)->value('date_format') ?? 'd/m/Y';
                        return getFormatedDate($state, $format);
                    }),
                TextColumn::make('payment_type')
                ->label('Order Type')
                ->formatStateUsing(function ($state) {
                         return match ($state) {
                        'pay_now'     => 'Pay Now',
                        'credit_line' => 'Credit Line',
                        'pay_later'   => 'Pay Later',
                        default       => ucfirst(str_replace('_', ' ', $state)),
                    };
                })->searchable(),
                TextColumn::make('order_products_count')->label('Items'),
                TextColumn::make('total_sub_order_value')
                    ->label('Order Total')
                    ->prefix('RM ')
                    ->formatStateUsing(function ($state) {
                        return number_format($state, 2);
                    })->searchable(),
                TextColumn::make('status')
                    // ->searchable()
                    ->label('Order Status')
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'accepted' => 'bi-patch-check-fill',
                            'cancelled' => 'bi-patch-check-fill',
                            'delivered' => 'bi-patch-check-fill',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color = config("constants.order_status.color.{$status}", '#424242');
                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),


            ])
            ->paginated(false)
            ->filters([

                SelectFilter::make('user_id')
                    ->label('Facilities')
                    ->options(function () {
                        $authUser = Filament::auth()->user();
                        $pcUserId = $authUser->parent_id ?? $authUser->id;

                        $users = User::where(function ($query) use ($pcUserId) {
                            // Users who are approved in clinic_pharma_suppliers
                            $query->whereHas('pharmaSuppliers', function ($q) use ($pcUserId) {
                                $q->where('pc_id', $pcUserId)
                                    ->where('status', 'approved');
                            });
                        })->orWhere(function ($query) use ($pcUserId) {
                            // Users who have orders in sub_orders related to the given pc_id
                            $query->whereHas('orders.subOrders', function ($q) use ($pcUserId) {
                                $q->where('user_id', $pcUserId);
                            });
                        })
                            ->whereHas('clinicData') // Only those users that have clinic data
                            ->with('clinicData:id,user_id,clinic_name')
                            ->get()
                            ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                            ->mapWithKeys(fn($user) => [
                                $user->id => $user->clinicData->clinic_name,
                            ])
                            ->sortBy(fn($name) => $name)
                            ->toArray();

                        return $users;
                    })
                    ->searchable()
                    ->preload()
                    ->placeholder('Facilities')
                    ->query(function (Builder $query, $state): Builder {
                        return $query->when(
                            $state['value'] ?? null,
                            fn($query, $userId) => $query->whereHas('order', fn($q) => $q->where('user_id', $userId))
                        );
                    }),

                SelectFilter::make('status')
                    ->label('Order Status')
                    ->options([
                        'pending' => 'Pending',
                        'accepted' => 'Accepted',
                        'rejected' => 'Rejected',
                        'in_transit' => 'In Transit',
                        'delivered' => 'Delivered',
                        'cancelled' => 'Cancelled',
                    ])
                    ->placeholder('All Status')
                    ->query(function (Builder $query, $state): Builder {
                        return $query->when(
                            $state['value'] ?? null,
                            fn($query, $status) => $query->where('sub_orders.status', $status)
                        );
                    }),

            ])

            ->emptyStateHeading('No Orders Available')
            ->emptyStateDescription('Recent Order will appear here.');
    }
}
