<?php

namespace App\Filament\Pc\Widgets;

use App\Filament\Exports\OrderExporter;
use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use App\Models\SubOrder;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Filters\SelectFilter;

class RevenueList extends BaseWidget
{
    protected static ?string $heading  = 'Revenue List';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $pollingInterval = '10s';

    public function getTableRecordKey(mixed $record): string
    {
        return (string) $record->product_id; // Ensure you're using the correct key
    }
    public function table(Tables\Table $table): Tables\Table
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;

        $subQuery = DB::table('order_products')
            ->select('sub_order_id', DB::raw('SUM(total_commission) as total_commission'), DB::raw('count(id) as total_items'))
            ->groupBy('sub_order_id');

        return $table
            ->query(
                fn() =>
                SubOrder::query()
                    ->leftJoinSub($subQuery, 'op_comm', 'op_comm.sub_order_id', '=', 'sub_orders.id')
                    ->where('sub_orders.user_id', $pcUserId)
                    ->whereIn('sub_orders.status', ['accepted', 'delivered'])
                    ->selectRaw("
                        TO_CHAR(sub_orders.created_at, 'Month') AS month,
                        COUNT(DISTINCT sub_orders.id) AS total_orders,
                        COALESCE(sum(op_comm.total_items), 0) as item_sold, 
                        COALESCE(SUM(sub_orders.total_sub_order_value), 0) as total_revenue,
                        COALESCE(SUM(op_comm.total_commission), 0) as comm, 
                        COALESCE(SUM(sub_orders.total_sub_order_value), 0) - COALESCE(SUM(op_comm.total_commission), 0) AS total_earning 
                    ")
                    ->groupByRaw("TO_CHAR(sub_orders.created_at, 'Month'), DATE_PART('month', sub_orders.created_at)")
                    ->orderByRaw("DATE_PART('month', sub_orders.created_at)")
            )
            // ->headerActions([
            //     ExportAction::make()->exporter(OrderExporter::class)
            // ])
            ->columns([
                TextColumn::make('month')
                    ->label('Month'),

                TextColumn::make('item_sold')
                    ->label('Item Sold'),

                TextColumn::make('total_orders')
                    ->label('Total Orders'),

                // TextColumn::make('comm')
                //     ->label('Comm')
                //     ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($state, 2)),

                 TextColumn::make('total_revenue')
                    ->label('Total Revenue')
                    ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($state, 2)),
                TextColumn::make('total_earning')
                    ->label('Total Earning')
                    ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($state, 2)),
            ])
            ->filters([
                // Filter::make('year')
                //     ->form([
                //         Select::make('year')
                //             ->label('Year')
                //             ->options(
                //                 collect(range(now()->year, now()->year - 15))
                //                     ->mapWithKeys(fn($year) => [$year => $year])
                //                     ->toArray()
                //             )
                //             ->searchable()
                //             ->placeholder('Select Year')
                //             ->default(now()->year)
                //             ->afterStateUpdated(function ($state, $set) {
                //                 // This will prevent the value from being empty.
                //                 if (empty($state)) {
                //                     $set('year', now()->year); // Set it back to the default value if cleared.
                //                 }
                //             }),

                //     ])
                //     ->query(function (Builder $query, array $data): Builder {
                //         return $query->when(
                //             $data['year'] ?? null,
                //             fn(Builder $query, $year) => $query->whereYear('sub_orders.created_at', $year)
                //         );
                //     }),

                SelectFilter::make('year')
                    ->label('Year')
                    ->options(
                        collect(range(now()->year, now()->year - 15))
                            ->mapWithKeys(fn($year) => [$year => $year])
                            ->toArray()
                    )
                    ->searchable()
                    ->placeholder('Select Year')
                    ->default(now()->year)
                    ->query(function (Builder $query, $state): Builder {
                        // Force fallback to current year if cleared
                        $year = $state ?? now()->year;
                        return $query->whereYear('sub_orders.created_at', $year);
                    }),
            ])
            ->paginated(false)
            ->emptyStateHeading('No data available')
            ->emptyStateDescription('No product sales data available for the selected year.');
    }
}
