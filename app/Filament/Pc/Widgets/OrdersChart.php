<?php

namespace App\Filament\Pc\Widgets;

use App\Models\Order;
use App\Models\SubOrder;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ToggleButtons;
use Filament\Support\RawJs;
use Illuminate\Support\Js;

class OrdersChart extends ApexChartWidget
{
    protected static ?string $chartId = 'OrdersChart';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Orders';

    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('facility_id')
                ->label('Facilities')
                ->options(function () {
                    $authUser = Filament::auth()->user();
                    $pcUserId = $authUser->parent_id ?? $authUser->id;

                    $users = User::where(function ($query) use ($pcUserId) {
                        // Users who are approved in clinic_pharma_suppliers
                        $query->whereHas('pharmaSuppliers', function ($q) use ($pcUserId) {
                            $q->where('pc_id', $pcUserId)
                                ->where('status', 'approved');
                        });
                    })->orWhere(function ($query) use ($pcUserId) {
                        // Users who have orders in sub_orders related to the given pc_id
                        $query->whereHas('orders.subOrders', function ($q) use ($pcUserId) {
                            $q->where('user_id', $pcUserId);
                        });
                    })
                        ->whereHas('clinicData') // Only those users that have clinic data
                        ->with('clinicData:id,user_id,clinic_name')
                        ->get()
                        ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                        ->mapWithKeys(fn($user) => [
                            $user->id => $user->clinicData->clinic_name,
                        ])
                        ->sortBy(fn($name) => $name)
                        ->toArray();

                    return $users;
                })
                ->searchable()
                ->preload()
                ->placeholder('Facilities')
                ->multiple()
                ->maxItems(10)
                ->reactive(),

            Select::make('range')
                ->options([
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->reactive()
                ->required(),

            DatePicker::make('date_start')
                ->label('Start Date')
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            DatePicker::make('date_end')
                ->label('End Date')
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start'))
                ->maxDate(now())
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            ToggleButtons::make('chart_type')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'bar'  => '📊',
                    'line' => '📈',
                ])
                ->default('bar')
                ->reactive()
        ];
    }

    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $facilityIds = $filters['facility_id'] ?? null;
        $chartType = $filters['chart_type'] ?? 'line';

        switch ($range) {
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
        }

        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;

        $isMultiFacility = !empty($facilityIds);

         // Use MM for monthly group, or YYYY-MM-DD for daily
        // $groupByFormat = $range === 'year' ? 'MM' : 'YYYY-MM-DD';

        //     $query = DB::table('sub_orders')
        //         ->join('orders', 'sub_orders.order_id', '=', 'orders.id')
        //         ->whereBetween('sub_orders.created_at', [$start, $end])
        //         ->where('sub_orders.user_id', $pcUserId);

        //     if ($isMultiFacility) {
        //         $query->leftJoin('clinic_details', 'orders.user_id', '=', 'clinic_details.user_id')
        //             ->whereIn('orders.user_id', $facilityIds)
        //             ->selectRaw("
        //         orders.user_id,
        //         clinic_details.clinic_name as facility_name,
        //         TO_CHAR(sub_orders.created_at, '{$groupByFormat}') as period,
        //         count(sub_orders.id) as total
        //     ")
        //             ->groupBy('orders.user_id', 'clinic_details.clinic_name', 'period');
        //     } else {
        //         $query->selectRaw("
        //     TO_CHAR(sub_orders.created_at, '{$groupByFormat}') as period,
        //     count(sub_orders.id) as total
        // ")
        //             ->groupBy('period');
        //     }

        //     $rawData = $query->orderBy('period')->get();

        $query = DB::table('sub_orders')
            ->join('orders', 'sub_orders.order_id', '=', 'orders.id')
            ->whereBetween('sub_orders.created_at', [$start, $end])
            ->where('sub_orders.user_id', $pcUserId);

        if ($isMultiFacility) {
            $query->leftJoin('clinic_details', 'orders.user_id', '=', 'clinic_details.user_id')
                ->whereIn('orders.user_id', $facilityIds)
                ->selectRaw("
                orders.user_id,
                clinic_details.clinic_name as facility_name,
                TO_CHAR(sub_orders.created_at, '{$groupByFormat}') as period,
                sub_orders.status,
                COUNT(sub_orders.id) as total
            ")
                ->groupBy('orders.user_id', 'clinic_details.clinic_name', 'period', 'sub_orders.status');
        } else {
            $query->selectRaw("
            TO_CHAR(sub_orders.created_at, '{$groupByFormat}') as period,
            sub_orders.status,
            COUNT(sub_orders.id) as total
        ")
                ->groupBy('period', 'sub_orders.status');
        }

        $rawData = $query->orderBy('period')->get();

        // Dynamic categories and keys based on range and custom date logic
        $categories = [];
        $keys = [];

        // if ($range === 'year') {
        //     // Create keys like '01', '02' and labels like 'Jan', 'Feb'
        //     $months = collect(range(1, 12))->mapWithKeys(function ($month) {
        //         $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT); // '01'
        //         $monthLabel = Carbon::create()->month($month)->format('M'); // 'Jan'
        //         return [$monthKey => $monthLabel];
        //     });

        //     $categories = $months->values()->toArray();  // For chart X-axis
        //     $keys = $months->keys();                     // For series data lookup
        // } else {
        //     // Daily range
        //     $startDate = Carbon::parse($start);
        //     $endDate = Carbon::parse($end);

        //     $dates = collect();
        //     while ($startDate->lte($endDate)) {
        //         $formatted = $startDate->format('Y-m-d');
        //         $dates->push($formatted);
        //         $startDate->addDay();
        //     }

        //     $categories = $dates->toArray();
        //     $keys = $dates;
        // }

        if ($range === 'year') {
            // Monthly data for current year
            $currentYear = now()->year;
            $months = [
                '01' => 'Jan', '02' => 'Feb', '03' => 'Mar', '04' => 'Apr',
                '05' => 'May', '06' => 'Jun', '07' => 'Jul', '08' => 'Aug',
                '09' => 'Sep', '10' => 'Oct', '11' => 'Nov', '12' => 'Dec',
            ];

            foreach ($months as $num => $name) {
                $categories[] = $name . ' ' . $currentYear;
                $keys[] = $num;
            }
        } elseif ($range === 'custom') {
            $daysDiff = $start->diffInDays($end);
            $monthsDiff = $start->diffInMonths($end);

            if ($daysDiff <= 31) {
                // Daily data
                $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());
                foreach ($period as $date) {
                    $dateStr = $date->format('Y-m-d');
                    $categories[] = $date->format('M d');
                    $keys[] = $dateStr;
                }
            } elseif ($monthsDiff <= 24) {
                // Monthly data with year
                $period = \Carbon\CarbonPeriod::create($start->startOfMonth(), '1 month', $end->endOfMonth());
                foreach ($period as $month) {
                    $monthStr = $month->format('Y-m');
                    $categories[] = $month->format('M Y');
                    $keys[] = $monthStr;
                }
            } else {
                // Yearly data
                $period = \Carbon\CarbonPeriod::create($start->startOfYear(), '1 year', $end->endOfYear());
                foreach ($period as $year) {
                    $yearStr = $year->format('Y');
                    $categories[] = $yearStr;
                    $keys[] = $yearStr;
                }
            }
        } else {
            // Monthly data (for 'month' range)
            $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());
            foreach ($period as $date) {
                $dateStr = $date->format('Y-m-d');
                $categories[] = $date->format('M d');
                $keys[] = $dateStr;
            }
        }

        // Build series data
        $series = [];

        //  if ($isMultiFacility) {
        //     $grouped = [];
        //     // dd($rawData);
        //     // foreach ($rawData as $row) {
        //     //     $facilityName = $row->facility_name ?? "Facility {$row->user_id}";
        //     //     $grouped[$row->status]['name'] = $facilityName;
        //     //     $grouped[$row->status]['data'][$row->period] = $row->total;
        //     // }

        //     // $series = collect($grouped)->map(fn($item) => [
        //     //     'name' => $item['name'],
        //     //     'group' => $item[$key],
        //     //     'data' => $keys->map(fn($key) => $item['data'][$key] ?? 0)->toArray(),
        //     // ])->values()->toArray();

        //     $grouped = [];

        //     foreach ($rawData as $row) {
        //         // Derive or assume Q1, Q2 etc. from existing row data
        //         // $periodGroup = $row->period_group ?? $row->period; // adjust as needed
        //         // $status = strtolower($row->status); // 'budget' or 'actual'

        //         // $key = $periodGroup . '-' . $status;
        //         $status = $row->status;
        //         $facilityName = $row->facility_name ?? "Facility {$row->user_id}";
        //         $key = $status . '-' . $row->user_id;

        //         $grouped[$key]['name'] = $facilityName . ' : ' . ucwords(str_replace('_', ' ', $status));

        //         $grouped[$key]['group'] = $facilityName;
        //         $grouped[$key]['data'][$row->period] = $row->total;
        //     }

        //     // Map to chart-ready series array
        //     $series = collect($grouped)->map(fn($item) => [
        //         'name' => $item['name'],
        //         'group' => $item['group'],
        //         'data' => $keys->map(fn($key) => $item['data'][$key] ?? ($chartType === 'bar' ? null : 0))->toArray(),
        //     ])->values()->toArray();
        // } else {
        //     $grouped = $rawData->groupBy('status'); // Group records by status

        //     $series = [];

        //     foreach ($grouped as $status => $items) {
        //         // Map each period (e.g., "2025-05") to its total
        //         $data = $keys->map(function ($key) use ($items, $chartType) {
        //             $item = $items->firstWhere('period', $key);
        //             // return $item ? $item->total : 0;
        //             return $item ? $item->total : ($chartType === 'bar' ? null : 0);
        //         });

        //         $series[] = [
        //             'name' => 'Total Orders : ' . ucwords(str_replace('_', ' ', $status)),
        //             'group' => 'Total Orders',
        //             'data' => $data->toArray(),
        //         ];
        //     }
        // }

        if ($isMultiFacility) {
            $grouped = [];

            foreach ($rawData as $row) {
                $status = $row->status;
                $facilityName = $row->facility_name ?? "Facility {$row->user_id}";
                $key = $status . '-' . $row->user_id;

                $grouped[$key]['name'] = $facilityName . ' : ' . ucwords(str_replace('_', ' ', $status));
                $grouped[$key]['group'] = $facilityName;
                $grouped[$key]['data'][$row->period] = $row->total;
            }

            $series = collect($grouped)->map(fn($item) => [
                'name' => $item['name'],
                'group' => $item['group'],
                'data' => collect($keys)->map(fn($key) => $item['data'][$key] ?? ($chartType === 'bar' ? null : 0))->toArray(),
            ])->values()->toArray();
        } else {
            $grouped = $rawData->groupBy('status');

            foreach ($grouped as $status => $items) {
                $data = collect($keys)->map(function ($key) use ($items, $chartType) {
                    $item = $items->firstWhere('period', $key);
                    return $item ? $item->total : ($chartType === 'bar' ? null : 0);
                });

                $series[] = [
                    'name' => ucwords(str_replace('_', ' ', $status)),
                    'group' => 'Total Orders',
                    'data' => $data->toArray(),
                ];
            }
        }

        return [
            'chart' => [
                'type' => $chartType,
                'height' => 600,
                'stacked' => $chartType === 'bar',
                'zoom' => [
                    'enabled' => false,
                ],
                'toolbar' => [
                    'show' => false,
                ],
            ],
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,
                    'dataLabels' => [
                        'position' => 'top',
                        'hideOverflowingLabels' => false,
                    ],
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                'offsetY' => $chartType === 'bar' ? -8 : -6,
            ],
            'series' => $series,
             'legend' => [
                 'fontSize'=> '15px'
            ],
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'show' => true,
                    'rotate' => count($categories) > 12 ? -45 : 0,
                    'rotateAlways' => false,
                    'hideOverlappingLabels' => false,
                    'showDuplicates' => true,
                    'trim' => false,
                    'maxHeight' => 80,
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                        'fontSize' => '11px',
                    ],
                ],
                'tickAmount' => count($categories),
                'tickPlacement' => 'on',
                'title' => [
                    'text' => count($series) === 1 ? collect($series)->pluck('name')->first() : '',
                    'style' => [
                        'fontWeight' => 600,
                        'fontSize' => '18px',
                    ],
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],
            'stroke' => [
                'curve' => $chartType === 'line' ? 'straight' : 'smooth',
                'width' => 2,
            ],
            'markers' => [
                'size' => $chartType === 'line' ? 6 : 0,
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 8,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return Math.round(val).toLocaleString();
                        }
                    }
                },
            }
        JS);
    }
}