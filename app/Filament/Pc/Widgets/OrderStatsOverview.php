<?php

namespace App\Filament\Pc\Widgets;

use App\Filament\Pc\Resources\CreditLineOrderResource;
use App\Filament\Pc\Resources\OrderResource;
use App\Filament\Pc\Resources\ShipmentResource;
use App\Models\Order;
use App\Models\SubOrder;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Filament\Facades\Filament;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Session;

class OrderStatsOverview extends BaseWidget
{
    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Orders';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.pc.widgets.order-stats-sverview';

    public function render(): View
    {
        return view('filament.pc.widgets.order-stats-sverview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public $today;

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        if(session()->has('filterType')){
            $this->filterOption = Session::get('filterType');
            $this->startDate = Session::get('startDate');
            $this->endDate = Session::get('endDate');
            // session()->forget('filterType');
        }

        $this->today = now()->toDateString();
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $startDate = null;
        $endDate = null;

        switch ($this->filterOption) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today()->endOfDay();
                break;

            case 'this_week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;

            case 'this_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;

            case 'this_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;

            case 'custom_dates':
                if ($this->startDate && $this->endDate) {
                    $startDate = Carbon::parse($this->startDate)->startOfDay();
                    $endDate = Carbon::parse($this->endDate)->endOfDay();
                } else {
                    // session()->flash('error', 'Please select both start and end dates.');
                    return;
                }
                break;
        }

        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;
        $subOrderStats = SubOrder::selectRaw("
            COUNT(*) as total_orders,
            COUNT(CASE WHEN payment_type = 'credit_line' THEN 1 END) as credit_line_orders,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
            COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted_orders,
            COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_orders,
            COUNT(CASE WHEN status = 'in_transit' THEN 1 END) as in_transit_orders,
            COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered_orders,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_orders
        ")
            ->where('user_id', $pcUserId)
            ->when(
                $startDate && $endDate,
                fn($query) =>
                $query->whereBetween('created_at', [$startDate, $endDate])
            )
            ->first();

        $this->stats = [
            [
                'label' => 'Total',
                'query' => number_format($subOrderStats->total_orders),
                'icon' => 'shopping-cart',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],
            [
                'label' => 'Pending',
                'query' => number_format($subOrderStats->pending_orders),
                'icon' => 'clock',
                'color' => 'var(--Warning-500, #F59E0B)',
                'bg' => '#FFF5E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=pending"

            ],
            [
                'label' => 'Accepted',
                'query' => number_format($subOrderStats->accepted_orders),
                'icon' => 'check-circle',
                'color' => '#8B5CF6',
                'bg' => '#EDE5FF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>ShipmentResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=accepted"
            ],
            [
                'label' => 'Rejected',
                'query' => number_format($subOrderStats->rejected_orders),
                'icon' => 'shopping-cart',
                'color' => 'var(--Danger-600, #DC2626)',
                'bg' => '#FFE5E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=rejected"
            ],
            [
                'label' => 'In-Transit',
                'query' => number_format($subOrderStats->in_transit_orders),
                'icon' => 'truck',
                'color' => 'var(--Info-600, #2563EB)',
                'bg' => '#E5EDFF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=in_transit"
            ],
            [
                'label' => 'Delivered',
                'query' => number_format($subOrderStats->delivered_orders),
                'icon' => 'shopping-cart',
                'color' => 'var(--Success-600, #16A34A)',
                'bg' => '#E5FFEF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=delivered"
            ],
            [
                'label' => 'Cancelled',
                'query' => number_format($subOrderStats->cancelled_orders),
                'icon' => 'x-circle',
                'color' => '#EAB308',
                'bg' => '#FFF9E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate&tableFilters[status][values][0]=cancelled"
            ],
            [
                'label' => 'Credit Line Orders',
                'query' => number_format($subOrderStats->credit_line_orders),
                'icon' => 'shopping-cart',
                'color' => '#805b9b',
                'bg' => '#dcd1e4;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>CreditLineOrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
            if($this->startDate && $this->endDate){
            Session::put('filterType', $this->filterOption);
                session::put('startDate', $this->startDate);
                session::put('endDate', $this->endDate);
            }

        } else {
            Session::put('filterType', $this->filterOption);
            $this->applyFilter();
        }
    }

    public function resetFilter()
    {
        $this->startDate = null;
        $this->endDate = null;
        $this->filterOption = 'this_month';
        session()->forget('filterType');
        session()->forget('startDate');
        session()->forget('endDate');
        $this->applyFilter();
    }
}
