<?php

namespace App\Filament\Pc\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductRelation;
use Carbon\Carbon;
use DateTime;
use Filament\Facades\Filament;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;

class TopExpireProducts extends BaseWidget
{
    protected static ?string $model = ProductRelation::class;
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Expiring Soon (Nearest 10)';


    public function table(Table $table): Table
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;
        $from = now();
        $until = $authUser->pcDetails->expiry_soon ?? now()->addMonths(3)->startOfMonth()->setTime(23, 59, 59);

        return $table
            ->description('Until ' . getFormatedDate($until, getDateTimeFormatForPc($authUser)))
            ->query(
                // Product::whereHas('productData', function ($query) use ($pcUserId) {
                //     $query->where('products_relation.user_id', $pcUserId);
                // })->whereHas('productData.productRelationStock', function ($stockQuery) use ($from, $until) {
                //                 $stockQuery->where(function ($q) use ($from, $until) {
                //                     // Non batch-wise stock with expiry_date in range
                //                     $q->where('is_batch_wise_stock', false)
                //                         ->whereBetween('expiry_date', [$from, $until]);
                //                 })->orWhere(function ($q) use ($from, $until) {
                //                     // Batch-wise stock with any batch expiry_date in range
                //                     $q->where('is_batch_wise_stock', true)
                //                         ->whereHas('productsBatch', function ($batchQuery) use ($from, $until) {
                //                             $batchQuery->whereBetween('expiry_date', [$from, $until]);
                //                         });
                //                 });
                //             })
                //             ->limit(10);

                Product::whereHas('productData', function ($query) use ($pcUserId, $from, $until) {
                    $query->where('user_id', $pcUserId)
                        ->whereNull('deleted_at')
                        ->where('is_rejected',false)
                        ->whereHas('productRelationStock', function ($stockQuery) use ($from, $until) {
                            $stockQuery->where('is_batch_wise_stock', false)
                                ->whereBetween('expiry_date', [$from, $until])
                                ->orWhere(function ($q) use ($from, $until) {
                                    $q->where('is_batch_wise_stock', true)
                                        ->whereHas('productsBatch', function ($batchQuery) use ($from, $until) {
                                            $batchQuery->whereBetween('expiry_date', [$from, $until]);
                                        });
                                });
                        });
                })
                    ->orderBy('id', 'asc')
                    ->limit(10)
            )
            // ->filters([
            //     Filter::make('expiry_filter')
            //         ->form([
            //             DatePicker::make('from')
            //                 ->label('Start Date')
            //                 ->default(now()),
            //             DatePicker::make('until')
            //                 ->label('End Date')
            //                 ->default(now()->addMonths(3)->startOfMonth()),
            //         ])
            //         ->query(function ($query, array $data) {
            //             $from = $data['from'] ?? null;
            //             $until = $data['until'] ?? null;

            //             if ($from && $until) {
            //                 $query->whereHas('productData.productRelationStock', function ($stockQuery) use ($from, $until) {
            //                     $stockQuery->where(function ($q) use ($from, $until) {
            //                         // Non batch-wise stock with expiry_date in range
            //                         $q->where('is_batch_wise_stock', false)
            //                             ->whereBetween('expiry_date', [$from, $until]);
            //                     })->orWhere(function ($q) use ($from, $until) {
            //                         // Batch-wise stock with any batch expiry_date in range
            //                         $q->where('is_batch_wise_stock', true)
            //                             ->whereHas('productsBatch', function ($batchQuery) use ($from, $until) {
            //                                 $batchQuery->whereBetween('expiry_date', [$from, $until]);
            //                             });
            //                     });
            //                 });
            //             }
            //         })
            //         ->indicateUsing(function (array $data): ?string {
            //             $from = $data['from'] ?? null;
            //             // dd(date($from );
            //             $until = $data['until'] ?? null;

            //             if ($from && $until) {
            //                 return "Range : From {$from} to {$until}";
            //             }

            //             if ($from) {
            //                 return "Range : From {$from}";
            //             }

            //             if ($until) {
            //                 return "Until {$until}";
            //             }

            //             return '';
            //         }),
            // ])
            ->columns([
                TextColumn::make('id')->prefix('#')
                    ->label('Product ID')
                    ->color('primary')
                    ->url(fn($record): string => route('filament.pc.resources.products.view', ['record' => $record->id])),

                TextColumn::make('name')
                    ->label('Product Name'),
                
               
                TextColumn::make('expiry_date')
                    ->label('Expiry Date')
                    ->getStateUsing(function ($record) use ($pcUserId,$from,$until) {

                        $stock = $record->productDataForPc($pcUserId)?->productRelationStock;

                        if (!$stock) {
                            return '';
                        }

                        if (!$stock->is_batch_wise_stock) {
                            return $stock->expiry_date ? date('Y-m-d', strtotime($stock->expiry_date)) : '';
                        }

                        $batches = $stock->productsBatch;

                        if ($batches->isEmpty()) {
                            return '';
                        } 
                        $filtered = $batches->filter(function ($batch) use ($from) {
                            return \Carbon\Carbon::parse($batch['expiry_date'])->gte(\Carbon\Carbon::parse($from->toDateString()));
                        });
                      
                        $latestExpiry = $filtered->min('expiry_date');//$batches->min('expiry_date');
                        return $latestExpiry ? date('Y-m-d', strtotime($latestExpiry)) : '';
                    })->formatStateUsing(function ($state) use ($authUser) {
                        $format = $authUser->pcDetails->date_format ?? 'M d, Y';
                        return getFormatedDate($state, $format);
                    }),
            ])

            ->paginated(false)
            ->emptyStateHeading('No Products Available')
            ->emptyStateDescription('Expiring Soon products will appear here.');
    }
}
