<?php

namespace App\Filament\Pc\Widgets;

use App\Filament\Admin\Resources\ProductResource;
use App\Models\Product;
use App\Models\ProductRelation;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Filament\Facades\Filament;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;

class ProductStatsOverview extends BaseWidget
{
    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Products';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.pc.widgets.product-stats-oververview';

    protected function getHeaderActions(): array
    {
        return [
            FilterAction::make()
                ->form([
                    DatePicker::make('startDate'),
                    DatePicker::make('endDate'),
                ]),
        ];
    }

    public function render(): View
    {
        return view('filament.pc.widgets.product-stats-oververview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $authUser = auth()->user();
        $userId = $authUser->parent_id ?? $authUser->id;
        $expireFrom = now();
        $expireUntil = $authUser->pcDetails->expiry_soon??now()->addMonths(3)->startOfMonth()->setTime(23, 59, 59);

        // $stats = Product::whereHas('productData', function ($q) use ($userId) {
        //     $q->where('user_id', $userId)
        //         ->whereHas('productRelationStock');
        // })
        //     ->with(['productData.productRelationStock'])
          $stats = Product::selectRaw('
                COUNT(DISTINCT products.id) as total_products,

                COUNT(DISTINCT CASE
                    WHEN product_relation_stocks.is_batch_wise_stock = true AND (
                        SELECT COALESCE(SUM(pb.available_stock), 0)
                        FROM products_batch pb
                        WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                    ) <= 0
                    OR (product_relation_stocks.is_batch_wise_stock = false AND product_relation_stocks.stock <= 0)
                THEN products.id END) as out_of_stock,

                COUNT(DISTINCT CASE
                    WHEN product_relation_stocks.is_batch_wise_stock = true AND (
                        SELECT COALESCE(SUM(pb.available_stock), 0)
                        FROM products_batch pb
                        WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                    ) < product_relation_stocks.low_stock AND (
                        SELECT COALESCE(SUM(pb.available_stock), 0)
                        FROM products_batch pb
                        WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                    ) > 0
                    OR (
                        product_relation_stocks.is_batch_wise_stock = false AND
                        product_relation_stocks.stock < product_relation_stocks.low_stock AND
                        product_relation_stocks.stock > 0
                    )
                THEN products.id END) as low_stock,

                COUNT(DISTINCT CASE
            WHEN (
                -- Non-batch-wise products with expired date
                (product_relation_stocks.is_batch_wise_stock = false AND product_relation_stocks.expiry_date < CURRENT_DATE)
            ) OR (
                -- Batch-wise products where ALL batches are expired
                product_relation_stocks.is_batch_wise_stock = true AND NOT EXISTS (
                    SELECT 1 FROM products_batch pb
                    WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                    AND pb.expiry_date > CURRENT_DATE
                ) AND EXISTS (
                    -- Make sure at least one batch exists (otherwise product has no batches at all)
                    SELECT 1 FROM products_batch pb
                    WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                )
            )
        THEN products.id END) as expired_products,

         COUNT(DISTINCT CASE
            WHEN (
                -- Non-batch-wise products expiring soon (within custom date range)
                (product_relation_stocks.is_batch_wise_stock = false AND
                 product_relation_stocks.expiry_date >= ? AND
                 product_relation_stocks.expiry_date <= ?)
            ) OR (
                -- Batch-wise products with at least one batch expiring soon
                product_relation_stocks.is_batch_wise_stock = true AND EXISTS (
                    SELECT 1 FROM products_batch pb
                    WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
                    AND pb.expiry_date >= ?
                    AND pb.expiry_date <= ?
                )
            )
        THEN products.id END) as expiring_soon_products
    ', [$expireFrom, $expireUntil, $expireFrom, $expireUntil])
            ->join('products_relation', 'products.id', '=', 'products_relation.product_id')
            ->leftJoin('product_relation_stocks', 'products_relation.id', '=', 'product_relation_stocks.product_relation_id')
            ->where('products_relation.user_id', $userId)
            ->whereNull('products_relation.deleted_at')
            ->where('products_relation.is_rejected',false)
            ->first();

        $this->stats = [
            [
                'label' => 'Total Products',
                // 'query' => number_format($allProducts->count() ?? 0),
                'query' => number_format($stats->total_products ?? 0),
                'icon' => 'queue-list',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ProductResource::getUrl()."?tab=all-products"
            ],
            [
                'label' => 'Low Stock Products',
                'query' => number_format($stats->low_stock ?? 0),
                'icon' => 'exclamation-triangle',
                'color' => 'var(--Warning-500, #F59E0B)',
                'bg' => '#FFF5E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ProductResource::getUrl() . '?tab=all-products&tableFilters[stock][value]=low_stock&tableFilters[pending%20approval][isActive]=false'
            ],
            [
                'label' => 'Expired Products',
                'query' => number_format($stats->expired_products ?? 0),
                'icon' => 'clock',
                'color' => '#2563EB',
                'bg' => '#E5EDFF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ProductResource::getUrl() . '?tab=all-products&tableFilters[stock][value]=expired&tableFilters[pending%20approval][isActive]=false'
            ],
            [
                'label' => 'Out of Stock Products',
                'query' => number_format($stats->out_of_stock ?? 0),
                'icon' => 'numbered-list',
                'color' => 'var(--Danger-600, #DC2626)',
                'bg' => '#FFE5E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ProductResource::getUrl() . '?tab=all-products&tableFilters[stock][value]=out_of_stock&tableFilters[pending%20approval][isActive]=false'
            ],
            [
                'label' => 'Expiring Soon (Untill '.getFormatedDate($expireUntil,getDateTimeFormatForPc($authUser)).')',
                'query' => number_format($stats->expiring_soon_products ?? 0),
                'icon' => 'clock',
                'color' => '#EAB308',
                'bg' => '#FFF9E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ProductResource::getUrl(). '?tab=all-products&tableFilters[stock][value]=expiring_soon&tableFilters[pending%20approval][isActive]=false'
            ]

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }
}
