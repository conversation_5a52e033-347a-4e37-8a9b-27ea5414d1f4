<?php

namespace App\Filament\Pc\Widgets;

use App\Filament\Pc\Resources\OrderResource;
use App\Models\Product;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Contracts\View\View;

class FinanceOverview extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected function getStats(): array
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;

        $revenueStats = DB::table('sub_orders as so')
            ->leftJoin(DB::raw('(
            SELECT sub_order_id, SUM(total_commission) AS total_commission
            FROM order_products
            GROUP BY sub_order_id
        ) as op_comm'), 'op_comm.sub_order_id', '=', 'so.id')
            ->where('so.user_id', $pcUserId)
            ->whereIn('so.status', ['accepted', 'delivered'])
            ->selectRaw('
            COALESCE(SUM(so.total_amount), 0) AS total_amount,
            COALESCE(SUM(DISTINCT op_comm.total_commission), 0) AS total_commission,
            COALESCE(SUM(so.total_amount), 0) - COALESCE(SUM(DISTINCT op_comm.total_commission), 0) AS final_amount
        ')
            ->first();

        return [
            Stat::make('Total Sales', 'RM ' . number_format($revenueStats->final_amount, 2))
                ->icon('heroicon-o-currency-dollar')
                ->extraAttributes([
                    'style' => 'background-color: #E5F7FF;',
                ])
                ->iconPosition('start')
                ->iconBackgroundColor('info')
                ->iconColor('light'),

        ];
    }

    protected static string $view = 'filament.pc.widgets.finance-overview';

    public function render(): View
    {
        return view('filament.pc.widgets.finance-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;

        $revenueStats = DB::table('sub_orders as so')
            ->leftJoin(DB::raw('(
            SELECT sub_order_id, SUM(total_commission) AS total_commission
            FROM order_products
            GROUP BY sub_order_id
        ) as op_comm'), 'op_comm.sub_order_id', '=', 'so.id')
            ->where('so.user_id', $pcUserId)
            ->whereIn('so.status', ['accepted', 'delivered'])
            ->selectRaw('
            COALESCE(SUM(so.total_amount), 0) AS total_amount,
            COALESCE(SUM(so.total_sub_order_value), 0) AS total_sub_order_value,
            COALESCE(SUM(op_comm.total_commission), 0) AS total_commission,
            COALESCE(SUM(so.total_sub_order_value), 0) - COALESCE(SUM(op_comm.total_commission), 0) AS final_amount
        ')
            ->first();

        $this->stats = [
            [
                'label' => 'Total Revenue',
                'query' => 'RM ' . number_format($revenueStats->total_sub_order_value, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered'
            ],
             [
                'label' => 'Total Earning',
                'query' => 'RM ' . number_format($revenueStats->final_amount, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Success-600, #16A34A)',
                'bg' => '#E5FFEF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered'
            ],
        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }
}
