<?php

namespace App\Filament\Pc\Widgets;

use App\Models\ClinicDetail;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Carbon;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use Filament\Forms\Components\ToggleButtons;

class RevenueChart extends ApexChartWidget
{
    protected static ?string $chartId = 'RevenueChart';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Revenue vs Earnings';
    protected static ?string $pollingInterval = '10s';
    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('facility_id')
                ->label('By Facility')
                ->options(function () {
                    $authUser = Filament::auth()->user();
                    $pcUserId = $authUser->parent_id ?? $authUser->id;

                    $users = User::where(function ($query) use ($pcUserId) {
                        // Users who are approved in clinic_pharma_suppliers
                        $query->whereHas('pharmaSuppliers', function ($q) use ($pcUserId) {
                            $q->where('pc_id', $pcUserId)
                                ->where('status', 'approved');
                        });
                    })->orWhere(function ($query) use ($pcUserId) {
                        // Users who have orders in sub_orders related to the given pc_id
                        $query->whereHas('orders.subOrders', function ($q) use ($pcUserId) {
                            $q->where('user_id', $pcUserId);
                        });
                    })
                        ->whereHas('clinicData') // Only those users that have clinic data
                        ->with('clinicData:id,user_id,clinic_name')
                        ->get()
                        ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                        ->mapWithKeys(fn($user) => [
                            $user->id => $user->clinicData->clinic_name,
                        ])
                        ->sortBy(fn($name) => $name)
                        ->toArray();

                    return $users;
                })
                ->searchable()
                ->preload()
                ->placeholder('By Facility')
                ->reactive(),

            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->reactive()
                ->required(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start')) // prevent end date before start date
                ->maxDate(now()) // prevent future dates
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            ToggleButtons::make('chart_type')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'line' => '📈', // Line chart emoji
                    'bar'  => '📊', // Bar chart emoji
                ])
                ->default('line')
                ->reactive()
        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

    protected function getOptions(): array
    {

        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $chartType = $filters['chart_type'] ?? 'line';
        $facilityId = $filters['facility_id'] ?? null;

        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
        }

        $authUser = Filament::auth()->user();
        $pcUserId = $authUser->parent_id ?? $authUser->id;

        $rawData = DB::table('sub_orders as so')
            ->join('orders', 'orders.id', '=', 'so.order_id')
            ->leftJoin(DB::raw('(
        SELECT sub_order_id, SUM(total_commission) AS total_commission
        FROM order_products
        GROUP BY sub_order_id
    ) as op_comm'), 'op_comm.sub_order_id', '=', 'so.id')
            ->whereBetween('so.created_at', [$start, $end])
            ->where('so.user_id', $pcUserId)
            ->whereIn('so.status', ['accepted', 'delivered'])
            ->when($facilityId, fn($query) => $query->where('orders.user_id', $facilityId))
            ->selectRaw("
        TO_CHAR(so.created_at, '{$groupByFormat}') as period, 
        COALESCE(SUM(so.total_sub_order_value), 0) as revenue,
        COALESCE(SUM(so.total_sub_order_value), 0) - COALESCE(SUM(op_comm.total_commission), 0) as earning
    ")
            ->groupBy('period')
            ->orderBy('period')
            ->get()->keyBy('period');

        if ($range === 'year') {
            $months = collect(range(1, 12))->mapWithKeys(function ($month) {
                $currentYear = now()->year;
                $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT); // '01'
                $monthLabel = Carbon::create()->month($month)->format('M') . ' ' . $currentYear; // 'Jan'
                return [$monthKey => $monthLabel];
            });

            $categories = $months->values()->toArray();  // For chart X-axis
            $keys = $months->keys();
        }
        if ($range === 'custom') {
            if ($daysDiff <= 31) {
                $periods = collect();
                $current = $start->copy();
                while ($current->lte($end)) {
                    $periods->push($current->format('Y-m-d'));
                    $current->addDay();
                }
                $categories = $periods->toArray();
                $keys = $periods;
            } elseif ($monthsDiff <= 24) {
                $periods = collect();
                $categories = [];

                $period = \Carbon\CarbonPeriod::create($start->startOfMonth(), '1 month', $end->endOfMonth());

                foreach ($period as $month) {
                    $monthStr = $month->format('Y-m');
                    $periods->push($monthStr); // used as key
                    $categories[] = $month->format('M Y'); // used for label
                }

                $keys = $periods;
            } else {
                $periods = collect();
                $current = $start->copy()->startOfYear();
                while ($current->lte($end)) {
                    $periods->push($current->format('Y'));
                    $current->addYear();
                }
                $categories = $periods->toArray();
                $keys = $periods;
            }
        } elseif ($range === 'year') {
            $months = collect(range(1, 12))->mapWithKeys(function ($month) {
                $currentYear = now()->year;
                $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT); // '01'
                $monthLabel = Carbon::create()->month($month)->format('M')  . ' ' . $currentYear; // 'Jan'
                return [$monthKey => $monthLabel];
            });

            $categories = $months->values()->toArray();  // For chart X-axis
            $keys = $months->keys();
        } else {
            // Default to daily grouping
            $startDate = Carbon::parse($start);
            $endDate = Carbon::parse($end);

            $dates = collect();
            while ($startDate->lte($endDate)) {
                $formatted = $startDate->format('Y-m-d');
                $dates->push($formatted);
                $startDate->addDay();
            }

            $categories = $dates->toArray();
            $keys = $dates;
        }


        $clinicName = $facilityId ? ClinicDetail::where('user_id', $facilityId)->value('clinic_name') . ' : ' : '';
        // 3. Build series
        $series = [
            [
                'name' => $clinicName . 'Revenue',
                'data' => $keys->map(fn($key) => round((float) ($rawData[$key]->revenue ?? 0), 2))->toArray(),
            ],
            [
                'name' => $clinicName . 'Earning',
                'data' => $keys->map(fn($key) => round((float) ($rawData[$key]->earning ?? 0), 2))->toArray(),
            ],
        ];

        $allPeriods = [];

        if ($range === 'year') {
            // Monthly: Jan to Dec
            for ($i = 1; $i <= 12; $i++) {
                $allPeriods[] = str_pad($i, 2, '0', STR_PAD_LEFT); // '01' to '12'
            }
        } else {
            // Daily: for custom date range
            $current = $start->copy();
            while ($current->lte($end)) {
                $allPeriods[] = $current->format('d'); // '01', '02', ..., '31'
                $current->addDay();
            }
        }
        return [
            'chart' => [
                'type' => $chartType,
                'height' => 400,
                'zoom' => [
                    'enabled' => false, // Disable zoom
                ],
                'toolbar' => [
                    'show' => false, // Disable toolbar which includes download option
                ],
                'width' => '100%',
            ],
            // 'series' => [
            //     [
            //         'name' => 'Earning',
            //         'data' => $seriesData,
            //     ],
            // ],
            'series' => $series,
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                // 'title' => [
                //     'text' => $clinicName,
                //     'style' => [
                //         'fontWeight' => 600,
                //         'fontSize' => '18px',
                //     ],
                // ],
            ],
             'legend' => [
                 'fontSize'=> '15px'
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                    // 'formatter' => 'function (val) { return "RM " + val.toFixed(2); }', // Add RM before the value
                ],
            ],
            // 'colors' => ['#0BA7F5'],
            'stroke' => [
                'curve' => 'straight', // no curve
                'width' => 2,
            ],
            'markers' => [
                'size' => 10, // size of the dot
                // 'colors' => ['#0BA7F5'],
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 10,
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                // 'enabledOnSeries' => [0], // ensure it's applied to the correct series
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                'offsetY' => -10, // moves label above point
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return 'RM ' + val.toLocaleString();
                        }
                    }
                },
            }
        JS);
    }
}
