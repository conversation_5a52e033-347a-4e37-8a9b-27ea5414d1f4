<?php

namespace App\Filament\Pc\Pages;

use App\Filament\Pc\Resources\UserResource;
use App\Forms\Components\PhoneWithPrefix;
use App\Models\User;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Pages\Page;
use App\Models\WareHouse;
use App\Models\PcCompanyType;
use Filament\Actions\Action;
use Nnjeim\World\Models\City;
use Filament\Facades\Filament;
use Nnjeim\World\Models\State;
use Illuminate\Validation\Rule;
use Illuminate\Support\HtmlString;
use Illuminate\Contracts\View\View;
use App\Service\StoreProfileService;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Wizard;
use Illuminate\Validation\Rules\File;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Wizard\Step;
use App\Http\Middleware\IsApprovedPcMiddleware;
use App\Models\PcCertificateFile;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Set;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\Button;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as NewAction;
use Stripe\Stripe;
use Stripe\Account;
use Stripe\AccountLink;
use Illuminate\Support\Facades\Crypt;
use Filament\Forms\Components\Placeholder;
use App\Models\PcDetail;
use App\Models\ZipCode;
use App\Rules\PhoneWithPrefixRule;
use Filament\Forms\Components\Checkbox;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class EditProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    public static array|string $routeMiddleware = IsApprovedPcMiddleware::class;

    public function getTitle(): string|Htmlable
    {
        return "Complete your profile";
    }

    protected static ?string $slug = 'onboarding';

    protected static string $view = 'filament.pc.pages.edit-profile';

    public array $data = [];

    public User $user;

    public ?WareHouse $wareHouse;

    public $business_name = '';

    public $company_name = '';

    public $company_registration_number = '';

    public $tin_number = '';

    public $sstc_number = '';

    public $phone_number = '';

    public $email = '';

    public $address_1 = '';

    public $address_2 = '';

    public $city_id = '';

    public $state_id = '';

    public $district = '';

    public $profile_email = '';

    public $ware_phone_number = '';

    public $ware_postal_code = '';

    public $ware_address_1 = '';

    public $ware_address_2 = '';

    public $ware_district = '';

    public $ware_state_id = '';

    public $ware_city_id = '';

    public $warehouse_type = '';

    public $ware_id = '';

    public $person_in_charge_name = '';

    public $person_in_charge_nric = '';

    public $person_in_charge_phone = '';

    public $person_in_charge_landline = '';

    public $company_registration_certificate = null;

    public $license_permit = null;

    public $postal_code = '';

    public $web_url = '';

    public $region = '';
    public $land_line_code = '';

    public $delivery_days = '';

    public $delivery_days_west = '';
    public $min_order_value = '';


    public static function shouldRegisterNavigation(): bool
    {
        return false;
        // return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin');
    }

    public function mount(): void
    {
        $this->user = Filament::auth()->user();
        $this->wareHouse = $this->user->warehouses()?->orderBy('updated_at', 'desc')->first();
        $contactDetails = $this->user->addresses->where('is_onboarding', true)->first();

        $certificateFiles = PcCertificateFile::where('user_id', $this->user->id)->where('status', 'active')
        ->get()
        ->groupBy('type');

        // dd($certificateFiles);
        // Prepare file arrays for each type
        $companyRegistrationFiles = $certificateFiles->get('company_registration_certificate', collect())
            ->pluck('name')
            // ->map(fn ($path) => ['users/'.$path])
            ->toArray();
        // dd($companyRegistrationFiles);
        $licensePermitFiles = $certificateFiles->get('license_permit', collect())
            ->pluck('name')
            // ->map(fn ($path) => ['users/'.$path])
            ->toArray();
        $this->form->fill([
            'business_name' => $this->user->pcDetails?->business_name,
            'company_type_id' => $this->user->pcDetails?->company_type_id,
            'company_name' => $this->user->pcDetails?->company_name,
            'company_registration_number' => $this->user->pcDetails?->company_registration_number,
            'tin_number' => $this->user->pcDetails?->tin_number,
            'sstc_number' => $this->user->pcDetails?->sstc_number,
            'phone_number' => $this->user->pcDetails?->phone_number,
            'profile_email' => $this->user->pcDetails?->profile_email,
            'postal_code' => $contactDetails?->postal_code,
            'web_url' => $this->user->pcDetails?->web_url,
            'region' => $this->user->pcDetails?->region,
            'user_email' => $this->user->email,

            'address_1' => $contactDetails?->address_1,
            'address_2' => $contactDetails?->address_2,
            'district' => $contactDetails?->district,
            'state_id' => $contactDetails?->state_id,
            'city_id' => $contactDetails?->city_id,
            // 'landline_number' => $contactDetails?->landline_number,
            'landline_number' => $contactDetails?->landline_number,
            'landline_code' => $contactDetails?->landline_code,


            'ware_phone_number' => $this->wareHouse?->phone_number,
            'warehouse_type' => $this->wareHouse?->warehouse_type,
            'ware_postal_code' => $this->wareHouse?->postal_code,
            'ware_address_1' => $this->wareHouse?->address_1,
            'ware_address_2' => $this->wareHouse?->address_2,
            'ware_district' => $this->wareHouse?->district,
            'ware_state_id' => $this->wareHouse?->state_id,
            'ware_city_id' => $this->wareHouse?->city_id,
            'ware_region' => $this->wareHouse?->ware_region,
            'ware_id' => $this->wareHouse?->id,

            'person_in_charge_name' => $this->user->pcDetails?->person_in_charge_name,
            'person_in_charge_email' => $this->user->pcDetails?->person_in_charge_email,
            'person_in_charge_nric' => $this->user->pcDetails?->person_in_charge_nric,
            'person_in_charge_phone' => $this->user->pcDetails?->person_in_charge_phone,
            'person_in_charge_landline' => $this->user->pcDetails?->person_in_charge_landline,
            'delivery_days' => $this->user->pcDetails?->delivery_days,
            'delivery_days_west' => $this->user->pcDetails?->delivery_days_west,
            'min_order_value' => $this->user->pcDetails?->min_order_value,

            ...$this->user->toArray(),

            'company_registration_certificate' => $companyRegistrationFiles,//$this->user->pcDetails?->company_registration_certificate,
            'license_permit' => $licensePermitFiles,//$this->user->pcDetails?->license_permit,

            'photo' => $this->user->photo ? ['users/'.$this->user->photo] : null,
        ]);

        $this->company_registration_certificate = $companyRegistrationFiles[0] ?? null;//$this->user->pcDetails?->company_registration_certificate;
        $this->license_permit = $licensePermitFiles[0] ?? null;//$this->user->pcDetails?->whereNotNull('step')->first()?->license_permit;
    }

    public function getHeader(): ?View
    {
        return View('custom-view.page-header', ['title' => 'Complete Your Profile!']);
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Wizard::make()
                ->previousAction(function ($action) {
                    return $action->label('Back');
                })
                ->nextAction(function ($action) {
                    return $action->label('Next');
                })
                ->schema([
                    Step::make('Company Information')
                        ->icon('phosphor-building')
                        ->schema([
                            Group::make()
                            ->schema([
                                FileUpload::make('photo')
                                    ->label('Company Logo')
                                    ->image()
                                    ->avatar()
                                    ->directory('users')
                                    ->rules(['nullable', File::types(['jpeg', 'png', 'jpg'])->max(2 * 1024)])
                                    ->helperText('Supported formats: JPEG, JPG, PNG (Max 2MB)')
                                    // ->columnSpanFull()
                            ])
                            ->columns(1),

                            Group::make()->schema([

                                TextInput::make('business_name')
                                    ->placeholder('Enter the business name')
                                    ->validationAttribute('Business Name')
                                    ->extraAttributes(['style' => 'margin-top: 0.3rem;'])
                                    ->label(fn () => new HtmlString('Business Name <span style="color: red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', Rule::unique('pc_details', 'business_name')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', 'unique:pc_details,business_name'];
                                    })->validationMessages([
                                        'required' => 'The Business Name field is required.',
                                        'string' => 'The Business Name must be a string.',
                                        'regex' => 'The Business Name must be a alphabetical.',
                                        'max' => 'The Business Name may not be greater than 64 characters.',
                                    ])
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'success' : null)
                                    ->live()                                    ,
                                Select::make('company_type_id')
                                    ->placeholder('Select company type')
                                    ->validationAttribute('Company Type')
                                    ->rules(['required'])
                                    ->label(new HtmlString(
                                        'Company Type <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Select the type of company that best describes your business.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                    ))
                                    ->options(fn () => PcCompanyType::pluck('name', 'id'))
                                    ->reactive(),
                                TextInput::make('company_name')
                                    ->placeholder('Enter the company name')
                                    ->label(function (Get $get) {
                                        $required = $get('company_type_id') != 1;
                                        $label = 'Company Name';
                                        if ($required) {
                                            $label .= '<span class="text-danger" style="color: #e3342f;">*</span>';
                                        }
                                        return new HtmlString($label);
                                    })
                                    ->validationAttribute('Company Name')
                                    ->rules(function (Get $get) {
                                        $required = $get('company_type_id') != 1;
                                        $baseRules = ['max:64', 'regex:/^[a-zA-Z\s]+$/'];
                                        if (!empty($this->user->id)) {
                                            $uniqueRule = Rule::unique('pc_details', 'company_name')->ignore($this->user->id, 'user_id');
                                        } else {
                                            $uniqueRule = 'unique:pc_details,company_name';
                                        }
                                        if ($required) {
                                            array_unshift($baseRules, 'required');
                                        }
                                        $baseRules[] = $uniqueRule;
                                        return $baseRules;
                                    })
                                    // ->required(fn (Get $get) => $get('company_type_id') != 1)
                                    ->validationMessages([
                                        'required' => 'The Company Name field is required.',
                                        'string' => 'The Company Name must be a string.',
                                        'regex' => 'The Company Name must be a alphabetical.',
                                        'max' => 'The Company Name may not be greater than 64 characters.',
                                    ])
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'success' : null)
                                    ->live()
                                    ->reactive(),
                                TextInput::make('company_registration_number')
                                    ->placeholder('Enter the company registration number')
                                    ->label(function (Get $get) {
                                        $required = $get('company_type_id') != 1;
                                        $label = 'Company Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>';
                                        if ($required) {
                                            $label .= '<span class="text-danger" style="color: #e3342f;">*</span>';
                                        }
                                        return new HtmlString($label);
                                    })
                                    ->validationAttribute('Company Registration Number')
                                    ->suffixIcon(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->live()
                                    ->rules(function (Get $get) {
                                        $required = $get('company_type_id') != 1;
                                        $baseRules = ['regex:/^[a-zA-Z0-9]+$/', 'max:20', 'min:2'];
                                        if (!empty($this->user->id)) {
                                            $uniqueRule = Rule::unique('pc_details', 'company_registration_number')->ignore($this->user->id, 'user_id');
                                        } else {
                                            $uniqueRule = 'unique:pc_details,company_registration_number';
                                        }
                                        if ($required) {
                                            array_unshift($baseRules, 'required');
                                        }
                                        $baseRules[] = $uniqueRule;
                                        return $baseRules;
                                    })
                                    // ->required(fn (Get $get) => $get('company_type_id') != 1)
                                    ->validationMessages([
                                        'required' => 'The Company Registration Number field is required.',
                                        'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                        'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                        'min' => 'The Company Registration Number must be at least 2 characters.',
                                        'unique' => 'The Company Registration Number has already been taken.',
                                    ])
                                    ->reactive(),
                                TextInput::make('tin_number')
                                    ->placeholder('Enter TIN number')
                                    ->label(fn () => new HtmlString('TIN Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Tax Identification Number (TIN) is a unique identifier assigned by the tax authority for businesses or individuals to track tax obligations.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg><span class="text-danger" style="color: #e3342f;">*</span>'))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['required', 'min:1', 'regex:/^[a-zA-Z0-9]+$/','max:20', Rule::unique('pc_details', 'tin_number')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['required', 'min:1','regex:/^[a-zA-Z0-9]+$/', 'max:20', 'unique:pc_details,tin_number'];
                                    })
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->live()
                                    ->validationAttribute('TIN Number')
                                    ->validationMessages([
                                        'max' => 'The TIN number may not be greater than 20 characters.',
                                        'min' => 'The TIN number must be at least 1 character.',
                                        'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                        'unique' => 'The TIN number has already been taken.',
                                        'required' => 'The TIN number field is required.',
                                    ]),
                                TextInput::make('sstc_number')
                                    ->placeholder('Enter SST number')
                                    ->label(fn () => new HtmlString('SST Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sales and Services Tax (SST) Registration Number is a unique code issued to businesses in Malaysia registered for SST compliance.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['nullable', 'max:20','regex:/^[a-zA-Z0-9]+$/', 'min:1', Rule::unique('pc_details', 'sstc_number')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/','min:1', 'unique:pc_details,sstc_number'];
                                    })
                                    ->validationAttribute('SST Number')
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->live()
                                    ->validationAttribute('SST Number')
                                    ->validationMessages([
                                        'max' => 'The SST number may not be greater than 20 characters.',
                                        'min' => 'The SST number must be at least 1 character.',
                                        'regex' => 'The SST number must be an alphanumeric string.',
                                        'unique' => 'The SST number has already been taken.',
                                    ]),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileService $service, $record) {
                            $data = [];
                            $data['business_name'] = $get('business_name');
                            $data['company_name'] = $get('company_name');
                            $data['company_registration_number'] = $get('company_registration_number');
                            $data['tin_number'] = $get('tin_number');
                            $data['sstc_number'] = $get('sstc_number');
                            $data['company_type_id'] = $get('company_type_id');

                            $data['user_id'] = $this->user->id;
                            $uploadedFile = $get('photo');
                            $uploadedFile['user_id'] = $this->user->id;
                            uploadUserImage($uploadedFile);
                            $service->storeCompanyInformations($data, 1);
                        }),
                    Step::make('Contact Details')
                        ->icon('heroicon-o-phone')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('user_email')
                                    ->label('Email')
                                    ->default($this->user->email)
                                    ->readOnly(),
                                TextInput::make('address_1')
                                    ->label('Address Line 1')
                                    ->placeholder('Enter address line 1')
                                    ->rules(['required', 'string', 'max:100'])
                                    ->required(),
                                TextInput::make('address_2')
                                    ->label('Address Line 2')
                                    ->placeholder('Enter address line 2')
                                    ->rules(['nullable', 'string', 'max:100']),
                                Select::make('state_id')
                                    ->label('State')
                                    ->placeholder('Select State')
                                    ->rules(['required'])
                                    ->required()
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('country_id', 132)
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if (empty($state)) {
                                            $set('region', null);
                                            $set('city_id', null);
                                            $set('postal_code', null);
                                            // Also clear landline_number and landline_code when state changes
                                            $set('landline_number.prefix', '');
                                            $set('landline_number.number', '');
                                            $set('landline_code', '');
                                            return;
                                        }
                                        $info = State::where('id', $state)->first();
                                        if ($info) {
                                            $set('region', ucfirst($info->zone));
                                        }
                                        $set('city_id', null);
                                        // Also clear landline_number and landline_code when state changes
                                        $set('landline_number.prefix', '');
                                        $set('landline_number.number', '');
                                        $set('landline_code', '');
                                    })
                                    ->required()->live(),
                                Select::make('city_id')
                                    ->label('City')
                                    ->rules(['required'])
                                    ->placeholder('Select City')
                                    ->required()
                                    ->loadingMessage('Loading cities...')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('state_id', $get('state_id'))
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->live()
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        $set('postal_code', null);
                                        // When city changes, update landline_code and landline_number.prefix
                                        $landlineCode = '';
                                        if ($state && $get('state_id')) {
                                            $city = City::where('id', $state)
                                                ->where('state_id', $get('state_id'))
                                                ->whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->first();
                                            if ($city) {
                                                $landlineCode = $city->landline_code;
                                            }
                                        }
                                        $set('landline_number.prefix', $landlineCode);
                                        $set('landline_code', $landlineCode);
                                    })
                                    ->live(onBlur: true)
                                    ->options(function (Get $get) {
                                        if (! empty($get('state_id'))) {
                                            return City::where('state_id', $get('state_id'))->pluck('name', 'id')->toArray();
                                        }
                                        return [];
                                    }),
                                TextInput::make('phone_number')
                                    ->placeholder('Enter mobile number')
                                    ->label('Mobile Number')
                                    ->mask('999999999999')
                                    ->prefix('+60')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->rules(['required','digits_between:8,12'])
                                    ->required()
                                    ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->live(),
                                PhoneWithPrefix::make('landline_number')
                                    ->label("Landline Number")
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '8'
                                    ])
                                    ->prefixOptions(function ($get, $set) {
                                        // Always show landline codes based on selected state and city
                                        $stateId = $get('state_id');
                                        $cityId = $get('city_id');
                                        $codes = [];

                                        if ($stateId && $cityId) {
                                            $codes = City::where('state_id', $stateId)
                                                ->where('id', $cityId)
                                                ->whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->distinct('landline_code')
                                                ->pluck('landline_code', 'landline_code')
                                                ->toArray();
                                        }

                                        // If no code found for city, fallback to all codes for state
                                        if (empty($codes) && $stateId) {
                                            $codes = City::where('state_id', $stateId)
                                                ->whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->distinct('landline_code')
                                                ->pluck('landline_code', 'landline_code')
                                                ->toArray();
                                        }

                                        // If still empty, fallback to all codes
                                        if (empty($codes)) {
                                            $codes = City::whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->distinct('landline_code')
                                                ->pluck('landline_code', 'landline_code')
                                                ->toArray();
                                        }

                                        // Set the prefix to the first available code if not set
                                        $currentPrefix = $get('landline_number')['prefix'] ?? '';
                                        if ($currentPrefix === '' && !empty($codes)) {
                                            $set('landline_number.prefix', array_key_first($codes));
                                            $set('landline_code', array_key_first($codes));
                                        }

                                        return $codes;
                                    })
                                    ->rules([new PhoneWithPrefixRule()])
                                    ->afterStateHydrated(function (Get $get, Set $set) {
                                        // Hydrate from DB if available
                                        if(isset($get('addresses')[0]["landline_code"]) && $get('addresses')[0]["landline_code"] != null) {
                                            $set("landline_number.prefix", $get('addresses')[0]["landline_code"]);
                                            $set("landline_number.number", $get('addresses')[0]["landline_number"]);
                                            $set("landline_code", $get('addresses')[0]["landline_code"]);
                                        } else {
                                            $set("landline_number", ["prefix" => "", "number" => ""]);
                                            $set("landline_code", "");
                                        }
                                    })
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        // Always keep landline_code in sync with prefix
                                        $prefix = is_array($state) ? ($state['prefix'] ?? '') : '';
                                        $set("landline_code", $prefix);
                                    })
                                    ->formatStateUsing(function ($get, $set, $state) {
                                        // Always return array with prefix and number
                                        $data = ['prefix' => '', 'number' => ''];
                                        if (is_array($state)) {
                                            $data = $state;
                                        }
                                        // If landline_code is set, use as prefix
                                        if ($get("landline_code")) {
                                            $data["prefix"] = $get("landline_code");
                                        }
                                        return $data;
                                    }),

                                Select::make('postal_code')->label('Postal Code')->placeholder('Select postal code')
                                    ->options(function (Get $get) {
                                        if (!empty($get('city_id'))) {
                                            return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                        }
                                        return [];
                                    })
                                    ->required()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        if ($get('city_id')) {
                                            return ZipCode::where('city_id', $get('city_id'))
                                            ->where('code', 'like', "%{$search}%")
                                            ->pluck('code', 'code')
                                            ->toArray();
                                        }
                                        return [];
                                    })
                                    ->live(onBlur: true)
                                    ->optionsLimit(100)
                                    ->loadingMessage('Loading postal code...')
                                    ->searchable(),
                                TextInput::make('region')->placeholder('Enter region')->disabled(),
                                TextInput::make('web_url')
                                    ->placeholder('Enter website url')
                                    ->nullable()
                                    ->rules([
                                        'regex:/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/',
                                    ])
                                    ->helperText('Ex: https://www.example.com, http://example.com, www.example.com, example.com')
                                    ->suffixIcon(function ($state) {
                                        $isValid = $state
                                            && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                        if (!$isValid) {
                                            return null;
                                        }

                                        $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                        $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                        return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                            ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(function ($state) {
                                        $isValid = $state
                                            && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                        if (!$isValid) {
                                            return null;
                                        }

                                        $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                        $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                        return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                            ? 'success' : null;
                                    })
                                    ->live(),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileService $service) {
                            $data = [];
                            $data['phone_number'] = $get('phone_number');
                            $data['landline_number'] = $get('landline_number')['number'] ?? "";
                            // Always get landline_code from the prefix (which is set by city/state selection)
                            $data['landline_code'] = $get('landline_number')['prefix'] ?? "";
                            $data['phone_code'] = '60';
                            $data['profile_email'] = $get('user_email');
                            $data['address_1'] = $get('address_1');
                            $data['address_2'] = $get('address_2');
                            $data['district'] = $get('district');
                            $data['state_id'] = $get('state_id');
                            $data['city_id'] = $get('city_id');
                            $data['postal_code'] = $get('postal_code');
                            $data['web_url'] = $get('web_url');
                            $data['region'] = $get('region');
                            // Save landline_code in DB correctly
                            $service->storeContactDetails($data, 2);
                        })->beforeValidation(function (Get $get, Set $set) {
                            // Set the prefix and number from the current state, or try to auto-select based on city/state
                            $stateId = $get('state_id');
                            $cityId = $get('city_id');
                            $prefix = $get('landline_number')['prefix'] ?? '';
                            $number = $get('landline_number')['number'] ?? '';

                            // If prefix is empty, try to get from city
                            if ($prefix === '' && $stateId && $cityId) {
                                $city = City::where('id', $cityId)
                                    ->where('state_id', $stateId)
                                    ->whereNotNull('landline_code')
                                    ->where('landline_code', '!=', '')
                                    ->first();
                                if ($city) {
                                    $prefix = $city->landline_code;
                                }
                            }

                            // If still empty, try to get from state
                            if ($prefix === '' && $stateId) {
                                $city = City::where('state_id', $stateId)
                                    ->whereNotNull('landline_code')
                                    ->where('landline_code', '!=', '')
                                    ->first();
                                if ($city) {
                                    $prefix = $city->landline_code;
                                }
                            }

                            $set("landline_number.prefix", $prefix);
                            $set("landline_number.number", $number);
                            $set("landline_code", $prefix);
                        }),
                    Step::make('Warehouse Addresses')
                        ->icon('heroicon-s-building-storefront')
                        ->schema([
                            Radio::make('warehouse_type')->options([
                                'owned' => new \Illuminate\Support\HtmlString(
                                    'Own Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with your own logistics,Dpharma incurs no extra costs,and you handle product delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                ),
                                'dpharma' => new \Illuminate\Support\HtmlString(
                                    'DPharma Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with DPharma logistics,our delivery partner manages product pickup and delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                ),
                            ])
                                ->rules(['required'])
                                ->required()
                                ->validationMessages([
                                    'required' => 'Warehouse Type is required',
                                ])
                                ->default('dpharma')
                                ->inline()
                                ->label('')->live(),
                                Checkbox::make('use_business_address')
                                    ->label('Use contact address as warehouse address')
                                    ->live()
                                    ->default(function () {
                                        $userAddress = $this->user->addresses->first();
                                        if (!$userAddress) {
                                            return false;
                                        }

                                        return $userAddress->address_1 === $this->data['ware_address_1'] &&
                                               $userAddress->address_2 === ($this->data['ware_address_2'] ?? null) &&
                                               $userAddress->state_id == $this->data['ware_state_id'] &&
                                               $userAddress->city_id == $this->data['ware_city_id'] &&
                                               $userAddress->postal_code === $this->data['ware_postal_code'];
                                        $userAddress->region === $this->data['ware_region'];

                                    })
                                    // Automatically check/uncheck when address fields change
                                    ->afterStateHydrated(function (Checkbox $component, Get $get) {
                                        $userAddress = $this->user->addresses->first();
                                        if (!$userAddress) {
                                            return;
                                        }

                                        $matches = $userAddress->address_1 === $get('ware_address_1') &&
                                                  $userAddress->address_2 === $get('ware_address_2') &&
                                                  $userAddress->state_id == $get('ware_state_id') &&
                                                  $userAddress->city_id == $get('ware_city_id') &&
                                                  $userAddress->postal_code === $get('ware_postal_code');
                                        $userAddress->region === $get('ware_region');


                                        $component->state($matches);
                                    })
                                    ->visible(fn (Get $get): bool => $get('warehouse_type') === 'dpharma')
                                    ->afterStateUpdated(function (Get $get, Set $set) {
                                        if ($get('use_business_address')) {
                                            // Get user's business address from database
                                            $userAddress = auth()->user()->addresses()->first();

                                            if ($userAddress) {
                                                $set('ware_address_1', $userAddress->address_1);
                                                $set('ware_address_2', $userAddress->address_2);
                                                $set('ware_state_id', $userAddress->state_id);
                                                $set('ware_city_id', $userAddress->city_id);
                                                $set('ware_postal_code', $userAddress->postal_code);
                                                $set('ware_region', $userAddress->region);
                                            }
                                        } else {
                                            // Clear fields if unchecked
                                            $set('ware_address_1', null);
                                            $set('ware_address_2', null);
                                            $set('ware_state_id', null);
                                            $set('ware_city_id', null);
                                            $set('ware_postal_code', null);
                                            $set('ware_region', null);
                                        }
                                    }),
                                Group::make()->schema([
                                    TextInput::make('min_order_value')
                                        ->rules(['numeric', 'min:1', 'max:100000', 'required'])
                                        ->label(new HtmlString("Minimum Order Value (Ringgit Malaysia) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'pattern' => '[0-9]*',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        ])
                                        ->validationMessages([
                                           'required' => 'The Minimum Order Value is required.',
                                           'min' => 'The Minimum Order Value must be at least 1.',
                                           'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                           'integer' => 'The Minimum Order Value must be an integer.',
                                        ])
                                    ->default($this->user->pcDetails->min_order_value ?? ''),
                                    TextInput::make('delivery_days')
                                        ->rules(['integer', 'min:1', 'max:7', 'required'])
                                        ->label(new HtmlString("ETA for East Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'pattern' => '[0-9]*',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Delivery Days is required.',
                                            'min' => 'The Delivery Days must be at least 1.',
                                            'max' => 'The Delivery Days may not be greater than 7.',
                                        ])
                                        ->default($this->user->pcDetails->delivery_days ?? ''),
                                    TextInput::make('delivery_days_west')
                                        ->rules(['integer', 'min:1', 'max:7', 'required'])
                                        ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'pattern' => '[0-9]*',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Delivery Days is required.',
                                            'min' => 'The Delivery Days must be at least 1.',
                                            'max' => 'The Delivery Days may not be greater than 7.',
                                        ])
                                        ->default($this->user->pcDetails->delivery_days_west ?? ''),
                                ])->columns(2)->visible(function (Get $get) {
                                    return $get('warehouse_type') === 'owned';
                                }),
                            Group::make()->schema([
                                TextInput::make('ware_address_1')
                                    ->placeholder('Enter address line 1')
                                    ->rules(['required', 'string', 'max:100'])
                                    ->label('Address Line 1')
                                    ->required(),
                                TextInput::make('ware_address_2')
                                    ->placeholder('Enter address line 2')
                                    ->rules(['nullable', 'string', 'max:100'])
                                    ->label('Address Line 2'),
                                // TextInput::make('ware_phone_number')
                                //     ->rules(['required', 'string', 'max:15', 'regex:/^\+?[0-9]{5,15}$/'])
                                //     ->label('Phone Number')
                                //     ->rules(['required', 'string', 'max:255'])
                                //     ->required(),


                                // TextInput::make('ware_district')
                                //     ->rules(['required', 'string', 'max:255'])
                                //     ->label('District')
                                //     ->required(),
                                Select::make('ware_state_id')
                                    ->placeholder('Select State')
                                    ->rules(['required'])
                                    ->label('State')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('country_id', 132)
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if (empty($state)) {
                                            $set('ware_region', null);
                                            $set('ware_city_id', null);
                                            $set('ware_postal_code', null);
                                            return;
                                        }
                                        $info = State::where('id', $get('ware_state_id'))->first();
                                        if ($info) {
                                            $set('ware_region', ucfirst($info->zone));
                                        }
                                        $set('ware_city_id', null);
                                    })
                                    ->required()->live(),
                                Select::make('ware_city_id')
                                    ->placeholder('Select City')
                                    ->rules(['required'])
                                    ->label('City')
                                    ->loadingMessage('Loading cities...')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('state_id', $get('ware_state_id'))
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->live()
                                    ->afterStateUpdated(function (Set $set) {
                                        $set('ware_postal_code', null); // Reset postal code when city changes
                                    })
                                    ->live(onBlur: true)
                                    ->options(function (Get $get) {
                                        if (! empty($get('ware_state_id'))) {
                                            return City::where('state_id', $get('ware_state_id'))->pluck('name', 'id')->toArray();
                                        }

                                        return [];
                                    })->required(),
                                Select::make('ware_postal_code')->label('Postal Code')->placeholder('Select postal code')
                                    ->options(function (Get $get) {
                                        if (!empty($get('ware_city_id'))) {
                                            return ZipCode::where('city_id', $get('ware_city_id'))->pluck('code', 'code');
                                        }
                                        return [];
                                    })
                                    ->required()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        if ($get('ware_city_id')) {
                                            return ZipCode::where('city_id', $get('ware_city_id'))
                                            ->where('code', 'like', "%{$search}%")
                                            ->pluck('code', 'code')
                                            ->toArray();
                                        }
                                        return [];
                                    })
                                    ->live(onBlur: true)
                                    ->optionsLimit(100)
                                    ->loadingMessage('Loading postal code...')
                                    ->searchable(),
                                // TextInput::make('ware_postal_code')
                                // ->extraAttributes([
                                //     'inputmode' => 'numeric',
                                //     'pattern' => '[0-9]*',
                                //     'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                //     'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                // ])
                                // ->required()->label('Postcode')->placeholder('Enter postcode')
                                // ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                // ->suffixIcon(fn ($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                // ->suffixIconColor(fn ($state) => strlen($state) === 5 ? 'success' : null)
                                // ->live(),
                                TextInput::make('ware_region')->label('Region')->disabled()->placeholder('Enter region'),
                                TextInput::make('ware_id')->visible(false),
                                // TextInput::make('min_order_value')
                                // ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'numeric', 'min:1', 'max:100000', 'required'])
                                // ->label(new HtmlString("Minimum Order Value (Ringgit Malaysia) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                // ->extraAttributes([
                                //     'inputmode' => 'numeric',
                                //     'pattern' => '[0-9.]*',
                                //     'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                //     'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "").replace(/(\..*)\./g, "$1")'
                                // ])
                                // ->validationAttribute('Minimum Order Value')
                                // ->validationMessages([
                                //     'required' => 'The Minimum Order Value is required.',
                                //     'min' => 'The Minimum Order Value must be at least 1.',
                                //     'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                // ]),
                            ])->columns(2)->visible(function (Get $get) {
                                return $get('warehouse_type') === 'dpharma';
                            }),
                        ])->afterValidation(function (Get $get, StoreProfileService $service) {
                            $data = [];
                            $data['phone_number'] = $get('ware_phone_number');
                            $data['address_1'] = $get('ware_address_1');
                            $data['address_2'] = $get('ware_address_2');
                            $data['district'] = $get('ware_district');
                            $data['state_id'] = $get('ware_state_id');
                            $data['postal_code'] = $get('ware_postal_code');
                            $data['city_id'] = $get('ware_city_id');
                            $data['warehouse_type'] = $get('warehouse_type');
                            $data['ware_id'] = $get('ware_id');
                            $data['ware_region'] = $get('ware_region');
                            // $data['delivery_days_west'] = $get('delivery_days_west');
                            // $data['delivery_days'] = $get('delivery_days');
                            // $data['min_order_value'] = $get('min_order_value');
                            PcDetail::updateOrCreate(['user_id' => auth()->user()->id], [
                                'delivery_days' => $get('delivery_days'),
                                'delivery_days_west' => $get('delivery_days_west'),
                                'min_order_value' => $get('min_order_value'),
                            ]);
                            $service->storeWareHouse($data);
                        }),
                    Step::make('Person In Charge')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('person_in_charge_name')
                                    ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                    ->placeholder('Enter the full name')
                                    ->live()
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'success' : null)
                                    ->validationMessages([

                                        'regex' => 'The Full Name must be a alphabetical.',
                                        'max' => 'The Full Name may not be greater than 50 characters.',
                                    ])
                                    ->label('Full Name'),
                                 TextInput::make('person_in_charge_nric')
                                    ->placeholder('Enter the NRIC / Passport Number')
                                    ->label(new HtmlString(
                                        'NRIC / Passport Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`National Registration Identity Card (NRIC) Number is a unique personal identification number assigned to Malaysian citizens and permanent residents`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['nullable', 'max:50'];
                                        }
                                        return ['nullable', 'max:50'];
                                    })
                                    ->live()
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                    ->validationMessages([
                                        'max' => 'The NRIC / Passport Number may not be greater than 50 characters.',
                                    ]),
                                TextInput::make('person_in_charge_email')
                                    ->placeholder('Enter email')
                                    ->required()
                                    ->rules(function (Get $get) {
                                        $user = Filament::auth()->user();
                                        return [
                                            'required',
                                            'email',
                                            'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                            function ($attribute, $value, $fail) use ($user) {
                                                // Check uniqueness in `person_in_charge_email`, `profile_email`, and `users` table email
                                                // exclude current user
                                                $existsInPcDetails = DB::table('pc_details')
                                                    ->whereNotIn('user_id', [$user->id])
                                                    ->where(function ($query) use ($value) {
                                                        $query->where('profile_email', $value)
                                                              ->orWhere('person_in_charge_email', $value);
                                                    })
                                                    ->first();

                                                $existsInUsers = DB::table('users')
                                                    ->where('id', '!=', $user->id)
                                                    ->where('email', $value)
                                                    ->first();

                                                if ($existsInPcDetails || $existsInUsers) {
                                                    $fail('The email has already been taken.');
                                                }
                                            },
                                        ];
                                    })
                                    ->label('Email')
                                    ->suffixIcon(function ($state) {
                                        return !empty($state) && filter_var($state, FILTER_VALIDATE_EMAIL) ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(fn ($state) => filter_var($state, FILTER_VALIDATE_EMAIL) ? 'success' : null)
                                    ->live(),
                                TextInput::make('person_in_charge_phone')->placeholder('Enter mobile number')->prefix('+60')
                                    ->label('Mobile Number')
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->rules(['required','digits_between:8,12'])
                                    ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->required()
                                    ->live(),

                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileService $service) {
                            $data = [];
                            $data['person_in_charge_name'] = $get('person_in_charge_name');
                            $data['person_in_charge_nric'] = $get('person_in_charge_nric');
                            $data['person_in_charge_phone'] = $get('person_in_charge_phone');
                            $data['person_in_charge_email'] = $get('person_in_charge_email');
                            $service->storePersonInCharge($data, 4);
                        }),
                    Step::make('Bank Details')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                Checkbox::make('is_credit_line')
                                    ->label(new HtmlString(
                                        'Enable Credit Line <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enable this option to allow the supplier to use a credit line.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->default(fn () => $this->user->pcDetails?->is_credit_line ?? false)->columnSpanFull()->reactive(),
                                TextInput::make('beneficiary_name')
                                    ->label(new HtmlString('Beneficiary Name <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Enter the beneficiary name')
                                    ->rules(['required', 'string', 'max:50', 'regex:/^[a-zA-Z\s]+$/'])
                                    ->live()
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('beneficiary_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('beneficiary_name')) ? 'success' : null)
                                    ->validationMessages([
                                        'required' => 'The Beneficiary Name field is required.',
                                        'regex' => 'The Beneficiary Name must be alphabetical.',
                                        'max' => 'The Beneficiary Name may not be greater than 50 characters.',
                                    ]),

                                Select::make('bank_name')
                                    ->label(new HtmlString('Bank Name <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Select bank')
                                    ->options(function (Get $get) {
                                        // If a search term is present, only show matching records
                                        $search = request()->input('search');
                                        if (!empty($search)) {
                                            return getBankNames($search);
                                        }
                                        return getBankNames();
                                    })
                                    ->live()
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        // Only return matching records for the search term
                                        return getBankNames($search);
                                    })
                                    ->validationMessages([
                                        'required' => 'The Bank Name field is required.',
                                    ])
,
                                TextInput::make('account_number')
                                    ->label(new HtmlString('Account Number <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Enter the account number')
                                    ->rules(['required', 'string', 'max:30', 'regex:/^[0-9]+$/'])
                                    ->live()
                                    ->suffixIcon(fn ($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'success' : null)
                                    ->validationMessages([
                                        'required' => 'The Account Number field is required.',
                                        'regex' => 'The Account Number must be numeric.',
                                        'max' => 'The Account Number may not be greater than 30 characters.',
                                    ]),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileService $service) {
                            $data = [];
                            $data['beneficiary_name'] = encryptTextParam($get('beneficiary_name'));
                            $data['bank_name'] = encryptTextParam($get('bank_name'));
                            $data['account_number'] = encryptTextParam($get('account_number'));
                            $data['is_credit_line'] = $get('is_credit_line', false);
                            $service->storeBankDetails($data);
                        }),
                    Step::make('Legal Documents')
                        ->icon('heroicon-o-document-text')
                        ->schema([
                            Group::make()->schema([
                                FileUpload::make('company_registration_certificate')
                                    ->label(new HtmlString(
                                        'Company Registration Certificate 
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->maxSize('2048')
                                    ->validationMessages([
                                        'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'max' => 'File size must not exceed 2 MB',
                                        'required' => 'The Company Registration Certificate is required',
                                    ])
                                    ->acceptedFileTypes([
                                        'image/jpeg',
                                        'image/png',
                                        'application/pdf',
                                    ])
                                    ->rules(['required', File::types(['jpeg', 'png', 'pdf'])])
                                    ->required()
                                    ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias').$this->user->pcDetails->id;
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('company_registration_certificate_'),
                                    ),
                                FileUpload::make('license_permit')
                                    ->label(new HtmlString(
                                        'Relevant certification
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->maxSize('2048')
                                    ->validationMessages([
                                        'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'max' => 'File size must not exceed 2 MB',
                                    ])
                                    ->acceptedFileTypes([
                                        'image/jpeg',
                                        'image/png',
                                        'application/pdf',
                                    ])
                                    ->rules(['nullable', File::types(['jpeg', 'png', 'pdf'])])
                                    // ->required()
                                    ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias').$this->user->pcDetails->id;
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('license_permit_'),
                                    ),
                            ])->columns(2),
                        ]),
                ])
                ->submitAction(
                    Action::make('submit')
                        ->label('Submit')
                        ->action('store')
                        ->color('primary')
                )
                ->startOnStep(function () {
                    if(!empty($this->user->rejection_reason) || empty($this->user->pcDetails)) {
                        return 1;
                    }
                    // return $this->user->pcDetails?->step === 4 ? 1 : ($this->user->pcDetails?->step ?? 1);
                    $currentStep = $this->user->pcDetails?->step ?? 1;
                    return min($currentStep + 1, 5);
                }),
        ])->statePath('data');
    }

    public function store(StoreProfileService $service)
    {
        $data['email'] = $this->user->email;
        $data['name'] = $this->user->pcDetails->business_name;
        activity()
        ->causedBy(auth()->user())
        ->useLog('users')
        ->performedOn($this->user)
        ->withProperties([
            'old' => [
                'status' => 'pending',
            ],
        ])
        ->log('Pharmaceutical Supplier ('.$this->user->pcDetails->company_name.') Onboarding has been completed');
        $service->storeDocuments($this->form->getState());
        sendEmailToAdmin($data);
        // return to_route('pc.register-success');
        return redirect()->route(ReviewProfile::getRouteName());
    }

    public function customButtonClicked()
    {
        $user = auth()->user();

        /** @var User $user */
        // if (empty($user->stripe_on_boarding_completed_at)) {
        Stripe::setApiKey(env('STRIPE_SECRET_KEY'));

        // if (empty($user->stripe_connect_id)) {
        /** @var Account $account */
        $account = Account::create([
            'type'         => 'express',
            'email'        => $user->email,
            'country'      => 'US',
            'capabilities' => [
                'card_payments' => ['requested' => true],
                'transfers'     => ['requested' => true],
            ],
            'settings'     => [
                'payouts' => [
                    'schedule' => [
                        'interval' => 'manual',
                    ],
                ],
            ],
        ]);
        // dd($account->id);
        //                 $user->stripe_connect_id = $account->id;
        //                 $user->save();
        // }
        PcDetail::where('user_id', $user->id)->update(['stripe_connect_id' => $account->id, 'stripe_on_boarding_status' => 'pending']);
        $user->fresh();
        // dd(route(EditProfile::getRouteName()));
        $onBoardLink = AccountLink::create([
            'account'     => $account->id,
            'refresh_url' => route('filament.pc.pages.onboarding'),
            'return_url'  => route('stripe.onboard-result', Crypt::encrypt($account->id)),
            'type'        => 'account_onboarding',
        ]);

        return redirect($onBoardLink->url);
        // }


        $loginLink = $this->stripeClient->accounts->createLoginLink($account->id, []);

        return redirect($loginLink->url);
    }
}
