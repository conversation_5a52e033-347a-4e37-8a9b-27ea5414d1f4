<?php

namespace App\Filament\Pc\Pages;

use App\Models\User;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Filament\Facades\Filament;
use Filament\Pages\SimplePage;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Hash;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Illuminate\Validation\Rules\Password;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;

class CreatePasswordPage extends SimplePage implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pc.pages.create-password-page';

    public $user;

    public ?array $data = [];

    public function mount($id): void
    {
        $id = decryptParam($id);
        $this->user = User::find($id);
        if (!$this->user) {
        Notification::make()
            ->title('Something went wrong, please try again later.')
            ->danger()
            ->send();

        redirect()->to('/login'); // or wherever appropriate
        return; // avoid further execution
    }
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make([
                TextInput::make('password')
                    ->label(new HtmlString('Password <span style="color: red;">*</span>'))
                    ->validationAttribute('Password')
                    ->helperText('Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
                    ->password()
                    ->revealable()
                    ->rules(['required', Password::defaults(), 'confirmed']),
                TextInput::make('password_confirmation')
                    ->label(new HtmlString('Confirm Password <span style="color: red;">*</span>'))
                    ->validationAttribute('Confirm Password')
                    ->helperText('Confirm Password must be 8+ characters with uppercase, lowercase, numbers, and symbols')
                    ->revealable()
                    ->password()
                    ->rules(['required']),
            ])
        ])->statePath('data');
    }

    public function submit(Request $request)
    {
        $this->validate();
        $roles = $this->user->roles->pluck('name')->toArray();
        $this->user->update([
            'password' => $this->data['password'],
        ]);
        $this->user->syncRoles($roles);

        Notification::make()
            ->body('The password has been created successfully.')
            ->success()
            ->send();

        // if (Str::contains($request->getUri(), 'pc')) {
        if (Str::contains($request->getUri(), [config('app.pc_url'),'suppliers', 'pc'])) {
            $redirectUrl = Filament::getPanel('pc')->getLoginUrl();
            return redirect()->to($redirectUrl);
        } else {
            $redirectUrl = Filament::getPanel('admin')->getLoginUrl();
            return redirect()->to($redirectUrl);
        }
    }
}
