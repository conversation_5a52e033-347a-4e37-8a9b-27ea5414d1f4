<?php

namespace App\Filament\Pc\Pages;

use App\Models\User;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ParentProfilePage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pc.pages.parent-profile-page';

    protected static ?string $navigationLabel = 'Parent Profile';

    protected static ?string $title = 'Parent Profile';

    public User $user;
    public $userAddress;
    public $userWarehouse;
    public $stateDetail;
    public $contactStateDetail;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->parent_id != null;
    }

    public function mount()
    {
        $userId = Auth::user();
        $this->user = User::find($userId->parent_id);
        $this->userAddress = $this->user->addresses()->where('is_onboarding', true)
            ->first();
        $this->userWarehouse = $this->user->warehouses()->first();
        $this->stateDetail = DB::table('states')->where('states.id', $this->userWarehouse->state_id)->join('countries', 'states.country_id', '=', 'countries.id')->select('states.*', 'countries.name as country_name')->first();
        $this->contactStateDetail = DB::table('states')->where('states.id', $this->userAddress->state_id)->join('countries', 'states.country_id', '=', 'countries.id')->select('states.*', 'countries.name as country_name')->first();
        // dd($this->user->pcDetails->company_registration_certificate);
    }
}
