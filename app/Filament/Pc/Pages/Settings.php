<?php

namespace App\Filament\Pc\Pages;

use App\Models\PcDetail;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Indianic\Settings\Models\GlobalSettings;
use Filament\Forms\Components\DateTimePicker;
use Carbon\Carbon;

class Settings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $view = 'filament.admin.pages.settings';

    // protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 6;

    public $contact_email;
    public $contact_number;
    public $mail_smtp_host;
    public $mail_smtp_driver;
    public $mail_smtp_port;
    public $mail_smtp_encryption;
    public $mail_smtp_username;
    public $mail_smtp_password;
    public $mail_smtp_mail_from_name;
    public $mail_smtp_mail_from_email;
    public $facebook_url;
    public $twitter_url;
    public $linkedin_url;
    public $instagram_url;
    public $timezone;
    public $date_format;
    public $time_format;
    public $min_order_value;
    public $delivery_days;
    public $delivery_days_west;
    public $expiry_soon;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('settings_view');
    }


    public function mount(): void
    {
        $user = Filament::auth()->user()->id;
        $globalSettings = GlobalSettings::all()->keyBy('name');

        $this->form = $this->form->fill([
            //General Settings
            'contact_email' => $globalSettings['contact_email']['value'] ?? '',
            'contact_number' => $globalSettings['contact_number']['value'] ?? '',

            //SMTP Settings
            'mail_smtp_host' => $globalSettings['mail_smtp_host']['value'] ?? '',
            'mail_smtp_driver' => $globalSettings['mail_smtp_driver']['value'] ?? '',
            'mail_smtp_port' => $globalSettings['mail_smtp_port']['value'] ?? '',
            'mail_smtp_encryption' => $globalSettings['mail_smtp_encryption']['value'] ?? '',
            'mail_smtp_username' => $globalSettings['mail_smtp_username']['value'] ?? '',
            'mail_smtp_password' => $globalSettings['mail_smtp_password']['value'] ?? '',
            'mail_smtp_mail_from_name' => $globalSettings['mail_smtp_mail_from_name']['value'] ?? '',
            'mail_smtp_mail_from_email' => $globalSettings['mail_smtp_mail_from_email']['value'] ?? '',
            //Social Settings
            'facebook_url' => $globalSettings['facebook_url']['value'] ?? '',
            'twitter_url' => $globalSettings['twitter_url']['value'] ?? '',
            'linkedin_url' => $globalSettings['linkedin_url']['value'] ?? '',
            'instagram_url' => $globalSettings['instagram_url']['value'] ?? '',

            //Date and Time Settings
            'timezone' => PcDetail::where('user_id', $user)->value('time_zone') ?? '',
            'date_format' => PcDetail::where('user_id', $user)->value('date_format') ?? 'd/m/Y',
            'time_format' => PcDetail::where('user_id', $user)->value('time_format') ?? '12-hour',
            'min_order_value' => PcDetail::where('user_id', $user)->value('min_order_value') ?? '0',
            'delivery_days' => PcDetail::where('user_id', $user)->value('delivery_days') ?? '0',
            'delivery_days_west' => PcDetail::where('user_id', $user)->value('delivery_days_west') ?? '0',
            'expiry_soon' =>  PcDetail::where('user_id', $user)->value('expiry_soon') ?? Carbon::now()->addDays(30)->format('Y-m-d H:i:s'),
        ]);
    }

    public function form(Form $form): Form
    {
        $user = Filament::auth()->user()->id;

        return $form
            ->schema([
              
                Section::make('Date and Time Setting')
                    ->schema([
                        // Select::make('timezone')
                        //     ->disableOptionWhen(fn() => auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->hasAnyPermission(['settings_update', 'settings_create']))
                        //     ->label('Time Zone')
                        //     ->options([
                        //         'UTC' => 'UTC',
                        //         'PST' => 'Pacific Standard Time',
                        //         'EST' => 'Eastern Standard Time',
                        //     ])
                        //     ->default('UTC')
                        //     ->columnSpan(1),
                        Select::make('date_format')
                            //->disableOptionWhen(fn() => auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->hasAnyPermission(['settings_update', 'settings_create']))
                            ->label('Date Format')
                            ->options([
                                'Y-m-d' => 'YYYY-MM-DD',
                                'd/m/Y' => 'DD/MM/YYYY',
                                'm/d/Y' => 'MM/DD/YYYY',
                                'F j, Y' => 'MONTH D, YYYY',
                            ])
                            ->default('d/m/Y')
                            ->columns(1),
                        Select::make('time_format')
                            //->disableOptionWhen(fn() => auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->hasAnyPermission(['settings_update', 'settings_create']))
                            ->label('Time Format')
                            ->options([
                                '12-hour' => '12-Hour (AM/PM)',
                                '24-hour' => '24-Hour',
                            ])
                            ->default('12-hour')
                            ->columns(1),
                        DateTimePicker::make('expiry_soon')
                            ->label('Expiry soon')
                            ->hint(new HtmlString("<span class='font-medium text-danger-600 dark:text-danger-400'></span>"))
                            ->placeholder('Select Expiry Soon date & time')
                            ->native(false)
                            ->columnSpan(1)
                            ->minDate(Carbon::today())
                            ->rules(['after_or_equal:today'])
                            ->suffixIcon('heroicon-o-calendar')
                            ->closeOnDateSelection(),

                        Actions::make([
                            ActionsAction::make('date_time_update')
                                ->label('Save')
                                ->visible(function () {
                                    $user = auth()->user();
                                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                                    return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('settings_datetime settings update');
                                })
                                ->action(function () {
                                    $this->validateSection('date_time');
                                    $user = Filament::auth()->user()->id;

                                    $data = $this->form->getState();

                                    //ActivityLog start

                                    $getReadableFormats = function ($dateFormat, $timeFormat) {
                                        $dateFormats = [
                                            'd/m/Y' => 'DD/MM/YYYY',
                                            'm/d/Y' => 'MM/DD/YYYY',
                                            'Y-m-d' => 'YYYY-MM-DD',
                                            'd-m-Y' => 'DD-MM-YYYY',
                                            'd.m.Y' => 'DD.MM.YYYY',
                                        ];

                                        $timeFormats = [
                                            '12-hour' => '12-hour format',
                                            '24-hour' => '24-hour format',
                                        ];

                                        return [
                                            'date_format' => $dateFormats[$dateFormat] ?? $dateFormat,
                                            'time_format' => $timeFormats[$timeFormat] ?? $timeFormat,
                                        ];
                                    };

                                    $oldValues = PcDetail::where('user_id', $user)->first() ?? new PcDetail();
                                    $oldData = [
                                        'date_format' => $oldValues->date_format,
                                        'time_format' => $oldValues->time_format,
                                        'expiry_soon' => $oldValues->expiry_soon,
                                    ];

                                    $newData = [
                                        'date_format' => $data['date_format'],
                                        'time_format' => $data['time_format'],
                                        'expiry_soon' => $data['expiry_soon'],
                                    ];

                                    $oldReadableFormats = $getReadableFormats($oldData['date_format'], $oldData['time_format']);
                                    $newReadableFormats = $getReadableFormats($newData['date_format'], $newData['time_format']);

                                    $oldDataReadable = [
                                        'date_format' => $oldReadableFormats['date_format'],
                                        'time_format' => $oldReadableFormats['time_format'],
                                        'expiry_soon' => $oldData['expiry_soon'],
                                    ];

                                    $newDataReadable = [
                                        'date_format' => $newReadableFormats['date_format'],
                                        'time_format' => $newReadableFormats['time_format'],
                                        'expiry_soon' => $newData['expiry_soon'],
                                    ];

                                    //ActivityLog end

                                    DB::table('pc_details')->updateOrInsert(
                                        ['user_id' => $user],
                                        [
                                            'date_format' => $data['date_format'],
                                            'time_format' => $data['time_format'],
                                            'expiry_soon' => $data['expiry_soon']

                                        ]
                                    );

                                    //ActivityLog start
                                    $changedData = array_diff_assoc($newDataReadable, $oldDataReadable);
                                    if (!empty($changedData)) {
                                        activity()
                                            ->causedBy(auth()->user())
                                            ->useLog('settings_update')
                                            ->performedOn($oldValues->exists ? $oldValues : null)
                                            ->withProperties([
                                                'old' => array_intersect_key(
                                                    array_filter($oldDataReadable, fn($value) => !is_null($value) && $value !== 'Not set'),
                                                    $changedData
                                                ),
                                                'attributes' => $changedData,
                                            ])
                                            ->log('Date and Time settings have been updated from the setting');
                                    }
                                    //ActivityLog end

                                    Notification::make()->title('Settings Updated')->body('Date and Time settings updated successfully.')->success()->send();
                                })
                        ])->columnSpan(2),
                    ])->columnSpan(1)->columns(2),

                // Minimum Order Value Section
                Section::make('Order Setting')
                    ->schema([
                        TextInput::make('min_order_value')
                            ->label('Minimum Order Value (Ringgit Malaysia)')
                            ->minValue(0)
                            ->maxValue(10000)
                            ->numeric()
                            ->helperText('Set the minimum order value for customers.')
                            ->extraInputAttributes([
                                'pattern' => '[0-9]*',
                                'inputmode' => 'numeric',
                                'oninput' => "this.value = this.value.replace(/[^0-9]/g, '')"
                            ]),
                        Actions::make([
                            ActionsAction::make('minimum_order_value_update')
                                ->label('Save')
                                ->visible(function () {
                                    $user = auth()->user();
                                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                                    return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('settings_order settings update');
                                })
                                ->action(function () {
                                    $this->validateSection('minimum_order_value');
                                    $user = Filament::auth()->user()->id;

                                    $data = $this->form->getState();

                                    //Activitylog start
                                    $oldValues = PcDetail::where('user_id', $user)->first() ?? new PcDetail();
                                    $oldData = [
                                        'min_order_value' => $oldValues->min_order_value ?? '0',
                                    ];

                                    $newData = [
                                        'min_order_value' => $data['min_order_value'],
                                    ];
                                    //Activitylog end

                                    DB::table('pc_details')->updateOrInsert(
                                        ['user_id' => $user],
                                        ['min_order_value' => $data['min_order_value']]
                                    );

                                    //Activitylog start
                                    $changedData = array_diff_assoc($newData, $oldData);
                                    if (!empty($changedData)) {
                                        activity()
                                            ->causedBy(auth()->user())
                                            ->useLog('settings_update')
                                            ->performedOn($oldValues->exists ? $oldValues : null)
                                            ->withProperties([
                                                'old' => array_intersect_key(
                                                    array_filter($oldData, fn($value) => !is_null($value)),
                                                    $changedData
                                                ),
                                                'attributes' => $changedData,

                                            ])
                                            ->log('Minimum Order Value has been updated from the setting');
                                    }
                                    //Activitylog end

                                    Notification::make()->title('Minimum Order Value Updated')->body('Minimum Order Value saved successfully.')->success()->send();
                                })
                        ]),
                    ])
                    ->visible(function () {
                        $user = auth()->user();
                        $firstWarehouse = $user->warehouses->first() ?? null;

                        if ($firstWarehouse !== null && $firstWarehouse->warehouse_type === 'owned') {
                            return true;
                        }
                    })
                    ->columnSpan(1)->columns(1),

                Section::make('ETA Setting')
                    ->schema([
                        TextInput::make('delivery_days_west')
                            // ->visible(function () {
                            //     $user = auth()->user();
                            //     if ($user->warehouses->first()->warehouse_type == 'owned') {
                            //         return true;
                            //     }
                            // })
                            ->rules(['integer', 'min:1', 'max:30', 'required'])
                            ->validationAttribute('delivery days west')
                            ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                            ->extraAttributes([
                                'inputmode' => 'numeric',
                                'pattern' => '[0-9]*',
                                'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                            ])
                            ->validationMessages([
                                'required' => 'The Delivery west Days is required.',
                                'min' => 'The Delivery Days west must be at least 1.',
                                'max' => 'The Delivery Days west may not be greater than 30.',
                            ])
                            ->default($this->user->pcDetails->delivery_days_west ?? ''),
                        TextInput::make('delivery_days')
                            ->rules(['integer', 'min:1', 'max:30', 'required'])
                            // ->visible(function () {
                            //     $user = auth()->user();
                            //     if ($user->warehouses->first()->warehouse_type == 'owned') {
                            //         return true;
                            //     }
                            // })
                            ->validationAttribute('delivery days')
                            ->label(new HtmlString("ETA for East Malaysia (Working Days) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                            ->extraAttributes([
                                'inputmode' => 'numeric',
                                'pattern' => '[0-9]*',
                                'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                            ])
                            ->validationMessages([
                                'required' => 'The Delivery Days is required.',
                                'min' => 'The Delivery Days must be at least 1.',
                                'max' => 'The Delivery Days may not be greater than 30.',
                            ])
                            ->default($this->user->pcDetails->delivery_days ?? ''),
                        Actions::make([
                            ActionsAction::make('delivery_days_update')
                                ->label('Save')
                                ->visible(function () {
                                    $user = auth()->user();
                                    $isPharmaceuticalCompany = isPharmaceuticalCompany();
                                    return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('settings_order settings update');
                                })
                                ->action(function () {
                                    $user = Filament::auth()->user()->id;

                                    $data = $this->form->getState();

                                    //ActivityLog start
                                    $oldValues = PcDetail::where('user_id', $user)->first() ?? new PcDetail();
                                    $oldData = [
                                        'delivery_days' => $oldValues->delivery_days ?? '0',
                                        'delivery_days_west' => $oldValues->delivery_days_west ?? '0',
                                    ];

                                    $newData = [
                                        'delivery_days' => $data['delivery_days'],
                                        'delivery_days_west' => $data['delivery_days_west'],
                                    ];
                                    //ActivityLog end

                                    DB::table('pc_details')->updateOrInsert(
                                        ['user_id' => $user],
                                        [
                                            'delivery_days' => $data['delivery_days'],
                                            'delivery_days_west' => $data['delivery_days_west']
                                        ]
                                    );

                                    //ActivityLog start
                                    $changedData = array_diff_assoc($newData, $oldData);
                                    if (!empty($changedData)) {
                                        activity()
                                            ->causedBy(auth()->user())
                                            ->useLog('settings_update')
                                            ->performedOn($oldValues->exists ? $oldValues : null)
                                            ->withProperties([
                                                'old' => array_intersect_key(
                                                    array_filter($oldData, fn($value) => !is_null($value)),
                                                    $changedData
                                                ),
                                                'attributes' => $changedData,
                                            ])
                                            ->log('Delivery Days have been updated from the setting');
                                    }
                                    //ActivityLog end

                                    Notification::make()->title('Delivery Days Updated')->body('Delivery Days saved successfully.')->success()->send();
                                })
                        ]),
                    ])
                    ->visible(function () {
                        $user = auth()->user();
                        $firstWarehouse = $user->warehouses->first() ?? null;
                        if ($firstWarehouse !== null && $firstWarehouse->warehouse_type === 'owned') {
                            return true;
                        }
                    })
                    ->columnSpan(1)->columns(1),

            ]);
    }

    protected function getFormActions(): array
    {
        return [
            // Action::make('update')->submit('Update'),
        ];
    }

    protected function validateSection(string $section): void
    {
        $rules = [];
        $messages = [];

        // Define validation rules for each section
        if ($section === 'date_time') {
            $rules = [
                'date_format' => ['required'],
                'time_format' => ['required'],
                'expiry_soon' => ['required', 'date', 'after_or_equal:today'],
            ];
            $messages = [
                'timezone.required' => 'The Timezone is required.',
                'date_format.required' => 'The Date Format is required.',
                'time_format.required' => 'The Time Format is required.',
                'expiry_soon.required' => 'The Expiry Soon field is required.',
            ];
        } else {
            $rules = [
                'min_order_value' => ['required', 'numeric'],
            ];
            $messages = [
                'min_order_value.required' => 'The Minimum Order Value is required.',
                'min_order_value.numeric' => 'The Minimum Order Value must be a valid number.',
            ];
        }

        // Apply the validation rules for the specified section
        $this->validate($rules, $messages);
    }
}
