<?php

namespace App\Filament\Pc\Pages;

use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Hash;
use Filament\Actions\Action;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Group;
use Filament\Forms\Form;
use Filament\Forms\Components\Section;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Password;
use Filament\Notifications\Notification;
use Filament\Facades\Filament;
use Illuminate\Support\HtmlString;

class UpdatePassword extends Page implements HasForms
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static bool $shouldRegisterNavigation = false;

    protected static string $view = 'filament.pc.pages.update-password';
    protected static ?string $title = 'Update Password';

    public ?string $current_password;

    public ?string $password;

    public ?string $password_confirmation;

    public function getBreadcrumbs(): array
    {
        return [
            MyProfile::getUrl() => "My Profile",
            3 => "Update Password",
        ];
    }


    public function form(Form $form): Form
    {

        return $form->schema([
            Section::make()->schema([
                Group::make()->schema([
                    TextInput::make('current_password')
                        ->rules(['required'])
                        ->label(new HtmlString('Current Password <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                        ->validationAttribute('Current Password')
                        ->exists(modifyRuleUsing: function (Exists $rule) {
                            return Hash::check($this->current_password, $this->user->password);
                        })
                        ->password()
                        ->revealable(),
                    TextInput::make('password')
                        ->label(new HtmlString('New Password <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                        ->validationAttribute('New Password')
                        ->rules([
                            'required',
                            'confirmed',
                            Password::defaults()->numbers()->symbols()
                        ])
                        ->password()
                        ->revealable(),
                    TextInput::make('password_confirmation')
                        ->label(new HtmlString('Confirm Password <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                        ->validationAttribute('Confirm Password')
                        ->rules(['required', 'same:password', Password::defaults()->numbers()->symbols()])
                        ->same('password')
                        ->password()
                        ->revealable(),
                ])->columns(3),
            ]),
        ]);
    }

    public function update()
    {
        $this->validate();
        if (! Hash::check($this->current_password, Filament::auth()->user()->password)) {
            return Notification::make()->title('Current password is wrong !')->success()->danger()->send();
        }
        if ($this->current_password === $this->password) {
            Notification::make()->title('The new password cannot be the same as the current password.')->danger()->send();
            return;
        }
        $user = Filament::auth()->user();
        $user->update(['password' => Hash::make($this->password)]);

        //Activity Log Start
        // activity()
        //     ->causedBy(auth()->user())
        //     ->useLog('password_update')
        //     ->log("Ps User {$user->name} password has been updated");
        //Activity Log End

        session()->put('user', $user->toArray());
        Notification::make()->title('Password updated successfully !')->success()->send();
        $this->password = '';
        $this->password_confirmation = '';
        $this->current_password = '';

        return redirect('/login');
    }
    protected function getFormActions(): array
    {
        return [
            Action::make('update')->submit('Update')->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-50 cursor-wait',
            ]),

            Action::make('cancel')
                ->label('Cancel')
                ->url(fn() => MyProfile::getUrl())
                ->color('gray')
        ];
    }
}
