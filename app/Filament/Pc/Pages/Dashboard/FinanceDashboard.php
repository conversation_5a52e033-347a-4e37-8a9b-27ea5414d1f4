<?php

namespace App\Filament\Pc\Pages\Dashboard;

use Filament\Pages\Page;
use App\Filament\Pc\Widgets\FinanceOverview;
use App\Filament\Pc\Widgets\RevenueChart;
use App\Filament\Pc\Widgets\RevenueList;
use App\Filament\Pc\Widgets\RevenueListByFacility;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;

class FinanceDashboard extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?string $navigationIcon = null;

    protected static ?int $navigationSort = 3;

    protected static ?string $title = 'Finance';

    protected static string $view = 'filament.admin.pages.finance-dashboard';

    public static function canAccess(): bool
    {
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_finance view');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            FinanceOverview::class,
            RevenueChart::class,
            RevenueList::class,
            // RevenueListByFacility::class,
        ];
    }
}
