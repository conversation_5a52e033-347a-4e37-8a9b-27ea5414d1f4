<?php

namespace App\Filament\Pc\Pages\Dashboard;

use App\Filament\Pc\Widgets\OrdersChart;
use App\Filament\Pc\Widgets\OrderStatsOverview;
use App\Filament\Pc\Widgets\RecentOrders;
use Filament\Navigation\NavigationItem;
use Filament\Pages\Dashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Pages\Page;

class OrderDashboard extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $title = 'Orders';

    protected static string $view = 'filament.admin.pages.order-dashboard';

    public static function canAccess(): bool
    {
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_order view');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            OrderStatsOverview::class,
            RecentOrders::class,
            OrdersChart::class,
        ];
    }

    public static function getNavigationItems(): array
    {
        return [
            NavigationItem::make('Orders')
                ->url(static::getUrl())
                ->isActiveWhen(
                    fn() =>
                    request()->routeIs('filament.pc.pages.dashboard') ||
                        request()->routeIs(static::getRouteName())
                )
                ->group('Dashboard'),
        ];
    }
}
