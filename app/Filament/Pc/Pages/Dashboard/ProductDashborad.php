<?php

namespace App\Filament\Pc\Pages\Dashboard;

use App\Filament\Pc\Widgets\ExpiredProducts;
use App\Filament\Pc\Widgets\ProductStatsOverview;
use App\Filament\Pc\Widgets\TopExpireProducts;
use App\Filament\Pc\Widgets\TopSellingProducts;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Pages\Page;

class ProductDashborad extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?string $navigationIcon = null;

    protected static ?int $navigationSort = 2;

    protected static ?string $title = 'Products';

    protected static string $view = 'filament.admin.pages.product-dashboard';

    public static function canAccess(): bool
    {
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_product view');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            ProductStatsOverview::class,
            TopSellingProducts::class,
            // LowSellingProducts::class,
            ExpiredProducts::class,
            TopExpireProducts::class,
        ];
    }
}
