<?php

namespace App\Filament\Pc\Pages;

use App\Models\Thread;
use App\Models\ThreadMessage;
use App\Models\User;
use App\Models\Order;
use App\Notifications\NewMessageNotification;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Pages\Page;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;

class ChatPage extends Page implements HasTable
{
    use InteractsWithTable, WithFileUploads;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-oval-left';
    protected static ?string $navigationLabel = 'Messages';
    protected static ?string $slug = 'messages';
    protected static ?string $model = Thread::class;

    public $selectedThread = null;
    public $messageText = '';
    public $messages = [];
    public $perPage = 15; // Number of messages to load per page
    public $page = 1; // Current page of messages
    public $hasMoreMessages = true;
    protected static string $view = 'filament.admin.pages.chat-page';
    public $attachedImages = [];
    public $attachedFiles = [];

    public function getTitle(): string
    {
        return '';
    }
    public static function shouldRegisterNavigation(): bool
    {
        return false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Thread::query()
                    ->where(function ($query) {
                        $query->where('sender_id', Auth::id())
                            ->orWhere('receiver_id', Auth::id());
                    })
                    ->with([
                        'messages' => fn($query) => $query->latest()->limit(1)
                    ])
                    ->orderByDesc(
                        ThreadMessage::whereColumn('thread_id', 'threads.id')
                            ->latest()
                            ->select('created_at')
                            ->limit(1)
                    )
            )

            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\Layout\Stack::make([
                        Tables\Columns\TextColumn::make('receiver.name')
                            ->label('')
                            ->formatStateUsing(
                                fn($record) =>
                                $record->sender_id === Auth::id()
                                    ? "{$record->receiver->name} - #{$record->order?->order_number}"
                                    : "{$record->sender->name} - #{$record->order?->order_number}"
                            )
                            ->searchable(),

                    ]),
                    Tables\Columns\TextColumn::make('lastMessage.created_at')
                        ->label('')
                        ->formatStateUsing(function (Thread $record) {
                            $lastMessage = $record->messages()->latest()->first();
                            if (!$lastMessage) {
                                return null;
                            }

                            $messageDate = Carbon::parse($lastMessage->created_at);
                            if ($messageDate->isToday()) {
                                return 'Today';
                            } elseif ($messageDate->isYesterday()) {
                                return 'Yesterday';
                            } else {
                                return $messageDate->format('Y-m-d');
                            }
                        })
                        ->alignEnd()
                        ->extraAttributes(['class' => 'text-xs text-gray-500']),
                ]),
                Tables\Columns\TextColumn::make('lastMessage.message')
                    ->label('')
                    ->limit(50)
                    ->formatStateUsing(function (Thread $record) {
                        $lastMessage = $record->messages()->latest()->first();
                        if ($lastMessage) {
                            return $lastMessage->message;
                        }
                        return 'No messages yet';
                    })
                    ->color('gray'),
                Tables\Columns\TextColumn::make('lastMessage.media')
                    ->label('')
                    ->icon(function (?Thread $record) {
                        if (!$record) {
                            return null;
                        }
                        $lastMessage = $record->messages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('chat-images')) {
                            return 'heroicon-o-photo'; // Icon for images
                        } elseif ($lastMessage && $lastMessage->hasMedia('chat-files')) {
                            return 'heroicon-o-document-text'; // Icon for PDFs/files
                        }
                        return null;
                    })
                    ->getStateUsing(function (?Thread $record) {
                        if (!$record) {
                            return null;
                        }

                        $lastMessage = $record->messages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('chat-images')) {
                            return 'Photo';
                        } elseif ($lastMessage && $lastMessage->hasMedia('chat-files')) {
                            $media = $lastMessage->getMedia('chat-files')->first();
                            return $media ? $media->name : null;
                        }
                        return null;
                    })
                    ->hidden(fn(?Thread $record) => !$record || (!$record->messages()->latest()->first()?->hasMedia('chat-images') && !$record->messages()->latest()->first()?->hasMedia('chat-files'))),

                Tables\Columns\TextColumn::make('unreadMessagesCount')
                    ->label('')
                    ->badge()
                    ->color('success')
                    ->state(function (Thread $record) {
                        $unreadCount = ThreadMessage::where('thread_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->alignEnd(),
            ])
            ->paginated([10, 25, 50])
            ->defaultSort('updated_at', 'desc')
            ->actions([
                Tables\Actions\Action::make('select')
                    ->label('')
                    ->action(function (Thread $record) {
                        $this->selectThread($record->id);
                    })
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->label('User')
                    ->options(User::pluck('name', 'id')) // Fetch users dynamically
                    ->preload()
                    ->searchable()
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'] ?? null,
                            fn($query, $userId) => $query->where(function ($query) use ($userId) {
                                $query->where('sender_id', $userId)
                                    ->orWhere('receiver_id', $userId);
                            })
                        );
                    }),


                // Add filter for order ID
                Tables\Filters\Filter::make('order_id')
                    ->form([
                        TextInput::make('order_id')
                            ->label('Order ID')
                            ->numeric()
                    ])
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['order_id'] ?? null,
                            fn($query, $orderId) => $query->where('order_id', $orderId)
                        );
                    })
            ])
            ->recordAction('select');
    }

    public function selectThread($threadId)
    {
        $this->selectedThread = Thread::find($threadId);
        $this->page = 1;
        $this->messages = [];
        $this->loadMessages();

        ThreadMessage::where('thread_id', $threadId)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    public function loadMessages()
    {
        if (!$this->selectedThread) {
            $this->messages = [];
            $this->hasMoreMessages = false;
            return;
        }

        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = ThreadMessage::where('thread_id', $this->selectedThread->id)
            ->with('sender')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = ThreadMessage::where('thread_id', $this->selectedThread->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);
    }

    public function easyLoadAllMessages()
    {
        $this->loadMessages(true);
    }

    // Livewire event listener for loading more messages
    #[On('load-more-messages')]
    public function loadMoreMessages()
    {
        if (!$this->hasMoreMessages || !$this->selectedThread) {
            return;
        }

        $this->page++;
        $this->loadMessages();
    }

    public function sendMessage()
    {
        if ((empty($this->messageText) && empty($this->attachedFiles)) || !$this->selectedThread) {
            return;
        }

        $validationPassed = $this->validateUploads();

        if (empty($this->messageText) && !$validationPassed) {
            Notification::make()
                ->title('Message not sent')
                ->body('Your message was not sent because the attached files are invalid.')
                ->danger()
                ->send();
            return;
        }

        $message = ThreadMessage::create([
            'thread_id' => $this->selectedThread->id,
            'from_id' => Auth::id(),
            'message' => $this->messageText,
            'is_read' => false,
        ]);

        if (!empty($this->attachedFiles)) {
            foreach ($this->attachedFiles as $file) {
                $mimeType = $file->getMimeType();
                $extension = strtolower($file->getClientOriginalExtension());

                $isPdf = $mimeType === 'application/pdf' ||
                    $mimeType === 'application/x-pdf' ||
                    $extension === 'pdf';

                $collection = $isPdf ? 'chat-files' : 'chat-images';

                $message->addMedia($file->getRealPath())
                    ->usingName($file->getClientOriginalName())
                    ->withCustomProperties(['mime_type' => $mimeType])
                    ->toMediaCollection($collection);
            }
        }

        $recipient = ($this->selectedThread->sender_id == Auth::id())
            ? User::find($this->selectedThread->receiver_id)
            : User::find($this->selectedThread->sender_id);

        // if ($recipient) {
        //     $recipient->notify(new App\Filament\Pc\Pages\NewMessageNotification($message));
        // }

        $this->messageText = '';
        $this->attachedFiles = [];
        $this->page = 1;
        $this->loadMessages();

        $this->dispatch('message-sent');
    }

    public function validateUploads()
    {
        if (empty($this->attachedFiles)) {
            return true;
        }

        if (count($this->attachedFiles) > 5) {
            Notification::make()
                ->title('Too many files')
                ->body('You can only upload up to 5 files at a time.')
                ->danger()
                ->send();

            $this->attachedFiles = array_slice($this->attachedFiles, 0, 5); // optionally limit
            return false;
        }

        $hasInvalidFiles = false;
        $invalidFiles = [];

        foreach ($this->attachedFiles as $key => $file) {
            $mimeType = $file->getMimeType();
            $extension = strtolower($file->getClientOriginalExtension());
            $fileSizeKB = $file->getSize() / 1024;

            $isPdf = in_array($mimeType, ['application/pdf', 'application/x-pdf']) || $extension === 'pdf';
            $isImage = strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png', 'gif']);

            $sizeLimitKB = 1024; // 1MB

            if ((!$isPdf && !$isImage) || $fileSizeKB > $sizeLimitKB) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            }
        }

        if ($hasInvalidFiles) {
            Notification::make()
                ->title('Invalid file(s)')
                ->body('Only images (JPG, PNG, GIF) and PDFs up to 1MB each are allowed.')
                ->danger()
                ->send();

            $this->attachedFiles = array_values($this->attachedFiles); // reindex array
        }

        return !$hasInvalidFiles;
    }

    public function updatedAttachedFiles()
    {
        $this->validateUploads();
    }

    public function removeFile($index)
    {
        if (isset($this->attachedFiles[$index])) {
            unset($this->attachedFiles[$index]);
            $this->attachedFiles = array_values($this->attachedFiles);
        }
    }
}
