<?php

namespace App\Filament\Pc\Pages;

use App\Models\User;
use Filament\Pages\Page;
use Filament\Forms;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Admin\Resources\UserResource;
use Filament\Facades\Filament;
use Filament\Actions\Action;

class ApprovalProfile extends Page
{


    protected static string $resource = UserResource::class;
    public $rejected_reason = "";
    protected static bool $shouldRegisterNavigation = false;

    public User $user;
    public array $data = [];
    protected static string $view = 'filament.pc.pages.approval-profile';

    public function getTitle(): string|Htmlable
    {
        return "";
    }



    public function mount()
    {

        $this->user = Filament::auth()->user();
        $this->user = User::with([
            // Explicitly load warehouses with their city/state
            'warehouses' => function ($query) {
                $query->with(['city', 'state']);
            },
            'pcDetails',
            'addresses',
            'addresses.city',
            'addresses.state',
            'address',
            'address.city',
            'address.state',
            'accounttype',
            'pharmaSuppliers'
        ])->find($this->user->id);
    }



    protected function getHeaderActions(): array
    {
        return [

        ];
    }
}
