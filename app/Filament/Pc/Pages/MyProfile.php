<?php

namespace App\Filament\Pc\Pages;

use App\Filament\Admin\Pages\MyProfile as PagesMyProfile;
use App\Forms\Components\PhoneWithPrefix;
use App\Mail\ApprovalForAdminMail;
use App\Models\Approval;
use App\Models\PcCertificateFile;
use App\Models\PcCompanyType;
use App\Models\PcDetail;
use App\Models\User;
use App\Models\ZipCode;
use App\OnboardingStep;
use App\Rules\PhoneWithPrefixRule;
use Carbon\Carbon;
use Dom\Text;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\DB;
use Filament\Infolists\Components\Group as InfoGroup;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Notifications\Notification;
use Google\Service\Books\Resource\Onboarding;
use Illuminate\Console\Concerns\InteractsWithIO;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\File;
use Indianic\Settings\Models\GlobalSettings;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;

class MyProfile extends Page implements HasForms
{
    use InteractsWithInfolists;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pc.pages.my-profile';
    public User $user;
    public $userAddress;
    public $userWarehouse;
    public $stateDetail;
    public $contactStateDetail;
    public $record;
    public $cityDetail;
    public $contactCityDetail;
    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin');
    }

    public function mount()
    {
        $this->user = Auth::user();
        $this->userAddress = $this->user->addresses()->where('is_onboarding', true)
            ->first();
        $this->userWarehouse = $this->user->warehouses()->first();
        $this->stateDetail = $this->userWarehouse ? DB::table('states')
            ->where('states.id', $this->userWarehouse->state_id)
            ->join('countries', 'states.country_id', '=', 'countries.id')
            ->select('states.*', 'countries.name as country_name')
            ->first() : null;
        $this->contactStateDetail = $this->userAddress ? DB::table('states')
            ->where('states.id', $this->userAddress->state_id)
            ->join('countries', 'states.country_id', '=', 'countries.id')
            ->select('states.*', 'countries.name as country_name')
            ->first() : null;
        $this->record = PcDetail::where('user_id', $this->user->id)->first();
        $this->cityDetail = $this->userWarehouse ? City::where('id', $this->userWarehouse->city_id)->first() : null;
        $this->contactCityDetail = $this->userAddress ? City::where('id', $this->userAddress->city_id)->first() : null;
        // dd($this->user->pcDetails->company_registration_certificate);
        // dd($this->user->email);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $approvals = (new static())->getStepOneChanges($this->user->id, OnboardingStep::BASIC_INFO);
        $stepTwoApprovals = (new static())->getStepOneChanges($this->user->id, OnboardingStep::ADDRESS);
        $stepThreeApprovals = (new static())->getStepOneChanges($this->user->id, OnboardingStep::DOCUMENTS);
        $stepFiveApprovals = (new static())->getStepOneChanges($this->user->id, OnboardingStep::CONTACT);
        $stepSixApprovals = (new static())->getStepOneChanges($this->user->id, OnboardingStep::PERSON_IN_CHARGE);

        $stepOneChanges = (new static())->organizeApprovalChanges($approvals);
        $stepTwoChanges = (new static())->organizeApprovalChanges($stepTwoApprovals);
        $stepThreeChanges = (new static())->organizeDocumentApprovalChanges($stepThreeApprovals);
        $stepFiveChanges = (new static())->organizeApprovalChanges($stepFiveApprovals);
        $stepSixChanges = (new static())->organizeApprovalChanges($stepSixApprovals);
        // dd($stepFiveChanges);
        return $infolist
            ->record($this->record)
            ->schema([
                InfoSection::make('Profile')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->label('Update Profile Image')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    FileUpload::make('photo')
                                        ->label('')
                                        ->image()
                                        ->imageEditor()
                                        ->circleCropper()
                                        ->directory('users')
                                        ->avatar()
                                        ->columnSpanFull()
                                        ->alignCenter()
                                        ->rules(['required', 'image', 'mimes:jpg,jpeg,png', 'max:2048']) // Image validation
                                        ->validationMessages([
                                            'required' => 'The avatar field is required.',
                                            'image' => 'The file must be an image.',
                                            'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                            'max' => 'The image must not exceed 2MB.'
                                        ])
                                        ->default($this->user->photo ? 'users/' . $this->user->photo : asset('/images/user-avatar.png')),

                                    // TextInput::make('name')
                                    //     ->label(new HtmlString("Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->rules(['required'])
                                    //     ->validationMessages([
                                    //         'required' => 'The name field is required.',
                                    //     ])
                                    //     ->default($this->user->name ?? ''),

                                    // TextInput::make('company_name')
                                    //     ->label(new HtmlString("Company Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->rules(['required'])
                                    //     ->readOnly()
                                    //     ->validationMessages([
                                    //         'required' => 'The company name field is required.',
                                    //     ])
                                    //     ->formatStateUsing(fn () => $this->user->pcDetails->company_name ?? '')
                                    //     ->default($this->user->pcDetails->company_name ?? ''),
                                    // TextInput::make('phone_number')
                                    //     ->numeric()
                                    //     ->prefix('+60')
                                    //     ->live()
                                    //     ->readOnly()
                                    //     ->mask('999999999999')
                                    //     ->stripCharacters(['-'])
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'numeric',
                                    //         'maxlength' => '12'
                                    //     ])
                                    //     ->validationMessages([
                                    //         'digits_between' => 'The phone number must be between :min and :max characters.',
                                    //         'required' => 'The phone number field is required.',
                                    //     ])
                                    //     ->label(new HtmlString('<span style="font-size: 14px !important;">Enter phone number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    //     ->placeholder('Phone Number')
                                    //     ->default($this->user->pcDetails->phone_number ?? '')
                                    //     ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    //     ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    //     ->rules(['required', 'digits_between:8,12']),

                                ])->columns(2)
                            ])
                            ->modalSubmitActionLabel('Save')
                            ->action(function (array $data) {
                                $this->user->update([
                                    'photo' => basename($data['photo']),
                                    // 'name' => $data['name'],
                                ]);

                                // Update the pc_details table
                                // $this->user->pcDetails->update([
                                //     'company_name' => $data['company_name'],
                                //     'phone_number' => $data['phone_number'],
                                // ]);

                                //Activity Log Start
                                activity()
                                    ->causedBy(auth()->user())
                                    ->useLog('users')
                                    ->performedOn($this->user)
                                    ->log('Profile photo updated');
                                //Activity Log End

                                Notification::make()
                                    ->title('Profile has been updated')
                                    ->success()
                                    ->send();

                                $this->redirect(MyProfile::getUrl());
                            })
                    ])
                    ->schema([
                        InfoGroup::make([
                            ImageEntry::make('photo')
                                ->label('')
                                // ->defaultImageUrl($this->user->photo)
                                ->default(Storage::disk('s3')->url('users/' . $this->user->photo))
                                ->width('138px')
                                ->height('138px')
                                ->circular()
                                ->extraAttributes(['class' => 'rounded-full'])
                                ->columnSpan(1),

                            InfoGroup::make([
                                // TextEntry::make('name')
                                //     ->label('Name')
                                //     ->formatStateUsing(fn () => $this->user->name)
                                //     ->default($this->user->name)
                                //     ->columnSpan(1),

                                TextEntry::make('company_name')
                                   ->label(new HtmlString(
                                        'Company Name <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`This name/field will be displayed in Dpharma Portal.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->default(fn() => $this->user->pcDetails->company_name ?? '-')
                                    ->formatStateUsing(fn() => $this->user->pcDetails->company_name ?? '-')
                                    ->columnSpan(1),

                                TextEntry::make('phone_number')
                                    ->label('Mobile Number')
                                    ->formatStateUsing(fn() => $this->user->pcDetails->phone_number ? '+' . $this->user->pcDetails->phone_number_code . ' ' . substr($this->user->pcDetails->phone_number, 0, 2) . substr($this->user->pcDetails->phone_number, 2) : '-')
                                    ->columnSpan(1),
                                TextEntry::make('is_active')
                                    ->label('Status')
                                    ->default(fn() => $this->user->is_active == 1 ? 'Active' : 'Inactive')
                                    ->badge()
                                    ->color(fn() => $this->user->is_active == 1 ? 'success' : 'danger')
                                    ->columnSpan(1),
                                TextEntry::make('commission_percentage')
                                    ->label('Commission to DP')
                                    ->formatStateUsing(
                                        fn() =>
                                        $this->user->pcDetails ?
                                            ($this->user->pcDetails->commission_type == 'flat' ? 'RM ' . $this->user->pcDetails->commission_percentage : $this->user->pcDetails->commission_percentage . '%')
                                            : '-'
                                    )
                                    ->columnSpan(1),

                                TextEntry::make('created_at')
                                    ->label('Created On')
                                    ->dateTime('M d, Y | H:i')
                                    // ->formatStateUsing(fn () => $this->user->created_at->format($this->user->pcDetails->date_format  . ' H:i' ?? 'd/m/Y | H:i'))
                                    ->columnSpan(1),
                                TextEntry::make('email')
                                    ->label('Email')
                                    ->formatStateUsing(fn() => $this->user->email)
                                    ->default($this->user->email)
                                    ->columnSpan(1),
                                TextEntry::make('admin_verified_on')
                                    ->label('Approved On')  
                                    ->default($this->user->admin_verified_on)                                                                     
                                    ->formatStateUsing(function ($state): string {
                                        if (empty($state)) {
                                            return '-';
                                        }                                
                                        $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                                        $convertedDate = Carbon::parse($state)->timezone($userTimezone);
                                
                                        return $convertedDate->format('M d, Y | H:i');
                                    })
                            ])
                                ->columns(4) // This ensures 4 fields per row
                                ->columnSpan(3),
                        ])
                            ->columns(4), // Main group also uses 4 columns
                    ]),

                // Company Details Section
                InfoSection::make('Company Details')
                    ->description(
                        empty($stepOneChanges)
                            ? ''
                            : '⚠️ Any changes made will be sent to the admin for verification. Updates will be reflected only after approval.'
                    )
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    TextInput::make('pcDetails.business_name')
                                        ->label(new HtmlString("Business Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->rules(['required'])
                                        ->validationMessages([
                                            'required' => 'The Business Name field is required.',
                                        ])
                                        ->default($this->user->pcDetails->business_name ?? ''),
                                    Select::make('pcDetails.company_type_id')
                                        ->placeholder('Select company type')
                                        ->validationAttribute('Company Type')
                                        ->rules(['required'])
                                        ->label(new HtmlString(
                                            'Company Type <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Select the type of company that best describes your business.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                            </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                        ))
                                        ->default($this->user->pcDetails->company_type_id ?? '')
                                        ->live()
                                        ->options(fn() => PcCompanyType::pluck('name', 'id')),

                                    TextInput::make('pcDetails.company_name')
                                        ->label(fn(Get $get) => 
                                            (intval($get('pcDetails.company_type_id') ?? $this->user->pcDetails->company_type_id) === 1)
                                                ? new HtmlString("Company Name")
                                                : new HtmlString("Company Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>")
                                        )
                                        ->rules(function (Get $get) {
                                            $companyTypeId = intval($get('pcDetails.company_type_id') ?? $this->user->pcDetails->company_type_id);
                                            if ($companyTypeId === 1) {
                                                return ['max:64', 'regex:/^[a-zA-Z\s]+$/'];
                                            }
                                            return ['required', 'max:64', 'regex:/^[a-zA-Z\s]+$/'];
                                        })
                                        ->validationMessages([
                                            'required' => 'The Company Name field is required.',
                                            'regex' => 'The Company Name must be a alphanumeric string.',
                                            'max' => 'The Company Name may not be greater than 64 characters.',
                                        ])
                                        ->default($this->user->pcDetails->company_name ?? ''),

                                    TextInput::make('pcDetails.company_registration_number')->live()
                                        ->label(fn(Get $get) =>
                                            (intval($get('pcDetails.company_type_id') ?? $this->user->pcDetails->company_type_id) === 1)
                                                ? new HtmlString("Company Registration Number")
                                                : new HtmlString("Company Registration Number <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>")
                                        )
                                        ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                        ->rules(function (Get $get) {
                                            $companyTypeId = intval($get('pcDetails.company_type_id') ?? $this->user->pcDetails->company_type_id);
                                            $baseRules = ['regex:/^[a-zA-Z0-9]+$/', 'min:2', 'max:20'];
                                            if (!empty($this->user->id)) {
                                                $uniqueRule = Rule::unique('pc_details', 'company_registration_number')->ignore($this->user->id, 'user_id');
                                            } else {
                                                $uniqueRule = 'unique:pc_details,company_registration_number';
                                            }
                                            if ($companyTypeId === 1) {
                                                return array_merge($baseRules, [$uniqueRule]);
                                            }
                                            return array_merge(['required'], $baseRules, [$uniqueRule]);
                                        })
                                        ->validationMessages([
                                            'required' => 'The Company Registration Number field is required.',
                                            'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                            'min' => 'The Company Registration Number must be at least 2 characters.',
                                            'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                            'unique' => 'The Company Registration Number has already been taken.',
                                        ])
                                        ->default($this->user->pcDetails->company_registration_number ?? ''),
                                    TextInput::make('pcDetails.tin_number')
                                        ->placeholder('Enter tin number')
                                        ->label(new HtmlString("TIN Number <span style='color: red;'>*</span>"))
                                        ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                        ->live()

                                        ->rules(function (Get $get) {
                                            if (!empty($this->user->id)) {
                                                return ['required', 'min:1', 'regex:/^[a-zA-Z0-9]+$/', 'max:20', Rule::unique('pc_details', 'tin_number')->ignore($this->user->id, 'user_id')];
                                            }
                                            return ['required', 'min:1', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'unique:pc_details,tin_number'];
                                        })
                                        ->validationMessages([
                                            'max' => 'The TIN number may not be greater than 20 characters.',
                                            'unique' => 'The TIN number has already been taken.',
                                            'regex' => 'The TIN number must be an alphanumeric string.',
                                            'min' => 'The TIN number must be at least 1 characters.',
                                            'required' => 'The TIN number field is required.',
                                        ])
                                        ->default($this->user->pcDetails->tin_number ?? ''),
                                    TextInput::make('pcDetails.sstc_number')->live()
                                        ->label(new HtmlString("SST Number"))
                                        ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                        ->placeholder('Enter SST registration number')
                                        ->rules(function (Get $get) {
                                            if (!empty($this->user->id)) {
                                                return ['nullable', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/', Rule::unique('pc_details', 'sstc_number')->ignore($this->user->id, 'user_id')];
                                            }
                                            return ['nullable', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/', 'unique:pc_details,sstc_number'];
                                        })
                                        ->validationMessages([
                                            'max' => 'The SST number may not be greater than 20 characters.',
                                            'unique' => 'The SST number has already been taken.',
                                            'regex' => 'The SST number must be an alphanumeric string.',
                                            'min' => 'The SST number must be at least 1 characters.',
                                        ])
                                        ->default($this->user->pcDetails->sstc_number ?? ''),

                                   
                                    
                                    // TextInput::make('userAddress.address_1')
                                    //     ->label(new HtmlString("Address <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->rules(['required', 'string', 'max:100'])
                                    //     ->validationMessages([
                                    //         'required' => 'The address field is required.',
                                    //         'string' => 'The address must be a string.',
                                    //         'max' => 'The address may not be greater than 100 characters.',
                                    //     ])
                                    //     ->default($this->userAddress->address_1 ?? ''),
                                    // Add other company fields
                                ])->columns(2)
                            ])

                            ->action(function (array $data) {
                                $oldValues = [
                                    'business_name' => $this->user->pcDetails->business_name ?? null,
                                    'company_name' =>  $this->user->pcDetails->company_name ?? null,
                                    'company_registration_number' => $this->user->pcDetails->company_registration_number ?? null,
                                    'tin_number' => $this->user->pcDetails->tin_number ?? null,
                                    'sst_number' => $this->user->pcDetails->sstc_number ?? null,
                                    'company_type_name' => $this->user->pcDetails->companyType->name ?? null,
                                    // 'is_credit_line' => $this->user->pcDetails->is_credit_line ?? null,
                                ];

                                $currentData = $this->getCurrentCompanyDetails();
                                $submittedData = $this->prepareSubmittedCompanyData($data);
                                // dd($submittedData);
                                if (!$this->hasCompanyChanges($currentData, $submittedData)) {
                                    Notification::make()
                                        ->title('No changes detected')
                                        ->warning()
                                        ->send();
                                    return;
                                }
                                $this->user->pcDetails->update(['is_version_pending' => true, 'is_restricted' => true]);

                                // $approvalData = array_merge(
                                //     $data['pcDetails'],
                                //     ['address_1' => $data['userAddress']['address_1']]
                                // );
                                $approvalData = $data['pcDetails'];

                                \App\Models\Approval::create([
                                    'approvalable_type' => 'App\Models\User',
                                    'approvalable_id' => $this->user->id,
                                    'new_data' => json_encode($approvalData),
                                    'user_type' => 'pc',
                                    'steps' => '1',
                                    'status' => 'pending'
                                ]);

                                //Activity Log Start
                                $newValues = [
                                    'business_name' => $data['pcDetails']['business_name'] ?? null,
                                    'company_name' => $data['pcDetails']['company_name'] ?? null,
                                    'company_registration_number' => $data['pcDetails']['company_registration_number'] ?? null,
                                    'tin_number' => $data['pcDetails']['tin_number'] ?? null,
                                    'sst_number' => $data['pcDetails']['sstc_number'] ?? null,
                                    // 'is_credit_line' => $data['pcDetails']['is_credit_line'] ?? null,
                                    'company_type_name' => PcCompanyType::find($data['pcDetails']['company_type_id'])?->name ?? null,
                                ];

                                $changedOld = [];
                                $changedNew = [];
                                foreach ($oldValues as $key => $oldValue) {
                                    if ($oldValue != $newValues[$key]) {
                                        $changedOld[$key] = $oldValue;
                                        $changedNew[$key] = $newValues[$key];
                                    }
                                }

                                if (!empty($changedOld)) {
                                    activity()
                                        ->causedBy(auth()->user())
                                        ->useLog('company_details')
                                        ->performedOn($this->user->pcDetails)
                                        ->withProperties([
                                            'old' => $changedOld,
                                            'attributes' => $changedNew,
                                        ])
                                        ->log('Company details have been submitted for approval');
                                }
                                //Activity Log End

                                Notification::make()
                                    ->title('Changes submitted for approval')
                                    ->success()
                                    ->send();
                                $this->sendApprovalEmail();
                                // $this->refresh();
                            })
                    ])
                    ->schema([
                        InfoGroup::make([
                            TextEntry::make('business_name')
                                ->label('Business Name')
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepOneChanges['business_name'] ?? null
                                    );
                                })
                                ->default(fn() => $this->user->pcDetails->business_name ?? '-'),

                            TextEntry::make('company_name')
                               ->label(new HtmlString(
                                        'Company Name <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`This name/field will be displayed in Dpharma Portal.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepOneChanges['company_name'] ?? null
                                    );
                                })
                                ->default(fn() => $this->user->pcDetails->company_name ?? '-'),
                            // TextEntry::make('profile_email')
                            //     ->label('Email')
                            //     ->formatStateUsing(fn () => $this->user->pcDetails->profile_email ?? '-'),
                            // TextEntry::make('web_url')
                            //     ->label('Website URL')
                            //     ->formatStateUsing(fn () => $this->user->pcDetails->web_url ?? '-'),
                            TextEntry::make('company_registration_number')
                                ->label('Company Registration Number')
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepOneChanges['company_registration_number'] ?? null
                                    );
                                })
                                ->default(fn() => $this->user->pcDetails->company_registration_number ?? '-'),
                            TextEntry::make('tin_number')
                                ->label('TIN Number')
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepOneChanges['tin_number'] ?? null
                                    );
                                })
                                ->default(fn() => $this->user->pcDetails->tin_number ?? '-'),
                            TextEntry::make('sstc_number')
                                ->label('SST Number')
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepOneChanges['sstc_number'] ?? null
                                    );
                                })
                                ->default(fn() => $this->user->pcDetails->sstc_number ?? '-'),
                            TextEntry::make('pcDetails.companyType.name')
                                ->default($this->user->pcDetails?->companyType?->name ?? '-')
                                // ->placeholder('Select company type')
                                ->label('Company Type')
                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                    return static::formatRelationshipWithPendingChanges(
                                        $state,
                                        $stepOneChanges['company_type_id'] ?? null,
                                        PcCompanyType::class
                                    );
                                })
                            // ->default($this->cityDetail->name ?? '-')
                            // ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                            //     return static::formatRelationshipWithPendingChanges(
                            //         $state,  // No need for null coalesce here - handled in function
                            //         $stepTwoChanges['warehouse_city'] ?? null,
                            //         City::class
                            //     );
                            // })
                            // ->formatStateUsing(fn () => $this->user->pcDetails->companyType->name ?? '-')
                            ,
                            // TextEntry::make('address_1')
                            //     ->label('Registered Address')
                            //     ->default($this->userAddress->address_1 ?? '-')
                            //     ->formatStateUsing(fn () => $this->userAddress->address_1 ?? ''),
                           
                        ])->columns(4)
                    ]),

                // Warehouse Address Section
                InfoSection::make('Warehouse Address')
                    // ->description(
                    //     empty($stepTwoChanges) 
                    //         ? ''
                    //         : '⚠️ Any changes made will be sent to the admin for verification. Updates will be reflected only after approval.'
                    // )
                    ->description(function () {
                        $desc = static::getWarehouseTypeChangeMessage($this->user);
                        if (($this->userWarehouse?->warehouse_type ?? null) === 'owned') {
                            $extra = 'Note: ETA and Minimum Order Value will be update from the Settings section';
                            $desc = $desc ? ($desc . ',' . $extra) : $extra;
                        }
                        
                        
                        return $desc;
                    })
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    Radio::make('warehouse_type')
                                        ->label('Logistics Type')
                                        ->options([
                                            'owned' => new \Illuminate\Support\HtmlString(
                                                'Own Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with your own logistics,Dpharma incurs no extra costs,and you handle product delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                            ),
                                            'dpharma' => new \Illuminate\Support\HtmlString(
                                                'DPharma Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with DPharma logistics,our delivery partner manages product pickup and delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                            ),
                                        ])
                                        ->rules(['required'])
                                        ->required()
                                        ->validationMessages([
                                            'required' => 'Warehouse Type is required',
                                        ])->live()
                                        ->default($this->userWarehouse?->warehouse_type),
                                ]),
                                Group::make()->schema([
                                    // TextInput::make('delivery_days_west')
                                    //     ->rules(['integer', 'min:1', 'max:30', 'required'])
                                    //     ->label(new HtmlString("ETA for West Malaysia <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'numeric',
                                    //         'pattern' => '[0-9]*',
                                    //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                    //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    //     ])
                                    //     ->readOnly()
                                    //     ->validationMessages([
                                    //         'required' => 'The Delivery Days is required.',
                                    //         'min' => 'The Delivery Days must be at least 1.',
                                    //         'max' => 'The Delivery Days may not be greater than 30.',
                                    //     ])
                                    //     ->default($this->user->pcDetails->delivery_days_west ?? ''),
                                    // TextInput::make('delivery_days')
                                    //     ->rules(['integer', 'min:1', 'max:30', 'required'])
                                    //     ->label(new HtmlString("ETA for East Malaysia <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'numeric',
                                    //         'pattern' => '[0-9]*',
                                    //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                    //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    //     ])
                                    //     ->readOnly()
                                    //     ->validationMessages([
                                    //         'required' => 'The Delivery Days is required.',
                                    //         'min' => 'The Delivery Days must be at least 1.',
                                    //         'max' => 'The Delivery Days may not be greater than 30.',
                                    //     ])
                                    //     ->default($this->user->pcDetails->delivery_days ?? ''),
                                    // TextInput::make('min_order_value')
                                    //     ->rules(['required', 'numeric', 'min:0.01', 'max:100000'])
                                    //     ->validationAttribute('min order value')
                                    //     ->label(new HtmlString("Minimum Order Value <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'decimal',
                                    //         'pattern' => '^\d+(?:\.\d+)?$',
                                    //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                    //         'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                    //     ])->readOnly()
                                    //     ->validationMessages([
                                    //         'required' => 'The Minimum Order Value is required.',
                                    //         'numeric' => 'The Minimum Order Value must be a decimal number.',
                                    //         'min' => 'The Minimum Order Value must be at least 0.01.',
                                    //         'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                    //     ])
                                    //     ->default($this->user->pcDetails->min_order_value ?? ''),
                                ])->columns(3)->visible(function (Get $get) {
                                    return $get('warehouse_type') === 'owned';
                                }),
                                Group::make()->schema([
                                    TextInput::make('userWarehouse.address_1')
                                        ->label(new HtmlString("Address1 <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->rules(['required', 'string', 'max:100'])
                                        ->validationMessages([
                                            'required' => 'The Address1 field is required.',
                                            'max' => 'The Address1 field must not be greater than 100 characters.',
                                        ])
                                        ->default($this->userWarehouse->address_1 ?? '')->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),
                                    TextInput::make('userWarehouse.address_2')
                                        ->label('Address2')
                                        ->rules(['nullable', 'string', 'max:100'])
                                        ->default($this->userWarehouse->address_2 ?? '')
                                        ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),
                                    Select::make('userWarehouse.state_id')
                                        ->label(fn() => new HtmlString('State <span style="color:red">*</span>'))
                                        ->rules(['required'])
                                        ->validationAttribute('State')
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search, Get $get) {
                                            return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                ->where('country_id', 132)
                                                ->pluck('name', 'id')
                                                ->toArray();
                                        })
                                        ->afterStateUpdated(function (Get $get, Set $set, $state) {

                                            $set('userWarehouse.city_id', null);
                                            $set('userWarehouse.postal_code', null);
                                        })
                                        ->live()
                                        ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                        ->default($this->userWarehouse->state_id ?? '')->live()
                                        ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),
                                    Select::make('userWarehouse.city_id')
                                        ->label('City')
                                        ->formatStateUsing(function () {
                                            return $this->userWarehouse->city_id ?? null;
                                        })
                                        ->searchable()
                                        ->getSearchResultsUsing(function (string $search, Get $get) {
                                            return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                ->where('state_id', $get('userWarehouse.state_id'))
                                                ->pluck('name', 'id')
                                                ->toArray();
                                        })
                                        ->label(fn() => new HtmlString('City <span style="color: red;">*</span>'))
                                        ->validationMessages([
                                            'required' => 'The City field is required.',
                                        ])
                                        ->live()
                                        ->afterStateUpdated(function (Set $set) {
                                            $set('userWarehouse.postal_code', null);
                                        })
                                        ->rules(['required'])
                                        ->options(function (Get $get) {
                                            if (! empty($get('userWarehouse.state_id'))) {
                                                return City::where('state_id', $get('userWarehouse.state_id'))->pluck('name', 'id')->toArray();
                                            }

                                            return [];
                                            // return City::where('state_id', $get('userWarehouse.state_id'))->pluck('name', 'id');
                                        })
                                        ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),
                                    // ->default($this->userWarehouse->city_id ?? ''),
                                    TextInput::make('stateDetail.country_name')
                                        ->label('Country')
                                        ->readOnly(true)
                                        ->default($this->stateDetail->country_name ?? 'Malaysia')
                                        ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),

                                    Select::make('userWarehouse.postal_code')->label('Postal Code')->placeholder('Select postal code')
                                        ->options(function (Get $get) {
                                            if (!empty($get('userWarehouse.city_id'))) {
                                                return ZipCode::where('city_id', $get('userWarehouse.city_id'))->pluck('code','code');
                                            }
                                            return [];
                                        })    
                                        ->formatStateUsing(function () {
                                            return $this->userWarehouse->postal_code ?? null;
                                        })                                           
                                        ->required()
                                        ->getSearchResultsUsing(function (string $search,Get $get) {
                                            if ($get('userWarehouse.city_id')) {
                                                return ZipCode::where('city_id', $get('userWarehouse.city_id'))
                                                ->where('code', 'like', "%{$search}%")
                                                ->pluck('code', 'code')
                                                ->toArray();
                                            }
                                            return [];
                                        }) 
                                        ->live(onBlur: true)
                                        ->optionsLimit(100)
                                        ->loadingMessage('Loading postal code...')
                                        ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma')
                                        ->searchable(),
                                    // TextInput::make('userWarehouse.postal_code')->live()
                                    //     ->label(fn() => new HtmlString('Postal Code <span style="color: red;">*</span>'))
                                    //     ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                    //     ->placeholder('Enter postcode')
                                    //     ->validationMessages([
                                    //         'required' => 'The Postal Code field is required.',
                                    //         'digits' => 'The Postal Code must be 5 digits.',
                                    //         'regex' => 'The Postal Code must be a number.',
                                    //     ])
                                    //     ->suffixIcon(fn($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                    //     ->suffixIconColor(fn($state) => strlen($state) === 5 ? 'success' : null)
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'numeric',
                                    //         'pattern' => '[0-9]*',
                                    //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                    //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    //     ])
                                    //     ->default($this->userWarehouse->postal_code ?? '')
                                    //     ->visible(fn(Get $get) => $get('warehouse_type') === 'dpharma'),
                                    // TextInput::make('pcDetails.delivery_days')
                                    //     ->rules(['string', 'max:10', 'regex:/^\+?[0-9]{1,2}$/'])
                                    //     ->label(fn () => new HtmlString('ETA for East Malaysia'))
                                    //     ->validationMessages([
                                    //         'required' => 'The Delivery Days is required.',
                                    //         'max' => 'The Delivery Days may not be greater than 10 characters.',
                                    //         'regex' => 'The Delivery Days must be a number.',
                                    //     ])
                                    //     ->default($this->user->pcDetails->delivery_days ?? ''),
                                    // TextInput::make('pcDetails.delivery_days_west')
                                    //     ->rules(['string', 'max:10', 'regex:/^\+?[0-9]{1,2}$/'])
                                    //     ->label(fn () => new HtmlString('ETA for West Malaysia'))
                                    //     ->validationMessages([
                                    //         'required' => 'The Delivery Days is required.',
                                    //         'max' => 'The Delivery Days may not be greater than 10 characters.',
                                    //         'regex' => 'The Delivery Days must be a number.',
                                    //     ])
                                    //     ->default($this->user->pcDetails->delivery_days_west ?? ''),
                                ])->columns(3)
                            ])
                            ->action(function (array $data) {
                                $currentData = $this->getCurrentWarehouseData();
                                $submittedData = $this->prepareSubmittedWarehouseData($data);

                                if (!$this->hasChanges($currentData, $submittedData)) {
                                    Notification::make()
                                        ->title('No changes detected')
                                        ->warning()
                                        ->send();
                                    return;
                                }
                                $this->user->pcDetails->update(['is_version_pending' => true, 'is_restricted' => true]);

                                if ($data['warehouse_type'] === 'dpharma') {
                                    $approvalData = [
                                        'warehouse_address_1' => $data['userWarehouse']['address_1'],
                                        'warehouse_address_2' => $data['userWarehouse']['address_2'],
                                        'warehouse_state' => $data['userWarehouse']['state_id'],
                                        'warehouse_city' => $data['userWarehouse']['city_id'],
                                        'warehouse_country' => $data['stateDetail']['country_name'],
                                        'warehouse_postal_code' => $data['userWarehouse']['postal_code'],
                                        'warehouse_type' => $data['warehouse_type'],
                                        // 'delivery_days' => $data['pcDetails']['delivery_days'],
                                        // 'delivery_days_west' => $data['pcDetails']['delivery_days_west'],
                                    ];
                                } else {
                                    $approvalData = [
                                        'warehouse_type' => $data['warehouse_type'],
                                    ];
                                }
                                \App\Models\Approval::create([
                                    'approvalable_type' => 'App\Models\User',
                                    'approvalable_id' => $this->user->id,
                                    'new_data' => json_encode($approvalData),
                                    'original_data' => json_encode($this->userWarehouse),
                                    'user_type' => 'pc',
                                    'steps' => OnboardingStep::ADDRESS,
                                    'status' => 'pending'
                                ]);

                                //Activity Log Start
                                $changedOld = [];
                                $changedNew = [];
                                foreach ($currentData as $key => $oldValue) {
                                    if ($oldValue != $submittedData[$key]) {
                                        $changedOld[$key] = $oldValue;
                                        $changedNew[$key] = $submittedData[$key];
                                    }
                                }
                                activity()
                                    ->causedBy(auth()->user())
                                    ->performedOn($this->user)
                                    ->useLog('warehouse_update')
                                    ->withProperties([
                                        'old' => $changedOld,
                                        'attributes' => $changedNew
                                    ])
                                    ->log('Warehouse details have been submitted for approval');
                                //Activity Log End

                                Notification::make()
                                    ->title('Changes submitted for approval')
                                    ->success()
                                    ->send();
                                $this->sendApprovalEmail();
                                return redirect()->route(MyProfile::getRouteName());
                                // $this->refresh();
                            }),
                        \Filament\Infolists\Components\Actions\Action::make('eta')
                            ->label('Edit ETA')
                            ->color('gray')
                            ->visible(false)
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    TextInput::make('pcDetails.delivery_days')
                                        ->rules(['integer', 'min:1', 'max:7', 'required'])
                                        ->label(new HtmlString("ETA for East Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'pattern' => '[0-9]*',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Delivery Days is required.',
                                            'min' => 'The Delivery Days must be at least 1.',
                                            'max' => 'The Delivery Days may not be greater than 7.',
                                        ])
                                        ->default($this->user->pcDetails->delivery_days ?? ''),
                                    TextInput::make('pcDetails.delivery_days_west')
                                        ->rules(['integer', 'min:1', 'max:7', 'required'])
                                        ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'pattern' => '[0-9]*',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Delivery Days is required.',
                                            'min' => 'The Delivery Days must be at least 1.',
                                            'max' => 'The Delivery Days may not be greater than 7.',
                                        ])
                                        ->default($this->user->pcDetails->delivery_days_west ?? ''),
                                    TextInput::make('pcDetails.min_order_value')
                                        ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'numeric', 'min:1', 'max:100000', 'required'])
                                        ->validationAttribute('min order value')
                                        ->label(new HtmlString("Minimum Order Value (Ringgit Malaysia) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->extraAttributes([
                                            'inputmode' => 'decimal',
                                            'pattern' => '^\d+(?:\.\d+)?$',
                                            'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                            'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Minimum Order Value is required.',
                                            'numeric' => 'The Minimum Order Value must be a decimal number.',
                                            'min' => 'The Minimum Order Value must be at least 1.',
                                            'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                        ])->columns(3)
                                        ->default($this->user->pcDetails->min_order_value ?? ''),
                                ])->columns(2)
                            ])
                            ->action(function (array $data) {
                                $this->user->pcDetails->update(['delivery_days' => $data['pcDetails']['delivery_days'], 'delivery_days_west' => $data['pcDetails']['delivery_days_west'], 'min_order_value' => $data['pcDetails']['min_order_value']]);

                                //Activity Lod Start
                                // activity()
                                //->causedBy(auth()->user())
                                //     ->performedOn($this->user->pcDetails)
                                //     ->useLog('eta_update')
                                //     ->withProperties([
                                //         'old' => collect($this->user->pcDetails->toArray())
                                //             ->only(['delivery_days', 'delivery_days_west', 'min_order_value'])
                                //             ->filter(fn($v) => !is_null($v))
                                //             ->all(),
                                //         'attributes' => collect($data['pcDetails'])
                                //             ->only(['delivery_days', 'delivery_days_west', 'min_order_value'])
                                //             ->filter(fn($v) => !is_null($v))
                                //             ->all(),
                                //     ])
                                //     ->log('ETA has been submitted for approval');
                                //Activity Lod End

                                Notification::make()
                                    ->title('ETA updated successfully')
                                    ->success()
                                    ->send();
                            })
                    ])
                    ->schema([
                        InfoGroup::make([
                            TextEntry::make('warehouse_type')
                                ->label('Logistics Type')
                                ->formatStateUsing(fn() => 'Own Logistics')
                                // ->columnSpanFull()
                                ->default('Own Logistics')
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type === 'owned'),
                            TextEntry::make('delivery_days')
                                ->formatStateUsing(fn() => isset($this->user->pcDetails->delivery_days) ? $this->user->pcDetails->delivery_days . ($this->user->pcDetails->delivery_days > 1 ? ' Days' : ' Day') : '-')
                                ->default(isset($this->user->pcDetails->delivery_days) ? $this->user->pcDetails->delivery_days . ($this->user->pcDetails->delivery_days > 1 ? ' Days' : ' Day') : '-')
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type === 'owned')
                                ->label('ETA for East Malaysia (Working Days)'),
                            TextEntry::make('delivery_days_west')
                                ->formatStateUsing(fn() => isset($this->user->pcDetails->delivery_days_west) ? $this->user->pcDetails->delivery_days_west . ($this->user->pcDetails->delivery_days_west > 1 ? ' Days' : ' Day') : '-')
                                ->default(isset($this->user->pcDetails->delivery_days_west) ? $this->user->pcDetails->delivery_days_west . ($this->user->pcDetails->delivery_days_west > 1 ? ' Days' : ' Day') : '-')
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type === 'owned')
                                ->label('ETA for West Malaysia (Working Days)'),
                            TextEntry::make('min_order_value')
                                ->default(!empty($this->user->pcDetails->min_order_value) ? $this->user->pcDetails->min_order_value : '-')
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type === 'owned')
                                ->label('Minimum Order Value (Ringgit Malaysia)'),
                            TextEntry::make('warehouse_type')
                                ->default(ucfirst($this->userWarehouse->warehouse_type ?? "-"))
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned')
                                ->label('Logistics Type'),
                            TextEntry::make('warehouse_address_1')
                                ->label('Street 1')
                                ->default($this->userWarehouse->address_1 ?? '-')
                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepTwoChanges['warehouse_address_1'] ?? null
                                    );
                                })
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            TextEntry::make('warehouse_address_2')
                                ->label('Street 2')
                                ->default($this->userWarehouse->address_2 ?? '-')
                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepTwoChanges['warehouse_address_2'] ?? null
                                    );
                                })
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            TextEntry::make('warehouse_state')
                                ->label('State')
                                ->default($this->stateDetail->name ?? '-')
                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                    return static::formatRelationshipWithPendingChanges(
                                        $state,
                                        $stepTwoChanges['warehouse_state'] ?? null,
                                        State::class
                                    );
                                })
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            TextEntry::make('warehouse_city')
                                ->label('City')
                                ->default($this->cityDetail->name ?? '-')
                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                    return static::formatRelationshipWithPendingChanges(
                                        $state,  // No need for null coalesce here - handled in function
                                        $stepTwoChanges['warehouse_city'] ?? null,
                                        City::class
                                    );
                                })
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            TextEntry::make('warehouse_country')
                                ->label('Country')
                                ->default($this->stateDetail->country_name ?? '-')
                                ->formatStateUsing(fn() => $this->stateDetail->country_name ?? '')
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            TextEntry::make('warehouse_postal_code')
                                ->label('Postal Code')
                                ->default($this->userWarehouse->postal_code ?? '-')
                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepTwoChanges['warehouse_postal_code'] ?? null
                                    );
                                })
                                ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            // TextEntry::make('pcDetails.delivery_days')
                            //     ->label('ETA for East Malaysia')
                            //     ->default(isset($this->user->pcDetails->delivery_days) ? $this->user->pcDetails->delivery_days . ($this->user->pcDetails->delivery_days > 1 ? ' Days' : ' Day') : '')
                            //     ->visible(fn () => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            // TextEntry::make('pcDetails.delivery_days_west')
                            //     ->label('ETA for West Malaysia')
                            //     ->default(isset($this->user->pcDetails->delivery_days_west) ? $this->user->pcDetails->delivery_days_west . ($this->user->pcDetails->delivery_days_west > 1 ? ' Days' : ' Day') : '')
                            //     ->visible(fn () => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                            // TextEntry::make('min_order_value')
                            //     ->default(!empty($this->user->pcDetails->min_order_value) ? $this->user->pcDetails->min_order_value : '-')
                            //     ->label('Minimum Order Value (Ringgit Malaysia)')
                            //     ->visible(fn() => $this->userWarehouse && $this->userWarehouse->warehouse_type !== 'owned'),
                        ])->columns(4)
                    ]),

                // Contact Details Section
                InfoSection::make('Contact Details')
                    ->description(
                        empty($stepFiveChanges)
                            ? ''
                            : '⚠️ Any changes made will be sent to the admin for verification. Updates will be reflected only after approval.'
                    )
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form(function ($record) {
                                $address = ($record->userAddress);
                                return [
                                    Group::make()->schema([
                                        TextInput::make('userAddress.address_1')
                                            ->label(new HtmlString("Address1 <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                            ->rules(['required', 'string', 'max:100'])
                                            ->validationMessages([
                                                'required' => "The Address1 field is required.",
                                                'max' => 'The Address1 must not exceed 100 characters.'
                                            ])
                                            ->default($this->userAddress->address_1 ?? ''),
                                        TextInput::make('userAddress.address_2')
                                            ->label('Address2')
                                            ->rules(['nullable', 'string', 'max:100'])
                                            ->default($this->userAddress->address_2 ?? ''),
                                        Select::make('userAddress.state_id')
                                            ->label(new HtmlString("State <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                            ->rules(['required'])
                                            ->searchable()
                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                    ->where('country_id', 132)
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->validationMessages([
                                                'required' => "The State field is required."
                                            ])
                                            ->afterStateUpdated(function (Set $set, Get $get) {
                                                $info = State::where('id', $get('userAddress.state_id'))->first();

                                                if ($info) {
                                                    $set('region', ucfirst($info->zone));
                                                } else {
                                                    $set('region', null);
                                                }
                                                $set('userAddress.city_id', null); // Reset city when state changes
                                                $set('userAddress.postal_code', null);
                                                $set('landline_number.prefix', null); // Reset landline code when state changes
                                            })
                                            ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                            ->default($this->userAddress->state_id ?? '')->live(),
                                        Select::make('userAddress.city_id')
                                            ->label(new HtmlString("City <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                            ->default($this->userAddress->city_id ?? '')->reactive()
                                            ->rules(['required'])
                                            ->loadingMessage('Loading cities...')
                                            ->searchable()
                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                    ->where('state_id', $get('userAddress.state_id'))
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->validationMessages([
                                                'required' => "The City field is required."
                                            ])
                                            ->live()
                                            ->afterStateUpdated(function (Set $set, Get $get) {
                                                $set('userAddress.postal_code', null); 
                                                // When city changes, update landline code if available
                                                $cityId = $get('userAddress.city_id');
                                                if ($cityId) {
                                                    $city = City::find($cityId);
                                                    if ($city && $city->landline_code) {
                                                        $set('landline_number.prefix', $city->landline_code);
                                                    } else {
                                                        $set('landline_number.prefix', null);
                                                    }
                                                } else {
                                                    $set('landline_number.prefix', null);
                                                }
                                            })
                                            ->formatStateUsing(function () {
                                                return $this->userAddress->city_id ?? null;
                                            })
                                            ->options(function (Get $get) {
                                                if (! empty($get('userAddress.state_id'))) {
                                                    return City::where('state_id', $get('userAddress.state_id'))->pluck('name', 'id')->toArray();
                                                }
                                                return [];
                                            }),
                                        TextInput::make('contactStateDetail.country_name')
                                            ->label('Country')
                                            ->readOnly(true)
                                            ->default($this->contactStateDetail->country_name ?? ''),
                                        Select::make('userAddress.postal_code')->label('Postal Code')->placeholder('Select postal code')
                                            ->options(function (Get $get) {        
                                                if (!empty($get('userAddress.city_id'))) {        
                                                    return ZipCode::where('city_id', $get('userAddress.city_id'))->pluck('code','code');
                                                }
                                                return [];
                                            })        
                                            ->formatStateUsing(function () {
                                                return $this->userAddress->postal_code ?? null;
                                            })                                       
                                            ->required()
                                            ->getSearchResultsUsing(function (string $search,Get $get) {
                                                if ($get('userAddress.city_id')) {
                                                    return ZipCode::where('city_id', $get('userAddress.city_id'))
                                                    ->where('code', 'like', "%{$search}%")
                                                    ->pluck('code', 'code')
                                                    ->toArray();                                                   
                                                }
                                                return [];
                                            }) 
                                            ->live(onBlur: true)
                                            ->optionsLimit(100)
                                            ->loadingMessage('Loading postal code...')
                                            ->searchable(),
                                        TextInput::make('pcDetails.phone_number')
                                            ->prefix($this->user->pcDetails?->phone_code ?? '')
                                            ->prefix('+60')
                                            ->live()
                                            ->label(new HtmlString("Mobile Number <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                            ->validationMessages([
                                                'required' => "The Mobile Number field is required.",
                                                'digits_between' => 'The Mobile Number must be between 8 and 12 digits.'
                                            ])
                                            ->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])
                                            ->rules(['required', 'digits_between:8,12'])
                                            ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->default($this->user->pcDetails->phone_number ?? ''),
                                        PhoneWithPrefix::make('landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8'
                                            ])
                                            ->prefixOptions(function (Get $get, Set $set) {
                                                // Show landline code based on state_id and city_id
                                                $stateId = $get('userAddress.state_id');
                                                $cityId = $get('userAddress.city_id');
                                                $codes = [];
                                                if ($cityId) {
                                                    $city = City::find($cityId);
                                                    if ($city && $city->landline_code) {
                                                        $codes[$city->landline_code] = $city->landline_code;
                                                    }
                                                }
                                                if (empty($codes) && $stateId) {
                                                    $codes = City::where('state_id', $stateId)
                                                        ->whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                if (empty($codes)) {
                                                    $codes = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                return $codes;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {
                                                // Set initial value for landline_number
                                                $address = $get('userAddress');
                                                if (is_array($address)) {
                                                    if (isset($address['landline_code'])) {
                                                        $set("landline_number.prefix", $address['landline_code']);
                                                    }
                                                    if (isset($address['landline_number'])) {
                                                        $set("landline_number.number", $address['landline_number']);
                                                    }
                                                } elseif (is_object($address)) {
                                                    if (isset($address->landline_code)) {
                                                        $set("landline_number.prefix", $address->landline_code);
                                                    }
                                                    if (isset($address->landline_number)) {
                                                        $set("landline_number.number", $address->landline_number);
                                                    }
                                                }
                                            })
                                            ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                // If city changes, update landline code
                                                $cityId = $get('userAddress.city_id');
                                                if ($cityId) {
                                                    $city = City::find($cityId);
                                                    if ($city && $city->landline_code) {
                                                        $set('landline_number.prefix', $city->landline_code);
                                                    }
                                                }
                                            })
                                            ->formatStateUsing(function ($state) use ($address) {
                                                if (is_array($address)) {
                                                    if (isset($address['landline_code'])) {
                                                        $state['prefix'] = $address['landline_code'];
                                                    }
                                                    if (isset($address['landline_number'])) {
                                                        $state['number'] = $address['landline_number'];
                                                    }
                                                } elseif (is_object($address)) {
                                                    if (isset($address->landline_code)) {
                                                        $state['prefix'] = $address->landline_code;
                                                    }
                                                    if (isset($address->landline_number)) {
                                                        $state['number'] = $address->landline_number;
                                                    }
                                                }
                                                return is_array($state) ? $state : ["prefix" => "", "number" => ""];
                                            })
                                            ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                            TextInput::make('region')
                                                ->label('Region')
                                                ->default($this->user->pcDetails->region ?? '-')
                                                ->readonly(),
                                            TextInput::make('web_url')
                                                ->label('Web URL')
                                                ->default($this->user->pcDetails->web_url ?? '-')
                                                ,
                                    ])->columns(2)
                                ];
                            })
                            ->action(function (array $data) {
                                $currentData = $this->getContactDetails();
                                $submittedData = $this->prepareSubmittedContactData($data);

                                // Get correct landline code based on city_id (city takes precedence)
                                $cityId = $data['userAddress']['city_id'] ?? null;
                                $stateId = $data['userAddress']['state_id'] ?? null;
                                $landLine = $data['landline_number'];
                                $landlineCode = $landLine['prefix'] ?? null;

                                // If not set, try to get from city
                                if (!$landlineCode && $cityId) {
                                    $city = City::find($cityId);
                                    if ($city && $city->landline_code) {
                                        $landlineCode = $city->landline_code;
                                    }
                                }
                                // If still not set, try to get from state
                                if (!$landlineCode && $stateId) {
                                    $landlineCode = City::where('state_id', $stateId)
                                        ->whereNotNull('landline_code')
                                        ->where('landline_code', '!=', '')
                                        ->value('landline_code');
                                }

                                $data['landline_number'] = $landLine['number'] ?? null;
                                $data['landline_code'] = $landlineCode;

                                // Check if there are any changes
                                if (!$this->hasContactChanges($currentData, $submittedData)) {
                                    Notification::make()
                                        ->title('No changes detected')
                                        ->warning()
                                        ->send();
                                    return;
                                }
                                // Prepare address data for approval
                                $addressChanges = [
                                    'address_1' => $data['userAddress']['address_1'],
                                    'address_2' => $data['userAddress']['address_2'],
                                    'state_id' => $data['userAddress']['state_id'],
                                    'city_id' => $data['userAddress']['city_id'],
                                    'postal_code' => $data['userAddress']['postal_code'],
                                    'landline_number' => $data['landline_number'],
                                    'landline_code' => $data['landline_code'],
                                    'phone_number' => $data['pcDetails']['phone_number'],
                                    'web_url' => $data['web_url'],
                                    'region' => $data['region'],
                                ];
                                // Mark as pending approval
                                $this->user->pcDetails->update([
                                    'is_version_pending' => true,
                                    'is_restricted' => true
                                ]);

                                // Create approval record for all changes
                                \App\Models\Approval::create([
                                    'approvalable_type' => 'App\Models\User',
                                    'approvalable_id' => $this->user->id,
                                    'new_data' => json_encode($addressChanges),
                                    'original_data' => json_encode($currentData),
                                    'user_type' => 'pc',
                                    'steps' => OnboardingStep::CONTACT,
                                    'status' => 'pending'
                                ]);

                                //Activity log Start
                                activity()
                                    ->causedBy(auth()->user())
                                    ->performedOn($this->user)
                                    ->useLog('contact_details_update')
                                    ->withProperties([
                                        'old' => collect($currentData)
                                            ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                            ->merge(self::getCityState($currentData['state_id'] ?? null, $currentData['city_id'] ?? null))
                                            ->all(),
                                        'attributes' => collect($addressChanges)
                                            ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                            ->merge(self::getCityState($addressChanges['state_id'] ?? null, $addressChanges['city_id'] ?? null))
                                            ->all(),
                                    ])
                                    ->log('Contact details have been submitted for approval');

                                Notification::make()
                                    ->title('Changes submitted for approval')
                                    ->success()
                                    ->send();

                                $this->sendApprovalEmail();
                                return redirect()->route(MyProfile::getRouteName());
                            })
                    ])
                    ->schema([
                        InfoGroup::make([
                            TextEntry::make('contact_address_1')
                                ->label('Street 1')
                                ->default($this->userAddress->address_1 ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['address_1'] ?? null
                                    );
                                }),
                            TextEntry::make('contact_address_2')
                                ->label('Street 2')
                                ->default($this->userAddress->address_2 ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['address_2'] ?? null
                                    );
                                }),
                            TextEntry::make('contact_state')
                                ->label('State')
                                ->default($this->contactStateDetail->name ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatRelationshipWithPendingChanges(
                                        $state,
                                        $stepFiveChanges['state_id'] ?? null,
                                        State::class
                                    );
                                }),
                            TextEntry::make('contact_city')
                                ->label('City')
                                ->default($this->contactCityDetail->name ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatRelationshipWithPendingChanges(
                                        $state,
                                        $stepFiveChanges['city_id'] ?? null,
                                        City::class
                                    );
                                }),
                            TextEntry::make('contact_country')
                                ->label('Country')
                                ->default($this->contactStateDetail->country_name ?? '-')
                                ->formatStateUsing(fn() => $this->contactStateDetail->country_name ?? ''),
                            TextEntry::make('contact_postal_code')
                                ->label('Postal Code')
                                ->default($this->userAddress->postal_code ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['postal_code'] ?? null
                                    );
                                }),
                            TextEntry::make('contact_phone')
                                ->label('Mobile Number')
                                ->prefix('+60 ')
                                ->default($this->user->pcDetails->phone_number ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['phone_number'] ?? null
                                    );
                                }),
                            TextEntry::make('contact_landline_number')
                                ->label('Landline Number')
                                ->default(function () {
                                    $code = $this->userAddress->landline_code ?? '';
                                    $number = $this->userAddress->landline_number ?? '-';
                                    // Remove any leading "+" from code and number
                                    $code = ltrim($code, '+');
                                    $number = ltrim($number, '+');
                                    if ($code && $number && strpos($number, $code) === 0) {
                                        // If number starts with code, remove code from number
                                        $number = trim(substr($number, strlen($code)));
                                    }
                                    if ($code) {
                                        return '+' . $code . ' ' . $number;
                                    }
                                    return $number;
                                })
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    $code = $this->userAddress->landline_code ?? '';
                                    $number = $state ?? '-';
                                    // Remove any leading "+" from code and number
                                    $code = ltrim($code, '+');
                                    $number = ltrim($number, '+');
                                    if ($code && $number && strpos($number, $code) === 0) {
                                        // If number starts with code, remove code from number
                                        $number = trim(substr($number, strlen($code)));
                                    }
                                    if ($code) {
                                        $currentValue = '+' . $code . ' ' . $number;
                                    } else {
                                        $currentValue = $number;
                                    }

                                    // Prepare changes for formatLandlinePendingChanges function
                                    $landlineChanges = [
                                        'number' => $stepFiveChanges['landline_number'] ?? null,
                                        'code' => $stepFiveChanges['landline_code'] ?? null
                                    ];

                                    // Check if both code and number have changes
                                    $hasNumberChange = isset($landlineChanges['number'][0]['new_value']);
                                    $hasCodeChange = isset($landlineChanges['code'][0]['new_value']);

                                    // If both code and number are changed, show only the combined new value
                                    if ($hasNumberChange && $hasCodeChange) {
                                        $newNumber = $landlineChanges['number'][0]['new_value'];
                                        $newCode = $landlineChanges['code'][0]['new_value'];
                                        $newCode = ltrim($newCode, '+');
                                        $formatted = $newCode ? '+' . $newCode . ' ' . $newNumber : $newNumber;
                                        
                                        // Create a modified changes array with only the combined result
                                        $combinedChanges = [
                                            'number' => [
                                                [
                                                    'new_value' => $formatted,
                                                    'old_value' => $currentValue
                                                ]
                                            ]
                                        ];
                                        
                                        return static::formatLandlinePendingChanges(
                                            $currentValue,
                                            $combinedChanges
                                        );
                                    }

                                    // Use the formatLandlinePendingChanges function for single field changes
                                    return static::formatLandlinePendingChanges(
                                        $currentValue,
                                        $landlineChanges
                                    );
                                }),
                                TextEntry::make('web_url')
                                ->label('Web URL')
                                ->default($this->user->pcDetails->web_url ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['web_url'] ?? null
                                    );
                                }),
                                TextEntry::make('region')
                                ->label('Region')
                                ->default($this->user->pcDetails->region ?? '-')
                                ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                    // dd($stepFiveChanges);
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepFiveChanges['region'] ?? null
                                    );
                                }),
                        ])->columns(4)
                    ]),

                // Doctor In Charge Section
                InfoSection::make('Person In Charge')
                    ->description(
                        empty($stepSixChanges)
                            ? ''
                            : '⚠️ Any changes made will be sent to the admin for verification. Updates will be reflected only after approval.'
                    )
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    TextInput::make('person_in_charge_name')
                                        ->label('Name')
                                        ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                        ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'success' : null)
                                        ->validationMessages([
                                            'regex' => 'The Full Name must be a alphabetical.',
                                            'max' => 'The Full Name may not be greater than 50 characters.',
                                        ])
                                        ->default($this->user->pcDetails->person_in_charge_name ?? ''),
                                    TextInput::make('person_in_charge_nric')
                                        ->rules(function (Get $get) {
                                            if (!empty($this->user->id)) {
                                                return ['nullable', 'max:50'];
                                            }
                                            return ['nullable', 'max:50'];
                                        })
                                        ->live()
                                        // ->extraAttributes([
                                        //     'maxlength' => 15,
                                        //     'pattern' => '[a-zA-Z0-9]*',
                                        //     'oninput' => 'this.value = this.value.replace(/[^a-zA-Z0-9]/g, "");',
                                        // ])
                                        ->suffixIcon(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                        ->label(new HtmlString("NRIC / Passport Number"))
                                        ->validationMessages([
                                            'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                        ])
                                        ->default($this->user->pcDetails->person_in_charge_nric ?? ''),
                                    TextInput::make('person_in_charge_phone')
                                        ->label(new HtmlString("Mobile Number <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->mask('999999999999')
                                        ->live()
                                        ->stripCharacters(['-'])
                                        ->extraAttributes([
                                            'inputmode' => 'numeric',
                                            'maxlength' => '12'
                                        ])
                                        ->validationMessages([
                                            'required' => 'The Mobile Number is required.',
                                            'digits_between' => 'The Mobile Number must be between 8 and 12 digits.',
                                        ])
                                        ->prefix('+60 ')
                                        ->rules(['required', 'digits_between:8,12'])
                                        ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                        ->default($this->user->pcDetails->person_in_charge_phone ?? ''),
                                    // TextInput::make('person_in_charge_landline')
                                    //     ->label('Landline')
                                    //     ->live()
                                    //     ->mask('9-9999999')
                                    //     ->prefix('+03')
                                    //     ->stripCharacters(['-'])
                                    //     ->extraAttributes([
                                    //         'inputmode' => 'numeric',
                                    //         'maxlength' => '8'
                                    //     ])
                                    //     ->rules(['digits_between:7,8'])
                                    //     ->suffixIcon(fn ($state) => in_array(strlen($state), [8, 9]) ? 'heroicon-s-check-circle' : null)
                                    //     ->suffixIconColor(fn ($state) => in_array(strlen($state), [8, 9]) ? 'success' : null)
                                    //     ->default($this->user->pcDetails->person_in_charge_landline ?? ''),

                                    
                                    TextInput::make('person_in_charge_email')
                                        ->placeholder('Enter email')
                                        ->rules(function (Get $get) {
                                            return [
                                                'required',
                                                'email',
                                                'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                                function ($attribute, $value, $fail) use ($get) {
                                                    $exists = DB::table('pc_details')
                                                        ->where(function ($query) use ($value) {
                                                            $query->where('profile_email', $value)
                                                                ->orWhere('person_in_charge_email', $value);
                                                        })
                                                        ->where('user_id', '=', $get('user_id'))  // Ignore the current user
                                                        ->exists();
                                                    //   dd($exists);
                                                    if ($exists) {
                                                        $fail('The email has already been taken.');
                                                    }
                                                },
                                            ];
                                        })
                                        ->validationMessages([
                                            'required' => 'The email is required.',
                                            'email' => 'The email must be a valid email address.',
                                            'regex' => 'The email must be a valid email address.',
                                        ])
                                        ->label(new HtmlString("Email <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                        ->suffixIcon(function ($state) {
                                            return !empty($state) && filter_var($state, FILTER_VALIDATE_EMAIL) ? 'heroicon-s-check-circle' : null;
                                        })
                                        ->suffixIconColor(fn($state) => filter_var($state, FILTER_VALIDATE_EMAIL) ? 'success' : null)
                                        ->default($this->user->pcDetails->person_in_charge_email ?? '')
                                        ->live(),
                                ])->columns(2)
                            ])
                            ->action(function (array $data) {

                                $currentData = [
                                    'person_in_charge_name' => $this->user->pcDetails->person_in_charge_name,
                                    'person_in_charge_nric' => $this->user->pcDetails->person_in_charge_nric,
                                    'person_in_charge_phone' => $this->user->pcDetails->person_in_charge_phone,
                                    'person_in_charge_email' => $this->user->pcDetails->person_in_charge_email,
                                ];

                                $newData = [
                                    'person_in_charge_name' => $data['person_in_charge_name'],
                                    'person_in_charge_nric' => $data['person_in_charge_nric'],
                                    'person_in_charge_phone' => $data['person_in_charge_phone'],
                                    'person_in_charge_email' => $data['person_in_charge_email'],
                                ];

                                $hasChanges = false;
                                foreach ($currentData as $key => $value) {
                                    if ($data[$key] != $value) {
                                        $hasChanges = true;
                                        break;
                                    }
                                }

                                if (!$hasChanges) {
                                    Notification::make()
                                        ->title('No changes detected')
                                        ->warning()
                                        ->send();
                                    return;
                                }

                                \App\Models\Approval::create([
                                    'approvalable_type' => 'App\Models\User',
                                    'approvalable_id' => $this->user->id,
                                    'new_data' => json_encode($newData),
                                    'original_data' => json_encode($currentData),
                                    'user_type' => 'pc',
                                    'steps' => OnboardingStep::PERSON_IN_CHARGE,
                                    'status' => 'pending'
                                ]);

                                //Activity log Start
                                $changedOld = [];
                                $changedAttributes = [];

                                foreach ($currentData as $key => $value) {
                                    if ($value != $newData[$key]) {
                                        $changedOld[$key] = $value;
                                        $changedAttributes[$key] = $newData[$key];
                                    }
                                }

                                if (!empty($changedOld)) {
                                    activity()
                                        ->performedOn($this->user)
                                        ->causedBy(auth()->user())
                                        ->useLog('person_in_charge')
                                        ->withProperties([
                                            'old' => $changedOld,
                                            'attributes' => $changedAttributes,
                                        ])
                                        ->log('Person-in-charge details have been submitted for approval');
                                }
                                //Activity log End

                                Notification::make()
                                    ->title('Changes submitted for approval')
                                    ->success()
                                    ->send();
                                // $this->user->pcDetails->update([
                                //     'person_in_charge_name' => $data['person_in_charge_name'],
                                //     'person_in_charge_nric' => $data['person_in_charge_nric'],
                                //     'person_in_charge_phone' => $data['person_in_charge_phone'],
                                //     'person_in_charge_email' => $data['person_in_charge_email'],

                                // ]);
                            })
                    ])
                    ->schema([
                        InfoGroup::make([
                            TextEntry::make('person_in_charge_name')
                                ->label('Full Name')
                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepSixChanges['person_in_charge_name'] ?? null
                                    );
                                }),
                            TextEntry::make('person_in_charge_nric')
                                ->label('NRIC / Passport Number')
                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepSixChanges['person_in_charge_nric'] ?? null
                                    );
                                }),
                            TextEntry::make('person_in_charge_phone')
                                ->label('Mobile Number')
                                ->prefix('+60 ')
                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepSixChanges['person_in_charge_phone'] ?? null
                                    );
                                }),
                            // TextEntry::make('person_in_charge_landline')
                            //     ->label('Landline Number')
                            //     ->formatStateUsing(fn () => $this->user->pcDetails->person_in_charge_landline ?? '-'),
                            TextEntry::make('person_in_charge_email')
                                ->label('Email')
                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                    return static::formatWithPendingChanges(
                                        $state ?? '-',
                                        $stepSixChanges['person_in_charge_email'] ?? null
                                    );
                                }),
                        ])->columns(4)
                    ]),

                InfoSection::make('Bank Details')
                    ->description(
                       new \Illuminate\Support\HtmlString(' ⚠️ <strong>Note:</strong> 
                       <span> Bank details will not be visible to the public and not require admin approval.</span> ')
                    )
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                        ->color('gray')
                        ->outlined()
                        ->button()
                        ->icon('heroicon-o-pencil')
                        ->action(function ($record, $data) {                                                                          
                            // Only update the allowed fields, not the whole $data array (which includes 'pcDetails')
                            PcDetail::where('id', $record->id)->update([
                                'beneficiary_name' => encryptTextParam($data['beneficiary_name'] ?? null),
                                'bank_name' => encryptTextParam($data['bank_name'] ?? null),
                                'account_number' => encryptTextParam($data['account_number'] ?? null),
                            ]);
                            Notification::make()
                            ->title('Changes have been saved successfully')
                            ->success()
                            ->send();                        
                            return redirect()->route(MyProfile::getRouteName());
                        })->form(function ($record) {
                            return [
                                Group::make()->schema([
                                    TextInput::make('beneficiary_name')
                                        ->rules(['required', 'string', 'max:50', 'regex:/^[a-zA-Z\s]+$/'])
                                        ->live()
                                        ->suffixIcon(fn ($state) => (strlen($state) > 0 && strlen($state) <= 50 && preg_match('/^[a-zA-Z\s]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn ($state) => (strlen($state) > 0 && strlen($state) <= 50 && preg_match('/^[a-zA-Z\s]+$/', $state)) ? 'success' : null)
                                        ->validationMessages([
                                            'required' => 'The Beneficiary Name field is required.',
                                            'regex' => 'The Beneficiary Name must be a alphabetical.',
                                            'max' => 'The Beneficiary Name may not be greater than 50 characters.',
                                        ])
                                        ->extraAttributes([
                                            'style' => 'font-family: monospace;', // Ensures consistent character width
                                        ])
                                        ->label(fn () => new HtmlString('Beneficiary Name <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                        ->placeholder('Enter the beneficiary name')
                                        ->default(function () use ($record) {
                                            $name = decryptParam($record->beneficiary_name);
                                            if (!$name) return '';
                                            $len = strlen($name);
                                            if ($len <= 4) {
                                                return str_repeat('*', $len);
                                            }
                                            return str_repeat('*', $len - 4) . substr($name, -4);
                                        }),
                                    Select::make('bank_name')
                                        ->label('Bank Name')
                                        ->options(function () {
                                            // Use a common helper or service to get bank names
                                            return getBankNames();
                                        })
                                        ->required()
                                        ->searchable()
                                        ->validationMessages([
                                            'required' => 'The Bank Name field is required.',
                                        ])
                                        ->formatStateUsing(function ($state, $record) {
                                            $bankName = $record?->bank_name ?? null;
                                            if (!$bankName) {
                                                return '';
                                            }
                                            $bank = decryptParam($bankName);
                                            if (!$bank) {
                                                return '';
                                            }
                                            $len = strlen($bank);
                                            if ($len <= 4) {
                                                return '<span style="font-family: monospace; letter-spacing: 1px;">' . str_repeat('*', $len) . '</span>';
                                            }
                                            return '<span style="font-family: monospace; letter-spacing: 1px;">' . str_repeat('*', $len - 4) . '</span>' . substr($bank, -4);
                                        })->allowHtml()
                                        ->placeholder('Select the bank name')
                                        ->default(function () use ($record) {
                                            $bank = decryptParam($record->bank_name);
                                            if (!$bank) return '';
                                            $len = strlen($bank);
                                            if ($len <= 4) {
                                                return str_repeat('*', $len);
                                            }
                                            return str_repeat('*', $len - 4) . substr($bank, -4);
                                        }),
                                    TextInput::make('account_number')
                                        ->label(fn () => new HtmlString('Account Number <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                        ->rules(['required', 'string', 'max:30', 'regex:/^[0-9]+$/'])
                                        ->live()
                                        ->suffixIcon(fn ($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                        ->suffixIconColor(fn ($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'success' : null)
                                        ->validationMessages([
                                            'required' => 'The Account Number field is required.',
                                            'regex' => 'The Account Number must be numeric.',
                                            'max' => 'The Account Number may not be greater than 30 characters.',
                                        ])
                                        ->extraAttributes([
                                            'style' => 'font-family: monospace;', // Ensures consistent character width
                                        ])
                                        ->placeholder('Enter the account number')
                                        ->default(function () use ($record) {
                                            $acc = decryptParam($record->account_number);
                                            if (!$acc) return '';
                                            $len = strlen($acc);
                                            // Return a string only, not an HtmlString, to avoid Livewire property type issues.
                                            if ($len <= 4) {
                                                return str_repeat('*', $len);
                                            }
                                            return str_repeat('*', $len - 4) . substr($acc, -4);
                                        }),
                                        // Checkbox::make('pcDetails.is_credit_line')->label('Enable Credit Line')->default(fn () => $this->user->pcDetails?->is_credit_line ?? false)->columnSpanFull()->reactive(),
                                ])->columns(2),
                            ];
                        })
                    ])
                    ->schema([
                        TextEntry::make('beneficiary_name')
                        ->formatStateUsing(function ($state, $record) {
                            $name = decryptParam($record?->beneficiary_name);
                            if (!$name) return '';
                            $len = strlen($name);
                            if ($len <= 4) {
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                            }
                            return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($name, -4)));
                        })
                        ->label('Beneficiary Name'),
                        TextEntry::make('bank_name')
                        ->formatStateUsing(function ($state, $record) {
                            $bank = decryptParam($record?->bank_name);
                            if (!$bank) return '';
                            $len = strlen($bank);
                            if ($len <= 4) {
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                            }
                            return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($bank, -4)));
                        })
                        ->label('Bank Name'),
                        TextEntry::make('account_number')
                        ->formatStateUsing(function ($state, $record) {
                            $acc = decryptParam($record?->account_number);
                            if (!$acc) return '';
                            $len = strlen($acc);
                            if ($len <= 4) {
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                            }
                            return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($acc, -4)));
                        })
                        ->label('Account Number'),
                        // TextEntry::make('is_credit_line')
                        // ->default('-')
                        // ->formatStateUsing(function ($state) use ($stepOneChanges) {
                        //     return static::formatCreditLineStatus(
                        //         $state,
                        //         $stepOneChanges['is_credit_line'] ?? null
                        //     );
                        // })
                        // ->label('Credit Line'),
                    ])->columns(4),
                // Uploaded Documents Section
                InfoSection::make('Uploaded Documents')
                    ->description(
                        empty($stepThreeChanges)
                            ? ''
                            // : '⚠️ Any changes made will be sent to the admin for verification and only latest approved files will be visible below.'
                            : new \Illuminate\Support\HtmlString(' ⚠️ <strong>Note:</strong> 
                                <span style="background-color: yellow;"> Your changes have been submitted for admin verification. 
                                Only the latest approved files will be displayed below.</span> ')
                    )
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->modalHeading('Update Document Files')
                            ->modalDescription(function () {
                                return "⚠️  Note: Only latest uploaded files will be processed for Admin approval and only approved files will be visible.\n";
                            })
                            ->form(function ($record) {
                                $certificateFiles = PcCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->get()->groupBy('type');
                                $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();
                                $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
                                // dd($licensePermitFiles);
                                return [
                                    Group::make()->schema([
                                        FileUpload::make('registration_certificate')
                                            ->label(new HtmlString(
                                                'Company Registration Certificate 
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>'
                                            ))
                                            ->multiple()
                                            // ->maxFiles(function () use ($companyCerts) {
                                            //     $existingCount = count($companyCerts);
                                            //     return 5 + $existingCount;
                                            // })
                                            ->maxFiles(3)
                                            ->maxSize('2048')
                                            ->rules(['required', File::types(['jpeg', 'jpg', 'png', 'pdf'])])
                                            ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                            ->required()
                                            ->directory(function (Get $get, $record) {
                                                return config('constants.api.media.pc_medias') . $record->id;
                                            })
                                            ->default(function () use ($companyCerts, $record) {
                                                if (empty($companyCerts)) {
                                                    return null;
                                                }

                                                return array_map(function ($file) use ($record) {
                                                    return config('constants.api.media.pc_medias') . $record->id . '/' . $file;
                                                }, $companyCerts);
                                            })
                                            ->validationMessages([
                                                'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'max' => 'File size must not exceed 2 MB',
                                                'required' => 'The Company Registration Certificate is required',
                                            ])
                                            ->acceptedFileTypes([
                                                'image/jpeg',
                                                'image/png',
                                                'application/pdf',
                                            ])
                                            ->validationAttribute('Upload Certificate of Registration')
                                            ->downloadable()
                                            ->previewable(),
                                        FileUpload::make('license_permit')
                                            ->label(new HtmlString(
                                                'Relevant certification
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>'
                                            ))
                                            ->rules(['nullable', File::types(['jpeg', 'jpg', 'png', 'pdf'])])
                                            ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                            // ->required()
                                            ->maxSize('2048')
                                            // ->maxFiles(function () use ($licensePermitFiles) {
                                            //     $existingCount = count($licensePermitFiles);
                                            //     return 5 + $existingCount;
                                            // })
                                            ->maxFiles(3)
                                            ->directory(function (Get $get, $record) {
                                                return config('constants.api.media.pc_medias') . $record->id;
                                            })
                                            ->default(function () use ($licensePermitFiles, $record) {
                                                if (empty($licensePermitFiles)) {
                                                    return null;
                                                }

                                                return array_map(function ($file) use ($record) {
                                                    return config('constants.api.media.pc_medias') . $record->id . '/' . $file;
                                                }, $licensePermitFiles);
                                            })
                                            ->validationMessages([
                                                'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'max' => 'File size must not exceed 2 MB',
                                            ])
                                            ->acceptedFileTypes([
                                                'image/jpeg',
                                                'image/png',
                                                'application/pdf',
                                            ])
                                            ->validationAttribute('Pharmaceutical License or Permit')
                                            ->downloadable()
                                            ->multiple()
                                            ->previewable(),
                                    ])->columns(2)
                                ];
                            })
                            ->action(function (array $data, $record) {
                                // Get original active files grouped by type with IDs
                                $originalFiles = PcCertificateFile::where('user_id', $record->user_id)
                                    ->where('status', 'active')
                                    ->get()
                                    ->groupBy('type');
                            
                                // Prepare original data with both names and IDs
                                $originalData = [
                                    'company_registration' => $originalFiles->get('company_registration_certificate', collect())
                                        ->mapWithKeys(fn($file) => [$file->id => $file->name])
                                        ->toArray(),
                                    'license_permit' => $originalFiles->get('license_permit', collect())
                                        ->mapWithKeys(fn($file) => [$file->id => $file->name])
                                        ->toArray()
                                ];
                            
                                // Prepare new data (just filenames)
                                $newData = [
                                    'company_registration' => array_map(
                                        fn($path) => basename($path),
                                        is_array($data['registration_certificate']) ? $data['registration_certificate'] : [$data['registration_certificate']]
                                    ),
                                    'license_permit' => array_map(
                                        fn($path) => basename($path),
                                        is_array($data['license_permit']) ? $data['license_permit'] : [$data['license_permit']]
                                    )
                                ];
                            
                                $changes = [];
                                $hasChanges = false;
                            
                                // Process company registration changes
                                $addedCompanyFiles = array_diff($newData['company_registration'], array_values($originalData['company_registration']));
                                $removedCompanyFiles = array_diff(array_values($originalData['company_registration']), $newData['company_registration']);
                                
                                if (!empty($addedCompanyFiles)) {
                                    $changes['company_registration_certificate'] = array_values($addedCompanyFiles);
                                    $hasChanges = true;
                                }
                                
                                if (!empty($removedCompanyFiles)) {
                                    // Get IDs of removed files
                                    $removedCompanyIds = array_keys(array_filter($originalData['company_registration'], function($name) use ($removedCompanyFiles) {
                                        return in_array($name, $removedCompanyFiles);
                                    }));
                                    $changes['removed_company_registration_certificate'] = $removedCompanyIds;
                                    $hasChanges = true;
                                }
                            
                                // Process license permit changes
                                $addedLicenseFiles = array_diff($newData['license_permit'], array_values($originalData['license_permit']));
                                $removedLicenseFiles = array_diff(array_values($originalData['license_permit']), $newData['license_permit']);
                                
                                if (!empty($addedLicenseFiles)) {
                                    $changes['license_permit'] = array_values($addedLicenseFiles);
                                    $hasChanges = true;
                                }
                                
                                if (!empty($removedLicenseFiles)) {
                                    // Get IDs of removed files
                                    $removedLicenseIds = array_keys(array_filter($originalData['license_permit'], function($name) use ($removedLicenseFiles) {
                                        return in_array($name, $removedLicenseFiles);
                                    }));
                                    
                                    $changes['removed_license_permit'] = $removedLicenseIds;
                                    $hasChanges = true;
                                }
                            
                                Log::info("Changes with removed IDs:", $changes);
                            
                                if ($hasChanges) {
                                    \App\Models\Approval::create([
                                        'approvalable_type' => 'App\Models\User',
                                        'approvalable_id' => $record->user_id,
                                        'original_data' => json_encode([
                                            'company_registration_certificate' => array_values($originalData['company_registration']),
                                            'license_permit' => array_values($originalData['license_permit'])
                                        ]),
                                        'new_data' => json_encode($changes),
                                        'user_type' => 'pc',
                                        'steps' => OnboardingStep::DOCUMENTS->value,
                                        'status' => 'pending',
                                    ]);
                            
                                    activity()
                                        ->performedOn($this->user)
                                        ->causedBy(getUser(auth()->user()))
                                        ->useLog('document_upload')
                                        ->log('Documents have been submitted for approval');
                            
                                    Notification::make()
                                        ->title('Document changes submitted for approval')
                                        ->success()
                                        ->send();
                            
                                    $this->sendApprovalEmail();
                                }
                            })
                            // ->action(function (array $data, $record) {
                            //     Log::info("Received registration_certificate files:", [
                            //         'count' => count((array) $data['registration_certificate']),
                            //         'files' => (array) $data['registration_certificate']
                            //     ]);

                            //     // Log license_permit files
                            //     Log::info("Received license_permit files:", [
                            //         'count' => count((array) $data['license_permit']),
                            //         'files' => (array) $data['license_permit']
                            //     ]);
                            //     $originalFiles = PcCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->get()->groupBy('type');

                            //     $originalData = [
                            //         'company_registration' => $originalFiles->get('company_registration_certificate', collect())
                            //             ->pluck('name')
                            //             ->toArray(),
                            //         'license_permit' => $originalFiles->get('license_permit', collect())
                            //             ->pluck('name')
                            //             ->toArray()
                            //     ];

                            //     $newData = [
                            //         'company_registration' => array_map(
                            //             fn($path) => basename($path),
                            //             is_array($data['registration_certificate']) ? $data['registration_certificate'] : [$data['registration_certificate']]
                            //         ),
                            //         'license_permit' => array_map(
                            //             fn($path) => basename($path),
                            //             is_array($data['license_permit']) ? $data['license_permit'] : [$data['license_permit']]
                            //         )
                            //     ];
                            //     Log::info("New data:", $newData);
                            //     // dd($originalData, $newData);
                            //     // dd($newData);
                            //     $changes = [];
                            //     $hasChanges = false;

                            //     $addedCompanyFiles = array_diff($newData['company_registration'], $originalData['company_registration']);
                            //     $removedCompanyFiles = array_diff($originalData['company_registration'], $newData['company_registration']);
                            //     if (!empty($addedCompanyFiles)) {
                            //         $changes['company_registration_certificate'] = array_values($addedCompanyFiles);
                            //         $hasChanges = true;
                            //     } elseif (!empty($removedCompanyFiles)) {
                            //         $changes['company_registration_certificate'] = array_values(array_diff(
                            //             $originalData['company_registration'],
                            //             $removedCompanyFiles
                            //         ));
                            //         $hasChanges = true;
                            //     }



                            //     $addedLicenseFiles = array_diff($newData['license_permit'], $originalData['license_permit']);
                            //     $removedLicenseFiles = array_diff($originalData['license_permit'], $newData['license_permit']);
                            //     if (!empty($addedLicenseFiles)) {
                            //         // Only store newly added files
                            //         $changes['license_permit'] = array_values($addedLicenseFiles);
                            //         $hasChanges = true;
                            //     } elseif (!empty($removedLicenseFiles)) {
                            //         // Only store remaining files (original minus removed)
                            //         $changes['license_permit'] = array_values(array_diff(
                            //             $originalData['license_permit'],
                            //             $removedLicenseFiles
                            //         ));
                            //         $hasChanges = true;
                            //     }
                            //     Log::info("Changes:", $changes);
                            //     // Only create approval if there are actual changes
                            //     if ($hasChanges) {
                            //         \App\Models\Approval::create([
                            //             'approvalable_type' => 'App\Models\User',
                            //             'approvalable_id' => $record->user_id,
                            //             'original_data' => json_encode($originalData),
                            //             'new_data' => json_encode($changes),
                            //             'user_type' => 'pc',
                            //             'steps' => OnboardingStep::DOCUMENTS->value,
                            //             'status' => 'pending',
                            //         ]);

                                    
                            //             activity()
                            //                 ->performedOn($this->user)
                            //                 ->causedBy(getUser(auth()->user()))
                            //                 ->useLog('document_upload')
                            //                 ->log('Documents have been submitted for approval');

                            //         Notification::make()
                            //             ->title('Document changes submitted for approval')
                            //             ->success()
                            //             ->send();

                            //         $this->sendApprovalEmail();
                            //     }
                            //     // $hasChanges = false;

                            //     // if (
                            //     //     isset($data['registration_certificate']) &&
                            //     //     $data['registration_certificate'] != $this->user->pcDetails->company_registration_certificate
                            //     // ) {
                            //     //     $originalData['company_registration_certificate'] = $this->user->pcDetails->company_registration_certificate;
                            //     //     $newData['company_registration_certificate'] = $data['registration_certificate'];
                            //     //     $hasChanges = true;
                            //     // }

                            //     // // Check license permit change (INDEPENDENT check)
                            //     // if (
                            //     //     isset($data['license_permit']) &&
                            //     //     $data['license_permit'] != $this->user->pcDetails->license_permit
                            //     // ) {
                            //     //     $originalData['license_permit'] = $this->user->pcDetails->license_permit;
                            //     //     $newData['license_permit'] = $data['license_permit'];
                            //     //     $hasChanges = true;
                            //     // }

                            //     // $originalData = [
                            //     //     'company_registration_certificate' => $this->user->pcDetails->company_registration_certificate ?? null,
                            //     //     'license_permit' => $this->user->pcDetails->license_permit ?? null
                            //     // ];
                            //     // if ($hasChanges) {
                            //     //     $this->user->pcDetails->update(['is_version_pending' => true, 'is_restricted' => true]);
                            //     //     // Prepare new document paths
                            //     //     // $newData = [
                            //     //     //     'company_registration_certificate' => $data['registration_certificate'],
                            //     //     //     'license_permit' => $data['license_permit']
                            //     //     // ];

                            //     //     // Create approval record
                            //     //     \App\Models\Approval::create([
                            //     //         'approvalable_type' => 'App\Models\User',
                            //     //         'approvalable_id' => $this->user->id,
                            //     //         'original_data' => json_encode($originalData),
                            //     //         'new_data' => json_encode($newData),
                            //     //         'user_type' => 'pc',
                            //     //         'steps' => OnboardingStep::DOCUMENTS->value,
                            //     //         'status' => 'pending',
                            //     //     ]);

                            //     //     Notification::make()
                            //     //         ->title('Document changes submitted for approval')
                            //     //         ->success()
                            //     //         ->send();
                            //     //     $this->sendApprovalEmail();
                            //     // }
                            // })
                    ])
                    ->schema(function ($record) use ($stepThreeChanges) {
                        // dd($stepThreeChanges);
                        $certificateFiles = PcCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->get()->groupBy('type');

                        $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();

                        $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
                        return [
                            InfoGroup::make([
                                ViewEntry::make('registration_certificate')
                                    ->label('Upload Certificate of Registration')
                                    ->view('filament.pc.resources.user-resource.pages.pc-myprofile-documents', [
                                        'filePath' => $companyCerts, //$this->user->pcDetails->company_registration_certificate ?? '-',
                                        'record' => $this->user->pcDetails,
                                        'name' => 'company_registration_certificate',
                                        'changes' => $stepThreeChanges['company_registration_certificate'] ?? [],
                                    ]),
                                ViewEntry::make('license_permit')
                                    ->label('Pharmaceutical License or Permit')
                                    ->view('filament.pc.resources.user-resource.pages.pc-myprofile-documents', [
                                        'filePath' => $licensePermitFiles, //$this->user->pcDetails->license_permit ?? '-',
                                        'name' => 'license_permit',
                                        'record' => $this->user->pcDetails,
                                        'changes' => $stepThreeChanges['license_permit'] ?? [],
                                    ]),

                            ])->columns(2)
                        ];
                    }),
            ]);
    }


    protected function getHeaderActions(): array
    {
        return [
            Action::make('toggle_credit_line')
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined()
                ->visible($this->user->parent_id == null)
                ->label(function ($record) {
                    return $this->user->pcDetails?->is_credit_line ? 'Disable Credit Line' : 'Enable Credit Line';
                })
                ->action(function () {
                    $current = $this->user->pcDetails?->is_credit_line;
                    $this->user->pcDetails()->update(['is_credit_line' => !$current]);
                }),
            Action::make('help_line')
                ->label(function () {
                    $contactNumber = GlobalSettings::where('name', 'contact_number')->first()?->value;
                    return $contactNumber ? 'Help Line : ' . $contactNumber : 'Help Line : 999999999';
                })->color('gray')
                ->extraAttributes([
                    'class' => 'cursor-default',
                ])
                ->disabled()
                ->tooltip('Contact this number for assistance'),
            Action::make('edit')->color('gray')->label('Update Password')->url(fn() => UpdatePassword::getUrl()),
            // Action::make('edit')->label('Contact Support')->url(fn () => ''),
            // Action::make('review')->label('Review Profile')->url(fn () => 'review-profile'),
            // Action::make('edit_profile')
            //     ->label('Edit Profile')
            //     ->url(fn() => EditProfile::getUrl()),
        ];
    }

    protected function sendApprovalEmail(): void
    {
        $adminData = getAdminData();

        if ($adminData->isNotEmpty()) {
            $adminEmail = $adminData->first()->email;
            Mail::to($adminEmail)->send(new ApprovalForAdminMail($this->user));
        }
    }

    public function getStepOneChanges($userId, $steps)
    {

        $data =  Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('approved_by', null)
            ->where('approved_at', null)
            ->where('rejected_at', null)
            ->where('rejected_by', null)
            ->where('user_type', 'pc')
            ->latest()
            ->orderBy('created_at', 'desc')
            ->select('new_data', 'original_data')
            ->first();
        if ($data) {
            $data->new_data = json_decode($data->new_data, true);
            $data->original_data = json_decode($data->original_data, true);
        }

        return $data;
    }
    public function organizeApprovalChanges($approvals)
    {
        if (!$approvals) {
            return [];
        }
        $changes = [];

        // foreach ($approvals as $approval) {
        $newData = $approvals->new_data;
        $originalData = $approvals->original_data;

        foreach ($newData as $key => $value) {
            if (!isset($changes[$key])) {
                $changes[$key] = [];
            }

            $changes[$key][] = [
                'new_value' => $value,
                'original_value' => $originalData[$key] ?? 'N/A',
            ];
        }
        // }
        // dd($changes);
        return $changes;
    }

    protected static function formatWithPendingChanges($currentValue, $changes = null)
    {
        $output = " $currentValue <br>";

        if ($changes) {
            foreach ($changes as $change) {
                if ($change['new_value'] != $currentValue) {
                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                }
            }
        }

        return new HtmlString($output);
    }

    // protected function organizeDocumentApprovalChanges($approval)
    // {

    //     if (!$approval) {
    //         return [];
    //     }
    //     // foreach ($approvals as $approval) {
    //     // Ensure proper JSON decoding
    //     $newData = is_array($approval->new_data)
    //         ? $approval->new_data
    //         : json_decode($approval->new_data, true);

    //     if (json_last_error() !== JSON_ERROR_NONE) {
    //         return [];
    //     }
    //     $changes = [];
    //     // Handle document changes
    //     if (isset($newData['license_permit'])) {
    //         $changes['license_permit'] = $newData['license_permit'];
    //     }
    //     if (isset($newData['company_registration_certificate'])) {
    //         $changes['company_registration_certificate'] = $newData['company_registration_certificate'];
    //     }

    //     // ... handle other fields
    //     // }

    //     return $changes;
    // }

    protected function organizeDocumentApprovalChanges($approval)
    {
        if (!$approval) {
            return [];
        }

        $newData = is_array($approval->new_data) 
            ? $approval->new_data 
            : json_decode($approval->new_data, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }

        $changes = [];

        $documentTypes = [
            'license_permit',
            'company_registration_certificate'
        ];

        foreach ($documentTypes as $type) {
            $changes[$type] = [];

            if (isset($newData[$type])) {
                $changes[$type] = array_merge(
                    $changes[$type],
                    is_array($newData[$type]) ? $newData[$type] : [$newData[$type]]
                );
            }

            // Add removed files if they exist (converted from IDs to filenames)
            $removedKey = 'removed_'.$type;
            if (isset($newData[$removedKey])) {
                $removedIds = is_array($newData[$removedKey]) ? $newData[$removedKey] : [$newData[$removedKey]];
                
                $removedFiles = PcCertificateFile::whereIn('id', $removedIds)
                    ->pluck('name')
                    ->toArray();

                $changes[$type] = array_merge($changes[$type], $removedFiles);
            }

            $changes[$type] = array_values(array_unique($changes[$type]));

            if (empty($changes[$type])) {
                unset($changes[$type]);
            }
        }
        return $changes;
    }

    protected static function formatRelationshipWithPendingChanges($currentName, ?array $changes, string $modelClass): HtmlString
    {
        $output = $currentName ?? '-';

        if (empty($changes)) {
            return new HtmlString($output);
        }

        $output = "{$output}<br>";

        foreach ($changes as $change) {
            $newId = $change['new_value'] ?? null;
            $newName = $modelClass::find($newId)?->name;

            if ($newName && $newName !== $currentName) {
                $output .= "<span style='background-color: yellow;'>{$newName}</span><br>";
            }
        }

        return new HtmlString($output);
    }

    protected static function getWarehouseTypeChangeMessage($record): ?string
    {
        $currentWarehouse = $record->warehouses()->first();
        if (!$currentWarehouse) {
            return null;
        }

        $approvalData = static::getPendingApproval($record->id, OnboardingStep::ADDRESS->value);
        if (!$approvalData) {
            return null;
        }

        $newData = json_decode($approvalData->new_data, true);
        $currentType = $currentWarehouse->warehouse_type;

        if (!isset($newData['warehouse_type']) || $newData['warehouse_type'] === $currentType) {
            return null;
        }

        $typeNames = [
            'dpharma' => 'DPharma Logistics',
            'owned' => 'Own Logistics'
        ];

        return sprintf(
            "Logistics Type Change Request: From %s to %s",
            $typeNames[$currentType] ?? $currentType,
            $typeNames[$newData['warehouse_type']] ?? $newData['warehouse_type']
        );
    }

    protected static function getPendingApproval($userId, $steps)
    {
        return Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('user_type', 'pc')
            ->pending()
            ->latest()
            ->first();
    }

    private function getCurrentWarehouseData(): array
    {
        return [
            'warehouse_type' => $this->userWarehouse ? $this->userWarehouse->warehouse_type : null,
            'address_1' => $this->userWarehouse ? $this->userWarehouse->address_1 : null,
            'address_2' => $this->userWarehouse->address_2 ?? null,
            'state_id' => $this->userWarehouse->state_id ?? null,
            'city_id' => $this->userWarehouse->city_id ?? null,
            'postal_code' => $this->userWarehouse->postal_code ?? null,
        ];
    }

    private function prepareSubmittedWarehouseData(array $formData): array
    {
        return [
            'warehouse_type' => $formData['warehouse_type'],
            'address_1' => $formData['userWarehouse']['address_1'] ?? null,
            'address_2' => $formData['userWarehouse']['address_2'] ?? null,
            'state_id' => $formData['userWarehouse']['state_id'] ?? null,
            'city_id' => $formData['userWarehouse']['city_id'] ?? null,
            'postal_code' => $formData['userWarehouse']['postal_code'] ?? null,
        ];
    }

    private function hasChanges(array $original, array $new): bool
    {
        foreach ($new as $key => $value) {
            if (!array_key_exists($key, $original) || $original[$key] != $value) {
                return true;
            }
        }
        return false;
    }

    private function getCurrentCompanyDetails(): array
    {
        return [
            'business_name' => $this->user->pcDetails->business_name ?? null,
            'profile_email' => $this->user->pcDetails->profile_email ?? null,
            'web_url' => $this->user->pcDetails->web_url ?? null,
            'company_registration_number' => $this->user->pcDetails->company_registration_number ?? null,
            'tin_number' => $this->user->pcDetails->tin_number ?? null,
            'sstc_number' => $this->user->pcDetails->sstc_number ?? null,
            'address_1' => $this->userAddress->address_1 ?? null,
        ];
    }

    private function prepareSubmittedCompanyData(array $formData): array
    {

        return [
            'business_name' => $formData['pcDetails']['business_name'],
            'profile_email' => $formData['pcDetails']['email'] ?? null,
            'web_url' => $formData['pcDetails']['web_url'] ?? null,
            'company_registration_number' => $formData['pcDetails']['company_registration_number'] ?? null,
            'tin_number' => $formData['pcDetails']['tin_number'] ?? null,
            'sstc_number' => $formData['pcDetails']['sstc_number'] ?? null,
            'address_1' => $formData['userAddress']['address_1'] ?? null
        ];
    }

    private function hasCompanyChanges(array $original, array $new): bool
    {
        foreach ($new as $key => $value) {
            if (!array_key_exists($key, $original) || $original[$key] != $value) {
                return true;
            }
        }
        return false;
    }


    private function getContactDetails(): array
    {
        return [
            'address_1' => $this->userAddress->address_1 ?? null,
            'address_2' => $this->userAddress->address_2 ?? null,
            'state_id' => $this->userAddress->state_id ?? null,
            'city_id' => $this->userAddress->city_id ?? null,
            'postal_code' => $this->userAddress->postal_code ?? null,
            'landline_number' => $this->userAddress->landline_number ?? null,
            'landline_code' => $this->userAddress->landline_code ?? null,
            'phone_number' => $this->user->pcDetails->phone_number ?? null
        ];
    }

    protected function prepareSubmittedContactData(array $formData): array
    {
        $landline = $formData['landline_number'] ?? [];

        return [

            // Address Details from form
            'address_1' => $formData['userAddress']['address_1'] ?? null,
            'address_2' => $formData['userAddress']['address_2'] ?? null,
            'state_id' => $formData['userAddress']['state_id'] ?? null,
            'city_id' => $formData['userAddress']['city_id'] ?? null,
            'postal_code' => $formData['userAddress']['postal_code'] ?? null,
            'landline_number' => $landline['number'] ?? null,
            'landline_code' => $landline['prefix'] ?? null,

            // Phone Number
            'phone_number' => $formData['pcDetails']['phone_number'] ?? null,

        ];
    }

    protected function hasContactChanges(array $current, array $new): bool
    {
        foreach ($new as $key => $value) {
            if ($current[$key] != $value) {
                return true;
            }
        }
        return false;
    }

    protected function hasPcDetailsChanges(array $current, array $new): bool
    {
        foreach ($new as $key => $value) {
            if ($current[$key] != $value) {
                return true;
            }
        }
        return false;
    }

    protected static function formatLandlinePendingChanges($currentValue, $changes = null)
    {
        $output = "{$currentValue}<br>";

        if ($changes) {
            $currentNumberOnly = preg_replace('/^\+?\d*\s*/', '', $currentValue);
            // Handle landline number changes
            if (isset($changes['number'])) {
                foreach ($changes['number'] as $change) {
                    if ($change['new_value'] != $currentValue) {
                        $newNumber = $change['new_value'];
                        $newCode = $changes['code'][0]['new_value'] ?? '';
                        if ($newNumber != $currentNumberOnly) {
                            $formatted = $newCode ? '+' . $newCode . ' ' . $newNumber : $newNumber;
                            $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                        }
                    }
                }
            }

            // Handle case where only code changes
            if (isset($changes['code'])) {
                foreach ($changes['code'] as $change) {
                    $currentCode = preg_match('/\+(\d+)/', $currentValue, $matches) ? $matches[1] : '';
                    $currentNumber = explode(' ', $currentValue)[1] ?? '';
                    if ($change['new_value'] != $currentCode) {
                        $formatted = '+' . $change['new_value'] . ' ' . $currentNumber;
                        $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                    }
                }
            }
        }

        return new HtmlString($output);
    }

    protected static function getCityState($stateId, $cityId)
    {
        $stateName = null;
        $cityName = null;

        if ($stateId) {
            $stateName = State::where('id', $stateId)->value('name');
        }

        if ($cityId) {
            $cityName = City::where('id', $cityId)->value('name');
        }

        return [
            'state_name' => $stateName,
            'city_name' => $cityName
        ];
    }

    protected static  function formatCreditLineStatus($currentValue, $changes = null): Htmlable 
    {
        $currentStatus = match($currentValue) {
            true => 'Enable',
            false => 'Disable',
            default => '-'
        };
        
        $output = $currentStatus . "<br>";
        
        if ($changes) {
            foreach ((array)$changes as $change) {
                // Convert pending boolean value to text
                $pendingStatus = match($change['new_value'] ?? null) {
                    true => 'Enable',
                    false => 'Disable',
                    default => '-'
                };
                
                if (($change['new_value'] ?? null) !== $currentValue) {
                    $output .= "<span style='background-color: yellow;'>$pendingStatus</span><br>";
                }
            }
        }
        
        return new HtmlString($output);
    }
}
