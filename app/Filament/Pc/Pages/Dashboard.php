<?php

namespace App\Filament\Pc\Pages;

use App\Filament\Pc\Widgets\OrdersChart;
use App\Filament\Pc\Widgets\OrderStatsOverview;
use App\Filament\Pc\Widgets\RecentOrders;
use Filament\Pages\Page;
use Filament\Facades\Filament;
use Filament\Pages\Dashboard as BasePage;

class Dashboard extends BasePage
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    // protected static string $view = 'filament.admin.pages.dashboard';
    protected static ?string $title = 'Orders';

    protected static string $view = 'filament.admin.pages.dashboard';

    protected static bool $shouldRegisterNavigation = false;

    public function getWidgets(): array
    {
        return [];
    }
    public function getHeaderWidgets(): array
    {
        return [
            OrderStatsOverview::class,
            RecentOrders::class,
            OrdersChart::class,
        ];
    }
}
