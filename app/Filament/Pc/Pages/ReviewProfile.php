<?php

namespace App\Filament\Pc\Pages;

use App\Models\User;
use Filament\Actions;
use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Forms\Contracts\HasForms;
use App\Filament\Admin\Resources\UserResource;
use Filament\Facades\Filament;
use Filament\Actions\Action;
use Filament\Forms\Get;
use Filament\Forms\Form;
use Filament\Infolists\Infolist;
use Illuminate\Support\HtmlString;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Set;

use Illuminate\Validation\Rules\File;
use Illuminate\Validation\Rule;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Illuminate\Database\Eloquent\Collection;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Grid as InfoGrid;
use App\Filament\Admin\Resources\UserResource\Pages;
use App\Filament\Admin\Resources\UserResource\Pages\PcOnboardingPage;
use App\Forms\Components\PhoneWithPrefix;
use App\Models\PcCertificateFile;
use App\Models\PcCompanyType;
use App\Models\PcDetail;
use App\Models\ZipCode;
use App\Notifications\PcOnBoardingNotification;
use App\Rules\PhoneWithPrefixRule;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\Group as InfoGroup;
use Filament\Infolists\Components\Section as InfoSection;
use Illuminate\Support\Arr;
use Filament\Forms\Components\FileUpload;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Livewire\Component;

use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ReviewProfile extends Page implements HasInfolists, HasForms
{
    use InteractsWithInfolists;
    use InteractsWithForms;
    protected static bool $shouldRegisterNavigation = false;
    protected static string $resource = UserResource::class;
    public $rejected_reason = "";

    public User $user;
    public array $data = [];
    protected static string $view = 'filament.pc.pages.review-profile';
    public $acceptDeclaration = false;
    public $acceptPrivacyPolicy = false;

    public function getTitle(): string|Htmlable
    {
        return "Review Details";
    }



    public function mount()
    {

        $this->user = Filament::auth()->user();
        $this->user = User::with([
            // Explicitly load warehouses with their city/state
            'warehouses' => function ($query) {
                $query->with(['city', 'state']);
            },
            'pcDetails',
            'addresses',
            'addresses.city',
            'addresses.state',
            'address',
            'address.city',
            'address.state',
            'accounttype',
            'pharmaSuppliers'
        ])->find($this->user->id);
    }

    public function infolist(Infolist $infolist): ?Infolist
    {
        return $infolist
            ->record($this->user)
            ->schema([
                InfoSection::make('Basic Information')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form([
                                Group::make()->schema([
                                    FileUpload::make('users.photo')
                                        ->label('')
                                        ->image()
                                        ->imageEditor()
                                        ->circleCropper()
                                        ->directory('users')
                                        ->avatar()
                                        ->columnSpanFull()
                                        ->alignCenter()
                                        ->rules(['required', 'image', 'mimes:jpg,jpeg,png', 'max:2048']) // Image validation
                                        ->validationMessages([
                                            'required' => 'The avatar field is required.',
                                            'image' => 'The file must be an image.',
                                            'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                            'max' => 'The image must not exceed 2MB.'
                                        ])
                                        ->default($this->user->photo ? 'users/' . $this->user->photo : asset('/images/user-avatar.png')),

                                ])->columns(2)
                            ])
                            ->action(function (array $data) {
                                // The key is 'users.photo' in the form, not 'photo'
                                $photoPath = $data['users']['photo'] ?? null;

                                if ($photoPath) {
                                    $this->user->update([
                                        'photo' => basename($photoPath),
                                    ]);
                                    Notification::make()
                                        ->title('Profile has been updated')
                                        ->success()
                                        ->send();
                                } else {
                                    Notification::make()
                                        ->title('No photo was uploaded')
                                        ->danger()
                                        ->send();
                                }

                                $this->redirect(ReviewProfile::getUrl());
                            })
                    ])
                    ->schema([
                        ImageEntry::make('users.photo')
                            ->label('')
                            // ->defaultImageUrl($this->user->photo)
                            ->default(function () {
                                return $this->user->photo ? Storage::disk('s3')->url('users/' . $this->user->photo) : asset('/images/user-avatar.png');
                            })
                            ->width('138px')
                            ->height('138px')
                            ->circular()
                            ->extraAttributes(['class' => 'rounded-full'])
                            ->columnSpan(1),
                        TextEntry::make('name')
                            ->label('Name')
                            ->default(function () {
                                return (isset($this->user->name) && trim($this->user->name) !== '') ? $this->user->name : '-';
                            }),
                        TextEntry::make('email')
                            ->label('Email'),
                    ])->columns(3),

                InfoSection::make('Company Information')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->form(function ($record, $livewire) {
                                return [
                                    Group::make()->schema([
                                        TextInput::make('pcDetails.business_name')
                                            ->placeholder('Enter the business name')
                                            ->validationAttribute('Business Name')
                                            ->default($record->pcDetails->business_name)
                                            ->label(fn() => new HtmlString('Business Name <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->rules(function (Get $get) {
                                                if (!empty($this->user->id)) {
                                                    return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', Rule::unique('pc_details', 'business_name')->ignore($this->user->id, 'user_id')];
                                                }
                                                return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', 'unique:pc_details,business_name'];
                                            })->validationMessages([
                                                'required' => 'The Business Name field is required.',
                                                'string' => 'The Business Name must be a string.',
                                                'regex' => 'The Business Name must be a alphabetical.',
                                                'max' => 'The Business Name may not be greater than 64 characters.',
                                            ])->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'success' : null)
                                            ->live(),
                                        Select::make('pcDetails.company_type_id')
                                            ->default($record->pcDetails->company_type_id)
                                            ->placeholder('Select company type')
                                            ->rules(['required'])
                                            ->validationMessages([
                                                'required' => 'The Company Type field is required.',
                                            ])
                                            ->validationAttribute('Company Type')
                                            ->label(new HtmlString(
                                                'Company Type <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Select the type of company that best describes your business.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                            ))
                                            ->options(fn() => PcCompanyType::pluck('name', 'id'))
                                            ->live(), // enable live updating for onchange

                                        TextInput::make('pcDetails.company_name')
                                            ->default($record->pcDetails->company_name)
                                            ->placeholder('Enter the company name')
                                            ->validationAttribute('Company Name')
                                            ->label(function (Get $get) {
                                                // If company_type_id == 1, do not show red star
                                                $companyTypeId = $get('company_type_id') ?? $get('pcDetails.company_type_id');
                                                $star = $companyTypeId == 1
                                                    ? ''
                                                    : '<span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>';
                                                return new HtmlString('Company Name ' . $star);
                                            })
                                            ->rules(function (Get $get) {
                                                $companyTypeId = $get('company_type_id') ?? $get('pcDetails.company_type_id');
                                                $baseRules = ['max:64', 'regex:/^[a-zA-Z\s]+$/'];
                                                if (!empty($this->user->id)) {
                                                    $uniqueRule = Rule::unique('pc_details', 'company_name')->ignore($this->user->id, 'user_id');
                                                } else {
                                                    $uniqueRule = 'unique:pc_details,company_name';
                                                }
                                                if ($companyTypeId == 1) {
                                                    // Not required
                                                    return array_merge($baseRules, [$uniqueRule]);
                                                }
                                                // Required
                                                return array_merge(['required'], $baseRules, [$uniqueRule]);
                                            })
                                            ->validationMessages([
                                                'required' => 'The Company Name field is required.',
                                                'string' => 'The Company Name must be a string.',
                                                'regex' => 'The Company Name must be a alphabetical.',
                                                'max' => 'The Company Name may not be greater than 64 characters.',
                                            ])
                                            ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'success' : null)
                                            ->live(),

                                        TextInput::make('pcDetails.company_registration_number')
                                            ->placeholder('Enter the company registration number')
                                            ->label(function (Get $get) {
                                                // If company_type_id == 1, do not show red star
                                                $companyTypeId = $get('company_type_id') ?? $get('pcDetails.company_type_id');
                                                $star = $companyTypeId == 1
                                                    ? ''
                                                    : '<span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>';
                                                return new HtmlString('Company Registration Number ' . $star);
                                            })
                                            ->default($record->pcDetails->company_registration_number)
                                            ->validationAttribute('Company Registration Number')
                                            ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->rules(function (Get $get) {
                                                $companyTypeId = $get('company_type_id') ?? $get('pcDetails.company_type_id');
                                                $baseRules = ['regex:/^[a-zA-Z0-9]+$/', 'min:2', 'max:20'];
                                                if (!empty($this->user->id)) {
                                                    $uniqueRule = Rule::unique('pc_details', 'company_registration_number')->ignore($this->user->id, 'user_id');
                                                } else {
                                                    $uniqueRule = 'unique:pc_details,company_registration_number';
                                                }
                                                if ($companyTypeId == 1) {
                                                    // Not required
                                                    return array_merge($baseRules, [$uniqueRule]);
                                                }
                                                // Required
                                                return array_merge(['required'], $baseRules, [$uniqueRule]);
                                            })
                                            ->validationMessages([
                                                'required' => 'The Company Registration Number field is required.',
                                                'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                                'min' => 'The Company Registration Number must be at least 2 characters.',
                                                'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                                'unique' => 'The Company Registration Number has already been taken.',
                                            ]),
                                        TextInput::make('pcDetails.tin_number')->default($record->pcDetails->tin_number)
                                            ->placeholder('Enter TIN number')
                                            ->label(fn() => new HtmlString('TIN Number <span style="color: red;">*</span>'))
                                            ->validationAttribute('TIN Number')
                                            ->rules(function (Get $get) {
                                                if (!empty($this->user->id)) {
                                                    return ['required', 'min:1', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', Rule::unique('pc_details', 'tin_number')->ignore($this->user->id, 'user_id')];
                                                }
                                                return ['required', 'min:1', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'unique:pc_details,tin_number'];
                                            })
                                            ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->validationAttribute('TIN Number')
                                            ->validationMessages([
                                                'max' => 'The TIN number may not be greater than 20 characters.',
                                                'unique' => 'The TIN number has already been taken.',
                                                'min' => 'The TIN number must be at least 1 characters.',
                                                'regex' => 'The TIN number must be an alphanumeric string.',
                                                'required' => 'The TIN number field is required.',
                                            ]),
                                        TextInput::make('pcDetails.sstc_number')->default($record->pcDetails->sstc_number)
                                            ->placeholder('Enter SST number')
                                            ->validationAttribute('SST Number')
                                            ->label('SST Number')
                                            ->rules(function (Get $get) {
                                                if (!empty($this->user->id)) {
                                                    return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', Rule::unique('pc_details', 'sstc_number')->ignore($this->user->id, 'user_id')];
                                                }
                                                return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', 'unique:pc_details,sstc_number'];
                                            })
                                            ->validationAttribute('SST Number')
                                            ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->validationMessages([
                                                'unique' => 'The SST number has already been taken.',
                                                'max' => 'The SST number may not be greater than 20 characters.',
                                                'min' => 'The SST number must be at least 1 characters.',
                                                'regex' => 'The SST number must be an alphanumeric string.',
                                            ]),
                                        // TextInput::make('pcDetails.companyType.name')
                                        // ->placeholder('Company Type')
                                        // ->validationAttribute('Company Type')
                                        // ->label('Company Type')
                                        // ->default($record->pcDetails->companyType ? $record->pcDetails->companyType->name : ''),
                                        


                                    ])->columns(3)
                                ];
                            })
                            ->action(function ($record, $data) {
                                User::where('id', $record->id)->update(['name' => $data['pcDetails']['company_name']]);
                                $record->pcDetails
                                    ->updateOrCreate(
                                        ['user_id' => $record->id],
                                        Arr::only($data['pcDetails'], ['business_name', 'company_name', 'company_registration_number', 'tin_number', 'sstc_number', 'phone_number', 'web_url', 'company_type_id'])
                                    );
                            })
                    ])
                    ->schema([
                        TextEntry::make('pcDetails.business_name')
                            ->label('Business Name'),
                        TextEntry::make('pcDetails.company_name')
                            ->label('Company Name')
                            ->default(fn($record) => $record->pcDetails->company_name ?? '-'),
                        TextEntry::make('companyType.name')
                            ->label('Company Type')->default(function ($record) {
                                return $record->pcDetails->companyType ? $record->pcDetails->companyType->name : '';
                            }),

                        TextEntry::make('pcDetails.company_registration_number')
                            ->default(fn($record) => $record->pcDetails->company_registration_number ?? '-')
                            ->label('Company Registration Number'),
                        TextEntry::make('pcDetails.tin_number')
                            ->label('TIN Number'),
                        TextEntry::make('pcDetails.sstc_number')
                            ->label('SST Number'),


                    ])->columns(4),

                InfoSection::make('Contact Details')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record, $data) {
                                $landLine = $data['landline_number'];
                                $data['landline_number'] = $landLine['number'] ?? null;
                                $data['landline_code'] = $landLine['prefix'] ?? null;
                                $data['country_id'] = 132;

                                // $record->addresses()->delete();
                                $record->addresses()->updateOrCreate(['user_id' => $record->id], Arr::only($data, ['address_1', 'address_2', 'state_id', 'country_id', 'city_id', 'postal_code', 'phone_number', 'region', 'landline_number', 'landline_code']));
                                $record->pcDetails
                                    ->updateOrCreate(
                                        ['user_id' => $record->id],
                                        Arr::only($data, ['web_url', 'phone_number'])
                                    );

                                // $record->addresses()->delete();
                                $record->addresses()->updateOrCreate(['user_id' => $record->id], Arr::only($data, ['address_1', 'address_2', 'state_id', 'country_id', 'city_id', 'postal_code', 'phone_number', 'region', 'landline_number', 'landline_code']));
                                $record->pcDetails
                                    ->updateOrCreate(
                                        ['user_id' => $record->id],
                                        Arr::only($data, ['web_url'])
                                    );
                                Notification::make()
                                    ->title('Changes updated successfully')
                                    ->success()
                                    ->send();
                                $this->redirect(ReviewProfile::getUrl());
                                //     Arr::only($data, ['address_1', 'address_2','state_id','city_id','postal_code','phone_number']));
                            })
                            ->form(function ($record) {
                                $address = ($record->addresses->first());
                                // dd($address);
                                return [

                                    Group::make()->schema([
                                        TextInput::make('address_1')->default($address?->address_1)
                                            ->label(fn() => new HtmlString('Address 1 <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->validationAttribute('Address 1')
                                            ->rules(['required', 'string', 'max:100'])->placeholder('Enter address 1'),
                                        TextInput::make('address_2')->default($address?->address_2)
                                            ->rules(['nullable', 'string', 'max:100'])->placeholder('Enter address 2')
                                            ->label('Address 2'),
                                        Select::make('state_id')->default($address?->state_id)
                                            // ->label('State')
                                            ->label(fn() => new HtmlString('State <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->rules(['required'])
                                            ->validationAttribute('State')
                                            ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                            ->searchable()->placeholder('Select State')
                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                    ->where('country_id', 132)
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                // $info = State::where('id', $get('state_id'))->first();
                                                // if($info) {
                                                //     $set('region', ucfirst($info->zone));

                                                // }
                                                // $set('city_id', null);
                                                // $set('region', null);

                                                if (empty($get('state_id'))) {
                                                    $set('region', null);
                                                    $set('city_id', null);
                                                    $set('postal_code', null);
                                                    return;
                                                }
                                                $info = State::where('id', $get('state_id'))->first();
                                                if ($info) {
                                                    $set('region', ucfirst($info->zone));
                                                }
                                                $set('city_id', null);
                                            })
                                            ->live(),
                                        Select::make('city_id')->default($address?->city_id)
                                            ->label(fn() => new HtmlString('City <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))->placeholder('Select City')
                                            ->rules(['required'])
                                            ->live()
                                            ->searchable()
                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                    ->where('state_id', $get('state_id'))
                                                    ->pluck('name', 'id')
                                                    ->toArray();
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');
                                                if ($stateId && $cityId) {
                                                    $query->where('state_id', $stateId)->where("id", $cityId);
                                                }
                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (count($data) == 1) {
                                                    $set('landline_number.prefix', array_key_first($data));
                                                }

                                                $set('postal_code', null);
                                            })
                                            ->validationAttribute('City')
                                            ->options(function (Get $get) {
                                                return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                                            }),
                                        Select::make('postal_code')->default($address?->postal_code)->label('Postal Code')->placeholder('Select postal code')
                                            ->options(function (Get $get) {
                                                if (!empty($get('city_id'))) {
                                                    return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                                }
                                                return [];
                                            })
                                            ->required()
                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                if ($get('city_id')) {
                                                    return ZipCode::where('city_id', $get('city_id'))
                                                        ->where('code', 'like', "%{$search}%")
                                                        ->pluck('code', 'code')
                                                        ->toArray();
                                                }
                                                return [];
                                            })
                                            ->live(onBlur: true)
                                            ->optionsLimit(100)
                                            ->loadingMessage('Loading postal code...')
                                            ->searchable(),
                                        // TextInput::make('postal_code')->default($address->postal_code)
                                        // ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                        // ->extraAttributes([
                                        //     'inputmode' => 'numeric',
                                        //     'pattern' => '[0-9]*',
                                        //     'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                        //     'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                        // ])->live()
                                        // ->suffixIcon(fn ($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                        // ->suffixIconColor(fn ($state) => strlen($state) === 5 ? 'success' : null)
                                        // ->label(fn () => new HtmlString('Postcode <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                        // ->placeholder('Enter Postcode')->validationAttribute('Postcode'),
                                        TextInput::make('phone_number')
                                            ->prefix('+60')->default($this->user->pcDetails?->phone_number)
                                            ->placeholder('Enter mobile number')
                                            ->validationAttribute('Mobile Number')
                                            ->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])
                                            ->rules(['required', 'digits_between:8,12'])
                                            ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->live()
                                            ->label(fn() => new HtmlString('Mobile Number <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>')),
                                        // TextInput::make('landline_number')->default($address?->landline_number)
                                        //     ->placeholder('Enter landline number')
                                        //     ->validationAttribute('Landline Number')
                                        //     ->mask('9-9999999')
                                        //     ->prefix('+03')
                                        //     ->stripCharacters(['-'])
                                        //     ->extraAttributes([
                                        //         'inputmode' => 'numeric',
                                        //         'maxlength' => '8'
                                        //     ])
                                        //     ->rules(['required','digits_between:7,8'])
                                        //     ->suffixIcon(fn ($state) => in_array(strlen($state), [8, 9]) ? 'heroicon-s-check-circle' : null)
                                        //     ->suffixIconColor(fn ($state) => in_array(strlen($state), [8, 9]) ? 'success' : null)
                                        //     ->live()
                                        //     ->label(fn () => new HtmlString('Landline Number <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>')),

                                        TextInput::make('region')->default($address->region)
                                            ->placeholder('Enter region')->readonly()
                                            ->label('Region'),
                                        TextInput::make('web_url')->default($record?->pcDetails->web_url)
                                            ->label('Web Url')->nullable()->placeholder('Enter website url')->live()
                                            ->rules([
                                                'regex:/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/',
                                            ])
                                            ->helperText('Ex: https://www.example.com, http://example.com, www.example.com, example.com')
                                            ->suffixIcon(function ($state) {
                                                $isValid = $state
                                                    && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                                if (!$isValid) {
                                                    return null;
                                                }

                                                $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                                $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                                return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                                    ? 'heroicon-s-check-circle' : null;
                                            })
                                            ->suffixIconColor(function ($state) {
                                                $isValid = $state
                                                    && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                                if (!$isValid) {
                                                    return null;
                                                }

                                                $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                                $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                                return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                                    ? 'success' : null;
                                            }),
                                        PhoneWithPrefix::make('landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8'
                                            ])
                                            ->prefixOptions(function ($get, $set) {
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');

                                                if (empty($get('landline_number'))) {
                                                    return [];
                                                }
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');

                                                if ($stateId) {
                                                    $query->where('state_id', $stateId);

                                                    if ($cityId) {
                                                        $query->where('id', $cityId);
                                                    }
                                                }

                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (empty($data)) {
                                                    $data = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                // FacadesLog::info($get('addresses'));
                                                if (isset($get('addresses')["landline_code"]) && $get('addresses')["landline_code"] != null) {
                                                    $set('landline_code.prefix', $get("addresses")["landline_code"]);
                                                }
                                                return $data;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {
                                                if (isset($get('addresses')[0]["landline_code"]) && $get('addresses')[0]["landline_code"] != null) {
                                                    $set("landline_number.prefix", $get('addresses')[0]["landline_code"]);
                                                    $set("landline_number.number", $get('addresses')[0]["landline_number"]);
                                                } else {
                                                    $set("landline_number", ["prefix" => "", "number" => ""]);
                                                }
                                            })
                                            ->formatStateUsing(function ($state) use ($address) {
                                                if (is_array($address)) {
                                                    if (isset($address['landline_code'])) {
                                                        $state['prefix'] = $address['landline_code'];
                                                    }
                                                    if (isset($address['landline_number'])) {
                                                        $state['number'] = $address['landline_number'];
                                                    }
                                                }
                                                // Or if $address is an object
                                                elseif (is_object($address)) {

                                                    if (isset($address->landline_code)) {
                                                        $state['prefix'] = $address->landline_code;
                                                    }
                                                    if (isset($address->landline_number)) {
                                                        $state['number'] = $address->landline_number;
                                                    }
                                                    // dd($state);
                                                }
                                                // dd(is_array($state) ? $state : ["prefix" => "", "number" => ""]);
                                                return is_array($state) ? $state : ["prefix" => "", "number" => ""];
                                            })
                                            ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                    ])
                                        ->columns(3)

                                ];
                            })
                    ])
                    ->schema(function () {
                        $address = $this->user->addresses->first();
                        return [
                            TextEntry::make('pcDetails.phone_number')
                                ->formatStateUsing(function ($record) {
                                    return '+' . $this->user->pcDetails->phone_number_code . " " . $this->user->pcDetails->phone_number;
                                })
                                ->label('Mobile Number'),
                            TextEntry::make('addresses.landline_number')
                                ->formatStateUsing(function ($record) {
                                    return "+" . $record->addresses->first()->landline_code . " " . $record->addresses->first()->landline_number;
                                })
                                ->label('Landline Number'),
                            TextEntry::make('pcDetails.profile_email')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $this->user->pcDetails->profile_email ?? '';
                                })->label('Email'),
                            TextEntry::make('addresses.address_1')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->address_1 ?? '';
                                })
                                ->label('Address line 1'),
                            TextEntry::make('addresses.address_2')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->address_2 ?? '';
                                })
                                ->label('Address line 2'),
                            TextEntry::make('addresses.city.name')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->city->name ?? '';
                                })
                                ->label('City'),
                            TextEntry::make('addresses.state.name')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->state->name ?? '';
                                })
                                ->label('State'),

                            TextEntry::make('addresses.postal_code')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->postal_code ?? '';
                                })
                                ->label('Postal Code'),
                            TextEntry::make('addresses.region')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $address->region ?? '';
                                })
                                ->label('Region'),
                            TextEntry::make('pcDetails.web_url')
                                ->formatStateUsing(function ($state) use ($address) {
                                    return $this->user->pcDetails->web_url ?? '';
                                })
                                ->label('Web URL'),

                        ];
                    })->columns(4),
                InfoSection::make('Warehouse Addresses')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record, $data) {
                                $warehousesData = $data ?? [];
                                if ($data['warehouse_type'] == 'dpharma') {
                                    $record->warehouses()->delete();
                                    // $res = $record->warehouses()->create($warehousesData);
                                    $record->warehouses()->create([
                                        'warehouse_type' => $data['warehouse_type'],
                                        'address_1' => $data['address_1'] ?? null,
                                        'address_2' => $data['address_2'] ?? null,
                                        'state_id' => $data['state_id'] ?? null,
                                        'city_id' => $data['city_id'] ?? null,
                                        'postal_code' => $data['postal_code'] ?? null,
                                        'ware_region' => $data['ware_region'] ?? null,
                                        // Don't include min_order_value here
                                    ]);
                                    PcDetail::updateOrCreate(['user_id' => auth()->user()->id], [
                                        'delivery_days' => null,
                                        'delivery_days_west' => null,
                                        'min_order_value' => $data['min_order_value'] ?? null,
                                    ]);
                                } else {
                                    $record->warehouses()->updateOrCreate(
                                        ['user_id' => $record->id],
                                        [
                                            'warehouse_type' => 'owned',
                                            'address_1' => null,
                                            'address_2' => null,
                                            'city_id' => null,
                                            'state_id' => null,
                                            'ware_region' => null,
                                            'postal_code' => null,
                                        ]
                                    );
                                    PcDetail::updateOrCreate(['user_id' => auth()->user()->id], [
                                        'delivery_days' => $data['delivery_days'],
                                        'delivery_days_west' => $data['delivery_days_west'],
                                        'min_order_value' => $data['min_order_value'],
                                    ]);
                                }
                                return redirect()->route(ReviewProfile::getRouteName());

                                // dd($res);
                            })
                            ->form(function () {
                                $address = $this->user->warehouses->first();
                                return [
                                    Group::make()->schema([
                                        Radio::make('warehouse_type')
                                            ->label('Logistics Type')
                                            ->options([
                                                'owned' => new \Illuminate\Support\HtmlString(
                                                    'Own Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with your own logistics,Dpharma incurs no extra costs,and you handle product delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                                ),
                                                'dpharma' => new \Illuminate\Support\HtmlString(
                                                    'DPharma Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with DPharma logistics,our delivery partner manages product pickup and delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                                ),
                                            ])
                                            ->rules(['required'])
                                            ->required()
                                            ->validationMessages([
                                                'required' => 'Warehouse Type is required',
                                            ])->live()
                                            ->default($address?->warehouse_type),
                                        // Group::make()->schema([
                                        //     TextInput::make('warehouse_type')
                                        //             ->formatStateUsing(fn ($state) => $state ?? '')
                                        //             ->default($address['warehouse_type'] ?? "")
                                        //             ->label('Logistics Type')
                                        //             ->readOnly()
                                        // ])->visible(function (Get $get) {
                                        //     return $get('warehouse_type') === 'owned';
                                        // }),
                                        Group::make()->schema([

                                            TextInput::make('delivery_days_west')
                                                ->rules(['integer', 'min:1', 'max:7', 'required'])
                                                ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                                ->extraAttributes([
                                                    'inputmode' => 'numeric',
                                                    'pattern' => '[0-9]*',
                                                    'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                    'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                ])
                                                ->validationMessages([
                                                    'required' => 'The Delivery Days is required.',
                                                    'min' => 'The Delivery Days must be at least 1.',
                                                    'max' => 'The Delivery Days may not be greater than 7.',
                                                ])->columnSpan(1)
                                                ->default($this->user->pcDetails->delivery_days_west ?? ''),
                                        ])->visible(function (Get $get) {
                                            return $get('warehouse_type') === 'owned';
                                        }),
                                        Group::make()->schema([
                                            TextInput::make('delivery_days')
                                                ->rules(['integer', 'min:1', 'max:7', 'required'])
                                                ->label(new HtmlString("ETA for East Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                                ->extraAttributes([
                                                    'inputmode' => 'numeric',
                                                    'pattern' => '[0-9]*',
                                                    'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                    'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                ])
                                                ->validationMessages([
                                                    'required' => 'The Delivery Days is required.',
                                                    'min' => 'The Delivery Days must be at least 1.',
                                                    'max' => 'The Delivery Days may not be greater than 7.',
                                                ])->columnSpan(1)
                                                ->default($this->user->pcDetails->delivery_days ?? ''),
                                            TextInput::make('min_order_value')
                                                ->rules(['required', 'numeric', 'min:0.01', 'max:100000'])
                                                ->validationAttribute('min order value')
                                                ->label(new HtmlString("Minimum Order Value(Ringgit Malaysia)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                                ->extraAttributes([
                                                    'inputmode' => 'decimal',
                                                    'pattern' => '^\d+(?:\.\d+)?$',
                                                    'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                                    'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                                ])
                                                ->validationMessages([
                                                    'required' => 'The Minimum Order Value is required.',
                                                    'numeric' => 'The Minimum Order Value must be a decimal number.',
                                                    'min' => 'The Minimum Order Value must be at least 0.01.',
                                                    'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                                ])->columnSpan(1)
                                                ->default($this->user->pcDetails->min_order_value ?? ''),
                                        ])->visible(function (Get $get) {
                                            return $get('warehouse_type') === 'owned';
                                        }),
                                        Group::make()->schema([
                                            TextInput::make('address_1')
                                                ->rules(['required', 'string', 'max:100'])
                                                ->validationAttribute('Address 1')
                                                ->placeholder('Enter address line 1')
                                                ->default($address?->address_1)
                                                ->label(fn() => new HtmlString('Address 1 <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>')),
                                            TextInput::make('address_2')
                                                ->rules(['nullable', 'string', 'max:100'])
                                                ->validationAttribute('Address 2')
                                                ->placeholder('Enter address line 2')
                                                ->default($address?->address_2)
                                                ->label('Address 2'),

                                            Select::make('state_id')
                                                ->placeholder('Select State')->default($address?->state_id)
                                                ->rules(['required'])
                                                ->validationAttribute('State')
                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                    // $info = State::where('id', $get('state_id'))->first();
                                                    // if($info) {
                                                    //     $set('ware_region', ucfirst($info->zone));
                                                    // }
                                                    // $set('city_id', null);
                                                    // $set('ware_region', null);

                                                    if (empty($get('state_id'))) {
                                                        $set('ware_region', null);
                                                        $set('city_id', null);
                                                        $set('postal_code', null);
                                                        return;
                                                    }
                                                    $info = State::where('id', $get('state_id'))->first();
                                                    if ($info) {
                                                        $set('ware_region', ucfirst($info->zone));
                                                    }
                                                    $set('city_id', null);
                                                    $set('postal_code', null);
                                                })
                                                ->label(fn() => new HtmlString('State <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                                ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                ->searchable()
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                        ->where('country_id', 132)
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->live(),
                                            Select::make('city_id')
                                                ->placeholder('Select City')
                                                ->loadingMessage('Loading cities...')
                                                ->searchable()
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                        ->where('state_id', $get('state_id'))
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->rules(['required'])
                                                ->live()
                                                ->afterStateUpdated(function (Set $set) {
                                                    $set('postal_code', null); // Reset postal code when city changes
                                                })
                                                ->default($address?->city_id)
                                                ->label(fn() => new HtmlString('City <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                                ->validationAttribute('City')
                                                ->options(function (Get $get) {
                                                    return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                                                }),
                                            Select::make('postal_code')->default($address?->postal_code)->label('Postal Code')->placeholder('Select postal code')
                                                ->options(function (Get $get) {
                                                    if (!empty($get('city_id'))) {
                                                        return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                                    }
                                                    return [];
                                                })
                                                ->required()
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    if ($get('city_id')) {
                                                        return ZipCode::where('city_id', $get('city_id'))
                                                            ->where('code', 'like', "%{$search}%")
                                                            ->pluck('code', 'code')
                                                            ->toArray();
                                                    }
                                                    return [];
                                                })
                                                ->live(onBlur: true)
                                                ->optionsLimit(100)
                                                ->loadingMessage('Loading postal code...')
                                                ->searchable(),
                                            // TextInput::make('postal_code')
                                            //     ->validationAttribute('Postcode')->live()
                                            //     ->extraAttributes([
                                            //         'inputmode' => 'numeric',
                                            //         'pattern' => '[0-9]*',
                                            //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                            //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                            //     ])
                                            //     ->placeholder('Enter postcode')
                                            //     ->suffixIcon(fn($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                            //     ->suffixIconColor(fn($state) => strlen($state) === 5 ? 'success' : null)
                                            //     ->rules(['required', 'string', 'min:5', 'max:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                            //     ->label(fn() => new HtmlString('Postcode <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))->default($address?->postal_code),
                                            TextInput::make('ware_region')->default($address?->ware_region)
                                                ->placeholder('Enter region')->readonly()
                                                ->label('Region'),
                                            // TextInput::make('min_order_value')
                                            //     ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'numeric', 'min:1', 'max:100000', 'required'])
                                            //     ->validationAttribute('min order value')
                                            //     ->label(new HtmlString("Minimum Order Value(Ringgit Malaysia)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                            //     ->extraAttributes([
                                            //         'inputmode' => 'decimal',
                                            //         'pattern' => '^\d+(?:\.\d+)?$',
                                            //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                            //         'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                            //     ])
                                            //     ->validationMessages([
                                            //        'required' => 'The Minimum Order Value is required.',
                                            //        'numeric' => 'The Minimum Order Value must be a decimal number.',
                                            //        'min' => 'The Minimum Order Value must be at least 1.',
                                            //        'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                            //     ])->columnSpan(1)
                                            //     ->default($this->user->pcDetails->min_order_value ?? ''),
                                        ])
                                            ->columns(3)->visible(function (Get $get) {
                                                return $get('warehouse_type') === 'dpharma';
                                            })
                                            // ])
                                            // ->orderColumn('sort')
                                            ->columnSpanFull()
                                    ])->columns(3)
                                ];
                            })
                    ])
                    ->schema([
                        InfoGroup::make()
                            ->schema(function () {
                                $address = $this->user->warehouses->first();
                                if ($address?->warehouse_type === 'owned') {
                                    return [
                                        TextEntry::make('warehouse_type')
                                            ->formatStateUsing(fn() => 'Own Logistics')
                                            ->default($address['warehouse_type'] ?? "")
                                            ->label('Logistics Type'),
                                        TextEntry::make('delivery_days')
                                            ->default(isset($this->user->pcDetails->delivery_days) ? $this->user->pcDetails->delivery_days . ($this->user->pcDetails->delivery_days > 1 ? ' Days' : ' Day') : '')
                                            ->label('ETA for East Malaysia (Working Days)'),
                                        TextEntry::make('delivery_days_west')
                                            ->default(isset($this->user->pcDetails->delivery_days_west) ? $this->user->pcDetails->delivery_days_west . ($this->user->pcDetails->delivery_days_west > 1 ? ' Days' : ' Day') : '')
                                            ->label('ETA for West Malaysia (Working Days)'),
                                        TextEntry::make('min_order_value')
                                            ->default(!empty($this->user->pcDetails->min_order_value) ? $this->user->pcDetails->min_order_value : '-')
                                            ->label('Minimum Order Value (Ringgit Malaysia)'),
                                    ];
                                }
                                return [
                                    TextEntry::make('warehouse_type')
                                        ->formatStateUsing(function ($state) {
                                            return ucfirst(str_replace('dpharma', 'DPharma', $state));
                                        })
                                        ->default($address['warehouse_type'] ?? "")
                                        ->label('Logistics Type'),
                                    TextEntry::make('address_1')
                                        ->default($address['address_1'] ?? "")
                                        ->label('Address line 1'),
                                    TextEntry::make('address_2')
                                        ->default($address['address_2'] ?? "")
                                        ->label('Address line 2'),
                                    TextEntry::make('addresses.city.name')
                                        ->formatStateUsing(function ($state) use ($address) {
                                            return $address->city->name ?? '';
                                        })
                                        ->label('City'),
                                    TextEntry::make('addresses.state.name')
                                        ->formatStateUsing(function ($state) use ($address) {
                                            return $address->state->name ?? '';
                                        })
                                        ->label('State'),
                                    TextEntry::make('postal_code')
                                        ->default($address['postal_code'] ?? "")
                                        ->label('Postal Code'),
                                    TextEntry::make('ware_region')
                                        ->default($address['ware_region'] ?? "")
                                        ->label('Region'),
                                    // TextEntry::make('min_order_value')
                                    // ->default(!empty($this->user->pcDetails->min_order_value) ? $this->user->pcDetails->min_order_value : '-')
                                    // ->label('Minimum Order Value (Ringgit Malaysia)'),

                                ];
                            })->columns(4)

                    ]),



                InfoSection::make('Person In Charge')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record, $data) {
                                $record->pcDetails->update($data['pcDetails'] ?? []);
                            })
                            ->form(function ($record) {
                                return [
                                    Group::make()->schema([
                                        TextInput::make('pcDetails.person_in_charge_name')
                                            ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                            ->validationMessages([

                                                'regex' => 'The Full Name must be a alphabetical.',
                                                'max' => 'The Full Name may not be greater than 50 characters.',
                                            ])->label('Full Name')
                                            ->placeholder('Enter the full name')
                                            ->default($record->pcDetails->person_in_charge_name),
                                        TextInput::make('pcDetails.person_in_charge_email')
                                            ->rules(['email', 'max:50', 'required', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
                                            ->validationMessages([
                                                'email' => 'The Email must be a valid email address.',
                                                'max' => 'The Email may not be greater than 50 characters.',
                                                'required' => 'The Email field is required.',
                                                'regex' => 'The Email must be a valid email address.',
                                            ])->label(fn() => new HtmlString('Email <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->placeholder('Enter email')
                                            ->default($record->pcDetails->person_in_charge_email),
                                        TextInput::make('pcDetails.person_in_charge_nric')
                                            ->label(fn() => new HtmlString('NRIC / Passport Number'))
                                            ->placeholder('Enter the NRIC / Passport Number')
                                            ->validationAttribute('NRIC / Passport Number')
                                            ->rules(function (Get $get) {
                                                if (!empty($this->user->id)) {
                                                    return ['nullable', 'max:50'];
                                                }
                                                return ['nullable', 'max:50'];
                                            })
                                            ->live()
                                            ->suffixIcon(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                            ->validationMessages([
                                                'max' => 'The NRIC / Passport Number may not be greater than 50 characters.',
                                            ])
                                            ->default($record->pcDetails->person_in_charge_nric),
                                        TextInput::make('pcDetails.person_in_charge_phone')
                                            ->prefix('+60')->placeholder('Enter mobile number')
                                            ->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])->label(fn() => new HtmlString('Mobile Number <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->live()
                                            ->rules(['required', 'digits_between:8,12'])
                                            ->validationMessages([
                                                'required' => 'The Mobile Number field is required.',
                                                'digits_between' => 'The Mobile Number must be between 8 and 12 digits.',
                                            ])
                                            ->default($record->pcDetails->person_in_charge_phone),

                                    ])->columns(3)
                                ];
                            })
                    ])
                    ->schema([
                        TextEntry::make('pcDetails.person_in_charge_name')
                            ->label('Full Name'),
                        TextEntry::make('pcDetails.person_in_charge_email')
                            ->label('Email'),
                        TextEntry::make('pcDetails.person_in_charge_nric')
                            ->label(new HtmlString('<span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">NRIC / Passport Number</span><x-heroicon-o-information-circle />')),
                        TextEntry::make('pcDetails.person_in_charge_phone')
                            ->formatStateUsing(function ($state) {
                                return "+60" . " " . $state;
                            })
                            ->label('Mobile Number'),
                    ])->columns(4),
                InfoSection::make('Bank Details')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record, $data) {
                                PcDetail::where('user_id', $record->id)->update([
                                    'beneficiary_name' => encryptTextParam($data['beneficiary_name'] ?? null),
                                    'bank_name' => encryptTextParam($data['bank_name'] ?? null),
                                    'account_number' => encryptTextParam($data['account_number'] ?? null),
                                    // 'is_credit_line' => $data['is_credit_line'] ?? false,
                                ]);
                                Notification::make()
                                    ->title('Changes have been saved successfully')
                                    ->success()
                                    ->send();
                                $this->redirect(ReviewProfile::getUrl());
                            })
                            ->form(function ($record) {
                                return [
                                    Group::make()->schema([
                                        TextInput::make('beneficiary_name')
                                            ->rules(['required', 'string', 'max:50', 'regex:/^[a-zA-Z\s]+$/'])
                                            ->live()
                                            ->suffixIcon(fn($state) => (strlen($state) > 0 && strlen($state) <= 50 && preg_match('/^[a-zA-Z\s]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => (strlen($state) > 0 && strlen($state) <= 50 && preg_match('/^[a-zA-Z\s]+$/', $state)) ? 'success' : null)
                                            ->validationMessages([
                                                'required' => 'The Beneficiary Name field is required.',
                                                'regex' => 'The Beneficiary Name must be a alphabetical.',
                                                'max' => 'The Beneficiary Name may not be greater than 50 characters.',
                                            ])
                                            ->extraAttributes([
                                                'style' => 'font-family: monospace;', // Ensures consistent character width
                                            ])
                                            ->label(fn() => new HtmlString('Beneficiary Name <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->placeholder('Enter the beneficiary name')
                                            ->default(function () use ($record) {
                                                $name = decryptParam($record->pcDetails->beneficiary_name);
                                                if (!$name) return '';
                                                $len = strlen($name);
                                                if ($len <= 4) {
                                                    return str_repeat('*', $len);
                                                }
                                                return str_repeat('*', $len - 4) . substr($name, -4);
                                            }),
                                        Select::make('bank_name')
                                            ->label('Bank Name')
                                            ->options(function () {
                                                // Use a common helper or service to get bank names
                                                return getBankNames();
                                            })
                                            ->required()
                                            ->formatStateUsing(function ($state, $record) {
                                                $bankName = $record?->pcDetails?->bank_name ?? null;
                                                if (!$bankName) {
                                                    return '';
                                                }
                                                $bank = decryptParam($bankName);
                                                if (!$bank) {
                                                    return '';
                                                }
                                                $len = strlen($bank);
                                                if ($len <= 4) {
                                                    return '<span style="font-family: monospace; letter-spacing: 1px;">' . str_repeat('*', $len) . '</span>';
                                                }
                                                return '<span style="font-family: monospace; letter-spacing: 1px;">' . str_repeat('*', $len - 4) . '</span>' . substr($bank, -4);
                                            })
                                            ->searchable()
                                            ->validationMessages([
                                                'required' => 'The Bank Name field is required.',
                                            ])->allowHtml()
                                            ->placeholder('Select the bank name')
                                            ->default(function () use ($record) {
                                                $bank = decryptParam($record->pcDetails->bank_name);
                                                if (!$bank) return '';
                                                $len = strlen($bank);
                                                if ($len <= 4) {
                                                    return str_repeat('*', $len);
                                                }
                                                return str_repeat('*', $len - 4) . substr($bank, -4);
                                            }),
                                        TextInput::make('account_number')
                                            ->label(fn() => new HtmlString('Account Number <span style="color:red;font-size: 0.7em;vertical-align:super;">*</span>'))
                                            ->rules(['required', 'string', 'max:30', 'regex:/^[0-9]+$/'])
                                            ->live()
                                            ->suffixIcon(fn($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => (strlen($state) > 0 && strlen($state) <= 30 && preg_match('/^[0-9]+$/', $state)) ? 'success' : null)
                                            ->validationMessages([
                                                'required' => 'The Account Number field is required.',
                                                'regex' => 'The Account Number must be numeric.',
                                                'max' => 'The Account Number may not be greater than 30 characters.',
                                            ])
                                            ->extraAttributes([
                                                'style' => 'font-family: monospace;', // Ensures consistent character width
                                            ])
                                            ->placeholder('Enter the account number')
                                            ->default(function () use ($record) {
                                                $acc = decryptParam($record->pcDetails->account_number);
                                                if (!$acc) return '';
                                                $len = strlen($acc);
                                                if ($len <= 4) {
                                                    return str_repeat('*', $len);
                                                }
                                                return str_repeat('*', $len - 4) . substr($acc, -4);
                                            }),
                                        // Checkbox::make('pcDetails.is_credit_line')->label('Enable Credit Line')->default(fn () => $record->pcDetails?->is_credit_line ?? false)->columnSpanFull()->reactive(),
                                    ])->columns(2),
                                    // Clear button for bank fields
                                    \Filament\Forms\Components\Actions::make([
                                        \Filament\Forms\Components\Actions\Action::make('clear_bank_fields')
                                            ->label('Clear')
                                            ->color('gray')
                                            ->outlined()
                                            ->action(function (Set $set) {
                                                $set('beneficiary_name', '');
                                                $set('bank_name', '');
                                                $set('account_number', '');
                                            })
                                    ])->extraAttributes([
                                        'style' => 'margin-bottom: -188px;margin-left: 172px;margin-top: 76px;'
                                    ]),
                                    // Show a one-line note below the submit and cancel buttons
                                    \Filament\Forms\Components\Placeholder::make('')
                                        ->content(new HtmlString(
                                            '<span style="display:block;margin-top:4px;font-size:0.93em;color:#b45309;">Note: Bank information is hidden for security. To update, clear all fields and enter new details.</span>'
                                        )),

                                ];
                            })
                        // The clear button now resets the form fields using Filament's modal form state
                        // ->extraModalFooterActions([
                        //     Action::make('clear')
                        //         ->color('gray')
                        //         ->outlined()
                        //         ->button()
                        //         ->label('Clear')
                        //         ->action(function (Form $form) {
                        //             // Reset the form fields to empty values
                        //             $form->fill([
                        //                 'pcDetails.beneficiary_name' => '',
                        //                 'pcDetails.bank_name' => '',
                        //                 'pcDetails.account_number' => '',
                        //             ]);
                        //         })
                        // ])
                    ])
                    ->schema([
                        TextEntry::make('pcDetails.beneficiary_name')
                            ->formatStateUsing(function ($state, $record) {
                                $name = decryptParam($record?->pcDetails->beneficiary_name);
                                if (!$name) return '';
                                $len = strlen($name);
                                if ($len <= 4) {
                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                }
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($name, -4)));
                            })
                            ->label('Beneficiary Name'),
                        TextEntry::make('pcDetails.bank_name')
                            ->formatStateUsing(function ($state, $record) {
                                $bank = decryptParam($record?->pcDetails->bank_name);
                                if (!$bank) return '';
                                $len = strlen($bank);
                                if ($len <= 4) {
                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                }
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($bank, -4)));
                            })
                            ->label('Bank Name'),
                        TextEntry::make('pcDetails.account_number')
                            ->formatStateUsing(function ($state, $record) {
                                $acc = decryptParam($record?->pcDetails->account_number);
                                if (!$acc) return '';
                                $len = strlen($acc);
                                if ($len <= 4) {
                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                }
                                return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($acc, -4)));
                            })
                            ->label('Account Number'),
                        // TextEntry::make('pcDetails.is_credit_line')
                        //     ->label('Credit Line')
                        //     ->formatStateUsing(fn ($state): string => $state === true ? 'Enable' : 'Disable')
                        //     ->default('-')
                    ])->columns(4),

                InfoSection::make('Uploaded Documents')
                    ->headerActions([
                        \Filament\Infolists\Components\Actions\Action::make('edit')
                            ->color('gray')
                            ->outlined()
                            ->button()
                            ->icon('heroicon-o-pencil')
                            ->action(function ($record, $data) {
                                // $record->pcDetails->update($data['pcDetails'] ?? []);
                                $this->updateCertificateFiles($record, $data);
                            })
                            ->form(function ($record) {
                                $certificateFiles = PcCertificateFile::where('user_id', $record->id)->where('status', 'active')
                                    ->get()
                                    ->groupBy('type');

                                $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();
                                $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
                                // dd($record->pcDetails->id);
                                return [
                                    Group::make()->schema([

                                        FileUpload::make('pcDetails.company_registration_certificate')
                                            ->label(new HtmlString(
                                                'Company Registration Certificate 
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>'
                                            ))
                                            ->rules(['required', File::types(['jpeg', 'png', 'pdf'])])
                                            ->maxSize('2048')
                                            ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                            ->required()
                                            ->directory(function (Get $get, $record) {
                                                return config('constants.api.media.pc_medias') . $record->pcDetails->id;
                                            })
                                            ->default(function () use ($record, $companyCerts) {
                                                if (empty($companyCerts)) {
                                                    return [asset('/images/user-avatar.png')];
                                                }

                                                return array_map(function ($filename) use ($record) {
                                                    return 'legal-documents/pc/' . $record->pcDetails->id . '/' . $filename;
                                                }, $companyCerts);
                                            })
                                            ->validationMessages([
                                                'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'max' => 'File size must not exceed 2 MB',
                                                'required' => 'The Company Registration Certificate is required',
                                            ])
                                            ->acceptedFileTypes([
                                                'image/jpeg',
                                                'image/png',
                                                'application/pdf',
                                            ])
                                            ->validationAttribute('Upload Company Registration Certificate')
                                            ->downloadable()
                                            ->multiple()
                                            ->maxFiles(3)
                                            ->previewable(),
                                        FileUpload::make('pcDetails.license_permit')
                                            ->label(new HtmlString(
                                                'Relevant certification
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>'
                                            ))
                                            ->rules(['nullable', File::types(['jpeg', 'png', 'pdf'])])
                                            ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                            // ->required()
                                            ->directory(function (Get $get, $record) {
                                                return config('constants.api.media.pc_medias') . $record->pcDetails->id;
                                            })
                                            ->validationMessages([
                                                'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                'max' => 'File size must not exceed 2 MB',
                                            ])
                                            ->acceptedFileTypes([
                                                'image/jpeg',
                                                'image/png',
                                                'application/pdf',
                                            ])
                                            ->validationAttribute('Relevant License and Permit')
                                            ->multiple()
                                            ->maxFiles(3)
                                            ->maxSize('2048')
                                            ->downloadable()
                                            ->default(function () use ($record, $licensePermitFiles) {
                                                if (empty($licensePermitFiles)) {
                                                    return [asset('/images/user-avatar.png')];
                                                }

                                                return array_map(function ($filename) use ($record) {
                                                    return 'legal-documents/pc/' . $record->pcDetails->id . '/' . $filename;
                                                }, $licensePermitFiles);
                                            })
                                            ->previewable(),

                                    ])->columns(2)
                                ];
                            })
                    ])
                    ->schema(function ($record) {
                        // dd($record->pcDetails->license_permit);
                        $certificateFiles = PcCertificateFile::where('user_id', $record->id)->where('status', 'active')
                            ->get()
                            ->groupBy('type');

                        $licensePermitFiles = $certificateFiles->get('license_permit', collect())
                            ->pluck('name')
                            ->toArray();
                        $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')
                            ->toArray();
                        // dd($companyCerts);
                        return [
                            ViewEntry::make('license_permit')
                                ->view('filament.admin.resources.user-resource.pages.pdf-image')
                                ->viewData([
                                    'filePath' => $licensePermitFiles,
                                    'name' => 'license_permit',
                                    'record' => $record,
                                ])
                                ->label('Business License'),
                            ViewEntry::make('company_registration_certificates')
                                ->view('filament.admin.resources.user-resource.pages.pdf-image')
                                ->viewData([
                                    'filePath' => $companyCerts,
                                    'name' => 'company_registration_certificate',
                                    'record' => $record,
                                ])
                                ->label('Business Registration Certificate'),
                        ];
                    })->columns(2)

            ]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('toggle_credit_line')
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined()
                ->label(function ($record) {
                    return $this->user->pcDetails?->is_credit_line ? 'Disable Credit Line' : 'Enable Credit Line';
                })
                ->action(function () {
                    $current = $this->user->pcDetails?->is_credit_line;
                    $this->user->pcDetails()->update(['is_credit_line' => !$current]);
                }),
            Action::make('submit')
                ->label('Submit for Verification')
                ->modalHeading('Confirmation and Agreement')
                // ->modalDescription('Please read and acknowledge the following statements')
                ->modalSubmitActionLabel('Confirm & Submit')
                ->mountUsing(function () {
                    $this->acceptDeclaration = false;
                    $this->acceptPrivacyPolicy = false;
                })

                ->form([
                    Forms\Components\Group::make()
                        ->schema([
                            Forms\Components\Checkbox::make('declare')
                                ->label('I declare that the information I have given on this form is true and complete.')
                                ->rules(['required'])->live()
                                ->afterStateUpdated(function ($state) {
                                    $this->acceptDeclaration = $state;
                                })
                                ->required(),
                            Forms\Components\Checkbox::make('accept')
                                // ->label('I accept and agree to be bound by the Dpharma Terms of Services and Privacy Policy.')
                                ->label(new HtmlString(
                                    'I accept and agree to be bound by the Dpharma
                                <a href="' . route('pc.term-and-conditions') . '" target="_blank" class="text-primary-600 underline">Terms of Services</a> 
                                and 
                                <a href="' . route('pc.privacy.policy') . '" target="_blank" class="text-primary-600 underline">Privacy Policy</a>.'
                                ))
                                ->required()->live()
                                ->afterStateUpdated(function ($state) {
                                    $this->acceptPrivacyPolicy = $state;
                                })
                                ->rules(['required']),
                        ])
                ])
                ->modalSubmitAction(function ($action, Get $get) {
                    return $action->disabled(!$this->acceptDeclaration || !$this->acceptPrivacyPolicy);
                })
                ->action(function (array $data, $record) {
                    // Validator::make($data, [
                    //     'declare' => 'required',
                    //     'accept' => 'required',
                    // ])->validate();

                    auth()->user()->update([
                        'rejection_reason' => null,
                        'is_admin_verified' => false,
                        'verification_status' => 'pending'
                    ]);
                    $this->user->update([
                        'rejection_reason' => null,
                        'is_admin_verified' => false,
                        'verification_status' => 'pending'
                    ]);
                    $this->user->pcDetails->update([
                        'is_submitted' => true,
                        'step' => 5,
                    ]);
                    // Activity Log Start
                    $user = $this->user;
                    $pcName = $user->name ?? ($user->pcDetails->business_name ?? 'N/A');
                    activity()
                        ->causedBy(auth()->user())
                        ->performedOn($user)
                        ->useLog('pc_profile')
                        ->withProperties([
                            'attributes' => [
                                'status' => 'pending',
                            ],
                        ])
                        ->log("Pharmaceutical Supplier ({$pcName}) has been submitted profile for approval");

                    $admins = getAdminData();
                    $currentUserId = \Auth::id();
                    $currentUserName = \Auth::user()->name;

                    \Mail::to(\Auth::user()->email)->send(new \App\Mail\PcUserRegisterMail($user));

                    foreach ($admins as $admin) {
                        if ($admin->id === $currentUserId) {
                            continue; // skip current user
                        } else {
                            $admin->notify(new PcOnBoardingNotification($currentUserId, $currentUserName));
                        }
                        //send email to admins
                        $loginUrl = config('app.admin_url');
                        $link = '<a style="color:blue;" href="' . $loginUrl . '/users/' . $currentUserId . '?activeTab=Pending">View</a>';
                        //$link = '<a style="color:blue;" href="' . url('/users/' . $currentUserId . '?activeTab=Pending') . '">View</a>';
                        \Mail::to($admin->email)->send(new \App\Mail\PCRegisterMail(\Auth::user(), $link));
                    }
                    return redirect()->route(UnderVerificationProfile::getRouteName());
                })
        ];
    }

    protected function submitAction()
    {
        return
            Action::make('submit')
            ->label('Submit for Verification')
            ->modalHeading('Confirmation and Agreement')
            // ->modalDescription('Please read and acknowledge the following statements')
            ->modalSubmitActionLabel('Confirm & Submit')
            ->form([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Checkbox::make('declare')
                            ->label('I declare that the information I have given on this form is true and complete.')
                            ->rules(['required'])
                            ->required(),
                        Forms\Components\Checkbox::make('accept')
                            ->label('I accept and agree to be bound by the Dpharma Terms of Services and Privacy Policy.')
                            ->required()
                            ->rules(['required'])
                    ])
            ])
            // ->disabled(fn ($get) => !$get('declare') || !$get('accept'))
            // ->requiresConfirmation() // Ensures modal behavior
            ->action(function (array $data, $record) {
                // Validator::make($data, [
                //     'declare' => 'required',
                //     'accept' => 'required',
                // ])->validate();

                auth()->user()->update([
                    'rejection_reason' => null,
                    'is_admin_verified' => false,
                    'verification_status' => 'pending'
                ]);
                $this->user->update([
                    'rejection_reason' => null,
                    'is_admin_verified' => false,
                    'verification_status' => 'pending'
                ]);
                $this->user->pcDetails->update([
                    'is_submitted' => true,
                    'step' => 5,
                ]);
                return redirect()->route(UnderVerificationProfile::getRouteName());
            });
    }


    protected function updateCertificateFiles($record, $data): void
    {
        $userId = $record->id;

        $certificateTypes = [
            'company_registration_certificate',
            'license_permit',
        ];

        foreach ($certificateTypes as $type) {
            if (!empty($data['pcDetails'][$type])) {
                $files = is_array($data['pcDetails'][$type])
                    ? $data['pcDetails'][$type]
                    : [$data['pcDetails'][$type]];

                PcCertificateFile::where('user_id', $userId)
                    ->where('type', $type)
                    ->where('status', 'active')
                    ->update(['status' => 'inactive']);

                foreach ($files as $filePath) {
                    PcCertificateFile::create([
                        'user_id' => $userId,
                        'type' => $type,
                        'name' => basename($filePath),
                        'size' => getFormattedFileSize($filePath),
                        'status' => 'active',
                    ]);
                }
            }
        }
    }
}
