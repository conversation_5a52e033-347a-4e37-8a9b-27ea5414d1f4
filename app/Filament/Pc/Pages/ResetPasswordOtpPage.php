<?php

namespace App\Filament\Pc\Pages;

use App\Models\User;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use App\Traits\SendTwoFaCode;
use Filament\Facades\Filament;
use Filament\Pages\SimplePage;
use Illuminate\Support\HtmlString;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Forms\Concerns\InteractsWithForms;
use Hasan<PERSON><PERSON>\FilamentOtpInput\Components\OtpInput;
use Solutionforest\FilamentEmail2fa\Models\TwoFaCode;

class ResetPasswordOtpPage extends SimplePage implements HasForms
{
    use InteractsWithForms;
    use SendTwoFaCode;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    // protected static string $view = 'filament.pc.pages.reset-password-otp-page';

    protected static string $layout = 'filament-email-2fa::simple-layout';

    protected static string $view = 'filament-email-2fa::email-sent';

    public ?array $data = [
        'fa_code' => '',
    ];

    public string $email;

    public $id;
    // public string $fa_code;

    public static function getLabel(): string
    {
        return config('app.name');
    }

    public static function getRelativeRouteName(): string
    {
        return 'sf-filament-2fa.2fa';
    }

    public function mount($id)
    {
        $id = decryptParam($id);
        $this->id = $id;

        $user = User::find($id);

        if (! $user) {
            Notification::make()
                ->title('Something went wrong, please try again later.')
                ->danger()
                ->send();

            redirect()->to('/login'); // or wherever appropriate
            return; // avoid further execution
        }

        $this->email = $user->email;
    }

    public function resend()
    {
        $this->form->fill($this->data);
        $user = $this->getUser();
        $res = $this->sendTwoFaCode($user, 'password_reset');
        if ($res) {
            session()->flash('resent-success', __('filament-email-2fa::filament-email-2fa.resend_success'));
            $this->dispatch('otp-resent');
        }
    }

    public function resAction()
    {
        return Action::make('resend')
            ->link()
            ->color('gray-200')
            ->label('Resend OTP')
            ->action('resend')
            ->extraAttributes([
                'class' => 'btn-d-abled cl_text'
            ])
            ->keyBindings(['mod+s']);
    }
    public function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Verify')
                ->action('check')
                ->extraAttributes([
                    'class' => 'w-full',
                    'style' => 'background-color: rgba(12, 58, 81);'
                ])
                ->keyBindings(['mod+s']),
        ];
    }

    public function check()
    {
        $validCode = TwoFaCode::where([
            'user_id' => $this->id,
            'type' => 'password_reset',
        ])
            ->where('expiry_at', '>', now())
            ->first();
        // dd($validCode);
        if (!empty($validCode) && $this->data['fa_code'] == $validCode->code && $validCode->expiry_at > now()) {
            return to_route('pc.create-password', ['id' => encryptParam($this->id)]);
        }
        return Notification::make()->title('Wrong OTP or OTP has expired')->danger()->send();
    }

    public function getUser()
    {
        $guard = $this->getCurrentGuard();
        $model = config("auth.providers.{$guard}.model");
        $user = $model::where('email', $this->email)->first();

        return $user;
    }

    public function getCurrentGuard()
    {
        return Filament::getCurrentPanel()->getAuthGuard();
    }

    public function form(Form $form): Form
    {
        return $form;
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        OtpInput::make('fa_code')
                            ->label('')
                            ->numberInput(4)
                            ->rules(['required', 'min:4', 'max:4'])

                            ->validationMessages([
                                'required' => 'OTP is required/OTP must be of 4 characters',
                                'min' => 'OTP must be 4 characters',
                                'max' => 'OTP must be 4 characters',
                            ])
                            ->extraAttributes([
                                'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 4);",
                                'onkeydown' => "if (['e', 'E', '+', '-'].includes(event.key)) event.preventDefault();",
                            ])
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    public function hasFullWidthFormActions(): bool
    {
        return false;
    }

    public function getFormActionsAlignment(): string|Alignment
    {
        return Alignment::End;
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('<div>Verify Your OTP</div>');
    }

    public function hasLogo(): bool
    {
        return true;
    }
}
