<?php

namespace App\Filament\Exports;

use App\Models\SubOrder;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Support\Facades\Log;

class OrderExporter extends Exporter
{
    protected static ?string $model = SubOrder::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('order.order_number')
                ->label('Order ID')
                ->formatStateUsing(fn(?string $state): string => !empty($state) ? '#' . $state : '-'),
            ExportColumn::make('order.user.clinicData.clinic_name')
                ->label('Facility Name')
                ->formatStateUsing(fn($state, $record) => $record->order->user->clinicData->clinic_name ?? '-'),
            ExportColumn::make('order.created_at')
                ->label('Order Date')
                ->formatStateUsing(function ($state) {
                    $user = auth()->user()->id;
                    $format = \App\Models\PcDetail::where('user_id', $user)->value('date_format') ?? 'M d, Y';
                    return \Carbon\Carbon::parse($state)->format($format);
                }),
            ExportColumn::make('order_products_count')
                ->label('Items')
                ->getStateUsing(fn($record) => $record->order_products_count ?? 0),
            ExportColumn::make('total_sub_order_value')
                ->label('Order Total')
                ->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),
            ExportColumn::make('credit_line_status')
                ->label('Credit Line')
                ->getStateUsing(function (SubOrder $record) {
                    return $record->payment_type === 'credit_line' ? 'Yes' : 'No';
                }),
            ExportColumn::make('status')
                ->label('Order Status')
                ->formatStateUsing(fn($state) => $state ? ucwords(str_replace('_', ' ', $state)) : 'Unknown'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your order export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }

}