<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Resources\FullPayoutResource\Widgets\FullPayoutItemsTable;
use App\Filament\Admin\Resources\FullPayoutResource\Widgets\PcCommissionTable;
use Filament\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\Rule;
use Indianic\Settings\Models\GlobalSettings;

class Commission extends Page implements HasForms
{
    use InteractsWithForms;

    // protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.commission';

    protected static ?string $navigationLabel = 'Global Commission';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 6;

    public $commission;

    public $commission_type;
    public $commission_percentage;
    public $commission_flat;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('global-commission_view');
    }

    public function getBreadcrumbs(): array
    {
        return [
            // Dashboard::getUrl() => "Master",
            1 => "Master",
            3 => "Global Commission",
        ];
    }

    public function mount(): void
    {
        $this->commission = DB::table('global_settings')->whereIn('name', ['commission_flat', 'commission_percentage'])->pluck('value', 'name')
            ->toArray();

        $this->commission_percentage = $this->commission['commission_percentage'] ?? '0';
        $this->commission_flat = $this->commission['commission_flat'] ?? '0';

        $this->form->fill([
            'commission_flat' => $this->commission_flat,
            'commission_percentage' => $this->commission_percentage,
        ]);
    }


    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Commission')
                    ->schema([
                        Grid::make(2)
                            ->schema([
                                TextInput::make('commission_percentage')
                                    ->label(new HtmlString("Value in Percentage<span class='text-danger-600 dark:text-danger-400 font-medium'> *</span>"))
                                    ->prefix('%')
                                    ->numeric()
                                    ->inputMode('decimal')
                                    ->step('0.01')
                                    ->default(2)
                                    ->placeholder(__('message.commission.percentage_placeholder'))
                                    ->rules(['required', 'numeric', 'min:0.01', 'max:100'])
                                    ->validationMessages([
                                        'required' => __('message.commission.required'),
                                        'numeric' => __('message.commission.numeric'),
                                        'min' => __('message.commission.min'),
                                        'max' => __('message.commission.commission_percentage_max'),
                                    ]),

                                TextInput::make('commission_flat')
                                    ->label(new HtmlString("Flat Value(RM)<span class='text-danger-600 dark:text-danger-400 font-medium'> *</span>"))
                                    ->prefix('RM')
                                    ->numeric()
                                    ->maxLength(5)
                                    ->inputMode('decimal')
                                    ->step('0.01')
                                    ->default($this->commission_flat)
                                    ->placeholder(__('message.commission.flat_value_placeholder'))
                                    ->rules(['numeric', 'min:0'])
                                    ->validationMessages([
                                        'required' => __('message.commission.required'),
                                        'numeric' => __('message.commission.numeric'),
                                        'min' => __('message.commission.min'),
                                        'max' => __('message.commission.commission_flat_max'),
                                    ]),
                            ]),

                        Placeholder::make('commission_note')->label(false)
                            ->content(__('message.commission.content'),)
                            ->extraAttributes(['class' => 'text-gray-500 text-sm']),
                    ])
                    ->columns(1)
            ]);
    }

    public function save(): void
    {
        $this->validateSection();
        $data = $this->form->getState();

        $settings = [
            ['name' => 'commission_flat', 'value' => $data['commission_flat']],
            ['name' => 'commission_percentage', 'value' => $data['commission_percentage']],
        ];

        // foreach ($settings as $setting) {
        //     DB::table('global_settings')->updateOrInsert(
        //         ['name' => $setting['name']], // Search by name
        //         ['value' => $setting['value']] // Update value or insert if not found
        //     );
        // }

        foreach ($settings as $setting) {
            $globalSetting = GlobalSettings::where('name', $setting['name'])->first();

            if ($globalSetting) {
                $globalSetting->update(['value' => $setting['value']]);
            } else {
                GlobalSettings::create([
                    'name' => $setting['name'],
                    'value' => $setting['value']
                ]);
            }
        }

        Notification::make()->title(__('message.commission.commission_updated'))->success()->send();
    }

    protected function validateSection(): void
    {
        $rules = [
            'commission_percentage' => ['required', 'max:99'],
            'commission_flat' => [
                'required',
                'numeric',
                'min:1',
                'max:99999',
            ],
        ];

        $messages = [
            'commission_flat.required' => __('message.commission.commission_flat_required'),
            'commission_percentage.required' => __('message.commission.commission_percentage_required'),
            'commission_flat.numeric' => __('message.commission.commission_flat_numeric'),
            'commission_flat.min' => __('message.commission.commission_flat_min'),
            'commission_flat.max' => __('message.commission.commission_flat_max'),
            'commission_percentage.max' => __('message.commission.commission_percentage_max'),
        ];

        $this->validate($rules, $messages);
    }

    public function getFormActions(): array
    {
        return [
            Action::make('save')
                ->visible(fn() => auth()->user()->hasRole('Super Admin') || auth()->user()->can('global-commission_update'))
                ->label('Save')
                ->action('save')
                ->color('primary'),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            PcCommissionTable::class,
        ];
    }
}
