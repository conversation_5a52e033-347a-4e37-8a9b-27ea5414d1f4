<?php

namespace App\Filament\Admin\Pages;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Product;
use Livewire\Component;
use App\Enums\LabelEnum;
use App\Models\Category;
use Filament\Pages\Page;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Mail\ProductRejectedMail;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Contracts\HasTable;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\CategorySubCategoryFilter;
use App\Filament\Admin\Resources\UserResource;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Tables\Concerns\InteractsWithTable;
use App\Filament\Admin\Resources\ProductResource;
use App\Filament\Admin\Resources\ProductResource\Pages\ViewProduct;

class PendingApprovalProducts extends Page implements HasTable, HasForms
{
    use InteractsWithTable;
    use InteractsWithForms;

    // protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.pending-approval-products';

    protected static ?string $title = 'Pending Approval Products';

    public static function canAccess(): bool
    {
        $user = auth()->user();

        $isPharmaceuticalCompany = isPharmaceuticalCompany();

        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') ||  $user->can('pending-requests_approve product');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return Product::query()->pendingApprovalsForAdmin()
                    ->with(['category', 'subcategory', 'productData', 'batches']);
            })
            ->actionsColumnLabel('Action')
            ->defaultSort('id', 'desc')
            ->recordUrl(fn($record)  => ViewProduct::getUrl([$record->id]),)
            ->filtersFormWidth('4xl')
            ->filtersFormColumns(2)

            ->columns([
                TextColumn::make('id')
                    ->toggleable()
                    ->label('Product ID')
                    ->prefix('#')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->toggleable()
                    ->label('Product Name')
                    ->searchable(),
                TextColumn::make('generic.name')
                    ->toggleable()
                    ->label('Generic Name')
                    ->searchable(),
                TextColumn::make('category.name')
                    ->toggleable()
                    ->label('Category')
                    ->searchable(),
                TextColumn::make('subcategory.name')
                    ->toggleable()
                    ->label('Subcategory')
                    ->searchable(),
                TextColumn::make('add_request_by')
                    ->label('Added From')
                    ->toggleable()
                    ->formatStateUsing(fn($state) => $state ? User::find($state)->name : ''),
                TextColumn::make('status')
                    ->label('Status'),
                TextColumn::make('updated_at')
                    ->toggleable()
                    ->label('Created by')
                    ->formatStateUsing(function ($record) {
                        $name = User::find($record->owner_id)->name;
                        return $name;
                    })
                    ->action(fn($record)  => ViewProduct::getUrl([$record->id]),),
                TextColumn::make('created_at')
                    ->label('Submitted On')
                    ->default('-')
                    ->toggleable()
                    ->formatStateUsing(function ($record) {
                        $submittedOn = $record->productDataForPC($record->owner_id)?->submitted_on;
                        $userTimezone = Auth::user()->timezone;
                        return !empty($submittedOn) ? Carbon::parse($submittedOn)->timezone($userTimezone)->format('d-m-Y h:i A') : '-';
                    }),
            ])

            ->filters([
                SelectFilter::make('status')
                    ->label(LabelEnum::STATUS->value)
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),

                SelectFilter::make('generic_name_id')
                    ->label(LabelEnum::GENERIC_NAME->value)
                    ->relationship('generic', 'name')
                    ->searchable()
                    ->preload(),

                CategorySubCategoryFilter::make('category-subcategory'),

                SelectFilter::make('add_request_by')
                    ->label(LabelEnum::ADDED_FROM->value)
                    ->options(function () {
                        return User::whereHas('productRelations')
                            ->whereNotNull('name')
                            ->where('name', '!=', '')
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->searchable(),

                // Filter::make('created_at')
                //     ->label('Created Date Range')
                //     ->form([
                //         \Filament\Forms\Components\Section::make('Created Date Range')
                //             ->schema([
                //                 Grid::make(2)
                //                     ->schema([
                //                         DatePicker::make('created_from')
                //                             ->label('From')
                //                             ->placeholder('Start date')
                //                             ->maxDate(today()),
                //                         DatePicker::make('created_until')
                //                             ->label('Until')
                //                             ->placeholder('End date')
                //                             ->minDate(fn($get) => $get('created_from'))
                //                             ->maxDate(today()),
                //                     ]),
                //             ])
                //     ])
                //     ->query(function (Builder $query, array $data): Builder {
                //         return $query
                //             ->when(
                //                 $data['created_from'],
                //                 fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                //             )
                //             ->when(
                //                 $data['created_until'],
                //                 fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                //             );
                //     })
                //     ->indicateUsing(function (array $data): array {
                //         $indicators = [];
                //         if ($data['created_from'] ?? null) {
                //             $indicators['created_from'] = 'Created from ' . \Carbon\Carbon::parse($data['created_from'])->format('M j, Y');
                //         }
                //         if ($data['created_until'] ?? null) {
                //             $indicators['created_until'] = 'Created until ' . \Carbon\Carbon::parse($data['created_until'])->format('M j, Y');
                //         }
                //         return $indicators;
                //     }),

                Filter::make('submitted_on')
                    ->label('Submitted Date Range')
                    ->form([
                        \Filament\Forms\Components\Section::make('Submitted Date Range')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        DatePicker::make('submitted_from')
                                            ->label('From')
                                            ->placeholder('Start date')
                                            ->maxDate(today()),
                                        DatePicker::make('submitted_until')
                                            ->label('Until')
                                            ->placeholder('End date')
                                            ->minDate(fn($get) => $get('submitted_from'))
                                            ->maxDate(today()),
                                    ]),
                            ])
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['submitted_from'],
                                fn(Builder $query, $date): Builder => $query->whereHas('productData', function ($subQuery) use ($date) {
                                    $subQuery->whereDate('submitted_on', '>=', $date);
                                }),
                            )
                            ->when(
                                $data['submitted_until'],
                                fn(Builder $query, $date): Builder => $query->whereHas('productData', function ($subQuery) use ($date) {
                                    $subQuery->whereDate('submitted_on', '<=', $date);
                                }),
                            );
                    })
                    ->columnSpanFull()
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['submitted_from'] ?? null) {
                            $indicators['submitted_from'] = 'Submitted from ' . \Carbon\Carbon::parse($data['submitted_from'])->format('M j, Y');
                        }
                        if ($data['submitted_until'] ?? null) {
                            $indicators['submitted_until'] = 'Submitted until ' . \Carbon\Carbon::parse($data['submitted_until'])->format('M j, Y');
                        }
                        return $indicators;
                    }),
            ])

            ->actions([

                \Filament\Tables\Actions\Action::make('view')
                    ->label(LabelEnum::VIEW->value)
                    ->tooltip(LabelEnum::VIEW->value)
                    ->icon('heroicon-o-eye')
                    ->size('sm')
                    ->iconButton()
                    ->color('gray')
                    ->visible(function ($record) {
                        $user = \Illuminate\Support\Facades\Auth::user();
                        return $user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_view');
                    })
                    ->action(function ($record) {
                        return redirect()->to(\App\Filament\Admin\Resources\ProductResource::getUrl('view', ['record' => $record->id]));
                    }),
                // Action::make('approve')
                //     ->icon('heroicon-o-check')
                //     ->visible(function ($record, $livewire) {
                //         return $record->status == 'pending';
                //     })
                //     ->color('success')
                //     ->tooltip('Approve Product')
                //     ->requiresConfirmation()
                //     ->action(function ($record) {
                //         $record->update(['status' => 'approved', 'approved_by' => Auth::id(), 'approved_on' => now()]);
                //         $record->productData?->update(['admin_approval' => true, 'is_rejected' => false]);

                //         // Activity Log Start
                //         // activity()
                //         //    ->causedBy(auth()->user())
                //         //     ->performedOn($record)
                //         //     ->useLog('product_approval')
                //         //     ->withProperties([
                //         //         'old' => [
                //         //             'status' => 'pending',
                //         //         ],
                //         //         'attributes' => array_filter([
                //         //             'status' => $record->status,
                //         //             'product_name' => $record->name ?? '',
                //         //         ], fn($value) => !is_null($value)),
                //         //     ])
                //         //     ->log(($record->name ?? '') . ' product has been approved');
                //         // Activity Log End

                //         // Get the user who should receive the notification
                //         $userId = $record->owner_id;
                //         $productDataForUser = $record->productDataForPc($userId);
                //         if ($productDataForUser) {
                //             Notification::make()
                //                 ->title('Your product has been approved by admin.')
                //                 ->body('Approved')
                //                 ->success()
                //                 ->actions([
                //                     \Filament\Notifications\Actions\Action::make('view')
                //                         ->label('View Product')
                //                         ->url(url(config('app.pc_url') . '/products/' . $record->id . "/view"))
                //                 ])
                //                 ->sendToDatabase(User::find($productDataForUser->user_id));
                //         }
                //         Notification::make()
                //             // ->title('Product Approved')
                //             ->title('Product approved successfully.')
                //             ->send();
                //     })
                //     // ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                //     // ->size('sm')
                //     ->iconButton(),
                // Action::make('reject')
                //     ->icon('heroicon-o-x-mark')
                //     ->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                //     ->tooltip('Reject Product')
                //     ->requiresConfirmation()
                //     ->modalHeading('Reject Product?')
                //     ->modalDescription('Are you sure you want to reject this product? This action cannot be undone.')
                //     ->modalSubmitActionLabel('Yes, Reject')
                //     ->modalIcon('heroicon-o-x-circle') // Optional icon
                //     ->color('danger') // Makes button red like delete modal
                //     ->form([
                //         Textarea::make('reason')
                //             ->label(new HtmlString("Reason <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                //             ->validationAttribute('reason')
                //             ->rules(['required', 'string'])
                //             ->maxLength(255)
                //     ])
                //     ->action(function ($record, $data) {
                //         $userId = $record->owner_id;
                //         $createdBy = User::find($record->productDataForPC($userId)?->user_id);
                //         if (empty($record->productDataForPc($userId))) {
                //             Notification::make()
                //                 ->title('Product Data Not Found')
                //                 ->danger()
                //                 ->send();
                //             return;
                //         }
                //         $record->productDataForPc($userId)->update([
                //             'admin_approval' => false,
                //             'rejected_reason' => $data['reason'],
                //             'is_rejected' => true,

                //         ]);
                //         $res = $record->update([
                //             'approved_by' => Auth::id(),
                //             'approved_on' => now(),
                //             'admin_verified_on' => now(),
                //             'status' => 'rejected',
                //         ]);

                //         // Activity Log Start
                //         // activity()
                //         //    ->causedBy(auth()->user())
                //         //     ->performedOn($record)
                //         //     ->useLog('product_approval')
                //         //     ->withProperties([
                //         //         'old' => [
                //         //             'status' => 'pending',
                //         //         ],
                //         //         'attributes' => array_filter([
                //         //             'status' => $record->status,
                //         //             'product_name' => $record->name ?? '',
                //         //             'rejected_reason' => $data['reason'] ?? '',
                //         //         ], fn($value) => !is_null($value)),
                //         //     ])
                //         //     ->log(($record->name ?? '') . ' product has been rejected');
                //         // Activity Log End

                //         Mail::to($createdBy->email)->send(new ProductRejectedMail($data['reason'], $record, $createdBy));

                //         if ($createdBy) {
                //             Notification::make()
                //                 ->body("Rejected")
                //                 ->info()
                //                 ->title("Your product ($record->name) has been rejected by admin.")
                //                 ->actions([
                //                     \Filament\Notifications\Actions\Action::make('view')
                //                         ->label('View Product')
                //                         ->url(url(config('app.pc_url') . '/products/' . $record->id . "/view"))
                //                 ])
                //                 ->sendToDatabase($createdBy);
                //         }

                //         Notification::make()
                //             ->title('Product rejected successfully.')
                //             ->send();
                //     }),
            ])

            ->bulkActions([
                // ...
            ]);
    }
}
