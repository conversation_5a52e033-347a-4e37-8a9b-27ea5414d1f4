<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Widgets\FacelityProfitTable;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Indianic\Settings\Models\GlobalSettings;
use Illuminate\Support\Facades\Mail;
use App\Models\OtpVerification;
use Carbon\Carbon;
use App\Mail\SendOtpMail;

class ProfitShareSettings extends Page implements HasForms
{
    use InteractsWithForms;

    // protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'Settings';
    protected static string $view = 'filament.admin.pages.profit-share-settings';
    protected static ?string $navigationLabel = 'Profit Share';
    protected static ?string $label = 'Profit Share';
    // protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 6;
    public $otp;
    public $generatedOtp;
    public $optTime;
    public $email = '';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin');
    }

    public function mount(): void
    {
        $this->email = auth()->user()->email;
        $this->optTime = config('constants.api.otp_time');
        // if (!auth()->user()->otp_verified) {
        //    // $this->otpCreate(auth()->user()->email, $this->optTime,'Digital Pharma Profit Share OTP');

        // }
        $globalSettings = GlobalSettings::all()->keyBy('name');
    }

    public function otpCreate($email, $time, $subject)
    {
        $otp = generateOTP();
        $this->email = $email;
        //before timing should not be update otp
        $res = OtpVerification::updateOrCreate(['email' => $email], [
            'email' => $email,
            'otp' => $otp,
            'expires_at' => Carbon::now()->addMinutes($time),
        ]);
        Mail::to($email)->send(new SendOtpMail($otp, $time, $subject));

        return [
            // 'otp' => $res['otp'],
            'expires_at' => $time * 60,
        ];
    }



    protected function getFormActions(): array
    {
        return [
            // Action::make('update')->submit('Update'),
        ];
    }



    protected function getFooterWidgets(): array
    {
        return [
            FacelityProfitTable::class,
        ];
    }
}
