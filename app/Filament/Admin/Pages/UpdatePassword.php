<?php

namespace App\Filament\Admin\Pages;

use App\Models\User;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\Rules\Password;

class UpdatePassword extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.update-password';

    protected static bool $shouldRegisterNavigation = false;

    public ?string $current_password;

    public ?string $password;

    public ?string $password_confirmation;

    public ?User $user = null;

    public function getBreadcrumbs(): array
    {
        return [
            MyProfile::getUrl() => "My Profile",
            3 => "Update Password",
        ];
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(MyProfile::getUrl()),
        ];
    }

    public function form(Form $form): Form
    {

        return $form->schema([
            Section::make()->schema([
                Group::make()->schema([
                    TextInput::make('current_password')
                        ->rules(['required'])
                        ->exists(modifyRuleUsing: function (Exists $rule) {
                            return Hash::check($this->current_password, $this->user->password);
                        })
                        ->password()
                        ->revealable(),
                    TextInput::make('password')
                        ->label('New Password')
                        ->rules([
                            'required',
                            'confirmed',
                            Password::defaults()->numbers()->symbols()
                        ])
                        ->password()
                        ->revealable(),
                    TextInput::make('password_confirmation')
                        ->label('Confirm Password')
                        ->rules(['required', 'same:password', Password::defaults()->numbers()->symbols()])
                        ->same('password')
                        ->password()
                        ->revealable(),
                ])->columns(3),
            ]),
        ]);
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('update')->submit('Update')->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-50 cursor-wait',
            ]),

            Action::make('cancel')
                ->label('Cancel')
                ->url(fn() => MyProfile::getUrl())
                ->color('gray')
        ];
    }

    public function update()
    {
        $this->validate();
        if (! Hash::check($this->current_password, Filament::auth()->user()->password)) {
            return Notification::make()->title('Current password is wrong !')->success()->danger()->send();
        }
        if ($this->current_password === $this->password) {
            Notification::make()->title('The new password cannot be the same as the current password.')->danger()->send();
            return;
        }
        $user = Filament::auth()->user();

        // activity()
        //     ->causedBy($user)
        //     ->useLog('password_update')
        //     ->performedOn()
        //     ->log("Admin user Password has been updated");

        $user->update(['password' => Hash::make($this->password)]);
        session()->put('user', $user->toArray());
        Notification::make()->title('Password updated successfully !')->success()->send();
        $this->password = '';
        $this->password_confirmation = '';
        $this->current_password = '';
    }
}
