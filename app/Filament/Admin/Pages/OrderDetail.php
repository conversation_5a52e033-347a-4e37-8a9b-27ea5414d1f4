<?php

namespace App\Filament\Admin\Pages;

use Filament\Pages\Page;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\SubOrder;
use Illuminate\Support\Facades\Auth;
use Filament\Actions\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class OrderDetail extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.order-detail';
    protected static bool $shouldRegisterNavigation = false;
    public User $user;
    public Order $order;
    public $statusCounts;
    public $suborder;
    public $isModalOpen = false;
    public $description = '';
    public $selectedItem = null;
    public function mount(Request $request)
    {
        $this->user = Auth::user();
        $orderId = request()->query('order_id');
        $this->order = Order::find($orderId);
        $this->order = Order::with(['orderProductsGrouped' => function ($query) {
            $query->select('order_id', 'status', DB::raw('COUNT(*) as count'))
                ->groupBy('order_id', 'status');
        }, 'subOrder', 'subOrder.user', 'orderProducts', 'orderProducts.product'])->find($orderId);

        $statusCounts = $this->order->orderProductsGrouped->mapWithKeys(function ($product) {
            $statusLabel = \App\Models\OrderProduct::STATUS_DETAIL[$product->status] ?? 'Unknown';
            return [$statusLabel => $product->count];
        });
        $this->statusCounts = $statusCounts->toArray();
        //dd($this->statusCounts);
    }


    public function openModal($id)
    {
        $this->selectedItem = collect($this->order->subOrder)->firstWhere('id', $id);
        $this->isModalOpen = true;
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->selectedItem = null;
    }
}
