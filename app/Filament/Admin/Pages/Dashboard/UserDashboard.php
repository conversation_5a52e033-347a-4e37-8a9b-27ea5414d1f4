<?php

namespace App\Filament\Admin\Pages\Dashboard;

use App\Filament\Admin\Widgets\UserAccountVerified;
use App\Filament\Admin\Widgets\UsersChart;
use App\Filament\Admin\Widgets\UserStatsOverview;
use App\Filament\Admin\Widgets\BlogPostsChart;
use App\Filament\Admin\Widgets\PsRevuewVsEarningChart;
use Filament\Navigation\NavigationItem;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Pages\Dashboard;
use Filament\Pages\Page;

class UserDashboard extends Page
{
  use HasFiltersAction;

  protected static ?string $routePath = 'dashboard';

  protected static ?string $navigationGroup = 'Dashboard';

  protected static ?int $navigationSort = 1;

  protected static ?string $navigationIcon = null;

  protected static string $view = 'filament.admin.pages.dashboard';

  protected static ?string $title = 'Users';

  public static function canAccess(): bool
  {
    return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_users view');
  }
  public function getHeaderWidgets(): array
  {
    return [
      UserStatsOverview::class,
      UsersChart::class,
      PsRevuewVsEarningChart::class,
      UserAccountVerified::class,
    ];
  }
  public static function getNavigationItems(): array
  {
    return [
      NavigationItem::make('Users')
        ->url(static::getUrl())
        ->isActiveWhen(
          fn() =>
          request()->routeIs('filament.admin.pages.dashboard') ||
            request()->routeIs(static::getRouteName())
        )
        ->group('Dashboard'),
    ];
  }
}
