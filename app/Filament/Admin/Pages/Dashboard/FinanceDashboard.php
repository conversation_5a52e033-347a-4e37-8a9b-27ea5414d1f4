<?php

namespace App\Filament\Admin\Pages\Dashboard;

use App\Filament\Admin\Widgets\FinanceOverview;
use Filament\Pages\Page;
use App\Filament\Admin\Widgets\LowSellingProducts;
use App\Filament\Admin\Widgets\PayoutsOverview;
use App\Filament\Admin\Widgets\ProductStatsOverview;
use App\Filament\Admin\Widgets\RevenueChart;
use App\Filament\Admin\Widgets\TopOrder;
use App\Filament\Admin\Widgets\TopRevenue;
use App\Filament\Admin\Widgets\TopSellingProducts;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;

class FinanceDashboard extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?string $navigationIcon = null;

    protected static ?int $navigationSort = 4;

    protected static ?string $title = 'Finance';

    protected static string $view = 'filament.admin.pages.finance-dashboard';
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_finance view');
    }
    protected function getHeaderWidgets(): array
    {
        return [
            FinanceOverview::class,
            PayoutsOverview::class,
            RevenueChart::class,
            // TopOrder::class,
            // TopRevenue::class,
        ];
    }
}
