<?php

namespace App\Filament\Admin\Pages\Dashboard;

use App\Filament\Admin\Widgets\PayoutHistory\PayoutsOverview;
use App\Filament\Admin\Widgets\PayoutHistory\PendingPayout;
use App\Filament\Admin\Widgets\PayoutHistory\ProcessedPayout;
use App\Filament\Admin\Widgets\PayoutHistory\ScheduledPayoutChart;
use Filament\Pages\Page;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Model;

class PayoutHistoryDashboard extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Finance';
    protected static ?string $navigationLabel = 'Dashboard';

    protected static ?string $navigationIcon = null;

    protected static ?int $navigationSort = 1;

    protected static ?string $title = 'Finance';
    protected static ?string $description = 'Note: The payout to the pharmaceutical company (PC) will be processed 3 days after the holding period, in line with the payout schedule.';
    protected static string $view = 'filament.admin.pages.finance-dashboard';

    protected function getHeaderWidgets(): array
    {

        return [
            PayoutsOverview::class,
            ProcessedPayout::class, //this is full payout chart with filter pending and processed
            ScheduledPayoutChart::class,
            PendingPayout::class,
        ];
    }
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('finance_dashboard');
    }

    public function getHeader(): ?View
    {
        return view('filament.admin.pages.dashboard.payout-history-dashboard-header');
    }
}
