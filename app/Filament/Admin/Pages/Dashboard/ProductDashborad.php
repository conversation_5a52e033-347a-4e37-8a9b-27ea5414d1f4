<?php

namespace App\Filament\Admin\Pages\Dashboard;

use App\Filament\Admin\Widgets\LowSellingProducts;
use App\Filament\Admin\Widgets\ProductStatsOverview;
use App\Filament\Admin\Widgets\TopSellingProducts;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Pages\Page;
use App\Filament\Admin\Widgets\TopOrder;
use App\Filament\Admin\Widgets\TopRevenue;

class ProductDashborad extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?string $navigationIcon = null;

    protected static ?int $navigationSort = 3;

    protected static ?string $title = 'Products';

    protected static string $view = 'filament.admin.pages.product-dashboard';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_product view');
    }
    protected function getHeaderWidgets(): array
    {
        return [
            ProductStatsOverview::class,
            TopSellingProducts::class,
            // LowSellingProducts::class,
            TopOrder::class,
            TopRevenue::class,
        ];
    }
}
