<?php

namespace App\Filament\Admin\Pages\Dashboard;

use App\Filament\Admin\Widgets\OrdersChart;
use App\Filament\Admin\Widgets\OrderStatsOverview;
use App\Filament\Admin\Widgets\RecentOrders;
use Filament\Pages\Dashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;
use Filament\Pages\Page;

class OrderDashboard extends Page
{
    use HasFiltersAction;

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = 2;

    protected static ?string $navigationIcon = null;

    protected static ?string $title = 'Orders';

    protected static string $view = 'filament.admin.pages.order-dashboard';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dashboard_order view');
    }
    protected function getHeaderWidgets(): array
    {
        return [
            OrderStatsOverview::class,
            RecentOrders::class,
            OrdersChart::class,
        ];
    }
}
