<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Pages\Dashboard\UserDashboard;
use App\Filament\Admin\Widgets\PsRevuewVsEarningChart;
use Filament\Pages\Page;
use Filament\Facades\Filament;
use Filament\Pages\Dashboard as BasePage;
use App\Filament\Admin\Widgets\StatsOverview;
use App\Filament\Admin\Widgets\UserAccountVerified;
use App\Filament\Admin\Widgets\UsersChart;
use App\Filament\Admin\Widgets\UserStatsOverview;
use Filament\Navigation\NavigationItem;

class Dashboard extends BasePage
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    // protected static string $view = 'filament.admin.pages.dashboard';
    protected static ?string $title = 'Users';
    protected static string $view = 'filament.admin.pages.dashboard';

    protected static bool $shouldRegisterNavigation = false;

    public function getWidgets(): array
    {
        return [
            StatsOverview::class,

        ];
    }
    public function getHeaderWidgets(): array
    {
        return [
            UserStatsOverview::class,
            UsersChart::class,
            PsRevuewVsEarningChart::class,
            UserAccountVerified::class,
        ];
    }
}
