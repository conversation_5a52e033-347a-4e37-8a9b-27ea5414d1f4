<?php

namespace App\Filament\Admin\Pages;

use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;

class EditProfile extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static string $view = 'filament.admin.pages.edit-profile';

    protected static bool $shouldRegisterNavigation = false;

    public $user;

    public $name;

    public $email;

    public $photo;

    public function getBreadcrumbs(): array
    {
        return [
            MyProfile::getUrl() => "My Profile",
            3 => "Edit Profile",
        ];
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(MyProfile::getUrl()),
        ];
    }

    public function mount()
    {
        $this->user = Auth::user();

        $this->form->fill([
            'name' => $this->user->name,
            'email' => $this->user->email,
            'photo' => $this->user->photo,
        ]);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    FileUpload::make('photo')
                        ->rules(['image', 'max:2048'])
                        ->label('Photo')
                        ->image()
                        ->directory('users')
                        ->helperText('Max size: 2MB. Only images are allowed.'),
                    TextInput::make('name')
                        ->label(new HtmlString('Name <span style="color: red;">*</span>'))
                        ->validationAttribute('name')
                        ->rules(['required', 'regex:/^[a-zA-Z\s]+$/', 'max:15'])
                        ->unique(ignoreRecord: true),
                    TextInput::make('email')->disabled(),
                ]),

            ]);
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')->submit('Save')->extraAttributes([
                'wire:loading.attr' => 'disabled',
                'wire:loading.class' => 'opacity-50 cursor-wait',
            ]),
            Action::make('cancel')
                ->label('Cancel')
                ->url(fn() => MyProfile::getUrl())
                ->color('gray')
        ];
    }

    public function update()
    {
        $this->validate();
        $data = $this->form->getState();
        $this->user->update([
            'name' => $data['name'],
            'photo' => $data['photo'], // Use the new photo path if uploaded
        ]);
        Notification::make()->title('Profile Updated Successfully')->success()->send();
        return redirect()->to(MyProfile::getUrl());
    }
}
