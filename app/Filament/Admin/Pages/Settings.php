<?php

namespace App\Filament\Admin\Pages;

use App\Filament\Admin\Widgets\FacelityProfitTable;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action as ActionsAction;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\IconPosition;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Indianic\Settings\Models\GlobalSettings;

class Settings extends Page implements HasForms
{
    use InteractsWithForms;

    // protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';
    protected static ?string $navigationGroup = 'Settings';
    protected static string $view = 'filament.admin.pages.settings';

    // protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 6;

    public $contact_email;
    public $contact_number;
    public $mail_smtp_host;
    public $mail_smtp_driver;
    public $mail_smtp_port;
    public $mail_smtp_encryption;
    public $mail_smtp_username;
    public $mail_smtp_password;
    public $mail_smtp_mail_from_name;
    public $mail_smtp_mail_from_email;
    public $facebook_url;
    public $twitter_url;
    public $linkedin_url;
    public $instagram_url;
    public $youtube_url;

    public static function canAccess(): bool
    {
        return (auth()->user()->hasRole('Super Admin') || auth()->user()->can('settings_view'));
    }

    public function mount(): void
    {
        if (!auth()->user()->otp_verified) {
            // redirect()->route('otp.verify'); // Make sure this route exists
        }
        $globalSettings = GlobalSettings::all()->keyBy('name');
        $this->form = $this->form->fill([
            //General Settings
            'contact_email' => $globalSettings['contact_email']['value'] ?? '',
            'contact_number' => $globalSettings['contact_number']['value'] ?? '',
            //SMTP Settings
            'mail_smtp_host' => $globalSettings['mail_smtp_host']['value'] ?? '',
            'mail_smtp_driver' => $globalSettings['mail_smtp_driver']['value'] ?? '',
            'mail_smtp_port' => $globalSettings['mail_smtp_port']['value'] ?? '',
            'mail_smtp_encryption' => $globalSettings['mail_smtp_encryption']['value'] ?? '',
            'mail_smtp_username' => $globalSettings['mail_smtp_username']['value'] ?? '',
            'mail_smtp_password' => $globalSettings['mail_smtp_password']['value'] ?? '',
            'mail_smtp_mail_from_name' => $globalSettings['mail_smtp_mail_from_name']['value'] ?? '',
            'mail_smtp_mail_from_email' => $globalSettings['mail_smtp_mail_from_email']['value'] ?? '',
            //Social Settings
            'facebook_url' => $globalSettings['facebook_url']['value'] ?? '',
            'twitter_url' => $globalSettings['twitter_url']['value'] ?? '',
            'linkedin_url' => $globalSettings['linkedin_url']['value'] ?? '',
            'instagram_url' => $globalSettings['instagram_url']['value'] ?? '',
            'youtube_url' => $globalSettings['youtube_url']['value'] ?? '',
        ]);
    }

    public static function canAccessGeneralSetting(): bool
    {

        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('settings_general settings update');
    }
    public static function canAccessSocialSetting(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('settings_social media update');
    }
    public static function canAccessSMTPSetting(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('settings_smtp settings update');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('General Setting')
                    // ->visible(function () {
                    //     return static::canAccessGeneralSetting();
                    // })
                    ->schema([

                        TextInput::make('contact_email')
                            ->label(new HtmlString("Contact Email <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                        TextInput::make('contact_number')
                            ->mask('999999999999')
                            ->validationAttribute('Contact Number')
                            ->stripCharacters(['-'])
                            ->extraAttributes([
                                'inputmode' => 'numeric',
                                'maxlength' => '12'
                            ])
                            ->rules(['digits_between:8,12'])
                            ->live()
                            ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                            ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                            // ->required()
                            ->label(new HtmlString("Contact Number <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                        Actions::make([

                            ActionsAction::make('general_settings_update')
                                ->label('Save')
                                ->visible(fn() => auth()->user()->can('settings_general settings update') || auth()->user()->hasRole('Super Admin'))
                                ->action(function () {

                                    $this->validateSection('general');
                                    $data = $this->form->getState();

                                    // Activity Log Start
                                    $oldValues = GlobalSettings::whereIn('name', ['contact_email', 'contact_number'])->pluck('value', 'name')->toArray();
                                    $oldAttributes = [
                                        'contact_email' => $oldValues['contact_email'] ?? null,
                                        'contact_number' => $oldValues['contact_number'] ?? null,
                                    ];
                                    // Activity Log End

                                    $settings = [
                                        ['name' => 'contact_email', 'value' => $data['contact_email']],
                                        ['name' => 'contact_number', 'value' => $data['contact_number']],
                                    ];
                                    foreach ($settings as $setting) {
                                        DB::table('global_settings')->updateOrInsert(
                                            ['name' => $setting['name']], // Search by name
                                            ['value' => $setting['value']] // Update value or insert if not found
                                        );
                                    }

                                    // Activity Log Start
                                    $newAttributes = [
                                        'contact_email' => $data['contact_email'],
                                        'contact_number' => $data['contact_number'],
                                    ];
                                    $changedData = array_diff_assoc($newAttributes, $oldAttributes);
                                    if (!empty($changedData)) {
                                        activity()
                                            ->causedBy(auth()->user())
                                            ->useLog('settings_update')
                                            ->withProperties([
                                                'old' => array_filter(array_intersect_key($oldAttributes, $changedData), fn($value) => !is_null($value)),
                                                'attributes' => array_filter(array_intersect_key($newAttributes, $changedData), fn($value) => !is_null($value)),
                                            ])
                                            ->log('General settings updated');
                                    }
                                    // Activity Log End

                                    Notification::make()->title('Settings Updated')->body('General Settings Updated Successfully')->success()->send();
                                })
                        ])->columnSpan(2),

                    ])->columnSpan(1)->columns(2),

                // Section::make('SMTP Details')->visible(function () {
                //     return static::canAccessSMTPSetting();
                // })
                //     ->schema([
                //         TextInput::make('mail_smtp_host')
                //             ->label(new HtmlString("SMTP Host <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_driver')
                //             ->label(new HtmlString("SMTP Driver <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_port')
                //             ->label(new HtmlString("SMTP Port <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_encryption')
                //             ->label("SMTP Encryption"),
                //         TextInput::make('mail_smtp_username')
                //             ->label(new HtmlString("SMTP Username <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_password')
                //             ->label(new HtmlString("SMTP Password <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_mail_from_email')
                //             ->label(new HtmlString("SMTP From Email Address <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         TextInput::make('mail_smtp_mail_from_name')
                //             ->label(new HtmlString("SMTP Form Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>")),
                //         Actions::make([
                //             ActionsAction::make('smtp_update')
                //                 ->label('Save')
                //                 ->visible(fn() => auth()->user()->can('settings_smtp_settings_update') || auth()->user()->hasRole('Super Admin'))
                //                 ->action(function () {
                //                     // $this->validate();
                //                     $this->validateSection('smtp');
                //                     $data = $this->form->getState();

                //                     // Activity Log Start
                //                     $oldValues = GlobalSettings::whereIn('name', [
                //                         'mail_smtp_host',
                //                         'mail_smtp_driver',
                //                         'mail_smtp_port',
                //                         'mail_smtp_encryption',
                //                         'mail_smtp_username',
                //                         'mail_smtp_password',
                //                         'mail_smtp_mail_from_name',
                //                         'mail_smtp_mail_from_email'
                //                     ])->pluck('value', 'name')->toArray();
                //                     $oldAttributes = [
                //                         'mail_smtp_host' => $oldValues['mail_smtp_host'] ?? null,
                //                         'mail_smtp_driver' => $oldValues['mail_smtp_driver'] ?? null,
                //                         'mail_smtp_port' => $oldValues['mail_smtp_port'] ?? null,
                //                         'mail_smtp_encryption' => $oldValues['mail_smtp_encryption'] ?? null,
                //                         'mail_smtp_username' => $oldValues['mail_smtp_username'] ?? null,
                //                         'mail_smtp_password' => $oldValues['mail_smtp_password'] ?? null,
                //                         'mail_smtp_mail_from_name' => $oldValues['mail_smtp_mail_from_name'] ?? null,
                //                         'mail_smtp_mail_from_email' => $oldValues['mail_smtp_mail_from_email'] ?? null,
                //                     ];
                //                     // Activity Log End

                //                     $settings = [
                //                         ['name' => 'mail_smtp_host', 'value' => $data['mail_smtp_host']],
                //                         ['name' => 'mail_smtp_driver', 'value' => $data['mail_smtp_driver']],
                //                         ['name' => 'mail_smtp_port', 'value' => $data['mail_smtp_port']],
                //                         ['name' => 'mail_smtp_encryption', 'value' => $data['mail_smtp_encryption']],
                //                         ['name' => 'mail_smtp_username', 'value' => $data['mail_smtp_username']],
                //                         ['name' => 'mail_smtp_password', 'value' => $data['mail_smtp_password']],
                //                         ['name' => 'mail_smtp_mail_from_name', 'value' => $data['mail_smtp_mail_from_name']],
                //                         ['name' => 'mail_smtp_mail_from_email', 'value' => $data['mail_smtp_mail_from_email']],
                //                     ];
                //                     foreach ($settings as $setting) {
                //                         DB::table('global_settings')->updateOrInsert(
                //                             ['name' => $setting['name']], // Search by name
                //                             ['value' => $setting['value']] // Update value or insert if not found
                //                         );
                //                     }

                //                     // Activity Log Start
                //                     $newAttributes = [
                //                         'mail_smtp_host' => $data['mail_smtp_host'],
                //                         'mail_smtp_driver' => $data['mail_smtp_driver'],
                //                         'mail_smtp_port' => $data['mail_smtp_port'],
                //                         'mail_smtp_encryption' => $data['mail_smtp_encryption'],
                //                         'mail_smtp_username' => $data['mail_smtp_username'],
                //                         'mail_smtp_password' => $data['mail_smtp_password'],
                //                         'mail_smtp_mail_from_name' => $data['mail_smtp_mail_from_name'],
                //                         'mail_smtp_mail_from_email' => $data['mail_smtp_mail_from_email'],
                //                     ];
                //                     $changedData = array_diff_assoc($newAttributes, $oldAttributes);
                //                     if (!empty($changedData)) {
                //                         activity()
                //                             ->causedBy(auth()->user())
                //                             ->useLog('settings_update')
                //                             ->withProperties([
                //                                 'old' => array_filter(array_intersect_key($oldAttributes, $changedData), fn($value) => !is_null($value)),
                //                                 'attributes' => array_filter(array_intersect_key($newAttributes, $changedData), fn($value) => !is_null($value)),
                //                             ])
                //                             ->log('SMTP settings updated');
                //                     }
                //                     // Activity Log End

                //                     Notification::make()->title('Settings Updated')->body('SMTP Settings Updated Successfully')->success()->send();
                //                 })
                //         ]),

                //     ])->columnSpan(1)->columns(2),

                Section::make('Social Media')
                    // ->visible(function () {
                    //     return static::canAccessSocialSetting();
                    // })
                    ->schema([
                        TextInput::make('facebook_url')
                            ->label('Facebook URL')
                            ->prefixIcon('eva-facebook'),
                        TextInput::make('twitter_url')
                            ->label('X URL')
                            ->prefixIcon('ri-twitter-x-fill'),
                        TextInput::make('linkedin_url')
                            ->label('Linkedin URL')
                            ->prefixIcon('ri-linkedin-fill'),
                        TextInput::make('instagram_url')
                            ->label('Instagram URL')
                            ->prefixIcon('ri-instagram-fill'),
                        TextInput::make('youtube_url')
                            ->label('Youtube URL')
                            ->prefixIcon('ri-youtube-fill'),
                        Actions::make([
                            ActionsAction::make('social_media_update')
                                ->label('Save')
                                ->visible(fn() => auth()->user()->can('settings_social media update') || auth()->user()->hasRole('Super Admin'))
                                ->action(function () {
                                    // $this->validate();
                                    $this->validateSection('social');
                                    $data = $this->form->getState();

                                    // Activity Log Start
                                    $oldValues = GlobalSettings::whereIn('name', [
                                        'facebook_url',
                                        'twitter_url',
                                        'linkedin_url',
                                        'instagram_url',
                                        'youtube_url'
                                    ])->pluck('value', 'name')->toArray();
                                    $oldAttributes = [
                                        'facebook_url' => $oldValues['facebook_url'] ?? null,
                                        'twitter_url' => $oldValues['twitter_url'] ?? null,
                                        'linkedin_url' => $oldValues['linkedin_url'] ?? null,
                                        'instagram_url' => $oldValues['instagram_url'] ?? null,
                                        'youtube_url' => $oldValues['youtube_url'] ?? null,
                                    ];
                                    // Activity Log End

                                    $settings = [
                                        ['name' => 'facebook_url', 'value' => $data['facebook_url']],
                                        ['name' => 'twitter_url', 'value' => $data['twitter_url']],
                                        ['name' => 'linkedin_url', 'value' => $data['linkedin_url']],
                                        ['name' => 'instagram_url', 'value' => $data['instagram_url']],
                                        ['name' => 'youtube_url', 'value' => $data['youtube_url']],
                                    ];
                                    foreach ($settings as $setting) {
                                        DB::table('global_settings')->updateOrInsert(
                                            ['name' => $setting['name']], // Search by name
                                            ['value' => $setting['value']] // Update value or insert if not found
                                        );
                                    }

                                    // Activity Log Start
                                    $newAttributes = [
                                        'facebook_url' => $data['facebook_url'],
                                        'twitter_url' => $data['twitter_url'],
                                        'linkedin_url' => $data['linkedin_url'],
                                        'instagram_url' => $data['instagram_url'],
                                        'youtube_url' => $data['youtube_url'],
                                    ];
                                    $changedData = array_diff_assoc($newAttributes, $oldAttributes);
                                    if (!empty($changedData)) {
                                        activity()
                                            ->causedBy(auth()->user())
                                            ->useLog('settings_update')
                                            ->withProperties([
                                                'old' => array_filter(array_intersect_key($oldAttributes, $changedData), fn($value) => !is_null($value)),
                                                'attributes' => array_filter(array_intersect_key($newAttributes, $changedData), fn($value) => !is_null($value)),
                                            ])
                                            ->log('Social media settings updated successfully');
                                    }

                                    // Activity Log End

                                    Notification::make()->title('Settings Updated')->body('Social Settings Updated Successfully')->success()->send();
                                })
                        ])->columnSpan(2)->columns(1),

                    ])->columnSpan(1)->columns(2),
            ]);
    }

    protected function getFormActions(): array
    {
        return [
            // Action::make('update')->submit('Update'),
        ];
    }

    protected function validateSection(string $section): void
    {
        $rules = [];
        if ($section === 'general') {

            $rules = [
                'contact_email' => ['required', 'email', 'regex:/^[^@]+@[^@]+\.[a-zA-Z]{2,}$/'],
                'contact_number' => ['required', 'numeric'],
            ];
            $messages = [
                'contact_email.required' => 'The contact email field is required.',
                'contact_email.email' => 'The contact email must be a valid email address.',
                'contact_email.regex' => 'The contact email must be a valid email address.',
                'contact_number.required' => 'The contact number field is required.',
                'contact_number.numeric' => 'The contact number must be a valid number.'
            ];
        } elseif ($section === 'smtp') {
            $rules = [
                'mail_smtp_host' => ['required'],
                'mail_smtp_driver' => ['required'],
                'mail_smtp_port' => ['required', 'numeric'],
                'mail_smtp_encryption' => [],
                'mail_smtp_username' => ['required'],
                'mail_smtp_password' => ['required'],
                'mail_smtp_mail_from_name' => ['required'],
                'mail_smtp_mail_from_email' => ['required', 'email'],
            ];
            $messages = [
                'mail_smtp_host.required' => 'The SMTP host is required.',
                'mail_smtp_driver.required' => 'The SMTP driver is required.',
                'mail_smtp_port.required' => 'The SMTP port is required.',
                'mail_smtp_port.numeric' => 'The SMTP port must be a valid number.',
                'mail_smtp_encryption.required' => 'The SMTP encryption is required.',
                'mail_smtp_username.required' => 'The SMTP username is required.',
                'mail_smtp_password.required' => 'The SMTP password is required.',
                'mail_smtp_mail_from_name.required' => 'The SMTP from name is required.',
                'mail_smtp_mail_from_email.required' => 'The SMTP from email is required.',
                'mail_smtp_mail_from_email.email' => 'The SMTP from email must be a valid email address.',
            ];
        } elseif ($section === 'social') {
            $rules = [
                'facebook_url' => ['url'],
                'twitter_url' => ['url'],
                'linkedin_url' => ['url'],
                'instagram_url' => ['url'],
                'youtube_url' => ['url'],
            ];
            $messages = [
                // 'facebook_url.required' => 'The Facebook URL is required.',
                'facebook_url.url' => 'The Facebook URL must be a valid URL.',

                // 'twitter_url.required' => 'The Twitter URL is required.',
                'twitter_url.url' => 'The Twitter URL must be a valid URL.',

                // 'linkedin_url.required' => 'The LinkedIn URL is required.',
                'linkedin_url.url' => 'The LinkedIn URL must be a valid URL.',

                // 'instagram_url.required' => 'The Instagram URL is required.',
                'instagram_url.url' => 'The Instagram URL must be a valid URL.',

                // 'youtube_url.required' => 'The YouTube URL is required.',
                'youtube_url.url' => 'The YouTube URL must be a valid URL.',
            ];
        }


        $this->validate($rules, $messages);
    }
}
