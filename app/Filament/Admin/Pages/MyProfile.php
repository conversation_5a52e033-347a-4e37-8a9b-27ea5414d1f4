<?php

namespace App\Filament\Admin\Pages;

use App\Models\User;
use Filament\Actions\Action;
use Filament\Pages\Page;
use Illuminate\Support\Facades\Auth;

class MyProfile extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.admin.pages.my-profile';

    protected static bool $shouldRegisterNavigation = false;

    public User $user;

    public function mount()
    {
        $this->user = Auth::user();
    }

    protected function getHeaderActions(): array
    {
        return [

            Action::make('edit')->label('Update Password')->url(fn () => UpdatePassword::getUrl()),
            Action::make('edit_profile')
                ->label('Edit')
                // ->icon('heroicon-m-pencil')
                ->url(fn () => EditProfile::getUrl()),
        ];
    }
}
