<?php

namespace App\Filament\Admin\Widgets;

use App\Filament\Admin\Resources\ClinicResource;
use App\Filament\Admin\Resources\ProductResource;
use App\Filament\Admin\Resources\UserResource;
use App\Models\ClinicDetail;
use App\Models\PcDetail;
use App\Models\Product;
use App\Models\User;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class UserStatsOverview extends BaseWidget
{
    protected static ?string $navigationGroup = 'Dashboard';
    protected static ?int $sort = 1;
    protected static ?string $navigationIcon = null;
    protected static ?string $label = 'Users';
    protected int | string | array $rowSpan = 'full';
    protected static ?string $pollingInterval = '10s';
    protected static string $view = 'filament.admin.widgets.user-stats-overview';

    // protected function getHeaderActions(): array
    // {
    //     return [
    //         FilterAction::make()
    //             ->form([
    //                 DatePicker::make('startDate'),
    //                 DatePicker::make('endDate'),
    //             ]),
    //     ];
    // }

    // protected function getStats(): array
    // {
    //     // Optimize by using a single raw query to get all counts at once
    //     $tierStats = DB::select("
    //             SELECT 
    //                 (SELECT COUNT(*) FROM pc_details) AS pharmacy_count,
    //                 COUNT(*) AS facility_count,
    //                 SUM(CASE WHEN LOWER(tier) = 'gold' THEN 1 ELSE 0 END) AS gold_tier_count,
    //                 SUM(CASE WHEN LOWER(tier) = 'silver' THEN 1 ELSE 0 END) AS silver_tier_count,
    //                 SUM(CASE WHEN LOWER(tier) = 'bronze' THEN 1 ELSE 0 END) AS bronze_tier_count
    //             FROM clinic_details
    //         ");

    //     $stats = $tierStats[0];
    //     $totalUsers = $stats->facility_count + $stats->pharmacy_count;

    //     $statsData = [
    //         ['label' => 'Total Users',      'count' => number_format($totalUsers),             'icon' => 'heroicon-o-user-group',          'color' => 'info',    'bg' => 'rgba(229, 247, 255, 1)',     'iconColor' => 'light'],
    //         ['label' => 'Pharma Suppliers', 'count' => number_format($stats->pharmacy_count),  'icon' => 'heroicon-o-users',               'color' => 'warning', 'bg' => 'rgba(255, 245, 229, 1)',     'iconColor' => 'light'],
    //         ['label' => 'Facilities',       'count' => number_format($stats->facility_count),  'icon' => 'heroicon-o-building-storefront', 'color' => 'primary', 'bg' => 'rgba(229, 237, 255, 1)',       'iconColor' => 'light'],
    //         ['label' => 'Gold Tier',        'count' => number_format($stats->gold_tier_count), 'icon' => 'heroicon-o-star',                'color' => 'success', 'bg' => 'rgba(255, 244, 224, 1)',     'iconColor' => '#ffffff'],
    //         ['label' => 'Silver Tier',      'count' => number_format($stats->silver_tier_count), 'icon' => 'heroicon-o-star',              'color' => 'info',    'bg' => 'rgba(230, 230, 230, 1)',       'iconColor' => '#ffffff'],
    //         ['label' => 'Bronze Tier',      'count' => number_format($stats->bronze_tier_count), 'icon' => 'heroicon-o-star',              'color' => 'danger',  'bg' => 'rgba(255, 231, 217, 1)',     'iconColor' => '#ffffff'],
    //     ];

    //     return array_map(
    //         fn($stat) =>
    //         Stat::make($stat['label'], $stat['count'])
    //             ->icon($stat['icon'])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor($stat['color'])
    //             ->iconColor($stat['iconColor'])
    //             // ->url(route('filament.admin.resources.orders.index'))
    //             ->extraAttributes([
    //                 'style' => "background-color: {$stat['bg']};",
    //             ]),
    //         $statsData
    //     );

    //     // return $statsData;
    // }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $tierStats = DB::select("
    SELECT 
        (
            SELECT COUNT(*) 
            FROM users u
            WHERE u.deleted_at IS NULL
              AND EXISTS (
                  SELECT 1 
                  FROM model_has_roles mhr
                  JOIN roles r ON mhr.role_id = r.id
                  WHERE mhr.model_id = u.id
                    AND mhr.model_type = ?
                    AND LOWER(r.name) = 'pharmaceutical company'
              )
        ) AS pharmacy_count,

        COUNT(*) AS facility_count,
        SUM(CASE WHEN LOWER(cd.tier) = 'gold' THEN 1 ELSE 0 END) AS gold_tier_count,
        SUM(CASE WHEN LOWER(cd.tier) = 'silver' THEN 1 ELSE 0 END) AS silver_tier_count,
        SUM(CASE WHEN LOWER(cd.tier) = 'bronze' THEN 1 ELSE 0 END) AS bronze_tier_count

    FROM clinic_details cd
    JOIN users u ON u.id = cd.user_id AND u.deleted_at IS NULL
    JOIN model_has_roles mhr ON mhr.model_id = cd.user_id AND mhr.model_type = ?
    JOIN roles r ON r.id = mhr.role_id
    WHERE LOWER(r.name) = 'clinic'
", [User::class, User::class]);
      
        $stats = $tierStats[0];
        $totalUsers = $stats->facility_count + $stats->pharmacy_count;

        $this->stats = [
            [
                'label' => 'Total Users',
                'query' => number_format($totalUsers),
                'icon' => 'user-group',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => null
            ],
            [
                'label' => 'Pharma Suppliers',
                'query' => number_format($stats->pharmacy_count),
                'icon' => 'users',
                'color' => 'var(--Warning-500, #F59E0B)',
                'bg' => '#FFF5E5;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => UserResource::getUrl()
            ],
            [
                'label' => 'Facilities',
                'query' => number_format($stats->facility_count),
                'icon' => 'building-storefront',
                'color' => '#2563EB',
                'bg' => '#E5EDFF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ClinicResource::getUrl()
            ],
            [
                'label' => 'Gold Tier',
                'query' => number_format($stats->gold_tier_count),
                'icon' => 'star',
                'color' => '#C79538;',
                'bg' => '#FFF4E0;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ClinicResource::getUrl() . '?tableFilters[tier][value]=gold'
            ],
            [
                'label' => 'Silver Tier',
                'query' => number_format($stats->silver_tier_count),
                'icon' => 'star',
                'color' => '#868686;',
                'bg' => '#E1E1E1;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ClinicResource::getUrl() . '?tableFilters[tier][value]=silver'
            ],
            [
                'label' => 'Bronze Tier',
                'query' => number_format($stats->bronze_tier_count),
                'icon' => 'star',
                'color' => '#BC6837;',
                'bg' => '#FFE7D9;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url' => ClinicResource::getUrl() . '?tableFilters[tier][value]=bronze'
            ]

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }
}
