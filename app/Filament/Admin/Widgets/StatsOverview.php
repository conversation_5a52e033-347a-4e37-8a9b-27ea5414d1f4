<?php

namespace App\Filament\Admin\Widgets;

use App\Models\User;
use Filament\Widgets\StatsOverviewWidget\Card;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Illuminate\Support\HtmlString;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        return [
            Card::make('Total Users', User::count())
                ->icon('heroicon-o-users')
                ->url('/admin/users')
                ->extraAttributes(
                    [
                        'class' => 'cursor-pointer',
                    ]
                )
                ->color('info'),

            Card::make('Pharma Suppliers', '100')
                ->label(new HtmlString('<span style="font-size: 18px !important;">Pharma Suppliers</span>'))
                ->icon('heroicon-o-user-group')
                ->extraAttributes(
                    [
                        'style' => 'background-color: #f6f6f6; color: #000; font-weight: bold;',
                    ]
                )
                ->color('warning'),

            Card::make('Facilities', '200')
                ->icon('heroicon-o-building-storefront')
                ->color('primary'),

            Card::make('Gold Tier', '300')
                ->icon('heroicon-o-star')
                ->color('warning'),

            Card::make('Silver Tier', '400')
                ->icon('heroicon-o-star')
                ->color('gray'),

            Card::make('Bronze Tier', '500')
                ->icon('heroicon-o-star')
                ->color('orange'),
        ];
    }
}
