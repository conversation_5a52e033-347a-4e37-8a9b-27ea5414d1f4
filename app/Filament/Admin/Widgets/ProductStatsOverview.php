<?php

namespace App\Filament\Admin\Widgets;

use App\Filament\Admin\Resources\ProductResource;
use App\Models\Product;
use App\Models\ProductRelation;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Contracts\View\View;

class ProductStatsOverview extends BaseWidget
{
    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Products';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.admin.widgets.product-stats-overview';

    public function render(): View
    {
        return view('filament.admin.widgets.product-stats-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public $today;

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->today = now()->toDateString();
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $stats = Product::query()
            ->selectRaw('
        COUNT(DISTINCT products.id) as total_products,
        -- Total Approved Products
        COUNT(DISTINCT CASE WHEN products.status NOT IN (\'rejected\', \'pending\') THEN products.id END) as total_products,

        -- Pending Approval Products
        COUNT(DISTINCT CASE WHEN products.status NOT IN (\'approved\', \'rejected\') THEN products.id END) as pending_approval_products,

        -- Rejected Products
        COUNT(DISTINCT CASE WHEN products.status =  \'rejected\' THEN products.id END) as rejected_products, 

        -- Out of Stock
        COUNT(DISTINCT CASE
            WHEN product_relation_stocks.is_batch_wise_stock = true AND (
                SELECT COALESCE(SUM(pb.available_stock), 0)
                FROM products_batch pb
                WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
            ) <= 0
            OR (product_relation_stocks.is_batch_wise_stock = false AND product_relation_stocks.stock <= 0)
        THEN products.id END) as out_of_stock,

        -- Low Stock
        COUNT(DISTINCT CASE
            WHEN product_relation_stocks.is_batch_wise_stock = true AND (
                SELECT COALESCE(SUM(pb.available_stock), 0)
                FROM products_batch pb
                WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
            ) < product_relation_stocks.low_stock 
            AND (
                SELECT COALESCE(SUM(pb.available_stock), 0)
                FROM products_batch pb
                WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
            ) > 0
            OR (
                product_relation_stocks.is_batch_wise_stock = false AND
                product_relation_stocks.stock < product_relation_stocks.low_stock AND
                product_relation_stocks.stock > 0
            )
        THEN products.id END) as low_stock,

        -- Expired Products
        COUNT(DISTINCT CASE
            WHEN product_relation_stocks.expiry_date < CURRENT_DATE
        THEN products.id END) as expired_products
    ')
            // ->where('products.status', 'approved') // Filter approved products
            ->leftJoin('products_relation', 'products.id', '=', 'products_relation.product_id') // Left join to handle no products_relation
            ->leftJoin('product_relation_stocks', 'products_relation.id', '=', 'product_relation_stocks.product_relation_id')
            ->first();

        $this->stats = [
            [
                'label' => 'Total Products',
                'query' => number_format($stats->total_products ?? 0),
                'icon' => 'queue-list',
                'color' => '#004668;',
                'bg' => '#E5F7FF',
                'iconColor' => '#FFFFFF',
                'url' => ProductResource::getUrl('index',parameters:['tab'=>'all-products'])
            ],
            [
                'label' => 'Pending Approval',
                'query' => number_format($stats->pending_approval_products ?? 0),
                'icon' => 'exclamation-triangle',
                'color' => '#F59E0B;',
                'bg' => '#FFF5E5;',
                'iconColor' => '#FFFFFF',
                'url' => ProductResource::getUrl('index',parameters:['tab'=>'pending-approval'])
            ],
            [
                'label' => 'Rejected',
                'query' => number_format($stats->rejected_products ?? 0),
                'icon' => 'numbered-list',
                'color' => '#DC2626;',
                'bg' => ' #FFE5E5;',
                'iconColor' => '#FFFFFF',
                'url' => ProductResource::getUrl('index',parameters:['tab'=>'rejected'])
            ],
            // [
            //     'label' => 'Low Stock Products',
            //     'query' => number_format($stats->low_stock ?? 0),
            //     'icon' => 'exclamation-triangle',
            //     'color' => '#F59E0B;',
            //     'bg' => '#FFF5E5;',
            //     'iconColor' => '#FFFFFF',
            // ],
            // [
            //     'label' => 'Expired Products',
            //     'query' => number_format($stats->expired_products ?? 0),
            //     'icon' => 'clock',
            //     'color' => '#2563EB;',
            //     'bg' => '#E5EDFF;',
            //     'iconColor' => '#FFFFFF',
            // ],
            // [
            //     'label' => 'Out of Stock Products',
            //     'query' => number_format($stats->out_of_stock ?? 0),
            //     'icon' => 'numbered-list',
            //     'color' => '#DC2626;',
            //     'bg' => ' #FFE5E5;',
            //     'iconColor' => '#FFFFFF',
            // ],
        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }

    // protected function getStats(): array
    // {

    //     $stats = Product::query()
    //         ->selectRaw('
    //         COUNT(DISTINCT products.id) as total_products,

    //         -- Out of Stock
    //         COUNT(DISTINCT CASE
    //             WHEN product_relation_stocks.is_batch_wise_stock = true AND (
    //                 SELECT COALESCE(SUM(pb.available_stock), 0)
    //                 FROM products_batch pb
    //                 WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
    //             ) <= 0
    //             OR (product_relation_stocks.is_batch_wise_stock = false AND product_relation_stocks.stock <= 0)
    //         THEN products.id END) as out_of_stock,

    //         -- Low Stock
    //         COUNT(DISTINCT CASE
    //             WHEN product_relation_stocks.is_batch_wise_stock = true AND (
    //                 SELECT COALESCE(SUM(pb.available_stock), 0)
    //                 FROM products_batch pb
    //                 WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
    //             ) < product_relation_stocks.low_stock 
    //             AND (
    //                 SELECT COALESCE(SUM(pb.available_stock), 0)
    //                 FROM products_batch pb
    //                 WHERE pb.products_relation_id = product_relation_stocks.product_relation_id
    //             ) > 0
    //             OR (
    //                 product_relation_stocks.is_batch_wise_stock = false AND
    //                 product_relation_stocks.stock < product_relation_stocks.low_stock AND
    //                 product_relation_stocks.stock > 0
    //             )
    //         THEN products.id END) as low_stock,

    //         -- Expired Products
    //         COUNT(DISTINCT CASE
    //             WHEN product_relation_stocks.expiry_date < CURRENT_DATE
    //         THEN products.id END) as expired_products
    //     ')
    //         ->where('products.status', 'approved') // Filter approved products
    //         ->leftJoin('products_relation', 'products.id', '=', 'products_relation.product_id') // Left join to handle no products_relation
    //         ->leftJoin('product_relation_stocks', 'products_relation.id', '=', 'product_relation_stocks.product_relation_id')
    //         ->first();


    //     return [
    //         Stat::make('Total Products', number_format($stats->total_products ?? 0))
    //             ->icon('heroicon-o-queue-list')
    //             ->extraAttributes([
    //                 'style' => 'background-color: #E5F7FF;',
    //             ])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor('info')
    //             ->iconColor('light'),

    //         Stat::make('Low Stock Products', number_format($stats->low_stock ?? 0))
    //             ->icon('heroicon-o-exclamation-triangle')
    //             ->extraAttributes([
    //                 'style' => 'background-color: #FFF5E5;',
    //             ])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor('warning')
    //             ->iconColor('#ffffff'),

    //         Stat::make('Expired Products', number_format($stats->expired_products ?? 0))
    //             ->icon('heroicon-o-clock')
    //             ->extraAttributes([
    //                 'style' => 'background-color: #E5EDFF;',
    //             ])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor('info')
    //             ->iconColor('#ffffff'),

    //         Stat::make('Out of Stock Products', number_format($stats->out_of_stock ?? 0))
    //             ->icon('heroicon-o-numbered-list')
    //             ->extraAttributes([
    //                 'style' => 'background-color: #FFE5E5;',
    //             ])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor('danger')
    //             ->iconColor('#ffffff'),
    //     ];
    // }
}
