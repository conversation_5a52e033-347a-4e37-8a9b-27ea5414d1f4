<?php

namespace App\Filament\Admin\Widgets;

use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductRelation;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Illuminate\Support\Str;

class TopSellingProducts extends BaseWidget
{
    protected static ?string $model = ProductRelation::class;
    protected static ?int $sort = 3;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
        ->description("Below are the top 10 selling products based on quantity sold.")
            ->query(
                OrderProduct::query()
                    ->select(
                        'products.id',
                        'products.name as product_name',
                        DB::raw('SUM(order_products.quantity) as order_quantity'),
                        DB::raw('COUNT(distinct order_products.order_id) as orders_count'),
                        DB::raw('SUM(so.total_amount) as orders_amount')
                    )
                    ->join('sub_orders as so', 'so.id', '=', 'order_products.sub_order_id')
                    ->join('products', 'products.id', '=', 'order_products.product_id')
                    ->where('order_products.status', 'accepted')
                    ->groupBy('products.id', 'products.name')
                    ->orderByDesc('order_quantity')
                    ->limit(10)
            )
            // ->defaultSort('order_quantity', 'desc')
            ->columns([
                TextColumn::make('order_quantity')
                    ->label('Order Quantity')
                    ->color('success')
                    ->formatStateUsing(fn($state, $record) => number_format($record->order_quantity)),
                TextColumn::make('id')->prefix('#')
                    ->label('Product ID')
                    ->color('primary')
                    // ->sortable()
                    ->url(fn($record): string => route('filament.admin.resources.products.view', ['record' => $record->id])),

                    TextColumn::make('product_name')
                    ->label('Product Name')
                    ->formatStateUsing(fn($state) => Str::upper($state)),

                TextColumn::make('orders_amount')
                    ->label('Order Amount')
                    ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($record->orders_amount, 2)),
                TextColumn::make('orders_count')
                    ->label('Order Count')
                    ->formatStateUsing(fn($state, $record) => number_format($record->orders_count)),

            ])
            ->filters([
                Filter::make('date_filter')
                    ->label('Date Filter')
                    ->form([
                        Select::make('range')
                            ->label('Select Range')
                            ->options([
                                'this_month' => 'This Month',
                                'this_year' => 'This Year',
                                'custom' => 'Custom Range',
                            ])
                            ->required()
                            ->default('this_year'),

                        DatePicker::make('from')
                            ->label('From')
                            ->maxDate(fn($get) => $get('until') ?? now())
                            ->visible(fn($get) => $get('range') === 'custom')
                            ->closeOnDateSelection(),

                        DatePicker::make('until')
                            ->label('Until')
                            ->minDate(fn($get) => $get('from')) // prevent end date before start date
                            ->maxDate(now()) // prevent future dates
                            ->visible(fn($get) => $get('range') === 'custom')
                            ->closeOnDateSelection(),
                    ])
                    ->query(function ($query, array $data) {
                        return $query
                            ->when($data['range'] === 'this_month', function ($query) {
                                $query->whereBetween('order_products.created_at', [
                                    now()->startOfMonth()->toDateString(),
                                    now()->endOfMonth()->toDateString(),
                                ]);
                            })
                            ->when($data['range'] === 'this_year', function ($query) {
                                $query->whereBetween('order_products.created_at', [
                                    now()->startOfYear()->toDateString(),
                                    now()->endOfYear()->toDateString(),
                                ]);
                            })
                            ->when($data['range'] === 'custom', function ($query) use ($data) {
                                $query
                                    ->when($data['from'], fn($q) => $q->whereDate('order_products.created_at', '>=', $data['from']))
                                    ->when($data['until'], fn($q) => $q->whereDate('order_products.created_at', '<=', $data['until']));
                            });
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['range'] === 'this_month') {
                            return 'This Month';
                        }

                        if ($data['range'] === 'this_year') {
                            return 'This Year';
                        }

                        if ($data['range'] === 'custom') {
                            $from = $data['from'] ?? null;
                            $until = $data['until'] ?? null;

                            if ($from && $until) {
                                return "From {$from} to {$until}";
                            }

                            if ($from) {
                                return "From {$from}";
                            }

                            if ($until) {
                                return "Until {$until}";
                            }

                            return 'Custom Range';
                        }

                        return null;
                    }),
            ])
            ->paginated(false)
            ->emptyStateHeading('No Products Available')
            ->emptyStateDescription('Top-selling products will appear here once there is sufficient sales data available.');
    }
}
