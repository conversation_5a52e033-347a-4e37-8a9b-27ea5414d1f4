<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ClinicDetail;
use App\Models\PcDetail;
use App\Models\SubOrder;
use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Carbon;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\DatePicker;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use Filament\Forms\Components\ToggleButtons;

class PsRevuewVsEarningChart extends ApexChartWidget
{
    protected static ?string $chartId = 'RevenueChart';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Revenue vs Earnings (Pharmaceutical Suppliers)';
    protected static ?string $pollingInterval = '10s';
    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('pharma_user_id')
                ->label('Pharmaceutical Supplier')
                ->options(
                    fn() => PcDetail::query()
                    ->whereNotNull('company_type_id')
                    ->with('companyType')
                    ->get()
                    ->mapWithKeys(function ($record) {
                        if ($record->company_type_id == 1) {
                            $name = $record->company_name ?? $record->business_name;
                        } else {
                            $name = $record->company_name ?? '-';
                        }
            
                        return [$record->user_id => $name]; // <-- key = ID, value = name
                    })
                    ->reject(fn($name) => empty($name)) // remove null/empty
                    ->unique()
                    ->sort()
                    ->toArray()
                )
                ->searchable()
                ->preload()
                ->placeholder('By Pharmaceutical Supplier')
                ->reactive(),

            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->reactive()
                ->required(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start')) // prevent end date before start date
                ->maxDate(now()) // prevent future dates
                ->reactive()
                ->closeOnDateSelection()
                ->required(),

            ToggleButtons::make('revuew_earning_chart_type')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'line' => '📈', // Line chart emoji
                    'bar'  => '📊', // Bar chart emoji
                ])
                ->default('line')
                ->reactive()
        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */
    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] ?? 'year';
        $chartType = $filters['revuew_earning_chart_type'] ?? 'line';
        $pcUserId = $filters['pharma_user_id'] ?? null; // pc_user_id is user_id from sub_orders

        // Date range and group by format
        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
            default:
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
        }

        // Use Eloquent models and relationships for the query

        // Assuming you have SubOrder and OrderProduct models with proper relationships
        // SubOrder hasMany OrderProduct as 'orderProducts'
        // SubOrder belongsTo User as 'user'

        // $subQuery = \DB::table('order_products')
        //     ->select('sub_order_id', \DB::raw('SUM(total_commission) as total_commission'))
        //     ->groupBy('sub_order_id');

        // $query = \DB::table('sub_orders')
        //     ->leftJoinSub($subQuery, 'op_comm', function ($join) {
        //         $join->on('op_comm.sub_order_id', '=', 'sub_orders.id');
        //     })
        //     ->whereIn('sub_orders.status', ['accepted', 'delivered'])
        //     ->whereBetween('sub_orders.approved_at', [$start, $end]);

        // if ($pcUserId) {
        //     $query->where('sub_orders.user_id', $pcUserId);
        // }

        // $rawData = $query
        //     ->selectRaw("
        //         TO_CHAR(sub_orders.approved_at, '{$groupByFormat}') as period,
        //         COALESCE(SUM(sub_orders.total_amount), 0) as revenue,
        //         COALESCE(SUM(op_comm.total_commission), 0) as earning
        //     ")
        //     ->groupBy('period')
        //     ->orderBy('period')
        //     ->get()
        //     ->keyBy('period');

        // dd($rawData);

        $subOrdersQuery = SubOrder::query()
            ->withSum('orderProducts as total_commission', 'total_commission')
            ->whereNotIn('status', ['pending','rejected','cancelled'])
            ->whereBetween('approved_at', [$start, $end]);

        if ($pcUserId) {
            $subOrdersQuery->where('user_id', $pcUserId);
        }

        // Get all sub orders in the range, eager loaded with commission sum
        $subOrders = $subOrdersQuery->get();

        // Group and aggregate by period using collection methods
        $rawData = $subOrders->groupBy(function ($subOrder) use ($groupByFormat) {
                // Use Carbon for formatting
                return Carbon::parse($subOrder->approved_at)->format(
                    match ($groupByFormat) {
                        'YYYY-MM-DD' => 'Y-m-d',
                        'YYYY-MM'    => 'Y-m',
                        'YYYY'       => 'Y',
                        'MM'         => 'm',
                        'HH24:MI'    => 'H:i',
                        default      => 'Y-m-d',
                    }
                );
            })
            ->map(function ($group) {
                return (object) [
                    'revenue' => $group->sum('total_amount'),
                    'earning' => $group->sum(function ($item) {
                        // total_commission is from withSum, may be null
                        return $item->total_commission ?? 0;
                    }),
                ];
            });

        // Re-key by period for compatibility
        $rawData = $rawData->mapWithKeys(function ($item, $period) {
            return [$period => (object) array_merge(['period' => $period], (array) $item)];
        });
        
        // Build categories and keys for the X axis
        $categories = [];
        $keys = [];

        if ($range === 'year') {
            $currentYear = now()->year;
            $months = collect(range(1, 12))->mapWithKeys(function ($month) use ($currentYear) {
                $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT);
                $monthLabel = \Carbon\Carbon::create()->month($month)->format('M') . ' ' . $currentYear;
                return [$monthKey => $monthLabel];
            });
            $categories = $months->values()->toArray();
            $keys = $months->keys();
        } elseif ($range === 'custom') {
            $daysDiff = $start->diffInDays($end);
            $monthsDiff = $start->diffInMonths($end);

            if ($daysDiff <= 31) {
                $periods = collect();
                $current = $start->copy();
                while ($current->lte($end)) {
                    $periods->push($current->format('Y-m-d'));
                    $current->addDay();
                }
                $categories = $periods->map(fn($d) => \Carbon\Carbon::parse($d)->format('M d'))->toArray();
                $keys = $periods;
            } elseif ($monthsDiff <= 24) {
                $periods = collect();
                $categories = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfMonth(), '1 month', $end->endOfMonth());
                foreach ($period as $month) {
                    $monthStr = $month->format('Y-m');
                    $periods->push($monthStr);
                    $categories[] = $month->format('M Y');
                }
                $keys = $periods;
            } else {
                $periods = collect();
                $current = $start->copy()->startOfYear();
                while ($current->lte($end)) {
                    $periods->push($current->format('Y'));
                    $current->addYear();
                }
                $categories = $periods->toArray();
                $keys = $periods;
            }
        } else {
            // Default to daily grouping for month/week, hourly for today
            if ($range === 'month' || $range === 'week') {
                $periods = collect();
                $current = $start->copy();
                while ($current->lte($end)) {
                    $periods->push($current->format('Y-m-d'));
                    $current->addDay();
                }
                $categories = $periods->map(fn($d) => \Carbon\Carbon::parse($d)->format('M d'))->toArray();
                $keys = $periods;
            } elseif ($range === 'today') {
                $periods = collect();
                $current = $start->copy();
                while ($current->lte($end)) {
                    $periods->push($current->format('H:i'));
                    $current->addHour();
                }
                $categories = $periods->toArray();
                $keys = $periods;
            }
        }

        // Build series for Revenue and Earning
        $revenueData = $keys->map(fn($key) => round((float) ($rawData[$key]->revenue ?? 0), 2))->toArray();
        $earningData = $keys->map(fn($key) => round((float) ($rawData[$key]->earning ?? 0), 2))->toArray();
        
        $pcDetail = PcDetail::whereUserId($pcUserId)->first();

        $pcName = '';

        if ($pcDetail) {
            if ($pcDetail->company_type_id == 1) {
                // If sole → prefer business_name, fallback to company_name
                $pcName = ($pcDetail->company_name ?? $pcDetail->business_name) . ' : ';
            } else {
                // Otherwise → company_name only
                $pcName = ($pcDetail->company_name ?? '') . ' : ';
            }
        }
        return [
            'chart' => [
                'type' => $chartType,
                'height' => 400,
                // 'stacked' => $chartType === 'bar',
                'zoom' => [
                    'enabled' => false,
                ],
                'toolbar' => [
                    'show' => false,
                ],
                'width' => '100%',
            ],
            'series' => [
                [
                    'name' => $pcName .'Revenue',
                    'data' => $revenueData,
                ],
                [
                    'name' => $pcName . 'Earning',
                    'data' => $earningData,
                ],
            ],
           'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                // 'title' => [
                //     'text' => $clinicName,
                //     'style' => [
                //         'fontWeight' => 600,
                //         'fontSize' => '18px',
                //     ],
                // ],
            ],
             'legend' => [
                 'fontSize'=> '15px'
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                    // 'formatter' => 'function (val) { return "RM " + val.toFixed(2); }', // Add RM before the value
                ],
            ],
            // 'colors' => ['#0BA7F5'],
            'stroke' => [
                'curve' => 'straight', // no curve
                'width' => 2,
            ],
            'markers' => [
                'size' => 10, // size of the dot
                // 'colors' => ['#0BA7F5'],
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 10,
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                // 'enabledOnSeries' => [0], // ensure it's applied to the correct series
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                'offsetY' => -10, // moves label above point
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return 'RM ' + val.toLocaleString();
                        }
                    }
                },
            }
        JS);
    }
}