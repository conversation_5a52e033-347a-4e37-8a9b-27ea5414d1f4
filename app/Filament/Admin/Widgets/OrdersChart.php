<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Order;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Js;
use Filament\Forms\Components\ToggleButtons;


class OrdersChart extends ApexChartWidget
{
    protected static ?string $chartId = 'OrdersChart';
    protected int | string | array $columnSpan = 'full';
    protected static ?string $heading = 'Orders';

    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->reactive()
                ->required(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()
                ->required()
                ->closeOnDateSelection(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start')) // prevent end date before start date
                ->maxDate(now()) // prevent future dates
                ->reactive()
                ->required()
                ->closeOnDateSelection(),

            Select::make('user_id')
                ->label('Facilities')
                ->options(
                    fn() => User::query()
                        ->whereHas('clinicData')
                        ->with('clinicData:id,user_id,clinic_name')
                        ->get()
                        ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                        ->mapWithKeys(fn($user) => [
                            $user->id => $user->clinicData->clinic_name,
                        ])
                        ->sortBy(fn($name) => $name)
                        ->toArray()
                )
                ->searchable()
                ->preload()
                ->placeholder('Facilities')
                ->multiple()
                ->maxItems(10)
                ->reactive(),

            ToggleButtons::make('chart_type')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'bar'  => '📊', // Bar chart emoji
                    'line' => '📈', // Line chart emoji
                ])
                ->default('bar')
                ->reactive()
        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

    protected function getOptions_old(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $userId = $filters['user_id'] ?? null;
        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $groupByFormat = 'YYYY-MM-DD';
                break;
        }

        $data = Order::query()
            ->when($userId, fn($query) => $query->where('user_id', $userId))
            ->whereBetween('created_at', [$start, $end])
            ->where('status', 'delivered')
            ->selectRaw("TO_CHAR(created_at, '{$groupByFormat}') as period, count(id) as orders")
            ->groupBy('period')
            ->orderBy('period')
            ->pluck('orders', 'period');

        if ($range === 'year') {
            $months = [
                '01' => $userId . 'Jan',
                '02' => 'Feb',
                '03' => 'Mar',
                '04' => 'Apr',
                '05' => 'May',
                '06' => 'Jun',
                '07' => 'Jul',
                '08' => 'Aug',
                '09' => 'Sep',
                '10' => 'Oct',
                '11' => 'Nov',
                '12' => 'Dec',
            ];

            $categories = [];
            $seriesData = [];

            foreach ($months as $num => $name) {
                $categories[] = $name;
                $seriesData[] = (int) ($data[$num] ?? 0);
            }
        } else {
            $categories = $data->keys()->toArray();
            $seriesData = $data->values()->map(fn($value) => (int) $value)->toArray();
        }

        return [
            'chart' => [
                'type' => 'line',
                'height' => 300,
                'zoom' => [
                    'enabled' => false, // Disable zoom
                ],
                'toolbar' => [
                    'show' => false, // Disable toolbar which includes download option
                ],
                'width' => '100%',
            ],
            'series' => [
                [
                    'name' => 'Orders',
                    'data' => $seriesData,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],
            'colors' => ['#0BA7F5'],
            'stroke' => [
                'curve' => 'straight', // no curve
                'width' => 2,
            ],
            'markers' => [
                'size' => 4, // size of the dot
                'colors' => ['#0BA7F5'],
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 8,
                ],
            ],
        ];
    }

    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $chartType = $filters['chart_type'] ?? 'line';
        $facilityIds = $filters['user_id'] ?? null;

        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            // case 'custom':
            //     $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
            //     $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
            //     $groupByFormat = 'YYYY-MM-DD';
            //     break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
        }
        $isMultiFacility = !empty($facilityIds);

        $query = Order::query()
            ->whereBetween('orders.created_at', [$start, $end]);

        if ($isMultiFacility) {
            $query->leftJoin('clinic_details', 'orders.user_id', '=', 'clinic_details.user_id')
                ->whereIn('orders.user_id', $facilityIds)
                ->selectRaw("
                orders.user_id,
                clinic_details.clinic_name as facility_name,
                TO_CHAR(orders.created_at, '{$groupByFormat}') as period,
                orders.status,
                COUNT(orders.id) as total
            ")
                ->groupBy('orders.user_id', 'clinic_details.clinic_name', 'period', 'orders.status');
        } else {
            $query->selectRaw("
            TO_CHAR(orders.created_at, '{$groupByFormat}') as period,
            orders.status,
            count(id) as total
        ")
                ->groupBy('period', 'orders.status');
        }

        $rawData = $query->orderBy('period')->get();

        $categories = [];
        $keys = [];

        if ($range === 'year') {
            // Create keys like '01', '02' and labels like 'Jan', 'Feb'
            $months = collect(range(1, 12))->mapWithKeys(function ($month) {
                $currentYear = now()->year;
                $monthKey = str_pad($month, 2, '0', STR_PAD_LEFT); // '01'
                $monthLabel = Carbon::create()->month($month)->format('M') . ' ' . $currentYear; // 'Jan'
                return [$monthKey => $monthLabel];
            });

            $categories = $months->values()->toArray();  // For chart X-axis
            $keys = $months->keys();                     // For series data lookup
        } elseif ($range === 'custom') {
            $startDate = Carbon::parse($start);
            $endDate = Carbon::parse($end);
            $daysDiff = $startDate->diffInDays($endDate);
            $monthsDiff = $startDate->diffInMonths($endDate);

            if ($daysDiff <= 31) {
                // Daily format - Fixed the issue here
                $dates = collect();
                $currentDate = $startDate->copy();

                while ($currentDate->lte($endDate)) {
                    $formatted = $currentDate->format('Y-m-d');
                    $label = $currentDate->format('M j'); // e.g., "May 2"
                    $dates->put($formatted, $label);
                    $currentDate->addDay();
                }

                $categories = $dates->values()->toArray();
                $keys = $dates->keys();
            } elseif ($monthsDiff <= 24) {
                // Monthly format
                $months = collect();
                $currentDate = $startDate->copy()->startOfMonth();

                while ($currentDate->lte($endDate)) {
                    $formatted = $currentDate->format('Y-m');
                    $label = $currentDate->format('M Y'); // e.g., "May 2024"
                    $months->put($formatted, $label);
                    $currentDate->addMonth();
                }

                $categories = $months->values()->toArray();
                $keys = $months->keys();
            } else {
                // Yearly format
                $years = collect();
                $currentDate = $startDate->copy()->startOfYear();

                while ($currentDate->lte($endDate)) {
                    $formatted = $currentDate->format('Y');
                    $years->put($formatted, $formatted);
                    $currentDate->addYear();
                }

                $categories = $years->values()->toArray();
                $keys = $years->keys();
            }
        } else {
            // For month, week, today - daily format
            $startDate = Carbon::parse($start);
            $endDate = Carbon::parse($end);

            $dates = collect();
            $currentDate = $startDate->copy();

            while ($currentDate->lte($endDate)) {
                $formatted = $currentDate->format('Y-m-d');
                $label = $currentDate->format('M j'); // e.g., "May 2"
                $dates->put($formatted, $label);
                $currentDate->addDay();
            }

            $categories = $dates->values()->toArray();
            $keys = $dates->keys();
        }

        // Build series data
        $series = [];

        if ($isMultiFacility) {
            $grouped = [];
            foreach ($rawData as $row) {
                $status = strtolower($row->status) == 'delivered' ? 'Completed' : $row->status;
                $facilityName = $row->facility_name ?? "Facility {$row->user_id}";
                $key = $status . '-' . $row->user_id;

                $grouped[$key]['name'] = $facilityName . ' : ' . ucwords(str_replace('_', ' ', $status));

                $grouped[$key]['group'] = $facilityName;
                $grouped[$key]['data'][$row->period] = $row->total;
            }
            $series = collect($grouped)->map(fn($item) => [
                'name' => $item['name'],
                'group' => $item['group'],
                'data' => $keys->map(fn($key) => $item['data'][$key] ?? ($chartType === 'bar' ? null : 0))->toArray(),
            ])->values()->toArray();
        } else {
            $grouped = $rawData->groupBy('status');
            foreach ($grouped as $status => $items) {
                $status = strtolower($status) == 'delivered' ? 'Completed' : $status;
                $data = $keys->map(function ($key) use ($items, $chartType) {
                    $item = $items->firstWhere('period', $key);
                    return $item ? $item->total : ($chartType === 'bar' ? null : 0);
                });

                $series[] = [
                    'name' =>  ucwords(str_replace('_', ' ', $status)),
                    'group' => 'Total Orders',
                    'data' => $data->toArray(),
                ];
            }
        }

        return [
            'chart' => [
                'type' => $chartType, // 'bar' or 'line'
                'height' => 600,
                'stacked' => $chartType === 'bar', // stacked only for bar chart
                'zoom' => [
                    'enabled' => false,
                ],
                'toolbar' => [
                    'show' => false,
                ],
                 'width' => '100%',
            ],
            // Bar-specific options
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,
                    'dataLabels' => [
                        'position' => 'top', // or 'middle'
                        'hideOverflowingLabels' => false,
                    ],
                ],
            ],

            'dataLabels' => [
                'enabled' => true,
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                // Offset differently for bar and line
                'offsetY' => $chartType === 'bar' ? -8 : -6,
            ],

            'series' => $series,

             'legend' => [
                 'fontSize'=> '15px'
            ],
            
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'rotate' => -45,
                    'hideOverlappingLabels' => false,
                    'trim' => false,
                    'maxHeight' => 120,
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                'title' => [
                    'text' => count($series) === 1 ? collect($series)->pluck('name')->first() : '',
                    'style' => [
                        'fontWeight' => 600,
                        'fontSize' => '18px',
                    ],
                ],
            ],

            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
            ],

            // Optional: Line stroke
            'stroke' => [
                'curve' => $chartType === 'line' ? 'straight' : 'smooth',
                'width' => 2,
            ],

            // Optional: markers for line
            'markers' => [
                'size' => $chartType === 'line' ? 6 : 0,
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 8,
                ],
            ],
        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return Math.round(val).toLocaleString();
                        }
                    }
                },
            }
        JS);
    }
}
