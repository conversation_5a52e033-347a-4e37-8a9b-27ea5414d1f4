<?php

namespace App\Filament\Admin\Widgets;

use App\Models\DpharmaPoint;
use App\Models\Payout;
use App\Models\PayoutSubOrder;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Order;
use App\Models\OtpVerification;
use App\Models\PcDetail;
use App\Models\ProfitSharingHistory;
use App\Models\User;
use Filament\Tables\Filters\SelectFilter;
use Carbon\Carbon;
use Filament\Tables\Columns\Summarizers\Sum;
use Filament\Notifications\Notification;
use Hasan<PERSON>hani\FilamentOtpInput\Components\OtpInput;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Mail;
use App\Mail\SendOtpMail;
use Filament\Tables\Columns\Summarizers\Summarizer;

class FacelityProfitTable extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';
    public $amount = null;
    public $email = '';
    public $otp = '';
    public $fa_code = '';
    public $tableHidden = false;
    public $open = false;
    public $submitButton = false;
    public $otpTime;
    protected static string $view = 'filament.admin.widgets.facelity-profit-table';
    protected static ?string $heading  = '';
    protected $listeners = ['confirmClinicSubmission' => 'submitClinic'];
    public $currentYear = '';
    public $startYear = '';
    public $year = '';
    // public function table(Table $table): Table
    // {
    //     $record = 1;

    //     return $table
    //         ->query(Payout::where('id', $record)->with(['user','payoutSubOrders.order'])
    //         )
    //         ->columns([
    //             TextColumn::make('payoutSubOrders.order.order_number')->label('Order ID'),
    //             TextColumn::make('payoutSubOrders.order.created_at')->label('Order Date'),
    //             TextColumn::make('payoutSubOrders.order.amount')->label('Order Total')



    //             // ->hidden(
    //             //     fn(TextColumn $column): bool => dd($column->getRecord())
    //             //     // $column->getRecord() &&
    //             //     //     $column->getRecord()->productData &&
    //             //     //     $column->getRecord()->productData->is_batch_wise_stock
    //             // )
    //         ]);
    // }

    public function mount()
    {
        $this->currentYear = date('Y');
        $this->startYear = date('Y') - 10;
        $this->email = auth()->user()->email;
        $res = ProfitSharingHistory::where(['year' => date('Y')])->first();
        // if (!empty($res)) {
        //     $this->amount = $res->amount;
        // }
    }
    public function table(Table $table): Table
    {
        return $table
            ->paginated(false) // must be false to work with collection
            ->query(function () {
                return  User::role('Clinic')
                    // ->whereHas('orders')
                    ->whereHas('orders', fn($q) => $q->where('status', 'delivered')->whereYear('created_at', $this->year))
                    ->with('clinicDetails', 'orders');

                // $totalRow = new User();
                // $totalRow->id = 'total-row'; // unique ID
                // $totalRow->clinicDetails = (object) ['clinic_name' => 'Total'];
                // $totalRow->orders = $users->first()->flatMap?->orders;

                // return $users->first()->push($totalRow);
                // dd($totalRow);
            })
            ->columns([
                TextColumn::make('clinicDetails.clinic_name')->label('Facility Name')->summarize(
                    Summarizer::make()
                        ->label('Total')
                        ->using(fn () => '')
                        ->html()
                ),
                TextColumn::make('orders')->label('Total Purchases')
                    ->formatStateUsing(function ($record) {
                        $amount = $record->orders->where('status', 'delivered')->sum('amount');

                        return $record->id === 'total-row'
                            ? '**RM ' . number_format($amount, 2) . '**'
                            : 'RM ' . number_format($amount, 2);
                    })
                    ->summarize(
                        Summarizer::make()
                            ->label('')
                            ->using(fn () => $this->getPurchaseTotals()['total']) // Your static value here
                            ->html()
                    ),
                TextColumn::make('orders.amount')->label('Contribution')
                    ->formatStateUsing(function ($record) {
                        if ($record->id === 'total-row') {
                            return '**100%**';
                        }

                        $users = User::role('Clinic')
                            ->whereHas('orders', fn($q) => $q->where('status', 'delivered'))
                            ->with('orders')
                            ->get();

                        $totalPurchase = $users->flatMap->orders->where('status', 'delivered')->sum('amount');
                        $amount = $record->orders->where('status', 'delivered')->sum('amount');

                        $percent = $totalPurchase > 0
                            ? ($amount / $totalPurchase) * 100
                            : 0;

                        return number_format($percent, 2) . '%';
                    })
                    ->summarize(
                        Summarizer::make()
                            ->label('')
                            ->using(fn () => $this->getPurchaseTotals()['total_contribution']) // Your static value here
                            ->html()
                    ),
                TextColumn::make('orders.status')->label('Profit Share')
                    ->formatStateUsing(function ($record) {
                        $users = User::role('Clinic')
                            ->whereHas('orders', fn($q) => $q->where('status', 'delivered'))
                            ->with('orders')
                            ->get();

                        $totalPurchase = $users->flatMap->orders->where('status', 'delivered')->sum('amount');

                        $amount = $record->orders->where('status', 'delivered')->sum('amount');

                        $percent = $totalPurchase > 0
                            ? ($amount / $totalPurchase) * 100
                            : 0;

                        $newAmount = $this->amount * $percent / 100;

                        return 'RM ' . number_format($newAmount, 2);
                    })
                    ->summarize(
                        Summarizer::make()
                            ->label('')
                            ->using(fn () => $this->getPurchaseTotals()['total_profit_amount']) // Your static value here
                            ->html()
                    ),
                TextColumn::make('orders.points')->label('DPharma Points (RM1 = 1 Point)')
                    ->formatStateUsing(function ($record) {
                        $users = User::role('Clinic')
                            ->whereHas('orders', fn($q) => $q->where('status', 'delivered'))
                            ->with('orders')
                            ->get();

                        $totalPurchase = $users->flatMap->orders->where('status', 'delivered')->sum('amount');

                        $amount = $record->orders->where('status', 'delivered')->sum('amount');

                        $percent = $totalPurchase > 0
                            ? ($amount / $totalPurchase) * 100
                            : 0;

                        $newAmount = $this->amount * $percent / 100;

                        return number_format($newAmount, 2) . ' Points';
                    })
                    ->summarize(
                        Summarizer::make()
                            ->label('')
                            ->using(fn () => $this->getPurchaseTotals()['total_profit_points']) // Your static value here
                            ->html()
                    ),
            ])->filters([]);
    }

    public function checkCalculation()
    {
        if (trim($this->amount) === '' || !is_numeric($this->amount) || $this->amount == 0) {
            Notification::make()
                ->body('Amount is required and must be a valid number.')
                ->danger()
                ->send();
            return;
        }
        if (trim($this->year) === '') {
            Notification::make()
                ->body('Please Select a Year.')
                ->danger()
                ->send();
            return;
        }
        $res = ProfitSharingHistory::where(['year' => $this->year])->first();
        if (!empty($res)) {
            Notification::make()
                ->body('Already submitted for this year.')
                ->danger()
                ->send();
            return;
        }
        $this->getPurchaseTotals();
        //$this->submitButton = true;
        $this->tableHidden = true;
        $this->amount = $this->amount;
    }

    public function verifyOtp()
    {
        if (trim($this->otp) === '') {
            Notification::make()
                ->body('Amount is required.')
                ->danger()
                ->send();
            return;
        }
        // dd($this->otp);
    }

    public function resend()
    {
        $otp = generateOTP();
        $this->email = auth()->user()->email;
        $time = config('constants.api.otp_time');
        //before timing should not be update otp
        $res = OtpVerification::updateOrCreate(['email' => $this->email], [
            'email' => $this->email,
            'otp' => $otp,
            'expires_at' => Carbon::now()->addMinutes($time),
        ]);
        Mail::to('<EMAIL>')->send(new SendOtpMail($otp, $time, 'Resend Otp'));


        Notification::make()
            ->title('OTP Sent')
            ->body('A new OTP has been sent to your email..')
            ->success()
            ->send();
        return [
            // 'otp' => $res['otp'],
            'expires_at' => $time * 60,
        ];
    }

    public function submitForVerification()
    {
        if (trim($this->amount) === '' || $this->amount == 0) {
            Notification::make()
                ->body('Amount is required.')
                ->danger()
                ->send();
            return;
        }
        $this->open = true;
        $this->email = auth()->user()->email;
        $this->otpTime = config('constants.api.otp_time');
        if (!auth()->user()->otp_verified) {
            $this->otpCreate(auth()->user()->email, $this->otpTime, 'Digital Pharma Profit Share OTP');
        }
    }

    public function otpCreate($email, $time, $subject)
    {
        $otp = generateOTP();
        $this->email = $email;
        //before timing should not be update otp
        $res = OtpVerification::updateOrCreate(['email' => $email], [
            'email' => $email,
            'otp' => $otp,
            'expires_at' => Carbon::now()->addMinutes($time),
        ]);
        Mail::to($email)->send(new SendOtpMail($otp, $time, $subject));

        return [
            // 'otp' => $res['otp'],
            'expires_at' => $time * 60,
        ];
    }

    public function getPurchaseTotals(): array
    {
        $users = User::role('Clinic')
            ->whereHas('orders', fn($q) => $q->where('status', 'delivered')->whereYear('created_at', $this->year))
            ->with('orders')
            ->get();

        $total = $users->flatMap->orders->where('status', 'delivered')->sum('amount');
        $delivered = $users->flatMap->orders->where('status', 'delivered')->sum('amount');
        $this->submitButton = ($total === 0) ? false : true;
        return [
            'total' => ($total == 0) ? '' : 'RM ' . number_format($total, 2),
            'delivered' => $delivered,
            'total_contribution' => ($total == 0) ? '' : '100%',
            'total_profit_amount' => ($total == 0) ? '' : 'RM ' . number_format($this->amount, 2),
            'total_profit_points' => ($total == 0) ? '' : 'RM ' . number_format($this->amount, 2)
        ];
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            OtpInput::make('fa_code')
                ->label('')
                ->numberInput(4)
                ->default(null)
                ->rules(['required','min:4', 'max:4'])

                ->validationMessages([
                    'required' => 'OTP is required/OTP must be of 4 characters',
                    'min' => 'OTP must be 4 characters',
                    'max' => 'OTP must be 4 characters',
                ])
                ->extraAttributes([
                    'class' => 'fa_code',
                    'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 4);",
                    'onkeydown' => "if (['e', 'E', '+', '-'].includes(event.key)) event.preventDefault();",
                ])

        ]);
    }

    public function cancelVerification() {
        $this->form->fill([
            'fa_code' => null, // or '' if you want empty string
        ]);
    }

    public function submit()
    {
        $data = $this->form->getState();

        $otpVerification = OtpVerification::where('email', auth()->user()->email)
            ->where('otp', $data['fa_code'])
            ->first();

        if (! $otpVerification) {
            Notification::make()
                ->body(__('api.auth.otp_invalid'))
                ->danger()
                ->send();
            return;
        }

        $this->open = false;

        $res = ProfitSharingHistory::where(['year' => $this->year])->first();
        if (!empty($res)) {
            Notification::make()
                ->body('Already submitted for this year.')
                ->danger()
                ->send();
            return;
        } else {
            $this->amount = $this->amount;
            $history = new ProfitSharingHistory();
            $history->user_id = auth()->user()->id;
            $history->amount = $this->amount;
            $history->year = $this->year;
            $history->save();

            //share profit points
            $users = User::role('Clinic')
                ->whereHas('orders')
                ->with('clinicDetails', 'orders')->get();
            if (count($users) > 0) {
                foreach ($users as $user) {
                    $totalPurchase = Order::where('status', 'delivered')->sum('amount');
                    $amount = $user->orders->where('status', 'delivered')->sum('amount');

                    $percent = $totalPurchase > 0
                        ? ($amount / $totalPurchase) * 100
                        : 0;
                    $profitSharePercent = $this->amount * $percent / 100;
                    $dpharmaPoints = DpharmaPoint::where(['user_id' => $user->id])->orderBy('created_at', 'desc')->first();
                    if (!empty($dpharmaPoints)) {
                        $new = new DpharmaPoint();
                        $new->user_id = $user->id;
                        $new->points = (int)$profitSharePercent;
                        $new->balance = $dpharmaPoints->balance + (int)$profitSharePercent;
                        $new->description = 'DPharma Profit Share Points for' . $this->year;
                        $new->save();
                    } else {
                        $new = new DpharmaPoint();
                        $new->user_id = $user->id;
                        $new->points = (int)$profitSharePercent;
                        $new->balance = (int)$profitSharePercent;
                        $new->description = 'DPharma Profit Share Points for' . $this->year;
                        $new->save();
                    }
                }
            }
            Notification::make()
                ->body('Profit points shared successfully.')
                ->success()
                ->send();

            //reset list and filters
            $this->tableHidden = false;
            $this->amount = null;
            $this->year = null;

        }
    }
}
