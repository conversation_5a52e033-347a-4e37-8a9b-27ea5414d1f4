<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\DB;

class TopOrder extends BaseWidget
{
    protected static ?string $heading  = 'Top Product by Orders';
    public function getTableRecordKey(mixed $record): string
    {
        return (string) $record->product_id; // Ensure you're using the correct key
    }
    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->query(
                fn() => OrderProduct::query()
                    ->join('sub_orders as so', 'so.id', '=', 'order_products.sub_order_id')
                    ->join('products', 'products.id', '=', 'order_products.product_id')
                    ->select(
                        'order_products.product_id',
                        'products.name as product_name',
                        DB::raw("STRING_AGG(order_products.sub_order_id::text, ',') as sub_order_ids"),
                        DB::raw('COUNT(*) as total_items'),
                        DB::raw('SUM(order_products.quantity) as total_quantity'),
                        DB::raw('SUM(so.total_amount) as total_amount')
                    )
                    ->where('order_products.status','accepted')
                    ->groupBy('order_products.product_id', 'products.name')
                    ->orderByDesc('total_quantity')->limit(10)
            )
            ->columns([
                TextColumn::make('product_name')
                    ->label('Product Name')
                    ->color('primary')
                    ->url(fn($record): string => route('filament.admin.resources.products.view', ['record' => $record->product_id])),

                TextColumn::make('total_amount')
                    ->label('Total Amount')
                    ->formatStateUsing(fn($state,$record) => 'RM ' . number_format($state, 2)),
                    // ->formatStateUsing(fn($state,$record) => 'RM ' . number_format($state, 2) . ' ('.$record->total_quantity.')'),
            ])
            ->filters([
                // Single Date Filter
                Tables\Filters\Filter::make('date')
                    ->label('Date')
                    ->form([
                        // Adding a single date picker
                        DatePicker::make('date')  // Correct usage of DatePicker
                            ->label('Select Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                    ])
                    ->query(function (Builder $query, array $data) {
                        // Adding filter logic for single date
                        if (isset($data['date'])) {
                            $query->whereDate('order_products.created_at', Carbon::parse($data['date'])->format('Y-m-d'));
                        }
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return Carbon::parse($data['date'])->toFormattedDateString();
                    }), 
            ])
            ->paginated(false)
            ->emptyStateHeading('No data available')
            ->emptyStateDescription('No top products by quantity available.');
    }
}