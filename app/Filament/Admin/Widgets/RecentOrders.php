<?php

namespace App\Filament\Admin\Widgets;

use Filament\Widgets\TableWidget as BaseWidget;
use App\Models\Order;
use App\Models\PcDetail;
use Filament\Tables\Table;
use Illuminate\Support\Facades\View;
use App\Models\User;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Filters\SelectFilter;

class RecentOrders extends BaseWidget
{
    protected static ?string $label = 'Verify Account Number';
    protected static ?string $model = Order::class;
    protected static ?int $sort = 2;
    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Order::with('subOrder.user', 'orderProducts', 'user')->withCount('orderProducts')->latest()->limit(10)
            )
            ->recordUrl(fn($record): string => route('filament.admin.resources.orders.view', ['record' => $record->id]))
            ->columns([
                TextColumn::make('order_number')
                    ->label('Order ID')
                    ->prefix('#')
                    // ->sortable()
                    ->searchable()
                    ->color('primary')
                    ->url(fn($record): string => route('filament.admin.resources.orders.view', ['record' => $record->id])),

                TextColumn::make('user.name')
                    ->label('Facility Name')
                    // ->sortable()
                    ->searchable()
                    ->color('primary')
                     ->url(function ($record) {
                    // Check if user and clinic relationship exists
                    if ($record->user) {
                        return route('filament.admin.resources.clinics.view', [
                            'record' => $record->user->id
                        ]);
                    }
                    return null;
                }),

                TextColumn::make('pharmaceutical_company')
                    ->label('Pharma. Supplier')
                    ->tooltip(function (Order $record) {
                        $names = $record->subOrder
                            ->map(function ($sub) {
                                if ($sub->pcDetail?->company_type_id == 1) {
                                return $sub->pcDetail?->company_name 
                                    ?? $sub->pcDetail?->business_name;
                            }
                            return $sub->pcDetail?->company_name;
                            })
                            ->filter() // remove nulls
                            ->unique()
                            ->values();

                        return implode(', ', $names->toArray());
                    })
                    ->getStateUsing(function (Order $record) {
                        $names = $record->subOrder
                        ->map(function ($sub) {
                            if ($sub->pcDetail?->company_type_id == 1) {
                                return $sub->pcDetail?->company_name 
                                    ?? $sub->pcDetail?->business_name;
                            }
                            return $sub->pcDetail?->company_name;
                        })
                        ->filter()
                        ->unique()
                        ->values();
            
                    $total = $names->count();
            
                    if ($total > 1) {
                        return ucwords($names->take(1)->implode(', ')) . "... +" . ($total - 1);
                    }
            
                    return ucwords($names->implode(', '));
                    })
                    ->html(),

                TextColumn::make('amount')
                    ->label('Order Total')
                    ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($record->amount, 2)),

                TextColumn::make('created_at')
                    ->label('Order Date')
                    ->dateTime('M d, Y'),
                TextColumn::make('order_products_count')
                    ->label('Items')
                    ->formatStateUsing(fn($state, $record) => number_format($record->order_products_count)),
                TextColumn::make('status')
                    ->searchable()
                    ->label('Order Status')
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', ($record->status=='delivered'?'Completed':$record->status))) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            // 'rejected' => 'bi-x-circle-fill',
                            'in_transit' => 'bi-truck',
                            'cancelled' => 'heroicon-o-x-mark',
                            'delivered' => 'heroicon-o-truck',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color = config("constants.order_status.color.{$status}", '#424242');

                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),


            ])
            ->paginated(false)
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Facilities')
                    ->options(
                        fn() => User::query()
                            ->whereHas('clinicData')
                            ->with('clinicData:id,user_id,clinic_name')
                            ->get()
                            ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                            ->mapWithKeys(fn($user) => [
                                $user->id => $user->clinicData->clinic_name,
                            ])
                            ->sortBy(fn($name) => $name)
                            ->toArray()
                    )
                    ->searchable()
                    ->preload()
                    ->placeholder('Facilities')
                    ->preload()
                    ->query(function (Builder $query, $state): Builder {
                        return $query->when(
                            $state['value'] ?? null,
                            fn($query, $userId) => $query->where('user_id', $userId)
                        );
                    }),


                SelectFilter::make('pharma_user_id')
                    ->label('Pharmaceutical Supplier')
                    ->options(
                        fn() => PcDetail::query()
                        ->whereNotNull('company_type_id')
                        ->with('companyType')
                        ->get()
                        ->mapWithKeys(function ($record) {
                            if ($record->company_type_id == 1) {
                                $name = $record->company_name ?? $record->business_name;
                            } else {
                                $name = $record->company_name ?? '-';
                            }
                
                            return [$record->user_id => $name]; // <-- key = ID, value = name
                        })
                        ->reject(fn($name) => empty($name)) // remove null/empty
                        ->unique()
                        ->sort()
                        ->toArray()
                    )
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, $state): Builder {
                        return $query->when(
                            $state['value'] ?? null,
                            fn($query, $userId) => $query->whereHas('subOrder.user', fn($q) => $q->where('id', $userId))
                        );
                    }),

                SelectFilter::make('status')
                    ->label('Order Status')
                    ->options([
                        'pending' => 'Pending',
                        'in_transit' => 'In Transit',
                        'cancelled' => 'Cancelled',
                        'delivered' => 'Completed',
                    ])
                    ->searchable()
                    ->placeholder('All Status')
                    ->query(function (Builder $query, $state): Builder {
                        return $query->when(
                            $state['value'] ?? null,
                            fn($query, $status) => $query->where('status', $status)
                        );
                    }),

            ])

            ->emptyStateHeading('No Orders Available')
            ->emptyStateDescription('Recent Order will appear here.');
    }
}
