<?php

namespace App\Filament\Admin\Widgets;

use App\Models\Payout;
use App\Models\Product;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Illuminate\Contracts\View\View;

class PayoutsOverview extends BaseWidget
{
    protected static ?int $sort = 2;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Payouts';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.admin.widgets.payouts-overview';

    public function render(): View
    {
        return view('filament.admin.widgets.payouts-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public $today;

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->today = now()->toDateString();
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $startDate = null;
        $endDate = null;

        switch ($this->filterOption) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today()->endOfDay();
                break;

            case 'this_week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;

            case 'this_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;

            case 'this_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;

            case 'custom_dates':
                if ($this->startDate && $this->endDate) {
                    $startDate = Carbon::parse($this->startDate)->startOfDay();
                    $endDate = Carbon::parse($this->endDate)->endOfDay();
                } else {
                    // session()->flash('error', 'Please select both start and end dates.');
                    return;
                }
                break;
        }

        $query = Payout::query()
            ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
            ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id')
            ->whereNotIn('sub_orders.status', ['pending','rejected','cancelled']);
        if ($startDate && $endDate) {
            $query->whereBetween('payouts.payout_on', [$startDate, $endDate]);
        }
        $payout = $query->selectRaw('
                SUM(sub_orders.total_amount) AS total_payout,
                SUM(CASE WHEN payouts.is_payout = true THEN sub_orders.total_amount ELSE 0 END) AS processed_payout,
                SUM(CASE WHEN payouts.is_payout = false THEN sub_orders.total_amount ELSE 0 END) AS pending_payout
            ')
            ->first();

        $this->stats = [
            [
                'label' => 'Total Revenue',
                'query' => 'RM '.number_format($payout->total_payout, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Primary-600, #004668);',
                'bg' => '#E5F7FF',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Payout Pending',
                'query' => 'RM '.number_format($payout->pending_payout, 2),
                'icon' => 'clock',
                'color' => '#8B5CF6',
                'bg' => '#EDE5FF;',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Payout Processed',
                'query' => 'RM '.number_format($payout->processed_payout, 2),
                'icon' => 'square-3-stack-3d',
                'color' => 'var(--Success-600, #16A34A);',
                'bg' => '#E5FFEF',
                'iconColor' => '#FFFFFF',
            ],

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }
}
