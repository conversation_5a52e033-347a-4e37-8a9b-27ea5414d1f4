<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ClinicDetail;
use App\Models\Order;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Js;
use Filament\Forms\Components\ToggleButtons;

class UsersChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'blogPostsChart';
    protected int | string | array $columnSpan = 'full';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Sales for Facilities';

    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->required()
                ->reactive(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()->required()
                ->closeOnDateSelection(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start')) // prevent end date before start date
                ->maxDate(now()) // prevent future dates
                ->reactive()->required()
                ->closeOnDateSelection(),

            Select::make('user_id')
                ->label('Facilities')
                ->options(
                    fn() => User::query()
                        ->whereHas('clinicData')
                        ->with('clinicData:id,user_id,clinic_name')
                        ->get()
                        ->filter(fn($user) => $user->clinicData && $user->clinicData->clinic_name)
                        ->mapWithKeys(fn($user) => [
                            $user->id => $user->clinicData->clinic_name,
                        ])
                        ->sortBy(fn($name) => $name)
                        ->toArray()
                )
                ->searchable()
                ->preload()
                ->placeholder('Facilities')
                ->reactive(),
                ToggleButtons::make('chart_type')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'line' => '📈', // Line chart emoji
                    'bar'  => '📊', // Bar chart emoji
                ])
                ->default('line')
                ->reactive()

        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $userId = $filters['user_id'] ?? null;
        $chartType = $filters['chart_type'] ?? 'line';

        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            // case 'custom':
            //     $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
            //     $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
            //     $groupByFormat = 'YYYY-MM-DD';
            //     break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
        }

        $data = Order::query()
            ->when($userId, fn($query) => $query->where('user_id', $userId))
            ->whereBetween('created_at', [$start, $end])
            ->where('status', 'delivered')
            ->selectRaw("TO_CHAR(created_at, '{$groupByFormat}') as period, SUM(amount) as amount")
            ->groupBy('period')
            ->orderBy('period')
            ->pluck('amount', 'period');

        if ($range === 'year') {
            $currentYear = now()->year;
            $months = [
                '01' => 'Jan',
                '02' => 'Feb',
                '03' => 'Mar',
                '04' => 'Apr',
                '05' => 'May',
                '06' => 'Jun',
                '07' => 'Jul',
                '08' => 'Aug',
                '09' => 'Sep',
                '10' => 'Oct',
                '11' => 'Nov',
                '12' => 'Dec',
            ];
            // dd($months);
            $categories = [];
            $seriesData = [];

            foreach ($months as $num => $name) {
                $categories[] = $name . ' ' . $currentYear;
                $seriesData[] = round((float) ($data[$num] ?? 0), 2);
            }
            //      } else {
            //     // $categories = $data->keys()->toArray();
            //     // $seriesData = $data->values()->map(fn($value) => (float) $value)->toArray();
            //     $startDate = Carbon::parse($start);
            //     $endDate = Carbon::parse($end);

            //     $dateRange = \Carbon\CarbonPeriod::create($startDate, $endDate);

            //     $categories = [];
            //     $seriesData = [];

            //     foreach ($dateRange as $date) {
            //         $dateStr = $date->format('Y-m-d');
            //         $categories[] = $dateStr;
            //         $seriesData[] = isset($data[$dateStr]) ? (float) $data[$dateStr] : 0;
            //     }
            // }
        } elseif ($range === 'custom') {
            $daysDiff = $start->diffInDays($end);
            $monthsDiff = $start->diffInMonths($end);

            if ($daysDiff <= 31) {
                // Daily data
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());

                foreach ($period as $date) {
                    $dateStr = $date->format('Y-m-d');
                    $categories[] = $date->format('M d');
                    $seriesData[] = isset($data[$dateStr]) ? round((float) $data[$dateStr], 2) : 0;
                }
            } elseif ($monthsDiff <= 24) {
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfMonth(), '1 month', $end->endOfMonth());

                foreach ($period as $month) {
                    $monthStr = $month->format('Y-m');
                    $categories[] = $month->format('M Y');
                    $seriesData[] = isset($data[$monthStr]) ? round((float) $data[$monthStr], 2) : 0;
                }
            } else {
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfYear(), '1 year', $end->endOfYear());

                foreach ($period as $year) {
                    $yearStr = $year->format('Y');
                    $categories[] = $yearStr;
                    $seriesData[] = isset($data[$yearStr]) ? round((float) $data[$yearStr], 2) : 0;
                }
            }
        } else {
            $categories = [];
            $seriesData = [];

            if ($range === 'month' || $range === 'week') {
                $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());

                foreach ($period as $date) {
                    $dateStr = $date->format('Y-m-d');
                    $categories[] = $date->format('M d');
                    $seriesData[] = isset($data[$dateStr]) ? round((float) $data[$dateStr], 2) : 0;
                }
            } elseif ($range === 'today') {
                $period = \Carbon\CarbonPeriod::create($start, '1 hour', $end);

                foreach ($period as $hour) {
                    $hourStr = $hour->format('H:i');
                    $categories[] = $hourStr;
                    $seriesData[] = isset($data[$hourStr]) ? round((float) $data[$hourStr], 2) : 0;
                }
            }
        }
        $clinicName = $userId ? ClinicDetail::where('user_id', $userId)->value('clinic_name') : '';
        return [
            'chart' => [
               'type' => $chartType,
                'height' => 400,
                'stacked' => $chartType === 'bar', // stacked only for bar chart
                'zoom' => [
                    'enabled' => false, // Disable zoom
                ],
                'toolbar' => [
                    'show' => false, // Disable toolbar which includes download option
                ],
                'width' => '100%',
            ],
            'series' => [
                [
                    'name' => 'Amount',
                    'data' => $seriesData,
                ],
            ],
           'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                'title' => [
                    'text' => $clinicName,
                    'style' => [
                        'fontWeight' => 600,
                        'fontSize' => '18px',
                    ],
                ],
            ],
             'legend' => [
                 'fontSize'=> '15px'
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                    // 'formatter' => 'function (val) { return "RM " + val.toFixed(2); }', // Add RM before the value
                ],
            ],
            // 'colors' => ['#0BA7F5'],
            'stroke' => [
                'curve' => 'straight', // no curve
                'width' => 2,
            ],
            'markers' => [
                'size' => 10, // size of the dot
                // 'colors' => ['#0BA7F5'],
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 10,
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                // 'enabledOnSeries' => [0], // ensure it's applied to the correct series
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                'offsetY' => -10, // moves label above point
            ],

        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                // xaxis: {
                //     labels: {
                //         formatter: function (val) {
                //             return val + '/24';
                //         }
                //     }
                // },
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return 'RM ' + val.toLocaleString();
                        }
                    }
                },
                // tooltip: {
                //     x: {
                //         formatter: function (val) {
                //             return val + '/24';
                //         }
                //     }
                // },
                // dataLabels: {
                //     enabled: true,
                //     formatter: function (val, opt) {
                //         return opt.w.globals.labels[opt.dataPointIndex] + ': RM ' + val.toLocaleString();
                //     },
                //     dropShadow: {
                //         enabled: true
                //     }
                // }
            }
        JS);
    }
}
