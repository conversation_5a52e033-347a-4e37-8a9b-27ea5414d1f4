<?php

namespace App\Filament\Admin\Widgets;

use App\Filament\Admin\Resources\OrderResource;
use App\Models\Order;
use App\Models\SubOrder;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Illuminate\Contracts\View\View;

class OrderStatsOverview extends BaseWidget
{
    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Orders';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.admin.widgets.order-stats-overview';
    // Render the view with stats data
    public function render(): View
    {
        return view('filament.admin.widgets.order-stats-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];

    // protected function getStats(): array
    // {

    //     $orderStats = Order::selectRaw("
    //             COUNT(*) as total,
    //             COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
    //             COUNT(CASE WHEN status = 'in_transit' THEN 1 END) as in_transit,
    //             COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered
    //         ")->first();

    //     // ONE QUERY for sub_orders table
    //     $subOrderStats = SubOrder::selectRaw("
    //         COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted,
    //         COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
    //     ")->first();

    //     $statuses = [
    //         [
    //             'label' => 'Total',
    //             'query' => $orderStats->total,
    //             'icon' => 'bi-bag',
    //             'color' => 'info',
    //             'bg' => 'rgba(229, 247, 255, 1)',
    //             'iconColor' => '#3b82f6',
    //         ],
    //         [
    //             'label' => 'Pending Acceptance',
    //             'query' => $orderStats->pending,
    //             'icon' => 'bi-clock-fill',
    //             'color' => 'warning',
    //             'bg' => 'rgba(255, 245, 229, 1)',
    //             'iconColor' => 'rgba(217, 119, 6, 1)',
    //         ],
    //         [
    //             'label' => 'Accepted Orders',
    //             'query' =>$subOrderStats->accepted,
    //             'icon' => 'bi-patch-check-fill',
    //             'color' => 'info',
    //             'bg' => 'rgba(237, 229, 255, 1)',
    //             'iconColor' => 'rgba(0, 70, 104, 1)',
    //         ],
    //         [
    //             'label' => 'Rejected Orders',
    //             'query' => $subOrderStats->rejected,
    //             'icon' => 'bi-x-circle-fill',
    //             'color' => 'danger',
    //             'bg' => 'rgba(255, 229, 229, 1)',
    //             'iconColor' => 'rgba(220, 38, 38, 1)',
    //         ],
    //         [
    //             'label' => 'In-Transit Orders',
    //             'query' => $orderStats->in_transit,
    //             'icon' => 'bi-truck',
    //             'color' => 'primary',
    //             'bg' => 'rgba(229, 237, 255, 1)',
    //             'iconColor' => '#2563EB',
    //         ],
    //         [
    //             'label' => 'Delivered Orders',
    //             'query' => $orderStats->delivered,
    //             'icon' => 'bi-box-seam',
    //             'color' => 'success',
    //             'bg' => 'rgba(229, 255, 239, 1)',
    //             'iconColor' => 'var(--Success-600, rgba(22, 163, 74, 1))',
    //         ],
    //     ];

    //     return array_map(
    //         fn($stat) =>
    //         Stat::make($stat['label'], $stat['query'])
    //             ->icon($stat['icon'])
    //             ->iconPosition('start')
    //             ->iconBackgroundColor($stat['color'])
    //             ->iconColor($stat['iconColor'])
    //             ->extraAttributes([
    //                 'style' => "background-color: {$stat['bg']};",
    //             ]),
    //         $statuses
    //     );
    // }

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $startDate = null;
        $endDate = null;

        switch ($this->filterOption) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today()->endOfDay();
                break;

            case 'this_week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;

            case 'this_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;

            case 'this_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;

            case 'custom_dates':
                if ($this->startDate && $this->endDate) {
                    $startDate = Carbon::parse($this->startDate)->startOfDay();
                    $endDate = Carbon::parse($this->endDate)->endOfDay();
                } else {
                    // session()->flash('error', 'Please select both start and end dates.');
                    return;
                }
                break;
        }
        // Apply date filter if range exists
        $orderQuery = Order::query();
        $subOrderQuery = SubOrder::query();

        if ($startDate && $endDate) {
            $orderQuery->whereBetween('created_at', [$startDate, $endDate]);
            $subOrderQuery->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Order stats
        $orderStats = $orderQuery->selectRaw("
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'in_transit' THEN 1 END) as in_transit,
        COUNT(CASE WHEN status = 'delivered' THEN 1 END) as delivered,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled
        ")->first();

        // SubOrder stats
        $subOrderStats = $subOrderQuery->selectRaw("
        COUNT(CASE WHEN status = 'accepted' THEN 1 END) as accepted,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        ")->first();

        // Set the stats after filtering
        $this->stats = [
            [
                'label' => 'Total',
                'query' => number_format($orderStats->total),
                'icon' => 'shopping-cart',
                'color' => '#004668',
                'bg' => 'rgba(229, 247, 255, 1)',
                'iconColor' => '#FFFFFF',
                'url' => OrderResource::getUrl()."?tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],
            [
                'label' => 'Pending Orders',
                'query' => number_format($orderStats->pending),
                'icon' => 'clock',
                'color' => '#F59E0B',
                'bg' => 'rgba(255, 245, 229, 1)',
               'iconColor' => '#FFFFFF',
                'url' => OrderResource::getUrl()."?tableFilters[status][values][0]=pending&tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],
            /*
            [
                'label' => 'Accepted Orders',
                'query' => number_format($subOrderStats->accepted),
                'icon' => 'check-circle',
                'color' => '#8B5CF6',
                'bg' => 'rgba(237, 229, 255, 1)',
                'iconColor' => '#FFFFFF',
                 //'url' => OrderResource::getUrl().'?tableFilters[status][values][0]=accepted'
            ],
            [
                'label' => 'Rejected Orders',
                'query' => number_format($subOrderStats->rejected),
                'icon' => 'shopping-cart',
                'color' => '#DC2626',
                'bg' => 'rgba(255, 229, 229, 1)',
               'iconColor' => '#FFFFFF',
                //'url' => OrderResource::getUrl().'?tableFilters[status][values][0]=rejected'
            ],
            */
            [
                'label' => 'In-Transit Orders',
                'query' => number_format($orderStats->in_transit),
                'icon' => 'truck',
                'color' => '#2563EB',
                'bg' => 'rgba(229, 237, 255, 1)',
               'iconColor' => '#FFFFFF',
                'url' => OrderResource::getUrl()."?tableFilters[status][values][0]=in_transit&tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],
            [
                'label' => 'Completed Orders',
                'query' => number_format($orderStats->delivered),
                'icon' => 'shopping-cart',
                'color' => '#16A34A',
                'bg' => 'rgba(229, 255, 239, 1)',
               'iconColor' => '#FFFFFF',
                'url' => OrderResource::getUrl()."?tableFilters[status][values][0]=delivered&tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],
            [
                'label' => 'Cancelled Orders',
                'query' => number_format($orderStats->cancelled),
                'icon' => 'shopping-cart',
                'color' => '#DC2626',
                'bg' => 'rgba(255, 229, 229, 1)',
               'iconColor' => '#FFFFFF',
                'url' => OrderResource::getUrl()."?tableFilters[status][values][0]=cancelled&tableFilters[created_at][from_date]=$startDate&tableFilters[created_at][to_date]=$endDate"
            ],

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }
}
