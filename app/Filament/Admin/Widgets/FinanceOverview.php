<?php

namespace App\Filament\Admin\Widgets;

use App\Models\OrderProduct;
use App\Models\Product;
use Filament\Forms\Components\DatePicker;
use Filament\Pages\Dashboard\Actions\FilterAction;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Contracts\View\View;

class FinanceOverview extends BaseWidget
{
    protected static ?int $sort = 1;

    protected static ?string $navigationIcon = null;

    protected static ?string $label = 'Finance123';

    protected int | string | array $rowSpan = 'full';

    protected static ?string $pollingInterval = '10s';

    protected static string $view = 'filament.admin.widgets.finance-overview';

    public function render(): View
    {
        return view('filament.admin.widgets.finance-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }
    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public $today;

    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->today = now()->toDateString();
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $today = Carbon::today()->toDateString();
        $startOfWeek = Carbon::now()->startOfWeek()->toDateString();
        $endOfWeek = Carbon::now()->endOfWeek()->toDateString();
        
        $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
        $endOfMonth = Carbon::now()->endOfMonth()->toDateString();
        
        // 1. Commission from order_products
        $commissionStats = \App\Models\OrderProduct::query()
            ->join('sub_orders', 'order_products.sub_order_id', '=', 'sub_orders.id')
            ->whereNotIn('sub_orders.status', ['pending','rejected','cancelled'])
            ->selectRaw("
                SUM(CASE WHEN DATE(sub_orders.approved_at) = ? THEN order_products.total_commission ELSE 0 END) as today_earning,
                SUM(CASE WHEN DATE(sub_orders.approved_at) BETWEEN ? AND ? THEN order_products.total_commission ELSE 0 END) as week_earning,
                SUM(CASE WHEN DATE(sub_orders.approved_at) BETWEEN ? AND ? THEN order_products.total_commission ELSE 0 END) as month_earning
            ", [$today, $startOfWeek, $endOfWeek, $startOfMonth, $endOfMonth])
            ->first();
        
        // 2. Revenue from sub_orders only (no join)
        $revenueStats = \App\Models\SubOrder::query()
            ->whereNotIn('status', ['pending','rejected','cancelled'])
            ->selectRaw("
                SUM(CASE WHEN DATE(approved_at) = ? THEN total_amount ELSE 0 END) as today_revenue,
                SUM(CASE WHEN DATE(approved_at) BETWEEN ? AND ? THEN total_amount ELSE 0 END) as week_revenue,
                SUM(CASE WHEN DATE(approved_at) BETWEEN ? AND ? THEN total_amount ELSE 0 END) as month_revenue
            ", [$today, $startOfWeek, $endOfWeek, $startOfMonth, $endOfMonth])
            ->first();
        
        // 3. Merge both results into a single object
        $finalStats = (object) [
            'today_earning' => $commissionStats->today_earning ?? 0,
            'week_earning' => $commissionStats->week_earning ?? 0,
            'month_earning' => $commissionStats->month_earning ?? 0,
            'today_revenue' => $revenueStats->today_revenue ?? 0,
            'week_revenue' => $revenueStats->week_revenue ?? 0,
            'month_revenue' => $revenueStats->month_revenue ?? 0,
        ];

        $this->stats = [
            [
                'label' => 'Today’s Revenue',
                'query' => 'RM '.number_format($finalStats->today_revenue, 2),
                'icon' => 'calendar-days',
                'color' => '#F59E0B;',
                'bg' => '#FFF5E5',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Weekly Revenue',
                'query' => 'RM '.number_format($finalStats->week_revenue, 2),
                'icon' => 'calendar-date-range',
                'color' => '#2563EB;',
                'bg' => '#E5EDFF;',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Monthly Revenue',
                'query' => 'RM '.number_format($finalStats->month_revenue, 2),
                'icon' => 'calendar',
                'color' => '#DC2626;',
                'bg' => '#FFE5E5',
                'iconColor' => '#FFFFFF',
            ],

            [
                'label' => 'Today’s Earnings',
                'query' => 'RM '.number_format($finalStats->today_earning, 2),
                'icon' => 'calendar-days',
                'color' => '#F59E0B;',
                'bg' => '#FFF5E5',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Weekly Earnings',
                'query' => 'RM '.number_format($finalStats->week_earning, 2),
                'icon' => 'calendar-date-range',
                'color' => '#2563EB;',
                'bg' => '#E5EDFF;',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Monthly Earnings',
                'query' => 'RM '.number_format($finalStats->month_earning, 2),
                'icon' => 'calendar',
                'color' => '#DC2626;',
                'bg' => '#FFE5E5',
                'iconColor' => '#FFFFFF',
            ],

        ];
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }

//     protected function getStats(): array
//     {
//         $today = Carbon::today()->toDateString();
//         $startOfWeek = Carbon::now()->startOfWeek()->toDateString();
//         $endOfWeek = Carbon::now()->endOfWeek()->toDateString();

//         $startOfMonth = Carbon::now()->startOfMonth()->toDateString();
//         $endOfMonth = Carbon::now()->endOfMonth()->toDateString();

//         $revenueStats = OrderProduct::query()
//             ->join('sub_orders', 'order_products.sub_order_id', '=', 'sub_orders.id')
//             ->whereIn('sub_orders.status', ['accepted', 'delivered'])
//             ->selectRaw("
//     SUM(CASE WHEN DATE(sub_orders.approved_at) = ? THEN order_products.total_commission ELSE 0 END) as today,
//     SUM(CASE WHEN DATE(sub_orders.approved_at) BETWEEN ? AND ? THEN order_products.total_commission ELSE 0 END) as week,
//     SUM(CASE WHEN DATE(sub_orders.approved_at) BETWEEN ? AND ? THEN order_products.total_commission ELSE 0 END) as month
// ", [$today, $startOfWeek, $endOfWeek, $startOfMonth, $endOfMonth])
//             ->first();

//         //sub_orders ke aprroved_at se leni he and status accepted
//         return [
//             Stat::make('Today’s Revenue', 'RM ' . number_format($revenueStats->today, 2))
//                 ->icon('heroicon-o-calendar-days')
//                 ->extraAttributes([
//                     'style' => 'background-color: #FFF5E5;',
//                 ])
//                 ->iconPosition('start')
//                 ->iconBackgroundColor('info')
//                 ->iconColor('light'),

//             Stat::make('Weekly Revenue', 'RM ' . number_format($revenueStats->week, 2))
//                 ->icon('heroicon-o-calendar-date-range')
//                 ->extraAttributes([
//                     'style' => 'background-color: #E5EDFF;',
//                 ])
//                 ->iconPosition('start')
//                 ->iconBackgroundColor('warning')
//                 ->iconColor('#ffffff'),

//             Stat::make('Monthly Revenue', 'RM ' . number_format($revenueStats->month, 2))
//                 ->icon('heroicon-o-calendar')
//                 ->extraAttributes([
//                     'style' => 'background-color: #FFE5E5;',
//                 ])
//                 ->iconPosition('start')
//                 ->iconBackgroundColor('info')
//                 ->iconColor('#ffffff'),
//         ];
//     }
}
