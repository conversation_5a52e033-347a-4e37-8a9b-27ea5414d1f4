<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ClinicPharmaSupplier;
use App\Models\OrderProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\DB;

class TopRevenue extends BaseWidget
{
    protected static ?string $heading  = 'Top Product by Revenue';

    public function getTableRecordKey(mixed $record): string
    {
        return (string) $record->product_id;
    }
    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->query(
                fn() => OrderProduct::query()
                    ->join('products', 'products.id', '=', 'order_products.product_id')
                    ->select(
                        'order_products.product_id',
                        'products.name as product_name',
                        DB::raw('SUM(order_products.total_commission) as total_commission'),
                    )
                    ->where('order_products.status','accepted')
                    ->groupBy('order_products.product_id', 'products.name')
                    ->orderByDesc('total_commission')->limit(10)
            )
            ->columns([
                TextColumn::make('product_name')
                    ->label('Product Name')
                    ->color('primary')
                     ->url(fn($record): string => route('filament.admin.resources.products.view', ['record' => $record->product_id])),

                TextColumn::make('total_commission')
                    ->label('Total Earning')
                    ->formatStateUsing(fn($state,$record) => 'RM ' . number_format($state, 2)),
            ])
            ->filters([
                Tables\Filters\Filter::make('date')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date') 
                            ->label('Select Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['date'])) {
                            $query->whereDate('order_products.created_at', Carbon::parse($data['date'])->format('Y-m-d'));
                        }
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return Carbon::parse($data['date'])->toFormattedDateString();
                    }),
            ])
            ->paginated(false)
            ->emptyStateHeading('No data available')
            ->emptyStateDescription('No top products by quantity available.');
    }
}
