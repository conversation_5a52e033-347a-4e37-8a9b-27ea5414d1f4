<?php

namespace App\Filament\Admin\Widgets;

use App\Models\ClinicPharmaSupplier;
use App\Models\User;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserAccountVerified extends BaseWidget
{
    protected static ?string $heading  = 'Verify Account Number';

    protected static ?string $model = ClinicPharmaSupplier::class;/*  */

    protected static ?int $sort = 3;

    protected int | string | array $columnSpan = 'full';

    public function table(Table $table): Table
    {
        return $table
            ->query(
                ClinicPharmaSupplier::query()
                    ->with(['clinic', 'pcDetail'])
                    ->whereNull('approved_by')
                    ->WhereNull('reject_by')
                    ->WhereNotNull('account_number')
                    ->latest()
                    ->limit(10)
            )
            ->columns([
                TextColumn::make('clinic.name')
                    ->label('Facilities')
                    ->searchable()
                    ->color('primary')
                    ->url(fn($record): string => route('filament.admin.resources.clinics.view', ['record' => $record->clinic_id])),

                TextColumn::make('pcDetail.name')
                    ->label('Pharma Supplier')
                    ->searchable()
                    ->color('primary')
                    ->formatStateUsing(function($record){
                        if ($record->pcDetail?->pcDetails?->company_type_id == 1) {
                            // If sole → prefer business_name, fallback to company_name
                            $pcName = ($record->pcDetail?->pcDetails?->company_name ?? $record->pcDetail?->pcDetails?->business_name);
                        } else {
                            // Otherwise → company_name only
                            $pcName = ($record->pcDetail?->pcDetails?->company_name ?? '');
                        }

                        return ucwords($pcName);//ucwords($record->pcDetail?->pcDetails?->company_name ?? $record->pcDetail?->name);                        
                    })
                    ->url(fn($record): string => route('filament.admin.resources.users.view', ['record' => $record->pc_id])),

                TextColumn::make('account_number')
                    ->label('Account Number')
                    ->searchable(),
                // TextInputColumn::make('account_number')->searchable()
                //     ->label('Account Number')->placeholder('Account Number')
                //     ->disabled(fn($record) => $record->status === 'approved')
                //     ->getStateUsing(function ($record) {
                //         return $record->account_number;
                //     })->searchable(),
            ])->actionsColumnLabel('Action')
            ->actions([
                Action::make('accept')
                    ->label('')
                    ->icon('heroicon-m-check')->size('sm')->iconButton()
                    ->color('success')
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg'])
                    ->action(function ($record) {
                        $validator = Validator::make(['account_number' => $record->account_number], [
                            'account_number' => ['min:1', 'max:20']
                        ]);

                        if ($validator->fails()) {
                            Notification::make()
                                ->title('Validation Error')
                                ->body($validator->errors()->first('account_number'))
                                ->danger()
                                ->send();
                            return;
                        }

                        $oldValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];

                        $record->update([
                            'status' => 'approved',
                            'approved_by' => Auth::id(),
                            'reject_by' => null
                        ]);


                        $newValues = [
                            'status' => $record->status,
                            'supplier_name' => ucwords($record->pcDetail?->pcDetails?->company_name ?? $record->pcDetail?->name),
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,  
                        ];
                        
                        activity()
                        ->causedBy(auth()->user())
                        ->useLog('facility_approval')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => $oldValues,
                            'attributes' => $newValues,
                        ])
                        ->log("The facility's account number of " . (ucwords($record->clinicDetail?->clinicData?->clinic_name) ?? '') . " has been approved");

                        Notification::make()
                            ->title('Facility Approved')
                            ->body('You have successfully approved the account.')
                            ->success()
                            ->send();
                    })
                    ->hidden(fn($record) => !is_null($record->approved_by) || !is_null($record->reject_by))
                    ->tooltip('Accept'),

                Action::make('reject')
                    ->label('')
                    ->icon('heroicon-m-x-mark')->size('sm')->iconButton()
                    ->color('danger')
                    ->action(function ($data, $record) {
                        $oldValues = [
                            'status' => $record->status,
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                        ];

                        $record->update([
                            'status' => 'rejected',
                            'reject_by' => Auth::id(),
                            'approved_by' => null,
                            'reject_reason' => $data['reason']

                        ]);

                        $newValues = [
                            'status' => $record->status,
                            'supplier_name' => ucwords($record->pcDetail?->pcDetails?->company_name ?? $record->pcDetail?->name),
                            'approved_by' => $record->approved_by ? (User::find($record->approved_by)?->name) : null,
                            'reject_by' => $record->reject_by ? (User::find($record->reject_by)?->name) : null,
                            'reject_reason' => $record->reject_reason,
                        ];

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('facility_rejection')
                            ->performedOn($record)
                            ->withProperties([
                                'old' => $oldValues,
                                'attributes' => $newValues,
                            ])
                            ->log("The facility's account number of " . (ucwords($record->clinicDetail?->clinicData?->clinic_name)?? '') . " has been rejected");
                        //Activity Log End

                        Notification::make()
                            ->title('Facility Rejected')
                            ->body('You have successfully rejected the account.')
                            ->success()
                            ->send();
                    })
                    ->extraAttributes(['class' => 'border-2 border-danger rounded-lg', 'style' => 'margin-left: inherit;'])
                    ->modalSubmitAction(
                        fn($action) =>
                        $action->label('Save')->color('danger')
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('')
                            ->validationMessages([
                                'required' => "The reason field is required.",
                            ])
                            ->rules(['required'])
                    ])
                    ->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to reject this facility?');
                        return $action;
                    })
                    ->hidden(fn($record) => !is_null($record->approved_by) || !is_null($record->reject_by))
                    ->tooltip('Reject'),
            ])
            ->paginated(false)
            ->emptyStateHeading('No Accounts Awaiting Verification')
            ->emptyStateDescription('Once clinics register new account numbers, they will be listed here for your review and verification.');
    }
}
