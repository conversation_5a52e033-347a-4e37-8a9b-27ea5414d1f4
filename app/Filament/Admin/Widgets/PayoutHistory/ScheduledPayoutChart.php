<?php

namespace App\Filament\Admin\Widgets\PayoutHistory;

use App\Models\Payout;
use App\Models\PcDetail;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Support\RawJs;
use Illuminate\Support\Facades\DB;
use Leandrocfe\FilamentApexCharts\Widgets\ApexChartWidget;
use Filament\Forms\Components\ToggleButtons;

class ScheduledPayoutChart extends ApexChartWidget
{
    /**
     * Chart Id
     *
     * @var string
     */
    protected static ?string $chartId = 'ScheduledPayoutChart';

    protected int | string | array $columnSpan = 'full';

    /**
     * Widget Title
     *
     * @var string|null
     */
    protected static ?string $heading = 'Scheduled Payout';

    protected static bool $isLazy = false;

    protected function getFormSchema(): array
    {
        return [
            Select::make('is_payout')
                ->label('Payout Status')
                ->options([
                    true => 'Proccessed',
                    false => 'Pending',
                ])
                ->placeholder('Total')
                ->reactive(),

            Select::make('pc_id')
                ->label('All Companies')
                ->options(
                    fn() => PcDetail::query()
                    ->whereNotNull('company_type_id')
                    ->with('companyType')
                    ->get()
                    ->mapWithKeys(function ($record) {
                        if ($record->company_type_id == 1) {
                            $name = $record->company_name ?? $record->business_name;
                        } else {
                            $name = $record->company_name ?? '-';
                        }
            
                        return [$record->user_id => $name]; // <-- key = ID, value = name
                    })
                    ->reject(fn($name) => empty($name)) // remove null/empty
                    ->unique()
                    ->sort()
                    ->toArray()
                )
                ->searchable()
                ->preload()
                ->placeholder('All Companies')
                ->reactive(),

            Select::make('range')
                ->options([
                    // 'today' => 'Today',
                    // 'week' => 'This Week',
                    'month' => 'This Month',
                    'year' => 'This Year',
                    'custom' => 'Custom Range',
                ])
                ->default('year')
                ->reactive(),

            DatePicker::make('date_start')
                ->label('Start Date')
                // ->default(now()->startOfYear())
                ->visible(fn($get) => $get('range') === 'custom')
                ->maxDate(fn($get) => $get('date_end') ?? now())
                ->reactive()
                ->required()
                ->closeOnDateSelection(),

            DatePicker::make('date_end')
                ->label('End Date')
                // ->default(now())
                ->visible(fn($get) => $get('range') === 'custom')
                ->minDate(fn($get) => $get('date_start')) // prevent end date before start date
                ->maxDate(now()) // prevent future dates
                ->reactive()
                ->required()
                ->closeOnDateSelection(),

                ToggleButtons::make('chart_type1')
                ->label('Chart Type')
                ->inline()
                ->options([
                    'line' => '📈', // Line chart emoji
                    'bar'  => '📊', // Bar chart emoji
                ])
                ->default('line')
                ->reactive()

        ];
    }


    /**
     * Chart options (series, labels, types, size, animations...)
     * https://apexcharts.com/docs/options
     *
     * @return array
     */

    protected function getOptions(): array
    {
        $filters = $this->filterFormData ?? [];
        $range = $filters['range'] != '' ? $filters['range'] : 'year';
        $pcId = $filters['pc_id'] ?? null;
        $isPayout = $filters['is_payout'] ?? null;
        $chartType = $filters['chart_type1'] ?? 'line';

        switch ($range) {
            case 'today':
                $start = now()->startOfDay();
                $end = now()->endOfDay();
                $groupByFormat = 'HH24:MI';
                break;
            case 'week':
                $start = now()->startOfWeek();
                $end = now()->endOfWeek();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'month':
                $start = now()->startOfMonth();
                $end = now()->endOfMonth();
                $groupByFormat = 'YYYY-MM-DD';
                break;
            case 'year':
                $start = now()->startOfYear();
                $end = now()->endOfYear();
                $groupByFormat = 'MM';
                break;
            // case 'custom':
            //     $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
            //     $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
            //     $groupByFormat = 'YYYY-MM-DD';
            //     break;
            case 'custom':
                $start = isset($filters['date_start']) ? \Carbon\Carbon::parse($filters['date_start'])->startOfDay() : now()->startOfYear();
                $end = isset($filters['date_end']) ? \Carbon\Carbon::parse($filters['date_end'])->endOfDay() : now()->endOfYear();
                $daysDiff = $start->diffInDays($end);
                $monthsDiff = $start->diffInMonths($end);

                if ($daysDiff <= 31) {
                    $groupByFormat = 'YYYY-MM-DD';
                } elseif ($monthsDiff <= 24) {
                    $groupByFormat = 'YYYY-MM';
                } else {
                    $groupByFormat = 'YYYY';
                }
                break;
        }

        $data = Payout::query()
            ->selectRaw("TO_CHAR(payouts.end_date, '{$groupByFormat}') as period, SUM(sub_orders.total_amount - COALESCE(op_comm.total_commission, 0)) AS amount")
            ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
            ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id')
            ->leftJoin(DB::raw('(
                SELECT sub_order_id, SUM(total_commission) AS total_commission
                FROM order_products
                GROUP BY sub_order_id
            ) as op_comm'), 'op_comm.sub_order_id', '=', 'sub_orders.id')
            ->when($pcId, fn($query) => $query->where('sub_orders.user_id', $pcId))
            ->whereBetween('payouts.end_date', [$start, $end])
            ->when(isset($isPayout) && $isPayout !== '', fn($query) => $query->where('payouts.is_payout', $isPayout))
            ->where('payouts.payout_type', 'schedule')
            ->groupBy('period')
            ->orderBy('period')
            ->pluck('amount', 'period');

        if ($range === 'year') {
            $currentYear = now()->year;
            $months = [
                '01' => $pcId . 'Jan',
                '02' => 'Feb',
                '03' => 'Mar',
                '04' => 'Apr',
                '05' => 'May',
                '06' => 'Jun',
                '07' => 'Jul',
                '08' => 'Aug',
                '09' => 'Sep',
                '10' => 'Oct',
                '11' => 'Nov',
                '12' => 'Dec',
            ];

            $categories = [];
            $seriesData = [];

            foreach ($months as $num => $name) {
                // dd($data,$num);
                $categories[] = $name . ' ' . $currentYear;
                $seriesData[] = round((float) ($data[$num] ?? 0), 2);
            }
            // } else {
            //     // $categories = $data->keys()->toArray();
            //     // $seriesData = $data->values()->map(fn($value) => (float) $value)->toArray();
            //     $startDate = Carbon::parse($start);
            //     $endDate = Carbon::parse($end);

            //     $dateRange = \Carbon\CarbonPeriod::create($startDate, $endDate);

            //     $categories = [];
            //     $seriesData = [];

            //     foreach ($dateRange as $date) {
            //         $dateStr = $date->format('Y-m-d');
            //         $categories[] = $dateStr;

            //         $seriesData[] = isset($data[$dateStr]) ? (float) $data[$dateStr] : 0;
            //     }
            // }

        } elseif ($range === 'custom') {
            $daysDiff = $start->diffInDays($end);
            $monthsDiff = $start->diffInMonths($end);

            if ($daysDiff <= 31) {
                // Daily data
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());

                foreach ($period as $date) {
                    $dateStr = $date->format('Y-m-d');
                    $categories[] = $date->format('M d');
                    $seriesData[] = isset($data[$dateStr]) ? round((float) $data[$dateStr], 2) : 0;
                }
            } elseif ($monthsDiff <= 24) {
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfMonth(), '1 month', $end->endOfMonth());

                foreach ($period as $month) {
                    $monthStr = $month->format('Y-m');
                    $categories[] = $month->format('M Y');
                    $seriesData[] = isset($data[$monthStr]) ? round((float) $data[$monthStr], 2) : 0;
                }
            } else {
                $categories = [];
                $seriesData = [];
                $period = \Carbon\CarbonPeriod::create($start->startOfYear(), '1 year', $end->endOfYear());

                foreach ($period as $year) {
                    $yearStr = $year->format('Y');
                    $categories[] = $yearStr;
                    $seriesData[] = isset($data[$yearStr]) ? round((float) $data[$yearStr], 2) : 0;
                }
            }
        } else {
            $categories = [];
            $seriesData = [];

            if ($range === 'month' || $range === 'week') {
                $period = \Carbon\CarbonPeriod::create($start->startOfDay(), '1 day', $end->endOfDay());

                foreach ($period as $date) {
                    $dateStr = $date->format('Y-m-d');
                    $categories[] = $date->format('M d');
                    $seriesData[] = isset($data[$dateStr]) ? round((float) $data[$dateStr], 2) : 0;
                }
            } elseif ($range === 'today') {
                $period = \Carbon\CarbonPeriod::create($start, '1 hour', $end);

                foreach ($period as $hour) {
                    $hourStr = $hour->format('H:i');
                    $categories[] = $hourStr;
                    $seriesData[] = isset($data[$hourStr]) ? round((float) $data[$hourStr], 2) : 0;
                }
            }
        }
        $pcDetail = PcDetail::whereUserId($pcId)->first();

        $pcName = '';

        if ($pcDetail) {
            if ($pcDetail->company_type_id == 1) {
                // If sole → prefer business_name, fallback to company_name
                $pcName = ($pcDetail->company_name ?? $pcDetail->business_name);
            } else {
                // Otherwise → company_name only
                $pcName = ($pcDetail->company_name ?? '');
            }
        }
        return [
            'chart' => [
                'type' => $chartType,
                'height' => 400,
                'stacked' => $chartType === 'bar', // stacked only for bar chart
                'zoom' => [
                    'enabled' => false, // Disable zoom
                ],
                'toolbar' => [
                    'show' => false, // Disable toolbar which includes download option
                ],
                'width' => '100%',
            ],
            'series' => [
                [
                    'name' => 'Amount',
                    'data' => $seriesData,
                ],
            ],
            'xaxis' => [
                'categories' => $categories,
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                ],
                'title' => [
                    'text' => $pcName,
                    'style' => [
                        'fontWeight' => 600,
                        'fontSize' => '18px',
                    ],
                ],
            ],
            'yaxis' => [
                'labels' => [
                    'style' => [
                        'colors' => '#9ca3af',
                        'fontWeight' => 600,
                    ],
                    // 'formatter' => 'function (val) { return "RM " + val.toFixed(2); }', // Add RM before the value
                ],
            ],
            'colors' => ['#0BA7F5'],
            'plotOptions' => [
                'bar' => [
                    'horizontal' => false,
                    'dataLabels' => [
                        'position' => 'top', // or 'middle'
                        'hideOverflowingLabels' => false,
                    ],
                ],
            ],
'stroke' => [
                'curve' => 'straight', // no curve
                'width' => 2,
            ],
            'markers' => [
                'size' => 10, // size of the dot
                // 'colors' => ['#0BA7F5'],
                'strokeColors' => '#fff',
                'strokeWidth' => 2,
                'hover' => [
                    'size' => 10,
                ],
            ],
            'dataLabels' => [
                'enabled' => true,
                // 'enabledOnSeries' => [0], // ensure it's applied to the correct series
                'style' => [
                    'fontSize' => '11px',
                    'colors' => ['#333'],
                ],
                'background' => [
                    'enabled' => true,
                    'borderRadius' => 4,
                    'dropShadow' => [
                        'enabled' => false,
                    ],
                ],
                'offsetY' => -10, // moves label above point
            ],

        ];
    }

    protected function extraJsOptions(): ?RawJs
    {
        return RawJs::make(<<<'JS'
            {
                // xaxis: {
                //     labels: {
                //         formatter: function (val) {
                //             return val + '/24';
                //         }
                //     }
                // },
                yaxis: {
                    labels: {
                        formatter: function (val) {
                            return 'RM ' + val.toLocaleString();
                        }
                    }
                },
                // tooltip: {
                //     x: {
                //         formatter: function (val) {
                //             return val + '/24';
                //         }
                //     }
                // },
                // dataLabels: {
                //     enabled: true,
                //     formatter: function (val, opt) {
                //         return opt.w.globals.labels[opt.dataPointIndex] + ': RM ' + val.toLocaleString();
                //     },
                //     dropShadow: {
                //         enabled: true
                //     }
                // }
            }
        JS);
    }
}
