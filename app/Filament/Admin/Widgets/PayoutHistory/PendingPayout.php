<?php

namespace App\Filament\Admin\Widgets\PayoutHistory;

use App\Models\OrderProduct;
use App\Models\Payout;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Facades\DB;

class PendingPayout extends BaseWidget
{
    protected static ?string $heading  = 'Pending Payout';
    protected int | string | array $columnSpan = 'full';

    public function getTableRecordKey(mixed $record): string
    {
        return (string) $record->product_id; // Ensure you're using the correct key
    }
    public function table(Tables\Table $table): Tables\Table
    {
        $latestProcessedPayout = Payout::where('is_payout', true)
            ->orderByDesc('payout_on')
            ->first();

        $nextPayout = Payout::query()
            ->where('is_payout', false)
            ->orderByDesc('end_date')
            ->first();

        return $table
            ->query(
                fn() => Payout::query()
                    ->select(
                        'payouts.id',
                        'sub_orders.user_id as pc_id',
                        // 'users.name as pc_name',
                        DB::raw("
                            CASE 
                                WHEN pc_details.company_type_id = 1 
                                    THEN COALESCE(pc_details.company_name, pc_details.business_name) 
                                ELSE pc_details.company_name 
                            END as pc_name
                        "),
                        DB::raw('SUM(sub_orders.total_amount) AS total_amount'),
                        DB::raw("'" . $latestProcessedPayout?->payout_on . "' as last_payout_date"),
                        DB::raw("'" . $nextPayout?->end_date . "' as next_payout_date")
                    )
                    ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
                    ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id')
                    ->join('users', 'users.id', '=', 'sub_orders.user_id')
                    ->leftJoin('pc_details', 'pc_details.user_id', '=', 'users.id') // join pc_details table
                    ->where('payouts.is_payout', false)
                    ->groupBy(
                        'payouts.id', 
                        'sub_orders.user_id', 
                        // 'users.name',
                        'pc_details.company_type_id',
                        'pc_details.company_name',
                        'pc_details.business_name',
                        'payouts.payout_on'
                        )
            )->defaultSort('payouts.id', 'desc')
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->formatStateUsing(fn($state, $record) => $record->id),

                TextColumn::make('pc_name')
                    ->label('Pharmaceutical Supplier')
                    ->formatStateUsing(fn($state, $record) => $record->pc_name)
                    ->color('primary')
                    ->sortable()
                    ->url(fn($record): string => route('filament.admin.resources.users.view', ['record' => $record->pc_id])),

                TextColumn::make('last_payout_date')
                    ->label('Last Payout Date')
                    ->sortable()
                    ->formatStateUsing(fn($state, $record) => $record->last_payout_date ? Carbon::parse($record->last_payout_date)->format('M d, Y | h:i A') : '-'),

                TextColumn::make('next_payout_date')
                    ->label('Next Payout Date')
                    ->sortable()
                    ->formatStateUsing(fn($state, $record) => $record->next_payout_date ? Carbon::parse($record->next_payout_date)->format('M d, Y | h:i A') : '-'),

                TextColumn::make('total_amount')
                    ->label('Amount')
                    ->sortable()
                    ->formatStateUsing(fn($state, $record) => 'RM ' . number_format($record->total_amount, 2)),
            ])
            // ->paginated(true)
            ->emptyStateHeading('No data available')
            ->emptyStateDescription('No top products by quantity available.');
    }
}
