<?php

namespace App\Filament\Admin\Widgets\PayoutHistory;

use App\Filament\Admin\Resources\FullPayoutResource;
use App\Filament\Admin\Resources\ScheduledPayoutResource;
use App\Filament\Admin\Resources\OrderResource;
use App\Models\Payout;
use Illuminate\Contracts\View\View; // Import the View contract
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;
use EightyNine\FilamentAdvancedWidget\AdvancedStatsOverviewWidget as BaseWidget;
use Illuminate\Support\Facades\DB;

class PayoutsOverview extends BaseWidget
{
    protected static ?int $sort = 2;
    protected static ?string $navigationIcon = null;
    protected static ?string $label = 'Payouts';
    protected int | string | array $rowSpan = 'full';
    protected static ?string $pollingInterval = '10s';
    protected static string $view = 'filament.admin.widgets.payout-history.payouts-overview';

    public $startDate;
    public $endDate;
    public $filterOption = 'this_month';
    public $stats = [];
    public function getColumnSpan(): int | string | array
    {
        return 2;
    }

    public function mount()
    {
        $this->applyFilter();
    }

    public function applyFilter()
    {
        $startDate = null;
        $endDate = null;

        switch ($this->filterOption) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today()->endOfDay();
                break;

            case 'this_week':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;

            case 'this_month':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;

            case 'this_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;

            case 'custom_dates':
                if ($this->startDate && $this->endDate) {
                    $startDate = Carbon::parse($this->startDate)->startOfDay();
                    $endDate = Carbon::parse($this->endDate)->endOfDay();
                } else {
                    // session()->flash('error', 'Please select both start and end dates.');
                    return;
                }
                break;
        }

        // Apply filters to the query
        // $payout = Payout::query()
        //     ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
        //     ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id')
        //     ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
        //         $query->whereBetween('payouts.end_date', [$startDate, $endDate]);
        //     })
        //     ->selectRaw('
        //     SUM(CASE WHEN payouts.is_payout = true THEN sub_orders.total_amount ELSE 0 END) AS processed_full_payout,
        //     SUM(CASE WHEN payouts.is_payout = false THEN sub_orders.total_amount ELSE 0 END) AS pending_full_payout
        // ')
        //     ->first();

        $payout = Payout::query()
        ->join('payout_sub_orders', 'payouts.id', '=', 'payout_sub_orders.payout_id')
        ->join('sub_orders', 'sub_orders.id', '=', 'payout_sub_orders.sub_order_id')
        ->leftJoin(DB::raw('(
            SELECT sub_order_id, SUM(total_commission) AS total_commission
            FROM order_products
            GROUP BY sub_order_id
        ) as op_comm'), 'op_comm.sub_order_id', '=', 'sub_orders.id')
        ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
            $query->whereBetween('payouts.end_date', [$startDate, $endDate]);
        })
        ->selectRaw('
            SUM(CASE WHEN payouts.is_payout = true AND payouts.payout_type = \'full\' THEN sub_orders.total_amount ELSE 0 END) AS processed_full_payout,
            SUM(CASE WHEN payouts.is_payout = false AND payouts.payout_type = \'full\' THEN sub_orders.total_amount ELSE 0 END) AS pending_full_payout,
            SUM(CASE WHEN payouts.is_payout = true AND payouts.payout_type = \'schedule\' THEN sub_orders.total_amount - COALESCE(op_comm.total_commission, 0) ELSE 0 END) AS processed_scheduled_payout,
            SUM(CASE WHEN payouts.is_payout = false AND payouts.payout_type = \'schedule\' THEN sub_orders.total_amount - COALESCE(op_comm.total_commission, 0) ELSE 0 END) AS pending_scheduled_payout,
            SUM(CASE WHEN payouts.payout_type = \'schedule\' THEN COALESCE(op_comm.total_commission, 0) ELSE 0 END) AS total_commission
        ')
        ->first();
        
        // Always fetch next_payout_on regardless of filter
        $nextPayout = Payout::query()
            ->where('is_payout', false)
            ->orderByDesc('end_date')
            ->value('end_date');

        $orderReve = DB::table('sub_orders as so')
            ->leftJoin(DB::raw('(
                SELECT sub_order_id, SUM(total_commission) AS total_commission
                FROM order_products
                GROUP BY sub_order_id
            ) as op_comm'), 'op_comm.sub_order_id', '=', 'so.id')
            ->whereNotIn('so.status', ['pending', 'rejected', 'cancelled'])
            ->selectRaw('

                COALESCE(SUM(so.total_amount), 0) AS total_revenue,
                COALESCE(SUM(op_comm.total_commission), 0) AS total_commission
            ')
            ->when($startDate && $endDate, function ($query) use ($startDate, $endDate) {
                $query->whereBetween(DB::raw('DATE(so.created_at)'), [$startDate, $endDate]);
            })
            ->first();
        

        // Set the stats after filtering
        $this->stats = [
            [
                'label' => 'Pending Full Payout',
                'query' =>  'RM ' . number_format($payout->pending_full_payout, 2),
                'icon' => 'exclamation-triangle',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF',
                'iconColor' => '#FFFFFF',
                'url' => FullPayoutResource::getUrl('index')."?tableFilters[payout_on][payout_on_from]=$startDate&tableFilters[payout_on][payout_on_until]=$endDate"
            ],
             [
                'label' => 'Processed Full Payout',
                'query' =>  'RM ' . number_format($payout->processed_full_payout, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Success-600, #16A34A);',
                'bg' => '#E5FFEF',
                'iconColor' => '#FFFFFF',
                'url' => FullPayoutResource::getUrl('index')."?tableFilters[payout_on][payout_on_from]=$startDate&tableFilters[payout_on][payout_on_until]=$endDate"
            ],
            [
                'label' => 'Pending Scheduled Payout',
                'query' =>  'RM ' . number_format($payout->pending_scheduled_payout, 2),
                'icon' => 'exclamation-triangle',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF',
                'iconColor' => '#FFFFFF',
                'url' => ScheduledPayoutResource::getUrl('index')."?tableFilters[payout_on][payout_on_from]=$startDate&tableFilters[payout_on][payout_on_until]=$endDate"
            ],
             [
                'label' => 'Processed Scheduled Payout',
                'query' =>  'RM ' . number_format($payout->processed_scheduled_payout, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Success-600, #16A34A);',
                'bg' => '#E5FFEF',
                'iconColor' => '#FFFFFF',
                'url' => ScheduledPayoutResource::getUrl('index')."?tableFilters[payout_on][payout_on_from]=$startDate&tableFilters[payout_on][payout_on_until]=$endDate"
            ],
            [
                'label' => 'Next Payout on',
                'query' => $nextPayout ? Carbon::parse($nextPayout)->format('M d, Y | h:i A') : null,
                'icon' => 'information-circle',
                'color' => 'var(--Warning-500, #F59E0B);',
                'bg' => '#FFF5E5',
                'iconColor' => '#FFFFFF',
            ],
            [
                'label' => 'Total Pending Payout',
                'query' => 'RM ' . number_format($payout->pending_full_payout + $payout->pending_scheduled_payout, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Rose-600, #E11D48)', // Use a rose/red color for distinction
                'bg' => '#FFE5EC;', // Light rose/pink background
                'iconColor' => 'var(--White, #FFFFFF)',
                // 'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered&tableFilters[status][values][2]=in_transit'
            ],
            [
                'label' => 'Total Processed Payout',
                'query' => 'RM ' . number_format($payout->processed_full_payout + $payout->processed_scheduled_payout, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Purple-600, #7C3AED)', // Use a purple color for distinction
                'bg' => '#F3E8FF;', // Light purple background
                'iconColor' => 'var(--White, #FFFFFF)',
                // 'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered&tableFilters[status][values][2]=in_transit'
            ],
            [
                'label' => 'Total Revenue',
                'query' => 'RM ' . number_format($orderReve->total_revenue, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Primary-600, #004668)',
                'bg' => '#E5F7FF;', 
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered&tableFilters[status][values][2]=in_transit'
            ],
            [
                'label' => 'Total Earnings',
                'query' => 'RM ' . number_format($orderReve->total_commission, 2),
                'icon' => 'currency-dollar',
                'color' => 'var(--Success-600, #16A34A)',
                'bg' => '#E5FFEF;',
                'iconColor' => 'var(--White, #FFFFFF)',
                'url'=>OrderResource::getUrl().'?tableFilters[status][values][0]=accepted&tableFilters[status][values][1]=delivered&tableFilters[status][values][2]=in_transit'
            ],
        ];
    }

    // Render the view with stats data
    public function render(): View
    {
        return view('filament.admin.widgets.payout-history.payouts-overview', [
            'stats' => $this->applyFilter(),
            'filterOption' => $this->filterOption,
        ]);
    }

    public function onFilterOptionChange()
    {
        if ($this->filterOption === 'custom_dates') {
            // maybe show a message or reset dates
        } else {
            $this->applyFilter();
        }
    }

    public function download(): StreamedResponse
    {
        $filename = 'payouts_' . now()->format('Ymd_His') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];
        return response()->streamDownload(function () {
            $handle = fopen('php://output', 'w');

            // Header row
            fputcsv($handle, ['Type', 'Amount']);

            // Example data — replace with real filtered results
            $data = [
                ['Pending Payout', $this->stats['pending_payout'] ?? 0],
                ['Processed Payout', $this->stats['processed_payout'] ?? 0],
                ['Next Payout On', $this->stats['next_payout_on'] ?? 'N/A'],
            ];
            foreach ($data as $row) {
                fputcsv($handle, $row);
            }
            fclose($handle);
        }, $filename, $headers);
    }
}
