<?php

namespace App\Filament\Admin\Resources\BannerResource\Pages;

use App\Filament\Admin\Resources\BannerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListBanners extends ListRecords
{
    protected static string $resource = BannerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Banner'),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // $this->getResource()::getUrl('index') => "Banner Management",
            // 3 => "List",
        ];
    }

    public function getTabs(): array
    {
        return [
            'Pop-up Banner' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->where('banner_type', 'popup_banner');
            }),
            'Main Banner' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->where('banner_type', 'main_banner');
            }),
            'Sub Banner' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                 $query->where('banner_type', 'sub_banner');
            }),
        ];
    }
}
