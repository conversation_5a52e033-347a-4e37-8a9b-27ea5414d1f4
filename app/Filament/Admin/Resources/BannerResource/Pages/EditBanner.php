<?php

namespace App\Filament\Admin\Resources\BannerResource\Pages;

use App\Filament\Admin\Resources\BannerResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;


class EditBanner extends EditRecord
{
    protected static string $resource = BannerResource::class;

    public function getBreadcrumbs(): array
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            // 1 => "Master",
            $indexUrl => "Banner Management",
            3 => "Edit Banner",
        ];
    }

    protected function getRedirectUrl(): string
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return $indexUrl;
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.banner.title.updated'))
            ->title(__('message.banner.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Publish'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function getHeaderActions(): array
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url($indexUrl),
        ];
    }
}
