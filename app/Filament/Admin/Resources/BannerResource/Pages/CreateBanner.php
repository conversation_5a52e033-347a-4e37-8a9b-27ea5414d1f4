<?php

namespace App\Filament\Admin\Resources\BannerResource\Pages;

use App\Filament\Admin\Resources\BannerResource;
use App\Models\Banner;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;


class CreateBanner extends CreateRecord
{
    protected static string $resource = BannerResource::class;

    public function getBreadcrumbs(): array
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            // 1 => "Master",
            $indexUrl => "Banner Management",
            3 => "Add Banner",
        ];
    }


    protected function getRedirectUrl(): string
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return $indexUrl;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        if ($data['banner_type'] == 'popup_banner') {
            $checkPopupBanner = Banner::where(['banner_type' => 'popup_banner', 'status' => true])->first();
            $overlappingBanner = Banner::where('banner_type', 'popup_banner')
                ->where('status', true)
                ->where(function ($query) use ($data) {
                    $query->where(function ($q) use ($data) {
                        $q->where('start_date', '<=', $data['start_date'])
                            ->where('end_date', '>=', $data['start_date']);
                    })->orWhere(function ($q) use ($data) {
                        $q->where('start_date', '<=', $data['end_date'])
                            ->where('end_date', '>=', $data['end_date']);
                    })->orWhere(function ($q) use ($data) {
                        $q->where('start_date', '>=', $data['start_date'])
                            ->where('end_date', '<=', $data['end_date']);
                    });
                })
                ->first();

            if ($overlappingBanner) {
                // Set status to inactive if there's an overlapping active popup banner
                $data['status'] = false;

                // Prepare notification to be shown after create
                $this->dispatch('show-popup-banner-conflict-notification');
            }
        }

        return $data;
    }


    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.banner.title.created'))
            ->title(__('message.banner.create_success'));
    }


    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Publish'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function getHeaderActions(): array
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $indexUrl = BannerResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url($indexUrl),
        ];
    }

    public function mount(): void
    {
        parent::mount();

        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $bannerType = match ($activeTab) {
            'Pop-up Banner' => 'popup_banner',
            'Main Banner' => 'main_banner',
            'Sub Banner' => 'sub_banner',
            default => 'popup_banner',
        };

        $this->form->fill([
            'banner_type' => $bannerType,
        ]);
    }
}
