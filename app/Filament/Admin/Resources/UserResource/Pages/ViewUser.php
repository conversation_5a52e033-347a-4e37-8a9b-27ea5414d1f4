<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Models\User;
use Filament\Actions;
use Filament\Forms\Components\Textarea;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Admin\Resources\UserResource;
use App\Mail\RejectPCMail;
use Filament\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Indianic\Settings\Models\GlobalSettings;
use Illuminate\Support\Facades\Mail;

class ViewUser extends ViewRecord
{
    protected static string $resource = UserResource::class;
    public $rejected_reason = "";
    public $isProduct = false;
    public ?int $activeTab = 1;

    public static function canConfig(Model $record): bool
    {
        return Auth::user()->hasRole('Super Admin') || Auth::user()->can('pharmaceutical-suppliers_configure');
    }

    public function getTitle(): string|Htmlable
    {
        return pcCompanyName($this->record->pcDetails) ?? "";
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Pharmaceutical suppliers",
            3 => "View",
        ];
    }

    public function mount(int | string $record): void
    {
        $this->record = User::with([
            // Explicitly load warehouses with their city/state
            'warehouses.city',
            'warehouses.state',
            'pcDetails.companyType',
            'addresses.city',
            'addresses.state',
            'addresses.country',
            'address.city',
            'address.state',
            'address.country',
            'accounttype',
            'pharmaSuppliers',
            'userAddresses.city',
            'userAddresses.state',
            'userAddresses.country'
        ])->find($record);
    }

    public static function canCreate(): bool
    {
        return Auth::user()->hasRole('Super Admin') ||  Auth::user()->can('products_create product for pharmaceutical company');
    }


    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('is_credit_line_enable')
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined()
                ->label('Enable Credit Line')
                ->visible(function (User $record) {
                    return !$record->pcDetails?->is_credit_line;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_credit_line' => true]);
                }),
            Actions\Action::make('is_credit_line_disable')
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->label('Disable Credit Line')
                ->outlined()
                ->visible(function (User $record) {
                    return $record->pcDetails?->is_credit_line;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_credit_line' => false]);
                }),

            Actions\Action::make('mark_as_restricted')
                ->visible(function (User $record) {
                    return !$record->pcDetails?->is_restricted;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_restricted' => true]);

                    // Activity log for marking as restricted
                    $companyName = $record->pcDetails?->business_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'attributes' => [
                                'is_restricted' => true,
                            ]
                        ])
                        ->log("Pharmaceutical Supplier ({$companyName}) has been marked as Restricted ");
                })
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined(),
            Actions\Action::make('mark_as_featured')
                ->visible(function (User $record) {
                    return !$record->pcDetails?->is_featured;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_featured' => true]);
                    $companyName = $record->pcDetails?->business_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'attributes' => [
                                'is_featured' => true,
                            ]
                        ])
                        ->log("Pharmaceutical Supplier ({$companyName}) has been marked as Featured");
                })
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined(),

            Actions\Action::make('mark_as_non_featured')
                ->visible(function (User $record) {
                    return $record->pcDetails?->is_featured;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_featured' => false]);
                    $companyName = $record->pcDetails?->business_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'attributes' => [
                                'is_featured' => false,
                            ]
                        ])
                        ->log("Pharmaceutical Supplier ({$companyName}) has been marked as Non Featured");
                })
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined(),
            Actions\Action::make('configure')
                ->visible(function ($record) {
                    return !empty($record->pcDetails) && static::canConfig($record);
                })
                ->icon('heroicon-o-cog')
                ->action(function ($record, $data) {
                    if (empty($record->pcDetails)) {
                        Notification::make()
                            ->warning()
                            ->title('Pc Details is not available for this Pharmaceutical Company, please add it first!')
                            ->send();
                        return;
                    }

                    if ($record->payouts()->where('is_payout', false)->exists()) {
                        Notification::make()
                            ->warning()
                            ->title('You can\'t change payout because already ongoing payout found!')
                            ->send();
                        return;
                    }

                    // Get old values for activity log
                    $oldData = [
                        'commission_type' => $record->pcDetails->commission_type ?? null,
                        'commission_percentage' => $record->pcDetails->commission_percentage ?? null,
                        'commission_payout_option' => $record->pcDetails->commission_payout_option ?? null,
                        'payment_method' => isset($record->pcDetails->payment_method)
                            ? ($record->pcDetails->payment_method === 'online_manual'
                                ? 'Online Through Link + Manual'
                                : ($record->pcDetails->payment_method === 'manual'
                                    ? 'Manual'
                                    : $record->pcDetails->payment_method))
                            : null,
                        'cycle_type' => isset($record->pcDetails->cycle_type)
                            ? ($record->pcDetails->cycle_type === 'bi_weekly'
                                ? '15 Days'
                                : ($record->pcDetails->cycle_type === 'monthly'
                                    ? 'Monthly'
                                    : $record->pcDetails->cycle_type))
                            : null,
                        'is_restricted' => isset($record->pcDetails->is_restricted)
                            ? ($record->pcDetails->is_restricted ? 'Restricted' : 'Global')
                            : null,
                    ];

                    // Prepare new data for activity log (before updating)
                    $newData = [
                        'commission_type' => $data['commission_type'] ?? null,
                        'commission_percentage' => $data['commission_percentage'] ?? null,
                        'commission_payout_option' => $data['commission_payout_option'] ?? null,
                        'payment_method' => isset($data['payment_method'])
                            ? ($data['payment_method'] === 'online_manual'
                                ? 'Online Through Link + Manual'
                                : ($data['payment_method'] === 'manual'
                                    ? 'Manual'
                                    : $data['payment_method']))
                            : null,
                        'cycle_type' => isset($data['cycle_type'])
                            ? ($data['cycle_type'] === 'bi_weekly'
                                ? '15 Days'
                                : ($data['cycle_type'] === 'monthly'
                                    ? 'Monthly'
                                    : $data['cycle_type']))
                            : null,
                        'is_restricted' => isset($data['is_restricted'])
                            ? ($data['is_restricted'] ? 'Restricted' : 'Global')
                            : null,
                    ];

                    // Find changed attributes
                    $changedData = array_diff_assoc($newData, $oldData);

                    if (
                        isset($data['commission_payout_option']) && $data['commission_payout_option'] === 'full' &&
                        (!isset($data['payment_method']) || $data['payment_method'] !== 'online_manual')
                    ) {
                        $data['cycle_type'] = 'monthly';
                    }

                    if (!empty($changedData)) {
                        $record->pcDetails->update($data);
                        $companyName = $record->pcDetails?->company_name ?? 'N/A';

                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('users')
                            ->performedOn($record)
                            ->withProperties([
                                'attributes' => $changedData,
                                'old' => array_intersect_key($oldData, $changedData),
                            ])
                            ->log("Pharmaceutical Supplier Configuration has been updated for ({$companyName})");
                    }

                    Notification::make()
                        ->success()
                        ->title('Pharmaceutical Supplier Configuration has been updated successfully!')
                        ->send();
                })
                ->form([
                    Section::make()
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('commission_type')
                                        ->label(new HtmlString('Commission Type <sapn style="color:red">*</span>'))
                                        ->validationAttribute('commission type')
                                        ->placeholder('Select commission type')
                                        ->options([
                                            'percentage' => 'Percentage',
                                            'flat' => 'Flat',
                                        ])
                                        ->disabled(function () {
                                            $user = \Auth::user();
                                            if ($user->hasRole('Super Admin')) {
                                                return false; // Enable for Super Admin
                                            }
                                            if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                                return false;
                                            }
                                            return true;
                                        })
                                        ->default(fn($record) => $record->pcDetails->commission_type ?? null)
                                        ->reactive()
                                        ->rules(['required']),
                                    TextInput::make('commission_percentage')
                                        ->label(new HtmlString('Commission <sapn style="color:red">*</span>'))
                                        ->validationAttribute('commission percentage')
                                        ->placeholder('Enter commission')
                                        ->disabled(function () {
                                            $user = \Auth::user();
                                            if ($user->hasRole('Super Admin')) {
                                                return false; // Enable for Super Admin
                                            }
                                            if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                                return false;
                                            }
                                            return true;
                                        })
                                        ->default(fn($record) => $record->pcDetails->commission_percentage ?? null)
                                        ->rules(function (Get $get) {
                                            $rules = ['required', 'numeric', 'min:0'];
                                            $minPercentage = GlobalSettings::where('name', 'commission_percentage')->value('value');
                                            $minFlat = GlobalSettings::where('name', 'commission_flat')->value('value');
                                            if ($get('commission_type') == 'percentage') {
                                                if ($minPercentage !== null) {
                                                    $rules[] = 'min:' . $minPercentage;
                                                } else {
                                                    // Default max percentage if not set in global settings
                                                    $rules[] = 'max:100';
                                                }
                                            } else {
                                                if ($minFlat !== null) {
                                                    $rules[] = 'min:' . $minFlat;
                                                }
                                            }

                                            return $rules;
                                        })->helperText(function (Get $get) {
                                            if ($get('commission_type') == 'percentage') {
                                                $minPercentage = GlobalSettings::where('name', 'commission_percentage')
                                                    ->value('value');
                                                return $minPercentage !== null ? "Minimum allowed percentage: {$minPercentage}%" : "Minimum allowed percentage: 100%";
                                            } else {
                                                $minFlat = GlobalSettings::where('name', 'commission_flat')
                                                    ->value('value');
                                                return $minFlat !== null ? "Minimum allowed flat value: {$minFlat}" : null;
                                            }
                                        }),
                                ]),

                            // Payout Option Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('commission_payout_option')
                                        ->label('Select Option for Payout')
                                        ->options([
                                            'schedule' => 'Scheduled Payout (This would be the payout after the commission deduction)',
                                            'full' => 'Full Payout (This would be the full amount paid to the Pharmaceutical Supplier, the commission would be invoiced later)',
                                        ])
                                        ->required()
                                        ->default(fn($record) => $record->pcDetails->commission_payout_option ?? null)
                                        ->reactive(),
                                    Radio::make('cycle_type')
                                        ->label('Scheduled Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select schedule cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })->helperText('Select how often the scheduled payout should occur.'),

                                    // Conditional sub-option for Full Payout
                                    Radio::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'online_manual' => 'Online Through Link + Manual',
                                            'manual' => 'Manual',
                                        ])
                                        ->validationMessages([
                                            'required' => 'Please select payment method.',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->payment_method ?? null)
                                        ->visible(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))
                                        ->required(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))->reactive(),

                                    Radio::make('cycle_type')
                                        ->label('Full Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select Full Payout cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })->helperText('Select how often the full payout should occur (for Online + Manual).'),
                                ]),

                            // Access Type Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('is_restricted')
                                        ->label('Access Type')
                                        ->options([
                                            false => 'Global',
                                            true => 'Restricted',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->is_restricted ?? false)
                                        ->required(),
                                ]),

                        ])
                        ->columns(1),
                ]),

            Actions\Action::make('mark_as_non_restricted')
                ->visible(function (User $record) {
                    return $record->pcDetails?->is_restricted;
                })
                ->action(function (User $record) {
                    $record->pcDetails()->update(['is_restricted' => false]);
                    $companyName = $record->pcDetails?->business_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'attributes' => [
                                'is_restricted' => false,
                            ]
                        ])
                        ->log("Pharmaceutical Supplier ({$companyName}) has been marked as Non Restricted");
                })
                ->requiresConfirmation()
                ->button()
                ->color('gray')
                ->outlined(),
            Actions\Action::make('approve')
                ->visible(function (User $record) {
                    return ($record->verification_status == null || $record->verification_status == 'pending') && $record->pcDetails->is_submitted == true;
                })
                ->form([
                    Section::make()
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('commission_type')
                                        ->label(new HtmlString('Commission Type <sapn style="color:red">*</span>'))
                                        ->validationAttribute('commission type')
                                        ->placeholder('Select commission type')
                                        ->options([
                                            'percentage' => 'Percentage',
                                            'flat' => 'Flat',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->commission_type ?? null)->reactive()
                                        ->rules(['required']),

                                    TextInput::make('commission_percentage')
                                        ->label(new HtmlString('Commission <sapn style="color:red">*</span>'))
                                        ->validationAttribute('commission percentage')
                                        ->placeholder('Enter commission')
                                        ->default(fn($record) => $record->pcDetails->commission_percentage ?? null)
                                        ->rules(function (Get $get) {
                                            $rules = ['required', 'numeric', 'min:0'];
                                            $minPercentage = GlobalSettings::where('name', 'commission_percentage')->value('value');
                                            $minFlat = GlobalSettings::where('name', 'commission_flat')->value('value');

                                            if ($get('commission_type') == 'percentage') {
                                                if ($minPercentage !== null) {
                                                    $rules[] = 'min:' . $minPercentage;
                                                } else {
                                                    $rules[] = 'max:100';
                                                }
                                            } else {
                                                if ($minFlat !== null) {
                                                    $rules[] = 'min:' . $minFlat;
                                                }
                                            }

                                            return $rules;
                                        })->helperText(function (Get $get) {
                                            if ($get('commission_type') == 'percentage') {
                                                $minPercentage = GlobalSettings::where('name', 'commission_percentage')
                                                    ->value('value');
                                                return $minPercentage !== null ? "Minimum allowed percentage: {$minPercentage}%" : "Minimum allowed percentage: 100%";
                                            } else {
                                                $minFlat = GlobalSettings::where('name', 'commission_flat')
                                                    ->value('value');
                                                return $minFlat !== null ? "Minimum allowed flat value: {$minFlat}" : null;
                                            }
                                        }),
                                ]),

                            // Payout Option Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('commission_payout_option')
                                        ->label('Select Option for Payout')
                                        ->options([
                                            'schedule' => 'Scheduled Payout (This would be the payout after the commission deduction)',
                                            'full' => 'Full Payout (This would be the full amount paid to the Pharmaceutical Supplier, the commission would be invoiced later)',
                                        ])
                                        ->required()
                                        ->default(fn($record) => $record->pcDetails->commission_payout_option ?? null)
                                        ->reactive(),
                                    Radio::make('cycle_type')
                                        ->label('Scheduled Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select schedule cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })->helperText('Select how often the scheduled payout should occur.'),

                                    // Conditional sub-option for Full Payout
                                    Radio::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'online_manual' => 'Online Through Link + Manual',
                                            'manual' => 'Manual',
                                        ])
                                        ->validationMessages([
                                            'required' => 'Please select payment method.',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->payment_method ?? null)
                                        ->visible(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))
                                        ->required(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))->reactive(),

                                    Radio::make('cycle_type')
                                        ->label('Full Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select Full Payout cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })->helperText('Select how often the full payout should occur (for Online + Manual).'),
                                ]),

                            // Access Type Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('is_restricted')
                                        ->label('Access Type')
                                        ->options([
                                            false => 'Global',
                                            true => 'Restricted',
                                        ])
                                        ->default(fn($record) => $record->pcDetails->is_restricted ?? false)
                                        ->required(),
                                ]),

                        ])
                        ->columns(1),
                ])
                ->action(function (User $record, array $data, $action) {
                    \Mail::to($record->email)->send(new \App\Mail\PcApproveMail($record));
                    if (
                        isset($data['commission_payout_option']) && $data['commission_payout_option'] === 'full' &&
                        (!isset($data['payment_method']) || $data['payment_method'] !== 'online_manual')
                    ) {
                        $data['cycle_type'] = 'monthly';
                    }
                    $record->pcDetails->update([
                        'step' => 5,
                        'commission_type' => $data['commission_type'],
                        'commission_percentage' => $data['commission_percentage'],
                        'commission_payout_option' => $data['commission_payout_option'],
                        'payment_method' => $data['payment_method'] ?? null,
                        'cycle_type' => $data['cycle_type'] ?? null,
                        'is_restricted' => $data['is_restricted'],
                    ]);
                    $record->update([
                        'is_admin_verified' => true,
                        'admin_verified_by' => Auth::id(),
                        'admin_verified_on' => now(),
                        'verification_status' => 'approved'
                    ]);

                    // Activity log for commission approval
                    $companyName = $record->pcDetails?->company_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'verification_status' => 'pending',
                            ],
                            'attributes' => [
                                'commission_type' => $data['commission_type'] ?? null,
                                'commission_percentage' => $data['commission_percentage'] ?? null,
                                'commission_payout_option' => $data['commission_payout_option'] ?? null,
                                'payment_method' => isset($data['payment_method'])
                                    ? ($data['payment_method'] === 'online_manual'
                                        ? 'Online Through Link + Manual'
                                        : ($data['payment_method'] === 'manual'
                                            ? 'Manual'
                                            : $data['payment_method']))
                                    : null,
                                'cycle_type' => isset($data['cycle_type'])
                                    ? ($data['cycle_type'] === 'bi_weekly'
                                        ? '15 Days'
                                        : ($data['cycle_type'] === 'monthly'
                                            ? 'Monthly'
                                            : $data['cycle_type']))
                                    : null,
                                'is_restricted' => isset($data['is_restricted'])
                                    ? ($data['is_restricted'] ? 'Restricted' : 'Global')
                                    : null,
                                'verification_status' => 'approved'
                            ]
                        ])
                        ->log("Pharmaceutical Supplier Configuration has been updated for ({$companyName})");

                    $action->redirect(UserResource::getUrl('index'));
                })
                ->after(function () {
                    Notification::make()
                        ->success()
                        ->title('User approved successfully')
                        ->send();
                })
                ->color('green'),
            Actions\Action::make('reject')
                ->form([
                    Textarea::make('reason')
                        ->required()
                ])
                ->visible(function (User $record) {
                    return ($record->verification_status == null || $record->verification_status == 'pending') && $record->pcDetails->is_submitted == true;
                })
                ->requiresConfirmation()
                ->action(function (User $record, $data) {

                    $record->update([
                        'is_admin_verified' => false,
                        'admin_verified_by' => Auth::id(),
                        'admin_verified_on' => now(),
                        'verification_status' => 'rejected',
                        'rejection_reason' => $data['reason']
                    ]);

                    // Activity log for rejection with reason
                    $companyName = $record->pcDetails?->company_name ?? 'N/A';
                    activity()
                        ->causedBy(Auth::user())
                        ->useLog('users')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'verification_status' => 'pending',
                            ],
                            'attributes' => [
                                'rejection_reason' => $data['reason'] ?? null,
                                'verification_status' => 'rejected'
                            ]
                        ])
                        ->log("Pharmaceutical Supplier ({$companyName}) has been rejected by Admin");

                    Mail::to($record->email)->send(new RejectPCMail($record));
                    Notification::make()
                        ->success()
                        ->title('User rejected successfully')
                        ->send();
                })
                ->color('danger'),
            Actions\Action::make('add_product')
                ->label('+ Add Product')

                ->visible(function (User $record, $livewire) {
                    return $record->verification_status == 'approved' && static::canCreate($record);
                })
                ->action(function (User $record, $data) {
                    return redirect()->route('filament.admin.resources.products.create', ['user_id' => $record->id]);
                }),

            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserResource::getUrl('index', ['activeTab' => session('user_resource_active_tab')])),
        ];
    }

    // public function updateFixedPrice()
    // {
    //     dd('here');
    // }
}
