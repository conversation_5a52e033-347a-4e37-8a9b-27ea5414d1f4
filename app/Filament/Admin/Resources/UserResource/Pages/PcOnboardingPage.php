<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use App\Models\User;
use Filament\Forms\Get;
use Filament\Forms\Form;
use App\Models\WareHouse;
use Filament\Actions\Action;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Illuminate\Validation\Rule;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Wizard;
use Illuminate\Validation\Rules\File;
use Filament\Forms\Components\Textarea;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Wizard\Step;
use App\Service\StoreProfileByAdminService;
use App\Filament\Admin\Resources\UserResource;
use App\Forms\Components\PhoneWithPrefix;
use App\Mail\RejectPCMail;
use App\Models\PcCertificateFile;
use App\Models\PcCompanyType;
use App\Models\PcDetail;
use App\Models\ZipCode;
use App\Rules\PhoneWithPrefixRule;
use AWS\CRT\Log;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log as FacadesLog;
use Indianic\Settings\Models\GlobalSettings;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class PcOnboardingPage extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected ?string $heading = 'Pharmaceutical Company Onboarding';

    protected static string $view = 'filament.admin.resources.user-resource.pages.pc-onboarding-page';

    public ?array $data = [];

    public User $user;

    public ?WareHouse $wareHouse;

    public $business_name = '';

    public $company_name = '';

    public $company_registration_number = '';

    public $tin_number = '';

    public $sstc_number = '';

    public $phone_number = '';
    public $land_line_number = '';

    public $email = '';

    public $address_1 = '';

    public $address_2 = '';

    public $city_id = '';

    public $state_id = '';

    public $district = '';

    public $profile_email = '';

    public $ware_phone_number = '';

    public $ware_postal_code = '';

    public $ware_address_1 = '';

    public $ware_address_2 = '';

    public $ware_district = '';

    public $ware_state_id = '';

    public $ware_city_id = '';

    public $warehouse_type = '';

    public $ware_id = '';

    public $person_in_charge_name = '';

    public $person_in_charge_nric = '';

    public $person_in_charge_phone = '';

    public $person_in_charge_email = '';

    public $person_in_charge_landline = '';

    public $company_registration_certificate = null;

    public $license_permit = null;

    public $user_id = '';

    public $reason = '';

    public $postal_code = '';

    public $web_url = '';

    public $phone_code = '';

    public $land_line_code = '';

    public $delivery_days = '';

    public $delivery_days_west = '';

    public $min_order_value = '';


    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Pharmaceutical suppliers",
            3 => "Edit",
        ];
    }

    public function mount(int|string $record): void
    {
        $this->user = User::find($record);
        $this->wareHouse = $this->user->warehouses()?->first();
        $contactDetails = $this->user->addresses->where('is_onboarding', true)->first();

        $certificateFiles = PcCertificateFile::where('user_id', $this->user->id)->where('status', 'active')->get()->groupBy('type');
        $companyRegistrationFiles = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
        $companyRegistrationFiles = getFileUrl($companyRegistrationFiles, $this->user);

        $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();
        $licensePermitFiles = getFileUrl($licensePermitFiles, $this->user);
        $this->form->fill([
            'business_name' => $this->user->pcDetails?->business_name,
            'company_name' => $this->user->pcDetails?->company_name,
            'company_registration_number' => $this->user->pcDetails?->company_registration_number,
            'tin_number' => $this->user->pcDetails?->tin_number,
            'sstc_number' => $this->user->pcDetails?->sstc_number,
            'phone_number' => $this->user->pcDetails?->phone_number,
            "phone_code" => $this->user->pcDetails?->phone_number_code,
            'company_type_id' => $this->user->pcDetails?->company_type_id,
            'profile_email' => $this->user->pcDetails?->profile_email,
            'postal_code' =>  $this->user->pcDetails?->postal_code,
            'web_url' =>  $this->user->pcDetails?->web_url,
            'user_email' => $this->user->email,
            'is_credit_line' =>  $this->user->pcDetails?->is_credit_line,

            'address_1' => $contactDetails?->address_1,
            'address_2' => $contactDetails?->address_2,
            'district' => $contactDetails?->district,
            'state_id' => $contactDetails?->state_id,
            'city_id' => $contactDetails?->city_id,
            'landline_number' => $contactDetails?->landline_number,
            'landline_code' => $contactDetails?->landline_code,
            'postal_code' =>  $contactDetails?->postal_code,
            'web_url' =>  $this->user->pcDetails?->web_url,
            'region' =>  $contactDetails?->region,

            'ware_phone_number' => $this->wareHouse?->phone_number,
            'warehouse_type' => $this->wareHouse?->warehouse_type,
            'ware_postal_code' => $this->wareHouse?->postal_code,
            'ware_address_1' => $this->wareHouse?->address_1,
            'ware_address_2' => $this->wareHouse?->address_2,
            'ware_district' => $this->wareHouse?->district,
            'ware_state_id' => $this->wareHouse?->state_id,
            'ware_city_id' => $this->wareHouse?->city_id,
            'ware_region' => $this->wareHouse?->ware_region,
            'ware_id' => $this->wareHouse?->id,

            'person_in_charge_name' => $this->user->pcDetails?->person_in_charge_name,
            'person_in_charge_nric' => $this->user->pcDetails?->person_in_charge_nric,
            'person_in_charge_phone' => $this->user->pcDetails?->person_in_charge_phone,
            'person_in_charge_email' => $this->user->pcDetails?->person_in_charge_email,
            'person_in_charge_landline' => $this->user->pcDetails?->person_in_charge_landline,


            'delivery_days' => $this->user->pcDetails?->delivery_days,
            'delivery_days_west' => $this->user->pcDetails?->delivery_days_west,
            'min_order_value' => $this->user->pcDetails?->min_order_value,
            ...$this->user->toArray(),

            'company_registration_certificate' => $companyRegistrationFiles, //$this->user->pcDetails?->company_registration_certificate,
            'license_permit' => $licensePermitFiles, //$this->user->pcDetails?->license_permit,

            'photo' => $this->user->photo ? ['users/' . $this->user->photo] : null,
            'beneficiary_name' => $this->user->pcDetails?->beneficiary_name,
            'bank_name' => $this->user->pcDetails?->bank_name,
            'account_number' => $this->user->pcDetails?->account_number,
            // 'reson' => $this->user->rejection_reason,
        ]);
        $this->company_registration_certificate = $companyRegistrationFiles[0] ?? null; //$this->user->pcDetails?->company_registration_certificate;
        $this->license_permit = $licensePermitFiles[0] ?? null; //$this->user->pcDetails?->whereNotNull('step')->first()?->license_permit;
        $this->reason = $this->user->rejection_reason;
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('approve')
                ->label('Approve')
                ->color('success')
                ->visible(function ($record) {
                    return ($record->verification_status == null || $record->verification_status == 'pending') && $record?->pcDetails?->is_submitted == true;
                })
                // ->requiresConfirmation()
                ->modalSubmitActionLabel('Approve')
                ->modalDescription('Are you sure you want to approve this Pharmaceutical Company?')
                ->form([
                    Section::make()
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('commission_type')
                                        ->label(new HtmlString('Commission Type <span style="color:red">*</span>'))
                                        ->validationAttribute('commission type')
                                        ->placeholder('Select commission type')
                                        ->options([
                                            'percentage' => 'Percentage',
                                            'flat' => 'Flat',
                                        ])
                                        ->default(fn ($record) => $record->pcDetails->commission_type ?? null)->reactive()
                                        ->rules(['required']),

                                    TextInput::make('commission_percentage')
                                        ->label(new HtmlString('Commission <span style="color:red">*</span>'))
                                        ->validationAttribute('commission percentage')
                                        ->placeholder('Enter commission')
                                        ->default(fn ($record) => $record->pcDetails->commission_percentage ?? null)
                                        ->rules(function (Get $get) {
                                            $rules = ['required', 'numeric', 'min:0'];
                                            $minPercentage = GlobalSettings::where('name', 'commission_percentage')->value('value');
                                            $minFlat = GlobalSettings::where('name', 'commission_flat')->value('value');

                                            if ($get('commission_type') == 'percentage') {
                                                if ($minPercentage !== null) {
                                                    $rules[] = 'min:' . $minPercentage;
                                                } else {
                                                    // Default max percentage if not set in global settings
                                                    $rules[] = 'max:100';
                                                }
                                            } else {

                                                if ($minFlat !== null) {
                                                    $rules[] = 'min:' . $minFlat;
                                                }
                                            }

                                            return $rules;
                                        })->helperText(function (Get $get) {
                                            if ($get('commission_type') == 'percentage') {
                                                $minPercentage = GlobalSettings::where('name', 'commission_percentage')
                                                    ->value('value');
                                                return $minPercentage !== null ? "Minimum allowed percentage: {$minPercentage}%" : "Minimum allowed percentage: 100%";
                                            } else {
                                                $minFlat = GlobalSettings::where('name', 'commission_flat')
                                                    ->value('value');
                                                return $minFlat !== null ? "Minimum allowed flat value: {$minFlat}" : null;
                                            }
                                        }),
                                ]),

                            // Payout Option Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('commission_payout_option')
                                        ->label('Select Option for Payout')
                                        ->options([
                                            'schedule' => 'Scheduled Payout (This would be the payout after the commission deduction)',
                                            'full' => 'Full Payout (This would be the full amount paid to the Pharmaceutical Supplier, the commission would be invoiced later)',
                                        ])
                                        ->required()
                                        ->default(fn ($record) => $record->pcDetails->commission_payout_option ?? null)
                                        ->reactive(),
                                    Radio::make('cycle_type')
                                        ->label('Scheduled Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn ($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select schedule cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'schedule';
                                        })->helperText('Select how often the scheduled payout should occur.'),

                                    // Conditional sub-option for Full Payout
                                    Radio::make('payment_method')
                                        ->label('Payment Method')
                                        ->options([
                                            'online_manual' => 'Online Through Link + Manual',
                                            'manual' => 'Manual',
                                        ])
                                        ->validationMessages([
                                            'required' => 'Please select payment method.',
                                        ])
                                        ->default(fn ($record) => $record->pcDetails->payment_method ?? null)
                                        ->visible(fn ($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))
                                        ->required(fn ($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))->reactive(),

                                    Radio::make('cycle_type')
                                        ->label('Full Payout Cycle')
                                        ->options([
                                            'bi_weekly' => '15 Days',
                                            'monthly' => 'Monthly',
                                        ])
                                        ->default(fn ($record) => $record->pcDetails->cycle_type ?? null)
                                        ->visible(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })
                                        ->validationMessages([
                                            'required' => 'Please select Full Payout cycle type.',

                                        ])
                                        ->required(function ($get) {
                                            return $get('commission_payout_option') === 'full'
                                                && $get('payment_method') === 'online_manual';
                                        })->helperText('Select how often the full payout should occur (for Online + Manual).'),
                                ]),

                            // Access Type Section
                            Grid::make(1)
                                ->schema([
                                    Radio::make('is_restricted')
                                        ->label('Access Type')
                                        ->options([
                                            false => 'Global',
                                            true => 'Restricted',
                                        ])
                                        ->default(fn ($record) => $record->pcDetails->is_restricted ?? false)
                                        ->required(),
                                ]),

                        ])
                        ->columns(1),
                ])
                ->action(function (User $record, array $data, $action) {

                    $record->pcDetails->update([
                        'step' => 5,
                        'commission_type' => $data['commission_type'],
                        'commission_percentage' => $data['commission_percentage'],
                        'commission_payout_option' => $data['commission_payout_option'],
                        'cycle_type' => $data['cycle_type'] ?? null,
                        'payment_method' => $data['payment_method'] ?? null,
                        'is_restricted' => $data['is_restricted'],
                    ]);
                    $record->update(['is_admin_verified' => true, 'admin_verified_by' => auth()->user()->id, 'admin_verified_on' => now(), 'verification_status' => 'approved']);
                    \Mail::to($record->email)->send(new \App\Mail\PcApproveMail($record));
                    $action->redirect(UserResource::getUrl('index'));
                }),

            Action::make('reject')
                // ->label('Reject')
                ->color('danger')
                ->requiresConfirmation()
                ->modalSubmitActionLabel('Reject')
                ->modalDescription('Are you sure you want to reject this Pharmaceutical Company?')
                ->form([
                    Textarea::make('reason')->required(),
                ])
                ->visible(function ($record) {
                    // return $record->verification_status == 'approved' || $record->verification_status == null || $record->verification_status == 'pending';
                    return ($record->verification_status == null || $record->verification_status == 'pending') && $record?->pcDetails?->is_submitted == true;
                })
                ->action(function ($record, array $data) {
                    Mail::to($record->email)->send(new RejectPCMail($record));
                    $record->update(['is_admin_verified' => false, 'admin_verified_by' => auth()->user()->id, 'rejection_reason' => $data['reason'], 'verification_status' => 'rejected']);
                    Notification::make()
                        ->success()
                        ->title('User rejected successfully')
                        ->send();
                }),

            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserResource::getUrl('index')),

        ];
    }

    public function getViewData(): array
    {
        return [
            'actions' => $this->getHeaderActions(),
        ];
    }

    public function getRecord(): Model
    {
        return User::find($this->record) ?? abort(404);
    }

    public function form(Form $form): Form
    {

        return $form->schema([
            Wizard::make()
                ->startOnStep(function () {
                    if (request()->getUri() == UserResource::getUrl('edit', ['record' => $this->record])) {
                        return ($this->user->pcDetails?->step < 5) ? $this->user->pcDetails?->step + 2 : $this->user->pcDetails?->step + 1;
                    } elseif (request()->headers->get('referer') == UserResource::getUrl('create')) {
                        return 2;
                    } elseif (request()->headers->get('referer') == UserResource::getUrl('index')) {
                        return 1;
                    } elseif (!isset($this->user->pcDetails?->step)) {
                        return 2;
                    } else {
                        return ($this->user->pcDetails?->step < 5) ? $this->user->pcDetails?->step + 2 : $this->user->pcDetails?->step;
                    }
                })
                ->previousAction(function ($action) {
                    return $action->label('Back')->color('create_button');
                })
                ->nextAction(function ($action) {
                    return $action->label('Save & Continue')->color('create_button');
                })
                ->schema([
                    Step::make('User Creation')
                        ->icon('heroicon-o-user-plus')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('name')
                                    ->label('Company Name')
                                    ->placeholder('Enter your name')
                                    ->rules(['required', 'max:100', 'string'])
                                    ->required()
                                    ->visible(fn ($get) => $get('company_type_id') != 1),
                                TextInput::make('business_name')
                                    ->label('Business Name')
                                    ->placeholder('Enter Business Name')
                                    ->rules(['required', 'max:100', 'string'])
                                    ->required()
                                    ->visible(fn ($get) => $get('company_type_id') == 1),
                                TextInput::make('email')
                                    ->placeholder('Enter your email')
                                    ->readOnly()
                                    ->regex('/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'),
                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, Set $set, StoreProfileByAdminService $service) {
                            $user = User::updateOrCreate(['name' => $get('name')]);
                            // return redirect()->to(self::getUrl('edit',['record'=>$user]));
                            // $data = [];
                            // $data['email'] = $get('email');
                            // $data['user_id'] = $get('user_id');
                            // $service->sendCredential($data);
                        }),
                    Step::make('Supplier Information')
                        ->icon('phosphor-building')
                        ->schema([
                            Group::make()
                                ->schema([
                                    FileUpload::make('photo')
                                        ->label('Company Logo')
                                        ->image()
                                        ->avatar()
                                        ->directory('users')
                                        ->rules(['nullable', File::types(['jpeg', 'png', 'jpg'])->max(2 * 1024)])
                                        ->helperText('Supported formats: JPEG, JPG, PNG (Max 2MB)')
                                        ->default($this->user->photo ? Storage::disk('s3')->url('users/' . $this->user->photo) : asset('/images/user-avatar.png')),


                                    // ->columnSpanFull()
                                ])
                                ->columns(1),
                            Group::make()->schema([
                                TextInput::make('business_name')
                                    ->placeholder('Enter Business Name')
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', Rule::unique('pc_details', 'business_name')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', 'unique:pc_details,business_name'];
                                    })->validationMessages([
                                        'required' => 'The Business Name field is required.',
                                        'string' => 'The Business Name must be a string.',
                                        'regex' => 'The Business Name must be a alphabetical.',
                                        'max' => 'The Business Name may not be greater than 64 characters.',
                                    ])
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'success' : null)
                                    ->live()
                                    ->required(),
                                Select::make('company_type_id')
                                    ->placeholder('Select company type')
                                    ->validationAttribute('Company Type')
                                    ->required()
                                    ->label(new HtmlString(
                                        'Company Type <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Select the type of company that best describes your business.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->options(fn () => PcCompanyType::pluck('name', 'id'))
                                    ->reactive(), // <-- Make dropdown reactive so dependent fields update on change
                                TextInput::make('name')
                                    ->placeholder('Enter company name')
                                    ->label('Company Name')
                                    ->readOnly(true),
                                TextInput::make('company_registration_number')
                                    ->live()
                                    ->reactive() // <-- Make this field reactive so it updates on dropdown change
                                    ->suffixIcon(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->placeholder('Enter the company registration number')
                                    ->label(function (Get $get) {
                                        $label = 'Company Registration Number';
                                        $tooltip = '<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>';
                                        return new HtmlString($label . $tooltip);
                                    })
                                    ->rules(function (Get $get) {
                                        // Dynamically set required based on company_type_id
                                        $rules = ['regex:/^[a-zA-Z0-9]+$/', 'max:20', 'min:2'];
                                        if (!empty($this->user->id)) {
                                            $rules[] = Rule::unique('pc_details', 'company_registration_number')->ignore($this->user->id, 'user_id');
                                        } else {
                                            $rules[] = 'unique:pc_details,company_registration_number';
                                        }
                                        // If company_type_id is not 1, make required
                                        if ($get('company_type_id') != 1) {
                                            array_unshift($rules, 'required');
                                        }
                                        return $rules;
                                    })
                                    ->validationMessages([
                                        'required' => 'The Company Registration Number field is required.',
                                        'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                        'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                        'min' => 'The Company Registration Number must be at least 2 characters.',
                                        'unique' => 'The Company Registration Number has already been taken.',
                                    ])
                                    // Required dynamically updates on dropdown change
                                    ->required(fn (Get $get) => $get('company_type_id') != 1),
                                TextInput::make('tin_number')
                                    ->placeholder('Enter the TIN number')
                                    ->live()
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)

                                    ->label(new HtmlString(
                                        'TIN Number <span style="color:red;font-size: 11px !important;">*</span> <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Tax Identification Number (TIN) is a unique identifier assigned by the tax authority for businesses or individuals to track tax obligations.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['required', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', Rule::unique('pc_details', 'tin_number')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['required', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', 'unique:pc_details,tin_number'];
                                    })
                                    ->validationMessages([
                                        'max' => 'The TIN number may not be greater than 20 characters.',
                                        'unique' => 'The TIN number has already been taken.',
                                        'regex' => 'The TIN number must be an alphanumeric string.',
                                        'min' => 'The TIN number must be at least 1 character.',
                                        'required' => 'The TIN number field is required.',
                                    ]),
                                TextInput::make('sstc_number')
                                    ->placeholder('Enter the SST number')
                                    ->live()
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->label(new HtmlString(
                                        'SST Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sales and Services Tax (SST) Registration Number is a unique code issued to businesses in Malaysia registered for SST compliance.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', Rule::unique('pc_details', 'sstc_number')->ignore($this->user->id, 'user_id')];
                                        }
                                        return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', 'unique:pc_details,sstc_number'];
                                    })
                                    ->validationMessages([
                                        'max' => 'The SST number may not be greater than 20 characters.',
                                        'unique' => 'The SST number has already been taken.',
                                        'regex' => 'The SST number must be an alphanumeric string.',
                                        'min' => 'The SST number must be at least 1 character.',
                                    ]),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileByAdminService $service, $record) {
                            $data = [];
                            $data['business_name'] = $get('business_name');
                            $data['company_name'] = $get('name');
                            $data['company_registration_number'] = $get('company_registration_number');
                            $data['tin_number'] = $get('tin_number');
                            $data['sstc_number'] = $get('sstc_number');
                            $data['company_type_id'] = $get('company_type_id');

                            $data['user_id'] = $record->id;
                            $uploadedFile = $get('photo');
                            $uploadedFile['user_id'] = $record->id;
                            uploadUserImage($uploadedFile);
                            $service->storeCompanyInformations($data, 1);
                        }),
                    Step::make('Contact Details')
                        ->icon('heroicon-o-phone')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('user_email')
                                    ->label('Email')
                                    ->default($this->user->email)
                                    ->readOnly(),
                                TextInput::make('address_1')
                                    ->placeholder('Enter address 1')
                                    ->rules(['required', 'string', 'max:100'])
                                    ->required(),
                                TextInput::make('address_2')
                                    ->placeholder('Enter address 2')
                                    ->rules(['nullable', 'string', 'max:100']),
                                // TextInput::make('district')
                                //     ->rules(['required', 'string', 'max:255'])
                                //     ->required(),
                                Select::make('state_id')
                                    ->label('State')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('country_id', 132)
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if (empty($state)) {
                                            $set('region', null);
                                            $set('city_id', null);
                                            $set('postal_code', null);
                                            return;
                                        }
                                        $info = State::where('id', $get('state_id'))->first();
                                        if ($info) {
                                            $set('region', ucfirst($info->zone));
                                        }
                                        $set('city_id', null);
                                    })
                                    ->required()->live(),
                                Select::make('city_id')
                                    ->label('City')
                                    ->placeholder('Select City')
                                    ->loadingMessage('Loading cities...')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('state_id', $get('state_id'))
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->live(onBlur: true)
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        $query = City::whereNotNull('landline_code')
                                            ->where('landline_code', '!=', '');
                                        $stateId = $get('state_id');
                                        $cityId = $get('city_id');
                                        if ($stateId && $cityId) {
                                            $query->where('state_id', $stateId)->where("id", $cityId);
                                        }
                                        $data = $query
                                            ->distinct('landline_code')
                                            ->pluck('landline_code', 'landline_code')
                                            ->toArray();
                                        if (count($data) == 1) {
                                            $set('landline_number.prefix', array_key_first($data));
                                        }
                                        $set('postal_code', null);
                                    })

                                    ->options(function (Get $get) {
                                        if (! empty($get('state_id'))) {
                                            return City::where('state_id', $get('state_id'))->pluck('name', 'id')->toArray();
                                        }

                                        return [];
                                    })
                                    ->rules(['required'])
                                    ->required(),
                                TextInput::make('region')
                                    ->placeholder('Enter region')->disabled(),
                                TextInput::make('phone_number')
                                    ->placeholder('Enter mobile number')
                                    ->prefix('+60')
                                    ->label('Mobile Number')
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->rules(['required', 'digits_between:8,12'])
                                    ->live()
                                    ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->required(),

                                // PhoneWithPrefix::make('phone_number')
                                //     ->label("Phone")
                                //     ->extraAttributes([
                                //         'inputmode' => 'numeric',
                                //         'maxlength' => '10'
                                //     ])
                                //     ->prefixOptions(function ($get, $set) {
                                //         if(empty($get('phone_number'))) {
                                //             return [];
                                //         }
                                //         $query = City::whereNotNull('landline_code')
                                //             ->where('landline_code', '!=', '');
                                //         $stateId = $get('state_id');

                                //         if ($stateId) {
                                //             $query->where('state_id', $stateId);
                                //         }

                                //         $data = $query
                                //             ->distinct('landline_code')
                                //             ->pluck('landline_code', 'landline_code')
                                //             ->toArray();
                                //         if(empty($data)) {
                                //             $data = City::whereNotNull('landline_code')
                                //             ->where('landline_code', '!=', '')
                                //             ->distinct('landline_code')
                                //             ->pluck('landline_code', 'landline_code')
                                //             ->toArray();
                                //         }
                                //         if($get('pc_details')["phone_number_code"] == null) {
                                //             // $set('phone_number.prefix', $data[ array_key_first($data)] ?? '');
                                //         }else{
                                //             $set('phone_number.prefix', $get("pc_details")["phone_number_code"]);
                                //         }
                                //         return $data;
                                //     })
                                //     ->rules([new PhoneWithPrefixRule()])
                                //     ->afterStateUpdated(function (Get $get, Set $set) {
                                //         $set("phone_code", implode(" ", $get("phone_number")));
                                //     })->formatStateUsing(function ($get, $set, $state) {
                                //         $data = ['prefix' => '', 'number' => ''];
                                //         if($get("phone_number_code")) {
                                //             $data["prefix"] = $get("phone_number_code");
                                //         }
                                //         if($get("phone_number")) {
                                //             $data["number"] = $get("phone_number");
                                //         }
                                //         return is_array($state) ? $state : $data;
                                //     })
                                //     ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{9,10}$/', $state) ? 'heroicon-s-check-circle' : null)
                                //     ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{9,10}$/', $state) ? 'success' : null),
                                // TextInput::make('landline_number')->placeholder('Enter landline number')
                                //     ->mask('9-9999999')
                                //     ->prefix('+03')
                                //     ->stripCharacters(['-'])
                                //     ->extraAttributes([
                                //         'inputmode' => 'numeric',
                                //         'maxlength' => '8'
                                //     ])
                                //     ->rules(['nullable','digits_between:7,8'])
                                //     ->live()
                                //     ->suffixIcon(fn ($state) => in_array(strlen($state), [8, 9]) ? 'heroicon-s-check-circle' : null)
                                //     ->suffixIconColor(fn ($state) => in_array(strlen($state), [8, 9]) ? 'success' : null)
                                //         ->live(),
                                PhoneWithPrefix::make('landline_number')
                                    ->label("Landline Number")
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '8'
                                    ])
                                    ->prefixOptions(function ($get, $set) {
                                        if (empty($get('landline_number'))) {
                                            return [];
                                        }
                                        $query = City::whereNotNull('landline_code')
                                            ->where('landline_code', '!=', '');
                                        $stateId = $get('state_id');
                                        $cityId = $get('city_id');
                                        if ($stateId) {
                                            $query->where('state_id', $stateId);

                                            if ($cityId) {
                                                $query->where('id', $cityId);
                                            }
                                        }


                                        $data = $query
                                            ->distinct('landline_code')
                                            ->pluck('landline_code', 'landline_code')
                                            ->toArray();
                                        if (empty($data)) {
                                            $data = City::whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->distinct('landline_code')
                                                ->pluck('landline_code', 'landline_code')
                                                ->toArray();
                                        }
                                        if ($get("landline_number")["prefix"] === "") {
                                            $set('landline_number.prefix', $data[array_key_first($data)] ?? '');
                                        }
                                        return $data;
                                    })
                                    ->rules([new PhoneWithPrefixRule()])
                                    ->afterStateHydrated(function (Get $get, Set $set) {
                                        if (isset($get('addresses')[0]["landline_code"]) && $get('addresses')[0]["landline_code"] != null) {
                                            $set("landline_number.prefix", $get('addresses')[0]["landline_code"]);
                                            $set("landline_number.number", $get('addresses')[0]["landline_number"]);
                                        } else {
                                            $set("landline_number", ["prefix" => "", "number" => ""]);
                                        }
                                    })->formatStateUsing(function ($get, $set, $state) {
                                        $data = ['prefix' => '', 'number' => ''];
                                        if ($get("landline_code")) {
                                            $data["prefix"] = $get("landline_code");
                                        }
                                        if ($get("landline_number")) {
                                            $data["number"] = $get("landline_number");
                                        }
                                        return is_array($state) ? $state : $data;
                                    }),
                                Select::make('postal_code')->label('Postal Code')->placeholder('Select postal code')
                                    ->options(function (Get $get) {

                                        if (!empty($get('city_id'))) {

                                            return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                        }
                                        return [];
                                    })
                                    ->required()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        if ($get('city_id')) {
                                            return ZipCode::where('city_id', $get('city_id'))
                                                ->where('code', 'like', "%{$search}%")
                                                ->pluck('code', 'code')
                                                ->toArray();
                                        }
                                        return [];
                                    })
                                    ->live(onBlur: true)
                                    ->optionsLimit(100)
                                    ->loadingMessage('Loading postal code...')
                                    ->searchable(),
                                // TextInput::make('postal_code')
                                //     ->placeholder('Enter postal code')
                                //     ->required()
                                //     ->extraAttributes([
                                //         'inputmode' => 'numeric',
                                //         'pattern' => '[0-9]*',
                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                //     ])->live()
                                //     ->suffixIcon(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                //     ->suffixIconColor(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                //     ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/']),
                                TextInput::make('web_url')
                                    ->placeholder('Enter website url')
                                    ->nullable()->live()
                                    ->rules([
                                        'regex:/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/',
                                    ])
                                    ->helperText('Ex: https://www.example.com, http://example.com, www.example.com, example.com')
                                    ->suffixIcon(function ($state) {
                                        $isValid = $state
                                            && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                        if (!$isValid) {
                                            return null;
                                        }

                                        $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                        $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                        return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                            ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(function ($state) {
                                        $isValid = $state
                                            && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                        if (!$isValid) {
                                            return null;
                                        }

                                        $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                        $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                        return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                            ? 'success' : null;
                                    }),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileByAdminService $service, $record) {
                            $data = [];
                            $data['phone_number'] = $get('phone_number');
                            // $data['phone_number'] = $get('phone_number')["number"] ?? "";
                            // $data['landline_number'] = $get('landline_number');
                            $data['landline_number'] = $get('landline_number')["number"] ?? "";
                            $data['landline_code'] = $get('landline_number')["prefix"] ?? "";
                            $data['profile_email'] = $get('user_email');
                            $data['address_1'] = $get('address_1');
                            $data['address_2'] = $get('address_2');
                            $data['district'] = $get('district');
                            $data['state_id'] = $get('state_id');
                            $data['city_id'] = $get('city_id');
                            $data['postal_code'] = $get('postal_code');
                            $data['web_url'] = $get('web_url');
                            $data['region'] = $get('region');
                            $data['user_id'] = $record->id;
                            $data["phone_code"] = $get('phone_number')["prefix"] ?? '60';
                            $service->storeContactDetails($data, 2);
                        })->beforeValidation(function (Get $get, Set $set) {
                            $set("landline_number.prefix", $get("landline_number")["prefix"] ?? "60");
                            $set("landline_number.number", $get("landline_number")["number"] ?? "60");
                        }),
                    Step::make('Warehouse Addresses')
                        ->icon('heroicon-s-building-storefront')
                        ->schema([
                            Radio::make('warehouse_type')->options(
                                [
                                    'owned' => new \Illuminate\Support\HtmlString(
                                        'Own Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with your own logistics,Dpharma incurs no extra costs,and you handle product delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                    ),
                                    'dpharma' => new \Illuminate\Support\HtmlString(
                                        'DPharma Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with DPharma logistics,our delivery partner manages product pickup and delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                    ),
                                ]
                            )
                                ->rules(['required'])
                                ->inline()
                                ->label('')
                                ->required()
                                ->validationMessages([
                                    'required' => 'Warehouse Type is required',
                                ])
                                ->formatStateUsing(function ($state) {
                                    return $state ?? 'dpharma';
                                })->live()
                                ->default('dpharma'),
                            Checkbox::make('use_business_address')
                                ->label('Use contact address as warehouse address')
                                ->live()
                                ->default(function () {
                                    $userAddress = $this->user->addresses->first();
                                    if (!$userAddress) {
                                        return false;
                                    }

                                    return $userAddress->address_1 === $this->data['ware_address_1'] &&
                                        $userAddress->address_2 === ($this->data['ware_address_2'] ?? null) &&
                                        $userAddress->state_id == $this->data['ware_state_id'] &&
                                        $userAddress->city_id == $this->data['ware_city_id'] &&
                                        $userAddress->postal_code === $this->data['ware_postal_code'];
                                    $userAddress->region === $this->data['ware_region'];
                                })
                                // Automatically check/uncheck when address fields change
                                ->afterStateHydrated(function (Checkbox $component, Get $get) {
                                    $userAddress = $this->user->addresses->first();
                                    if (!$userAddress) {
                                        return;
                                    }

                                    $matches = $userAddress->address_1 === $get('ware_address_1') &&
                                        $userAddress->address_2 === $get('ware_address_2') &&
                                        $userAddress->state_id == $get('ware_state_id') &&
                                        $userAddress->city_id == $get('ware_city_id') &&
                                        $userAddress->postal_code === $get('ware_postal_code');
                                    $userAddress->region === $get('ware_region');

                                    $component->state($matches);
                                })
                                ->visible(fn (Get $get): bool => $get('warehouse_type') === 'dpharma')
                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                    if ($get('use_business_address')) {
                                        $userAddress = $this->user->addresses->first();
                                        if ($userAddress) {
                                            $set('ware_address_1', $userAddress->address_1);
                                            $set('ware_address_2', $userAddress->address_2);
                                            $set('ware_state_id', $userAddress->state_id);
                                            $set('ware_city_id', $userAddress->city_id);
                                            $set('ware_postal_code', $userAddress->postal_code);
                                            $set('ware_region', $userAddress->region);
                                        }
                                    } else {
                                        // Clear fields if unchecked
                                        $set('ware_address_1', null);
                                        $set('ware_address_2', null);
                                        $set('ware_state_id', null);
                                        $set('ware_city_id', null);
                                        $set('ware_postal_code', null);
                                        $set('ware_region', null);
                                    }
                                }),
                            Group::make()->schema([
                                TextInput::make('delivery_days')
                                    ->rules(['integer', 'min:1', 'max:7', 'required'])
                                    ->label(new HtmlString("ETA for East Malaysia  (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    ])
                                    ->validationMessages([
                                        'required' => 'The Delivery Days is required.',
                                        'min' => 'The Delivery Days must be at least 1.',
                                        'max' => 'The Delivery Days may not be greater than 7.',
                                    ]),
                                TextInput::make('delivery_days_west')
                                    ->rules(['integer', 'min:1', 'max:7', 'required'])
                                    ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    ])
                                    ->validationMessages([
                                        'required' => 'The Delivery Days is required.',
                                        'min' => 'The Delivery Days must be at least 1.',
                                        'max' => 'The Delivery Days may not be greater than 7.',
                                    ]),
                                TextInput::make('min_order_value')
                                    ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'min:1', 'max:100000', 'required'])
                                    ->label(new HtmlString("Minimum Order Value (Ringgit Malaysia) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9.]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "").replace(/(\..*)\./g, "$1")'
                                    ])
                                    ->validationAttribute('Minimum Order Value')
                                    ->validationMessages([
                                        'required' => 'The Minimum Order Value is required.',
                                        'min' => 'The Minimum Order Value must be at least 1.',
                                        'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                    ]),
                            ])->columns(2)->visible(function (Get $get) {
                                return $get('warehouse_type') === 'owned';
                            }),
                            Group::make()->schema([
                                TextInput::make('ware_address_1')
                                    ->placeholder('Enter address 1')
                                    ->rules(['required', 'string', 'max:100'])
                                    ->label('Address Line 1')
                                    ->required(),
                                TextInput::make('ware_address_2')
                                    ->placeholder('Enter address 2')
                                    ->rules(['nullable', 'string', 'max:100'])
                                    ->label('Address Line 2'),
                                Select::make('ware_state_id')
                                    ->label('State')
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('country_id', 132)
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if (empty($state)) {
                                            $set('ware_region', null);
                                            $set('ware_city_id', null);
                                            $set('ware_postal_code', null);
                                            return;
                                        }
                                        $info = State::where('id', $get('state_id'))->first();
                                        if ($info) {
                                            $set('ware_region', ucfirst($info->zone));
                                        }
                                        $set('ware_city_id', null);
                                    })
                                    ->required()->live(),
                                // Select::make('ware_state_id')
                                // ->rules(['required'])
                                // ->label('State')
                                // ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())->required()->live(),
                                Select::make('ware_city_id')
                                    ->rules(['required'])
                                    ->label('City')
                                    ->live(onBlur: true)
                                    ->searchable()
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                            ->where('state_id', $get('ware_state_id'))
                                            ->pluck('name', 'id')
                                            ->toArray();
                                    })
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        $set('ware_postal_code', null);
                                    })
                                    ->options(function (Get $get) {
                                        if (! empty($get('ware_state_id'))) {
                                            $cities =  City::where('state_id', $get('ware_state_id'))->pluck('name', 'id')->toArray();

                                            if ($this->ware_city_id && !array_key_exists($this->ware_city_id, $cities)) {
                                                $city = City::find($this->ware_city_id);
                                                if ($city) {
                                                    $cities[$city->id] = $city->name;
                                                }
                                            }

                                            return $cities;
                                        }

                                        return [];
                                    })->required(),

                                Select::make('ware_postal_code')->label('Postal Code')->placeholder('Select postal code')
                                    ->options(function (Get $get) {

                                        if (!empty($get('ware_city_id'))) {

                                            return ZipCode::where('city_id', $get('ware_city_id'))->pluck('code', 'code');
                                        }
                                        return [];
                                    })
                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                        if ($get('ware_city_id')) {
                                            return ZipCode::where('city_id', $get('ware_city_id'))
                                                ->where('code', 'like', "%{$search}%")
                                                ->pluck('code', 'code')
                                                ->toArray();
                                        }
                                        return [];
                                    })
                                    ->required()
                                    ->live(onBlur: true)
                                    ->optionsLimit(100)
                                    ->loadingMessage('Loading postal code...')
                                    ->searchable(),
                                // TextInput::make('ware_postal_code')
                                //     ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                //     ->label('Postal Code')
                                //     ->placeholder('Enter postcode')
                                //     ->live()
                                //     ->suffixIcon(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                //     ->suffixIconColor(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                //     ->extraAttributes([
                                //         'inputmode' => 'numeric',
                                //         'pattern' => '[0-9]*',
                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                //     ])
                                //     ->required(),

                                // TextInput::make('ware_district')
                                //     ->rules(['required', 'string', 'max:255'])
                                //     ->label('District')
                                //     ->required(),

                                TextInput::make('ware_id')
                                    ->visible(false),
                                TextInput::make('ware_region')->label('Region')->readOnly()->placeholder('Enter region'),
                                // TextInput::make('min_order_value')
                                //     ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'numeric', 'min:1', 'max:100000', 'required'])
                                //     ->label(new HtmlString("Minimum Order Value (Ringgit Malaysia) <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                                //     ->extraAttributes([
                                //         'inputmode' => 'numeric',
                                //         'pattern' => '[0-9.]*',
                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                //         'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "").replace(/(\..*)\./g, "$1")'
                                //     ])
                                //     ->validationAttribute('Minimum Order Value')
                                //     ->validationMessages([
                                //         'required' => 'The Minimum Order Value is required.',
                                //         'min' => 'The Minimum Order Value must be at least 1.',
                                //         'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                //     ]),
                            ])->columns(2)->visible(function (Get $get) {
                                return $get('warehouse_type') === 'dpharma';
                            }),
                        ])->afterValidation(function (Get $get, StoreProfileByAdminService $service, $record) {
                            $data = [];
                            $data['phone_number'] = $get('ware_phone_number');
                            $data['address_1'] = $get('ware_address_1');
                            $data['address_2'] = $get('ware_address_2');
                            $data['district'] = $get('ware_district');
                            $data['state_id'] = $get('ware_state_id');
                            $data['postal_code'] = $get('ware_postal_code');
                            $data['city_id'] = $get('ware_city_id');
                            $data['warehouse_type'] = $get('warehouse_type');
                            $data['ware_region'] = $get('ware_region');
                            $data['ware_id'] = $get('ware_id');
                            $data['user_id'] = $record->id;
                            // dd($get(''));
                            PcDetail::updateOrCreate(['user_id' => $record->id], [
                                'delivery_days' => $get('delivery_days'),
                                'delivery_days_west' => $get('delivery_days_west'),
                                'min_order_value' => $get('min_order_value'),
                            ]);
                            $service->storeWareHouse($data);
                        }),
                    Step::make('Person In Charge')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('person_in_charge_name')
                                    ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                    ->placeholder('Enter the full name')
                                    ->suffixIcon(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn (Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'success' : null)
                                    ->validationMessages([
                                        'regex' => 'The Full Name must be a alphabetical.',
                                        'max' => 'The Full Name may not be greater than 50 characters.',
                                    ])
                                    ->label('Full Name'),
                                TextInput::make('person_in_charge_nric')
                                    ->placeholder('Enter NRIC number')
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['nullable', 'max:50'];
                                        }
                                        return ['nullable', 'max:50'];
                                    })
                                    // ->extraAttributes([
                                    //     'inputmode' => 'numeric',
                                    //     'pattern' => '[0-9]*',
                                    //     'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "");',
                                    //     'onkeypress' => 'return event.charCode >= 48 && event.charCode <= 57',
                                    // ])
                                    ->live()
                                    ->suffixIcon(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                    ->label(new HtmlString(
                                        'NRIC / Passport Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`National Registration Identity Card (NRIC) Number is a unique personal identification number assigned to Malaysian citizens and permanent residents.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->validationMessages([
                                        'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                    ]),
                                TextInput::make('person_in_charge_phone')->placeholder('Enter mobile number')->prefix('+60')
                                    ->label('Mobile Number')
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->rules(['required', 'digits_between:8,12'])
                                    ->required()
                                    ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->required()
                                    ->live(),
                                TextInput::make('person_in_charge_email')
                                    ->placeholder('Enter email')
                                    ->required()
                                    ->rules(function (Get $get) {
                                        return [
                                            'required',
                                            'email',
                                            'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                            function ($attribute, $value, $fail) use ($get) {
                                                $exists = DB::table('pc_details')
                                                    ->where(function ($query) use ($value) {
                                                        $query->where('profile_email', $value)
                                                            ->orWhere('person_in_charge_email', $value);
                                                    })
                                                    ->where('user_id', '=', $get('user_id'))  // Ignore the current user
                                                    ->exists();
                                                //   dd($exists);
                                                if ($exists) {
                                                    $fail('The email has already been taken.');
                                                }
                                            },
                                        ];
                                    })
                                    ->label('Email')
                                    ->suffixIcon(function ($state) {
                                        return !empty($state) && filter_var($state, FILTER_VALIDATE_EMAIL) ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(fn ($state) => filter_var($state, FILTER_VALIDATE_EMAIL) ? 'success' : null)
                                    ->live(),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileByAdminService $service, $record) {
                            $data = [];
                            $data['person_in_charge_name'] = $get('person_in_charge_name');
                            $data['person_in_charge_nric'] = $get('person_in_charge_nric');
                            $data['person_in_charge_phone'] = $get('person_in_charge_phone');
                            $data['person_in_charge_email'] = $get('person_in_charge_email');
                            $data['user_id'] = $record->id;
                            $service->storePersonInCharge($data, 4);
                        }),
                    Step::make('Bank Details')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                Checkbox::make('is_credit_line')
                                    ->label(new HtmlString(
                                        'Enable Credit Line <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enable this option to allow the supplier to use a credit line.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->default(fn () => $this->user->pcDetails?->is_credit_line ?? false)
                                    ->columnSpanFull()
                                    ->reactive(),
                                TextInput::make('beneficiary_name')
                                    ->formatStateUsing(function ($state, $record) {
                                        $beneficiaryName = $record?->pcDetails?->beneficiary_name ?? null;
                                        if (!$beneficiaryName) {
                                            return '';
                                        }
                                        $name = decryptParam($beneficiaryName);
                                        if (!$name) {
                                            return '';
                                        }
                                        $len = strlen($name);
                                        if ($len <= 4) {
                                            return str_repeat('*', $len);
                                        }
                                        return str_repeat('*', $len - 4) . substr($name, -4);
                                    })
                                    ->disabled(fn ($record) => !empty($record?->pcDetails?->beneficiary_name)) // Disable if already filled
                                    ->dehydrated(fn ($record) => !empty($record?->pcDetails?->beneficiary_name)) // Skip validation if disabled
                                    ->label(new HtmlString('Beneficiary Name <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Enter the beneficiary name')
                                    ->rules(function (Get $get, $record) {
                                        // Only apply validation if field is empty (not already filled in db)
                                        if (!empty($record?->pcDetails?->beneficiary_name)) {
                                            return [];
                                        }
                                        return ['required', 'string', 'max:50', 'regex:/^[a-zA-Z\s]+$/'];
                                    })
                                    ->live()
                                    ->suffixIcon(
                                        fn (Get $get, $record = null) => (!empty($record?->pcDetails?->beneficiary_name) || preg_match('/^[a-zA-Z\s]+$/', $get('beneficiary_name')))
                                            ? 'heroicon-s-check-circle'
                                            : null
                                    )
                                    ->suffixIconColor(
                                        fn (Get $get, $record = null) => (!empty($record?->pcDetails?->beneficiary_name) || preg_match('/^[a-zA-Z\s]+$/', $get('beneficiary_name')))
                                            ? 'success'
                                            : null
                                    )
                                    ->extraAttributes([
                                        'style' => 'font-family: monospace;', // Ensures consistent character width
                                    ])
                                    ->validationMessages([
                                        'required' => 'The Beneficiary Name field is required.',
                                        'regex' => 'The Beneficiary Name must be alphabetical.',
                                        'max' => 'The Beneficiary Name may not be greater than 50 characters.',
                                    ]),
                                Select::make('bank_name')
                                    ->formatStateUsing(function ($state, $record) {
                                        $bankName = $record?->pcDetails?->bank_name ?? null;
                                        if (!$bankName) {
                                            return '';
                                        }
                                        $bank = decryptParam($bankName);
                                        if (!$bank) {
                                            return '';
                                        }
                                        $len = strlen($bank);
                                        if ($len <= 4) {
                                            return str_repeat('*', $len);
                                        }
                                        return str_repeat('*', $len - 4) . substr($bank, -4);
                                    })
                                    ->disabled(fn ($record) => !empty($record?->pcDetails?->bank_name)) // Disable if already filled
                                    ->dehydrated(fn ($record) => !empty($record?->pcDetails?->bank_name)) // Skip validation if disabled
                                    ->label(new HtmlString('Bank Name '))
                                    ->placeholder('Select bank')
                                    ->options(function () {
                                        return getBankNames();
                                    })
                                    ->required()
                                    ->searchable()
                                    ->rules(function (Get $get, $record) {
                                        // Only apply validation if field is empty (not already filled in db)
                                        if (!empty($record?->pcDetails?->bank_name)) {
                                            return [];
                                        }
                                        return ['required', 'string', 'max:50', 'regex:/^[a-zA-Z\s]+$/'];
                                    })
                                    ->extraAttributes([
                                        'style' => 'font-family: monospace;', // Ensures consistent character width
                                    ])
                                    ->validationMessages([
                                        'required' => 'The Bank Name field is required.',
                                    ]),
                                TextInput::make('account_number')
                                    ->formatStateUsing(function ($state, $record) {
                                        $acc = decryptParam($record?->pcDetails?->account_number ?? null);
                                        if (!$acc) {
                                            return '';
                                        }
                                        $len = strlen($acc);
                                        if ($len <= 4) {
                                            return str_repeat('*', $len);
                                        }
                                        return str_repeat('*', $len - 4) . substr($acc, -4);
                                    })
                                    ->disabled(fn ($record) => !empty($record?->pcDetails?->account_number)) // Disable if already filled
                                    ->dehydrated(fn ($record) => !empty($record?->pcDetails?->account_number)) // Skip validation if disabled
                                    ->label(new HtmlString('Account Number <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                    ->placeholder('Enter the account number')
                                    ->rules(function (Get $get, $record) {
                                        // Only apply validation if field is empty (not already filled in db)
                                        if (!empty($record?->pcDetails?->account_number)) {
                                            return [];
                                        }
                                        return ['required', 'string', 'max:30', 'regex:/^[0-9]+$/'];
                                    })
                                    ->live()
                                    ->suffixIcon(
                                        fn (Get $get, $record = null) => (!empty($record?->pcDetails?->account_number) || preg_match('/^[0-9]+$/', $get('account_number')))
                                            ? 'heroicon-s-check-circle'
                                            : null
                                    )
                                    ->suffixIconColor(
                                        fn (Get $get, $record = null) => (!empty($record?->pcDetails?->account_number) || preg_match('/^[0-9]+$/', $get('account_number')))
                                            ? 'success'
                                            : null
                                    )
                                    ->extraAttributes([
                                        'style' => 'font-family: monospace;', // Ensures consistent character width
                                    ])
                                    ->validationMessages([
                                        'required' => 'The Account Number field is required.',
                                        'regex' => 'The Account Number must be numeric.',
                                        'max' => 'The Account Number may not be greater than 30 characters.',
                                    ]),
                            ])->columns(2),
                        ])->afterValidation(function (Get $get, StoreProfileByAdminService $service, $record) {
                            $data = [];
                            $data['beneficiary_name'] = encryptTextParam($get('beneficiary_name'));
                            $data['bank_name'] = encryptTextParam($get('bank_name'));
                            $data['account_number'] = encryptTextParam($get('account_number'));
                            $data['is_credit_line'] = $get('is_credit_line', false);
                            $data['user_id'] = $record->id;
                            $service->storeBankDetails($data);
                        }),
                    Step::make('Legal Documents')
                        ->icon('heroicon-o-document-text')
                        ->schema([
                            Group::make()->schema([
                                FileUpload::make('company_registration_certificate')
                                    ->rules(function () {
                                        // Explicitly restrict SVG by not including it in allowed types
                                        return ['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])];
                                    })
                                    ->required()
                                    ->label(new HtmlString(
                                        'Company Registration Certificate 
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->maxSize('2048')
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->downloadable()
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias') . $this->user->pcDetails->id;
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('company_registration_certificate_'),
                                    )
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'application/pdf', '.jpg', '.jpeg', '.png', '.pdf']) // explicitly restrict SVG
                                    ->validationMessages([
                                        'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'max' => 'File size must not exceed 2 MB',
                                        'required' => 'The Company Registration Certificate is required',
                                    ]),
                                FileUpload::make('license_permit')
                                    ->previewable()
                                    // ->required()
                                    ->label(new HtmlString(
                                        'Relevant certification
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->multiple()
                                    ->downloadable()
                                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'application/pdf', '.jpg', '.jpeg', '.png', '.pdf']) // explicitly restrict SVG
                                    ->validationMessages([
                                        'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                        'max' => 'File size must not exceed 2 MB',                                        
                                    ])
                                    ->maxSize('2048')
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])])
                                    ->maxFiles(3)
                                    ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias') . $this->user->pcDetails->id;
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('license_permit_'),
                                    ),
                                TextInput::make('user_id')->visible(false)->default(fn ($record) => $record->id),
                            ])->columns(2),
                        ]),
                ])
                ->submitAction(
                    Action::make('submit')
                        ->label('Submit')
                        ->action('store')
                        ->color('primary')
                )
                // ->startOnStep(function () {
                //     return ($this->user->pcDetails?->step === 5 && $this->user->pcDetails?->is_submitted) ? 1 : ($this->user->pcDetails?->step ?? 1);
                // })
                ->columnSpanFull(),
        ])->statePath('data');
    }

    public function store(StoreProfileByAdminService $service)
    {
        $data['user_id'] = $this->record;
        $data['email'] = $this->user->email;
        if (!empty($this->user->created_by) && empty($this->user->password)) {
            $service->sendCredential($data);
        }
        $service->storeDocuments($this->form->getState(), $this->record);

        // Activity Log: Only log if is_submitted is true
        if (!$this->user->pcDetails?->is_submitted) {
            activity()
                ->causedBy(auth()->user())
                ->useLog('pc_profile_create')
                ->performedOn($this->user->pcDetails ?? $this->user)
                ->withProperties([
                    'old' => [
                        'status' => 'pending',
                    ],
                ])
                ->log("Pharmaceutical Supplier onboarding has been completed by admin for {$this->user->name}");
        }

        $this->redirect(UserResource::getUrl('index'));
    }
}
