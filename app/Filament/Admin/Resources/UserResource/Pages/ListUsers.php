<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Actions\ExportAction;
use Filament\Support\Enums\MaxWidth;
use App\Filament\Exports\UserExporter;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\FileUpload;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Admin\Resources\UserResource;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\Pages\ListRecords\Tab;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;


    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label("+ Add Pharmaceutical Supplier")
                ->extraAttributes(['class' => 'mb-4']),
            // ExportAction::make()->label("Export")->exporter(UserExporter::class)
            //     ->extraAttributes(['class' => 'mb-4']),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Users",
            // $this->getResource()::getUrl('index') => "Pharmaceutical suppliers",
            // 3 => "List",
        ];
    }

    public function getTabs(): array
    {
        return [
            'All' => Tab::make(),
            'Pending' => Tab::make()
                ->query(function (Builder $query) {
                    // Match the filter logic: pending = 'pending' or null
                    return $query->where(function ($q) {
                        $q->where('verification_status', 'pending')
                          ->orWhereNull('verification_status');
                    });
                })->badge(fn() => ($count = User::query()
                    ->role('Pharmaceutical Company')
                    ->where(function ($q) {
                        $q->where('verification_status', 'pending')
                          ->orWhereNull('verification_status');
                    })
                    ->count()) > 0 ? $count : null),
            'Approved' => Tab::make()
                ->query(function (Builder $query) {
                    return $query->where('verification_status', 'approved');
                }),
            'Rejected' => Tab::make()
                ->query(function (Builder $query) {
                    return $query->where('verification_status', 'rejected');
                }),
        ];
    }
}
