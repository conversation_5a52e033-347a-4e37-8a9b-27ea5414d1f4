<?php

namespace App\Filament\Admin\Resources\UserResource\Pages;

use Closure;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use Filament\Resources\Pages\CreateRecord;
use App\Service\StoreProfileByAdminService;
use Filament\Forms\Components\Actions\Action;
use App\Filament\Admin\Resources\UserResource;
use Filament\Panel\Concerns\HasNotifications;
use App\Models\PcDetail;
use App\Models\User;
use Filament\Actions\Action as ActionsAction;
use Filament\Notifications\Notification;
use Filament\Actions\Action as Actions;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;


    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Pharmaceutical supplier has been created successfully');
    }

    protected function getFormActions(): array
    {
        return [];
    }

    public function handleRecordCreation(array $data): Model
    {
        // PcDetail::where('user_id', $this->data['user_id'])->update(['company_registration_certificate' => $data['company_registration_certificate'], 'license_permit' => $data['license_permit']]);
        PcDetail::where('user_id', $this->data['user_id'])->update([
            'company_registration_certificate' => $data['company_registration_certificate'],
            'license_permit' => $data['license_permit'],
            'step' => 5
        ]);
        $data = $this->data;
        $data['email'] = $data['email'];
        $data['user_id'] = $data['user_id'];
        sendCredential($data);
        return User::find($this->data['user_id']);
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Pharmaceutical suppliers",
            3 => "Create",
        ];
    }

    public function getHeaderActions(): array
    {
        return [ Actions::make('back')
        ->label('Back')
        ->color('gray')
        ->url(UserResource::getUrl('index')),];
    }
}
