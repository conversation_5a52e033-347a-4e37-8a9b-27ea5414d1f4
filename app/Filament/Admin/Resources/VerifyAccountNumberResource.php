<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\VerifyAccountNumberResource\Pages;
use App\Filament\Admin\Resources\VerifyAccountNumberResource\RelationManagers;
use App\Models\ClinicDetail;
use App\Models\ClinicPharmaSupplier;
use App\Models\User;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;

class VerifyAccountNumberResource extends Resource
{
    protected static ?string $model = ClinicPharmaSupplier::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Pending Requests';
    protected static ?string $navigationLabel = 'Verify Credit Line';
    protected static ?int $navigationSort = 1;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_view verify credit line');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('clinicDetail.name')
                    ->label('Facility Name')->searchable()->toggleable()
                    ->getStateUsing(function ($record) {
                        return $record->clinicDetail->name ?? 'N/A';
                    }),
                TextColumn::make('pcInfo.company_name')
                    ->label('Pharma Supplier')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        // Search by company_name or business_name, matching the display logic
                        return $query->whereHas('pcInfo', function ($q) use ($search) {
                            $q->where(function ($subQ) use ($search) {
                                $subQ->where('company_name', 'like', "%{$search}%")
                                    ->orWhere('business_name', 'like', "%{$search}%");
                            });
                        });
                    })
                    ->toggleable()
                    ->getStateUsing(function ($record) {
                        $companyName = $record->pcInfo->company_name ?? null;
                        if (!empty($companyName)) {
                            return $companyName;
                        }
                        $businessName = $record->pcInfo->business_name ?? null;
                        return !empty($businessName) ? $businessName : 'N/A';
                    }),
                TextColumn::make('account_number')->searchable()->toggleable()
                    ->label('Account Number')->getStateUsing(function ($record) {
                        return $record->account_number;
                    }),
                TextColumn::make('approved_by')->searchable()->toggleable()
                    ->label('Verified By')
                    ->getStateUsing(function ($record) {
                        return $record->approvedBy->company_name ?? 'Admin';
                    })
                    ->hidden(fn ($livewire) => !$livewire->isVerified),
                TextColumn::make('created_at')
                    ->label('Requested Date')
                    ->toggleable()
                    ->formatStateUsing(function ($state): string {
                        if (empty($state)) {
                            return '-';
                        }
                        $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                        $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                        return $convertedDate->format('M d, Y | h:i A');
                    })
                    ->sortable()
                    ->searchable(),
                TextColumn::make('rejected_by')->toggleable()
                    ->label('Rejected By')
                    ->getStateUsing(function ($record) {
                        $rejectedByUser = User::find($record->reject_by);
                        return $rejectedByUser->name ?? 'Admin';
                    })
                    ->visible(fn ($livewire) => $livewire->isRejected),
                TextColumn::make('reject_reason')->searchable()->toggleable()
                    ->label('Reject Reason')
                    ->getStateUsing(function ($record) {
                        return $record->reject_reason ?? '-';
                    })
                    ->formatStateUsing(function ($state) {
                        if (strlen($state) <= 40) {
                            return $state;
                        }
                        return '<span title="' . e($state) . '" style="cursor: default">' . e(substr($state, 0, 40)) . '...</span>';
                    })
                    ->html()
                    ->visible(fn ($livewire) => $livewire->isRejected),

                TextColumn::make('status')
                    ->searchable()
                    ->visible(false)
                    ->label('Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', $record->status)) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'approved' => 'bi-patch-check-fill',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'warning',
                            'rejected' => 'danger',
                            'approved' => 'rgba(0, 70, 104, 1)',
                            default => 'secondary',
                        };
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light yellow for pending
                            'rejected' => ['style' => 'background-color:rgba(254, 242, 242, 1); border: 1px solid rgba(254, 226, 226, 1); border-radius: 6px; color:rgba(220, 38, 38, 1); padding: 4px 8px; width: fit-content;'], // light red for rejected
                            'approved' => ['style' => 'background-color:rgba(243, 251, 255, 1); border: 1px solid rgba(206, 237, 253, 1); border-radius: 6px; color:rgba(0, 70, 104, 1); padding: 4px 8px; width: fit-content;'], // light green for approved
                            default => ['style' => 'background-color:rgba(255, 251, 235, 1); border: 1px solid rgba(253, 236, 206, 1); border-radius: 6px; color:rgba(217, 119, 6, 1); padding: 4px 8px; width: fit-content;'], // light gray for unknown
                        };
                    }),
                // TextColumn::make('actions')
                // ->label('Action')
                // ->extraAttributes(['style' => 'marign-right:100px;'])
                // ->visible(fn ($record, $livewire) => !$livewire->isVerified)
                // ->formatStateUsing(fn ($state, $record) => $state ?? 'No Actions Available'),
            ])
            ->actionsColumnLabel(function ($livewire) {
                // if (!$livewire->isVerified) {
                //     return 'Action';
                // } else {
                //     return '';
                // }
                return 'Actions';
            })
            ->filters([
                // SelectFilter::make('status')
                //     ->options([
                //         'pending' => 'Pending',
                //         'approved' => 'Approved',
                //         'rejected' => 'Rejected',
                //     ])
                //     ->label('Status'),
                // SelectFilter::make('clinic_id')
                //     ->relationship('clinicDetail', 'name')
                //     ->searchable()
                //     ->preload()
                //     ->options(ClinicPharmaSupplier::where('pc_id', auth()->user()->id)
                //         ->get()
                //         ->pluck('clinicDetail.name', 'clinic_id')
                //         ->filter()
                //         ->toArray())
                //     ->label('Facility'),
                SelectFilter::make('clinic_id')
                    ->label('Facilities')
                    ->searchable()
                    ->preload()
                    ->options(function () {
                        // Get the same list as the table query: User::query()->role('Clinic')->with('clinicDetails', ...)
                        return \App\Models\User::query()
                            ->role('Clinic')
                            ->with('clinicDetails')
                            ->whereHas('clinicDetails', function ($q) {
                                $q->whereNotNull('clinic_name');
                            })
                            ->get()
                            ->pluck('clinicDetails.clinic_name', 'id')
                            ->filter()
                            ->toArray();
                    }),
                SelectFilter::make('pc_id')
                    ->label('Pharma Supplier')
                    ->searchable()
                    ->preload()
                    ->options(function () {
                        // Use the same query as the pc list page for consistency
                        return \App\Models\User::query()
                            ->role('Pharmaceutical Company')
                            ->with('pcDetails')
                            ->whereHas('pcDetails', function ($q) {
                                $q->whereNotNull('company_type_id');
                            })
                            ->get()
                            ->mapWithKeys(function ($user) {
                                $companyTypeId = $user->pcDetails->company_type_id ?? null;
                                $companyName = $user->pcDetails->company_name ?? null;
                                $businessName = $user->pcDetails->business_name ?? null;

                                // If company_type_id is 1 and company name is null, use business name
                                if ($companyTypeId == 1 && empty($companyName)) {
                                    $displayName = $businessName;
                                } else {
                                    $displayName = $companyName ?: $businessName;
                                }

                                return !empty($displayName) ? [$user->id => $displayName] : [];
                            })
                            ->filter()
                            ->toArray();
                    }),
                Filter::make('created_at')
                    ->label('Requested Date')
                    ->form([
                        DatePicker::make('created_from')->label('Requested From')->closeOnDateSelection()->reactive()->maxDate(now()),
                        DatePicker::make('created_until')->label('Requested Until')->closeOnDateSelection()->maxDate(now())->minDate(function ($get) {
                            return $get('created_from') ? Carbon::parse($get('created_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),

            ])
            ->actions([
                ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->url(function ($record) {
                        return ClinicResource::getUrl('view', ['record' => $record->clinic_id]);
                    }),
                Action::make('approve')
                    ->label('')
                    ->tooltip('Approve')
                    ->icon('heroicon-m-check')->size('sm')->iconButton()
                    ->color('success')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    // ->extraAttributes(['class' => 'border-2 border-success rounded-lg', 'style' => 'margin-left: inherit;'])
                    ->action(function ($record) {
                        $record->update([
                            'status' => 'approved',
                            'approved_by' => auth()->user()->id,
                            'reject_by' => null
                        ]);

                        //Activity Log Start
                        $currentUser = getUser(auth()->user());
                        $approvedByName = $currentUser && isset($currentUser->name) ? $currentUser->name : 'Admin';
                        $facilityName = $record->clinicDetail && isset($record->clinicDetail->name) ? $record->clinicDetail->name : '';
                        activity()
                            ->causedBy(auth()->user())
                            ->performedOn($record)
                            ->useLog('account_number_verified')
                            ->withProperties([
                                'old' => [
                                    'status' => 'pending',
                                ],
                                'attributes' => array_filter([
                                    'status' => $record->status,
                                    'approved_by' => $approvedByName,
                                    'facility_name' => $facilityName,
                                ], fn ($value) => !is_null($value)),
                            ])
                            ->log($facilityName . "'s account number has been approved");
                        //Activity Log End

                        Notification::make()
                            ->title('Facility Approved')
                            ->success()
                            ->send();
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to approve this facility?');
                        return $action;
                    })->visible(fn ($record, $livewire) => !$livewire->isVerified && $record->status !== 'approved'), // Show if $livewire->isVerified is true

                Action::make('reject')
                    ->label('')
                    ->tooltip('Reject')
                    ->icon('heroicon-m-x-mark')->size('sm')->iconButton()
                    ->color('danger')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    // ->extraAttributes(['class' => 'border-2 border-danger rounded-lg', 'style' => 'margin-left: inherit;'])
                    ->modalSubmitAction(
                        fn ($action) =>
                        $action->label('Save')->color('danger')
                    )
                    ->form([
                        Textarea::make('reason')
                            ->label('')
                            ->validationMessages([
                                'required' => "The reason field is required.",
                            ])
                            ->rules(['required'])
                    ])
                    ->action(function ($data, $record) {
                        $record->update([
                            'status' => 'rejected',
                            'reject_by' => auth()->user()->id,
                            'approved_by' => null,
                            'reject_reason' => $data['reason']
                        ]);

                        //Activity Log Start
                        $currentUser = getUser(auth()->user());
                        $rejectByName = $currentUser && !empty($currentUser->name) ? $currentUser->name : 'Admin';
                        $facilityName = $record->clinicDetail->name ?? '';
                        $rejectReason = $data['reason'] ?? '';

                        activity()
                            ->causedBy(auth()->user())
                            ->performedOn($record)
                            ->useLog('account_number_rejected')
                            ->withProperties([
                                'old' => [
                                    'status' => 'pending',
                                ],
                                'attributes' => array_filter([
                                    'status' => $record->status,
                                    'reject_by' => $rejectByName,
                                    'reject_reason' => $rejectReason,
                                    'facility_name' => $facilityName,
                                ], fn ($value) => !is_null($value)),
                            ])
                            ->log(($facilityName ?: 'Facility') . "'s account number has been rejected");
                        //Activity Log End

                        Notification::make()
                            ->title('Facility Rejected')
                            ->success()
                            ->send();
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to reject this facility?');
                        return $action;
                    })->visible(fn ($record, $livewire) => !$livewire->isVerified && $record->status !== 'rejected'),


            ]);
        // ->bulkActions([
        //     Tables\Actions\BulkAction::make('approve')
        //         ->label('Approve')
        //         ->icon('heroicon-o-check-circle')
        //         ->color('success')
        //         ->hidden(fn ($livewire) => $livewire->isVerified)
        //         ->action(function ($records) {
        //             $records->each(function ($record) {
        //                 $record->update(['status' => 'approved', 'approved_by' => auth()->user()->id, 'reject_by' => null]);
        //             });

        //             //Activity Log Start
        //             // $currentUser = getUser(auth()->user());
        //             // activity()
        //             //     ->causedBy(auth()->user())
        //             //     ->performedOn($records)
        //             //     ->useLog('account_number_verified')
        //             //     ->withProperties([
        //             //         'old' => [
        //             //             'status' => 'pending',
        //             //         ],
        //             //         'attributes' => array_filter([
        //             //             'status' => $records->status,
        //             //             'approved_by' => $currentUser->name,
        //             //             'facility_name' => $records->clinicDetail->name ?? '',
        //             //         ], fn($value) => !is_null($value)),
        //             //     ])
        //             //     ->log(($record->clinicDetail->name ?? '') . " account number have been approved");
        //             //Activity Log End

        //             Notification::make()
        //                 ->title('Request Approved')
        //                 ->body('The selected request have been approved successfully.')
        //                 ->success()
        //                 ->send();
        //         })->after(function () {
        //             redirect(static::getUrl('index'));
        //         })
        //         ->requiresConfirmation(),
        //     Tables\Actions\BulkAction::make('reject')
        //         ->label('Reject')
        //         ->icon('heroicon-o-x-circle')
        //         ->color('danger')
        //         ->hidden(fn ($livewire) => $livewire->isVerified)
        //         ->action(function ($records) {
        //             $records->each(function ($record) {
        //                 $record->update(['status' => 'rejected', 'reject_by' => auth()->user()->id, 'approved_by' => null]);
        //             });

        //             //Activity Log Start
        //             // $currentUser = getUser(auth()->user());
        //             // activity()
        //             //     ->causedBy(auth()->user())
        //             //     ->performedOn($records)
        //             //     ->useLog('account_number_rejected')
        //             //     ->withProperties([
        //             //         'old' => [
        //             //             'status' => 'pending',
        //             //         ],
        //             //         'attributes' => array_filter([
        //             //             'status' => $records->status,
        //             //             'reject_by' => $currentUser->name,
        //             //             'facility_name' => $record->clinicDetail->name ?? '',
        //             //         ], fn($value) => !is_null($value)),
        //             //     ])
        //             //     ->log(($record->clinicDetail->name ?? '') . " account number have been rejected");
        //             //Activity Log End

        //             Notification::make()
        //                 ->title('Request Rejected')
        //                 ->body('The selected request have been rejected successfully.')
        //                 ->success()
        //                 ->send();
        //         })->after(function () {
        //             redirect(static::getUrl('index'));
        //         })
        //         ->requiresConfirmation(),
        // ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVerifyAccountNumbers::route('/'),
            'create' => Pages\CreateVerifyAccountNumber::route('/create'),
            // 'edit' => Pages\EditVerifyAccountNumber::route('/{record}/edit'),
        ];
    }
}
