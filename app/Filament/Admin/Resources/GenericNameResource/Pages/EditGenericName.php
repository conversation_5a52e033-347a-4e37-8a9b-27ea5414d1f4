<?php

namespace App\Filament\Admin\Resources\GenericNameResource\Pages;

use App\Filament\Admin\Resources\GenericNameResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditGenericName extends EditRecord
{
    protected static string $resource = GenericNameResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //  Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(GenericNameResource::getUrl()),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.generic_name.title.updated'))
            ->title(__('message.generic_name.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Generic Names",
            3 => "Edit Generic Name",
        ];
    }
}
