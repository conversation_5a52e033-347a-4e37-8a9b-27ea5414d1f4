<?php

namespace App\Filament\Admin\Resources\GenericNameResource\Pages;

use App\Filament\Admin\Resources\GenericNameResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListGenericNames extends ListRecords
{
    protected static string $resource = GenericNameResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Generic Name'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // $this->getResource()::getUrl('index') => "Generic Names",
            // 3 => "List",
        ];
    }
}
