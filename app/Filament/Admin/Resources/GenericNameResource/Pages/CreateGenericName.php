<?php

namespace App\Filament\Admin\Resources\GenericNameResource\Pages;

use App\Filament\Admin\Resources\GenericNameResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateGenericName extends CreateRecord
{
    protected static string $resource = GenericNameResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(GenericNameResource::getUrl()),
        ];
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.generic_name.title.created'))
            ->title(__('message.generic_name.create_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Add'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Generic Names",
            3 => "Add Generic Name",
        ];
    }
    public function getTitle(): string
    {
        return 'Add Generic Name';
    }
}
