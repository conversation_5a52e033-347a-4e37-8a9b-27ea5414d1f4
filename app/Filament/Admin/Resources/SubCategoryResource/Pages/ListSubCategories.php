<?php

namespace App\Filament\Admin\Resources\SubCategoryResource\Pages;

use App\Filament\Admin\Resources\SubCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSubCategories extends ListRecords
{
    protected static string $resource = SubCategoryResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // 2 => "Sub Categories",
            // 3 => "List",
        ];
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Sub Category'),
        ];
    }
}
