<?php

namespace App\Filament\Admin\Resources\SubCategoryResource\Pages;

use App\Filament\Admin\Resources\SubCategoryResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;


class EditSubCategory extends EditRecord
{
    protected static string $resource = SubCategoryResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Sub Categories",
            3 => "Edit Sub Category",
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            //  Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(SubCategoryResource::getUrl()),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.sub-category.title.updated'))
            ->title(__('message.sub-category.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
}
