<?php

namespace App\Filament\Admin\Resources\SubCategoryResource\Pages;

use App\Filament\Admin\Resources\SubCategoryResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateSubCategory extends CreateRecord
{
    protected static string $resource = SubCategoryResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Sub Categories",
            3 => "Add Sub Category",
        ];
    }
    protected function getHeaderActions(): array
    {
        return [
           Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(SubCategoryResource::getUrl()),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.sub-category.title.created'))
            ->title(__('message.sub-category.create_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getTitle(): string
    {
        return 'Add Sub Category';
    }
}
