<?php

namespace App\Filament\Admin\Resources\CategoryResource\Pages;

use App\Filament\Admin\Resources\CategoryResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateCategory extends CreateRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model
    {
        // Create the record using the parent method
        $record = parent::handleRecordCreation($data);
        
        // Clear any product-related caches when categories are created
        // This ensures fresh category data is loaded in ProductResource
        if (class_exists(\App\Filament\Admin\Resources\ProductResource::class)) {
            \App\Filament\Admin\Resources\ProductResource::clearCaches();
        }
        
        return $record;
    }
    
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.category.title.created'))
            ->title(__('message.category.create_success'));
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(CategoryResource::getUrl()),

        ];
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Categories",
            3 => "Add Category",
        ];
    }
    public function getTitle(): string
    {
        return 'Add Category';
    }
}
