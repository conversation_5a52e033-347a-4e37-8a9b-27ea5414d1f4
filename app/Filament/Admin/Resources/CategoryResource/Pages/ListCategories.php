<?php

namespace App\Filament\Admin\Resources\CategoryResource\Pages;

use App\Filament\Admin\Resources\CategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCategories extends ListRecords
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Category'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // $this->getResource()::getUrl('index') => "Categories",
            // 3 => "List",
        ];
    }
}
