<?php

namespace App\Filament\Admin\Resources\CategoryResource\Pages;

use App\Filament\Admin\Resources\CategoryResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Spatie\Activitylog\Models\Activity;

class EditCategory extends EditRecord
{
    protected static string $resource = CategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //  Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                 ->url(CategoryResource::getUrl()),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    
    protected function handleRecordUpdate(\Illuminate\Database\Eloquent\Model $record, array $data): \Illuminate\Database\Eloquent\Model
    {
        // Update the record using the parent method
        $record = parent::handleRecordUpdate($record, $data);
        
        // Clear any product-related caches when categories are updated
        // This ensures fresh category data is loaded in ProductResource
        if (class_exists(\App\Filament\Admin\Resources\ProductResource::class)) {
            \App\Filament\Admin\Resources\ProductResource::clearCaches();
        }
        
        return $record;
    }
    
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.category.title.updated'))
            ->title(__('message.category.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Categories",
            3 => "Edit Category",
        ];
    }
}
