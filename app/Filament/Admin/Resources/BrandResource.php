<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\BrandResource\Pages;
use App\Models\Brand;
use App\Models\Product;
use App\Rules\CaseSensitiveUnique;
use Closure;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Container\Attributes\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB as FacadesDB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Model;

class BrandResource extends Resource
{
    protected static ?string $model = Brand::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 4;
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('brands_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('brands_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('brands_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('brands_delete');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->extraAttributes(['novalidate' => true])
            ->schema([
                Section::make()->schema([
                    TextInput::make('name')->label(new HtmlString("Brand Name<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->rules([
                            'required',
                            'max:100',
                            // 'regex:/^[\w\s\p{P}]+$/u',
                            fn(Get $get) => new CaseSensitiveUnique(Brand::class, 'name', $get('id'))
                        ])
                        ->maxLength(100)
                        // ->unique(table: 'brands', column:'name', ignoreRecord: true)
                        ->validationMessages([
                            'required' => __('message.brand.required'),
                            // 'regex' => __('message.brand.regex'),
                            'max' => __('message.brand.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.brand.case_sensitive_unique'),
                        ]),
                    FileUpload::make('logo')->image()->directory('brands')->imageEditor()
                        ->maxSize(2048)
                        ->hint('Supported formats: JPEG, JPG, PNG (Max 2MB), dimension: 580px x 580px')
                        ->rules([
                            'required',
                            'image',
                            'mimes:jpeg,png,jpg',
                            // 'dimensions:width=580,height=580',
                        ])
                        ->validationMessages([
                            'required' => 'logo is required',
                            'image' => 'The file must be an image.',
                            'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                            // 'dimensions' => 'The image must be 580px x 580px.',
                        ])
                        ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])->required(),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Brand Name')->sortable()->searchable()->toggleable(),
                ImageColumn::make('logo')->circular()->toggleable()
                    ->getStateUsing(function ($record) {
                        return $record->logo
                            ? Storage::disk('s3')->url($record->logo) : null;
                    }),
                ToggleColumn::make('status')->label('Status')
                    ->sortable()
                    ->toggleable()
                    ->disabled(function ($record) {
                        return Product::query()->where('brand_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('brand_id', $record->id)->exists()) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                // ->title(__('message.brand.title.warning'))
                                ->title(__('message.brand.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('message.brand.title.saved'))
                            ->duration(1000)
                            ->title(__('message.brand.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])->tooltip('Delete')
                    ->visible(function ($record) {
                        return !Product::query()->where('brand_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.brand.title.deleted'))
                            ->title(__('message.brand.delete_success'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = FacadesDB::select("SELECT * FROM products WHERE brand_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('brand_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name; // Assuming "name" is the attribute for display
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.brand.title.deletion_completed'))
                                ->title(__('message.brand.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.brand.title.partial_deleted'))
                                ->title(__('message.brand.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.brand.title.activated'))
                            ->title(__('message.brand.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('brand_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.brand.title.deactivated'))
                                ->title(__('message.brand.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.brand.title.partial_inactivated'))
                                ->title(__('message.brand.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBrands::route('/'),
            'create' => Pages\CreateBrand::route('/create'),
            'edit' => Pages\EditBrand::route('/{record}/edit'),
        ];
    }
}
