<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\InvoicesResource\Pages;
use App\Filament\Admin\Resources\InvoicesResource\RelationManagers;
use App\Models\Invoices;
use App\Models\Order;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\Action;
use Filament\Facades\Filament;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Columns\ViewColumn;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\Stack;
use Illuminate\Support\Facades\Storage;

use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;

class InvoicesResource extends Resource
{
    protected static ?string $model = Order::class;
    protected static ?string $navigationGroup = 'Finance';
   // protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-list';
   protected static ?string $navigationIcon = '';
    protected static ?string $navigationLabel = 'Invoices';
    protected static ?int $navigationSort = 5;


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                Order::with('subOrder.user', 'orderProducts', 'user', 'payoutOrder')->withCount('orderProducts')
            )
            ->recordUrl(null)
            ->defaultSort('id', 'desc')
            ->columns([
                TextColumn::make('order_number')->label('Order ID')->sortable()->searchable()->toggleable(),
                TextColumn::make('amount')->label('Order Amount')->sortable()->searchable()->toggleable()->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),
                TextColumn::make('orderProducts')
                    ->label('Admin Fees')
                    ->getStateUsing(function (Order $record) {
                        return $record->orderProducts->sum('total_commission') > 0
                            ? 'RM ' . number_format($record->orderProducts->sum('total_commission'), 2)
                            : '-';
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->orderBy(
                            DB::raw('(SELECT COALESCE(SUM(total_commission), 0) FROM order_products WHERE order_products.order_id = orders.id)'),
                            $direction
                        );
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        // Search for orders where the sum of total_commission matches the search value
                        // Remove 'RM', commas, and spaces from the search string, and try to match as float
                        $searchValue = floatval(str_replace([',', 'RM', ' '], '', $search));
                        return $query->whereRaw(
                            '(SELECT COALESCE(SUM(total_commission), 0) FROM order_products WHERE order_products.order_id = orders.id) = ?',
                            [$searchValue]
                        );
                    })
                    ->toggleable(),
                ViewColumn::make('purchase_order_download')
                    ->label('Receipt')
                    ->toggleable()
                    ->view('components.table-columns.view-admin-invoice-icon'),
                    TextColumn::make('commision_invoice')
                    ->label('Dpharma Invoice')
                    ->view('components.table-columns.view-dpharma-admin-invoice-icon')

                    // ->getStateUsing(function ($record) {
                    //     return $record->invoice_path ? 'View' : 'No Receipt';
                    // })
                    // ->url(function ($record) {
                    //     $fileName = $record->invoice_path;
                    //     if (!$fileName) {
                    //         return null;
                    //     }

                    //     $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                    //     $filePath = $invoicePath . $fileName;

                    //     return Storage::disk('s3')->temporaryUrl(
                    //         $filePath,
                    //         now()->addMinutes(15) // URL will be valid for 15 minutes
                    //     );
                    // })
                    ->getStateUsing(function ($record) {
                            return '-';
                        })
                    ->icon('')
                    ->color(function ($record) {
                        return  'gray';
                    })
                    ->sortable(false)
                    ->toggleable(),
                    TextColumn::make('payoutOrder.Payout.payout_status')
                    ->label('Payout Status')
                    ->searchable()
                    ->badge()
                    ->toggleable()
                    ->default('pending')
                    ->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
                    ->color(function ($state) {
                        $st = match ($state) {
                            'pending' => 'warning',
                            'paid' => 'success',
                            'failed' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->leftJoin('payout_sub_orders', 'orders.id', '=', 'payout_sub_orders.order_id')
                            ->leftJoin('payouts', 'payout_sub_orders.payout_id', '=', 'payouts.id')
                            ->orderBy('payouts.payout_status', $direction)
                            ->select('orders.*');
                    }),
            ])
            ->actions([
                Action::make('view_suborders')
                    ->label(false)
                    ->icon('heroicon-o-eye')
                    ->tooltip('Details')
                    ->size('w-5 h-5')
                    // ->color('text-gray-500')
                    // ->extraAttributes(['class' => 'fi-color-gray  fi-icon-btn h-8 w-8 rounded-lg justify-center items-center -m-1.5'])

                ->extraAttributes(['class' => 'fi-color-gray fi-icon-btn h-8 w-8 rounded-lg justify-center items-center -m-0'])
                    ->modalHeading('Sub Orders Invoice')
                    ->modalContent(function (Order $record) {
                        $suborders = $record->subOrder()->with(['user', 'orderProducts', 'payoutSubOrder', 'payoutSubOrder.Payout'])
                        ->selectRaw('*, (total_sub_order_value - (
                            SELECT COALESCE(SUM(total_commission), 0)
                            FROM order_products
                            WHERE sub_order_id = sub_orders.id
                        )) AS net_earnings')
                        ->get();
                        return view('filament.admin.resources.invoices.suborders-modal', [
                            'suborders' => $suborders,
                        ]);
                    })
                    ->modalSubmitAction(false)
            ])
            ->actionsColumnLabel('Actions')

            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ])
            ->filters([
                SelectFilter::make('payoutOrder.Payout.payout_status')
                    ->label('Payout Status')
                    ->options([
                        'pending' => 'Pending',
                        'paid' => 'Paid',
                        'failed' => 'Failed',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }
                        return $query->whereHas('payoutOrder.Payout', function ($q) use ($data) {
                            $q->where('payout_status', $data['value']);
                        });
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInvoices::route('/invoices-admin'),
            'create' => Pages\CreateInvoices::route('/create'),
            'edit' => Pages\EditInvoices::route('/{record}/edit'),
        ];
    }
}
