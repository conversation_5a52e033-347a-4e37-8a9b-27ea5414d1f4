<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ContainerResource\Pages;
use App\Filament\Admin\Resources\ContainerResource\RelationManagers;
use App\Models\Container;
use App\Models\Product;
use App\Rules\CaseSensitiveUnique;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\Model;

class ContainerResource extends Resource
{
    protected static ?string $model = Container::class;

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 9;
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('packages_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('packages_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('packages_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('packages_delete');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    TextInput::make('name')
                        ->label(new HtmlString("Package Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->maxLength(100)
                        ->placeholder('Enter Package Name')
                        ->rules([
                            'required',
                            // 'regex:/^[\w\s\p{P}]+$/u',
                            fn(Get $get) => new CaseSensitiveUnique(Container::class, 'name', $get('id'))
                        ])
                        ->validationMessages([
                            'required' => __('message.container.required'),
                            // 'regex' => __('message.container.regex'),
                            'max' => __('message.container.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.container.case_sensitive_unique'),
                        ]),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Packages Name')->sortable()->searchable()->toggleable(),
                ToggleColumn::make('status')->label('Status')

                    ->sortable()
                    ->toggleable()
                    ->disabled(function ($record) {
                        return Product::query()->where('container_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('container_id', $record->id)->exists()) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                ->title(__('message.container.title.warning'))
                                ->title(__('message.container.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('message.container.title.saved'))
                            ->duration(1000)
                            ->title(__('message.container.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !Product::query()->where('container_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.container.title.deleted'))
                            ->title(__('message.container.delete_success'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = DB::select("SELECT * FROM products WHERE container_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('container_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name; // Assuming "name" is the attribute for display
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.container.title.deletion_completed'))
                                ->title(__('message.container.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.container.title.partial_deleted'))
                                ->title(__('message.container.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.container.title.activated'))
                            ->title(__('message.container.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('container_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.container.title.deactivated'))
                                ->title(__('message.container.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.container.title.partial_inactivated'))
                                ->title(__('message.container.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContainers::route('/'),
            'create' => Pages\CreateContainer::route('/create'),
            'edit' => Pages\EditContainer::route('/{record}/edit'),
        ];
    }
}
