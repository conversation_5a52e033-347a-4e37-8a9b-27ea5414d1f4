<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\DosageFormResource\Pages;
use App\Models\DosageForm;
use App\Models\Product;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;
use App\Rules\CaseSensitiveUnique;
use Closure;
use Filament\Forms\Get;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class DosageFormResource extends Resource
{
    protected static ?string $model = DosageForm::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 3;

    protected static ?string $label = 'Dosage Form';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dosage-forms_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dosage-forms_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dosage-forms_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('dosage-forms_delete');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    // Section::make()->schema([
                    TextInput::make('name')
                        ->label(new HtmlString("Dosage Form Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->maxLength(100)
                        // ->unique(DosageForm::class, 'name', ignoreRecord: true)
                        ->rules([
                            'required',
                            // 'regex:/^[\w\s\p{P}]+$/u', 
                            'max:100',
                            fn(Get $get) => new CaseSensitiveUnique(DosageForm::class, 'name', $get('id')) // Return the custom rule instance
                        ])
                        ->validationMessages([
                            'required' => __('message.dosage_foam.required'),
                            // 'regex' => __('message.dosage_foam.regex'),
                            'max' => __('message.dosage_foam.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.dosage_foam.case_sensitive_unique'),
                        ]),

                    // ]),
                    // Section::make()->schema([
                    //     Toggle::make('status')
                    //         ->onColor('success')
                    //         ->offColor('danger')->default(true),

                    // ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Dosage Form Name')->sortable()->searchable()->toggleable(),
                ToggleColumn::make('status')->label('Status')
                    
                    ->sortable()
                    ->toggleable()
                    ->disabled(function ($record) {
                        return Product::query()->where('dosage_foams_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('dosage_foams_id', $record->id)->exists()) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                // ->title(__('message.dosage_foam.title.warning'))
                                ->title(__('message.dosage_foam.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('message.dosage_foam.title.saved'))
                            ->duration(1000)
                            ->title(__('message.dosage_foam.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),

            ])

            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !Product::query()->where('dosage_foams_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        
                            $record->delete();
                            Notification::make()
                                ->success()
                                // ->title(__('message.dosage_foam.title.deleted'))
                            ->title(__('message.dosage_foam.delete_success'))
                            ->send();

                    }),
            ])

            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = DB::select("SELECT * FROM products WHERE dosage_foams_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('dosage_foams_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name; // Assuming "name" is the attribute for display
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.dosage_foam.title.deletion_completed'))
                                ->title(__('message.dosage_foam.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.dosage_foam.title.partial_deleted'))
                                ->title(__('message.dosage_foam.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.dosage_foam.title.activated'))
                            ->title(__('message.dosage_foam.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('dosage_foams_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.dosage_foam.title.deactivated'))
                                ->title(__('message.dosage_foam.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.dosage_foam.title.partial_inactivated'))
                                ->title(__('message.dosage_foam.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDosageForms::route('/'),
            'create' => Pages\CreateDosageForm::route('/create'),
            'edit' => Pages\EditDosageForm::route('/{record}/edit'),
        ];
    }
}
