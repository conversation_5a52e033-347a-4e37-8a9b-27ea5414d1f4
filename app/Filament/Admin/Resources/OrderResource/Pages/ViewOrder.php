<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\CreditLineOrderResource;
use Filament\Actions;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Filament\Infolists\Infolist;
use Illuminate\Support\Facades\Storage;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Admin\Resources\OrderResource;
use Illuminate\Auth\Access\AuthorizationException;

class ViewOrder extends ViewRecord
{
    use HasBackButton;
    protected static string $resource = OrderResource::class;

    public string $queryType = 'credit_line';

    public static function viewOrder(): bool
    {
        return auth()->user()?->hasRole('Super Admin') || auth()->user()?->can('all-orders_view details');
    }


    #[\Override]
    public function mount(int | string $record): void
    {

        session(['queryType' => request()->get('type')]);
        parent::mount($record);
        if (!self::viewOrder()) {
            throw new AuthorizationException('Forbidden.');
        }
        $this->record->load(['user', 'orderProducts.product', 'subOrder.user', 'shippingCity', 'shippingState', 'shippingCountry', 'billingCity', 'billingState', 'billingCountry', 'subOrder.pcDetail', 'subOrder.orderProducts.product', 'subOrder.orderProducts']);
    }


    public function getBreadcrumbs(): array
    {
        $orderType = request()->query('type');
        $routeName = $orderType === 'credit_line' ? 'filament.admin.resources.credit-line-orders.index' : 'filament.admin.resources.orders.index';
        $title = $orderType === 'credit_line' ? 'Credit Line Orders' : 'All Orders';

        return [
            1 => 'Orders Management',
            route($routeName) => $title,
            3 => 'Order Details',
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('downloadInvoice')
                ->label('Download Invoice')
                ->icon('heroicon-o-arrow-down-tray')
                ->action(function ($record) {
                    $invoicePath = config('constants.api.order_invoices.main');

                    if ($invoicePath && $record->pdf_path) {
                        try {
                            $filePath = $invoicePath . $record->pdf_path;
                            $fileName = basename($filePath);
                            $fileContent = Storage::get($filePath);

                            //Activity Log Start
                            // activity()
                            //     ->causedBy(auth()->user())
                            //     ->performedOn($record)
                            //     ->useLog('invoice_download')
                            //     ->log(
                            //         $record->order_number
                            //             ? "Invoice for order #{$record->order_number} downloaded successfully"
                            //             : "Invoice downloaded successfully"
                            //     );
                            //Activity Log End

                            return response()->streamDownload(function () use ($fileContent) {
                                echo $fileContent;
                            }, $fileName);
                        } catch (\Exception $e) {
                            \Log::error('Error downloading invoice', [
                                'pdf_path' => $record->pdf_path,
                                'error' => $e->getMessage(),
                            ]);

                            Notification::make()
                                ->title('Error')
                                ->body('Unable to download invoice. Please try again later.')
                                ->danger()
                                ->send();

                            return null;
                        }
                    }

                    //Activity Log Start
                    // activity()
                    //     ->causedBy(auth()->user())
                    //     ->performedOn($record)
                    //     ->useLog('invoice_download')
                    //     ->withProperties([
                    //         'attributes' => array_filter([
                    //             'status' => 'failed',
                    //             'reason' => 'Unable to download invoice: ',
                    //         ], fn($value) => !is_null($value)),
                    //     ])
                    //     ->log(
                    //         $record->order_number
                    //             ? "Failed to download invoice for order #{$record->order_number} due to not invoice found"
                    //             : "Failed to download invoice due to not invoice found"
                    //     );
                    //Activity Log End

                    Notification::make()
                        ->title('Error')
                        ->body('No invoice found.')
                        ->danger()
                        ->send();

                    return null;
                }),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                // ->url(OrderResource::getUrl('index'))
                ->url(function () {
                    $type = request()->get('type');
                    $type = request()->cookie('source');
                    if ($type == 'full-payout') {
                        $id = request()->cookie('id');
                        return route('filament.admin.resources.full-payouts.view', ['record' => $id]);
                    } else if ($type == 'scheduled') {
                        $id = request()->cookie('id');
                        return route('filament.admin.resources.scheduled-payouts.view', ['record' => $id]);
                    } else if ($type == 'outstanding') {
                        $id = request()->cookie('id');
                        return route('filament.admin.resources.outstanding-payments.view', ['record' => $id]);
                    } else if ($type == 'credit_line') {
                        return CreditLineOrderResource::getUrl('index');
                    }
                    return OrderResource::getUrl('index');
                }),
            // Action::make('downloadPO')
            // ->label('Download PO')
            // ->icon('heroicon-o-arrow-down-tray')
            // ->action(function ($record) {

            //     Notification::make()
            //         ->title('Error')
            //         ->body('No invoice found.')
            //         ->danger()
            //         ->send();

            //     return null;
            // }),
        ];
    }
    public function getTitle(): string
    {
        return '#' . $this->getRecord()->order_number ?? '-';
    }
}
