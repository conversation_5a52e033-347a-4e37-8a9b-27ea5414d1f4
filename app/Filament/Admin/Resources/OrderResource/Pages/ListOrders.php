<?php

namespace App\Filament\Admin\Resources\OrderResource\Pages;

use App\Filament\Admin\Resources\OrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    public static function canAccess(array $parameters = []): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();

        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') ||  $user->can('all-orders_view');
    }

    protected function getHeaderActions(): array
    {
        return [];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Orders Management",
            // $this->getResource()::getUrl('index') => "All Orders",
            // 2 => "List",
        ];
    }
    public function getTitle(): string
    {
        return 'All Orders';
    }
}
