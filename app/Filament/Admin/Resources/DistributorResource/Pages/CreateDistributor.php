<?php

namespace App\Filament\Admin\Resources\DistributorResource\Pages;

use App\Filament\Admin\Resources\DistributorResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateDistributor extends CreateRecord
{
    protected static string $resource = DistributorResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(DistributorResource::getUrl()),
        ];
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.distributor.title.created'))
            ->title(__('message.distributor.create_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Distributors",
            3 => "Add Distributor",
        ];
    }
    public function getTitle(): string
    {
        return 'Add Distributor';
    }
}
