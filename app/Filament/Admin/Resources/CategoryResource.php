<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\CategoryResource\Pages;
use App\Models\Category;
use App\Models\Product;
use App\Models\ClinicAccountType;
use App\Rules\CaseSensitiveUnique;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Collection;
use Filament\Forms\Get;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextInputColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class CategoryResource extends Resource
{
    protected static ?string $model = Category::class;

    // protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 1;

    public static function canAccess(): bool
    {
        return Auth::user()->hasRole('Super Admin') || Auth::user()->can('categories_view');
    }
    public static function canCreate(): bool
    {
        return Auth::user()->hasRole('Super Admin') || Auth::user()->can('categories_create');
    }
    public static function canEdit(Model $record): bool
    {
        return Auth::user()->hasRole('Super Admin') || Auth::user()->can('categories_update');
    }
    public static function canDelete(Model $record): bool
    {
        return Auth::user()->hasRole('Super Admin') || Auth::user()->can('categories_delete');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    Grid::make(2)->schema([
                        TextInput::make('name')->label(new HtmlString("Category Name<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                            ->rules([
                                'required',
                                'max:100',
                                // 'regex:/^[\w\s\p{P}]+$/u',
                                fn(Get $get) => new CaseSensitiveUnique(Category::class, 'name', $get('id'))
                            ])
                            ->maxLength(100)
                            ->validationMessages([
                                'required' => __('message.category.required'),
                                // 'regex' => __('message.category.regex'),
                                'max' => __('message.category.max'),
                                'App\\Rules\\CaseSensitiveUnique' => __('message.category.case_sensitive_unique'),
                            ]),
                        TextInput::make('serial_number')->label("Order")
                            ->numeric()
                            ->rules([
                                'nullable',
                                'min:1'
                            ])
                            ->validationMessages([
                                'min' => __('message.category.min_order'),
                            ])
                    ]),
                    Select::make('not_accessible')->label('Not Accessible to')
                        ->relationship(name: 'categoryAccountTypes', titleAttribute: 'name')
                        ->multiple()->preload()
                        ->searchable()
                    // ->pivotData([
                    //     'is_primary' => true,
                    // ])
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function () {
                return Category::query()->whereNull('parent_id');
            })
            ->columns([
                TextColumn::make('name')->label('Category Name')
                    ->sortable()->searchable()->toggleable()->width('30%')->wrap(),
                TextInputColumn::make('serial_number')
                    ->label('Order')->type('number')->placeholder('-')->sortable()->width('80px')->searchable()->toggleable()
                    ->rules(['nullable', 'numeric', 'min:1'])->alignment(Alignment::Center)
                    ->updateStateUsing(function ($record, $state) {
                        if ($state !== null && !is_numeric($state)) {
                            Notification::make()->danger()->title('Order must be a number.')->send();
                            return $record->serial_number;
                        }
                        if ($state !== null && $state !== '' && $state < 1) {
                            Notification::make()->danger()->title('Order must be at least 1.')->send();
                            return $record->serial_number;
                        }
                        $record->update(['serial_number' => $state === '' ? null : $state]);
                        Notification::make()
                            ->success()
                            ->title('The order has been updated successfully.')
                            ->duration(2000)
                            ->send();

                        return $state;
                    }),
                ToggleColumn::make('status')
                    ->label('Status')->toggleable()->sortable()->alignment(Alignment::Center)->width('40%')
                    ->disabled(function ($record) {
                        $hasProducts = Product::where('category_id', $record->id)->exists();
                        $hasSubcategories = Category::where('parent_id', $record->id)->exists();
                        return $hasProducts || $hasSubcategories;
                    })
                    ->afterStateUpdated(function ($record, $state, $livewire) {
                        $hasProduct = Product::where('category_id', $record->id)->exists();
                        $hasSubcategory = Category::where('parent_id', $record->id)->exists();

                        if (!$state && ($hasProduct || $hasSubcategory)) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                ->title(__('message.category.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        // Clear product-related caches when category status changes
                        if (class_exists(\App\Filament\Admin\Resources\ProductResource::class)) {
                            \App\Filament\Admin\Resources\ProductResource::clearCaches();
                        }

                        Notification::make()
                            ->success()
                            ->duration(1000)
                            ->title(__('message.category.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                        'x-data' => '{ loading: false }',
                        'x-on:change' => 'loading = true; setTimeout(() => loading = false, 1000)',
                        'x-bind:class' => 'loading ? "opacity-50 cursor-wait" : ""',
                    ]),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        $hasSubcategories = Category::query()->where('parent_id', $record->id)->exists();
                        $hasProducts = Product::query()->where('category_id', $record->id)->exists();
                        return !$hasSubcategories && !$hasProducts;
                    })
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.category.title.deleted'))
                            ->title(__('message.category.delete_success'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failedDueToProducts = [];
                        $failedDueToSubcategories = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failedDueToProducts, &$failedDueToSubcategories, &$deleted) {
                            $hasSubcategories = Category::query()->where('parent_id', $record->id)->exists();

                            if ($hasSubcategories) {
                                $failedDueToSubcategories[] = $record->name;
                                return;
                            }

                            $hasProducts = Product::query()->where('category_id', $record->id)->exists();

                            if ($hasProducts) {
                                $failedDueToProducts[] = $record->name;
                                return;
                            }

                            $record->delete();
                            $deleted++;
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.category.title.deletion_completed'))
                                ->title(__('message.category.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failedDueToSubcategories)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.category.title.subcategories_found'))
                                ->title(__('message.category.bulk_delete_failed_subcategories', ['names' => implode(', ', $failedDueToSubcategories)]))
                                ->send();
                        }

                        if (!empty($failedDueToProducts)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.category.title.products_found'))
                                ->title(__('message.category.bulk_delete_failed_products', ['names' => implode(', ', $failedDueToProducts)]))
                                ->send();
                        }
                    }),

                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.category.title.activated'))
                            ->title(__('message.category.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::where('category_id', $record->id)->exists();
                            $hasSubcategory = Category::where('parent_id', $record->id)->exists(); // Check for child categories

                            if ($hasProduct || $hasSubcategory) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.category.title.deactivated'))
                                ->title(__('message.category.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.category.title.partial_inactivated'))
                                ->title(__('message.category.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategory::route('/create'),
            'edit' => Pages\EditCategory::route('/{record}/edit'),
        ];
    }
}
