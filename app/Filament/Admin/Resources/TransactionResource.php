<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\TransactionResource\Pages;
use App\Filament\Admin\Resources\TransactionResource\RelationManagers;
use App\Models\Transaction;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Database\Eloquent\Model;

class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('transactions_view');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('transactions_create');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('transactions_update');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('transactions_update');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->query(
            Transaction::with('order.user', 'sender')

        )
        ->defaultSort('id', 'desc')
            ->columns([
                TextColumn::make('transaction_id')->label('Transaction ID')->sortable()->searchable()->toggleable(),
                TextColumn::make('order.order_number')
                ->label('Order ID')
                ->url(function($record){
                    // dd($record);
                    return route('filament.admin.resources.orders.view', ['record' => $record->order_id]);
                })
                ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                ->sortable()->searchable()->toggleable(),
                TextColumn::make('created_at')->label('Date & Time')->sortable()->searchable()->toggleable()
                ->formatStateUsing(function ($record) {
                    // dd($record);
                    $timezone = ($record?->order?->user?->timezone) ? $record?->order?->user?->timezone : 'Asia/Kolkata';
                    $time = Carbon::parse($record->created_at)->timezone($timezone);
                    return  $time->format('d M, Y h:i A');
                }),
                TextColumn::make('order.user.name')->label('Sender')->sortable()->searchable()->toggleable(),
                // TextColumn::make('transaction_type')->label('Transaction Type')->sortable()->searchable()->toggleable()->formatStateUsing(function ($state) {
                //     $parts = explode('_', $state);
                //     $parts = array_map(function ($part) {
                //         return ucfirst($part);
                //     }, $parts);
                //     return implode(' ', $parts);
                // }),
                TextColumn::make('payment_method')->label('Payment Method')->sortable()->searchable()->toggleable()->formatStateUsing(function ($state) {
                    $parts = explode('_', $state);
                    $parts = array_map(function ($part) {
                        return ucfirst($part);
                    }, $parts);
                    return implode(' ', $parts);
                }),
                TextColumn::make('amount')->label('Amount')->sortable()->searchable()->toggleable()->formatStateUsing(function ($state, $record) {
                    return $state > 0
                        ? 'RM ' . number_format($state, 2)
                        : '-';
                }),
                TextColumn::make('status')->label('Status')->sortable()->searchable()->toggleable()->formatStateUsing(fn(string $state): string => ucfirst(strtolower($state))),
            ])
            ->filters([
                SelectFilter::make('sender_id')
                    ->label('Sender')
                    ->multiple()
                    ->options(User::whereHas('transactions')->pluck('name', 'id')),
                SelectFilter::make('order_id')
                    ->label('Order Number')
                    ->multiple()
                    ->options(
                        Order::whereIn('id', Transaction::select('order_id')->distinct())
                            ->pluck('order_number', 'id')
                    ),
                SelectFilter::make('payment_method')
                    ->label('Payment Method')
                    ->multiple()
                    ->options([
                        'CREDIT' => 'CREDIT',
                        'DEBIT' => 'DEBIT',
                    ]),
                // ->query(fn($query, $data) => $query->whereIn('sender_id', array_map('intval', (array) $data))),
                Filter::make('created_at')
                    ->label('Created At')
                    ->form([
                        DatePicker::make('created_at')
                        ->maxDate(fn($get) => now())
                            ->closeOnDateSelection()
                            ->native(false) // Ensures a proper UI calendar popup
                            ->maxDate(now())
                    ])
                    ->query(function ($query, $data) {
                        if (!empty($data['created_at'])) {
                            $query->whereDate('created_at', $data['created_at']);
                        }
                    })->indicateUsing(function (array $data) {
                        $indicators = [];
                        if (!empty($data['created_at'])) {
                            if (isset($data['created_at'])) {
                                $indicators[] = Indicator::make(Carbon::parse($data['created_at'])->format('M d, Y'))
                                    ->removeField('from');
                            }
                        }
                        return $indicators;
                    }),
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            // 'create' => Pages\CreateTransaction::route('/create'),
            // 'edit' => Pages\EditTransaction::route('/{record}/edit'),
        ];
    }
}
