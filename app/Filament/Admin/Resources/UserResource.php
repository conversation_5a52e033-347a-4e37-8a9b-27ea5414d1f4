<?php

namespace App\Filament\Admin\Resources;

use Dom\Text;
use Carbon\Carbon;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\PcDetail;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Filament\Actions\Action;
use App\Models\PcCompanyType;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use App\Livewire\InfoListPrice;
use Illuminate\Validation\Rule;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Select;
// use Filament\Actions\ActionGroup;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Illuminate\Support\Facades\Cache;
use Illuminate\Validation\Rules\File;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Infolists\Components\Tabs;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Wizard\Step;
use App\Service\StoreProfileByAdminService;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Illuminate\Database\Eloquent\Collection;
use Indianic\Settings\Models\GlobalSettings;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Grid as InfoGrid;
use App\Filament\Admin\Resources\UserResource\Pages;
use App\Forms\Components\PhoneWithPrefix;
use App\Livewire\AdminProductList;
use App\Mail\ApprovalForAdminMail;
use App\Mail\ChangesApprovedMail;
use App\Mail\ChangesRejectedMail;
use App\Models\Approval;
use App\Models\ClinicCertificateFile;
use App\Models\PcCertificateFile;
use App\Models\UserAddress;
use App\Models\ZipCode;
use App\OnboardingStep;
use App\Rules\PhoneWithPrefixRule;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Livewire;
use Filament\Infolists\Components\Group as InfoGroup;
use Filament\Infolists\Components\Livewire as ComponentsLivewire;
use Filament\Infolists\Components\Section as InfoSection;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $label = 'Pharmaceutical suppliers';

    protected $listeners = [
        'tab-changed' => 'handleTabChange'
    ];

    public static function canViewAny(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pharmaceutical-suppliers_view');
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pharmaceutical-suppliers_view');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pharmaceutical-suppliers_create');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pharmaceutical-suppliers_update');
    }

    public static function canConfig(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pharmaceutical-suppliers_configure');
    }


    public function handleTabChange($data)
    {
        // $data['tab'] contains the tab ID
        // Do something with the active tab information
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $approvals = (new static())->getStepOneChanges($infolist->getRecord()->id, OnboardingStep::BASIC_INFO);
        $stepTwoApprovals = (new static())->getStepOneChanges($infolist->getRecord()->id, OnboardingStep::ADDRESS);
        $stepThreeApprovals = (new static())->getStepOneChanges($infolist->getRecord()->id, OnboardingStep::DOCUMENTS);
        $stepFiveApprovals = (new static())->getStepOneChanges($infolist->getRecord()->id, OnboardingStep::CONTACT);
        $stepSixApprovals = (new static())->getStepOneChanges($infolist->getRecord()->id, OnboardingStep::PERSON_IN_CHARGE);

        $stepOneChanges = (new static())->organizeApprovalChanges($approvals);
        $stepTwoChanges = (new static())->organizeApprovalChanges($stepTwoApprovals);
        $stepThreeChanges = (new static())->organizeDocumentApprovalChanges($stepThreeApprovals);
        $stepFiveChanges = (new static())->organizeApprovalChanges($stepFiveApprovals);
        $stepSixChanges = (new static())->organizeApprovalChanges($stepSixApprovals);

        return $infolist
            // Removed extraAttributes for AlpineJS tab handling
            ->schema(function ($record) use ($stepOneChanges, $stepTwoChanges, $stepThreeChanges, $stepFiveChanges, $stepSixChanges) {
                $record = $record->load('warehouses.city', 'warehouses.state');
                return [
                    InfoSection::make('Basic Details')
                        ->headerActions([
                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                ->extraAttributes([
                                    'wire:key' => 'edit-1'
                                ])
                                ->visible(fn($record) => static::canEdit($record))
                                ->icon('heroicon-o-pencil')
                                ->color('gray')
                                ->outlined()
                                ->button()
                                ->form(function ($record) {
                                    return [
                                        Group::make()->schema([
                                            // TextInput::make('pcDetails.company_name')
                                            //     ->autofocus(false)
                                            //     ->rules(function ($record) {
                                            //         if (!empty($record->id)) {
                                            //             return ['required', 'string', 'max:100', Rule::unique('pc_details', 'company_name')->ignore($record->id, 'user_id')];
                                            //         }
                                            //         return ['required', 'string', 'max:100', 'unique:pc_details,company_name'];
                                            //     })
                                            //     ->default($record->pcDetails?->company_name ?? ''),
                                            // TextInput::make('person_in_charge_name')
                                            //     ->readOnly()
                                            //     ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'heroicon-s-check-circle' : null)
                                            //     ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'success' : null)
                                            //     ->default($record->pcDetails->person_in_charge_name ?? '')->rules(['required', 'string', 'regex:/^[a-zA-Z\s]+$/', 'max:50']),
                                            // TextInput::make('email')
                                            //     ->rules(function ($record) {
                                            //         if (!empty($record->id)) {
                                            //             return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', Rule::unique('users', 'email')->ignore($record->id, 'id')];
                                            //         }
                                            //         return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', 'unique:users,email'];
                                            //     })->disabled()
                                            //     ->default($record->email),
                                            TextInput::make('phone_number')->default($record->pcDetails->phone_number ?? '')
                                                ->mask('999999999999')
                                                ->prefix('+60')
                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Mobile number </span> <span style="color:red;font-size: 11px !important;">*</span><span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Please enter your active mobile number in this format (e.g., 999999999999). Must be 8-12 digits.`" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>
                                                </span>'))
                                                ->live()
                                                ->validationMessages([
                                                    'required' => 'The Mobile number field is required.',
                                                    'digits_between' => 'The Mobile number must be between 8 and 12 digits long.'
                                                ])
                                                ->stripCharacters(['-'])
                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                ->extraAttributes([
                                                    'inputmode' => 'numeric',
                                                    'maxlength' => '12'
                                                ])
                                                ->rules(['required', 'digits_between:8,12']),
                                            // TextInput::make('commission')
                                            //     ->formatStateUsing(function ($record) {
                                            //         return ($record->pcDetails->commission_type ?? '');
                                            //     }),
                                            // TextInput::make('payout')
                                            //     ->formatStateUsing(function ($record) {
                                            //         return ($record->pcDetails->commission_payout_option ?? '');
                                            //     }),
                                            Toggle::make('is_active')->default($record->is_active),
                                        ])->columns(3)
                                    ];
                                })
                                ->action(function ($record, $data, Form $form) {
                                    $form->fill();

                                    //Activity Log Start
                                    // $oldValues = [
                                    //     'email' => $record->email,
                                    //     'is_active' => $record->is_active,
                                    //     'company_name' => $record->pcDetails?->company_name ?? '',
                                    //     'phone_number' => $record->pcDetails?->phone_number ?? '',
                                    //     'person_in_charge_name' => $record->pcDetails?->person_in_charge_name ?? '',
                                    // ];

                                    // $newValues = [
                                    //     'email' => $data['email'],
                                    //     'is_active' => $data['is_active'],
                                    //     'company_name' => $data['pcDetails']['company_name'] ?? '',
                                    //     'phone_number' => $data['pcDetails']['phone_number'] ?? '',
                                    //     'person_in_charge_name' => $data['person_in_charge_name'] ?? '',
                                    // ];
                                    // $changedOld = [];
                                    // $changedNew = [];
                                    // foreach ($oldValues as $key => $oldValue) {
                                    //     if ($oldValue != $newValues[$key]) {
                                    //         $changedOld[$key] = $oldValue;
                                    //         $changedNew[$key] = $newValues[$key];
                                    //     }
                                    // }
                                    //Activity Log End
                                    $record->update(Arr::only($data, ['name', 'is_active', 'email', 'phone']));
                                    if (!empty($data)) {
                                        $record->pcDetails->updateOrCreate([
                                            'user_id' => $record->id
                                        ], Arr::only($data, ['company_name', 'phone_number']));
                                    }

                                    //Activity Log Start
                                    // if (!empty($changedOld)) {
                                    //     activity()
                                    //         ->causedBy(auth()->user())
                                    //         ->performedOn($record->pcDetails ?? $record)
                                    //         ->useLog('partner_center_edit')
                                    //         ->withProperties([
                                    //             'old' => $changedOld,
                                    //             'attributes' => $changedNew,
                                    //         ])
                                    //         ->log("Basic Details for Pharmaceutical supplier have been updated");
                                    // }
                                    //Activity Log End
                                })
                        ])
                        ->schema([
                            \Filament\Infolists\Components\Grid::make([
                                // 'default' => 5,
                                // 'sm' => 5,
                            ])
                                ->columnSpan('full')
                                ->extraAttributes(['class' => 'gap-0'])
                                ->schema([
                                    ImageEntry::make('company_image')
                                        ->columnSpan(1)
                                        ->label('')
                                        ->default($record->photo ? Storage::disk('s3')->url('users/' . $record->photo) : asset('/images/user-avatar.png'))
                                        ->circular(),

                                    \Filament\Infolists\Components\Grid::make([
                                        'default' => 4,
                                        'sm' => 4,
                                    ])
                                        ->columnSpan(4)
                                        ->schema([
                                            // TextEntry::make('pcDetails.company_name')
                                            //     ->default('-')
                                            //     ->label('Company Name'),
                                            // TextEntry::make('pcDetails.person_in_charge_name')
                                            //     ->default('-')
                                            //     ->label('Full Name'),
                                            TextEntry::make('email')
                                                ->label('Email')
                                                ->default('-')
                                                ->icon('heroicon-m-envelope'),
                                            TextEntry::make('pcDetails.phone_number')
                                                ->default('-')
                                                ->prefix('+60 ')
                                                ->label('Mobile Number')
                                                ->icon('heroicon-m-phone'),
                                            TextEntry::make('created_at')
                                                ->default('-')
                                                ->label('Created On')
                                                ->dateTime('M d, Y | H:i'),
                                            TextEntry::make('is_active')
                                                ->default('-')
                                                ->label('Status')
                                                ->badge()
                                                ->formatStateUsing(fn($state): string =>  $state ? 'Active' : 'Inactive')
                                                ->color(fn($state): string => $state ? 'success' : 'danger'),
                                            TextEntry::make('pcDetails.commission_type')
                                                ->default('-')
                                                ->formatStateUsing(function ($record) {
                                                    $pcCommission = PcDetail::where('user_id', $record->id)->select('commission_percentage', 'commission_type')->first();
                                                    if (empty($pcCommission) || $pcCommission->commission_type == null) {
                                                        return '-';
                                                    }
                                                    if ($pcCommission->commission_type == 'percentage') {
                                                        return $pcCommission->commission_percentage . '%';
                                                    } else {
                                                        return "RM " . $pcCommission->commission_percentage;
                                                    }
                                                    return "-";
                                                })
                                                ->label('Commission'),
                                            TextEntry::make('pcDetails.commission_payout_option')
                                                ->default('-')
                                                ->formatStateUsing(fn($state) => ucfirst($state))
                                                ->label('Payout Type'),
                                            TextEntry::make('admin_verified_on')
                                                ->label('Approved On')
                                                ->formatStateUsing(function ($state): string {
                                                    if (empty($state)) {
                                                        return '-';
                                                    }

                                                    $userTimezone = auth()->user()->timezone;
                                                    $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                                                    return $convertedDate->format('M d, Y | H:i');
                                                }),
                                        ])
                                ])
                        ]),
                    Tabs::make()
                        // ->live() // Make tabs reactive
                        // ->activeTab(fn($livewire) => info($livewire->activeTab))
                        ->tabs([
                            Tab::make('General Details')
                                ->id('general') // Assign ID
                                ->schema([
                                    InfoSection::make('Supplier Details')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->icon('heroicon-o-pencil')
                                                ->visible(fn($record) => static::canEdit($record))
                                                ->form(function ($record) {
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('pcDetails.business_name')
                                                                ->default(isset($record->pcDetails->business_name) ? $record->pcDetails->business_name : '')
                                                                ->label(fn() => new HtmlString('Business Name <span style="color: red;">*</span>'))
                                                                ->validationMessages([
                                                                    'required' => 'The Business Name field is required.',
                                                                    'string' => 'The Business Name must be a string.',
                                                                    // 'regex' => 'The Business Name must be a alphanumeric string.',
                                                                    'max' => 'The Business Name may not be greater than 64 characters.',
                                                                ])
                                                                ->rules(function ($record) {
                                                                    if (!empty($record->id)) {
                                                                        return [
                                                                            'required',
                                                                            'string',
                                                                            'max:64',
                                                                            // 'regex:/^[a-zA-Z\s]+$/',
                                                                            Rule::unique('pc_details', 'business_name')->ignore($record->id, 'user_id')
                                                                        ];
                                                                    }
                                                                    return [
                                                                        'required',
                                                                        'string',
                                                                        'max:64',
                                                                        // 'regex:/^[a-zA-Z\s]+$/',
                                                                        'unique:pc_details,business_name'
                                                                    ];
                                                                }),
                                                            Select::make('pcDetails.company_type_id')
                                                                ->default(isset($record->pcDetails->company_type_id) ? $record->pcDetails->company_type_id : '')
                                                                ->placeholder('Select company type')
                                                                ->rules(['required']) // Add server-side validation
                                                                ->validationMessages([
                                                                    'required' => 'The Company Type field is required.',
                                                                ])
                                                                ->validationAttribute('Company Type')                                                               
                                                                ->label(new HtmlString(
                                                                    'Company Type <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Select the type of company that best describes your business.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                                                ))
                                                                ->options(fn() => PcCompanyType::pluck('name', 'id'))
                                                                ->reactive(),

                                                            TextInput::make('pcDetails.company_name')
                                                                ->default(isset($record->pcDetails->company_name) ? $record->pcDetails->company_name : '')
                                                                ->label(function ($get) {
                                                                    $companyTypeId = $get('pcDetails.company_type_id');
                                                                    // Only show red star if not 1 and not empty
                                                                    return new HtmlString(
                                                                        ($companyTypeId && $companyTypeId != 1
                                                                            ? 'Company Name <span style="color: red;">*</span>'
                                                                            : 'Company Name'
                                                                        )
                                                                    );
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The Company Name field is required.',
                                                                    'string' => 'The Company Name must be a string.',
                                                                    'regex' => 'The Company Name must be a alphanumeric string.',
                                                                    'max' => 'The Company Name may not be greater than 64 characters.',
                                                                ])
                                                                // Remove client-side required, only server-side validation
                                                                ->rules(function ($record, $get) {
                                                                    $companyTypeId = $get('pcDetails.company_type_id');
                                                                    $rules = ['max:64', 'regex:/^[a-zA-Z\s]+$/'];
                                                                    if (!empty($record->id)) {
                                                                        $rules[] = Rule::unique('pc_details', 'company_name')->ignore($record->id, 'user_id');
                                                                    } else {
                                                                        $rules[] = 'unique:pc_details,company_name';
                                                                    }
                                                                    // Only require if not 1 and not empty
                                                                    if ($companyTypeId && $companyTypeId != 1) {
                                                                        array_unshift($rules, 'required');
                                                                    }
                                                                    return $rules;
                                                                })
                                                                ->reactive(),

                                                            TextInput::make('pcDetails.company_registration_number')
                                                                ->default(isset($record->pcDetails->company_registration_number) ? $record->pcDetails->company_registration_number : '')
                                                                ->label(function ($get) {
                                                                    $companyTypeId = $get('pcDetails.company_type_id');
                                                                    // Only show red star if not 1 and not empty
                                                                    return new HtmlString(
                                                                        ($companyTypeId && $companyTypeId != 1
                                                                            ? 'Company Registration Number <span style="color: red;">*</span>'
                                                                            : 'Company Registration Number'
                                                                        )
                                                                    );
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The Company Registration Number field is required.',
                                                                    'regex' => 'The Company Registration Number must be a alphanumeric string.',
                                                                    'min' => 'The Company Registration Number must be at least 2 characters.',
                                                                    'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                                                    'unique' => 'The Company Registration Number has already been taken.',
                                                                ])
                                                                ->live()
                                                                ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                // Remove client-side required, only server-side validation
                                                                ->rules(function ($record, $get) {
                                                                    $companyTypeId = $get('pcDetails.company_type_id');
                                                                    $rules = ['regex:/^[a-zA-Z0-9]+$/', 'min:2', 'max:20'];
                                                                    if (!empty($record->id)) {
                                                                        $rules[] = Rule::unique('pc_details', 'company_registration_number')->ignore($record->id, 'user_id');
                                                                    } else {
                                                                        $rules[] = 'unique:pc_details,company_registration_number';
                                                                    }
                                                                    // Only require if not 1 and not empty
                                                                    if ($companyTypeId && $companyTypeId != 1) {
                                                                        array_unshift($rules, 'required');
                                                                    }
                                                                    return $rules;
                                                                })
                                                                ->reactive(),
                                                            TextInput::make('pcDetails.tin_number')
                                                                ->default(isset($record->pcDetails->tin_number) ? $record->pcDetails->tin_number : '')
                                                                ->label(fn() => new HtmlString('TIN Number <span style="color: red;">*</span>'))
                                                                ->validationMessages([
                                                                    'max' => 'The TIN number may not be greater than 20 characters.',
                                                                    'unique' => 'The TIN number has already been taken.',
                                                                    'min' => 'The TIN number must be at least 1 characters.',
                                                                    'regex' => 'The TIN number must be a alphanumeric string.',
                                                                    'required' => 'The TIN number field is required.',
                                                                ])
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->live()
                                                                ->rules(function ($record) {
                                                                    if (!empty($record->id)) {
                                                                        return ['required', 'min:1', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', Rule::unique('pc_details', 'tin_number')->ignore($record->id, 'user_id')];
                                                                    }
                                                                    return ['required', 'min:1', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'unique:pc_details,tin_number'];
                                                                }),
                                                            TextInput::make('pcDetails.sstc_number')
                                                                ->default(isset($record->pcDetails->sstc_number) ? $record->pcDetails->sstc_number : '')
                                                                ->label(fn() => new HtmlString('SST number'))
                                                                ->validationAttribute('SST number')
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->live()
                                                                ->validationMessages([
                                                                    // 'required' => 'The SST number field is required.',
                                                                    'max' => 'The SST number may not be greater than 20 characters.',
                                                                    'unique' => 'The SST number has already been taken.',
                                                                    'min' => 'The SST number must be at least 1 characters.',
                                                                    'regex' => 'The SST number must be a alphanumeric string.',
                                                                ])
                                                                ->rules(function ($record) {
                                                                    if (!empty($record->id)) {
                                                                        return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', Rule::unique('pc_details', 'sstc_number')->ignore($record->id, 'user_id')];
                                                                    }
                                                                    return ['nullable', 'max:20', 'regex:/^[a-zA-Z0-9]+$/', 'min:1', 'unique:pc_details,sstc_number'];
                                                                }),
                                                            // Checkbox::make('pcDetails.is_credit_line')->label('Enable Credit Line')->default(fn() => $record->pcDetails?->is_credit_line ?? false)->columnSpanFull()->reactive(),
                                                            // TextInput::make('pcDetails.phone_number')->default($record->pcDetails->phone_number)
                                                            //     ->rules(['required', 'string', 'max:15', 'regex:/^\+?[0-9]{5,15}$/']),
                                                            // TextInput::make('pcDetails.web_url')->default($record->pcDetails->web_url)
                                                            //     ->url(),
                                                        ])->columns(3)
                                                    ];
                                                })
                                                ->action(function ($record, $data) {

                                                    //Activity Log Start
                                                    // $oldValues = [
                                                    //     'business_name' => $record->pcDetails->business_name ?? null,
                                                    //     'company_name' =>  $record->pcDetails->company_name ?? null,
                                                    //     'company_registration_number' => $record->pcDetails->company_registration_number ?? null,
                                                    //     'tin_number' => $record->pcDetails->tin_number ?? null,
                                                    //     'sst_number' => $record->pcDetails->sstc_number ?? null,
                                                    //     'company_type_name' => $record->pcDetails->companyType->name ?? null
                                                    // ];
                                                    //Activity Log End

                                                    $record->pcDetails
                                                        ->updateOrCreate(
                                                            ['user_id' => $record->id],
                                                            Arr::only($data['pcDetails'], ['business_name', 'company_name', 'company_registration_number', 'tin_number', 'sstc_number', 'phone_number', 'web_url', 'company_type_id'])
                                                        );

                                                    //Activity Log Start
                                                    // $newValues = [
                                                    //     'business_name' => $data['pcDetails']['business_name'] ?? null,
                                                    //     'company_name' => $data['pcDetails']['company_name'] ?? null,
                                                    //     'company_registration_number' => $data['pcDetails']['company_registration_number'] ?? null,
                                                    //     'tin_number' => $data['pcDetails']['tin_number'] ?? null,
                                                    //     'sst_number' => $data['pcDetails']['sstc_number'] ?? null,
                                                    //     'company_type_name' => PcCompanyType::find($data['pcDetails']['company_type_id'])?->name ?? null,
                                                    // ];

                                                    // $changedOld = [];
                                                    // $changedNew = [];
                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     if ($oldValue != $newValues[$key]) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValues[$key];
                                                    //     }
                                                    // }

                                                    // if (!empty($changedOld)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('company_details')
                                                    //         ->performedOn($record->pcDetails)
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Supplier Details have been updated for Pharmaceutical supplier '{$record->pcDetails?->company_name}'");
                                                    // }
                                                    //Activity Log End
                                                }),
                                            static::makeAcceptAction(OnboardingStep::BASIC_INFO->value),
                                            static::makeRejectAction(OnboardingStep::BASIC_INFO->value),
                                        ])
                                        ->schema([
                                            TextEntry::make('pcDetails.business_name')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepOneChanges['business_name'] ?? null
                                                    );
                                                })
                                                ->label('Business Name'),
                                            TextEntry::make('pcDetails.company_name')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepOneChanges['company_name'] ?? null
                                                    );
                                                })
                                                ->label('Company Name'),
                                            TextEntry::make('pcDetails.company_registration_number')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepOneChanges['company_registration_number'] ?? null
                                                    );
                                                })
                                                ->label('Company Registration Number'),
                                            TextEntry::make('pcDetails.tin_number')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepOneChanges['tin_number'] ?? null
                                                    );
                                                })
                                                ->label('TIN Number'),
                                            TextEntry::make('pcDetails.sstc_number')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepOneChanges['sstc_number'] ?? null
                                                    );
                                                })
                                                ->label('SST Number'),
                                            TextEntry::make('pcDetails.companyType.name')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                                    return static::formatRelationshipWithPendingChanges(
                                                        $state,
                                                        $stepOneChanges['company_type_id'] ?? null,
                                                        PcCompanyType::class
                                                    );
                                                })
                                                ->label('Company Type'),
                                            // TextEntry::make('pcDetails.is_credit_line')
                                            //     ->default('-')
                                            //     ->formatStateUsing(function ($state) use ($stepOneChanges) {
                                            //         return static::formatCreditLineStatus(
                                            //             $state,
                                            //             $stepOneChanges['is_credit_line'] ?? null
                                            //         );
                                            //     })
                                            //     ->label('Credit Line'),
                                        ])->columns(4),

                                    InfoSection::make('Warehouse Addresses')
                                        ->description(static::getWarehouseTypeChangeMessage($record))
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->icon('heroicon-o-pencil')
                                                ->color('gray')
                                                ->outlined()
                                                ->visible(fn($record) => static::canEdit($record))
                                                ->button()
                                                ->extraAttributes([
                                                    'wire:key' => 'edit-2'
                                                ])
                                                ->action(function ($record, $data) {
                                                    //Activity Log Start
                                                    // $warehouse = $record->warehouses->first();
                                                    // $pcDetails = $record->pcDetails;
                                                    // $oldValues = [
                                                    //     'warehouse_type' => $warehouse->warehouse_type ?? null,
                                                    //     'address_1' => $warehouse->address_1 ?? null,
                                                    //     'address_2' => $warehouse->address_2 ?? null,
                                                    //     'state_id' => $warehouse->state_id ?? null,
                                                    //     'city_id' => $warehouse->city_id ?? null,
                                                    //     'postal_code' => $warehouse->postal_code ?? null,
                                                    //     'ware_region' => $warehouse->ware_region ?? null,
                                                    //     'delivery_days' => $pcDetails->delivery_days ?? null,
                                                    //     'delivery_days_west' => $pcDetails->delivery_days_west ?? null,
                                                    //     'min_order_value' => $pcDetails->min_order_value ?? null,
                                                    // ];
                                                    //Activity Log End

                                                    $warehousesData = Arr::except($data, ['pcDetails']);
                                                    $pcDetailsData = $data['pcDetails'] ?? [];
                                                    if ($data['warehouse_type'] === 'dpharma') {
                                                        $record->warehouses()->delete();
                                                        // $record->warehouses()->create($warehousesData);
                                                        $record->warehouses()->create([
                                                            'warehouse_type' => $data['warehouse_type'],
                                                            'address_1' => $data['address_1'] ?? null,
                                                            'address_2' => $data['address_2'] ?? null,
                                                            'state_id' => $data['state_id'] ?? null,
                                                            'city_id' => $data['city_id'] ?? null,
                                                            'postal_code' => $data['postal_code'] ?? null,
                                                            'ware_region' => $data['ware_region'] ?? null,

                                                        ]);
                                                        PcDetail::updateOrCreate(['user_id' => $record->id], [
                                                            'delivery_days' => null,
                                                            'delivery_days_west' => null,
                                                            'min_order_value' => $data['min_order_value'] ?? null,
                                                        ]);
                                                    } else {
                                                        $record->warehouses()->updateOrCreate(
                                                            ['user_id' => $record->warehouses()->first()->user_id],
                                                            [
                                                                "phone_number" => null,
                                                                "address_1" => null,
                                                                "address_2" => null,
                                                                "district" => null,
                                                                "state_id" => null,
                                                                "postal_code" => null,
                                                                "city_id" => null,
                                                                "ware_region" => null,
                                                                'warehouse_type' => 'owned'
                                                            ]
                                                        );

                                                        $record->pcDetails()->updateOrCreate(
                                                            ['user_id' => $record->id],
                                                            Arr::only($data, ['delivery_days', 'delivery_days_west', 'min_order_value'])
                                                        );
                                                    }

                                                    //Activity Log Start
                                                    // $newValues = [
                                                    //     'warehouse_type' => $data['warehouse_type'],
                                                    //     'address_1' => $data['address_1'] ?? null,
                                                    //     'address_2' => $data['address_2'] ?? null,
                                                    //     'state_id' => $data['state_id'] ?? null,
                                                    //     'city_id' => $data['city_id'] ?? null,
                                                    //     'postal_code' => $data['postal_code'] ?? null,
                                                    //     'ware_region' => $data['ware_region'] ?? null,
                                                    //     'delivery_days' => $data['warehouse_type'] === 'dpharma' ? null : ($data['delivery_days'] ?? null),
                                                    //     'delivery_days_west' => $data['warehouse_type'] === 'dpharma' ? null : ($data['delivery_days_west'] ?? null),
                                                    //     'min_order_value' => $data['min_order_value'] ?? null,
                                                    // ];

                                                    // $changedOld = [];
                                                    // $changedNew = [];
                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     if ($oldValue != $newValues[$key]) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValues[$key];
                                                    //     }
                                                    // }

                                                    // if (!empty($changedOld)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('warehouse_address')
                                                    //         ->performedOn($record) // Use $record as the subject, as it relates to the user
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Warehouse addresses have been updated for Pharmaceutical supplier '{$pcDetails->company_name}'");
                                                    // }
                                                    // Activity Log End
                                                    // if (!empty($pcDetailsData['delivery_days']) || !empty($pcDetailsData['delivery_days_west'])) {
                                                    //     $record->pcDetails()->updateOrCreate(
                                                    //         ['user_id' => $record->id],
                                                    //         Arr::only($pcDetailsData, ['delivery_days', 'delivery_days_west'])
                                                    //     );
                                                    // }
                                                })
                                                ->form(function () use ($record) {
                                                    $address = $record->warehouses->first()?->toArray();
                                                    $deliveryDays = $record->pcDetails ?? '';
                                                    return [
                                                        Group::make()->schema([
                                                            Radio::make('warehouse_type')
                                                                ->formatStateUsing(function () use ($address) {
                                                                    return $address['warehouse_type'] ?? null;
                                                                })
                                                                ->rules(['required'])
                                                                ->required()
                                                                ->validationMessages([
                                                                    'required' => 'Warehouse Type is required',
                                                                ])
                                                                ->label('Logistics Type')
                                                                ->options([
                                                                    'owned' => new \Illuminate\Support\HtmlString(
                                                                        'Own Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with your own logistics,Dpharma incurs no extra costs,and you handle product delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                                                    ),
                                                                    'dpharma' => new \Illuminate\Support\HtmlString(
                                                                        'DPharma Logistics <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`with DPharma logistics,our delivery partner manages product pickup and delivery to facilities.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" /></svg>'
                                                                    ),
                                                                ])->live(),
                                                            Group::make()->schema([
                                                                TextInput::make('delivery_days_west')
                                                                    ->rules(['integer', 'min:1', 'max:7', 'required'])
                                                                    ->label(new HtmlString("ETA for West Malaysia (Working Days)<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                    ->extraAttributes([
                                                                        'inputmode' => 'numeric',
                                                                        'pattern' => '[0-9]*',
                                                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                    ])
                                                                    ->validationMessages([
                                                                        'required' => 'The Delivery Days is required.',
                                                                        'min' => 'The Delivery Days must be at least 1.',
                                                                        'max' => 'The Delivery Days may not be greater than 7.',
                                                                    ])->columnSpan(1)
                                                                    ->default($deliveryDays->delivery_days_west ?? ''),
                                                            ])->visible(function (Get $get) {
                                                                return $get('warehouse_type') === 'owned';
                                                            }),
                                                            Group::make()->schema([
                                                                TextInput::make('delivery_days')
                                                                    ->rules(['integer', 'min:1', 'max:7', 'required'])
                                                                    ->label(new HtmlString("ETA for East Malaysia (Working Days)<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                    ->extraAttributes([
                                                                        'inputmode' => 'numeric',
                                                                        'pattern' => '[0-9]*',
                                                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                    ])
                                                                    ->validationMessages([
                                                                        'required' => 'The Delivery Days is required.',
                                                                        'min' => 'The Delivery Days must be at least 1.',
                                                                        'max' => 'The Delivery Days may not be greater than 7.',
                                                                    ])->columnSpan(1)
                                                                    ->default($deliveryDays->delivery_days ?? ''),
                                                                TextInput::make('min_order_value')
                                                                    ->rules(['required', 'numeric', 'min:1', 'max:100000'])
                                                                    ->validationAttribute('min order value')
                                                                    ->label(new HtmlString("Minimum Order Value(Ringgit Malaysia)<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                    ->extraAttributes([
                                                                        'inputmode' => 'decimal',
                                                                        'pattern' => '^\d+(?:\.\d+)?$',
                                                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                                                        'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                                                    ])
                                                                    ->validationMessages([
                                                                        'required' => 'The Minimum Order Value is required.',
                                                                        'numeric' => 'The Minimum Order Value must be a decimal number.',
                                                                        'min' => 'The Minimum Order Value must be at least 1',
                                                                        'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                                                    ])->columnSpan(1)
                                                                    ->default($deliveryDays->min_order_value ?? ''),
                                                            ])->visible(function (Get $get) {
                                                                return $get('warehouse_type') === 'owned';
                                                            }),
                                                            Group::make()->schema([
                                                                TextInput::make('address_1')
                                                                    ->rules(['required', 'string', 'max:100'])
                                                                    ->label(fn() => new HtmlString('Address 1 <span style="color: red;">*</span>'))
                                                                    ->validationMessages([
                                                                        'required' => 'The Address 1 field is required.',
                                                                        'string' => 'The Address 1 must be a string.',
                                                                        'max' => 'The Address 1 may not be greater than 100 characters.',
                                                                    ])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['address_1'] ?? null;
                                                                    }),
                                                                TextInput::make('address_2')
                                                                    ->rules(['nullable', 'string', 'max:100'])
                                                                    ->validationMessages([
                                                                        'string' => 'The Address 2 must be a string.',
                                                                        'max' => 'The address Line 2 field must not be greater than 100 characters. ',

                                                                    ])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['address_2'] ?? null;
                                                                    })
                                                                    ->label('Address 2'),
                                                                Select::make('state_id')
                                                                    ->rules(['required'])
                                                                    ->label(fn() => new HtmlString('State <span style="color: red;">*</span>'))
                                                                    ->validationMessages([
                                                                        'required' => 'The State field is required.',
                                                                    ])
                                                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                                        $info = State::where('id', $get('state_id'))->first();

                                                                        if ($info) {
                                                                            $set('ware_region', ucfirst($info->zone));
                                                                        } else {
                                                                            $set('ware_region', null);
                                                                        }
                                                                        $set('city_id', null);
                                                                        $set('postal_code', null);
                                                                    })
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['state_id'] ?? null;
                                                                    })
                                                                    // ->label('State')
                                                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                                                    ->searchable()
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                            ->where('country_id', 132)
                                                                            ->pluck('name', 'id')
                                                                            ->toArray();
                                                                    })
                                                                    ->live(),
                                                                Select::make('city_id')
                                                                    ->rules(['required'])
                                                                    ->label(fn() => new HtmlString('City <span style="color: red;">*</span>'))
                                                                    ->validationMessages([
                                                                        'required' => 'The City field is required.',
                                                                    ])
                                                                    ->live()
                                                                    ->searchable()
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                            ->where('state_id', $get('state_id'))
                                                                            ->pluck('name', 'id')
                                                                            ->toArray();
                                                                    })
                                                                    ->afterStateUpdated(function (Set $set) {
                                                                        $set('postal_code', null); // Reset postal code when city changes
                                                                    })
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['city_id'] ?? null;
                                                                    })
                                                                    // ->label('City')
                                                                    ->options(function (Get $get) {
                                                                        if (! empty($get('state_id'))) {
                                                                            return City::where('state_id', $get('state_id'))->pluck('name', 'id')->toArray();
                                                                        }

                                                                        return [];
                                                                    }),
                                                                Select::make('postal_code')
                                                                    ->label(new HtmlString('Postal code <span style="color:red">*</span>'))
                                                                    ->rules(['required'])
                                                                    ->validationMessages([
                                                                        'required' => 'The Postal code field is required.',
                                                                    ])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['postal_code'] ?? null;
                                                                    })
                                                                    ->placeholder('Select postal code')
                                                                    ->options(function (Get $get) {

                                                                        if (!empty($get('city_id'))) {

                                                                            return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                                                        }
                                                                        return [];
                                                                    })
                                                                    ->live()
                                                                    ->live(onBlur: true)
                                                                    ->loadingMessage('Loading postal code...')
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        if ($get('city_id')) {
                                                                            return ZipCode::where('city_id', $get('city_id'))
                                                                                ->where('code', 'like', "%{$search}%")
                                                                                ->pluck('code', 'code')
                                                                                ->toArray();
                                                                        }
                                                                        return [];
                                                                    })
                                                                    ->optionsLimit(100)
                                                                    ->searchable(),
                                                                // TextInput::make('postal_code')
                                                                //     ->validationAttribute('postal code')
                                                                //     ->live()
                                                                //     ->rules(['required', 'string', 'max:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                                                //     ->label(fn() => new HtmlString('Postal Code <span style="color: red;">*</span>'))
                                                                //     ->validationMessages([
                                                                //         'required' => 'The Postal Code is required.',
                                                                //     ])
                                                                //     ->formatStateUsing(function () use ($address) {
                                                                //         return $address['postal_code'] ?? null;
                                                                //     })
                                                                //     ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                //     ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                                                //     ->extraAttributes([
                                                                //         'inputmode' => 'numeric',
                                                                //         'pattern' => '[0-9]*',
                                                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                //     ]),
                                                                // TextInput::make('pcDetails.delivery_days')
                                                                //     ->rules(['integer', 'min:1', 'max:30', 'required'])
                                                                //     ->label(new HtmlString("ETA for East Malaysia <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                //     ->extraAttributes([
                                                                //         'inputmode' => 'numeric',
                                                                //         'pattern' => '[0-9]*',
                                                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                //     ])
                                                                //     ->validationMessages([
                                                                //         'required' => 'The Delivery Days is required.',
                                                                //         'min' => 'The Delivery Days must be at least 1.',
                                                                //         'max' => 'The Delivery Days may not be greater than 30.',
                                                                //     ])
                                                                //     ->formatStateUsing(function () use ($deliveryDays) {
                                                                //         return $deliveryDays['delivery_days'] ?? null;
                                                                //     }),
                                                                // TextInput::make('pcDetails.delivery_days_west')
                                                                //     ->validationAttribute('delivery days')
                                                                //     ->rules(['integer', 'min:1', 'max:30', 'required'])
                                                                //     ->label(new HtmlString("ETA for West Malaysia <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                //     ->extraAttributes([
                                                                //         'inputmode' => 'numeric',
                                                                //         'pattern' => '[0-9]*',
                                                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                //     ])
                                                                //     ->validationMessages([
                                                                //         'required' => 'The Delivery Days is required.',
                                                                //         'min' => 'The Delivery Days must be at least 1.',
                                                                //         'max' => 'The Delivery Days may not be greater than 30.',
                                                                //     ])
                                                                //     ->formatStateUsing(function () use ($deliveryDays) {
                                                                //         return $deliveryDays['delivery_days_west'] ?? null;
                                                                //     }),
                                                                // ->label('Postal Code'),
                                                                // Select::make('ware_region')->label('Region')
                                                                //     ->options([
                                                                //         'east' => 'East Region',
                                                                //         'west' => 'West Region',
                                                                //     ])
                                                                //     ->reactive()
                                                                //     ->formatStateUsing(function () use ($address, $record) {
                                                                //         return $address['ware_region'] ?? null;
                                                                //     })
                                                                //     ->placeholder('Enter region'),
                                                                TextInput::make('ware_region')
                                                                    ->label('Region')
                                                                    ->default($address['ware_region'] ?? '-')
                                                                    ->placeholder('Enter region')->readonly(),

                                                                // TextInput::make('min_order_value')
                                                                //     ->rules(['regex:/^\d+(\.\d{1,2})?$/', 'numeric', 'min:1', 'max:100000', 'required'])
                                                                //     ->validationAttribute('min order value')
                                                                //     ->label(new HtmlString("Minimum Order Value(Ringgit Malaysia)<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                                //     ->extraAttributes([
                                                                //         'inputmode' => 'decimal',
                                                                //         'pattern' => '^\d+(?:\.\d+)?$',
                                                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9.]$/.test(event.key)) event.preventDefault();',
                                                                //         'oninput' => 'this.value = this.value.replace(/[^0-9.]/g, "")'
                                                                //     ])
                                                                //     ->validationMessages([
                                                                //         'required' => 'The Minimum Order Value is required.',
                                                                //         'numeric' => 'The Minimum Order Value must be a decimal number.',
                                                                //         'min' => 'The Minimum Order Value must be at least 1.',
                                                                //         'max' => 'The Minimum Order Value may not be greater than 100,000.',
                                                                //     ])->columnSpan(1)
                                                                //     ->default($deliveryDays->min_order_value ?? ''),
                                                            ])->columns(3)
                                                                ->visible(function (Get $get) {
                                                                    return $get('warehouse_type') === 'dpharma';
                                                                })
                                                                ->columnSpanFull()
                                                        ])->columns(3)
                                                    ];
                                                }),

                                            static::makeAcceptAction(OnboardingStep::ADDRESS->value),
                                            static::makeRejectAction(OnboardingStep::ADDRESS->value),
                                        ])
                                        ->schema([
                                            InfoGroup::make()
                                                ->schema(function () use ($record, $stepTwoChanges) {
                                                    $address = $record->warehouses->first()?->toArray();
                                                    $deliveryDays = $record->pcDetails ? $record->pcDetails->toArray() : [];

                                                    if (isset($address['warehouse_type']) && $address['warehouse_type'] === 'owned') {
                                                        return [
                                                            TextEntry::make('warehouse_type')
                                                                ->formatStateUsing(fn() => 'Own Logistics')
                                                                ->default($address['warehouse_type'] ?? "")
                                                                ->label('Logistics Type'),
                                                            TextEntry::make('delivery_days')
                                                                ->default(isset($deliveryDays['delivery_days']) ? $deliveryDays['delivery_days'] . ($deliveryDays['delivery_days'] > 1 ? ' Days' : ' Day') : '-')
                                                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                    return static::formatWithPendingChanges(
                                                                        $state ?? '-',
                                                                        $stepTwoChanges['delivery_days'] ?? null
                                                                    );
                                                                })
                                                                ->label('ETA for East Malaysia (Working Days)'),
                                                            TextEntry::make('delivery_days_west')
                                                                ->default(isset($deliveryDays['delivery_days_west']) ? $deliveryDays['delivery_days_west'] . ($deliveryDays['delivery_days_west'] > 1 ? ' Days' : ' Day') : '-')
                                                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                    return static::formatWithPendingChanges(
                                                                        $state ?? '-',
                                                                        $stepTwoChanges['delivery_days_west'] ?? null
                                                                    );
                                                                })
                                                                ->label('ETA for West Malaysia (Working Days)'),
                                                            TextEntry::make('min_order_value')
                                                                ->default(!empty($deliveryDays['min_order_value']) ? $deliveryDays['min_order_value'] : '-')
                                                                ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                    return static::formatWithPendingChanges(
                                                                        $state ?? '-',
                                                                        $stepTwoChanges['min_order_value'] ?? null
                                                                    );
                                                                })
                                                                ->label('Minimum Order Value (Ringgit Malaysia)'),
                                                        ];
                                                    }
                                                    return [
                                                        TextEntry::make('warehouse_type')
                                                            ->default(ucfirst($address['warehouse_type'] ?? "-"))
                                                            ->label('Logistics Type'),
                                                        TextEntry::make('address_1')
                                                            ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                return static::formatWithPendingChanges(
                                                                    $state ?? '-',
                                                                    $stepTwoChanges['warehouse_address_1'] ?? null
                                                                );
                                                            })
                                                            ->default($address['address_1'] ?? "-")
                                                            ->label('Address 1'),
                                                        TextEntry::make('address_2')
                                                            ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                return static::formatWithPendingChanges(
                                                                    $state ?? '-',
                                                                    $stepTwoChanges['warehouse_address_2'] ?? null
                                                                );
                                                            })
                                                            ->default($address['address_2'] ?? "-")
                                                            ->label('Address 2'),
                                                        TextEntry::make('state.name')
                                                            // ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                            //     return static::formatWithPendingChanges(
                                                            //         $state ?? '-',
                                                            //         $stepTwoChanges['warehouse_state'] ?? null
                                                            //     );
                                                            // })
                                                            // ->formatStateUsing(function ($state, $record) use ($stepTwoChanges) {
                                                            //     $output = " $state <br>";

                                                            //     if (isset($stepTwoChanges['warehouse_state'])) {
                                                            //         foreach ($stepTwoChanges['warehouse_state'] as $change) {
                                                            //             $stateName = State::find($change['new_value'])?->name;
                                                            //             if ($stateName) {
                                                            //                 $output .= "<span style='background-color: yellow;'>{$stateName}</span><br>";
                                                            //             }
                                                            //         }
                                                            //     }

                                                            //     return new HtmlString($output);
                                                            // })
                                                            ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                return static::formatRelationshipWithPendingChanges(
                                                                    $state,  // No need for null coalesce here - handled in function
                                                                    $stepTwoChanges['warehouse_state'] ?? null,
                                                                    State::class
                                                                );
                                                            })
                                                            ->default($address['state']['name'] ?? "-")
                                                            ->label('State'),
                                                        TextEntry::make('city.name')
                                                            // ->formatStateUsing(function ($state, $record) use ($stepTwoChanges) {
                                                            //     $output = " $state <br>";

                                                            //     if (isset($stepTwoChanges['warehouse_city'])) {
                                                            //         foreach ($stepTwoChanges['warehouse_city'] as $change) {
                                                            //             $cityName = City::find($change['new_value'])?->name;
                                                            //             if ($cityName) {
                                                            //                 $output .= "<span style='background-color: yellow;'>{$cityName}</span><br>";
                                                            //             }
                                                            //         }
                                                            //     }

                                                            //     return new HtmlString($output);
                                                            // })
                                                            ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                return static::formatRelationshipWithPendingChanges(
                                                                    $state,  // No need for null coalesce here - handled in function
                                                                    $stepTwoChanges['warehouse_city'] ?? null,
                                                                    City::class
                                                                );
                                                            })
                                                            ->default($address['city']['name'] ?? "-")
                                                            ->label('City'),

                                                        TextEntry::make('postal_code')
                                                            ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                                return static::formatWithPendingChanges(
                                                                    $state ?? '-',
                                                                    $stepTwoChanges['warehouse_postal_code'] ?? null
                                                                );
                                                            })
                                                            ->default($address['postal_code'] ?? "-"),
                                                        TextEntry::make('region')
                                                            ->default(isset($address['ware_region']) ? ucfirst($address['ware_region']) : null),
                                                        // TextEntry::make('delivery_days')
                                                        //     ->label('ETA for East Malaysia')
                                                        //     ->default(isset($deliveryDays['delivery_days']) ? $deliveryDays['delivery_days'] . ($deliveryDays['delivery_days'] > 1 ? ' Days' : ' Day') : ''),
                                                        // TextEntry::make('delivery_days_west')
                                                        //     ->label('ETA for West Malaysia')
                                                        //     ->default(isset($deliveryDays['delivery_days_west']) ? $deliveryDays['delivery_days_west'] . ($deliveryDays['delivery_days_west'] > 1 ? ' Days' : ' Day') : ''),
                                                        // TextEntry::make('min_order_value')
                                                        //     ->default(!empty($deliveryDays['min_order_value']) ? $deliveryDays['min_order_value'] : '-')
                                                        //     ->label('Minimum Order Value (Ringgit Malaysia)'),
                                                        //     ->label('Postal Code'),
                                                        // TextEntry::make('phone_number')
                                                        //     ->default($address['phone_number'] ?? "")
                                                        //     ->label('Phone Number'),


                                                        // TextEntry::make('delivery_days')
                                                        //     ->default(isset($deliveryDays['delivery_days']) ? $deliveryDays['delivery_days'] . ($deliveryDays['delivery_days'] > 1 ? ' Days' : ' Day') : '')
                                                        //     ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                        //         return static::formatWithPendingChanges(
                                                        //             $state ?? '-',
                                                        //             $stepTwoChanges['delivery_days'] ?? null
                                                        //         );
                                                        //     })
                                                        //     ->label('ETA for East Malaysia'),
                                                        // TextEntry::make('delivery_days_west')
                                                        //     ->default(isset($deliveryDays['delivery_days_west']) ? $deliveryDays['delivery_days_west'] . ($deliveryDays['delivery_days_west'] > 1 ? ' Days' : ' Day') : '')
                                                        //     ->formatStateUsing(function ($state) use ($stepTwoChanges) {
                                                        //         return static::formatWithPendingChanges(
                                                        //             $state ?? '-',
                                                        //             $stepTwoChanges['delivery_days_west'] ?? null
                                                        //         );
                                                        //     })
                                                        //     ->label('ETA for West Malaysia'),
                                                    ];
                                                })->columns(4)

                                        ]),

                                    InfoSection::make('Contact Details')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->visible(fn($record) => static::canEdit($record))
                                                ->extraAttributes([
                                                    'wire:key' => 'edit-3'
                                                ])
                                                ->icon('heroicon-o-pencil')
                                                ->action(function ($record, $data) {
                                                    // --- Fix: Ensure landline_code is set based on city/state selection ---
                                                    
                                                    // Get the selected city and state
                                                    $cityId = $data['city_id'] ?? null;
                                                    $stateId = $data['state_id'] ?? null;

                                                    // Determine the correct landline code based on city/state
                                                    $landlineCode = null;
                                                    if (!empty($cityId)) {
                                                        $city = City::find($cityId);
                                                        if ($city && !empty($city->landline_code)) {
                                                            $landlineCode = $city->landline_code;
                                                        }
                                                    }
                                                    // If not found by city, try by state (get first city with code in state)
                                                    if (empty($landlineCode) && !empty($stateId)) {
                                                        $cityWithCode = City::where('state_id', $stateId)
                                                            ->whereNotNull('landline_code')
                                                            ->where('landline_code', '!=', '')
                                                            ->first();
                                                        if ($cityWithCode) {
                                                            $landlineCode = $cityWithCode->landline_code;
                                                        }
                                                    }
                                                    // Fallback to user input if still not found
                                                    if (empty($landlineCode)) {
                                                        $landlineCode = $data['landline_number']['prefix'] ?? null;
                                                    }

                                                    $landlineNumber = $data['landline_number']['number'] ?? null;

                                                    $addressesData = array_merge($data ?? [], [
                                                        'country_id' => 132,
                                                        'is_onboarding' => true,
                                                        'landline_number' => $landlineNumber,
                                                        'landline_code' => $landlineCode,
                                                    ]);

                                                    $pcDetailsData = [
                                                        'phone_number' => $data['phone_number'] ?? null,
                                                        'profile_email' => $data['profile_email'] ?? null,
                                                        'web_url' => $data['web_url'] ?? null,
                                                        'region' => $data['region'] ?? null,
                                                    ];
                                                    unset($addressesData['phone_number'], $addressesData['profile_email'], $addressesData['web_url']);

                                                    $record->addresses()->delete();
                                                    $record->addresses()->create($addressesData);
                                                    $record->pcDetails()->updateOrCreate([], $pcDetailsData);

                                                    Notification::make()
                                                        ->title('Changes updated successfully')
                                                        ->success()
                                                        ->send();
                                                    return redirect(UserResource::getUrl('view', ['record' => $record]));
                                                })
                                                ->form(function () use ($record) {
                                                    $address = $record->addresses->first()?->toArray();
                                                    return [
                                                        Group::make()->schema([
                                                            Group::make()->schema([
                                                                TextInput::make('address_1')
                                                                    ->label(fn() => new HtmlString('Address 1 <span style="color: red;">*</span>'))
                                                                    ->rules(['required', 'string', 'max:100'])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['address_1'] ?? null;
                                                                    })
                                                                    ->validationMessages([
                                                                        'required' => 'The Address 1 is required.',
                                                                        'max' => 'The Address 1 must not be greater than 100 characters.',
                                                                    ]),
                                                                TextInput::make('address_2')
                                                                    ->label(fn() => new HtmlString('Address 2 '))
                                                                    ->rules(['nullable', 'string', 'max:100'])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['address_2'] ?? null;
                                                                    })
                                                                    ->validationMessages([
                                                                        'max' => 'The Address 2 must not be greater than 100 characters.',
                                                                    ]),
                                                                Select::make('state_id')
                                                                    ->rules(['required'])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['state_id'] ?? null;
                                                                    })
                                                                    ->label(fn() => new HtmlString('State <span style="color: red;">*</span>'))
                                                                    ->validationMessages([
                                                                        'required' => 'The State field is required.',
                                                                    ])
                                                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                                        if (empty($state)) {
                                                                            $set('region', null);
                                                                            $set('city_id', null);
                                                                            $set('postal_code', null);
                                                                            return;
                                                                        }
                                                                        $info = State::where('id', $get('state_id'))->first();
                                                                        if ($info) {
                                                                            $set('region', ucfirst($info->zone));
                                                                        }
                                                                        $set('city_id', null);
                                                                        // $set('region', null);

                                                                        // --- Fix: Update landline_code when state changes ---
                                                                        // Try to get first city with landline_code in this state
                                                                        $cityWithCode = City::where('state_id', $get('state_id'))
                                                                            ->whereNotNull('landline_code')
                                                                            ->where('landline_code', '!=', '')
                                                                            ->first();
                                                                        if ($cityWithCode) {
                                                                            $set('landline_number.prefix', $cityWithCode->landline_code);
                                                                        } else {
                                                                            $set('landline_number.prefix', '');
                                                                        }
                                                                    })
                                                                    ->searchable()
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                            ->where('country_id', 132)
                                                                            ->pluck('name', 'id')
                                                                            ->toArray();
                                                                    })
                                                                    ->live(),
                                                                Select::make('city_id')
                                                                    ->rules(['required'])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['city_id'] ?? null;
                                                                    })
                                                                    ->live(onBlur: true)
                                                                    ->searchable()
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                            ->where('state_id', $get('state_id'))
                                                                            ->pluck('name', 'id')
                                                                            ->toArray();
                                                                    })
                                                                    ->afterStateUpdated(function (Set $set, Get $get, $state) {
                                                                        $set('postal_code', null); // Reset postal code when city changes

                                                                        // --- Fix: Update landline_code when city changes ---
                                                                        $city = City::find($state);
                                                                        if ($city && !empty($city->landline_code)) {
                                                                            $set('landline_number.prefix', $city->landline_code);
                                                                        } else {
                                                                            // fallback: try state
                                                                            $stateId = $get('state_id');
                                                                            $cityWithCode = City::where('state_id', $stateId)
                                                                                ->whereNotNull('landline_code')
                                                                                ->where('landline_code', '!=', '')
                                                                                ->first();
                                                                            if ($cityWithCode) {
                                                                                $set('landline_number.prefix', $cityWithCode->landline_code);
                                                                            } else {
                                                                                $set('landline_number.prefix', '');
                                                                            }
                                                                        }
                                                                    })
                                                                    ->label(fn() => new HtmlString('City <span style="color: red;">*</span>'))
                                                                    ->validationMessages([
                                                                        'required' => 'The City field is required.',
                                                                    ])
                                                                    ->options(function (Get $get) {
                                                                        return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                                                                    }),

                                                                Select::make('postal_code')
                                                                    ->label(new HtmlString('Postal code <span style="color:red">*</span>'))
                                                                    ->rules(['required'])
                                                                    ->validationMessages([
                                                                        'required' => 'The Postal code field is required.',
                                                                    ])
                                                                    ->formatStateUsing(function () use ($address) {
                                                                        return $address['postal_code'] ?? null;
                                                                    })
                                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                        if ($get('city_id')) {
                                                                            return ZipCode::where('city_id', $get('city_id'))
                                                                                ->where('code', 'like', "%{$search}%")
                                                                                ->pluck('code', 'code')
                                                                                ->toArray();
                                                                        }
                                                                        return [];
                                                                    })
                                                                    ->placeholder('Select postal code')
                                                                    ->options(function (Get $get) {
                                                                        if (!empty($get('city_id'))) {
                                                                            return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                                                        }
                                                                        return [];
                                                                    })
                                                                    ->live()
                                                                    ->live(onBlur: true)
                                                                    ->optionsLimit(100)
                                                                    ->loadingMessage('Loading postal code...')
                                                                    ->searchable(),
                                                                TextInput::make('phone_number')
                                                                    ->formatStateUsing(function () use ($address, $record) {
                                                                        return $record->pcDetails->phone_number ?? null;
                                                                    })->prefix('+60')
                                                                    ->mask('999999999999')
                                                                    ->stripCharacters(['-'])
                                                                    ->extraAttributes([
                                                                        'inputmode' => 'numeric',
                                                                        'maxlength' => '12'
                                                                    ])->live()
                                                                    ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                                    ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                                    ->validationMessages([
                                                                        'required' => 'The Mobile Number is required.',
                                                                        'max' => 'The Mobile Number must not be greater than 8 characters.',
                                                                        'regex' => 'The Mobile Number must be a valid mobile number.',
                                                                        'digits_between' => 'The Mobile Number must be between 8 and 12 digits.'
                                                                    ])
                                                                    ->rules(['required', 'digits_between:8,12'])
                                                                    ->label(fn() => new HtmlString('Mobile Number <span style="color: red;">*</span>')),

                                                                PhoneWithPrefix::make('landline_number')
                                                                    ->label("Landline Number")
                                                                    ->extraAttributes([
                                                                        'inputmode' => 'numeric',
                                                                        'maxlength' => '8'
                                                                    ])
                                                                    ->prefixOptions(function ($get, $set) {
                                                                        $stateId = $get('state_id');
                                                                        $cityId = $get('city_id');

                                                                        // Always show landline codes for selected city/state
                                                                        $query = City::whereNotNull('landline_code')
                                                                            ->where('landline_code', '!=', '');

                                                                        if ($stateId) {
                                                                            $query->where('state_id', $stateId);

                                                                            if ($cityId) {
                                                                                $query->where('id', $cityId);
                                                                            }
                                                                        }

                                                                        $data = $query
                                                                            ->distinct('landline_code')
                                                                            ->pluck('landline_code', 'landline_code')
                                                                            ->toArray();
                                                                        if (empty($data)) {
                                                                            $data = City::whereNotNull('landline_code')
                                                                                ->where('landline_code', '!=', '')
                                                                                ->distinct('landline_code')
                                                                                ->pluck('landline_code', 'landline_code')
                                                                                ->toArray();
                                                                        }
                                                                        // Set the prefix if available from city/state
                                                                        if ($cityId) {
                                                                            $city = City::find($cityId);
                                                                            if ($city && !empty($city->landline_code)) {
                                                                                $set('landline_number.prefix', $city->landline_code);
                                                                            }
                                                                        } elseif ($stateId) {
                                                                            $cityWithCode = City::where('state_id', $stateId)
                                                                                ->whereNotNull('landline_code')
                                                                                ->where('landline_code', '!=', '')
                                                                                ->first();
                                                                            if ($cityWithCode) {
                                                                                $set('landline_number.prefix', $cityWithCode->landline_code);
                                                                            }
                                                                        }
                                                                        return $data;
                                                                    })
                                                                    ->rules([new PhoneWithPrefixRule()])
                                                                    ->afterStateHydrated(function (Get $get, Set $set) use ($address) {
                                                                        // Set the prefix and number from address if available
                                                                        if (!empty($address)) {
                                                                            $set("landline_number.prefix", $address["landline_code"] ?? '');
                                                                            $set("landline_number.number", $address["landline_number"] ?? '');
                                                                        } else {
                                                                            $set("landline_number", ["prefix" => "", "number" => ""]);
                                                                        }
                                                                    })
                                                                    ->formatStateUsing(function ($state) use ($address) {
                                                                        if (is_array($address)) {
                                                                            if (isset($address['landline_code'])) {
                                                                                $state['prefix'] = $address['landline_code'];
                                                                            }
                                                                            if (isset($address['landline_number'])) {
                                                                                $state['number'] = $address['landline_number'];
                                                                            }
                                                                        } elseif (is_object($address)) {
                                                                            if (isset($address->landline_code)) {
                                                                                $state['prefix'] = $address->landline_code;
                                                                            }
                                                                            if (isset($address->landline_number)) {
                                                                                $state['number'] = $address->landline_number;
                                                                            }
                                                                        }
                                                                        return is_array($state) ? $state : ["prefix" => "", "number" => ""];
                                                                    })
                                                                    ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                    ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),

                                                                    TextInput::make('web_url')
                                                                        ->placeholder('Enter website url')
                                                                        ->default($record->pcDetails->web_url ?? '')
                                                                        ->nullable()->live()
                                                                        ->rules([
                                                                            'regex:/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/',
                                                                        ])
                                                                        ->helperText('Ex: https://www.example.com, http://example.com, www.example.com, example.com')
                                                                        ->suffixIcon(function ($state) {
                                                                            $isValid = $state
                                                                                && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                                                            if (!$isValid) {
                                                                                return null;
                                                                            }

                                                                            $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                                                            $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                                                            return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                                                                ? 'heroicon-s-check-circle' : null;
                                                                        })
                                                                        ->suffixIconColor(function ($state) {
                                                                            $isValid = $state
                                                                                && preg_match('/^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(\/[^\s]*)?$/', $state);

                                                                            if (!$isValid) {
                                                                                return null;
                                                                            }

                                                                            $host = parse_url((strpos($state, 'http') !== 0 ? 'http://' : '') . $state, PHP_URL_HOST);
                                                                            $tld = strtolower(array_slice(explode('.', $host ?? ''), -1)[0] ?? '');

                                                                            return in_array($tld, ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai', 'my'])
                                                                                ? 'success' : null;
                                                                        }),
                                                                        TextInput::make('region')
                                                                            ->default($record->pcDetails->region ?? '-')
                                                                            ->placeholder('Enter region')->readonly(),
                                                            ])
                                                                ->columns(3)
                                                        ])
                                                    ];
                                                }),
                                            static::makeAcceptAction(OnboardingStep::CONTACT->value),
                                            static::makeRejectAction(OnboardingStep::CONTACT->value),
                                        ])
                                        ->schema(function () use ($record, $stepFiveChanges) {
                                            $address = $record->addresses->first();
                                            return [
                                                TextEntry::make('addresses.address_1')
                                                    ->state($address->address_1 ?? '-')
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatWithPendingChanges(
                                                            $state ?? '-',
                                                            $stepFiveChanges['address_1'] ?? null
                                                        );
                                                    })
                                                    ->label('Street 1'),
                                                TextEntry::make('addresses.address_2')
                                                    ->state($address->address_2 ?? '-')
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatWithPendingChanges(
                                                            $state ?? '-',
                                                            $stepFiveChanges['address_2'] ?? null
                                                        );
                                                    })
                                                    ->label('Street 2'),
                                                TextEntry::make('addresses.city.name')
                                                    ->state($address->city->name ?? '-')
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatRelationshipWithPendingChanges(
                                                            $state,
                                                            $stepFiveChanges['city_id'] ?? null,
                                                            City::class
                                                        );
                                                    })
                                                    ->label('City'),
                                                TextEntry::make('addresses.state.name')
                                                    ->state($address->state->name ?? '-')
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatRelationshipWithPendingChanges(
                                                            $state,
                                                            $stepFiveChanges['state_id'] ?? null,
                                                            State::class
                                                        );
                                                    })
                                                    ->label('State'),
                                                TextEntry::make('addresses.country.name')
                                                    ->state($address->country->name ?? '-')
                                                    ->label('Country')
                                                    ->default('Malaysia'),
                                                TextEntry::make('postal_code')
                                                    ->default($address->postal_code ?? "-")
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatWithPendingChanges(
                                                            $state ?? '-',
                                                            $stepFiveChanges['postal_code'] ?? null
                                                        );
                                                    })
                                                    ->label('Postal Code'),
                                                TextEntry::make('phone_number')
                                                    ->prefix('+60 ')
                                                    ->default($record->pcDetails->phone_number ?? "-")
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                        return static::formatWithPendingChanges(
                                                            $state ?? '-',
                                                            $stepFiveChanges['phone_number'] ?? null
                                                        );
                                                    })
                                                    ->label('Mobile Number'),
                                                TextEntry::make('landline_number')
                                                    ->label('Landline Number')
                                                    ->default(
                                                        ($address?->landline_code ? '+' . $address->landline_code . ' ' : '') .
                                                        ($address?->landline_number ?? '-')
                                                    )
                                                    ->formatStateUsing(function ($state) use ($stepFiveChanges, $address) {
                                                        $currentValue = ($address?->landline_code ? '+' . $address->landline_code . ' ' : '') .
                                                            ($address?->landline_number ?? '-');

                                                        $landlineChanges = [
                                                            'number' => $stepFiveChanges['landline_number'] ?? null,
                                                            'code' => $stepFiveChanges['landline_code'] ?? null
                                                        ];

                                                        // Check if both code and number have changes
                                                        $hasNumberChange = isset($landlineChanges['number'][0]['new_value']);
                                                        $hasCodeChange = isset($landlineChanges['code'][0]['new_value']);

                                                        // If both code and number are changed, show only the combined new value
                                                        if ($hasNumberChange && $hasCodeChange) {
                                                            $newNumber = $landlineChanges['number'][0]['new_value'];
                                                            $newCode = $landlineChanges['code'][0]['new_value'];
                                                            $newCode = ltrim($newCode, '+');
                                                            $formatted = $newCode ? '+' . $newCode . ' ' . $newNumber : $newNumber;
                                                            
                                                            // Create a modified changes array with only the combined result
                                                            $combinedChanges = [
                                                                'number' => [
                                                                    [
                                                                        'new_value' => $formatted,
                                                                        'old_value' => $currentValue
                                                                    ]
                                                                ]
                                                            ];
                                                            
                                                            return static::formatLandLinePendingChanges(
                                                                $currentValue,
                                                                $combinedChanges
                                                            );
                                                        }

                                                        // Use the formatLandLinePendingChanges function for single field changes
                                                        return static::formatLandLinePendingChanges(
                                                            $currentValue,
                                                            $landlineChanges
                                                        );
                                                    }),
                                                    TextEntry::make('web_url')
                                                        ->label('Web URL')
                                                        ->default($record->pcDetails->web_url ?? '-')
                                                        ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                            return static::formatWithPendingChanges(
                                                                $state ?? '-',
                                                                $stepFiveChanges['web_url'] ?? null
                                                            );
                                                        }),
                                                    TextEntry::make('region')
                                                        ->default($record->pcDetails->region ?? '-')
                                                        ->label('Region')
                                                        ->formatStateUsing(function ($state) use ($stepFiveChanges) {
                                                            return static::formatWithPendingChanges(
                                                                $state ?? '-',
                                                                $stepFiveChanges['region'] ?? null
                                                            );
                                                        }),                                                   
                                            ];
                                        })->columns(4),

                                    InfoSection::make('Person In Charge')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->visible(fn($record) => static::canEdit($record))
                                                ->extraAttributes([
                                                    'wire:key' => 'edit-5'
                                                ])
                                                ->icon('heroicon-o-pencil')
                                                ->action(function ($record, $data) {
                                                    //Activity Log Start
                                                    // $pcDetails = $record->pcDetails;
                                                    // $oldValues = [
                                                    //     'person_in_charge_name' => $pcDetails->person_in_charge_name ?? null,
                                                    //     'person_in_charge_nric' => $pcDetails->person_in_charge_nric ?? null,
                                                    //     'person_in_charge_phone' => $pcDetails->person_in_charge_phone ?? null,
                                                    //     'person_in_charge_email' => $pcDetails->person_in_charge_email ?? null,
                                                    // ];
                                                    //Activity Log End

                                                    //This This Exiting Code but coz of log we use below code to get proper data
                                                    // $record->pcDetails->update($data['pcDetails'] ?? []); 

                                                    $pcDetailsData = $data['pcDetails'] ?? [];
                                                    $record->pcDetails->update($pcDetailsData);

                                                    //Activity Log Start
                                                    // $newValues = [
                                                    //     'person_in_charge_name' => $pcDetailsData['person_in_charge_name'] ?? null,
                                                    //     'person_in_charge_nric' => $pcDetailsData['person_in_charge_nric'] ?? null,
                                                    //     'person_in_charge_phone' => $pcDetailsData['person_in_charge_phone'] ?? null,
                                                    //     'person_in_charge_email' => $pcDetailsData['person_in_charge_email'] ?? null,
                                                    // ];

                                                    // $changedOld = [];
                                                    // $changedNew = [];
                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     if ($oldValue != $newValues[$key]) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValues[$key];
                                                    //     }
                                                    // }

                                                    // if (!empty($changedOld)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('person_in_charge')
                                                    //         ->performedOn($record)
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Person in charge details have been updated for Pharmaceutical supplier '{$pcDetails->company_name}'");
                                                    // }
                                                    //Activity Log End
                                                })
                                                ->form(function ($record) {
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('pcDetails.person_in_charge_name')
                                                                ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                                                ->placeholder('Enter the full name')
                                                                ->validationMessages([
                                                                    'regex' => 'The Full Name must be a alphabetical.',
                                                                    'max' => 'The Full Name may not be greater than 50 characters.',
                                                                ])
                                                                ->label(fn() => new HtmlString('Full Name'))
                                                                ->default($record->pcDetails->person_in_charge_name),
                                                            TextInput::make('pcDetails.person_in_charge_nric')
                                                                ->label(fn() => new HtmlString('NRIC / Passport Number'))
                                                                ->rules(function (Get $get) {
                                                                    return ['nullable', 'max:50'];
                                                                })
                                                                ->live()
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                                                // ->extraAttributes([
                                                                //     'inputmode' => 'numeric',
                                                                //     'pattern' => '[0-9]*',
                                                                //     'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "");',
                                                                //     'onkeypress' => 'return event.charCode >= 48 && event.charCode <= 57',
                                                                // ])
                                                                ->validationMessages([
                                                                    'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                                                ])
                                                                ->default($record->pcDetails->person_in_charge_nric),
                                                            TextInput::make('pcDetails.person_in_charge_phone')
                                                                ->label(fn() => new HtmlString('Mobile Number <span style="color: red;">*</span>'))
                                                                ->mask('999999999999')
                                                                ->prefix('+60')
                                                                ->stripCharacters(['-'])
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '12'
                                                                ])
                                                                ->rules(['required', 'digits_between:8,12'])
                                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                                ->live()
                                                                ->validationMessages([
                                                                    'required' => 'The Mobile Number field is required.',
                                                                    'max' => 'The Mobile Number may not be greater than 8 characters.',
                                                                    'regex' => 'The Mobile Number must be a valid mobile number.',
                                                                    'digits_between' => 'The Mobile Number must be 8 or 12 digits.',
                                                                ])
                                                                ->default($record->pcDetails->person_in_charge_phone),
                                                            TextInput::make('pcDetails.person_in_charge_email')
                                                                ->label(fn() => new HtmlString('Email <span style="color: red;">*</span>'))
                                                                ->rules(['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
                                                                ->validationMessages([
                                                                    'required' => 'The Email field is required.',
                                                                    'regex' => 'Please enter a valid email address.',

                                                                ])
                                                                ->default($record->pcDetails->person_in_charge_email),
                                                        ])->columns(3)
                                                    ];
                                                }),
                                            static::makeAcceptAction(OnboardingStep::PERSON_IN_CHARGE->value),
                                            static::makeRejectAction(OnboardingStep::PERSON_IN_CHARGE->value),
                                        ])
                                        ->schema([
                                            TextEntry::make('pcDetails.person_in_charge_name')
                                                ->default('-')
                                                ->label('Full Name')
                                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepSixChanges['person_in_charge_name'] ?? null
                                                    );
                                                }),
                                            TextEntry::make('pcDetails.person_in_charge_nric')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepSixChanges['person_in_charge_nric'] ?? null
                                                    );
                                                })
                                                ->label(new HtmlString('<span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">NRIC / Passport Number</span><x-heroicon-o-information-circle />')),
                                            TextEntry::make('pcDetails.person_in_charge_phone')
                                                ->default('-')
                                                ->prefix('+60')
                                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepSixChanges['person_in_charge_phone'] ?? null
                                                    );
                                                })
                                                ->label('Mobile Number'),
                                            TextEntry::make('pcDetails.person_in_charge_email')
                                                ->default('-')
                                                ->formatStateUsing(function ($state) use ($stepSixChanges) {
                                                    return static::formatWithPendingChanges(
                                                        $state ?? '-',
                                                        $stepSixChanges['person_in_charge_email'] ?? null
                                                    );
                                                })
                                                ->label('Email'),
                                        ])->columns(4),
                                    InfoSection::make('Bank Details')
                                        ->description(
                                            new \Illuminate\Support\HtmlString(' ⚠️ <strong>Note:</strong> 
                                            <span> Bank Related Information are secured , so Only Pharma Supplier can update it.</span> ')
                                        )
                                        ->schema([
                                            TextEntry::make('beneficiary_name')
                                                ->default(function () use ($record) {
                                                    $name = $record?->pcDetails?->beneficiary_name
                                                        ? decryptParam($record->pcDetails->beneficiary_name)
                                                        : null;

                                                    if (!$name) return '';
                                                    $len = strlen($name);
                                                    if ($len <= 4) {
                                                        return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                                    }
                                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($name, -4)));
                                                })

                                                // ->label('Beneficiary Name')
                                                // ->default(function () use ($record) {
                                                //     $name = $record?->pcDetails?->beneficiary_name ? decryptParam($record->pcDetails->beneficiary_name) : null;
                                                //     if (!$name) return '';
                                                //     $len = strlen($name);
                                                //     if ($len <= 4) {
                                                //         return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                                //     }
                                                //     return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($name, -4)));
                                                // })

                                                ->label('Beneficiary Name'),
                                            TextEntry::make('bank_name')
                                                ->default(function () use ($record) {
                                                    $bank = $record?->pcDetails?->bank_name
                                                        ? decryptParam($record->pcDetails->bank_name)
                                                        : null;

                                                    if (!$bank) return '';
                                                    $len = strlen($bank);
                                                    if ($len <= 4) {
                                                        // Wrap in <span style="letter-spacing:0.1em;"> to ensure stars are aligned horizontally
                                                        return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                                    }
                                                    // Also wrap masked part in a span for consistent alignment
                                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($bank, -4)));
                                                })
                                                ->label('Bank Name'),
                                            TextEntry::make('account_number')
                                                ->default(function () use ($record) {
                                                    $acc = $record?->pcDetails?->account_number
                                                        ? decryptParam($record->pcDetails->account_number)
                                                        : null;

                                                    if (!$acc) return '';
                                                    $len = strlen($acc);
                                                    if ($len <= 4) {
                                                        // Wrap in <span style="letter-spacing:0.1em;"> to ensure stars are aligned horizontally
                                                        return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                                    }
                                                    return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($acc, -4)));
                                                })
                                                ->label('Account Number'),
                                                // ->default(function () use ($record) {
                                                //     $bank = $record?->pcDetails?->bank_name ? decryptParam($record->pcDetails->bank_name) : null;
                                                //     if (!$bank) return '';
                                                //     $len = strlen($bank);
                                                //     if ($len <= 4) {
                                                //         // Wrap in <span style="letter-spacing:0.1em;"> to ensure stars are aligned horizontally
                                                //         return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                                //     }
                                                //     // Also wrap masked part in a span for consistent alignment
                                                //     return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($bank, -4)));
                                                // })
                                                // ->label('Bank Name'),
                                            // TextEntry::make('account_number')
                                            //     ->default(function () use ($record) {
                                            //         $acc = $record?->pcDetails?->account_number ? decryptParam($record->pcDetails->account_number) : null;
                                            //         if (!$acc) return '';
                                            //         $len = strlen($acc);
                                            //         if ($len <= 4) {
                                            //             // Wrap in <span style="letter-spacing:0.1em;"> to ensure stars are aligned horizontally
                                            //             return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len) . '</span>');
                                            //         }
                                            //         return new HtmlString('<span style="letter-spacing:0.1em;display:inline-block;">' . str_repeat('*', $len - 4) . '</span>' . e(substr($acc, -4)));
                                            //     })
                                            //     ->label('Account Number'),
                                        ])->columns(4),
                                    InfoSection::make('Uploaded Documents')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->visible(fn($record) => static::canEdit($record))
                                                ->extraAttributes([
                                                    'wire:key' => 'edit-6'
                                                ])
                                                ->icon('heroicon-o-pencil')
                                                ->action(function ($record, $data) {
                                                    // $record->pcDetails->update($data['pcDetails'] ?? []);
                                                    static::updateCertificateFiles($record, $data);
                                                    // Activity Log Start
                                                    // activity()
                                                    //     ->causedBy(auth()->user())
                                                    //     ->useLog('uploaded_documents')
                                                    //     ->performedOn($record)
                                                    //     ->log("Documents have been updated for Pharmaceutical supplier for '{$record->pcDetails->company_name}'");
                                                    // Activity Log End
                                                })
                                                ->form(function ($record) {

                                                    $certificateFiles = PcCertificateFile::where('user_id', $record->id)->where('status', 'active')->get()->groupBy('type');
                                                    $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();
                                                    $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
                                                    return [
                                                        Group::make()->schema([
                                                            FileUpload::make('pcDetails.license_permit')
                                                                ->multiple()
                                                                ->label(new HtmlString(
                                                                    'Relevant certification
                                                                        <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <circle cx="12" cy="12" r="10"></circle>
                                                                            <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                            <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                        </svg>'
                                                                ))
                                                                ->validationAttribute('License Permit')
                                                                ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                                                ->maxSize('2048')
                                                                ->maxFiles(3)
                                                                // ->required()
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.pc_medias') . $record->pcDetails->id;
                                                                })
                                                                ->validationMessages([
                                                                    'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                                    'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])
                                                                ->default(function () use ($licensePermitFiles, $record) {
                                                                    if (empty($licensePermitFiles)) {
                                                                        return null;
                                                                    }
                                                                    return array_map(function ($file) use ($record) {
                                                                        return config('constants.api.media.pc_medias') . $record->pcDetails->id . '/' . $file;
                                                                    }, $licensePermitFiles);
                                                                }),
                                                            // ->default($record->pcDetails->license_permit),
                                                            FileUpload::make('pcDetails.company_registration_certificate')
                                                                ->label(new HtmlString(
                                                                    'Company Registration Certificate 
                                                                        <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <circle cx="12" cy="12" r="10"></circle>
                                                                            <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                            <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                        </svg>'
                                                                ))
                                                                ->validationAttribute('Company Registration Certificate')
                                                                ->helperText('Supported formats: JPEG, PNG, PDF (Max 3 files, 2 MB each)')
                                                                ->maxFiles(3)
                                                                ->maxSize('2048')
                                                                ->required()
                                                                ->multiple()
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.pc_medias') . $record->pcDetails->id;
                                                                })
                                                                ->validationMessages([
                                                                    'mimes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                                    'mimetypes' => 'SVG files are not allowed. Only JPEG, PNG, and PDF files are accepted.',
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])
                                                                ->default(function () use ($companyCerts, $record) {
                                                                    if (empty($companyCerts)) {
                                                                        return null;
                                                                    }
                                                                    return array_map(function ($file) use ($record) {
                                                                        return config('constants.api.media.pc_medias') . $record->pcDetails->id . '/' . $file;
                                                                    }, $companyCerts);
                                                                })
                                                            // ->default($record->pcDetails->company_registration_certificate),
                                                        ])->columns(2)
                                                    ];
                                                }),
                                            static::makeAcceptAction(OnboardingStep::DOCUMENTS->value),
                                            static::makeRejectAction(OnboardingStep::DOCUMENTS->value),
                                        ])
                                        ->schema(function ($record) use ($stepThreeChanges) {
                                            // dd($stepThreeChanges);
                                            $certificateFiles = PcCertificateFile::where('user_id', $record->id)->where('status', 'active')->get()->groupBy('type');

                                            $licensePermitFiles = $certificateFiles->get('license_permit', collect())->pluck('name')->toArray();

                                            $companyCerts = $certificateFiles->get('company_registration_certificate', collect())->pluck('name')->toArray();
                                            return [
                                                ViewEntry::make('pcDetails.license_permit')
                                                    ->view('filament.admin.resources.user-resource.pages.pdf-image')
                                                    ->viewData([
                                                        'filePath' => $licensePermitFiles, //$record->pcDetails->license_permit ?? null,
                                                        'name' => 'license_permit',
                                                        'record' => $record,
                                                        'changes' => $stepThreeChanges['license_permit'] ?? [],
                                                    ])
                                                    ->label('Business License'),
                                                ViewEntry::make('pcDetails.company_registration_certificate')
                                                    ->view('filament.admin.resources.user-resource.pages.pdf-image')
                                                    ->viewData([
                                                        'filePath' => $companyCerts, //$record->pcDetails->company_registration_certificate ?? null,
                                                        'name' => 'company_registration_certificate',
                                                        'record' => $record,
                                                        'changes' => $stepThreeChanges['company_registration_certificate'] ?? [],
                                                    ])
                                                    ->label('Business Registration Certificate'),
                                            ];
                                        })->columns(2)
                                ]),
                            Tab::make('Products')
                                // ->extraAttributes(['x-on:click' => 'activeTab = 1'])
                                ->visible(function ($record, $livewire) {
                                    return $record->verification_status == 'approved' || $record->verification_status == null || $record->verification_status == 'pending';
                                })
                                ->extraAttributes([
                                    'x-data' => "{ activeTab: 'tab1' }",
                                    'x-on:click' => "console.log('changed')"
                                ])
                                ->schema([
                                    ComponentsLivewire::make(AdminProductList::class, ['id' => $record->id])
                                        ->key(fn($record) => "product-table-for-form-{$record->id}")
                                        ->columnSpanFull(),
                                    // RepeatableEntry::make('productRelations')
                                    //     ->key(function ($record) {
                                    //         return ['wire:key' => "product-price-{$record->products?->id}-" . uniqid()];
                                    //     })
                                    //     ->schema([
                                    //         InfoSection::make('Product Details')
                                    //             ->collapsible()
                                    //             ->visible(function ($record) {
                                    //                 return !empty($record->products);
                                    //             })
                                    //             ->heading(function ($record) {
                                    //                 return $record->products?->name;
                                    //             })
                                    //             ->schema([
                                    //                 InfoGroup::make()
                                    //                     ->schema([
                                    //                         TextEntry::make('products.category.name')->label('Category'),
                                    //                         TextEntry::make('products.subCategory.name')->label('Sub Category'),
                                    //                     ])
                                    //                     ->extraAttributes([
                                    //                         'class' => 'text-center'
                                    //                     ])
                                    //                     ->columns(2),
                                    //                 InfoGroup::make()
                                    //                     ->schema([
                                    //                         ComponentsLivewire::make(InfolistPrice::class)
                                    //                             ->key(fn($record) => "product-table-for-form-{$record->id}")
                                    //                             ->data(fn($record) => [$record->products?->productDataForPC($record->id)])
                                    //                             ->columnSpanFull(),

                                    //                     ])
                                    //             ])

                                    //     ])
                                ])
                        ])->columnSpanFull(),
                    // InfoSection::make('Supplier Details')
                    //     ->headerActions([
                    //         \Filament\Infolists\Components\Actions\Action::make('edit')
                    //             ->color('gray')
                    //             ->outlined()
                    //             ->button()
                    //             ->icon('heroicon-o-pencil')
                    //             ->form(function ($record) {
                    //                 return [
                    //                     Group::make()->schema([
                    //                         TextInput::make('pcDetails.business_name')->default($record->pcDetails->business_name)
                    //                             ->rules(function ($record) {
                    //                                 if (!empty($record->id)) {
                    //                                     return ['required', 'string', 'max:100', Rule::unique('pc_details', 'business_name')->ignore($record->id, 'user_id')];
                    //                                 }
                    //                                 return ['required', 'string', 'max:100', 'unique:pc_details,business_name'];
                    //                             }),
                    //                         TextInput::make('pcDetails.company_name')->default($record->pcDetails->company_name)
                    //                             ->rules(function ($record) {
                    //                                 if (!empty($record->id)) {
                    //                                     return ['required', 'string', 'max:100', Rule::unique('pc_details', 'company_name')->ignore($record->id, 'user_id')];
                    //                                 }
                    //                                 return ['required', 'string', 'max:100', 'unique:pc_details,company_name'];
                    //                             }),
                    //                         TextInput::make('pcDetails.company_registration_number')->default($record->pcDetails->company_registration_number)
                    //                             ->rules(function ($record) {
                    //                                 if (!empty($record->id)) {
                    //                                     return ['required', 'string', 'max:100', Rule::unique('pc_details', 'company_registration_number')->ignore($record->id, 'user_id')];
                    //                                 }
                    //                                 return ['required', 'string', 'max:100', 'unique:pc_details,company_registration_number'];
                    //                             }),
                    //                         TextInput::make('pcDetails.tin_number')->default($record->pcDetails->tin_number)
                    //                             ->rules(function ($record) {
                    //                                 if (!empty($record->id)) {
                    //                                     return ['required', 'string', 'max:100', Rule::unique('pc_details', 'tin_number')->ignore($record->id, 'user_id')];
                    //                                 }
                    //                                 return ['required', 'string', 'max:100', 'unique:pc_details,tin_number'];
                    //                             }),
                    //                         TextInput::make('pcDetails.sstc_number')->default($record->pcDetails->sstc_number)
                    //                             ->rules(function ($record) {
                    //                                 if (!empty($record->id)) {
                    //                                     return ['required', 'string', 'max:100', Rule::unique('pc_details', 'sstc_number')->ignore($record->id, 'user_id')];
                    //                                 }
                    //                                 return ['required', 'string', 'max:100', 'unique:pc_details,sstc_number'];
                    //                             }),
                    //                         TextInput::make('pcDetails.phone_number')->default($record->pcDetails->phone_number)
                    //                             ->rules(['required', 'string', 'max:15', 'regex:/^\+?[0-9]{5,15}$/']),
                    //                         TextInput::make('pcDetails.web_url')->default($record->pcDetails->web_url)
                    //                             ->url(),
                    //                     ])->columns(3)
                    //                 ];
                    //             })
                    //             ->action(function ($record, $data) {
                    //                 $record->pcDetails
                    //                     ->updateOrCreate(
                    //                         ['user_id' => $record->id],
                    //                         Arr::only($data['pcDetails'], ['business_name', 'company_name', 'company_registration_number', 'tin_number', 'sstc_number', 'phone_number', 'web_url'])
                    //                     );
                    //             })
                    //     ])
                    //     ->schema([
                    //         TextEntry::make('pcDetails.business_name')
                    //             ->label('Business Name'),
                    //         TextEntry::make('pcDetails.company_name')
                    //             ->label('Company Name'),
                    //         TextEntry::make('pcDetails.company_registration_number')
                    //             ->label('Company Registration Number'),
                    //         TextEntry::make('pcDetails.tin_number')
                    //             ->label('TIN Number'),
                    //         TextEntry::make('pcDetails.sstc_number')
                    //             ->label('SST Number'),
                    //         TextEntry::make('pcDetails.phone_number')
                    //             ->label('Phone Number'),
                    //         TextEntry::make('pcDetails.web_url')
                    //             ->label('Website Url')
                    //     ])->columns(4),

                    // InfoSection::make('Warehouse Addresses')
                    //     ->headerActions([
                    //         \Filament\Infolists\Components\Actions\Action::make('edit')
                    //             ->color('gray')
                    //             ->outlined()
                    //             ->button()
                    //             ->action(function ($record, $data) {
                    //                 $warehousesData = $data['warehouses'] ?? [];
                    //                 $record->warehouses()->delete();
                    //                 $record->warehouses()->createMany($warehousesData);
                    //             })
                    //             ->form(function () use ($record) {
                    //                 $address = $record->warehouses->first()?->toArray();
                    //                 return [
                    //                     Group::make()->schema([
                    //                         // Repeater::make('warehouses')
                    //                         // ->afterStateHydrated(function($state, Set $set) use($record){
                    //                         //     $set('warehouses', $record->warehouses->toArray());
                    //                         // })
                    //                         // ->schema([
                    //                         Group::make()->schema([
                    //                             TextInput::make('address_1')
                    //                                 ->formatStateUsing(function () use ($address) {
                    //                                     return $address['address_1'];
                    //                                 })
                    //                                 ->label('Address 1'),
                    //                             TextInput::make('address_2')
                    //                                 ->formatStateUsing(function () use ($address) {
                    //                                     return $address['address_2'];
                    //                                 })
                    //                                 ->label('Address 2'),
                    //                             Radio::make('warehouse_type')
                    //                                 ->formatStateUsing(function () use ($address) {
                    //                                     return $address['warehouse_type'];
                    //                                 })
                    //                                 ->label('Warehouse Type')
                    //                                 ->options([
                    //                                     'owned' => 'Owned',
                    //                                     'dpharma' => 'D Pharma',
                    //                                 ]),
                    //                             Select::make('state_id')
                    //                                 ->formatStateUsing(function () use ($address) {
                    //                                     return $address['state_id'];
                    //                                 })
                    //                                 ->label('State')
                    //                                 ->options(State::all()->pluck('name', 'id'))
                    //                                 ->searchable()
                    //                                 ->live(),
                    //                             Select::make('city_id')
                    //                                 ->formatStateUsing(function () use ($address) {
                    //                                     return $address['city_id'];
                    //                                 })
                    //                                 ->label('City')
                    //                                 ->options(function (Get $get) {
                    //                                     return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                    //                                 }),
                    //                             TextInput::make('postal_code')
                    //                                 ->label('Postal Code'),
                    //                             TextInput::make('phone_number')
                    //                                 ->label('Phone Number'),
                    //                         ])
                    //                             ->columns(3)
                    //                             // ])

                    //                             // ->orderColumn('sort')
                    //                             ->columnSpanFull()

                    //                     ])->columns(3)
                    //                 ];
                    //             })
                    //     ])
                    //     ->schema([
                    //         InfoGroup::make()
                    //             ->schema(function () use ($record) {
                    //                 $address = $record->warehouses->first()?->toArray();
                    //                 return [
                    //                     TextEntry::make('address_1')
                    //                         ->default($address['address_1'] ?? "")
                    //                         ->label('Address 1'),
                    //                     TextEntry::make('address_2')
                    //                         ->default($address['address_2'] ?? "")
                    //                         ->label('Address 2'),
                    //                     TextEntry::make('city.name')
                    //                         ->default($address['city']['name'] ?? "")
                    //                         ->label('City'),
                    //                     TextEntry::make('state.name')
                    //                         ->default($address['state']['name'] ?? "")
                    //                         ->label('State'),
                    //                     TextEntry::make('postal_code')
                    //                         ->default($address['postal_code'] ?? "")
                    //                         ->label('Postal Code'),
                    //                     TextEntry::make('phone_number')
                    //                         ->default($address['phone_number'] ?? "")
                    //                         ->label('Phone Number'),
                    //                     TextEntry::make('warehouse_type')
                    //                         ->default($address['warehouse_type'] ?? "")
                    //                         ->label('Logistics Type'),
                    //                 ];
                    //             })->columns(4)

                    //     ]),

                    // InfoSection::make('Contact Details')
                    //     ->headerActions([
                    //         \Filament\Infolists\Components\Actions\Action::make('edit')
                    //             ->color('gray')
                    //             ->outlined()
                    //             ->button()
                    //             ->icon('heroicon-o-pencil')
                    //             ->action(function ($record, $data) {
                    //                 $addressesData = $data['addresses'] ?? [];
                    //                 $record->addresses()->delete();
                    //                 $record->addresses()->createMany($addressesData);
                    //             })
                    //             ->form(function ($record) {
                    //                 return [
                    //                     Repeater::make('addresses')
                    //                         ->afterStateHydrated(function ($state, Set $set) use ($record) {
                    //                             $record = ($record->addresses->first());
                    //                             $set('addresses', $record ? [$record?->toArray()] : []);
                    //                         })
                    //                         ->schema([
                    //                             Group::make()->schema([
                    //                                 TextInput::make('address_1')
                    //                                     ->label('Address 1'),
                    //                                 TextInput::make('address_2')
                    //                                     ->label('Address 2'),
                    //                                 Select::make('state_id')
                    //                                     ->label('State')
                    //                                     ->options(State::all()->pluck('name', 'id'))
                    //                                     ->searchable()
                    //                                     ->live(),
                    //                                 Select::make('city_id')
                    //                                     ->label('City')
                    //                                     ->options(function (Get $get) {
                    //                                         return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                    //                                     }),
                    //                                 TextInput::make('postal_code')
                    //                                     ->label('Postal Code'),
                    //                                 TextInput::make('phone_number')
                    //                                     ->label('Phone Number'),
                    //                             ])
                    //                                 ->columns(3)
                    //                         ])
                    //                 ];
                    //             })
                    //     ])
                    //     ->schema(function () use ($record) {
                    //         $address = $record->addresses->first();
                    //         return [
                    //             TextEntry::make('addresses.address_1')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->address_1 ?? '';
                    //                 })
                    //                 ->label('Street 1'),
                    //             TextEntry::make('addresses.address_2')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->address_2 ?? '';
                    //                 })
                    //                 ->label('Street 2'),
                    //             TextEntry::make('addresses.city.name')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->city->name ?? '';
                    //                 })
                    //                 ->label('City'),
                    //             TextEntry::make('addresses.state.name')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->state->name ?? '';
                    //                 })
                    //                 ->label('State'),
                    //             TextEntry::make('addresses.country.name')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->country->name ?? '';
                    //                 })
                    //                 ->label('Country')
                    //                 ->default('Malasiya'),
                    //             TextEntry::make('addresses.postal_code')
                    //                 ->formatStateUsing(function ($state) use ($address) {
                    //                     return $address->postal_code ?? '';
                    //                 })
                    //                 ->label('Postal Code'),
                    //             TextEntry::make('addresses.phone_number')
                    //                 ->formatStateUsing(function ($record) {
                    //                     return $record->addresses->first()->phone_code . " " . $record->addresses->first()->phone_number;
                    //                 })
                    //                 ->label('Phone Number'),
                    //         ];
                    //     })->columns(4),

                    // InfoSection::make('Person In Charge')
                    //     ->headerActions([
                    //         \Filament\Infolists\Components\Actions\Action::make('edit')
                    //             ->color('gray')
                    //             ->outlined()
                    //             ->button()
                    //             ->icon('heroicon-o-pencil')
                    //             ->action(function ($record, $data) {
                    //                 $record->pcDetails->update($data['pcDetails'] ?? []);
                    //             })
                    //             ->form(function ($record) {
                    //                 return [
                    //                     Group::make()->schema([
                    //                         TextInput::make('pcDetails.person_in_charge_name')
                    //                             ->rules(['string', 'max:255'])
                    //                             ->default($record->pcDetails->person_in_charge_name),
                    //                         TextInput::make('pcDetails.person_in_charge_nric')
                    //                             ->label('NRIC')
                    //                             ->rules(['string', 'max:255'])
                    //                             ->default($record->pcDetails->person_in_charge_nric),
                    //                         TextInput::make('pcDetails.person_in_charge_phone')
                    //                             ->rules(['string', 'max:15', 'regex:/^[0-9]+$/'])
                    //                             ->default($record->pcDetails->person_in_charge_phone),
                    //                         TextInput::make('pcDetails.profile_email')
                    //                             ->rules(['email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
                    //                             ->default($record->pcDetails->profile_email),
                    //                     ])->columns(3)
                    //                 ];
                    //             })
                    //     ])
                    //     ->schema([
                    //         TextEntry::make('pcDetails.person_in_charge_name')
                    //             ->label('Full Name'),
                    //         TextEntry::make('pcDetails.person_in_charge_nric')
                    //             ->label(new HtmlString('<span class="text-sm font-medium leading-6 text-gray-950 dark:text-white">NRIC</span><x-heroicon-o-information-circle />')),
                    //         TextEntry::make('pcDetails.person_in_charge_phone')
                    //             ->formatStateUsing(function ($state) {
                    //                 return "+60" . " " . $state;
                    //             })
                    //             ->label('Pone Number'),
                    //         TextEntry::make('pcDetails.profile_email')
                    //             ->label('Email'),
                    //     ])->columns(4),

                    // InfoSection::make('Uploaded Documents')
                    //     ->headerActions([
                    //         \Filament\Infolists\Components\Actions\Action::make('edit')
                    //             ->color('gray')
                    //             ->outlined()
                    //             ->button()
                    //             ->icon('heroicon-o-pencil')
                    //             ->action(function ($record, $data) {
                    //                 $record->pcDetails->update($data['pcDetails'] ?? []);
                    //             })
                    //             ->form(function ($record) {
                    //                 return [
                    //                     Group::make()->schema([
                    //                         FileUpload::make('pcDetails.license_permit')->default($record->pcDetails->license_permit),
                    //                         FileUpload::make('pcDetails.company_registration_certificate')->default($record->pcDetails->company_registration_certificate),
                    //                     ])->columns(2)
                    //                 ];
                    //             })
                    //     ])
                    //     ->schema(function ($record) {
                    //         return [
                    //             ViewEntry::make('pcDetails.license_permit')
                    //                 ->view('filament.admin.resources.user-resource.pages.pdf-image')
                    //                 ->viewData([
                    //                     'filePath' => $record->pcDetails->license_permit,
                    //                 ])
                    //                 ->label('Business License'),
                    //             ViewEntry::make('pcDetails.license_permit')
                    //                 ->view('filament.admin.resources.user-resource.pages.pdf-image')
                    //                 ->viewData([
                    //                     'filePath' => $record->pcDetails->company_registration_certificate,
                    //                 ])
                    //                 ->label('Business Registration Certificate'),
                    //         ];
                    //     })->columns(2)

                ];
            });
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Wizard::make()
                ->previousAction(function ($action) {
                    return $action->label('Back')->color('create_button');
                })
                ->nextAction(function ($action) {
                    return $action->label('Save & Continue')->color('create_button');
                })
                ->schema([
                    Step::make('User Creation')
                        ->icon('heroicon-o-user-plus')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('name')
                                    ->label('Company Name')
                                    ->placeholder('Enter your name')
                                    ->rules(['required', 'max:100', 'string'])
                                    ->required(),
                                TextInput::make('email')
                                    ->placeholder('Enter your email')
                                    ->regex('/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/')
                                    ->rules(function (Get $get) {
                                        if (!empty($get('user_id'))) {
                                            return [
                                                'required',
                                                'email',
                                                'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                                'max:255',
                                                Rule::unique('users', 'email')->ignore($get('user_id'))
                                            ];
                                        }
                                        return [
                                            'required',
                                            'email',
                                            'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                            'max:255',
                                            'unique:users,email'
                                        ];
                                    })
                                    ->required(),
                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, Set $set, StoreProfileByAdminService $service) {
                            $user = User::updateOrCreate(['email' => $get('email')], ['name' => $get('name'), 'created_by' => auth()->user()->id]);
                            $user->assignRole('Pharmaceutical Company');
                            $set('user_id', $user->id);
                            return redirect()->to(self::getUrl('edit', ['record' => $user]));
                            // $data = [];
                            // $data['email'] = $get('email');
                            // $data['user_id'] = $get('user_id');
                            // $service->sendCredential($data);
                        }),
                    TextInput::make('user_id')->visible(false),
                    Step::make('Supplier Information')
                        ->icon('phosphor-building')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('business_name')
                                    ->placeholder('Enter business name')
                                    ->rules(function (Get $get) {
                                        if (!empty($get('user_id'))) {
                                            return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', Rule::unique('pc_details', 'business_name')->ignore($get('user_id'), 'user_id')];
                                        }
                                        return ['required', 'string', 'max:64', 'regex:/^[a-zA-Z\s]+$/', 'unique:pc_details,business_name'];
                                    })->validationMessages([
                                        'required' => 'The Business Name field is required.',
                                        'string' => 'The Business Name must be a string.',
                                        'regex' => 'The Business Name must be a alphabetical.',
                                        'max' => 'The Business Name may not be greater than 64 characters.',
                                    ])
                                    ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('business_name')) ? 'success' : null)
                                    ->live()
                                    ->required(),
                                Select::make('company_type_id')
                                    ->placeholder('Select company type')
                                    ->validationAttribute('Company Type')
                                    ->required()
                                    ->label('Company Type')
                                    // ->label(fn() => new HtmlString(
                                    //     'Company Type ' .
                                    //         view('components.heroicon-o-information-circle', ['class' => 'inline-block w-5 h-5 mt-1'])->render()
                                    // ))
                                    ->options(fn() => PcCompanyType::pluck('name', 'id')),
                                // TextInput::make('company_name')
                                //     ->placeholder('Enter company name')
                                //     ->rules(function (Get $get) {
                                //         if (!empty($get('user_id'))) {
                                //             return ['required', 'max:64', 'regex:/^[a-zA-Z\s]+$/', Rule::unique('pc_details', 'company_name')->ignore($get('user_id'), 'user_id')];
                                //         }
                                //         return ['required', 'max:64', 'regex:/^[a-zA-Z\s]+$/', 'unique:pc_details,company_name'];
                                //     })->validationMessages([
                                //         'required' => 'The Company Name field is required.',
                                //         'string' => 'The Company Name must be a string.',
                                //         'regex' => 'The Company Name must be a alphabetical.',
                                //         'max' => 'The Company Name may not be greater than 64 characters.',
                                //     ])
                                //     ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'heroicon-s-check-circle' : null)
                                //     ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('company_name')) ? 'success' : null)
                                //     ->live()
                                //     ->required(),
                                TextInput::make('company_name')
                                    ->placeholder('Enter company name')
                                    ->label('Company Name')
                                    ->readOnly(true)
                                // ->afterStateUpdated(function (Get $get, Set $set, $record) {
                                //     return User::Find($get('user_id'))->name;

                                // })
                                ,
                                TextInput::make('company_registration_number')
                                    ->placeholder('Enter company registration number')->live()
                                    ->suffixIcon(fn($state) => (strlen($state) <= 12 && strlen($state) >= 8 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => (strlen($state) <= 12 && strlen($state) >= 8 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                    ->rules(function (Get $get) {
                                        if (!empty($get('user_id'))) {
                                            return ['required', 'regex:/^[a-zA-Z0-9]+$/', 'min:8', 'max:12', Rule::unique('pc_details', 'company_registration_number')->ignore($get('user_id'), 'user_id')];
                                        }
                                        return ['required', 'regex:/^[a-zA-Z0-9]+$/', 'min:8', 'max:12', 'unique:pc_details,company_registration_number'];
                                    })
                                    ->validationMessages([
                                        'required' => 'The Company Registration Number field is required.',
                                        'regex' => 'The Company Registration Number must be an alphanumeric string.',
                                        'min' => 'The Company Registration Number must be at least 8 characters.',
                                        'max' => 'The Company Registration Number may not be greater than 12 characters.',
                                        'unique' => 'The Company Registration Number has already been taken.',
                                    ])
                                    ->required(),
                                TextInput::make('tin_number')
                                    ->placeholder('Enter tin number')
                                    ->label('TIN Number')
                                    ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0))  ? 'success' : null)
                                    ->live()
                                    ->rules(function (Get $get) {
                                        if (!empty($get('user_id'))) {
                                            return ['nullable', 'max:20', 'min:0', Rule::unique('pc_details', 'tin_number')->ignore($get('user_id'), 'user_id')];
                                        }
                                        return ['nullable', 'max:20', 'min:0', 'unique:pc_details,tin_number'];
                                    })
                                    ->validationMessages([
                                        'max' => 'The TIN number may not be greater than 20 characters.',
                                        'unique' => 'The TIN number has already been taken.',
                                    ]),
                                TextInput::make('sstc_number')
                                    ->placeholder('Enter sst number')
                                    ->live()
                                    ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0))  ? 'success' : null)
                                    ->label('SST Number')
                                    ->rules(function (Get $get) {
                                        if (!empty($get('user_id'))) {
                                            return ['nullable', 'max:20', 'min:0', Rule::unique('pc_details', 'sstc_number')->ignore($get('user_id'), 'user_id')];
                                        }
                                        return ['nullable', 'max:20', 'min:0', 'unique:pc_details,sstc_number'];
                                    })
                                    ->validationMessages([
                                        'max' => 'The SST number may not be greater than 20 characters.',
                                        'unique' => 'The SST number has already been taken.',
                                    ]),
                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, StoreProfileByAdminService $service) {
                            $data = [];
                            $data['business_name'] = $get('business_name');
                            $data['company_name'] = $get('name');
                            $data['company_registration_number'] = $get('company_registration_number');
                            $data['tin_number'] = $get('tin_number');
                            $data['sstc_number'] = $get('sstc_number');
                            $data['company_type_id'] = $get('company_type_id');
                            $data['user_id'] = $get('user_id');
                            $service->storeCompanyInformations($data, 1);
                        }),
                    Step::make('Contact Details')
                        ->icon('heroicon-o-phone')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('profile_email')
                                    ->placeholder('Enter email')
                                    ->regex("/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/")
                                    ->rules([
                                        'required',
                                        'email',
                                        'max:100'
                                    ])->required()
                                    ->label('Email')->suffixIcon(function ($state) {
                                        if (empty($state)) {
                                            return null;
                                        }
                                        return filter_var($state, FILTER_VALIDATE_EMAIL) ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(fn($state) => filter_var($state, FILTER_VALIDATE_EMAIL) ? 'success' : null)
                                    ->live(),
                                TextInput::make('address_1')
                                    ->placeholder('Enter address 1')
                                    ->rules(['required', 'string', 'max:100'])
                                    ->required(),
                                TextInput::make('address_2')
                                    ->placeholder('Enter address 2')
                                    ->rules(['nullable', 'string', 'max:100']),
                                Select::make('state_id')
                                    ->label('State')
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        $info = State::where('id', $get('state_id'))->first();
                                        if ($info) {
                                            $set('region', ucfirst($info->zone));
                                        }
                                        $set('city_id', null);
                                    })
                                    ->required()->live(),
                                Select::make('city_id')
                                    ->label('City')
                                    ->placeholder('Select City')
                                    ->loadingMessage('Loading cities...')
                                    ->searchable()
                                    ->live(onBlur: true)
                                    ->options(function (Get $get) {
                                        if (! empty($get('state_id'))) {
                                            return City::where('state_id', $get('state_id'))->pluck('name', 'id')->toArray();
                                        }

                                        return [];
                                    })
                                    ->rules(['required'])
                                    ->required(),
                                TextInput::make('region')->placeholder('Enter region')->disabled(),
                                TextInput::make('phone_number')
                                    ->placeholder('Enter phone number')
                                    ->prefix('+60')
                                    ->mask('99-99999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '10'
                                    ])
                                    ->rules(['required', 'digits_between:9,10'])
                                    ->required()
                                    ->suffixIcon(fn($state) => in_array(strlen($state), [10, 11]) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => in_array(strlen($state), [10, 11]) ? 'success' : null)
                                    ->live(), // Make it reactive,
                                TextInput::make('landline_number')->placeholder('Enter landline number')
                                    ->rules(['required', 'digits_between:7,8'])
                                    ->mask('9-9999999')
                                    ->prefix('+03')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '8'
                                    ])
                                    ->required()
                                    ->suffixIcon(fn($state) => in_array(strlen($state), [8, 9]) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => in_array(strlen($state), [8, 9]) ? 'success' : null)
                                    ->live(),
                                // TextInput::make('district')
                                //     ->rules(['required', 'string', 'max:50'])
                                //     ->required(),
                                TextInput::make('postal_code')
                                    ->placeholder('Enter postal code')
                                    ->required()
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    ])
                                    ->rules(['required', 'string', 'digits:5', 'regex:/^\+?[0-9]{5,5}$/'])->live()
                                    ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                TextInput::make('web_url')
                                    ->placeholder('Enter website url')->live()
                                    ->rules(['url', 'regex:/^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6}(\/[^\s]*)?$/',])
                                    ->nullable()
                                    ->suffixIcon(fn($state) => $state && filter_var($state, FILTER_VALIDATE_URL) && preg_match('/^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6}(\/[^\s]*)?$/', $state) && in_array(strtolower(array_slice(explode('.', parse_url($state, PHP_URL_HOST) ?? ''), -1)[0] ?? ''), ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai']) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => $state && filter_var($state, FILTER_VALIDATE_URL) && preg_match('/^(https?:\/\/)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,6}(\/[^\s]*)?$/', $state) && in_array(strtolower(array_slice(explode('.', parse_url($state, PHP_URL_HOST) ?? ''), -1)[0] ?? ''), ['com', 'org', 'net', 'edu', 'gov', 'mil', 'biz', 'info', 'io', 'co', 'app', 'ai']) ? 'success' : null)
                                    ->helperText('Ex: https://www.example.com, http://www.example.com')
                                    ->url(),

                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, StoreProfileByAdminService $service) {
                            $data = [];
                            $data['phone_number'] = $get('phone_number');
                            $data['landline_number'] = $get('landline_number');
                            $data['profile_email'] = $get('profile_email');
                            $data['address_1'] = $get('address_1');
                            $data['address_2'] = $get('address_2');
                            $data['district'] = $get('district');
                            $data['state_id'] = $get('state_id');
                            $data['city_id'] = $get('city_id');
                            $data['user_id'] = $get('user_id');
                            $data['postal_code'] = $get('postal_code');
                            $data['region'] = $get('region');
                            $data['web_url'] = $get('web_url');
                            $service->storeContactDetails($data, 2);
                        }),
                    Step::make('Warehouse Addresses')
                        ->icon('heroicon-s-building-storefront')
                        ->schema([
                            Radio::make('warehouse_type')->options([
                                'owned' => 'Own Logistics',
                                'dpharma' => 'DPharma Logistics',
                            ])
                                ->rules(['required'])
                                ->required()
                                ->inline()
                                ->label('')
                                ->validationMessages([
                                    'required' => 'Warehouse Type is required',
                                ])
                                ->default('dpharma'),
                            Group::make()->schema([
                                TextInput::make('ware_address_1')
                                    ->placeholder('Enter address 1')
                                    ->rules(['required', 'string', 'max:100', 'regex:/^[a-zA-Z0-9\s]+$/'])
                                    ->label('Address Line 1')
                                    ->validationMessages([
                                        'required' => 'Address Line 1 is required',
                                        'regex' => 'Address Line 1 should contain only letters and numbers',
                                        'max' => 'Address Line 1 is too long',

                                    ])
                                    ->required(),
                                TextInput::make('ware_address_2')
                                    ->placeholder('Enter address 2')
                                    ->rules(['nullable', 'string', 'max:100', 'regex:/^[a-zA-Z0-9\s]+$/'])
                                    ->validationMessages([
                                        'required' => 'Address Line 1 is required',
                                        'regex' => 'Address Line 1 should contain only letters and numbers',
                                        'max' => 'Address Line 1 is too long',

                                    ])
                                    ->label('Address Line 2'),
                                Select::make('ware_state_id')
                                    ->rules(['required'])
                                    ->required()
                                    ->label('State')
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        $info = State::where('id', $get('state_id'))->first();
                                        if ($info) {
                                            $set('ware_region', ucfirst($info->zone));
                                        }
                                        $set('ware_city_id', null);
                                    })
                                    ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())->required()->live(),
                                Select::make('ware_city_id')
                                    ->rules(['required'])
                                    ->required()
                                    ->label('City')
                                    ->options(function (Get $get) {
                                        if (! empty($get('ware_state_id'))) {
                                            return City::where('state_id', $get('ware_state_id'))->pluck('name', 'id')?->toArray();
                                        }

                                        return [];
                                    })->required(),
                                // TextInput::make('ware_phone_number')
                                //     ->rules(['required', 'string', 'max:15', 'regex:/^\+?[0-9]{5,15}$/'])
                                //     ->label('Phone Number')
                                //     ->required(),
                                TextInput::make('ware_postal_code')
                                    ->rules(['required', 'string', 'max:5', 'regex:/^\+?[0-9]{5,5}$/'])
                                    ->label('Postal Code')
                                    ->placeholder('Enter postcode')->live()
                                    ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9]*',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();',
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    ])
                                    ->required(),
                                TextInput::make('ware_region')->label('Region')->readOnly()->placeholder('Enter region'),


                                // TextInput::make('ware_district')
                                //     ->rules(['required', 'string', 'max:50'])
                                //     ->label('District')
                                //     ->required(),

                                TextInput::make('ware_id')->visible(false),
                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, StoreProfileByAdminService $service) {
                            $data = [];
                            $data['phone_number'] = $get('ware_phone_number');
                            $data['address_1'] = $get('ware_address_1');
                            $data['address_2'] = $get('ware_address_2');
                            $data['district'] = $get('ware_district');
                            $data['state_id'] = $get('ware_state_id');
                            $data['postal_code'] = $get('ware_postal_code');
                            $data['city_id'] = $get('ware_city_id');
                            $data['warehouse_type'] = $get('warehouse_type');
                            $data['ware_id'] = $get('ware_id');
                            $data['ware_region'] = $get('ware_region');
                            $data['user_id'] = $get('user_id');
                            $service->storeWareHouse($data);
                        }),
                    Step::make('Person In Charge')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('person_in_charge_name')
                                    ->rules(['string', 'regex:/^[a-zA-Z\s]+$/', 'max:50'])
                                    ->placeholder('Enter the full name')
                                    ->validationMessages([
                                        'regex' => 'The Full Name must be a alphabetical.',
                                        'max' => 'The Full Name may not be greater than 50 characters.',
                                    ])
                                    ->suffixIcon(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn(Get $get) => preg_match('/^[a-zA-Z\s]+$/', $get('person_in_charge_name')) ? 'success' : null)
                                    ->label('Full Name'),
                                TextInput::make('person_in_charge_nric')
                                    ->rules(function (Get $get) {
                                        if (!empty($this->user->id)) {
                                            return ['nullable', 'max:20', 'min:0'];
                                        }
                                        return ['nullable', 'max:20', 'min:0'];
                                    })
                                    ->live()
                                    ->validationAttribute('ID/NRIC Number')
                                    ->placeholder('Enter the ID/NRIC number')
                                    // ->extraAttributes([
                                    //     'inputmode' => 'numeric',
                                    //     'pattern' => '[0-9]*',
                                    //     'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "");',
                                    //     'onkeypress' => 'return event.charCode >= 48 && event.charCode <= 57',
                                    // ])
                                    ->label(fn() => new HtmlString('ID/NRIC Number'))
                                    ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0))  ? 'success' : null)
                                    ->validationMessages([
                                        'max' => 'The ID/NRIC Number may not be more than 20 characters.',
                                    ]),
                                // TextInput::make('person_in_charge_nric')
                                //     ->rules(['required','digits:12'])
                                //     ->label(fn () => new HtmlString('ID/NRIC Number <span style="color:red">*</span>' . view('components.heroicon-o-information-circle', ['class' => 'inline-block w-5 h-5 mt-1'])->render())),
                                TextInput::make('person_in_charge_phone')->placeholder('Enter phone number')->prefix('+60')
                                    ->label('Phone Number')
                                    ->mask('99-99999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '10'
                                    ])
                                    ->rules(['required', 'digits_between:9,10'])
                                    ->required()
                                    ->suffixIcon(fn($state) => in_array(strlen($state), [10, 11]) ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => in_array(strlen($state), [10, 11]) ? 'success' : null)
                                    ->required()
                                    ->live(),
                                TextInput::make('person_in_charge_email')
                                    ->placeholder('Enter email')
                                    ->required()
                                    ->rules(function (Get $get) {
                                        return [
                                            'required',
                                            'email',
                                            'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/',
                                            function ($attribute, $value, $fail) use ($get) {
                                                // Check uniqueness in BOTH `person_in_charge_email` and `profile_email`, ignore if it's the current user's email
                                                $exists = DB::table('pc_details')
                                                    ->where(function ($query) use ($value) {
                                                        $query->where('profile_email', $value)
                                                            ->orWhere('person_in_charge_email', $value);
                                                    })
                                                    ->where('user_id', '!=', $get('user_id')) // Ignore the current user
                                                    ->exists();
                                                if ($exists) {
                                                    $fail('The email has already been taken.');
                                                }
                                            },
                                        ];
                                    })
                                    ->label('Email')
                                    ->suffixIcon(function ($state) {
                                        return !empty($state) && filter_var($state, FILTER_VALIDATE_EMAIL) ? 'heroicon-s-check-circle' : null;
                                    })
                                    ->suffixIconColor(fn($state) => filter_var($state, FILTER_VALIDATE_EMAIL) ? 'success' : null)
                                    ->live(),
                            ])->columns(2),
                        ])
                        ->afterValidation(function (Get $get, Set $set, StoreProfileByAdminService $service) {
                            $data = [];
                            $data['id'] = PcDetail::where('user_id', $get('user_id'))->first()->id;
                            $data['person_in_charge_name'] = $get('person_in_charge_name');
                            $data['person_in_charge_nric'] = $get('person_in_charge_nric');
                            $data['person_in_charge_phone'] = $get('person_in_charge_phone');
                            $data['person_in_charge_landline'] = $get('person_in_charge_landline');
                            $data['person_in_charge_email'] = $get('person_in_charge_email');
                            $data['user_id'] = $get('user_id');
                            $set('id', $data['id']);
                            $service->storePersonInCharge($data, 4);
                        }),
                    Step::make('Legal Documents')
                        ->icon('heroicon-o-document-text')
                        ->schema([
                            Group::make()->schema([
                                FileUpload::make('company_registration_certificate')
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->required()
                                    ->label(new HtmlString(
                                        'Company Registration Certificate 
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Company Registration Certificate`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->helperText('Max size: 2MB. Only pdf, jpg, jpeg, png are allowed.')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias') . $get('id') . '/';
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('company_registration_certificate_'),
                                    )
                                    ->disk('public'),

                                FileUpload::make('license_permit')
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->required()
                                    ->label(new HtmlString(
                                        'Relevant certification
                                            <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Relevant Certification`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <circle cx="12" cy="12" r="10"></circle>
                                                <line x1="12" y1="16" x2="12" y2="12"></line>
                                                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                            </svg>'
                                    ))
                                    ->helperText('Max size: 2MB. Only pdf, jpg, jpeg, png are allowed.')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.pc_medias') . $get('id') . '/';
                                    })
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('license_permit_'),
                                    )
                                    ->disk('public'),
                            ])->columns(2),
                        ]),
                ])->submitAction(
                    Action::make('create')->label('Create')->action('create')
                )

                ->columnSpanFull(),
        ])->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('product_upload')
                ->form([
                    FileUpload::make('images')->rules(['required', 'file', 'mimes:zip']),
                    FileUpload::make('csv')->rules(['required', 'file', 'mimes:csv']),
                ])
                ->slideOver()
                ->requiresConfirmation()
        ];
    }

    public static function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Actions')
            ->query(function (Builder $query) {
                return User::query()->role('Pharmaceutical Company')->with('pcDetails', 'address.city', 'address.state', 'userAddresses.city', 'userAddresses.state');
            })
            ->columns([
                TextColumn::make('id')->sortable()->searchable()->toggleable(),
                TextColumn::make('pcDetails.company_name')->label('Name')->default('-')->sortable()->searchable()->toggleable(),
                TextColumn::make('pcDetails.business_name')->label('Business Name')->default('-')->sortable()->searchable()->toggleable(),
                TextColumn::make('pcDetails.is_restricted')
                    ->searchable(query: function (Builder $query, string $search): Builder {

                        $searchStr = "";
                        if (Str::lower($search) == 'global') {
                            $searchStr = 0;
                        } elseif (Str::lower($search) == 'restricted') {
                            $searchStr = 1;
                        } else {
                            $searchStr = null;
                        }
                        return $query
                            ->whereHas('pcDetails', function (Builder $query) use ($searchStr) {
                                $query->where('is_restricted', $searchStr);
                            });
                    })
                    ->label('Access')
                    ->formatStateUsing(function ($state) {
                        return $state ? 'Restricted' : 'Global';
                    })->toggleable(),
                TextColumn::make('userAddresses.city.name')->default('-')->sortable()->searchable()->toggleable(),
                TextColumn::make('userAddresses.state.name')->default('-')->sortable()->searchable()->toggleable(),
                TextColumn::make('email')->sortable()->searchable()->toggleable(),

                TextColumn::make('created_at')
                    ->label('Registered On')
                    ->dateTime('M d, Y')->sortable()->searchable()->toggleable(),
                TextColumn::make('pcDetails.is_submitted')
                    ->label('Profile Status')
                    ->badge()
                    ->color(fn($state): string => $state ? 'success' : 'warning')
                    ->formatStateUsing(fn($state): string => $state ? 'Submitted' : 'Pending')
                    ->toggleable(),
                TextColumn::make('pcDetails.is_featured')
                    ->label('Featured Status')
                    ->badge()
                    ->color(fn($state): string => $state ? 'success' : 'danger')
                    ->formatStateUsing(fn($state): string => $state ? 'Yes' : 'No')
                    ->toggleable(),
                TextColumn::make('verification_status')
                    ->label('Verification Status')
                    ->searchable(query: function (Builder $query, string $search) {
                        if (Str::lower($search) == 'pending') {
                            return $query->where('verification_status', 'pending')->orWhere('verification_status', null);
                        } elseif (Str::lower($search) == 'approved') {
                            return $query->where('verification_status', 'approved');
                        } elseif (Str::lower($search) == 'rejected') {
                            return $query->where('verification_status', 'rejected');
                        }
                    })
                    ->badge()
                    ->toggleable()
                    ->default('Pending')
                    ->formatStateUsing(fn($state): string =>  !empty($state) ? ucfirst($state) : 'Pending')
                    ->color(function ($state) {
                        $st = match ($state) {
                            'pending' => 'warning',
                            'approved' => 'success',
                            'rejected' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),
                ToggleColumn::make('is_active')->label('Status')->toggleable()
                    ->afterStateUpdated(function ($record, $state) {
                        if ($state == false) {
                            DB::table('sessions')->where('user_id', $record->id)->delete();
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                            ->duration(1000)
                            ->title('Status has been updated successfully.')
                            ->send();
                    })->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('company_name')
                    ->label('Name')
                    ->options(function () {
                        return \App\Models\PcDetail::whereNotNull('company_name')
                            ->pluck('company_name', 'company_name')
                            ->reject(fn($name) => empty($name)) // Remove empty names
                            ->unique()
                            ->sort()
                            ->toArray();
                    })
                    ->searchable()
                    ->native(false)
                    ->query(function (Builder $query, $data) {
                        if (!empty($data['value'])) {
                            $query->whereHas('pcDetails', function ($q) use ($data) {
                                $q->where('company_name', $data['value']);
                            });
                        }
                    }),
                SelectFilter::make('is_restricted')
                    ->options([
                        '0' => 'Global',
                        '1' => 'Restricted',
                    ])
                    ->modifyQueryUsing(function (Builder $query, array $data): Builder {
                        // info($data['value']);
                        if (isset($data['value'])) {
                            $d = $query->whereHas('pcDetails', function (Builder $query) use ($data) {
                                $query->where('is_restricted', $data['value']);
                            });
                            return $d;
                        }
                        return $query;
                    })
                    ->label('Access')
                    ->placeholder('Select Access'),

                SelectFilter::make('state')
                    ->label('State')
                    ->options(function () {
                        return UserAddress::whereHas('user', function ($query) {
                            $query->role('Pharmaceutical Company');
                        })
                            ->with('state')
                            ->get()
                            ->pluck('state.name', 'state.id')
                            ->filter() // Remove null values
                            ->map(fn($name) => $name ?? 'Unknown State') // Fallback
                            ->unique()
                            ->sort()
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) return;
                        $query->whereHas('userAddresses', function ($q) use ($state) {
                            $q->where('state_id', $state['value']);
                        });
                    }),

                // City Filter
                SelectFilter::make('city')
                    ->label('City')
                    ->options(function () {
                        return UserAddress::whereHas('user', function ($query) {
                            $query->role('Pharmaceutical Company');
                        })
                            ->with('city')
                            ->get()
                            ->pluck('city.name', 'city.id')
                            ->filter() // Remove null values
                            ->map(fn($name) => $name ?? 'Unknown City') // Fallback
                            ->unique()
                            ->sort()
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) return;
                        $query->whereHas('userAddresses', function ($q) use ($state) {
                            $q->where('city_id', $state['value']);
                        });
                    }),
                // SelectFilter::make('state_id')
                // ->label('State')
                // ->options(function () {
                //     return \Nnjeim\World\Models\State::whereHas('cities', function($query) {
                //             $query->whereHas('addresses', function($q) {
                //                 $q->whereHas('user', function($q) {
                //                     $q->role('Pharmaceutical Company');
                //                 });
                //             });
                //         })
                //         ->get()
                //         ->mapWithKeys(fn ($state) => [
                //             $state->id => $state->name ?? 'Unnamed State'
                //         ])
                //         ->sort()
                //         ->toArray();
                // })
                // ->searchable()
                // ->multiple()
                // ->native(false),
                SelectFilter::make('is_active')  // Changed from 'status' to match the column name
                    ->label('Status')
                    ->options([
                        '1' => 'Active',
                        '0' => 'Inactive',
                    ])
                    ->native(false),
                SelectFilter::make('is_submitted')
                    ->label('Profile Status')
                    ->options([
                        '1' => 'Submitted',
                        '0' => 'Pending',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->whereHas('pcDetails', function (Builder $query) use ($data) {
                                $query->where('is_submitted', $data['value']);
                            });
                        }
                    })
                    ->native(false),
                SelectFilter::make('is_featured')
                    ->label('Featured  Status')
                    ->options([
                        '1' => 'Yes',
                        '0' => 'No',
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (isset($data['value'])) {
                            $query->whereHas('pcDetails', function (Builder $query) use ($data) {
                                $query->where('is_featured', $data['value']);
                            });
                        }
                    })
                    ->native(false),
                // SelectFilter::make('verification_status')
                //     ->options([
                //         'pending' => 'Pending',
                //         'approved' => 'Approved',
                //         'rejected' => 'Rejected',
                //     ])
                //     ->modifyQueryUsing(function (Builder $query, array $data): Builder {
                //         $statuses = $data['values'] ?? [];
                //         if (in_array('pending', $statuses)) {
                //             $query->where(function ($q) {
                //                 $q->where('verification_status', 'pending')
                //                     ->orWhereNull('verification_status');
                //             });
                //         }

                //         if (in_array('approved', $statuses)) {
                //             $query->orWhere('verification_status', 'approved');
                //         }

                //         if (in_array('rejected', $statuses)) {
                //             $query->orWhere('verification_status', 'rejected');
                //         }

                //         return $query;
                //     })
                //     ->label('Verification Status')
                //     ->placeholder('Select Verification Status')
                //     ->searchable()
                //     ->multiple()
                //     ->native(false),

                Filter::make('created_at')
                    ->label('Registered On')
                    ->form([
                        DatePicker::make('created_from')->label('Registered From')->closeOnDateSelection()->reactive(),
                        DatePicker::make('created_until')->label('Registered Until')->closeOnDateSelection()->minDate(function ($get) {
                            return $get('created_from') ? Carbon::parse($get('created_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),

            ])
            ->filtersFormWidth('2xl')
            ->filtersFormColumns(2)
            ->deferFilters()
            ->actions([
                ActionGroup::make([
                    ViewAction::make('view')
                        ->url(function ($record, $livewire) {
                            // Use the current active tab from the Livewire component, defaulting to 'All'
                            $activeTab = $livewire->activeTab ?? session('user_resource_active_tab', 'All');
                            // Store in session for persistence
                            session(['user_resource_active_tab' => $activeTab]);
                            return static::getUrl('view', [
                                'record' => $record->id,
                                'activeTab' => $activeTab,
                            ]);
                        })
                        ->visible(function ($record) {
                            //return static::canView($record);
                            return !empty($record->pcDetails);
                        }),
                    Tables\Actions\Action::make('configure')
                        ->visible(function ($record) {
                            return !empty($record->pcDetails) && static::canConfig($record);
                        })
                        ->icon('heroicon-o-cog')
                        ->action(function ($record, $data) {
                            if (empty($record->pcDetails)) {
                                Notification::make()
                                    ->warning()
                                    ->title('Pc Details is not available for this Pharmaceutical Company, please add it first!')
                                    ->send();
                                return;
                            }

                            if ($record->payouts()->where('is_payout', false)->exists()) {
                                Notification::make()
                                    ->warning()
                                    ->title('You can\'t change payout because already ongoing payout found!')
                                    ->send();
                                return;
                            }

                            // Activity log: get old and new values, log only changed attributes
                            $oldData = [
                                'commission_type' => $record->pcDetails->commission_type ?? null,
                                'commission_percentage' => $record->pcDetails->commission_percentage ?? null,
                                'commission_payout_option' => $record->pcDetails->commission_payout_option ?? null,
                                'payment_method' => isset($record->pcDetails->payment_method)
                                    ? ($record->pcDetails->payment_method === 'online_manual'
                                        ? 'Online Through Link + Manual'
                                        : ($record->pcDetails->payment_method === 'manual'
                                            ? 'Manual'
                                            : $record->pcDetails->payment_method))
                                    : null,
                                'cycle_type' => isset($record->pcDetails->cycle_type)
                                    ? ($record->pcDetails->cycle_type === 'bi_weekly'
                                        ? '15 Days'
                                        : ($record->pcDetails->cycle_type === 'monthly'
                                            ? 'Monthly'
                                            : $record->pcDetails->cycle_type))
                                    : null,
                                'is_restricted' => isset($record->pcDetails->is_restricted)
                                    ? ($record->pcDetails->is_restricted ? 'Restricted' : 'Global')
                                    : null,
                            ];

                            $newData = [
                                'commission_type' => $data['commission_type'] ?? null,
                                'commission_percentage' => $data['commission_percentage'] ?? null,
                                'commission_payout_option' => $data['commission_payout_option'] ?? null,
                                'payment_method' => isset($data['payment_method'])
                                    ? ($data['payment_method'] === 'online_manual'
                                        ? 'Online Through Link + Manual'
                                        : ($data['payment_method'] === 'manual'
                                            ? 'Manual'
                                            : $data['payment_method']))
                                    : null,
                                'cycle_type' => isset($data['cycle_type'])
                                    ? ($data['cycle_type'] === 'bi_weekly'
                                        ? '15 Days'
                                        : ($data['cycle_type'] === 'monthly'
                                            ? 'Monthly'
                                            : $data['cycle_type']))
                                    : null,
                                'is_restricted' => isset($data['is_restricted'])
                                    ? ($data['is_restricted'] ? 'Restricted' : 'Global')
                                    : null,
                            ];

                            $changedData = array_diff_assoc($newData, $oldData);
                            if (
                                isset($data['commission_payout_option']) && $data['commission_payout_option'] === 'full' &&
                                (!isset($data['payment_method']) || $data['payment_method'] !== 'online_manual')
                            ) {
                                $data['cycle_type'] = 'monthly';
                            }
                            $record->pcDetails->update($data);

                            if (!empty($changedData)) {
                                $companyName = $record->pcDetails?->company_name ?? 'N/A';
                                activity()
                                    ->causedBy(auth()->user())
                                    ->useLog('users')
                                    ->performedOn($record)
                                    ->withProperties([
                                        'attributes' => $changedData,
                                        'old' => array_intersect_key($oldData, $changedData),
                                    ])
                                    ->log("Pharmaceutical Supplier Configuration has been updated for ({$companyName})");
                            }

                            Notification::make()
                                ->success()
                                ->title('Pharmaceutical Supplier Configuration has been updated successfully!')
                                ->send();
                        })
                        ->form([
                            Section::make()
                                ->schema([
                                    Grid::make(2)
                                        ->schema([
                                            Select::make('commission_type')
                                                ->label(new HtmlString('Commission Type <sapn style="color:red">*</span>'))
                                                ->validationAttribute('commission type')
                                                ->placeholder('Select commission type')
                                                ->options([
                                                    'percentage' => 'Percentage',
                                                    'flat' => 'Flat',
                                                ])
                                                ->disabled(function () {
                                                    $user = \Auth::user();
                                                    if ($user->hasRole('Super Admin')) {
                                                        return false; // Enable for Super Admin
                                                    }
                                                    if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                                        return false;
                                                    }
                                                    return true;
                                                })
                                                ->default(fn($record) => $record->pcDetails->commission_type ?? null)
                                                ->reactive()
                                                ->rules(['required']),

                                            TextInput::make('commission_percentage')
                                                ->label(new HtmlString('Commission <sapn style="color:red">*</span>'))
                                                ->validationAttribute('commission percentage')
                                                ->placeholder('Enter commission')
                                                ->disabled(function () {
                                                    $user = \Auth::user();
                                                    if ($user->hasRole('Super Admin')) {
                                                        return false; // Enable for Super Admin
                                                    }
                                                    if (\Auth::user()->can('pharmaceutical-suppliers_commissions')) {
                                                        return false;
                                                    }
                                                    return true;
                                                })
                                                ->default(fn($record) => $record->pcDetails->commission_percentage ?? null)
                                                ->rules(function (Get $get) {
                                                    $rules = ['required', 'numeric', 'min:0'];
                                                    $minPercentage = GlobalSettings::where('name', 'commission_percentage')->value('value');
                                                    $minFlat = GlobalSettings::where('name', 'commission_flat')->value('value');
                                                    if ($get('commission_type') == 'percentage') {
                                                        if ($minPercentage !== null) {
                                                            $rules[] = 'min:' . $minPercentage;
                                                        } else {
                                                            // Default max percentage if not set in global settings
                                                            $rules[] = 'max:100';
                                                        }
                                                    } else {
                                                        if ($minFlat !== null) {
                                                            $rules[] = 'min:' . $minFlat;
                                                        }
                                                    }

                                                    return $rules;
                                                })->helperText(function (Get $get) {
                                                    if ($get('commission_type') == 'percentage') {
                                                        $minPercentage = GlobalSettings::where('name', 'commission_percentage')
                                                            ->value('value');
                                                        return $minPercentage !== null ? "Minimum allowed percentage: {$minPercentage}%" : "Minimum allowed percentage: 100%";
                                                    } else {
                                                        $minFlat = GlobalSettings::where('name', 'commission_flat')
                                                            ->value('value');
                                                        return $minFlat !== null ? "Minimum allowed flat value: {$minFlat}" : null;
                                                    }
                                                }),
                                        ]),

                                    // Payout Option Section
                                    Grid::make(1)
                                        ->schema([
                                            Radio::make('commission_payout_option')
                                                ->label('Select Option for Payout')
                                                ->options([
                                                    'schedule' => 'Scheduled Payout (This would be the payout after the commission deduction)',
                                                    'full' => 'Full Payout (This would be the full amount paid to the Pharmaceutical Supplier, the commission would be invoiced later)',
                                                ])
                                                ->required()
                                                ->default(fn($record) => $record->pcDetails->commission_payout_option ?? null)
                                                ->reactive(),
                                            Radio::make('cycle_type')
                                                ->label('Scheduled Payout Cycle')
                                                ->options([
                                                    'bi_weekly' => '15 Days',
                                                    'monthly' => 'Monthly',
                                                ])
                                                ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                                ->visible(function ($get) {
                                                    return $get('commission_payout_option') === 'schedule';
                                                })
                                                ->validationMessages([
                                                    'required' => 'Please select schedule cycle type.',

                                                ])
                                                ->required(function ($get) {
                                                    return $get('commission_payout_option') === 'schedule';
                                                })->helperText('Select how often the scheduled payout should occur.'),

                                            // Conditional sub-option for Full Payout
                                            Radio::make('payment_method')
                                                ->label('Payment Method')
                                                ->options([
                                                    'online_manual' => 'Online Through Link + Manual',
                                                    'manual' => 'Manual',
                                                ])
                                                ->validationMessages([
                                                    'required' => 'Please select payment method.',
                                                ])
                                                ->default(fn($record) => $record->pcDetails->payment_method ?? null)
                                                ->visible(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))
                                                ->required(fn($get) => in_array($get('commission_payout_option'), ['schedule', 'full']))->reactive(),

                                            Radio::make('cycle_type')
                                                ->label('Full Payout Cycle')
                                                ->options([
                                                    'bi_weekly' => '15 Days',
                                                    'monthly' => 'Monthly',
                                                ])
                                                ->default(fn($record) => $record->pcDetails->cycle_type ?? null)
                                                ->visible(function ($get) {
                                                    return $get('commission_payout_option') === 'full'
                                                        && $get('payment_method') === 'online_manual';
                                                })
                                                ->validationMessages([
                                                    'required' => 'Please select Full Payout cycle type.',

                                                ])
                                                ->required(function ($get) {
                                                    return $get('commission_payout_option') === 'full'
                                                        && $get('payment_method') === 'online_manual';
                                                })->helperText('Select how often the full payout should occur (for Online + Manual).'),
                                        ]),

                                    // Access Type Section
                                    Grid::make(1)
                                        ->schema([
                                            Radio::make('is_restricted')
                                                ->label('Access Type')
                                                ->options([
                                                    false => 'Global',
                                                    true => 'Restricted',
                                                ])
                                                ->default(fn($record) => $record->pcDetails->is_restricted ?? false)
                                                ->required(),
                                        ]),

                                ])
                                ->columns(1),
                        ]),

                    Tables\Actions\EditAction::make()->icon('heroicon-o-pencil'),
                    // Tables\Actions\Action::make('approve')
                    //     ->requiresConfirmation()
                    //     ->icon(function ($record) {
                    //         return $record->is_admin_verified ? 'heroicon-o-minus-circle' : 'heroicon-o-check-circle';
                    //     })
                    //     ->action(function ($record) {
                    //         $status = $record->is_admin_verified ? false : true;
                    //         $record->update(['is_admin_verified' => $status, 'admin_verified_by' => auth()->user()->id, 'admin_verified_on' => now(), 'verification_status' => 'approved']);
                    //     }),
                ]),

            ])
            // ->bulkActions([
            //     Tables\Actions\BulkActionGroup::make([
            //         Tables\Actions\BulkAction::make('active')
            //             ->requiresConfirmation()
            //             ->action(function (Model $record, Collection $selectedRecords) {
            //                 $selectedRecords->each(function ($record) {
            //                     $record->update(['is_active' => true]);
            //                 });
            //             }),
            //         Tables\Actions\BulkAction::make('inactive')
            //             ->requiresConfirmation()
            //             ->action(function (Model $record, Collection $selectedRecords) {
            //                 $selectedRecords->each(function ($record) {
            //                     $record->update(['is_active' => false]);
            //                 });
            //             }),
            //     ]),

            // ]);
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['is_active' => true]);
                        });
                        Notification::make()
                            // ->title('Pharma Supplier Activated')
                            ->title('The selected suppilier have been activated successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['is_active' => false]);
                        });
                        Notification::make()
                            // ->title('Pharma Supplier Deactivated')
                            ->title('The selected suppilier have been deactivated successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\PcOnboardingPage::route('/{record}/edit'),
            'view' => Pages\ViewUser::route('/{record}'),
        ];
    }

    public function getStepOneChanges($userId, $steps)
    {

        $data =  Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('approved_by', null)
            ->where('approved_at', null)
            ->where('rejected_at', null)
            ->where('rejected_by', null)
            ->where('user_type', 'pc')
            ->latest()
            ->orderBy('created_at', 'desc')
            ->select('new_data', 'original_data')
            ->first();

        // $data->transform(function ($item) {
        //     $item->new_data = json_decode($item->new_data, true);
        //     $item->original_data = json_decode($item->original_data, true);
        //     return $item;
        // });
        if ($data) {
            $data->new_data = json_decode($data->new_data, true);
            $data->original_data = json_decode($data->original_data, true);
        }

        // dd($data);
        return $data;
    }

    public function organizeApprovalChanges($approvals)
    {
        if (!$approvals) {
            return [];
        }
        $changes = [];

        // foreach ($approvals as $approval) {
        $newData = $approvals->new_data;
        $originalData = $approvals->original_data;

        foreach ($newData as $key => $value) {
            if (!isset($changes[$key])) {
                $changes[$key] = [];
            }

            $changes[$key][] = [
                'new_value' => $value,
                'original_value' => $originalData[$key] ?? 'N/A',
            ];
        }
        // }
        // dd($changes);
        return $changes;
    }

    protected static function formatWithPendingChanges($currentValue, $changes = null)
    {
        $output = " $currentValue <br>";

        if ($changes) {
            foreach ($changes as $change) {
                if ($change['new_value'] != $currentValue) {
                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                }
            }
        }

        return new HtmlString($output);
    }

    //     protected function organizeDocumentApprovalChanges($approval)
    //     {

    //         if (!$approval) {
    //             return [];
    //         }
    //         // foreach ($approvals as $approval) {
    //         // Ensure proper JSON decoding
    //         $newData = is_array($approval->new_data)
    //             ? $approval->new_data
    //             : json_decode($approval->new_data, true);

    //         if (json_last_error() !== JSON_ERROR_NONE) {
    //             return [];
    //         }
    //         $changes = [];
    //         // dd($newData);
    //         // Handle document changes
    //         if (isset($newData['license_permit'])) {
    //             $changes['license_permit'] = $newData['license_permit'];
    //         }
    //         if (isset($newData['company_registration_certificate'])) {
    //             $changes['company_registration_certificate'] = $newData['company_registration_certificate'];
    //         }

    //         // Handle removed documents
    //         foreach ($newData as $key => $value) {
    //             if (str_starts_with($key, 'removed_')) {
    //                 $documentType = str_replace('removed_', '', $key);
    //                 $removedIds = is_array($value) ? $value : [$value];

    //                 // Fetch the filenames for these IDs
    //                 $removedFiles = PcCertificateFile::whereIn('id', $removedIds)
    //                     ->pluck('name')
    //                     ->toArray();

    //                 // Store under the document type key without "removed_" prefix
    //                 $changes[$documentType] = $removedFiles;
    //             }
    //         }
    // dd($changes);
    //         return $changes;
    //     }

    protected function organizeDocumentApprovalChanges($approval)
    {
        if (!$approval) {
            return [];
        }

        $newData = is_array($approval->new_data)
            ? $approval->new_data
            : json_decode($approval->new_data, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }

        $changes = [];

        // Handle document changes (both additions and removals)
        $documentTypes = [
            'license_permit',
            'company_registration_certificate'
            // Add other document types as needed
        ];

        foreach ($documentTypes as $type) {
            // Initialize array for this document type
            $changes[$type] = [];

            // Add newly uploaded files if they exist
            if (isset($newData[$type])) {
                $changes[$type] = array_merge(
                    $changes[$type],
                    is_array($newData[$type]) ? $newData[$type] : [$newData[$type]]
                );
            }

            // Add removed files if they exist (converted from IDs to filenames)
            $removedKey = 'removed_' . $type;
            if (isset($newData[$removedKey])) {
                $removedIds = is_array($newData[$removedKey]) ? $newData[$removedKey] : [$newData[$removedKey]];

                $removedFiles = PcCertificateFile::whereIn('id', $removedIds)
                    ->pluck('name')
                    ->toArray();

                $changes[$type] = array_merge($changes[$type], $removedFiles);
            }

            // Remove duplicates and reset array keys
            $changes[$type] = array_values(array_unique($changes[$type]));

            // Remove empty entries
            if (empty($changes[$type])) {
                unset($changes[$type]);
            }
        }
        // dd($changes);
        return $changes;
    }

    protected static function getPendingApproval($userId, $steps)
    {
        return Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('user_type', 'pc')
            ->pending()
            ->latest()
            ->first();
    }

    protected static function makeAcceptAction(string $step): \Filament\Infolists\Components\Actions\Action
    {
        return \Filament\Infolists\Components\Actions\Action::make('accept')
            ->color('success')
            ->label('Accept')
            ->outlined()
            ->button()
            ->requiresConfirmation()
            ->visible(fn($record) => Approval::pendingForSteps($step)
                ->where('user_type', 'pc')
                ->where('approvalable_id', $record->id)
                ->exists())
            ->modalHeading('Confirm Approval')
            ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
            ->modalSubmitActionLabel('Confirm')
            ->action(function ($record) use ($step) {
                $approvalData = static::getPendingApproval($record->id, $step);
                if (!$approvalData) {
                    Notification::make()
                        ->title('No pending approval found')
                        ->danger()
                        ->send();
                    return;
                }

                $newData = json_decode($approvalData->new_data, true);
                // dd($newData);
                // dd(OnboardingStep::ADDRESS);
                // Handle different approval steps
                switch ($step) {
                    case OnboardingStep::BASIC_INFO->value:
                        $record->pcDetails->update(['is_version_pending' => false, 'is_restricted' => false]);
                        $record->pcDetails->update(
                            Arr::only($newData, [
                                'business_name',
                                'company_name',
                                'company_registration_number',
                                'tin_number',
                                'sstc_number',
                                'phone_number',
                                'web_url',
                                'company_type_id',
                                // 'is_credit_line'
                            ])
                        );
                        break;

                    case OnboardingStep::ADDRESS->value:
                        $record->pcDetails->update(['is_version_pending' => false, 'is_restricted' => false]);
                        if ($record->warehouses()->exists()) {
                            $warehouse = $record->warehouses()->first();
                            if ($newData['warehouse_type'] === 'dpharma') {
                                $updateData = [
                                    'address_1' => $newData['warehouse_address_1'] ?? $warehouse->address_1,
                                    'address_2' => $newData['warehouse_address_2'] ?? $warehouse->address_2,
                                    'postal_code' => $newData['warehouse_postal_code'] ?? $warehouse->postal_code,
                                    'state_id' => $newData['warehouse_state'],
                                    'city_id' => $newData['warehouse_city'],
                                    'warehouse_type' => $newData['warehouse_type'],
                                ];
                                if (isset($newData['warehouse_state'])) {
                                    $state = State::where('name', $newData['warehouse_state'])->first();
                                    if ($state) {
                                        $updateData['state_id'] = $state->id;
                                    }
                                }
                                $record->pcDetails->update(['delivery_days' => null, 'delivery_days_west' => null, 'min_order_value' => null]);
                            } else {
                                $updateData = [
                                    'address_1' => null,
                                    'address_2' => null,
                                    'city_id' => null,
                                    'state_id' => null,
                                    'ware_region' => null,
                                    'postal_code' => null,
                                    'warehouse_type' => $newData['warehouse_type'],
                                ];
                            }
                            $warehouse->update($updateData);
                        }
                        break;

                    case OnboardingStep::DOCUMENTS->value:
                        // dd($record);
                        $certificateTypes = [
                            'license_permit',
                            'company_registration_certificate'
                        ];
                        foreach ($certificateTypes as $type) {
                            if (isset($newData[$type])) {

                                $newFiles = array_map(function ($file) {
                                    return is_array($file) ? ($file['name'] ?? null) : $file;
                                }, is_array($newData[$type]) ? $newData[$type] : [$newData[$type]]);

                                // Deactivate existing active records for this type
                                PcCertificateFile::where([
                                    'user_id' => $record->id,
                                    'type' => $type
                                ])->whereIn('name', $newFiles)->update(['status' => 'inactive']);

                                // Handle both single and multiple files
                                $files = is_array($newData[$type]) ? $newData[$type] : [$newData[$type]];

                                foreach ($files as $fileData) {
                                    // Handle both array format (from approval) and direct filename
                                    $filename = is_array($fileData) ? ($fileData['name'] ?? null) : $fileData;

                                    if ($filename) {
                                        PcCertificateFile::create([
                                            'user_id' => $record->id,
                                            'type' => $type,
                                            'name' => $filename,
                                            'status' => 'active'
                                        ]);
                                    }
                                }
                            }

                            $removedKey = "removed_{$type}";
                            if (isset($newData[$removedKey])) {
                                $removedIds = is_array($newData[$removedKey]) ? $newData[$removedKey] : [$newData[$removedKey]];
                                PcCertificateFile::where([
                                    'user_id' => $record->id,
                                    'type' => $type
                                ])
                                    ->whereIn('id', $removedIds)
                                    ->update(['status' => 'inactive']);
                            }
                        }
                        // $record->pcDetails->update(['is_version_pending' => false, 'is_restricted' => false]);
                        // $record->pcDetails->update(
                        //     Arr::only($newData, [
                        //         'license_permit',
                        //         'company_registration_certificate'
                        //     ])
                        // );
                        break;

                    case OnboardingStep::CONTACT->value:
                        $record->pcDetails->update([
                            'is_version_pending' => false,
                            'is_restricted' => false,
                            'phone_number' => $newData['phone_number'] ?? $record->pcDetails->phone_number,
                            'phone_number_code' => $newData['phone_number_code'] ?? '60',
                            'web_url' => $newData['web_url'] ?? $record->pcDetails->web_url,
                            'region' => $newData['region'] ?? $record->pcDetails->region,
                        ]);

                        // Update user address


                        foreach ($record->userAddresses as $address) {
                            $address->update(
                                Arr::only($newData, [
                                    'address_1',
                                    'address_2',
                                    'state_id',
                                    'city_id',
                                    'postal_code',
                                    'landline_number',
                                    'landline_code',
                                ])
                            );
                        }
                        break;


                    case OnboardingStep::PERSON_IN_CHARGE->value:
                        $record->pcDetails->update([
                            'is_version_pending' => false,
                            'is_restricted' => false,
                            'person_in_charge_name' => $newData['person_in_charge_name'],
                            'person_in_charge_nric' => $newData['person_in_charge_nric'],
                            'person_in_charge_phone' => $newData['person_in_charge_phone'],
                            'person_in_charge_email' => $newData['person_in_charge_email'],
                        ]);
                        break;
                }

                $approvalData->approve(auth()->id(), $step);
                Mail::to($record->email)->send(new ChangesApprovedMail($record));

                Notification::make()
                    ->title('Changes Approved Successfully')
                    ->success()
                    ->send();
            });
    }

    /**
     * Create a reusable Reject Action component
     */
    protected static function makeRejectAction(string $step): \Filament\Infolists\Components\Actions\Action
    {
        return \Filament\Infolists\Components\Actions\Action::make('reject')
            ->color('danger')
            ->outlined()
            ->label('Reject')
            ->button()
            ->requiresConfirmation()
            ->visible(fn($record) => Approval::pendingForSteps($step)
                ->where('user_type', 'pc')
                ->where('approvalable_id', $record->id)
                ->exists())
            ->modalHeading('Confirm Rejection')
            ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
            ->modalSubmitActionLabel('Confirm')
            ->action(function ($record) use ($step) {
                $approvalData = static::getPendingApproval($record->id, $step);

                if (!$approvalData) {
                    Notification::make()
                        ->title('No Data Found For Rejection')
                        ->danger()
                        ->send();
                    return;
                }

                $approvalData->reject(auth()->id(), $step);
                Mail::to($record->email)->send(new ChangesRejectedMail($record));
                Notification::make()
                    ->title('Changes Rejected Successfully')
                    ->success()
                    ->send();
            });
    }

    protected static function formatRelationshipWithPendingChanges($currentName, ?array $changes, string $modelClass): HtmlString
    {
        $output = $currentName ?? '-';

        if (empty($changes)) {
            return new HtmlString($output);
        }

        $output = "{$output}<br>";

        foreach ($changes as $change) {
            $newId = $change['new_value'] ?? null;
            $newName = $modelClass::find($newId)?->name;

            if ($newName && $newName !== $currentName) {
                $output .= "<span style='background-color: yellow;'>{$newName}</span><br>";
            }
        }

        return new HtmlString($output);
    }

    protected static function getWarehouseTypeChangeMessage($record): ?string
    {
        $currentWarehouse = $record->warehouses()->first();
        if (!$currentWarehouse) {
            return null;
        }

        $approvalData = static::getPendingApproval($record->id, OnboardingStep::ADDRESS->value);
        if (!$approvalData) {
            return null;
        }

        $newData = json_decode($approvalData->new_data, true);
        $currentType = $currentWarehouse->warehouse_type;

        if (!isset($newData['warehouse_type']) || $newData['warehouse_type'] === $currentType) {
            return null;
        }

        $typeNames = [
            'dpharma' => 'DPharma Logistics',
            'owned' => 'Own Logistics'
        ];

        return sprintf(
            "Logistics Type Change Request: From %s to %s",
            $typeNames[$currentType] ?? $currentType,
            $typeNames[$newData['warehouse_type']] ?? $newData['warehouse_type']
        );
    }


    protected static function formatLandlinePendingChanges($currentValue, $changes = null)
    {
        $output = "{$currentValue}<br>";

        if ($changes) {
            $currentNumberOnly = preg_replace('/^\+?\d*\s*/', '', $currentValue);
            // Handle landline number changes
            if (isset($changes['number'])) {
                foreach ($changes['number'] as $change) {
                    if ($change['new_value'] != $currentValue) {
                        $newNumber = $change['new_value'];
                        $newCode = $changes['code'][0]['new_value'] ?? '';
                        if ($newNumber != $currentNumberOnly) {
                            $formatted = $newCode ? '+' . $newCode . ' ' . $newNumber : $newNumber;
                            $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                        }
                    }
                }
            }

            // Handle case where only code changes
            if (isset($changes['code'])) {
                foreach ($changes['code'] as $change) {
                    $currentCode = preg_match('/\+(\d+)/', $currentValue, $matches) ? $matches[1] : '';

                    $currentNumber = explode(' ', $currentValue)[1] ?? '';
                    if ($change['new_value'] != $currentCode) {
                        $formatted = '+' . $change['new_value'] . ' ' . $currentNumber;
                        $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                    }
                }
            }
        }

        return new HtmlString($output);
    }


    protected static function updateCertificateFiles($record, $data): void
    {
        $userId = $record->id;

        $certificateTypes = [
            'company_registration_certificate',
            'license_permit',
        ];

        foreach ($certificateTypes as $type) {
            if (!empty($data['pcDetails'][$type])) {
                $files = is_array($data['pcDetails'][$type])
                    ? $data['pcDetails'][$type]
                    : [$data['pcDetails'][$type]];

                PcCertificateFile::where('user_id', $userId)
                    ->where('type', $type)
                    ->where('status', 'active')
                    ->update(['status' => 'inactive']);

                foreach ($files as $filePath) {
                    PcCertificateFile::create([
                        'user_id' => $userId,
                        'type' => $type,
                        'name' => basename($filePath),
                        'size' => getFormattedFileSize($filePath),
                        'status' => 'active',
                    ]);
                }
            }
        }
    }

    protected static  function formatCreditLineStatus($currentValue, $changes = null): Htmlable
    {
        $currentStatus = match ($currentValue) {
            true => 'Enable',
            false => 'Disable',
            default => '-'
        };

        $output = $currentStatus . "<br>";

        if ($changes) {
            foreach ((array)$changes as $change) {
                // Convert pending boolean value to text
                $pendingStatus = match ($change['new_value'] ?? null) {
                    true => 'Enable',
                    false => 'Disable',
                    default => '-'
                };

                if (($change['new_value'] ?? null) !== $currentValue) {
                    $output .= "<span style='background-color: yellow;'>$pendingStatus</span><br>";
                }
            }
        }

        return new HtmlString($output);
    }
}
