<?php

namespace App\Filament\Admin\Resources\TransactionResource\Pages;

use App\Filament\Admin\Resources\TransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListTransactions extends ListRecords
{
    protected static string $resource = TransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => 'Transactions',
            // 2 => 'List',
        ];
    }

    public function getTabs(): array
    {
        return [
             'Pharmaceutical Supplier' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->whereHas('sender', function ($q) {
                    $q->where(function ($query) {
                        $query->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'Pharmaceutical Company');
                        })->orWhereHas('parent.roles', function ($parentRoleQuery) {
                            $parentRoleQuery->where('name', 'Pharmaceutical Company');
                        });
                    });
                });
            }),
            'Facility' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->whereHas('sender', function ($q) {
                    $q->whereHas('roles', function ($roleQuery) {
                        // $roleQuery->where('name', 'Clinic');
                    });
                })->with(['sender' => function ($q) {
                    $q->select('id', 'parent_id');
                }]);
            }),
        ];
    }
}
