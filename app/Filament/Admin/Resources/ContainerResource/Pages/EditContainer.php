<?php

namespace App\Filament\Admin\Resources\ContainerResource\Pages;

use App\Filament\Admin\Resources\ContainerResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditContainer extends EditRecord
{
    protected static string $resource = ContainerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ContainerResource::getUrl()),
        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.container.title.updated'))
            ->title(__('message.container.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Packages",
            3 => "Edit Package",
        ];
    }
}
