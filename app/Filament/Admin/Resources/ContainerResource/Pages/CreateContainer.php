<?php

namespace App\Filament\Admin\Resources\ContainerResource\Pages;

use App\Filament\Admin\Resources\ContainerResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateContainer extends CreateRecord
{
    protected static string $resource = ContainerResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ContainerResource::getUrl()),
        ];
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.container.title.created'))
            ->title(__('message.container.create_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Packages",
            3 => "Add Package",
        ];
    }
    public function getTitle(): string
    {
        return 'Add Package';
    }
}
