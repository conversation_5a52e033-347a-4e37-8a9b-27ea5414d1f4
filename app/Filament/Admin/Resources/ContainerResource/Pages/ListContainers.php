<?php

namespace App\Filament\Admin\Resources\ContainerResource\Pages;

use App\Filament\Admin\Resources\ContainerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContainers extends ListRecords
{
    protected static string $resource = ContainerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Packages'),
        ];
    }

    public function getTitle(): string
    {
        return 'Packages';
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // $this->getResource()::getUrl('index') => "Containers",
            // 3 => "List",
        ];
    }
}
