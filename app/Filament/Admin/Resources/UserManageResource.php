<?php

namespace App\Filament\Admin\Resources;

use Filament\Forms;
use App\Models\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Filament\Resources\Resource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Filament\Forms\Components\Select;
use Illuminate\Support\Facades\Redis;
use Filament\Forms\Components\Section;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Unique;
use Filament\Forms\Components\TextInput;
use Filament\Navigation\NavigationGroup;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\ImageColumn;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Resources\Pages\CreateRecord;
use Filament\Tables\Actions\ExportBulkAction;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use App\Filament\Admin\Resources\UserManageResource\Pages;
use App\Filament\Admin\Resources\UserManageResource\RelationManagers;

class UserManageResource extends Resource
{
    protected static ?string $model = User::class;

    public const STATUS_ACTIVE = 1;

    public const STATUS_INACTIVE = 0;

    protected static ?string $navigationGroup = 'Users';

    protected static ?string $label = 'Admin User';
    protected static ?int $navigationSort = 1;


    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_view');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_create');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_update');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_delete');
    }

    public static function form(Form $form): Form
    {
        // $userId = getUser(\Auth::user())->id;
        // $roles = Role::query()->where('created_by', $userId);
        // $data = \Spatie\Permission\Models\Role::where('is_active', true)
        //     ->where('created_by', auth()->id())
        //     ->pluck('name', 'name');
        // dd($data, $roles->get());

        return $form
            ->schema([
                Section::make('')
                    ->schema([
                        Grid::make()
                            ->schema([
                                FileUpload::make('photo')
                                    ->label('')
                                    ->image()
                                    ->imageEditor()
                                    ->circleCropper()
                                    ->directory('users')
                                    ->avatar()
                                    ->columnSpanFull()
                                    // ->required()
                                    ->alignCenter()
                                    ->rules(['nullable', 'image', 'mimes:jpg,jpeg,png', 'max:2048'])
                                    ->validationMessages([
                                        // 'required' => 'The profile image field is required.',
                                        'image' => 'The file must be an image.',
                                        'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                        'max' => 'The image must not exceed 2MB.'
                                    ])

                            ])->extraAttributes([
                                'style' => 'text-align: center; '
                            ]),
                        Grid::make(5)
                            ->schema([
                                TextInput::make('name')
                                    ->placeholder('Enter Full Name')
                                    ->label(new HtmlString("Full Name<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->rules(['required', 'regex:/^[A-Za-z0-9\s]+$/'])
                                    ->maxLength(50)
                                    ->validationMessages([
                                        'required' => 'The name field is required.',
                                        'max' => 'The name must not exceed 255 characters.',
                                        'regex' => 'The name must contain only letters, numbers, and spaces.',
                                    ]),
                                TextInput::make('email')
                                    ->label(new HtmlString("Email <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->placeholder("Enter Email")
                                    ->rules(['required', 'email'])
                                    ->unique(
                                        table: User::class,
                                        column: 'email',
                                        ignoreRecord: true,
                                        modifyRuleUsing: function (Unique $rule) {
                                            return $rule->whereNull('deleted_at');
                                        }
                                    )
                                    ->validationMessages([
                                        'required' => 'Please enter an email address.',
                                        'email' => 'Please enter a valid email address.',
                                        'unique' => 'This email is already registered.'
                                    ])
                                    ->afterStateUpdated(
                                        function (Get $get, Set $set) {
                                            $set('email', \Str::lower($get('email')));
                                        }
                                    )
                                    ->disabled(fn() => $form->getOperation() === 'edit')
                                    ->live(),

                                TextInput::make('phone')
                                    ->numeric()
                                    ->prefix('+60')
                                    ->live()
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->validationMessages([
                                        'digits_between' => 'The phone number must be between :min and :max characters.',
                                        'required' => 'The phone number field is required.',
                                    ])
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">Enter phone number</span> <span class="font-medium text-danger-600 dark:text-danger-400">*</span>'))
                                    ->placeholder('Phone Number')
                                    ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->rules(['required', 'digits_between:8,12']),


                                Select::make('role')
                                    ->label(new HtmlString("Role<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->options(

                                        \Spatie\Permission\Models\Role::whereNotIn('name', ['Super Admin', 'Pharmaceutical Company', 'Clinic'])
                                            ->where('panel', 'admin')
                                            ->where('is_active', true)
                                            // ->where('created_by', getUser(auth()->user())->id)
                                            ->pluck('name', 'name')

                                    )

                                    ->rules('required')
                                    ->validationMessages([
                                        'required' => 'Please select a role.',
                                    ])
                                    ->default(function ($record) {
                                        return $record?->roles->first()?->name;
                                    })
                                    ->dehydrated(false),
                            ])
                            ->columns(2),
                    ])

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                User::whereIn('parent_id', function ($query) {
                    $query->select('id')
                        ->from('users')
                        ->whereExists(function ($roleQuery) {
                            $roleQuery->select(DB::raw(1))
                                ->from('model_has_roles')
                                ->whereColumn('model_has_roles.model_id', 'users.id')
                                ->whereIn('model_has_roles.role_id', function ($subQuery) {
                                    $subQuery->select('id')
                                        ->from('roles')
                                        ->where('name', 'Super Admin');
                                });
                        });
                })->where('id', '!=', auth()->id())
            )
            ->columns([
                TextColumn::make('id')
                    ->label('User ID')
                    ->formatStateUsing(fn(string $state): string => "#{$state}")
                    ->toggleable()
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->label('Name')
                    ->toggleable()
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state, $record) {
                        // Check if the record has a photo stored in S3
                        $avatar = $record->photo
                            ? '<img src="' . Storage::disk('s3')->url($record->photo) . '" alt="Avatar" class="inline-block w-8 h-8 mr-2 rounded-full">'
                            : '<img src="' . asset('images/default-avatar.png') . '" class="inline-block w-8 h-8 mr-2 rounded-full">';

                        return new HtmlString($avatar . e($state));
                    }),

                TextColumn::make('email')->sortable()->searchable()->toggleable(),
                TextColumn::make('roles.name')->label('Role')->default('-')->sortable()->searchable()->toggleable()->formatStateUsing(function (?string $state) {
                    return $state ? preg_replace('/^\d+-/', '', $state) : '-';
                }),
                ToggleColumn::make('is_active')->label('Status')
                    ->toggleable()
                    ->sortable()
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company']))
                    ->afterStateUpdated(function ($record) {
                        if(!$record->is_active) {
                            self::logoutUserById($record->id);
                            Notification::make()
                                ->success()
                                ->title('User has been deactivated and logged out successfully.')
                                ->send();
                        } else {
                            Notification::make()
                                ->success()
                                ->title('User has been activated successfully.')
                                ->send();
                        }
                    }),

            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('role')
                    ->label('Role')
                    ->options(
                        \Spatie\Permission\Models\Role::whereNotIn('name', ['Super Admin', 'Pharmaceutical Company', 'Clinic'])
                            ->where('panel', 'admin')
                            ->where('is_active', true)
                            ->pluck('name', 'name')
                    )
                    ->query(function ($query, array $data) {
                        if (!empty($data['value'])) {
                            $query->whereHas('roles', function ($query) use ($data) {
                                $query->where('name', $data['value']);
                            });
                        }
                    }),

                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])

            ->actionsColumnLabel('Action')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company'])),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);']),
                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title('User Deleted')
                            ->title('The users have been deleted successfully.'),
                    ),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $records->each->delete();
                        Notification::make()
                            ->success()
                            // ->title('Users Deleted')
                            ->title('The users have been deleted successfully.')
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('active')
                    ->label('Active')
                    ->color('success')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_ACTIVE; // Use is_active instead of status
                                $record->save();
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been activated successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to activate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactive')
                    ->label('Inactive')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_INACTIVE; // Use is_active instead of status
                                $record->save();

                                // Logout the deactivated user from all sessions
                                self::logoutUserById($record->id);
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been deactivated and logged out successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to deactivate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
            ]);
    }

    public static function logoutUserById($userId)
    {
        try {
            // Sessions are stored in a specific Redis database (REDIS_SESSION_DB=2 from .env)
            // We need to use the correct connection and database
            $sessionConnection = config('session.connection') ?: 'default';
            $prefix = config('database.redis.options.prefix', 'laravel_database_');
            $setKey = "user_sessions:{$userId}";

            Log::info("Starting logout process for user {$userId}", [
                'connection' => $sessionConnection,
                'prefix' => $prefix,
                'setKey' => $setKey,
                'session_db' => env('REDIS_SESSION_DB', '2')
            ]);

            // Get all session IDs for this user
            $sessionIds = Redis::connection($sessionConnection)->smembers($setKey);

            Log::info("Found sessions for user {$userId}", [
                'session_count' => count($sessionIds),
                'session_ids' => $sessionIds
            ]);

            // Debug Redis connection and try multiple approaches
            $redis = Redis::connection($sessionConnection);

            // Try different databases and key patterns
            $allResults = [];
            for ($db = 0; $db <= 2; $db++) {
                $redis->select($db);

                // Try different key patterns
                $patterns = [
                    $prefix . '*',           // laravel_database_*
                    'laravel_session:*',     // laravel_session:*
                    '*session*',             // anything with session
                    '*'                      // all keys
                ];

                foreach ($patterns as $pattern) {
                    $keys = $redis->keys($pattern);
                    $allResults["db_{$db}_{$pattern}"] = count($keys);

                    if (count($keys) > 0) {
                        Log::info("Found keys in Redis", [
                            'database' => $db,
                            'pattern' => $pattern,
                            'count' => count($keys),
                            'sample_keys' => array_slice($keys, 0, 5)
                        ]);
                    }
                }
            }

            Log::info("Redis scan results", $allResults);

            // Try all Redis databases to find where sessions are actually stored
            $userSessionKeys = [];
            $totalDeleted = 0;

            for ($db = 0; $db <= 2; $db++) {
                $redis->select($db);
                $allKeys = $redis->keys('laravel_database_*');

                if (count($allKeys) > 0) {
                    Log::info("Found session keys in database {$db}", [
                        'database' => $db,
                        'key_count' => count($allKeys),
                        'sample_keys' => array_slice($allKeys, 0, 3)
                    ]);

                    // Delete all session keys in this database
                    foreach ($allKeys as $key) {
                        $exists = $redis->exists($key);
                        if ($exists) {
                            $deleted = $redis->del($key);
                            if ($deleted > 0) {
                                $totalDeleted++;
                                $userSessionKeys[] = $key;
                                Log::info("Deleted session from DB {$db}", [
                                    'database' => $db,
                                    'key' => $key,
                                    'user_id' => $userId
                                ]);
                            }
                        }
                    }
                }
            }

            Log::info("Multi-database session deletion completed", [
                'user_id' => $userId,
                'total_sessions_deleted' => $totalDeleted
            ]);

            // If we still haven't deleted anything, try a more aggressive approach
            if ($totalDeleted == 0) {
                Log::info("No sessions deleted, trying aggressive cleanup");

                // Try to flush all sessions (nuclear option)
                for ($db = 0; $db <= 2; $db++) {
                    $redis->select($db);
                    $allKeys = $redis->keys('*');

                    foreach ($allKeys as $key) {
                        if (preg_match('/^laravel_database_[a-zA-Z0-9]{40}$/', $key)) {
                            $deleted = $redis->del($key);
                            if ($deleted > 0) {
                                $totalDeleted++;
                                Log::info("Aggressively deleted session", [
                                    'database' => $db,
                                    'key' => $key
                                ]);
                            }
                        }
                    }
                }
            }

            // Skip the complex session checking since we're doing bulk deletion

            // Also delete the user session tracking key we found
            $userSessionTrackingKey = "laravel_database_user_session:{$userId}";
            if ($redis->exists($userSessionTrackingKey)) {
                $redis->del($userSessionTrackingKey);
                Log::info("Deleted user session tracking key", [
                    'key' => $userSessionTrackingKey
                ]);
            }

            // The session deletion is already handled above in the multi-database approach

            // Clear the user sessions set from both databases
            Redis::connection($sessionConnection)->del($setKey);
            $redis = Redis::connection($sessionConnection);
            $redis->select(env('REDIS_SESSION_DB', 2));
            $redis->del($setKey);
            $redis->select(0);

            // Also try to delete from database sessions table as fallback
            try {
                $dbDeleted = DB::table('sessions')->where('user_id', $userId)->delete();
                Log::info("Database sessions deleted", ['count' => $dbDeleted]);
            } catch (\Exception $dbException) {
                Log::warning("Could not delete from sessions table: " . $dbException->getMessage());
            }

            Log::info("User {$userId} logout completed", [
                'user_id' => $userId,
                'sessions_found' => count($sessionIds),
                'sessions_deleted' => $totalDeleted
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to logout user {$userId}: " . $e->getMessage(), [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    public static function decodePhpSession($payload)
    {
        $result = [];
        $offset = 0;
        while ($offset < strlen($payload)) {
            $pos = strpos($payload, "|", $offset);
            if ($pos === false) break;
    
            $varnameLength = (int)substr($payload, $offset + 2, strpos($payload, ":", $offset + 2) - ($offset + 2));
            $varname = substr($payload, $offset + 4, $varnameLength); // skip s:<len>:"
    
            $offset = $pos + 1;
            $value = @unserialize(substr($payload, $offset));
            if ($value !== false || $payload[$offset] === "b") { // handle false boolean
                $result[$varname] = $value;
                $offset += strlen(serialize($value));
            } else {
                break;
            }
        }
        return $result;
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserManages::route('/'),
            'create' => Pages\CreateUserManage::route('/create'),
            'view' => Pages\ViewUserManage::route('/{record}'),
            'edit' => Pages\EditUserManage::route('/{record}/edit'),
        ];
    }
}
