<?php

namespace App\Filament\Admin\Resources\ProductResource\Widgets;

use App\Models\Product;
use Awcodes\TableRepeater\Components\TableRepeater;
use Awcodes\TableRepeater\Header;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Get;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\HtmlString;

class NormalProducts extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';

    protected static string $view = 'filament.pc.resources.product-resource.widgets.norml-widget';

    public function table(Table $table): Table
    {
        return $table
            ->paginated(false)
            ->query(
                function () {
                    $data = Product::query()->whereHas('productData', function ($query) {
                        $query->where('user_id', auth()->user()->id)
                            ->where('is_batch_wise_stock', false)
                            ->whereColumn('low_stock', '>', 'stock');
                    })->with('productData');

                    return $data;
                }
            )
            ->columns([
                TextColumn::make('name')->extraAttributes(['class' => 'w-full']),
                TextColumn::make('sku')
                    ->label('Stock Keeping Unit (SKU)')
                    ->extraAttributes(['class' => 'w-full']),
                TextColumn::make('productData.stock')
                    ->extraAttributes(['class' => 'w-full'])
                    ->formatStateUsing(function ($state) {
                        return new HtmlString('<div class="flex items-center w-full px-2 py-1 border border-gray-300 rounded-lg">
                            <input
                                type="text"
                                value="' . $state . '"
                                class="flex-grow text-gray-800 border-none focus:outline-none focus:ring-0"
                                readonly
                            />
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="w-5 h-5 text-gray-500 cursor-pointer"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="1"
                                d="M15.232 5.232l3.536 3.536M9 11l6-6 3.536 3.536-6 6H9v-3.536z"
                                />
                            </svg>
                            </div>');
                    })
                    ->action(
                        Action::make('viewBatches')
                            ->label('View Batches')
                            ->modalHeading('Batch Details')
                            ->form([
                                Section::make()->schema([
                                    TextInput::make('name')->disabled(),
                                    TextInput::make('sku')
                                        ->label('Stock Keeping Unit (SKU)')
                                        ->disabled(),
                                    Toggle::make('productData.is_batch_wise_stock')
                                        ->inline(false)
                                        ->live()
                                        ->reactive()
                                        ->formatStateUsing(function ($record) {
                                            $sessionData = session()->get('data.batch_data_.' . $record->id);
                                            $productData = $sessionData['productData'] ?? [];

                                            return isset($productData['is_batch_wise_stock']) ? $productData['is_batch_wise_stock'] : $record->productData->is_batch_wise_stock;
                                        })
                                        ->extraAttributes(['style' => 'margin-top: 40px !important;', 'class' => 'mt-4']),
                                ])
                                    ->columns(3),
                                Section::make()->schema([
                                    TextInput::make('available_stock'),
                                    DatePicker::make('expiry_date'),
                                ])
                                    ->columns(2)
                                    ->visible(function ($record, Get $get) {
                                        return ! $get('productData.is_batch_wise_stock');
                                    }),
                                TableRepeater::make('batches')
                                    ->headers([
                                        Header::make('Batch Name'),
                                        Header::make('Available Stock'),
                                        Header::make('Low Stock Trigger Value'),
                                        Header::make('Action'),
                                    ])
                                    ->schema([
                                        TextInput::make('batch_name'),
                                        TextInput::make('available_stock'),
                                        DatePicker::make('expiry_date'),
                                    ])
                                    ->columns(3)
                                    ->visible(function ($record, Get $get) {
                                        return $get('productData.is_batch_wise_stock');
                                    }),
                            ])
                            ->mountUsing(function ($form, $record) {
                                $sessionData = session()->get('data.batch_data_.' . $record->id);
                                $productData = $sessionData['productData'] ?? [];
                                $availableStock = $sessionData['available_stock'] ?? $record->productData->stock;
                                $expiryDate = $sessionData['expiry_date'] ?? $record->productData->expires_on_after;
                                $sessionBatchData = $sessionData['batches'] ?? [];
                                $batches = ! empty($sessionBatchData) ? $sessionBatchData : $record->batches->map(fn($batch) => [
                                    'batch_name' => $batch->batch_name,
                                    'available_stock' => $batch->available_stock,
                                    'expiry_date' => $batch->expiry_date,
                                ])->toArray();

                                return $form->fill([
                                    'name' => $record->name,
                                    'sku' => $record->sku,
                                    'productData' => $productData,
                                    'batches' => $batches,
                                    'available_stock' => $availableStock ?? 0,
                                    'expiry_date' => $expiryDate ?? null,
                                ]);
                            })
                            ->action(function (array $data, $record) {
                                session()->forget('data.batch_data_.' . $record->id);
                                session()->put('data.batch_data_.' . $record->id, $data);
                            })
                            ->modalWidth(MaxWidth::Full),

                    ),
            ]);
    }
}
