<?php

namespace App\Filament\Admin\Resources\ProductResource\Widgets;

use App\Models\Product;
use Filament\Forms\Get;
use Filament\Tables\Table;
use Livewire\Attributes\On;
use App\Models\ProductRelation;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\HtmlString;
use Filament\Tables\Actions\Action;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Widgets\TableWidget as BaseWidget;
use Awcodes\TableRepeater\Components\TableRepeater;
use Carbon\Carbon;
use Filament\Support\Enums\Alignment;

class BatchWiseProducts extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';

    protected static string $view = 'filament.pc.resources.product-resource.widgets.batch-wise-widget';

    #[On('refresh')]
    public function refreshTable(): void
    {
        $this->resetTable();
    }

    public function table(Table $table): Table
    {
        return $table
            ->poll('10s')
            ->paginated(false)
            ->query(function () {
                // Use caching service to get product IDs for user
                $userId = auth()->user()->id;
                $cacheKey = "batch_wise_products_user_{$userId}";
                $productIds = \Illuminate\Support\Facades\Cache::remember($cacheKey, 300, function() use ($userId) {
                    return ProductRelation::whereUserId($userId)
                        ->pluck('product_id')
                        ->toArray();
                });

                $batchWiseProducts = Product::whereIn('id', $productIds)
                    ->with('productData', 'batches', 'unit');
                return $batchWiseProducts;
            })
            ->columns([
                TextColumn::make('name'),
                TextColumn::make('sku')->label('Stock Keeping Unit (SKU)'),
                TextColumn::make('unit')
                    ->visible()
                    ->formatStateUsing(function ($record) {
                        return $record->unit->name;
                    }),
                TextColumn::make('productData.stock')
                    ->label('Stock')
                    ->formatStateUsing(function ($record) {
                        $stock = $record->productData->is_batch_wise_stock ? $record->batches->sum('available_stock') : $record->productData->stock;

                        return new HtmlString('<div class="inline-flex items-center justify-between w-32 h-10 px-2 py-1 bg-white border border-gray-300 rounded-md">
                            <input
                                type="text"
                                value="' . $stock . '"
                                class="w-full text-sm text-gray-800 truncate bg-transparent border-none cursor-default focus:outline-none focus:ring-0"
                                readonly
                            />
                            <a href="#" class="p-1 transition rounded shrink-0 hover:bg-gray-200">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="w-4 h-4 text-gray-500 cursor-pointer"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M15.232 5.232l3.536 3.536M9 11l6-6 3.536 3.536-6 6H9v-3.536z"
                                    />
                                </svg>
                            </a>
                        </div>');
                    })
                    ->action(
                        Action::make('viewBatches')
                            ->label('View Batches')
                            // ->visible(fn($record) => $record->productData->is_batch_wise_stock)
                            ->modalHeading(function ($record) {
                                if ($record->productData->is_batch_wise_stock) {
                                    return "Batch Wise" . " - " . $record->name;
                                }
                                return "Stock Wise" . " - " . $record->name;
                            })
                            ->form(function ($record) {
                                if ($record->productData->is_batch_wise_stock) {
                                    return [
                                        Section::make()->schema([
                                            TextInput::make('name')->disabled(),
                                            TextInput::make('sku')->disabled(),
                                            Toggle::make('productData.is_batch_wise_stock')
                                                ->inline(false)
                                                ->live()
                                                ->disabled()
                                                ->reactive()
                                                ->formatStateUsing(function ($record) {
                                                    $sessionData = session()->get('data.batch_data_.' . $record->id);
                                                    // dd($sessionData);
                                                    $productData = $sessionData['productData'] ?? [];
                                                    // dd(isset($productData['is_batch_wise_stock']) ? $productData['is_batch_wise_stock'] : $record->productData->is_batch_wise_stock);
                                                    return isset($productData['is_batch_wise_stock']) ? $productData['is_batch_wise_stock'] : $record->productData->is_batch_wise_stock;
                                                })
                                                ->extraAttributes(['style' => 'margin-top: 40px !important;', 'class' => 'mt-4']),
                                        ])
                                            ->columns(3),
                                        Section::make()->schema([
                                            TextInput::make('available_stock'),
                                            DatePicker::make('expiry_date'),
                                        ])
                                            ->columns(2)
                                            ->visible(function ($record, Get $get) {
                                                return ! $get('productData.is_batch_wise_stock');
                                            }),
                                        TableRepeater::make('batches')
                                            ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                                    ->extraAttributes([
                                                        'style' => 'border: none !important; box-shadow: none !important;'
                                                    ]);
                                            })
                                            ->reorderable(false)
                                            ->addActionAlignment(Alignment::End)
                                            ->headers([
                                                Header::make('Batch Name'),
                                                Header::make('Available Stock'),
                                                Header::make('Low Stock Trigger Value'),
                                                Header::make('Action'),
                                            ])
                                            ->schema([
                                                TextInput::make('batch_name'),
                                                TextInput::make('available_stock'),
                                                DatePicker::make('expiry_date')
                                                    ->displayFormat('M d,Y')
                                                    ->native(false)
                                                    ->minDate(now())
                                                    ->default(now()->addDays(10))
                                                    ->label('Expiry Date'),
                                            ])
                                            ->columns(3)
                                            ->visible(function ($record, Get $get) {
                                                return $get('productData.is_batch_wise_stock');
                                            }),
                                    ];
                                } else {
                                    return [
                                        TextInput::make('stock')
                                            ->numeric()
                                            ->autofocus(false),
                                    ];
                                }
                            })
                            ->mountUsing(function ($form, $record) {
                                // session()->forget('data');
                                $sessionData = session()->get('data.batch_data_.' . $record->id);
                                $normalSessionData = (session()->get('data')['normal'] ?? []);
                                $normalStockSession = null;
                                if (!empty($normalSessionData)) {
                                    foreach ($normalSessionData as $key => $session) {
                                        if ($key == $record->id) {
                                            $normalStockSession = $session;
                                        }
                                    }
                                }
                                $productData = $sessionData['productData'] ?? [];
                                $availableStock = $sessionData['available_stock'] ?? '';
                                $expiryDate = $sessionData['expiry_date'] ?? '';
                                $sessionBatchData = $sessionData['batches'] ?? [];
                                $batches = ! empty($sessionBatchData) ? $sessionBatchData : $record->batches->map(fn($batch) => [
                                    'batch_name' => $batch->batch_name,
                                    'available_stock' => $batch->available_stock,
                                    'expiry_date' => $batch->expiry_date,
                                ])->toArray();

                                return $form->fill([
                                    'name' => $record->name,
                                    'sku' => $record->sku,
                                    'productData' => $productData,
                                    'batches' => $batches,
                                    'available_stock' => $availableStock ?? 0,
                                    'expiry_date' => $expiryDate ?? null,
                                    'stock' => $normalStockSession ?? $record->productData->stock ?? null,
                                ]);
                            })
                            ->action(function (array $data, $record) {
                                session()->forget('data.batch_data_.' . $record->id);
                                session()->put('data.batch_data_.' . $record->id, $data);
                                session()->forget('data.normal.' . $record->id);
                                session()->put('data.normal.' . $record->id, $data['stock']);
                            })
                            ->modalWidth(function ($record) {
                                if ($record->productData->is_batch_wise_stock) {
                                    return MaxWidth::Full;
                                } else {
                                    return MaxWidth::Medium;
                                }
                            }),


                    ),
                TextColumn::make('productData.is_batch_wise_stock')
                    ->label('Stock Type')
                    ->formatStateUsing(function ($record) {
                        return $record->productData->is_batch_wise_stock ? 'Batch Wise' : 'Product Wise';
                    }),
                TextColumn::make('productData.expires_on_after')
                    ->date('d-m-Y')
                    ->formatStateUsing(function ($record, TextColumn $component) {
                        return $record && $record->productData && $record->productData->expires_on_after
                            ? Carbon::parse($record->productData->expires_on_after)->format('d-m-Y')
                            : null;
                    })
                    ->label('Expiry Date')
                // ->hidden(
                //     fn(TextColumn $column): bool => dd($column->getRecord())
                //     // $column->getRecord() &&
                //     //     $column->getRecord()->productData &&
                //     //     $column->getRecord()->productData->is_batch_wise_stock
                // )
            ]);
    }
}
