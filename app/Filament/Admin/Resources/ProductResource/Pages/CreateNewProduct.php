<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use Closure;
use App\Models\Unit;
use App\Models\Brand;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Distributor;
use App\Models\GenericName;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use App\Forms\Components\DragDrop;
use App\Rules\CaseSensitiveUnique;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs\Tab;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Placeholder;
use Filament\Resources\Pages\CreateRecord;
use App\Forms\Components\MultipleImageUploader;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Filament\Admin\Resources\ProductResource;
use Awcodes\TableRepeater\Components\TableRepeater;
use App\Component\Products\ProductDescriptionSection;
use App\Component\Products\ProductStockDetailsSection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Component\Products\ProductMultipleImageSection;
use App\Component\Products\ProductGeneralDetailsSection;
use App\Services\PostSaveAttributeService;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use App\Component\Products\NewProductPackagingDetailsSection;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;


class CreateNewProduct extends CreateRecord
{
    use HasBackButton;
    protected ?string $heading = 'Add Product';

    protected static string $resource = ProductResource::class;


    protected static string $view = 'filament.pc.resources.product-resource.pages.create-new-product';

    public ?array $data = [];

    public  $commission;

    public $pcCommission;

    public $globalCommission;

    public $finalCommissionType;

    public $finalCommission;

    public $productData = null;

    public $batchData = null;

    public $uploadedImages = null;

    public ?array $images = [];

    public ?string $defaultFileName = "";
    public $defaultIndex = 0;
    public $defaultImage = "";
    public $activityData = [];

    protected $listeners = ['updateDefaultImage', 'handleChangeDefaultImage'];




    protected function getCreatedNotification(): ?Notification
    {
        $title = $this->getCreatedNotificationTitle();

        if (blank($title)) {
            return null;
        }

        return Notification::make()
            ->success()
            ->body('Product has been Created Successfully')
            ->title($title);
    }

    protected function getRedirectUrl(): string
    {
        return ProductResource::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Product Catalog',
            "#" => 'Add Product',
        ];
    }


    public function refresh($uuid): void
    {
        $this->form->fill(["pcInfo_east.{$uuid}.min_quantity"]);

        // $this->fillForm();
    }

    public function handleChangeDefaultImage($fileName)
    {
        $this->defaultFileName = $fileName;
    }


    public function uploadFiles($data)
    {
        $uploadedFiles = [];
        $tempIds = $data['tempIds'] ?? [];
        $files = $data['files'] ?? [];
        foreach ($files as $index => $file) {
            $uploadedFiles[] = [
                'tempId' => $tempIds[$index],
                'id' => rand(0, 1000000),
                'original_url' => null,
            ];
        }

        return $uploadedFiles;
    }


    public function updatedImages($value)
    {
        // Ensure the state reflects all current media
        $mediaIds = $this->record->getMedia('product-images')->pluck('id');
        // $this->images = $mediaIds->toArray();
    }

    public function removeMedia($mediaId)
    {
        if (!$this->record) {
            return false;
        }

        $media = Media::find($mediaId);
        if ($media && $media->model_id == $this->record->id) {
            $media->delete();

            // Update the form state
            $currentState = $this->form->getState();
            if (isset($currentState['images'])) {
                $currentState['images'] = array_filter($currentState['images'], function ($id) use ($mediaId) {
                    return $id !== $mediaId;
                });
                $this->form->fill($currentState);
            }

            return true;
        }
        return false;
    }

    public function setDefaultImage($index)
    {
        $this->defaultIndex = $index;
    }

    public function clearLivewireTmpFiles()
    {
        $tmpPath = storage_path('app/livewire-tmp');
        if (File::exists($tmpPath)) {
            File::cleanDirectory($tmpPath);
        }
    }

    public ?Model $record = null;



    public function mount(): void
    {
        $this->record = $record ?? new Product();

        parent::mount();
        // static::addBackButton();
        $data = [
            'productData' => [
                'price_type' => 'fixed',
                'stock_type' => 'normal',
            ],
        ];
        $data['admin_fees'] = $this->finalCommission;
        $data['commission_type'] = $this->finalCommissionType;
        $data['pcInfo_east'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);

        $data['pcInfo_west'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);
        $this->form->fill($data);
    }

    public function form(Form $form): Form
    {
        return  $form->schema([
            TextInput::make('admin_fees')->hidden()->live(),
            TextInput::make('commission_type')->hidden(),
            ProductGeneralDetailsSection::make(),
            ProductMultipleImageSection::make(),
            ProductDescriptionSection::make(),
            NewProductPackagingDetailsSection::make(),
    
            // Group::make()
            //     ->visible(function ($livewire) {
            //         // dd($livewire);
            //     })
            //     ->schema([
            //         Section::make('Stock Details')
            //             ->schema([
            //                 Radio::make('productData.stock_type')
            //                     ->label('Manage Product')
            //                     ->options([
            //                         'normal' => 'Without Batch Number',
            //                         'batch' => 'By Batch'
            //                     ])
            //                     ->live()
            //                     ->inline()
            //                     ->formatStateUsing(fn() => 'batch')
            //                     ->rules(['required'])
            //                     ->columnSpanFull(),
            //                 Select::make('in_stock')
            //                     ->label(new HtmlString('Stock Status<span class="text-red-500" style="color:red;">*</span>'))
            //                     ->validationAttribute('stock status')
            //                     ->live()
            //                     ->rules(['required'])
            //                     ->options([
            //                         'yes' => 'In Stock',
            //                         'no' => 'Out of Stock',
            //                     ]),
            //                 TextInput::make('productData.stock')
            //                     ->placeholder('Stock')
            //                     ->label(new HtmlString('Stock<span class="text-red-500" style="color:red;">*</span>'))
            //                     ->rules(['numeric', 'gt:0'])
            //                     ->visible(fn(Get $get) => $get('productData.stock_type') == 'normal')
            //                     ->requiredIf(fn(Get $get) => $get('in_stock') == 'yes' && $get('productData.stock_type') == 'normal', true)
            //                     ->rules(function (Get $get) {
            //                         if ($get('in_stock') == 'yes') {
            //                             return ['required'];
            //                         }
            //                         return ['sometimes'];
            //                     })
            //                     ->validationAttribute('stock'),
            //                 Select::make('unit_id')
            //                     ->label(new HtmlString('Unit<span class="text-red-500" style="color:red;">*</span>'))
            //                     ->rules(['required'])
            //                     ->validationAttribute('unit')
            //                     ->options(fn() => Unit::all()->pluck('name', 'id')),
            //                 TextInput::make('productData.quantity_per_unit')
            //                     ->placeholder('Quantity per unit')
            //                     ->validationAttribute('quantity per unit')
            //                     ->rules(['required'])
            //                     ->label(new HtmlString('Quantity per unit<span class="text-red-500" style="color:red;">*</span>')),
            //                 Select::make('dosage_foams_id')
            //                     ->label('Product Form')
            //                     ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
            //                 TextInput::make('productData.low_stock')
            //                     ->placeholder('Low Stock Trigger Value')
            //                     ->numeric()
            //                     ->rules(function (Get $get) {
            //                         if ($get('in_stock') == 'yes') {
            //                             return ['required', 'numeric', 'gt:0'];
            //                         }
            //                         return ['sometimes', 'numeric', 'gt:0'];
            //                     })
            //                     ->validationAttribute('low stock trigger value')
            //                     ->label(new HtmlString('Low stock trigger value<span class="text-red-500" style="color:red;">*</span>')),
            //                 DatePicker::make('productData.expires_on_after')
            //                     ->minDate(today())
            //                     ->rules([Rule::date()->afterOrEqual(today())])
            //                     ->label('Expiry Date')
            //             ])->columns(3),
            //         Section::make()
            //             ->heading('Manage Batch')
            //             ->visible(fn(Get $get) => $get('productData.stock_type') == 'batch')
            //             ->schema([
            //                 TableRepeater::make('batches')
            //                     ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
            //                         return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
            //                             ->extraAttributes([
            //                                 'style' => 'border: none !important; box-shadow: none !important;'
            //                             ]);
            //                     })
            //                     ->defaultItems(3)
            //                     ->reorderable(false)
            //                     ->addActionAlignment(Alignment::End)
            //                     ->headers([
            //                         Header::make('Batch Name'),
            //                         Header::make('Available Stock'),
            //                         Header::make('Expiry Date'),
            //                         Header::make('Action'),
            //                     ])
            //                     ->schema([
            //                         TextInput::make('batch_name')
            //                             ->placeholder('Batch Name')
            //                             ->label('Name')
            //                             ->rules(function () {
            //                                 return [
            //                                     'required_if:productData.stock_type,batch',
            //                                     Rule::unique('products_batch', 'batch_name')->where(fn($query) => $query->where('user_id', auth()->id())),
            //                                 ];
            //                             })

            //                             ->label('Batch Name'),
            //                         TextInput::make('available_stock')
            //                             ->placeholder('Available Stock')
            //                             ->rule(function (Get $get) {
            //                                 if ($get('productData.stock_type') == 'batch' && $get('in_stock') == 'yes') {
            //                                     return 'required';
            //                                 }
            //                                 return 'sometimes';
            //                             })
            //                             ->label('Available Stock'),
            //                         DatePicker::make('expiry_date')
            //                             ->displayFormat('M d,Y')
            //                             ->minDate(today())
            //                             ->placeholder('Select the expiry date')
            //                             ->label('Expiry Date'),
            //                     ])
            //                     ->visible(fn(Get $get) => $get('productData.stock_type') == 'batch')
            //                     ->columnSpan('full'),
            //             ]),
            //         Section::make('Price')
            //             ->heading('Pricing')
            //             ->schema([
            //                 Radio::make('productData.price_type')
            //                     ->label('Select Pricing Structure')
            //                     ->extraAttributes([
            //                         'class' => 'gap-1',
            //                         'style' => 'margin:0px !important;'
            //                     ])
            //                     ->options([
            //                         'fixed' => new HtmlString('Fixed &nbsp;
            //                 <div x-data="{ tooltip: false }" class="relative inline-block">
            //                     <svg x-on:mouseenter="{tooltip: true}" x-on:mouseleave="{tooltip: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            //                     <circle cx="12" cy="12" r="10"></circle>
            //                     <line x1="12" y1="16" x2="12" y2="12"></line>
            //                     <line x1="12" y1="8" x2="12.01" y2="8"></line>
            //                     <title x-show="{tooltip : true}">Short Description about Bonus Pricing lorem impsum dolor sit amet consectetur. Commodo tellus pulvinar elit ac ultricies. Tincidunt sodales ipsum arcu amet</title>
            //                 </svg>
            //                 </div>
            //                 '),

            //                         'bonus' => new HtmlString('Bonus Pricing &nbsp;
            //                 <div x-data="{ tooltip2: false }" class="relative inline-block">
            //                     <svg x-on:mouseenter="{tooltip2: true}" x-on:mouseleave="{tooltip2: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            //                     <circle cx="12" cy="12" r="10"></circle>
            //                     <line x1="12" y1="16" x2="12" y2="12"></line>
            //                     <line x1="12" y1="8" x2="12.01" y2="8"></line>
            //                     <title x-show="{tooltip2 : true}">Short Description about Bonus Pricing lorem impsum dolor sit amet consectetur. Commodo tellus pulvinar elit ac ultricies. Tincidunt sodales ipsum arcu amet</title>
            //                 </svg>
            //                 </div>
            //                 '),

            //                         'tier' => new HtmlString('Tier Pricing &nbsp;
            //                 <div x-data="{ tooltip3: false }" class="relative inline-block">
            //                     <svg x-on:mouseenter="{tooltip3: true}" x-on:mouseleave="{tooltip3: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            //                     <circle cx="12" cy="12" r="10"></circle>
            //                     <line x1="12" y1="16" x2="12" y2="12"></line>
            //                     <line x1="12" y1="8" x2="12.01" y2="8"></line>
            //                     <title x-show="{tooltip3 : true}">Short Description about Bonus Pricing lorem impsum dolor sit amet consectetur. Commodo tellus pulvinar elit ac ultricies. Tincidunt sodales ipsum arcu amet</title>
            //                 </svg>
            //                 </div>
            //                 '),
            //                     ])
            //                     ->rules(['required'])
            //                     ->live()
            //                     ->inline(),
            //                 Section::make('Fixed Pricing')
            //                     ->heading('Fix Pricing')
            //                     ->extraAttributes(['style' => 'padding:0px !important;'])
            //                     ->visible(function (Get $get) {
            //                         if ($get('productData.price_type') == 'fixed') {
            //                             return true;
            //                         }
            //                         return false;
            //                     })
            //                     ->schema([
            //                         Group::make()
            //                             // ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
            //                             ->schema([
            //                                 PlaceHolder::make('Region'),
            //                                 PlaceHolder::make('price')->label("Price(RM)"),
            //                                 PlaceHolder::make('admin_fess')->label('Admin Fees'),
            //                                 PlaceHolder::make('net_earnings')->label('New Earnings')
            //                             ])->columns(4),
            //                         Group::make()
            //                             ->reactive()
            //                             ->schema([
            //                                 PlaceHolder::make('west_region')
            //                                     ->content('West Malaysia')
            //                                     ->label("")
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                                 TextInput::make('productData.west_zone_price')
            //                                     ->numeric()
            //                                     ->placeholder('West Zone Price')
            //                                     ->validationAttribute('west zone price')
            //                                     ->live(onBlur: true)
            //                                     ->label("test")
            //                                     ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                     ->rules(function (Get $get) {
            //                                         if ($get('productData.price_type') == 'fixed') {
            //                                             return ['required'];
            //                                         }
            //                                     })
            //                                     ->label(""),
            //                                 PlaceHolder::make('10')
            //                                     ->label("")
            //                                     ->content(function () {
            //                                         if ($this->finalCommissionType == 'percentage') {
            //                                             return $this->finalCommission . "%";
            //                                         }
            //                                         return 'RM ' . $this->finalCommission;
            //                                     })
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                                 PlaceHolder::make('25')
            //                                     ->label(function (Get $get) {
            //                                         if ($this->finalCommissionType == 'percentage') {
            //                                             $commission = $this->finalCommission * (float)$get('productData.west_zone_price') / 100;
            //                                             $earning = (float)$get('productData.west_zone_price') - $commission;
            //                                             return 'RM ' . number_format((float)$earning, 2);
            //                                         } else {
            //                                             $earning = (float)$get('productData.west_zone_price') - $this->finalCommission;
            //                                             return 'RM ' . number_format($earning, 2);
            //                                         }
            //                                         return 'RM15';
            //                                     })
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                                 PlaceHolder::make('east_region')
            //                                     ->label("")
            //                                     ->content('East Malaysia')
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                                 TextInput::make('productData.east_zone_price')
            //                                     ->numeric()
            //                                     ->placeholder('East Zone Price')
            //                                     ->live(onBlur: true)
            //                                     ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                     ->validationAttribute('east zone price')
            //                                     ->rules(function (Get $get) {
            //                                         if ($get('productData.price_type') == 'fixed') {
            //                                             return ['required'];
            //                                         }
            //                                     })
            //                                     ->label(""),
            //                                 PlaceHolder::make('10')
            //                                     ->label("")
            //                                     ->content(function () {
            //                                         if ($this->finalCommissionType == 'percentage') {
            //                                             return $this->finalCommission . "%";
            //                                         }
            //                                         return 'RM' . $this->finalCommission;
            //                                     })
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                                 PlaceHolder::make('25')
            //                                     ->label("")
            //                                     ->content(function (Get $get) {
            //                                         if ($this->finalCommissionType == 'percentage') {
            //                                             $commission = $this->finalCommission * (float)$get('productData.east_zone_price') / 100;
            //                                             $earning = (float)$get('productData.east_zone_price') - $commission;
            //                                             return 'RM ' . number_format((float)$earning, 2);
            //                                         } else {
            //                                             $earning = (float)$get('productData.east_zone_price') - $this->finalCommission;
            //                                             return 'RM ' . number_format($earning, 2);
            //                                         }
            //                                         return 'RM15';
            //                                     })
            //                                     ->extraAttributes(['class' => 'mt-3']),
            //                             ])->columns(4),
            //                     ]),
            //                 Group::make()
            //                     ->columnSpanFull()
            //                     ->visible(fn(Get $get) => $get('productData.price_type') === 'bonus')
            //                     ->schema([
            //                         Section::make('east_malasiya')
            //                             ->heading('East Malaysia')
            //                             ->schema([
            //                                 Group::make()->schema(function ($livewire) {
            //                                     return [
            //                                         TextInput::make('productData.east_zone_price')
            //                                             ->numeric()
            //                                             ->live(onBlur: true)
            //                                             ->placeholder('250.00')
            //                                             ->validationAttribute('east bonus base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'bonus') {
            //                                                     return ['required'];
            //                                                 }
            //                                             })

            //                                             ->afterStateUpdated(function (Get $get, Set $set) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.east_zone_price') * $get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.east_zone_price') - $commission;
            //                                                     $set('bonus_1_net_earnings', number_format((float)$earnings, 2, '.', ''));
            //                                                 } else {
            //                                                     $commission = (float)$get('productData.east_zone_price') * $get('admin_fees');
            //                                                     $earnings = (float)$get('productData.east_zone_price') - $commission;
            //                                                     $set('bonus_1_net_earnings', number_format((float)$earnings, 2, '.', ''));
            //                                                 }
            //                                             })
            //                                             ->prefix('RM')
            //                                             ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
            //                                         TextInput::make('admin_fees_1')
            //                                             ->disabled()
            //                                             ->formatStateUsing(function (Get $get) {
            //                                                 return $get('admin_fees');
            //                                             })
            //                                             ->prefix(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return '%';
            //                                                 }
            //                                                 return 'RM';
            //                                             })
            //                                             ->label('Admin Fees'),
            //                                         TextInput::make('bonus_1_net_earnings')
            //                                             ->reactive()
            //                                             ->disabled()
            //                                             ->prefix('RM')
            //                                             ->label('Net Earnings'),
            //                                     ];
            //                                 })->columns(3),
            //                                 Group::make()->schema([
            //                                     TextInput::make('productData.east_bonus_1_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('east bonus quantity')
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.price_type') == 'bonus') {
            //                                                 return ['required', 'integer', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['integer', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Quantity'),
            //                                     TextInput::make('productData.east_bonus_1_quantity_value')
            //                                         ->placeholder('Enter free quantity')
            //                                         ->validationAttribute('east bonus quantity value')
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.price_type') == 'bonus') {
            //                                                 return ['required', 'integer', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['lt:$maxQty', 'integer', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Bonus Quantity'),


            //                                     TextInput::make('productData.east_bonus_2_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('east bonus quantity')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->rules(['integer', 'regex:/^\d+$/'])
            //                                         ->label('Quantity'),
            //                                     TextInput::make('productData.east_bonus_2_quantity_value')
            //                                         ->placeholder('Enter free quantity')
            //                                         ->rules(function (Get $get) {
            //                                             if (!empty($get('productData.east_bonus_2_quantity'))) {
            //                                                 return ['required', 'numeric', 'gt:0', 'integer', 'regex:/^\d+$/'];
            //                                             }

            //                                             return ['integer', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->validationAttribute('east bonus quantity value')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Bonus Quantity'),
            //                                     TextInput::make('productData.east_bonus_3_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('east bonus quantity')
            //                                         ->rules(['integer', 'regex:/^\d+$/'])
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Quantity'),
            //                                     TextInput::make('productData.east_bonus_3_quantity_value')
            //                                         ->placeholder('Enter free quantity')
            //                                         ->rules(function (Get $get) {
            //                                             if (!empty($get('productData.east_bonus_3_quantity'))) {
            //                                                 return ['required', 'numeric', 'gt:0', 'integer', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['integer', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->validationAttribute('east bonus quantity value')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Bonus Quantity'),
            //                                 ])->columns(2),
            //                             ]),
            //                         Section::make('west_malasiya')
            //                             ->heading('West Malaysia')
            //                             ->schema([
            //                                 Group::make()->schema([
            //                                     TextInput::make('productData.west_zone_price')
            //                                         ->placeholder('250.00')
            //                                         ->extraAlpineAttributes(fn() => self::numericValueValidationRule())
            //                                         ->live(onBlur: true)
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.price_type') == 'bonus') {
            //                                                 return ['required'];
            //                                             }
            //                                         })
            //                                         ->afterStateUpdated(function (Get $get, Set $set) {
            //                                             if ($get('commission_type') == 'percentage') {
            //                                                 $commission = (float)$get('productData.west_zone_price') * (int)$get('admin_fees') / 100;
            //                                                 $earnings = (float)$get('productData.west_zone_price') - $commission;
            //                                                 $set('bonus_2_net_earnings', number_format((float)$earnings, 2, '.', ''));
            //                                             } else {
            //                                                 $commission = (float)$get('productData.west_zone_price') * (int)$get('admin_fees');
            //                                                 $earnings = (float)$get('productData.west_zone_price') - $commission;
            //                                                 $set('bonus_2_net_earnings', number_format((float)$earnings, 2, '.', ''));
            //                                             }
            //                                         })
            //                                         ->prefix('RM')
            //                                         ->validationAttribute('west bonus base price')
            //                                         ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
            //                                     TextInput::make('west_admin_fees')
            //                                         ->disabled()
            //                                         ->formatStateUsing(function (Get $get) {
            //                                             return $get('admin_fees');
            //                                         })
            //                                         ->prefix(function (Get $get) {
            //                                             if ($get('commission_type') == 'percentage') {
            //                                                 return '%';
            //                                             }
            //                                             return 'RM';
            //                                         })
            //                                         ->label('Admin Fees'),
            //                                     TextInput::make('bonus_2_net_earnings')
            //                                         ->disabled()
            //                                         ->reactive()
            //                                         ->prefix('RM')
            //                                         ->label('Net Earnings'),
            //                                 ])->columns(3),
            //                                 Group::make()->schema([
            //                                     TextInput::make('productData.west_bonus_1_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('west bonus quantity')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.price_type') == 'bonus') {
            //                                                 return ['required', 'numeric', 'max:999999', "gt:0", 'integer', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
            //                                         })
            //                                         ->label('Quantity')
            //                                         ->requiredWith('productData.west_base_price'),
            //                                     TextInput::make('productData.west_bonus_1_quantity_value')
            //                                         ->placeholder('Enter bonus quantity')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.price_type') == 'bonus') {
            //                                                 return ['required',  'integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
            //                                         })
            //                                         ->requiredWith('west_base_price')
            //                                         ->label('Bonus Quantity'),

            //                                     TextInput::make('productData.west_bonus_2_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('west free quantity')
            //                                         ->rules(function (Get $get) {
            //                                             if ($get('productData.west_bonus_1_quantity_value') > 0) {
            //                                                 return ['required', 'numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Quantity'),
            //                                     TextInput::make('productData.west_bonus_2_quantity_value')
            //                                         ->placeholder('Enter free quantity')
            //                                         ->rules(function (Get $get) {
            //                                             if (!empty($get('productData.west_bonus_2_quantity'))) {
            //                                                 return ['required', 'numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Bonus Quantity'),

            //                                     TextInput::make('productData.west_bonus_3_quantity')
            //                                         ->placeholder('Enter quantity')
            //                                         ->validationAttribute('west bonus quantity')
            //                                         ->rules(['integer', 'regex:/^\d+$/'])
            //                                         ->live(onBlur: true)
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->label('Quantity'),
            //                                     TextInput::make('productData.west_bonus_3_quantity_value')
            //                                         ->placeholder('Enter free quantity')
            //                                         ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                         ->rules(function (Get $get) {
            //                                             if (!empty($get('productData.west_bonus_3_quantity'))) {
            //                                                 return ['required', 'numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                             }
            //                                             return ['numeric', 'gt:0', 'integer', 'max:999999', 'regex:/^\d+$/'];
            //                                         })
            //                                         ->label('Bonus Quantity'),
            //                                 ])->columns(2),
            //                             ])
            //                     ]),

            //                 //tier pricing
            //                 Group::make()
            //                     ->visible(fn(Get $get) => $get('productData.price_type') === 'tier')
            //                     ->schema(function ($get) {
            //                         return [
            //                             Section::make('East Malaysia')->schema([

            //                                 Group::make()
            //                                     ->extraAttributes(['class' => 'ml-2'])
            //                                     ->schema([
            //                                         PlaceHolder::make('tier_1'),
            //                                         TextInput::make('productData.east_tier_1_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->formatStateUsing(fn() => 1)
            //                                             ->readOnly()
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->rule(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return 'required';
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.east_tier_1_max_quantity')
            //                                             ->placeholder('Enter max quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->validationAttribute('east tier 1 max quantity')
            //                                             ->live(onBlur: true)
            //                                             ->afterStateUpdated(function (Get $get, Set $set) {

            //                                                 if ((int)$get('productData.east_tier_1_max_quantity') > 0) {
            //                                                     $maxQty = (int)$get('productData.east_tier_1_max_quantity') + 1;
            //                                                     $set('productData.east_tier_2_min_quantity', $maxQty);
            //                                                 } else {
            //                                                     $set('productData.east_tier_2_min_quantity', null);
            //                                                 }
            //                                             })
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     $minQty = (int) $get('productData.east_tier_1_min_quantity');
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.east_tier_1_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('east tier 1 base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             // ->live(onBlur: true)
            //                                             ->rule(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return 'required';
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         PlaceHolder::make('productData.east_tier_1_percent')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return $get('admin_fees') . "%";
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         PlaceHolder::make('productData.east_tier_1_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.east_tier_1_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.east_tier_1_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.east_tier_1_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             })->reactive(),
            //                                         PlaceHolder::make('tier_2'),
            //                                         TextInput::make('productData.east_tier_2_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->reactive()
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.east_tier_1_max_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->validationAttribute('east tier 2 min quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->label(""),
            //                                         TextInput::make('productData.east_tier_2_max_quantity')
            //                                             ->placeholder('Enter max quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->validationAttribute('east tier 2 max quantity')
            //                                             ->reactive()
            //                                             // ->live(onBlur: true)
            //                                             ->afterStateUpdated(function (Get $get, Set $set) {
            //                                                 if ((int)$get('productData.east_tier_2_max_quantity') > 0) {
            //                                                     $maxQty = (int)$get('productData.east_tier_2_max_quantity') + 1;
            //                                                     $set('productData.east_tier_3_min_quantity', $maxQty);
            //                                                 } else {
            //                                                     $set('productData.east_tier_3_min_quantity', null);
            //                                                 }
            //                                             })
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     $minQty = (int) $get('productData.east_tier_2_min_quantity');
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.east_tier_2_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('east tier 2 base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             // ->live(onBlur: true)
            //                                             ->rule(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return 'required';
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         PlaceHolder::make('productData.east_tier_2_percent')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return $get('admin_fees') . "%";
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         PlaceHolder::make('productData.east_tier_2_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.east_tier_2_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.east_tier_2_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.east_tier_2_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             }),

            //                                         PlaceHolder::make('tier_3'),
            //                                         TextInput::make('productData.east_tier_3_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->validationAttribute('east tier 3 min quantity')
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.east_tier_2_max_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         Placeholder::make('productData.east_tier_3_max_quantity')
            //                                             ->extraAttributes(['class' => 'pt-3'])
            //                                             ->label(new HtmlString('<div class="pt-3">and above</div>')),
            //                                         TextInput::make('productData.east_tier_3_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('east tier 3 base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             // ->live(onBlur: true)
            //                                             ->rule(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return 'required';
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         PlaceHolder::make('productData.east_tier_3_percent')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $fees = $get('admin_fees') . "%";
            //                                                     return new HtmlString("<div class='pt-3'>$fees</div>");
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         PlaceHolder::make('productData.east_tier_3_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.east_tier_3_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.east_tier_3_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.east_tier_3_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             }),

            //                                     ])->columns(6),
            //                             ]),

            //                             Section::make('West Malaysia')->schema([

            //                                 Group::make()
            //                                     ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 0px;margin-right: 0px;'])
            //                                     ->schema([
            //                                         PlaceHolder::make('Type'),
            //                                         PlaceHolder::make('min_quantity')->label("Min Qty"),
            //                                         PlaceHolder::make('maz_qty')->label("Max Qty"),
            //                                         PlaceHolder::make('price')->label("Price per Unit"),
            //                                         PlaceHolder::make('admin_fess'),
            //                                         PlaceHolder::make('net_earnings')
            //                                     ])->columns(6),
            //                                 Group::make()
            //                                     ->extraAttributes(['class' => 'ml-2'])
            //                                     ->schema([
            //                                         PlaceHolder::make('tier_1'),
            //                                         TextInput::make('productData.west_tier_1_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->formatStateUsing(fn() => 1)
            //                                             ->readOnly()
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->rule(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return 'required';
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.west_tier_1_max_quantity')
            //                                             ->placeholder('Enter max quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->validationAttribute('west tier 1 max quantity')
            //                                             ->live(onBlur: true)
            //                                             ->afterStateUpdated(function (Get $get, Set $set) {
            //                                                 if ((int)$get('productData.west_tier_1_max_quantity') > 0) {
            //                                                     $maxQty = (int)$get('productData.west_tier_1_max_quantity') + 1;
            //                                                     $set('productData.west_tier_2_min_quantity', $maxQty);
            //                                                 } else {
            //                                                     $set('productData.west_tier_2_min_quantity', null);
            //                                                 }
            //                                             })
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.west_tier_1_min_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.west_tier_1_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('west tier 1 base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->live(onBlur: true)
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', 'price'];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         PlaceHolder::make('west_admin_fees')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return $get('admin_fees') . "%";
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         PlaceHolder::make('wesst_1_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.west_tier_1_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.west_tier_1_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.west_tier_1_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             }),

            //                                         PlaceHolder::make('tier_2'),
            //                                         TextInput::make('productData.west_tier_2_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->validationAttribute('west tier 2 min quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.west_tier_1_max_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.west_tier_2_max_quantity')
            //                                             ->placeholder('Enter max quantity')
            //                                             ->validationAttribute('west tier 2 max quantity')
            //                                             ->live(onBlur: true)
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->afterStateUpdated(function (Get $get, Set $set) {
            //                                                 if ((int)$get('productData.west_tier_2_max_quantity') > 0) {
            //                                                     $maxQty = (int)$get('productData.west_tier_2_max_quantity') + 1;
            //                                                     $set('productData.west_tier_3_min_quantity', $maxQty);
            //                                                 } else {
            //                                                     $set('productData.west_tier_3_min_quantity', null);
            //                                                 }
            //                                             })
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.west_tier_2_min_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         TextInput::make('productData.west_tier_2_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('west tier 2 base price')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->live(onBlur: true)
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', 'price'];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         PlaceHolder::make('west_admin_fees')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return $get('admin_fees') . "%";
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         PlaceHolder::make('west_tier_2_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.west_tier_2_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.west_tier_2_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.west_tier_2_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             }),

            //                                         PlaceHolder::make('tier_3'),
            //                                         TextInput::make('productData.west_tier_3_min_quantity')
            //                                             ->placeholder('Enter min quantity')
            //                                             ->validationAttribute('west tier 3 min quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->rules(function (Get $get) {
            //                                                 $minQty = (int) $get('productData.west_tier_2_max_quantity');
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', "gt:$minQty"];
            //                                                 }
            //                                             })
            //                                             ->label(""),
            //                                         Placeholder::make('productData.west_tier_3_max_quantity')
            //                                             ->extraAttributes(fn() => self::numericValueValidationRule())
            //                                             ->label("and above"),
            //                                         TextInput::make('productData.west_tier_3_base_price')
            //                                             ->placeholder('Enter base price')
            //                                             ->validationAttribute('west tier 3 base price')
            //                                             ->rules(function (Get $get) {
            //                                                 if ($get('productData.price_type') == 'tier') {
            //                                                     return ['required', 'price'];
            //                                                 }
            //                                             })
            //                                             ->live(onBlur: true)
            //                                             ->label(""),
            //                                         PlaceHolder::make('west_3_admin_fees')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     return $get('admin_fees') . "%";
            //                                                 }
            //                                                 return 'RM ' . $get('admin_fees');
            //                                             }),
            //                                         // ->label("15%"),
            //                                         Placeholder::make('west_3_net_earnings')
            //                                             ->label(function (Get $get) {
            //                                                 if ($get('commission_type') == 'percentage') {
            //                                                     $commission = (float)$get('productData.west_tier_3_base_price') * (float)$get('admin_fees') / 100;
            //                                                     $earnings = (float)$get('productData.west_tier_3_base_price') - $commission;
            //                                                     return 'RM ' . number_format((float)$earnings, 2, '.', '');
            //                                                 } else {
            //                                                     return 'RM ' . (float)$get('productData.west_tier_3_base_price') - (float)$get('admin_fees');
            //                                                 }
            //                                                 return 'RM15';
            //                                             }),

            //                                     ])->columns(6),
            //                             ])
            //                         ];
            //                     })
            //             ]),
            //     ])




        ])->statePath('data');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        unset($data['images']);
        $productData = $data['productData'] ?? null;
        unset($data['productData']);
        unset($data['in_stock']);
        unset($data['east_net_earnings']);
        unset($data['west_net_earnings']);
        unset($data['east_admin_fees']);
        unset($data['west_admin_fees']);
        $batchkData = $this->data['batches'] ?? [];
        unset($data['stock_type']);
        unset($data['batches']);
        $data['add_request_by'] = Auth::user()->id;
        $this->productData = $productData;
        $this->batchData = $batchkData;
        $data['owner_id'] = Auth::user()->id; //need to update after sub user introduced
        $data['status'] = 'approved';
        $this->activityData = $data;
        return $data;
    }

    protected function afterCreate(): void
    {
        $product = $this->record;
        $defaultImage = $this->defaultIndex;
        if (isset($defaultImage)) {
            $defaulImages = DB::table('media')->where(['model_id' => $this->record->id])->get();
            foreach ($defaulImages as $key => $value) {
                if ($key == $defaultImage) {
                    $defaultImage = $value->id;
                }
            }
            $this->record->update(['default_image_id' => $defaultImage]);
        }


        if (!empty($this->productData) && is_array($this->productData)) {
            $productRelationId = $product->productData()->create($this->productData);
            $sku = mb_substr($this->data['name'], 0, 4) . "_" . $productRelationId->id . "_" . rand(100000, 999999);
            $productRelationId->update(['sku' => $sku]);
            if (!empty($this->batchData) && is_array($this->batchData)) {
                foreach ($this->batchData as $batch) {
                    $batch['products_relation_id'] = $productRelationId->id;
                    $batch['product_id'] = $product->id;
                    $batch['user_id'] = Auth::user()->id;
                    ProductBatch::insert($batch);
                }
            }
        }
        $activityData['Name'] = Str::upper($product->name);
        $activityData['Category'] = $product->category->name;
        $activityData['SubCategory'] = $product->subcategory->name;
        $activityData['Brand'] = $product->brand->name;
        $activityData['Volume Unit'] = $product->unit->name;
        $activityData['Product form'] = $product->foam->name;
        $activityData['Volume by packaging'] = $product->quantity_per_unit;
        $activityData['Container'] = $product->container->name;

        activity()
                ->performedOn($product)
                ->causedBy(Auth::user())
                ->withProperties(['attributes' => $activityData])
                ->log("Master Product created by Admin ($product->name)");
    }

    public function saveAction(): Action
    {
        return Action::make('save')
            ->label('Add')
            ->action('create');
    }

    public function reAction(): Action
    {
        return Action::make('cancel')
            ->label('Cancel')
            ->action('resetForm');
    }

    public function saveData()
    {
        $this->validate();
        $productId = DB::table('products')->insertGetId([
            'name' => $this->data['name'],
            'category_id' => $this->data['category_id'],
            'sub_category_id' => $this->data['sub_category_id'],
            'container_id' => $this->data['container_id'],
            'unit_id' => $this->data['unit_id'],
            'sku' => $this->data['sku'],
            'dosage_foams_id' => $this->data['dosage_foams_id'],
            // 'unit_id' => $this->data['unit_id'],
            // 'quantity_per_unit' => $this->data['quantity_per_unit'],
            'add_request_by' => auth()->user()->id,
            'status' => 'pending',
        ]);
        $product = Product::find($productId);
        if (isset($this->data['images']) && is_array($this->data['images'])) {
            foreach ($this->data['images'] as $image) {
                if ($image instanceof TemporaryUploadedFile) {
                    $product->addMedia($image->getRealPath())
                        ->usingFileName($image->getClientOriginalName())
                        ->toMediaCollection('product-images');
                }
            }
        }

        $prductRelationData = $this->data['productData'];
        $productRelationId = DB::table('products_relation')->insertGetId([
            'product_id' => $productId,
            'user_id' => auth()->user()->id,
            'requested_by' => auth()->user()->id,
            'quantity_per_unit' => $prductRelationData['quantity_per_unit'],
            'price_type' => $prductRelationData['price_type'],
            'low_stock' => $prductRelationData['low_stock'],
            'west_bonus_3_base_price' => $prductRelationData['west_bonus_1_base_price'] ?? null,
            'west_bonus_2_base_price' => $prductRelationData['east_bonus_1_base_price'] ?? null,
            'east_bonus_1_quantity' => $prductRelationData['east_bonus_1_quantity'] ?? null,
            'west_bonus_1_quantity' => $prductRelationData['west_bonus_1_quantity'] ?? null,
            'east_bonus_1_quantity_value' => $prductRelationData['east_bonus_1_quantity_value'] ?? null,
            'west_bonus_1_quantity_value' => $prductRelationData['west_bonus_1_quantity_value'] ?? null,
            'is_batch_wise_stock' => $prductRelationData['stock_type'] == 'batch',
            'west_zone_price' => $prductRelationData['west_zone_price'] ?? null,
            'east_zone_price' => $prductRelationData['east_zone_price'] ?? null,
            'stock' => $prductRelationData['stock'] ?? null,
            'expires_on_after' => $prductRelationData['expires_on_after'] ?? null,
            'west_tier_3_base_price' => $prductRelationData['west_tier_3_base_price'] ?? null,
            'west_tier_2_base_price' => $prductRelationData['west_tier_2_base_price'] ?? null,
            'west_tier_1_base_price' => $prductRelationData['west_tier_1_base_price'] ?? null,

            'east_tier_3_base_price' => $prductRelationData['east_tier_3_base_price'] ?? null,
            'east_tier_2_base_price' => $prductRelationData['east_tier_2_base_price'] ?? null,
            'east_tier_1_base_price' => $prductRelationData['east_tier_1_base_price'] ?? null,

            'west_tier_3_max_quantity' => $prductRelationData['west_tier_3_max_qty'] ?? null,
            'west_tier_2_max_quantity' => $prductRelationData['west_tier_2_max_qty'] ?? null,
            'west_tier_1_max_quantity' => $prductRelationData['west_tier_1_max_qty'] ?? null,
            'west_tier_3_min_quantity' => $prductRelationData['west_tier_3_min_qty'] ?? null,
            'west_tier_2_min_quantity' => $prductRelationData['west_tier_2_min_qty'] ?? null,
            'west_tier_1_min_quantity' => $prductRelationData['west_tier_1_min_qty'] ?? null,

            'west_tier_3_max_quantity' => $prductRelationData['west_tier_3_max_qty'] ?? null,
            'west_tier_2_max_quantity' => $prductRelationData['west_tier_2_max_qty'] ?? null,
            'west_tier_1_max_quantity' => $prductRelationData['west_tier_1_max_qty'] ?? null,
            'west_tier_3_min_quantity' => $prductRelationData['west_tier_3_min_qty'] ?? null,
            'west_tier_2_min_quantity' => $prductRelationData['west_tier_2_min_qty'] ?? null,
            'west_tier_1_min_quantity' => $prductRelationData['west_tier_1_min_qty'] ?? null,

        ]);
        $batchData = $this->data['batches'];
        $batchDataFinal = [];
        foreach ($batchData as $key => $batch) {
            $batchDataFinal[$key]['batch_name'] = $batch['batch_name'];
            $batchDataFinal[$key]['products_relation_id'] = $productRelationId;
            $batchDataFinal[$key]['user_id'] = auth()->user()->id;
            $batchDataFinal[$key]['product_id'] = $productId;
        }
        $productBatchId = DB::table('products_batch')->insert($batchDataFinal);
        Notification::make()
            // ->title('Product Created')
            ->title('Product has been created successfully !')
            ->success()->send();
    }

    public function resetForm()
    {
        $this->data = [
            'price_type' => 'fixed',
        ];
        $this->form->fill($this->data);
        return redirect()->to(ProductResource::getUrl('index'));
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }
}
