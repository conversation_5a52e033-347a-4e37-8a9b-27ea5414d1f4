<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use App\Models\Unit;
use App\Models\Brand;
use Filament\Actions;
use App\Models\Product;
use Filament\Forms\Get;
use App\Models\Category;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Distributor;
use App\Models\GenericName;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Illuminate\Validation\Rule;
use App\Forms\Components\DragDrop;
use App\Rules\CaseSensitiveUnique;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Tabs;
use Illuminate\Support\Facades\Log;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Filament\Forms\Components\RichEditor;
use App\Filament\Admin\Resources\ProductResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class EditProduct extends EditRecord
{
    use HasBackButton;
    protected static string $resource = ProductResource::class;

    public $defaultIndex;

    public $images;

    public $oldData = [];

    protected function getSavedNotification(): ?Notification
    {
        $title = $this->getSavedNotificationTitle();

        if (blank($title)) {
            return null;
        }

        return Notification::make()
            ->success()
            ->body('Product Updated Successfully')
            ->title($title);
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Product Catalog',
            "#" => 'Product Edit',
        ];
    }

    protected function getRedirectUrl(): ?string
    {
        return ProductResource::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('index')),

        ];
    }

    public function setDefaultImage($index)
    {
        $this->defaultIndex = $index;
    }

    public function removeMedia($mediaId)
    {
        try {
            Media::findOrFail($mediaId)->delete();
        } catch (\Exception $e) {
            Log::error('Failed to delete media: ' . $e->getMessage());
        }
        $currentState = $this->form->getState();
        if (isset($currentState['images'])) {
            // First remove the media from database
            // $media = Media::findOrFail($mediaId);

            // dd($media);
            // if ($media) {
            //     $media->delete();
            // }

            // Convert mediaId to string for comparison since UUIDs are strings
            $mediaIdString = (string) $mediaId;

            // Filter out the removed media ID
            $currentState['images'] = array_values(array_filter($currentState['images'], function ($uuid) use ($mediaIdString) {
                return $uuid !== $mediaIdString;
            }));

            // Update the form state
            $this->form->fill($currentState);
        }


        // Remove from media library if record exists
        if ($this->record instanceof \App\Models\Product) {
            $media = $this->record->getMedia('images')->find($mediaId);
            if ($media) {
                $media->delete();
            }
        }
        // dd($this->form->getState());
        // $this->form->validateOnly('images');
    }

    public function clearLivewireTmpFiles()
    {
        $tmpPath = storage_path('app/private/livewire-tmp');
        File::cleanDirectory($tmpPath); // Removes all temporary files
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Section::make()->schema([
                TextInput::make('name')
                    ->label('Product Name')
                    ->validationAttribute('product name')
                    ->label(new HtmlString('Product Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->rules(fn($record) => ['required', 'max:255',  Rule::unique('products', 'name')->ignore($record->id)]),
                Select::make('brand_id')
                    ->label(new HtmlString('Brand<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('brand')
                    ->options(fn() => Brand::pluck('name', 'id'))
                    ->rules(['required']),
                Select::make('category_id')
                    ->label(new HtmlString('Category<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('category')
                    ->options(fn() => Category::whereNull('parent_id')->pluck('name', 'id'))
                    ->reactive()
                    ->rules(['required']),
                Select::make('sub_category_id')
                    ->label('Sub Category')
                    ->options(function (Get $get) {
                        if (!empty($get('category_id'))) {
                            $subCategories = Category::where('parent_id', $get('category_id'))->pluck('name', 'id');
                            return $subCategories;
                        }
                        return [];
                    }),
                SpatieMediaLibraryFileUpload::make('mda_document')
                    ->collection('mda-documents')
                    ->visibility('public')
                    ->disk('s3')
                    ->columnSpanFull()
                    ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg', 'application/pdf'])
                    ->maxSize(2048)
                    ->visible(function (Get $get) {
                        return (bool) Category::find($get('sub_category_id'))?->is_mda;
                    })
                    ->reactive(),
                Select::make('generic_name_id')
                    ->label(new HtmlString('Generic Name<span class="text-red-500" style="color:red;">*</span>'))
                    ->rules(['required'])
                    ->options(fn() => GenericName::pluck('name', 'id')),
                TextInput::make('sku')
                    ->label(new HtmlString('Stock Keeping Unit (SKU)<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('SKU')
                    ->rules(fn($record) => ['required', 'max:255', Rule::unique('products', 'sku')->ignore($record->id)]),
                Select::make('distributor_id')
                    ->relationship('distributors', 'name')
                    ->searchable(['name'])
                    ->createOptionForm([
                        Section::make()->schema([
                            TextInput::make('name')
                                ->label(new HtmlString("Distributor Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                ->maxLength(100)
                                ->placeholder('Enter Distributor Name')
                                ->rules([
                                    'required',
                                    // 'regex:/^[\w\s\p{P}]+$/u',
                                    'max:100',
                                    fn(Get $get) => new CaseSensitiveUnique(Distributor::class, 'name', $get('id'))
                                ])
                                ->validationMessages([
                                    'required' => __('message.distributor.required'),
                                    // 'regex' => __('message.distributor.regex'),
                                    'max' => __('message.distributor.max'),
                                    'App\\Rules\\CaseSensitiveUnique' => __('message.distributor.case_sensitive_unique'),
                                ]),

                        ]),
                    ])
                    ->preload()
                    ->multiple()
                    ->label(new HtmlString('Distributor<span class="text-red-500" style="color:red;">*</span>'))
                    ->validationAttribute('distributor')
                    ->rules(['required']),
                Radio::make('is_prescription_required')
                    ->label('Prescription Required')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ])
                    ->inline()
                    ->default(0)

            ])->columns(2),
            Section::make()
                ->extraAttributes(['style' => 'z-index: 1 !important;position:relative;'])
                ->schema([
                    DragDrop::make('images')
                        ->formatStateUsing(function ($state) {
                            return $state;
                        })
                        ->rules(['required'])
                        ->required()
                        ->collection('product-images')
                        ->multiple()
                        ->validationAttribute('images')
                        ->label(new HtmlString('Images<span class="text-red-500" style="color:red;">*</span>'))
                        ->validationMessages([
                            'image' => 'The :attribute must be an image.',
                        ])
                        ->collection('product-images')
                        ->dehydrated(true)
                        ->beforeStateDehydrated(function ($state) {
                            return array_filter((array) $state);
                        })
                ]),
            Section::make()
                ->heading('Product Description')
                ->schema([
                    Tabs::make('Product Details')
                        ->schema([
                            Tab::make('Product Description')
                                ->schema([
                                    RichEditor::make('product_description') // need to add migration
                                ]),
                            Tab::make('Key Ingredients')
                                ->schema([
                                    RichEditor::make('description_ingredients') // need to add migration
                                ]),
                            Tab::make('Storage Instructions')
                                ->schema([
                                    RichEditor::make('description_storage_instructions')  // need to add migration
                                ]),
                            Tab::make('Usage/Indication')
                                ->schema([
                                    RichEditor::make('description_indications')
                                ]),
                            Tab::make('Contradiction')
                                ->schema([
                                    RichEditor::make('description_contradictions')
                                ]),
                            Tab::make('How to Use')
                                ->schema([
                                    RichEditor::make('description_how_to_use') // need to add migration
                                ]),
                            Tab::make('Safety Information/Pregnancy')
                                ->schema([
                                    RichEditor::make('description_safety_information') // need to add migration
                                ]),
                            Tab::make('Dosage Information')
                                ->schema([
                                    RichEditor::make('description_dosage')
                                ]),
                            Tab::make('Side Effects')
                                ->schema([
                                    RichEditor::make('description_side_effects')
                                ]),

                        ])
                ]),
            Section::make()
                ->schema([
                    Group::make()
                        ->schema([
                            Select::make('container_id')
                                ->label(fn() => PackagingToolTip::tooltip())
                                ->rules(['required'])
                                ->live()
                                ->validationAttribute('container')
                                ->options(fn() =>  Container::all()->pluck('name', 'id')),
                            Select::make('dosage_foams_id')
                                ->label('Product Form')
                                ->options(fn() =>  DosageForm::all()->pluck('name', 'id')),
                            TextInput::make('quantity_per_unit')
                                ->numeric()
                                ->placeholder('Quantity per unit')
                                ->validationAttribute('quantity per unit')
                                ->rules(['required', 'numeric', 'max:999999999'])
                                ->label(function (Get $get) {
                                    if (!empty($get('container_id'))) {
                                        $containerName = Container::find($get('container_id'))?->name;
                                        return new HtmlString("Volume by $containerName<span class='text-red-500' style='color:red;'>*</span>");
                                    }
                                    return new HtmlString('Volume<span class="text-red-500" style="color:red;">*</span>');
                                }),
                            Select::make('unit_id')
                                ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>'))
                                ->rules(['required'])
                                ->validationAttribute('unit')
                                ->options(fn() => Unit::all()->pluck('name', 'id')),



                            TextInput::make('weight')
                                ->label(new HtmlString('Weight (gms)<span class="text-red-500" style="color:red;">*</span>'))
                                // ->numeric()
                                ->validationMessages([
                                    'numeric' => 'The :attribute must be a number.',
                                ])
                                ->validationAttribute('weight')
                                ->rules(['required',  'numeric', 'gt:0'])
                        ])->columns(5)
                ]),
        ]);
    }

    public function mutateFormDataBeforeSave(array $data): array
    {
        $this->oldData = Product::find($this->record->id)->toArray();
        $this->images = $data['images'];
        unset($data['images']);
        return $data;
    }

    public function afterSave(): void
    {
        $product = $this->record;
        $pcRelation = $product->productDataForPc($product->owner_id);
        if (!empty($productRelation)) {
            $pcRelation->update(['pc_approval' => false, 'pc_rejection_reason' => null]);
        }
        $changes = $product->getChanges();
        $ignoreFields = ['updated_at', 'created_at', 'id', 'default_image_id'];
        $changes = array_diff_key($changes, array_flip($ignoreFields));
        $changeKeys = array_keys($changes);
        $oldFinal = collect($this->oldData)->only($changeKeys)->toArray();
        $defaultImage = $this->defaultIndex;
        if (isset($defaultImage)) {
            $media = $product->getMedia('product-images')->get($defaultImage);
            if ($media) {
                $product->update(['default_image_id' => $media->id]);
            }
        }
        //here in activity log I want to add the old data and new data
        $oldData = ['old' => $oldFinal];
        $newData = ['attributes' => $changes];
        $activityData = array_merge($oldData, $newData);
        activity()
            ->performedOn($product)
            ->causedBy(Auth::user())
            ->withProperties($activityData ?? [])
            ->log("Master Product updated by Admin ($product->name)");
    }
}
