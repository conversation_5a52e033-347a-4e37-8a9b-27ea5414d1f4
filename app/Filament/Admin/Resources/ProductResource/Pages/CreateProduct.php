<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use Filament\Actions;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use App\Models\ProductRelation;
use Illuminate\Database\Eloquent\Model;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Admin\Resources\UserResource;
use App\Filament\Admin\Resources\ProductResource;

class CreateProduct extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected static bool $canCreateAnother = false;

    public $isForPc = false;

    public ?string $heading = 'Add Product';
    public $pcId = null;

    public function getFormActions(): array
    {
        return [];
    }

    public function mount(): void
    {
        $this->pcId = request()->query('user_id');
        if (!empty($this->pcId)) {
            $this->pcId = getUser($this->pcId);
            $this->isForPc = true;
        }
        $this->authorizeAccess();

        $this->fillForm();

        $this->previousUrl = url()->previous();
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'My Products',
            "#" => 'Add Product',
        ];
    }

    public function create(bool $another = false): void
    {
        $this->navigate($this->form->getState());
    }

    public function navigate($data)
    {
        if ($data['product_id'] == 'create_new') {
            return redirect()->to(ProductResource::getUrl('create-new-product-for-pc', ['create_new' => true, 'user_id' => $this->pcId]));
        } else {
            $exists = \App\Services\ProductRelationCacheService::hasProductRelation($this->data['product_id'], $this->pcId);
            if ($exists) {
                Notification::make()
                    // ->title('Product already exists')
                    ->title('Product already exists')
                    ->danger()
                    ->send();
                return;
            }
            return redirect()->to(ProductResource::getUrl('create-existing', ['record' => $data['product_id'], 'user_id' => $this->pcId]));
        }
    }

    protected function getCreateFormAction(): Action
    {
        return Action::make('create')
            ->label('Add')
            ->submit('create')
            ->keyBindings(['mod+s']);
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('create_new_prodcut')
                ->label('+ Create New Product')
                ->view('custom-view.action-hint-text')
                ->viewData(['hint' => 'If the product is not available in the master list, you can create it manually from scratch'])
                ->url(fn() => $this->getResource()::getUrl('create-new-product-for-pc', ['user_id' => $this->pcId])),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->extraAttributes(['style' => 'margin-bottom: 22px;'])
                ->url(UserResource::getUrl('view', ['record' => $this->pcId])),

        ];
    }
}
