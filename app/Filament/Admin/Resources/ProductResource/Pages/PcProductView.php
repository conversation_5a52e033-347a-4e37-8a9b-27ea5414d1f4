<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use Carbon\Carbon;
use App\Models\User;
use App\Models\Product;
use App\Models\PcDetail;
use App\Models\ProductBatch;
use Filament\Infolists\Infolist;
use Filament\Resources\Pages\Page;
use Illuminate\Support\HtmlString;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Contracts\HasInfolists;
use App\Infolists\Components\DragDropImageEntry;
use App\Filament\Admin\Resources\ProductResource;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Grid as InfoGrid;
use Filament\Infolists\Components\Group as InfoGroup;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Components\Section as InfoSection;
use Illuminate\Database\Eloquent\Model;

class PcProductView extends Page implements HasInfolists
{
    use InteractsWithInfolists;
    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.admin.resources.product-resource.pages.pc-product-view';

    public static $record;

    public static $user;
    public function mount(Product $record, User $user): void
    {
        self::$record = $record;
        self::$user = $user;
    }

    public function getTitle(): string|\Illuminate\Contracts\Support\Htmlable
    {
        return new HtmlString(
            '<div class="flex flex-col items-start justify-between w-full">
            <span class="font-bold">' . self::$record->name . '</span>
            <span class="text-sm text-gray-500">' . self::$user->name . '</span>
        </div>'
        );
    }

    public function getBreadcrumb(): string
    {
        return self::$record->name; // plain text for breadcrumbs
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $userId = self::$user->id;
        $pcCommission = PcDetail::where('user_id', $userId)->first();
        $finalCommissionType = '';
        $finalCommission = 0;
        $finalCommissionType = $pcCommission?->commission_type ?: null;
        $finalCommission = $pcCommission?->commission_percentage ?: 0;
        return $infolist
            ->record(self::$record)
            ->extraAttributes(['x-data' => '{ showDetails: false }'])
            ->schema(function () use ($finalCommission, $finalCommissionType, $userId) {

                return [
                    InfoGrid::make(3)->schema([
                        InfoSection::make('Product Details')
                            ->schema([
                                InfoGrid::make(2)->schema([
                                    InfoGroup::make()->schema([
                                        TextEntry::make('name')->label('Product Name'),
                                        TextEntry::make('category.name')
                                            // ->formatStateUsing(function ($state) {
                                            //     return new HtmlString("<span class='px-2 py-2 font-semibold text-green-600 border-2 border-green-300 rounded-lg shadow-sm text-md bg-green-50'>$state</span>");
                                            // })
                                            ->label('Category'),
                                        TextEntry::make('subcategory.name')
                                            ->label('Sub Category'),
                                        TextEntry::make('productData.created_at')
                                            ->formatStateUsing(function ($state, $record) use ($userId) {

                                                return $record->productDataForPc($userId)->sku ?? '';
                                            })
                                            ->label('Stock Keeping Unit (SKU)'),
                                        TextEntry::make('productData')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->generic?->name ?? '';
                                            })
                                            ->label('Generic Name'),
                                        TextEntry::make('productData.created_at')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->brand?->name ?? '';
                                            })
                                            ->label('Brand'),
                                        TextEntry::make('productData')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->weight ?? '';
                                            })
                                            ->label('Weight'),
                                        TextEntry::make('productData')
                                            ->formatStateUsing(function ($record) {
                                                $distributors = $record->distributors
                                                    ? $record->distributors->pluck('name')->join(' | ')
                                                    : '—';
                                                return $distributors;
                                            })
                                            ->label('Distributors'),
                                        InfoGroup::make()
                                            ->schema([
                                                ViewEntry::make('view_more')
                                                    ->label('')
                                                    ->view('custom-view.show-hide-button'),
                                            ])->columnSpanFull(),
                                    ])->columns(3)->columnSpanFull(),
                                ]),
                            ])->columnSpan(2),
                        InfoSection::make('Product Images')
                            ->extraAttributes(['class' => 'min-h-full'])
                            ->schema([
                                DragDropImageEntry::make('images')
                            ])->columnSpan(1),
                    ])->columnSpanFull(),
                    InfoSection::make()

                        ->heading('Product Description')
                        ->extraAttributes(['x-show' => 'showDetails', 'x-cloak' => true])
                        ->schema([
                            InfoGroup::make()
                                ->label('Product Details')
                                ->schema([
                                    Tabs::make(label: 'Product Details')
                                        ->schema([
                                            Tab::make('Product Description')
                                                ->schema([
                                                    TextEntry::make('product_description')
                                                        ->html()
                                                        ->formatStateUsing(fn($state) => $state)
                                                        ->label(''),
                                                ]),
                                            Tab::make('Key Ingredients')
                                                ->schema([
                                                    TextEntry::make('description_ingredients')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Storage Instructions')
                                                ->schema([
                                                    TextEntry::make('description_storage_instructions')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Usage/Indication')
                                                ->schema([
                                                    TextEntry::make('description_indications')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Contradiction')
                                                ->schema([
                                                    TextEntry::make('description_contradictions')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('How to Use')
                                                ->schema([
                                                    TextEntry::make('description_how_to_use')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Safety Information/Pregnancy')
                                                ->schema([
                                                    TextEntry::make('description_safety_information')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Dosage Information')
                                                ->schema([
                                                    TextEntry::make('description_dosage')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                            Tab::make('Side Effects')
                                                ->schema([
                                                    TextEntry::make('description_side_effects')
                                                        ->html()
                                                        ->label(''),
                                                ]),
                                        ]),
                                ]),
                        ])->columnSpanFull(),

                    InfoSection::make('Stock Details')
                        ->schema([
                            TextEntry::make('productData')
                                ->formatStateUsing(function ($record) use ($userId) {

                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    if ($stockData?->is_batch_wise_stock) {
                                        $currentStock = ProductBatch::where(['product_id' => $record->id, 'user_id' => $userId])->sum('available_stock');
                                        $lowStock = $stockData?->low_stock;
                                        if ($currentStock < $lowStock) {
                                            return new HtmlString('<span class="px-1 py-1 text-white bg-red-500 rounded-md">Low Stock</span>');
                                        }
                                        return new HtmlString('<span class="px-1 py-1 text-white bg-green-500 rounded-md x-1">In Stock</span>');
                                    } else {
                                        $currentStock = $stockData?->total_stock;
                                        $lowStock = $stockData?->low_stock;

                                        // Default to "In Stock"
                                        $badgeClass = 'bg-green-500';
                                        $badgeText = 'In Stock';

                                        // Check if current stock is null or 0
                                        if (is_null($currentStock) || $currentStock == 0) {
                                            $badgeClass = 'bg-red-500';
                                            $badgeText = 'Out of Stock';
                                        } elseif (!is_null($lowStock) && $currentStock < $lowStock) {
                                            // Check if stock is below low stock threshold
                                            $badgeClass = 'bg-red-500'; // Optional: use yellow to differentiate
                                            $badgeText = 'Low Stock';
                                        } else {
                                            $badgeClass = 'bg-green-500';
                                            $badgeText = 'In Stock';
                                        }
                                        return new HtmlString('<span class="px-1 py-1 text-white ' . $badgeClass . ' rounded-md">' . $badgeText . '</span>');
                                    }
                                })
                                ->label('Stock Status'),
                            TextEntry::make('created_at')
                                ->formatStateUsing(function ($record) use ($userId) {

                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    if ($stockData?->is_batch_wise_stock) {
                                        return $record->batches->where('user_id', $userId)->sum('available_stock') ?? '-';
                                    } else {
                                        return \App\Services\ProductRelationCacheService::getProductRelationStock($productRelation?->id)?->stock ?? '-';
                                    }
                                })
                                ->default('-')
                                ->label('Stock'),

                            TextEntry::make('unit.name')
                                ->label('Volume Unit'),
                            TextEntry::make('quantity_per_unit')
                                ->label('Volume'),
                            TextEntry::make('foam.name')->label('Product Form'),
                            TextEntry::make('productData')
                                ->formatStateUsing(function ($record) use ($userId) {
                                    $productRelation = ($record->productDataForPc($userId));
                                    return \App\Services\ProductRelationCacheService::getProductRelationStock($productRelation?->id)?->low_stock ?? '-';
                                })
                                ->label('Low stock trigger value'),
                            TextEntry::make('productData.is_tier_pricing')
                                ->formatStateUsing(function ($record) use ($userId) {

                                    // Get user-specific date format or default
                                    $format = PcDetail::where('user_id', $userId)->value('date_format') ?? 'M d, Y';

                                    $productRelation = ($record->productDataForPc($userId));
                                    $date = \App\Services\ProductRelationCacheService::getProductRelationStock($productRelation?->id)?->expiry_date;
                                    return !empty($date) ? Carbon::parse($date)->format($format) : '-';
                                })
                                ->visible(function ($record) use ($userId) {

                                    $productRelation = self::getPcData($record, $userId);
                                    $stockData = self::getStockData($productRelation?->id);
                                    return !$stockData?->is_batch_wise_stock;
                                })
                                ->default('-')
                                ->label('Expiry Date'),
                            TextEntry::make('container.name')->label('Packaging')
                        ])->columns(4),
                    InfoSection::make()
                        ->heading('By Batch')
                        ->visible(function ($record) use ($userId) {
                            $productRelation = self::getPcData($record, $userId);
                            $stockData = self::getStockData($productRelation?->id);
                            return  $stockData?->is_batch_wise_stock == true;
                        })

                        ->schema(function ($record) {
                            $data = $record;
                            return [
                                ViewEntry::make('batches')
                                    ->view('custom-view.batch-repeatable-entry')
                                    ->viewData(['batches' => self::getBatchesAttribute($record)])

                            ];
                        }),
                    InfoGroup::make()
                        ->columnSpanFull()
                        ->schema([
                            InfoSection::make()
                                ->heading('Fixed Price')
                                ->visible(function ($record) use ($userId) {

                                    return $record->productDataForPc($userId)?->price_type == 'fixed';
                                })
                                ->schema([
                                    InfoGroup::make()
                                        ->columns(4)
                                        ->schema([
                                            TextEntry::make('region')->label('Region'),
                                            TextEntry::make('price')
                                                ->label('Price by'),
                                            TextEntry::make('admin_fees')
                                                ->label('Admin Fees'),
                                            TextEntry::make('net_earnings')->label('Net Earnings'),
                                        ]),
                                    InfoGroup::make()
                                        ->columns(4)
                                        ->schema([
                                            TextEntry::make('east_malasiya')->label('East Malaysia'),
                                            TextEntry::make('productData.admin_approval')
                                                ->formatStateUsing(function ($record) use ($userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    return "RM " . ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->east_zone_price;
                                                })
                                                ->label(''),
                                            TextEntry::make('admin_fees')
                                                ->label(function () use ($finalCommission, $finalCommissionType) {
                                                    if ($finalCommissionType == 'percentage') {
                                                        return $finalCommission . '%';
                                                    } else {
                                                        return 'RM ' . $finalCommission;
                                                    }
                                                }),
                                            TextEntry::make('net_earnings')
                                                ->label(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    $eastZonePrice = ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->east_zone_price;
                                                    if ($finalCommissionType == 'percentage') {
                                                        $earnings = $eastZonePrice - ($eastZonePrice * $finalCommission / 100);
                                                        return "RM " . number_format($earnings, 2);
                                                    } else {
                                                        return 'RM ' . $eastZonePrice - $finalCommission;
                                                    }
                                                }),

                                            TextEntry::make('west_malasiya')->label('West Malaysia'),
                                            TextEntry::make('productData.admin_approval')
                                                ->formatStateUsing(function ($record) use ($userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    return "RM " . ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->west_zone_price;
                                                    // return "RM " . $record->productDataForPc($userId)?->west_zone_price;
                                                })
                                                ->label(''),
                                            TextEntry::make('west_admin_fees')
                                                ->label(function () use ($finalCommission, $finalCommissionType) {
                                                    if ($finalCommissionType == 'percentage') {
                                                        return $finalCommission . '%';
                                                    } else {
                                                        return 'RM ' . $finalCommission;
                                                    }
                                                }),
                                            TextEntry::make('west_net_earnings')
                                                ->label(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                    $productRelation = ($record->productDataForPc($userId));
                                                    $westZonePrice = ProductRelationPrice::where(['product_relation_id' => $productRelation?->id])->first()?->west_zone_price;
                                                    if ($finalCommissionType == 'percentage') {
                                                        $earnings = $westZonePrice - ($westZonePrice * $finalCommission / 100);
                                                        return "RM " . number_format($earnings, 2);
                                                    } else {
                                                        return 'RM ' . $westZonePrice - $finalCommission;
                                                    }
                                                }),
                                        ]),
                                ]),
                        ]),
                    InfoSection::make()
                        ->heading('Bonus Pricing')
                        ->visible(function ($record) use ($userId) {
                            return $record->productDataForPc($userId)?->price_type == 'bonus';
                        })
                        ->schema([
                            InfoGrid::make(2) // Change from 3 to 2 to ensure two sections side by side
                                ->extraAttributes(['class' => 'm-0'])

                                ->schema([
                                    InfoSection::make()
                                        ->heading('East Malaysia')
                                        ->schema([
                                            InfoGroup::make()->schema([
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($userId) {
                                                        $productRelation = self::getPcData($record, $userId);
                                                        $eastZonePrice = self::getPriceData($productRelation?->id)?->east_zone_price;
                                                        return "RM " . number_format($eastZonePrice, 2);
                                                    })
                                                    ->label('Base Price'),
                                                TextEntry::make('productData')
                                                    ->label('Admin Fees')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return  $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('Net Earnings')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                        $productRelation = ($record->productDataForPc($userId));
                                                        $eastZonePrice = self::getPriceData($productRelation?->id)?->east_zone_price;
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $eastZonePrice - ($eastZonePrice * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $eastZonePrice - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                            ])->columns(3),
                                            InfoGroup::make()->schema([
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'bg-gray-200 p-3 pt-4 rounded-md'])
                                                    ->schema([
                                                        TextEntry::make('quantity')
                                                            ->label('Quantity'),
                                                        TextEntry::make('bonus_quantity')
                                                            ->label('Bonus Quantity'),
                                                    ])->columns(2),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'ml-4'])
                                                    ->schema([
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_1_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_1_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_2_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_2_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_3_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = ($record->productDataForPc($userId));
                                                                return self::getPriceData($productRelation?->id)?->east_bonus_3_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                    ])->columns(2)
                                            ])->columnSpanFull(3),
                                        ])->columnSpan(1),

                                    InfoSection::make()
                                        ->heading('West Malaysia')
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            return [
                                                InfoGroup::make()->schema([
                                                    TextEntry::make('productData')
                                                        ->formatStateUsing(function ($record) use ($userId) {
                                                            $productRelation = self::getPcData($record, $userId);
                                                            $westZonePrice = self::getPriceData($productRelation?->id)?->west_zone_price;
                                                            return 'RM ' . number_format($westZonePrice, 2);
                                                        })
                                                        ->label('Base Price'),
                                                    TextEntry::make('productData')
                                                        ->label('Admin Fees')
                                                        ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                            return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                        }),
                                                    TextEntry::make('productData')
                                                        ->label('Net Earnings')
                                                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                                            $productRelation = self::getPcData($record, $userId);
                                                            $westZonePrice = self::getPriceData($productRelation?->id)?->west_zone_price;
                                                            if ($finalCommissionType == 'percentage') {
                                                                $earnings = $westZonePrice - ($westZonePrice * $finalCommission / 100);
                                                            } else {
                                                                $earnings = $westZonePrice - $finalCommission;
                                                            }
                                                            return "RM " . number_format($earnings, 2);
                                                        }),
                                                ])->columns(3),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'bg-gray-200 p-3 pt-4 rounded-md'])
                                                    ->schema([
                                                        TextEntry::make('quantity')
                                                            ->label('Quantity'),
                                                        TextEntry::make('bonus_quantity')
                                                            ->label('Bonus Quantity'),
                                                    ])->columns(2),
                                                InfoGroup::make()
                                                    ->extraAttributes(['class' => 'ml-4'])
                                                    ->schema([
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_1_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_1_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_2_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_2_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_3_quantity;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                        TextEntry::make('productData')
                                                            ->formatStateUsing(function ($record) use ($userId) {
                                                                $productRelation = self::getPcData($record, $userId);
                                                                return self::getPriceData($productRelation?->id)?->west_bonus_3_quantity_value;
                                                            })
                                                            ->weight('bold')
                                                            ->label(""),
                                                    ])->columns(2)
                                            ];
                                        })->columnSpan(1),
                                ])->columnSpanFull(),
                        ]),
                    InfoSection::make()
                        ->heading('Tier Pricing')
                        ->visible(function ($record) use ($userId) {
                            return $record->productDataForPc($userId)?->price_type == 'tier';
                        })
                        ->schema([
                            InfoSection::make()
                                ->heading(function ($record) use ($userId) {
                                    return new HtmlString("
                                    <div class='flex flex-col justify-center h-full'>
                                        <div class='flex flex-row items-center justify-between w-full'>
                                            <span class='font-bold'>East Malaysia</span>
                                            <span class='font-normal text-right text-gray-400'>{$record?->quantity_per_unit} {$record?->foam?->name} in {$record?->unit?->name}</span>
                                        </div>
                                    </div>
                                ");
                                })
                                ->schema([
                                    InfoGroup::make([
                                        TextEntry::make('type')->label('Type'),
                                        TextEntry::make('quantity')->label('Quantity'),
                                        TextEntry::make('price')->label('Price'),
                                        TextEntry::make('admin_fees')->label('Admin Fees'),
                                        TextEntry::make('net_earnings')->label('Net Earnings'),
                                    ])->columns(5),
                                    InfoGroup::make()
                                        ->columns(5)
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            $productRelation = self::getPcData($record, $userId);
                                            $tierData = self::getPriceData($productRelation?->id);
                                            return [
                                                TextEntry::make('tier_1')->label('Tier 1'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->east_tier_1_max_quantity) || $tierData?->east_tier_1_max_quantity !== 0 ? $tierData->east_tier_1_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData?->east_tier_1_min_quantity . ' - ' . $tierData?->east_tier_1_max_quantity
                                                            : $tierData?->east_tier_1_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_1_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_1_base_price - ($tierData?->east_tier_1_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_1_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_2')->label('Tier 2')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->east_tier_2_max_quantity) || $tierData?->east_tier_2_max_quantity !== 0 ? $tierData?->east_tier_2_max_quantity : null;

                                                        return $tierData && $maxQty !== null
                                                            ? $tierData?->east_tier_2_min_quantity . ' - ' . $tierData?->east_tier_2_max_quantity
                                                            : $tierData?->east_tier_2_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_2_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_2_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_2_base_price - ($tierData?->east_tier_2_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_2_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_3')
                                                    ->visible(function () use ($tierData) {

                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->label('Tier 3'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        // dd($tierData?->east_tier_3_min_quantity);
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity .  " and above";
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->east_tier_3_base_price;
                                                    })
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType, $tierData) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->east_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->east_tier_3_base_price - ($tierData?->east_tier_3_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->east_tier_3_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    })
                                            ];
                                        })
                                ]),
                            InfoSection::make()
                                ->heading(function ($record) {
                                    return new HtmlString("
                                    <div class='flex flex-col justify-center h-full'>
                                        <div class='flex flex-row items-center justify-between w-full'>
                                            <span class='font-bold'>West Malaysia</span>
                                            <span class='font-normal text-right text-gray-400'>{$record->quantity_per_unit} {$record->foam?->name} in {$record->unit?->name}</span>
                                        </div>
                                    </div>
                                ");
                                })
                                ->schema([
                                    InfoGroup::make([
                                        TextEntry::make('type')->label('Type'),
                                        TextEntry::make('quantity')->label('Quantity'),
                                        TextEntry::make('price')->label('Price'),
                                        TextEntry::make('admin_fees')->label('Admin Fees'),
                                        TextEntry::make('net_earnings')->label('Net Earnings'),
                                    ])->columns(5),
                                    InfoGroup::make()
                                        ->columns(5)
                                        ->schema(function ($record) use ($finalCommission, $finalCommissionType, $userId) {
                                            $productRelation = self::getPcData($record, $userId);
                                            $tierData = self::getPriceData($productRelation->id);
                                            return [
                                                TextEntry::make('tier_1')->label('Tier 1'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->west_tier_1_max_quantity) || $tierData?->west_tier_1_max_quantity !== 0 ? $tierData->west_tier_1_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData->west_tier_1_min_quantity . ' - ' . $tierData->west_tier_1_max_quantity
                                                            : $tierData->west_tier_1_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_1_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_1_base_price - ($tierData?->east_tier_1_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_1_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_2')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->label('Tier 2'),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        $maxQty = !empty($tierData?->west_tier_2_max_quantity) || $tierData?->west_tier_2_max_quantity !== 0 ? $tierData->west_tier_2_max_quantity : null;
                                                        return $tierData && $maxQty !== null
                                                            ? $tierData->west_tier_2_min_quantity . ' - ' . $tierData->west_tier_2_max_quantity
                                                            : $tierData->west_tier_2_min_quantity . ' and above';
                                                    }),
                                                TextEntry::make('productData')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_2_base_price;
                                                    })
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_2_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_2_base_price - ($tierData?->west_tier_2_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_2_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    }),
                                                TextEntry::make('tier_3')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('Tier 3'),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity . " and above";
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function ($record) use ($tierData) {
                                                        return "RM " . $tierData?->west_tier_3_base_price;
                                                    })
                                                    ->label(''),
                                                TextEntry::make('productData')
                                                    ->label('')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->formatStateUsing(function () use ($finalCommission, $finalCommissionType) {
                                                        return $finalCommissionType == 'percentage' ? $finalCommission . "%" : "RM " . $finalCommission;
                                                    }),
                                                TextEntry::make('productData')
                                                    ->visible(function () use ($tierData) {
                                                        return $tierData?->west_tier_3_min_quantity != null;
                                                    })
                                                    ->label('')
                                                    ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType, $tierData) {
                                                        if ($finalCommissionType == 'percentage') {
                                                            $earnings = $tierData?->west_tier_3_base_price - ($tierData?->west_tier_3_base_price * $finalCommission / 100);
                                                        } else {
                                                            $earnings = $tierData?->west_tier_3_base_price - $finalCommission;
                                                        }
                                                        return "RM " . number_format($earnings, 2);
                                                    })
                                            ];
                                        })
                                ])
                        ])
                ];
            });
    }

    public static function getPcData($record, $userId)
    {
        return $record->productDataForPc($userId);
    }

    public static function getStockData($key)
    {
        return \App\Services\ProductRelationCacheService::getProductRelationStock($key);
    }

    public function getImages()
    {
        $images = self::$record->load('media')->media;
        // dd($images);
        return $images;
    }

    public static function getPriceData($key)
    {
        return ProductRelationPrice::where('product_relation_id', $key)->first();
    }

    public static function getBatchesAttribute($record)
    {
        $userId = self::$user->id;
        return $record->batches->where('user_id', $userId)->map(function ($batch) {
            return [
                'batch_name' => $batch->batch_name,
                'available_stock' => $batch->available_stock,
                'expiry_date' => $batch->expiry_date,
            ];
        })->toArray();
    }
}
