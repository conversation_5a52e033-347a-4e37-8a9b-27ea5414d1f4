<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use App\Models\User;
use App\Models\Product;
use Filament\Forms\Get;
use App\Enums\LabelEnum;
use App\Models\PcDetail;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use App\Models\ProductRelation;
use Filament\Infolists\Infolist;
use App\Mail\ProductApprovedMail;
use App\Mail\ProductRejectedMail;
use Awcodes\TableRepeater\Header;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Contracts\View\View;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Illuminate\Contracts\Support\Htmlable;
use App\Filament\Admin\Resources\ProductResource;
use Awcodes\TableRepeater\Components\TableRepeater;
use Filament\Infolists\Concerns\InteractsWithInfolists;

class ViewProduct extends ViewRecord
{
    use HasBackButton;
    public $showModal = false;

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Product Catalog',
            "#" => 'Product Details',
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return $this->getRecord()->name;
    }

    public function getImages()
    {
        $images = $this->record->getMedia('product-images');
        // dd($images);
        return $images;
    }

    public function showAllImages()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
    }
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('edit_product')
                ->label('Edit')
                ->url(fn() => $this->getResource()::getUrl('edit', ['record' => $this->record->getKey()]))
                ->visible(fn($record) => $record->status == 'rejected')
                ->button()
                ->color('primary'),

            Action::make('approve')
                ->outlined()
                ->label('Approve')
                ->visible(function ($record) {
                    return $record->status == 'pending';
                })
                ->icon('heroicon-o-check')
                ->action(function (array $data, $record) {
                    $originalAttributes = $record->toArray();
                    $originalProductData = $record->productDataForPc($record->owner_id)?->toArray();
                    $productData = $record->productDataForPc($record->owner_id);
                    $createdBy = $productData?->userDetails;
                    // Update product data for PC
                    $productData?->update([
                        'admin_approval' => true, 
                        'is_rejected' => false
                    ]);
                    
                    // Update main product record
                    $record->update([
                        'approved_by' => auth()->user()->id,
                        'approved_on' => now(),
                        'admin_verified_on' => now(),
                        'status' => 'approved',
                    ]);
                    
                    // Prepare comprehensive activity data
                    $activityData = [
                        'product_id' => $record->id,
                        'name' => $record->name,
                        'category_id' => $record->category_id,
                        'sub_category_id' => $record->sub_category_id,
                        'brand_id' => $record->brand_id,
                        'unit_id' => $record->unit_id,
                        'dosage_foams_id' => $record->dosage_foams_id,
                        'quantity_per_unit' => $record->quantity_per_unit,
                        'approved_by' => Auth::user()->id,
                        'approved_by_name' => Auth::user()->name,
                        'approved_on' => now()->toDateTimeString(),
                        'admin_verified_on' => now()->toDateTimeString(),
                        'status' => 'approved',
                        'previous_status' => $originalAttributes['status'] ?? 'pending',
                        'admin_approval' => true,
                        'is_rejected' => false,
                        'owner_id' => $record->owner_id,
                        'created_by' => $createdBy?->name ?? 'Unknown',
                    ];
                    
                    // Log the activity with before/after state
                    activity()
                        ->performedOn($record)
                        ->causedBy(Auth::user())
                        ->withProperties([
                            'old' => [
                                'product' => $originalAttributes,
                                'product_data' => $originalProductData
                            ],
                            'attributes' => $activityData
                        ])
                        ->log("Product '{$record->name}' approved by Admin " . Auth::user()->name . " (ID: " . Auth::user()->id . ")");
                    
                    // Send notification to product creator
                    if ($createdBy) {
                        Notification::make()
                            ->sendToDatabase($createdBy)
                            ->title('Your product has been approved by admin.')
                            ->body("Product '{$record->name}' has been approved and is now available.");
                    }
                    Mail::to($createdBy->email)->send(new ProductApprovedMail($record, $createdBy));
                    
                    // Send success notification to admin
                    Notification::make()
                        ->title('Product has been approved successfully')
                        ->body("Product '{$record->name}' has been approved.")
                        ->success()
                        ->send();
                }),
                Action::make('reject')
                ->outlined()
                ->label('Reject')
                ->visible(function ($record) {
                    // dd($record->status);
                    return $record->status == 'pending';
                })->requiresConfirmation()
                ->modalHeading('Reject Product?')
                ->modalDescription('Are you sure you want to reject this product? This action cannot be undone.')
                ->modalSubmitActionLabel('Yes, Reject')
                ->modalIcon('heroicon-o-x-circle') // Optional icon
                ->color('danger') // Makes button red like delete modal
                ->form([
                    Textarea::make('reason')
                        ->label(new HtmlString(LabelEnum::REASON->value . " <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->validationAttribute('reason')
                        ->rules(['required', 'string'])
                        ->maxLength(255)
                ])
                ->action(function ($record, $data)  {
                    
                    $userId = $record->owner_id;
                    // Cache productData to avoid duplicate queries
                    $productData = $record->productDataForPC($userId);
                    $createdBy = User::find($productData?->user_id);
                    $productData?->update([
                        'admin_approval' => false,
                        'rejected_reason' => $data['reason'],
                        'is_rejected' => true,
                        'rejected_on' => now(),

                    ]);
                    $res = $record->update([
                        'status' => 'rejected',
                    ]);
                    $activityData = [
                        'name' => $record->name,
                        'reason' => $data['reason'],
                        'status' => 'rejected',
                        'rejected_on' => now(),
                        'admin_approval' => false,
                        'is_rejected' => true,
                        'category_id' => $record->category_id,
                        'sub_category_id' => $record->sub_category_id,
                        'brand_id' => $record->brand_id,
                        'unit_id' => $record->unit_id,
                        'dosage_foams_id' => $record->dosage_foams_id,
                        'quantity_per_unit' => $record->quantity_per_unit,
                    ];
                    activity()
                        ->performedOn($record)
                        ->causedBy(Auth::user())
                        ->withProperties(['attributes' => $activityData])
                        ->log("Product rejected by Admin ($record->name)");
                    Mail::to($createdBy->email)->send(new ProductRejectedMail($data['reason'], $record, $createdBy));

                    Notification::make()
                        ->body("Rejected")
                        ->info()
                        ->title("Your product ($record->name) has been rejected by admin.")
                        ->actions([
                            \Filament\Notifications\Actions\Action::make('view')
                                ->label('View Product')
                                ->url(\App\Filament\Pc\Resources\ProductResource::getUrl('view', ['record' => $record->id], panel: 'pc')),

                        ])
                        ->sendToDatabase($createdBy);

                    Notification::make()
                        ->title('Product rejected successfully.')
                        ->send();
                }),
            Action::make('back')
                ->color('gray')
                ->label('Back')
                ->url(ProductResource::getUrl('index'))

        ];
    }
}
