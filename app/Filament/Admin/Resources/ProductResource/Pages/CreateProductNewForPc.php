<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use Closure;
use Exception;
use Carbon\Carbon;
use App\Models\Unit;
use App\Models\User;
use App\Models\Brand;
use Filament\Actions;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use App\Models\Distributor;
use App\Models\GenericName;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use App\Forms\Components\DragDrop;
use App\Rules\CaseSensitiveUnique;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Tabs;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use App\Service\TierValidationService;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Placeholder;
use Filament\Resources\Pages\CreateRecord;
use App\Mail\AdminCreatedNewProductForPCMail;
use Illuminate\Validation\ValidationException;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Filament\Admin\Resources\ProductResource;
use Awcodes\TableRepeater\Components\TableRepeater;
use App\Component\Products\ProductDescriptionSection;
use App\Component\Products\ProductStockDetailsSection;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Component\Products\ProductMultipleImageSection;
use App\Component\Products\ProductGeneralDetailsSection;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class CreateProductNewForPc extends CreateRecord
{
    protected static string $resource = ProductResource::class;

    protected static bool $canCreateAnother = false;

    public ?array $data = [];

    public  $commission;

    public $pcCommission;

    public $globalCommission;

    public $finalCommissionType;

    public $finalCommission;

    public $productData = null;

    public $batchData = null;

    public $uploadedImages = null;

    public ?array $images = [];

    public ?string $defaultFileName = "";
    public $defaultIndex = 0;
    public $defaultImage = "";

    public $pcId = null;

    public $params = [];

    public $productPriceData = [];

    public $inStock = null;

    public $qty = 0;

    public $wholeSalePackSize = null;

    public $stockType = null;

    public $preservedStockData = [];
    
    public $preservedTierData = [];

    protected $listeners = ['updateDefaultImage', 'handleChangeDefaultImage'];

    protected function getCreatedNotification(): ?Notification
    {
        $title = $this->getCreatedNotificationTitle();

        if (blank($title)) {
            return null;
        }

        return Notification::make()
            ->success()
            ->body('Product Created Successfully')
            ->title($title);
    }

    protected function getCreateFormAction(): Action
    {
        return Action::make('create')
            ->label('Add')
            ->submit('create')
            ->keyBindings(['mod+s']);
    }

    // public function clearLivewireTmpFiles()
    // {
    //     $tmpPath = storage_path('app/livewire-tmp');
    //     if (File::exists($tmpPath)) {
    //         File::cleanDirectory($tmpPath);
    //     }
    // }


    protected function getRedirectUrl(): string
    {
        return ProductResource::getUrl('index');
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Products',
            "#" => 'Add Product',
        ];
    }


    public function refresh($uuid): void
    {
        $this->form->fill(["pcInfo_east.{$uuid}.min_quantity"]);

        // $this->fillForm();
    }

    public function handleChangeDefaultImage($fileName)
    {
        $this->defaultFileName = $fileName;
    }

    public function setDefaultImage($index)
    {
        $this->defaultIndex = $index;
    }

    public function removeMedia($mediaId)
    {
        if (!$this->record) {
            return false;
        }

        $media = Media::find($mediaId);
        if ($media && $media->model_id == $this->record->id) {
            $media->delete();

            // Update the form state
            $currentState = $this->form->getState();
            if (isset($currentState['images'])) {
                $currentState['images'] = array_filter($currentState['images'], function ($id) use ($mediaId) {
                    return $id !== $mediaId;
                });
                $this->form->fill($currentState);
            }

            return true;
        }
        return false;
    }

    public function clearLivewireTmpFiles()
    {
        $tmpPath = storage_path('app/livewire-tmp');
        if (File::exists($tmpPath)) {
            File::cleanDirectory($tmpPath);
        }
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('create', ['user_id' => $this->pcId])),
        ];
    }

    public function mount(): void
    {
        $this->pcId = request()->route('user_id');
        parent::mount();
        $data = [
            'productData' => [
                'price_type' => 'fixed',
                'stock_type' => 'normal',
            ],
        ];
        $pcCommission = PcDetail::where('user_id', $this->pcId)->first();
        $this->finalCommissionType = $pcCommission?->commission_type ?? 'percentage';
        $this->finalCommission = $pcCommission?->commission_percentage ?? 0;

        $searchTerm = session()->get('search_term_' . auth()->id()) ?? null;
        $tempSku = !empty($searchTerm) ? mb_substr($searchTerm, 0, 4) . "_"  . rand(100000, 999999) : null;
        $data['sku'] = $tempSku;
        $data['admin_fees'] = $this->finalCommission;
        $data['commission_type'] = $this->finalCommissionType;
        $data['name'] = $searchTerm;
        $data['pcInfo_east'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);

        $data['pcInfo_west'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);
        $this->form->fill($data);
        session()->forget('product_search_term');
    }

    public function form(Form $form): Form
    {
        return  $form->schema([
            TextInput::make('admin_fees')->hidden()->live(),
            TextInput::make('commission_type')->hidden(),
            ProductGeneralDetailsSection::make(),
            ProductMultipleImageSection::make(),
            ProductDescriptionSection::make(),

            Group::make()
                ->visible(function () {
                    return \Auth::user()->hasRole('Super Admin') || \Auth::user()->can('pharmaceutical-suppliers_pricing');
                })
                ->columnSpanFull()
                ->schema([
                    ProductStockDetailsSection::make(),
                    Section::make('Price')
                        ->heading(function (Get $get) {

                            $text = "Pricing ";

                            $rawQty = $get('quantity_per_unit');
                            $wholeSalePackSize = $get('whole_sale_pack_size');
                            $containerId = $get('container_id');
                            $dosageFoamId = $get('dosage_foams_id');

                            $qty = $rawQty ?? 0;

                           $stockType = $get('stock_type');
                            $wholeSalePackSize = $stockType == 'wps' ? $get('whole_sale_pack_size') : 1;
                            if (!empty($rawQty)) {
                                $text .= "{$wholeSalePackSize} ";
                            }
                            // $text .= "{$wholeSalePackSize} ";
                            if (!empty($rawQty) && !empty($wholeSalePackSize) && $wholeSalePackSize > 0) {
                                $qty = $rawQty * (int) $wholeSalePackSize;
                            }

                            $containerName = !empty($containerId)
                                ? Container::find($containerId)?->name
                                : null;

                            if (!empty($containerName)) {
                                $text .= Str::plural($containerName) . ' ';
                            }

                            $foamName = !empty($dosageFoamId)
                                ? DosageForm::find($dosageFoamId)?->name
                                : null;

                            $foam = !empty($foamName) ? Str::plural($foamName) : null;

                            if (!empty($qty) && !empty($foam)) {
                                $text .= "( {$qty} {$foam} )";
                            }
                            // dd($text);

                            return new HtmlString("<div class='font-bold text-gray-600'>{$text}</div>");
                        })
                        ->schema([
                            Radio::make('productData.price_type')
                                ->rules(['required'])
                                ->label('Select Pricing Structure')
                                ->extraAttributes([
                                    'class' => 'gap-1',
                                    'style' => 'margin:0px !important;'
                                ])
                                ->options([
                                    'fixed' => new HtmlString('Fixed &nbsp;
                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sets a fixed price that does not vary with quantity or duration.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>
                                    '),
                        
                                        'bonus' => new HtmlString('Bonus &nbsp;
                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>
                                    
                                    
                                    '),
                        
                                        'tier' => new HtmlString('Tier &nbsp;
                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Applies different prices based on quantity purchased — more units may cost less per unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>
                                    '),
                                ])
                                ->live()
                                ->inline(),
                            Section::make('Fixed')
                                ->heading('Fixed')
                                ->extraAttributes(['style' => 'padding:0px !important;'])
                                ->visible(function (Get $get) {
                                    if ($get('productData.price_type') == 'fixed') {
                                        return true;
                                    }
                                    return false;
                                })
                                ->schema([
                                    Group::make()
                                        // ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                                        ->schema([
                                            PlaceHolder::make('Region'),
                                            PlaceHolder::make('price')->label("Price by"),
                                            PlaceHolder::make('admin_fess')->label('Admin Fees'),
                                            PlaceHolder::make('net_earnings')->label('New Earnings')
                                        ])->columns(4),
                                    Group::make()
                                        ->reactive()
                                        ->schema([
                                            PlaceHolder::make('west_region')
                                                ->content('West Malaysia')
                                                ->label("")
                                                ->extraAttributes(['class' => 'mt-3']),
                                            TextInput::make('productData.west_zone_price')
                                                ->placeholder('West zone price')
                                                ->validationAttribute('west zone price')
                                                ->validationMessages([
                                                    'required' => 'The west zone price is required',
                                                    'gt' => 'The west zone price must be greater than 0.',
                                                    'numeric' => 'The west zone price must be a number.',
                                                ])
                                                ->live()
                                                ->label("test")
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->rules(function (Get $get) {
                                                    if ($get('productData.price_type') == 'fixed') {
                                                        return ['required', 'numeric', 'gt:0'];
                                                    }
                                                })
                                                ->label(""),
                                            PlaceHolder::make('10')
                                                ->label("")
                                                ->content(function () {
                                                    if ($this->finalCommissionType == 'percentage') {
                                                        return $this->finalCommission . "%";
                                                    }
                                                    return 'RM ' . $this->finalCommission;
                                                })
                                                ->extraAttributes(['class' => 'mt-3']),
                                            PlaceHolder::make('25')
                                                ->label(function (Get $get) {
                                                    if ($this->finalCommissionType == 'percentage') {
                                                        $commission = $this->finalCommission * (float)$get('productData.west_zone_price') / 100;
                                                        $earning = (float)$get('productData.west_zone_price') - $commission;
                                                    } else {
                                                        $earning = (float)$get('productData.west_zone_price') - $this->finalCommission;
                                                    }
                                                    $earning = $earning < 0 ? 0 : $earning;
                                                    $earning = 'RM ' . number_format((float)$earning, 2);
                                                    return new HtmlString("<div class='text-sm font-normal'>$earning</div>");
                                                })
                                                ->extraAttributes(['class' => 'mt-3']),
                                            PlaceHolder::make('east_region')
                                                ->label("")
                                                ->content('East Malaysia')
                                                ->extraAttributes(['class' => 'mt-3']),
                                            TextInput::make('productData.east_zone_price')
                                                ->placeholder('East zone price')
                                                ->live()
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                // ->validationAttribute('east zone price')
                                                ->validationMessages([
                                                    'required' => 'The east zone price is required',
                                                    'gt' => 'The east zone price must be greater than 0.',
                                                    'numeric' => 'The east zone price must be a number.',
                                                ])
                                                ->rules(function (Get $get) {
                                                    if ($get('productData.price_type') == 'fixed') {
                                                        return ['required', 'numeric', 'gt:0'];
                                                    }
                                                })
                                                ->label(""),
                                            PlaceHolder::make('10')
                                                ->label("")
                                                ->content(function () {
                                                    if ($this->finalCommissionType == 'percentage') {
                                                        return $this->finalCommission . "%";
                                                    }
                                                    return 'RM' . $this->finalCommission;
                                                })
                                                ->extraAttributes(['class' => 'mt-3']),
                                            PlaceHolder::make('25')
                                                ->label("")
                                                ->content(function (Get $get) {
                                                    if ($this->finalCommissionType == 'percentage') {
                                                        $commission = $this->finalCommission * (float)$get('productData.east_zone_price') / 100;
                                                        $earning = (float)$get('productData.east_zone_price') - $commission;
                                                    } else {
                                                        $earning = (float)$get('productData.east_zone_price') - $this->finalCommission;
                                                    }
                                                    $earning = $earning < 0 ? 0 : $earning;
                                                    return 'RM ' . number_format((float)$earning, 2);
                                                })
                                                ->extraAttributes(['class' => 'mt-3']),
                                        ])->columns(4),
                                ]),
                            Group::make()
                                ->columnSpanFull()
                                ->visible(fn(Get $get) => $get('productData.price_type') === 'bonus')
                                ->schema([
                                    Section::make('east_malasiya')
                                        ->heading('East Malaysia')
                                        ->schema([
                                            Group::make()->schema(function ($livewire) {
                                                return [
                                                    TextInput::make('productData.east_zone_price')

                                                        ->placeholder('250.00')
                                                        ->validationAttribute('east bonus base price')
                                                        ->extraAttributes(fn() => self::numericValueValidationRule())
                                                        ->rules(function (Get $get) {
                                                            if ($get('productData.price_type') == 'bonus') {
                                                                return ['required', 'numeric', 'gt:0'];
                                                            }
                                                        })
                                                        ->calculateNetEarnings(
                                                            commission: $this->finalCommission,
                                                            commissionType: $this->finalCommissionType,
                                                            fieldId: 'data.bonus_1_net_earnings',
                                                            currentField: 'data.productData.east_zone_price',
                                                        )
                                                        ->prefix('RM')
                                                        ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                                                    TextInput::make('admin_fees_1')
                                                        ->disabled()
                                                        ->formatStateUsing(function (Get $get) {
                                                            return $get('admin_fees');
                                                        })
                                                        ->prefix(function (Get $get) {
                                                            if ($get('commission_type') == 'percentage') {
                                                                return '%';
                                                            }
                                                            return 'RM';
                                                        })
                                                        ->label('Admin Fees'),
                                                    TextInput::make('bonus_1_net_earnings')
                                                        ->reactive()
                                                        ->formatStateUsing(function (Get $get) {
                                                            if ($get('commission_type') == 'percentage') {
                                                                $commission = (float)$get('productData.east_zone_price') * $get('admin_fees') / 100;
                                                                $earnings = (float)$get('productData.east_zone_price') - $commission;
                                                            } else {
                                                                $commission = (float)$get('productData.east_zone_price') * $get('admin_fees');
                                                                $earnings = (float)$get('productData.east_zone_price') - $commission;
                                                            }
                                                            $earnings = $earnings < 0 ? 0 : $earnings;
                                                            return number_format((float)$earnings, 2, '.', '');
                                                        })
                                                        ->disabled()
                                                        ->prefix('RM')
                                                        ->label('Net Earnings'),
                                                ];
                                            })->columns(3),
                                            Group::make()->schema([
                                                TextInput::make('productData.east_bonus_1_quantity')

                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('east bonus quantity')
                                                    ->rules(function (Get $get) {
                                                        if ($get('productData.price_type') == 'bonus') {
                                                            return ['required', 'integer', 'regex:/^\d+$/'];
                                                        }
                                                        return ['integer', 'regex:/^\d+$/'];
                                                    })
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Quantity'),
                                                TextInput::make('productData.east_bonus_1_quantity_value')
                                                    ->placeholder('Enter free quantity')
                                                    ->validationAttribute('east bonus quantity value')
                                                    ->rules(function (Get $get) {
                                                        if ($get('productData.price_type') == 'bonus') {
                                                            return ['required', 'integer', 'regex:/^\d+$/'];
                                                        }
                                                        return ['lt:$maxQty', 'integer', 'regex:/^\d+$/'];
                                                    })
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Bonus Quantity'),


                                                TextInput::make('productData.east_bonus_2_quantity')
                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('east bonus quantity')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                    ->label('Quantity'),
                                                TextInput::make('productData.east_bonus_2_quantity_value')
                                                    ->placeholder('Enter free quantity')
                                                    ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                    ->validationAttribute('east bonus quantity value')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Bonus Quantity'),
                                                TextInput::make('productData.east_bonus_3_quantity')
                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('east bonus quantity')
                                                    ->rules(['integer', 'regex:/^\d+$/'])
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Quantity'),
                                                TextInput::make('productData.east_bonus_3_quantity_value')
                                                    ->placeholder('Enter free quantity')
                                                    ->rules(function (Get $get) {
                                                        if (!empty($get('productData.east_bonus_3_quantity'))) {
                                                            return ['required', 'numeric', 'gt:0', 'integer', 'regex:/^\d+$/'];
                                                        }
                                                        return ['integer', 'regex:/^\d+$/'];
                                                    })
                                                    ->validationAttribute('east bonus quantity value')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Bonus Quantity'),
                                            ])->columns(2),
                                        ]),
                                    Section::make('west_malasiya')
                                        ->heading('West Malaysia')
                                        ->schema([
                                            Group::make()->schema([
                                                TextInput::make('productData.west_zone_price')
                                                    ->placeholder('250.00')
                                                    ->extraAlpineAttributes(fn() => self::numericValueValidationRule())
                                                    ->rules(function (Get $get) {
                                                        if ($get('productData.price_type') == 'bonus') {
                                                            return ['required', 'numeric', 'gt:0'];
                                                        }
                                                    })
                                                    ->calculateNetEarnings(
                                                        commission: $this->finalCommission,
                                                        commissionType: $this->finalCommissionType,
                                                        fieldId: 'data.bonus_2_net_earnings',
                                                        currentField: 'data.productData.west_zone_price',
                                                    )

                                                    ->prefix('RM')
                                                    ->validationAttribute('west bonus base price')
                                                    ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                                                TextInput::make('west_admin_fees')
                                                    ->disabled()
                                                    ->formatStateUsing(function (Get $get) {
                                                        return $get('admin_fees');
                                                    })
                                                    ->prefix(function (Get $get) {
                                                        if ($get('commission_type') == 'percentage') {
                                                            return '%';
                                                        }
                                                        return 'RM';
                                                    })
                                                    ->label('Admin Fees'),
                                                TextInput::make('bonus_2_net_earnings')
                                                    ->disabled()
                                                    ->reactive()
                                                    ->prefix('RM')
                                                    ->label('Net Earnings'),
                                            ])->columns(3),
                                            Group::make()->schema([
                                                TextInput::make('productData.west_bonus_1_quantity')
                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('west bonus quantity')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->rules(function (Get $get) {
                                                        if ($get('productData.price_type') == 'bonus') {
                                                            return ['required', 'numeric', 'max:999999', "gt:0", 'integer', 'regex:/^\d+$/'];
                                                        }
                                                        return ['integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
                                                    })
                                                    ->label('Quantity')
                                                    ->requiredWith('productData.west_base_price'),
                                                TextInput::make('productData.west_bonus_1_quantity_value')
                                                    ->placeholder('Enter bonus quantity')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->rules(function (Get $get) {
                                                        if ($get('productData.price_type') == 'bonus') {
                                                            return ['required',  'integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
                                                        }
                                                        return ['integer', 'max:999999', "gt:0", 'regex:/^\d+$/'];
                                                    })
                                                    ->requiredWith('west_base_price')
                                                    ->label('Bonus Quantity'),

                                                TextInput::make('productData.west_bonus_2_quantity')
                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('west free quantity')
                                                    ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Quantity'),
                                                TextInput::make('productData.west_bonus_2_quantity_value')
                                                    ->placeholder('Enter free quantity')
                                                    ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Bonus Quantity'),

                                                TextInput::make('productData.west_bonus_3_quantity')
                                                    ->placeholder('Enter quantity')
                                                    ->validationAttribute('west bonus quantity')
                                                    ->rules(['integer', 'regex:/^\d+$/'])
                                                    ->live(onBlur: true)
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->label('Quantity'),
                                                TextInput::make('productData.west_bonus_3_quantity_value')
                                                    ->placeholder('Enter free quantity')
                                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                                    ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                    ->label('Bonus Quantity'),
                                            ])->columns(2),
                                        ])
                                ]),

                            //tier pricing
                            Group::make()
                                ->visible(fn(Get $get) => $get('productData.price_type') === 'tier')
                                ->schema(function ($get) {
                                    return [
                                        Section::make('East Malaysia')->schema([
                                            TableRepeater::make('pcInfo_east')
                                                ->maxItems(3)
                                                ->minItems(function (Get $get) {
                                                    return $get('price_type') === 'tier' ? 1 : 0;
                                                })
                                                ->required(function (Get $get) {
                                                    return $get('price_type') === 'tier';
                                                })
                                                ->validationMessages([
                                                    'required' => 'The east zone tier price is required.',
                                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                                ])
                                                ->deletable(function (Get $get) {
                                                    if (count($get('pcInfo_east')) > 1) {
                                                        return true;
                                                    }
                                                    return false;
                                                })
                                                ->live()
                                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) {

                                                        $tiers = $component->getState();

                                                        if (!empty($tiers)) {
                                                            unset($tiers[$arguments['item']]);
                                                            foreach ($tiers as $key => $tier) {
                                                                if (isset($tier['max_quantity'])) {
                                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                                }

                                                                // Do the same for min_quantity and price
                                                                if (isset($tier['min_quantity'])) {
                                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                                }

                                                                if (isset($tier['price'])) {
                                                                    $priceStr = (string)$tier['price'];
                                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                                }
                                                            }

                                                            $tierKeys = array_keys($tiers);
                                                            $tiersArray = array_values($tiers);

                                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                                $tierNumber = $i + 1;
                                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);

                                                                if ($i == 0) {
                                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                                } else {
                                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                                    if ($previousMax > 0) {
                                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                                    }
                                                                }
                                                            }

                                                            $updatedTiers = [];
                                                            foreach ($tierKeys as $index => $key) {
                                                                if (isset($tiersArray[$index])) {
                                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                                }
                                                            }
                                                            $set('pcInfo_east', $updatedTiers);
                                                        }
                                                    });
                                                })
                                                ->afterStateUpdated(function (Get $get, Set $set) {
                                                    $tiers = $get('pcInfo_east');
                                                    $westTiers = $get('pcInfo_west');
                                                    // dd($tiers);
                                                    if (!empty($tiers)) {
                                                        // Iterate over tiers without modifying keys
                                                        $tierKeys = array_keys($tiers);
                                                        $tiersArray = array_values($tiers);

                                                        for ($i = 1; $i < count($tiersArray); $i++) {
                                                            $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                            if ($previousMax > 0) {
                                                                // Update min_quantity without changing the key
                                                                $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                                            }
                                                            $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);
                                                        }

                                                        // Update the state without modifying keys
                                                        $set('pcInfo_east', $tiers);
                                                    }
                                                    foreach ($westTiers as $key => $tier) {
                                                        $westTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                    }
                                                    $set('pcInfo_west', $westTiers);
                                                })
                                                ->reorderable(false)
                                                ->headers([
                                                    Header::make('Type'),
                                                    Header::make('min_quantity')->label("Min Qty"),
                                                    Header::make('max_qty')->label("Max Qty"),
                                                    Header::make('price')->label("Price per Unit"),
                                                    Header::make('admin_fees')->label("Admin Fees"),
                                                    Header::make('net_earnings')->label("Net Earnings")
                                                ])
                                                ->schema([
                                                    TextInput::make('type')
                                                        ->label('')
                                                        ->disabled()
                                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                            // Find this item's position in the repeater
                                                            $rowUuid = $component->getStatePath();
                                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                                            $tiers = $get('pcInfo_east');
                                                            if (empty($tiers)) {
                                                                return "Tier 1";
                                                            } else {
                                                                return "Tier " . count($tiers) + 1;
                                                            }

                                                            $keys = array_keys($tiers);
                                                            $position = array_search($rowUuid, $keys);

                                                            if ($position === false) return "Tier ?";
                                                            return "Tier " . ($position + 1);
                                                        }),

                                                    TextInput::make('min_quantity')
                                                        ->label('')
                                                        ->formatStateUsing(fn() => 1)
                                                        ->placeholder('Min quantity')
                                                        ->numeric()
                                                        ->disabled()
                                                        ->dehydrated(true),

                                                    TextInput::make('max_quantity')
                                                        ->label('')
                                                        ->placeholder('and above')
                                                        ->dehydrated(true)
                                                        ->readOnly(function (Get $get, Component $component) {
                                                            $rowPath = $component->getStatePath();
                                                            $pathParts = explode('.', $rowPath);
                                                            $rowKey = $pathParts[2] ?? null;
                                                            $tiers = $get('../../pcInfo_east') ?? [];
                                                            $keys = array_keys($tiers);
                                                            $index = array_search($rowKey, $keys);
                                                            return $index === 2;
                                                        })
                                                        ->numeric()
                                                        ->live('blur')
                                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                            // When max_quantity is updated, we'll trigger the parent repeater's
                                                            // afterStateUpdated to recalculate min quantities for all rows
                                                            $tiers = $get('pcInfo_east');
                                                            $set('pcInfo_east', $tiers);
                                                        })
                                                        ->validationAttribute('Maximum quantity'),

                                                    TextInput::make('price')
                                                        ->label('')
                                                        ->placeholder('Price')
                                                        ->validationAttribute('Price')
                                                        ->calculateNetEarningsForRepeater(
                                                            $commission = $this->finalCommission,
                                                            $commissionType = $this->finalCommissionType,
                                                        )
                                                        ->numeric()
                                                        ->rules(function () {
                                                            return ['required', 'numeric', 'gt:0', 'price'];
                                                        })
                                                        ->prefix('RM'),

                                                    TextInput::make('admin_fees')
                                                        ->label('')
                                                        ->disabled()
                                                        ->formatStateUsing(function () use ($get) {
                                                            return (int)$get('admin_fees');
                                                        })
                                                        ->placeholder('Admin fees')
                                                        ->numeric()
                                                        ->prefix(function () {
                                                            if ($this->finalCommissionType === 'percentage') {
                                                                return '%';
                                                            }
                                                            return 'RM';
                                                        })
                                                        ->live(),

                                                    TextInput::make('net_earnings')
                                                        ->label('')
                                                        ->placeholder('Net earnings')
                                                        ->reactive()
                                                        ->disabled()
                                                        ->prefix('RM')
                                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                            $rowUuid = $component->getStatePath();
                                                            $lastDotPos = strrpos($rowUuid, '.');
                                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                                            $tiers = $get('pcInfo_east');
                                                            if (!isset($tiers[$rowUuid])) return 0;

                                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;
                                                            if ($this->finalCommissionType == 'percentage') {
                                                                $adminFees = $price * $adminFees / 100;
                                                            }
                                                            return number_format($price - $adminFees, 2);
                                                        })
                                                ])
                                                ->defaultItems(1)
                                                ->label("")
                                                ->addActionAlignment(Alignment::End)
                                                ->addActionLabel("")
                                                ->addAction(
                                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                                        ->label('+ Add Tier')
                                                        ->extraAttributes([
                                                            'style' => 'border: none !important; box-shadow: none !important;'
                                                        ])
                                                        ->action(function (Get $get, Set $set, TierValidationService $validation) {
                                                            $tiers = $get('pcInfo_east');
                                                            $isValid = $validation->validateTierCompletion($tiers);
                                                            if (!$isValid) {
                                                                Notification::make()
                                                                    ->danger()
                                                                    // ->title('Invalid Tier Addition')
                                                                    ->title('Please complete the current tier before adding a new one.')
                                                                    ->send();
                                                                return;
                                                            }
                                                            if (empty($tiers)) {
                                                                // First tier starts at 1
                                                                $minQty = 1;
                                                            } else {
                                                                // Find the max quantity of the last tier
                                                                $lastTier = end($tiers);
                                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                                            }
                                                            foreach ($tiers as $key => $tier) {

                                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                            }

                                                            $set('pcInfo_east', [
                                                                ...$tiers,
                                                                [
                                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                                    'min_quantity' => $minQty ?? 1,
                                                                    'max_quantity' => null,
                                                                    'price' => null,
                                                                    'admin_fees' => (int)$get('admin_fees')
                                                                ]
                                                            ]);
                                                        })
                                                ),

                                        ]),

                                        Section::make('West Malasiya')->schema([
                                            TableRepeater::make('pcInfo_west')
                                                ->maxItems(3)
                                                ->live()
                                                ->minItems(function (Get $get) {
                                                    return $get('price_type') === 'tier' ? 1 : 0;
                                                })
                                                ->required(function (Get $get) {
                                                    return $get('price_type') === 'tier';
                                                })
                                                ->validationMessages([
                                                    'required' => 'The east zone tier price is required.',
                                                    'min_items' => 'At least one iteration of zone tier price is required.',
                                                ])
                                                ->deletable(function (Get $get) {
                                                    if (count($get('pcInfo_west')) > 1) {
                                                        return true;
                                                    }
                                                    return false;
                                                })
                                                ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                    return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) {

                                                        $tiers = $component->getState();

                                                        if (!empty($tiers)) {
                                                            unset($tiers[$arguments['item']]);
                                                            foreach ($tiers as $key => $tier) {
                                                                if (isset($tier['max_quantity'])) {
                                                                    $maxQtyStr = (string)$tier['max_quantity'];

                                                                    $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                                }

                                                                // Do the same for min_quantity and price
                                                                if (isset($tier['min_quantity'])) {
                                                                    $minQtyStr = (string)$tier['min_quantity'];
                                                                    $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                                }

                                                                if (isset($tier['price'])) {
                                                                    $priceStr = (string)$tier['price'];
                                                                    $tiers[$key]['price'] = (float)$priceStr;
                                                                }
                                                            }

                                                            $tierKeys = array_keys($tiers);
                                                            $tiersArray = array_values($tiers);

                                                            for ($i = 0; $i < count($tiersArray); $i++) {
                                                                $tierNumber = $i + 1;
                                                                $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                                $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);

                                                                if ($i == 0) {
                                                                    $tiersArray[$i]['min_quantity'] = 1;
                                                                } else {
                                                                    $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                        (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                                    if ($previousMax > 0) {
                                                                        $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                                    }
                                                                }
                                                            }

                                                            $updatedTiers = [];
                                                            foreach ($tierKeys as $index => $key) {
                                                                if (isset($tiersArray[$index])) {
                                                                    $updatedTiers[$key] = $tiersArray[$index];
                                                                }
                                                            }
                                                            $set('pcInfo_west', $updatedTiers);
                                                        }
                                                    });
                                                })
                                                ->afterStateUpdated(function (Get $get, Set $set) {
                                                    $tiers = $get('pcInfo_west');
                                                    $eastTiers = $get('pcInfo_east');

                                                    if (!empty($tiers)) {
                                                        // Iterate over tiers without modifying keys
                                                        $tierKeys = array_keys($tiers);
                                                        $tiersArray = array_values($tiers);

                                                        for ($i = 1; $i < count($tiersArray); $i++) {
                                                            $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                            if ($previousMax > 0) {
                                                                // Update min_quantity without changing the key
                                                                $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                                            }
                                                            $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);
                                                        }

                                                        // Update the state without modifying keys
                                                        $set('pcInfo_west', $tiers);
                                                    }
                                                    foreach ($eastTiers as $key => $tier) {
                                                        $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                    }
                                                    $set('pcInfo_east', $eastTiers);
                                                })
                                                ->reorderable(false)
                                                ->headers([
                                                    Header::make('Type'),
                                                    Header::make('min_quantity')->label("Min Qty"),
                                                    Header::make('max_qty')->label("Max Qty"),
                                                    Header::make('price')->label("Price per Unit"),
                                                    Header::make('admin_fees')->label("Admin Fees"),
                                                    Header::make('net_earnings')->label("Net Earnings")
                                                ])
                                                ->schema([
                                                    TextInput::make('type')
                                                        ->label('')
                                                        ->disabled()
                                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                            // Find this item's position in the repeater
                                                            $rowUuid = $component->getStatePath();
                                                            $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                                            $tiers = $get('pcInfo_west');
                                                            if (empty($tiers)) {
                                                                return "Tier 1";
                                                            } else {
                                                                return "Tier " . count($tiers) + 1;
                                                            }

                                                            $keys = array_keys($tiers);
                                                            $position = array_search($rowUuid, $keys);

                                                            if ($position === false) return "Tier ?";
                                                            return "Tier " . ($position + 1);
                                                        }),

                                                    TextInput::make('min_quantity')
                                                        ->label('')
                                                        ->formatStateUsing(fn() => 1)
                                                        ->placeholder('Min quantity')
                                                        ->numeric()
                                                        ->disabled()
                                                        ->dehydrated(true),

                                                    TextInput::make('max_quantity')
                                                        ->label('')
                                                        ->placeholder('and above')
                                                        ->dehydrated(true)
                                                        ->readOnly(function (Get $get, Component $component) {
                                                            $rowPath = $component->getStatePath();
                                                            $pathParts = explode('.', $rowPath);
                                                            $rowKey = $pathParts[2] ?? null;
                                                            $tiers = $get('../../pcInfo_west') ?? [];
                                                            $keys = array_keys($tiers);
                                                            $index = array_search($rowKey, $keys);
                                                            return $index === 2;
                                                        })
                                                        ->numeric()
                                                        ->live(onBlur: true)
                                                        ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                            $tiers = $get('pcInfo_west');
                                                            $set('pcInfo_west', $tiers);
                                                        })
                                                        ->validationAttribute('Maximum quantity'),

                                                    TextInput::make('price')
                                                        ->label('')
                                                        ->placeholder('Price')
                                                        ->validationAttribute('Price')
                                                        ->calculateNetEarningsForRepeater(
                                                            $commission = $this->finalCommission,
                                                            $commissionType = $this->finalCommissionType,
                                                        )
                                                        ->numeric()
                                                        ->rules(function () {
                                                            return ['required', 'numeric', 'gt:0', 'price'];
                                                        })
                                                        ->prefix('RM'),

                                                    TextInput::make('admin_fees')
                                                        ->label('')
                                                        ->disabled()
                                                        ->formatStateUsing(function () use ($get) {
                                                            return (int)$get('admin_fees');
                                                        })
                                                        ->placeholder('Admin fees')
                                                        ->numeric()
                                                        ->prefix(function () {
                                                            if ($this->finalCommissionType === 'percentage') {
                                                                return '%';
                                                            }
                                                            return 'RM';
                                                        }),

                                                    TextInput::make('net_earnings')
                                                        ->label('')
                                                        ->placeholder('Net earnings')
                                                        ->disabled()
                                                        ->prefix('RM')
                                                        ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                            $rowUuid = $component->getStatePath();
                                                            $lastDotPos = strrpos($rowUuid, '.');
                                                            $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                                            $tiers = $get('pcInfo_west');
                                                            if (!isset($tiers[$rowUuid])) return 0;

                                                            $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                                            $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                                            return number_format($price - $adminFees, 2);
                                                        })
                                                ])
                                                ->defaultItems(1)
                                                ->label("")
                                                ->addActionAlignment(Alignment::End)
                                                ->addActionLabel("")
                                                ->addAction(
                                                    fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                                        ->label('+ Add Tier')
                                                        ->extraAttributes([
                                                            'style' => 'border: none !important; box-shadow: none !important;'
                                                        ])
                                                        ->action(function (Get $get, Set $set, TierValidationService $validation) {
                                                            $tiers = $get('pcInfo_west');
                                                            $isValid = $validation->validateTierCompletion($tiers);
                                                            if (!$isValid) {
                                                                Notification::make()
                                                                    ->danger()
                                                                    // ->title('Invalid Tier Addition')
                                                                    ->title('Please complete the current tier before adding a new one.')
                                                                    ->send();
                                                                return;
                                                            }
                                                            if (empty($tiers)) {
                                                                $minQty = 1;
                                                            } else {
                                                                $lastTier = end($tiers);
                                                                $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                                            }
                                                            foreach ($tiers as $key => $tier) {

                                                                $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                            }
                                                            $set('pcInfo_west', [
                                                                ...$tiers,
                                                                [
                                                                    'type' => 'Tier ' . count($tiers) + 1,
                                                                    'min_quantity' => $minQty ?? 1,
                                                                    'max_quantity' => null,
                                                                    'price' => null,
                                                                    'admin_fees' => (int)$get('admin_fees')
                                                                ]
                                                            ]);
                                                        })
                                                ),

                                        ])
                                    ];
                                })
                        ]),
                ])




        ])->statePath('data');
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {
        if ($finalCommissionType == 'percentage') {
            $commission = $finalCommission * $price / 100;
            $earning = $price - $commission;
        } else {
            $earning = $price - $finalCommission;
        }

        return number_format($earning, 2);
    }

    /**
     * Format bonus structure for activity logging
     */
    private function formatBonusStructure($region): array
    {
        $structure = [];
        for ($i = 1; $i <= 3; $i++) {
            $qty = $this->productData["{$region}_bonus_{$i}_quantity"] ?? null;
            $bonusQty = $this->productData["{$region}_bonus_{$i}_quantity_value"] ?? null;
            
            if (!empty($qty) && !empty($bonusQty)) {
                $structure["Level {$i}"] = "Buy {$qty}, Get {$bonusQty} Free";
            }
        }
        return empty($structure) ? ['No bonus structure set'] : $structure;
    }

    /**
     * Format tier structure for activity logging
     */
    private function formatTierStructure($tierData): array
    {
        if (empty($tierData)) {
            return ['No tier structure set'];
        }
        
        $structure = [];
        foreach ($tierData as $index => $tier) {
            $tierNum = $index + 1;
            $minQty = $tier['min_quantity'] ?? 'N/A';
            $maxQty = $tier['max_quantity'] ?? 'Above';
            $price = isset($tier['price']) ? 'RM ' . number_format($tier['price'], 2) : 'Not Set';
            
            if ($maxQty === 'Above' || empty($maxQty)) {
                $structure["Tier {$tierNum}"] = "{$minQty}+ units = {$price} each";
            } else {
                $structure["Tier {$tierNum}"] = "{$minQty}-{$maxQty} units = {$price} each";
            }
        }
        return $structure;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $this->wholeSalePackSize = $data['whole_sale_pack_size'] ?? 1;
        $this->stockType = $data['stock_type'];
        
        // 🎯 Preserve important data for activity logging before they get unset
        $this->preservedStockData = [
            'stock_type' => $data['stock_type'],
            'whole_sale_pack_size' => $data['whole_sale_pack_size'] ?? 1
        ];
        
        unset($data['whole_sale_pack_size']);
        unset($data['stock_type']);
        $this->inStock = $data['in_stock'];
        unset($data['images']);
        $productData = $data['productData'] ?? null;

        if ($productData['price_type'] == 'bonus') {
            // East bonus quantity validation
            $bonusEastQuantity = [];
            for ($i = 1; $i < 4; $i++) {
                if (!empty($productData["east_bonus_{$i}_quantity"])) {
                    $bonusEastQuantity[$i] = $productData["east_bonus_{$i}_quantity"];
                }
            }

            $eastIndexes = array_keys($bonusEastQuantity);
            $expectedEast = range(1, count($bonusEastQuantity));

            if ($eastIndexes !== $expectedEast) {
                Notification::make()
                    ->body('Please enter east bonus quantities in sequential order (no gaps).')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'east_bonus_quantity' => ['Please fill East quantities in order (no skipping).'],
                ]);
            }

            $eastUnique = array_unique($bonusEastQuantity);
            if (count($bonusEastQuantity) !== count($eastUnique)) {
                Notification::make()
                    ->body('Please make sure that all east quantities are unique.')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'east_bonus_quantity' => ['East quantities must be unique.'],
                ]);
            }

            // West bonus quantity validation
            $bonusWestQuantity = [];
            for ($i = 1; $i < 4; $i++) {
                if (!empty($productData["west_bonus_{$i}_quantity"])) {
                    $bonusWestQuantity[$i] = $productData["west_bonus_{$i}_quantity"];
                }
            }

            $westIndexes = array_keys($bonusWestQuantity);
            $expectedWest = range(1, count($bonusWestQuantity));

            if ($westIndexes !== $expectedWest) {
                Notification::make()
                    ->body('Please enter west bonus quantities in sequential order (no gaps).')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'west_bonus_quantity' => ['Please fill West quantities in order (no skipping).'],
                ]);
            }

            $westUnique = array_unique($bonusWestQuantity);
            if (count($bonusWestQuantity) !== count($westUnique)) {
                Notification::make()
                    ->body('Please make sure that all west quantities are unique.')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'west_bonus_quantity' => ['West quantities must be unique.'],
                ]);
            }
        }

        // 🎯 Initialize preserved tier data
        $this->preservedTierData = [
            'pcInfo_east' => [],
            'pcInfo_west' => []
        ];
        
        if ($productData['price_type'] == 'tier') {
            $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
            $westTierPriceInfo = $data['pcInfo_west'] ?? [];
            
            // 🎯 Preserve tier data for activity logging before transformation
            $this->preservedTierData = [
                'pcInfo_east' => $eastTierPriceInfo,
                'pcInfo_west' => $westTierPriceInfo
            ];
            
            $eastTierPrice = [];
            $westTierPrice = [];
            foreach ($eastTierPriceInfo as $key => $value) {
                $count = $key + 1;
                $eastTierPrice["east_tier_{$count}_min_quantity"] = $value['min_quantity'];
                $eastTierPrice["east_tier_{$count}_max_quantity"] = $value['max_quantity'];
                $eastTierPrice["east_tier_{$count}_base_price"] = $value['price'];
            }
            foreach ($westTierPriceInfo as $key => $value) {
                $count = $key + 1;
                $westTierPrice["west_tier_{$count}_min_quantity"] = $value['min_quantity'];
                $westTierPrice["west_tier_{$count}_max_quantity"] = $value['max_quantity'];
                $westTierPrice["west_tier_{$count}_base_price"] = $value['price'];
            }
            $productData = array_merge($productData, $eastTierPrice, $westTierPrice);
            unset($data['pcInfo_east']);
            unset($data['pcInfo_west']);
        }
        $productData['is_batch_wise_stock'] = $productData['stock_type'] == 'batch_wise' ? true : false;
        $productData['sku'] = $data['sku'];
        unset($data['productData']);
        unset($data['in_stock']);
        unset($data['east_net_earnings']);
        unset($data['west_net_earnings']);
        unset($data['east_admin_fees']);
        unset($data['west_admin_fees']);
        $batchkData = $data['batches'] ?? [];
        unset($data['batches']);
        $data['add_request_by'] = auth()->user()->id;
        $data['is_created_by_admin'] = true;
        $data['approved_on'] = now();
        $data['approved_by'] = auth()->user()->id;
        $data['status'] = 'approved';
        $this->productData = $productData;

        $this->productPriceData = Arr::except($this->productData, ['stock_type', 'stock', 'low_stock', 'expires_on_after', 'sku', 'price_type', 'is_batch_wise_stock']);

        $this->batchData = $batchkData;
        $data['owner_id'] = $this->pcId; //need to update after sub user introduced
        $data['status'] = 'approved';
        return $data;
    }

    protected function afterCreate(): void
    {
        $product = $this->record;
        $defaultImage = $this->defaultIndex;
        $this->productData['user_id'] = $this->pcId;
        $this->productData['requested_by'] = $this->pcId;
        $this->productData['is_batch_wise_stock'] = $this->productData['stock_type'] == 'batch' ? true : false;
        $totalStock = 0;
        $this->productData['admin_approval'] = true;
        if ($this->inStock == 'yes') {
            if (!empty($this->batchData) && is_array($this->batchData)) {
                foreach ($this->batchData as $batch) {
                    $totalStock += $batch['available_stock'];
                }
            } else {
                $totalStock = $this->productData['stock'] ?? 0;
            }
        }
        unset($this->productData['stock_type']);
        $defaultImage = DB::table('media')->where(['model_id' => $this->record->id])->get()->toArray()[$defaultImage]->id;
        $this->record->update(['default_image_id' => $defaultImage]);

        if (!empty($this->productData) && is_array($this->productData)) {
            $productRelationData['user_id'] = $this->pcId;
            $productRelationData['requested_by'] = $this->pcId;
            $productRelationData['admin_approval'] = true;
            $productRelationData['price_type'] = $this->productData['price_type'];

            $productRelationId = $product->productData()->create($productRelationData);
            $productStockData['product_relation_id'] = $productRelationId->id;
            $this->productPriceData['product_relation_id'] = $productRelationId->id;
            $productStockData['expiry_date'] = !empty($this->productData['expires_on_after']) ? Carbon::parse($this->productData['expires_on_after'])->setTime(23,59,00) : null;
            $productStockData['is_batch_wise_stock'] = $this->productData['is_batch_wise_stock'] == 'batch' ? true : false;
            $productStockData['stock'] = $this->productData['stock'] ?? 0;
            $productStockData['total_stock'] = $totalStock ?? 0;
            $productStockData['low_stock'] = $this->productData['low_stock'] ?? null;
            $productStockData['wholesale_pack_size'] = $this->wholeSalePackSize;
            $productStockData['stock_type'] = $this->stockType ?? 'unit';
            ProductRelationStock::create($productStockData);
            ProductRelationPrice::create($this->productPriceData);
            // $sku = mb_substr($this->data['name'], 0, 4) . "_" . $productRelationId->id . "_" . rand(100000, 999999);
            $productRelationId->update(['sku' => $this->productData['sku'] ?? null]);

            if (!empty($this->batchData) && is_array($this->batchData)) {
                foreach ($this->batchData as $batch) {
                    $batch['products_relation_id'] = $productRelationId->id;
                    $batch['product_id'] = $product->id;
                    $batch['user_id'] = $this->pcId;
                    ProductBatch::insert($batch);
                }
            }
        }
        $name = User::find($this->pcId)->name;
        
        // 🎯 Create comprehensive human-readable activity data
        $humanReadableData = [
            // Basic Product Info
            'Product Name' => $product->name,
            'SKU' => $this->productData['sku'] ?? 'Not Set',
            'Created By Admin' => Auth::user()->name,
            'Created For PC' => $name,
            'Created By ID' => Auth::user()->id,
            'PC ID' => $this->pcId,
            
            // Product Details (Human Readable)
            'Category' => $product->category?->name ?? 'Unknown Category',
            'Sub Category' => $product->subcategory?->name ?? 'No Sub Category',
            'Brand' => $product->brand?->name ?? 'Unknown Brand',
            'Unit' => $product->unit?->name ?? 'Unknown Unit',
            'Dosage Form' => $product->dosageForm?->name ?? 'Unknown Form',
            'Generic Name' => $product->genericName?->name ?? 'No Generic Name',
            'Container' => $product->container?->name ?? 'No Container',
            'Quantity Per Unit' => $product->quantity_per_unit ?? 'Not Set',
            
            // Pricing Information
            'Pricing Type' => ucfirst($this->productData['price_type'] ?? 'unknown'),
            'Commission Type' => ucfirst($this->finalCommissionType ?? 'percentage'),
            'Commission Value' => $this->finalCommissionType === 'percentage' 
                ? $this->finalCommission . '%' 
                : 'RM ' . number_format($this->finalCommission, 2),
                
            // Stock Configuration
            'Stock Management' => $this->productData['is_batch_wise_stock'] ? 'By Batch' : 'Without Batch',
            'Stock Type' => $this->preservedStockData['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
            'Wholesale Pack Size' => $this->preservedStockData['whole_sale_pack_size'] ?? 'Not Set',
            'Low Stock Trigger' => $this->productData['low_stock'] ?? 'Not Set',
            'Total Stock' => $this->productData['is_batch_wise_stock'] ? $totalStock : ($this->productData['stock'] ?? 'Not Set'),
            
            // Product Status
            'Status' => ucfirst($product->status ?? 'pending'),
            'Admin Approved' => 'Yes',
            'Approved On' => now()->format('Y-m-d H:i:s'),
            'Default Image Set' => !empty($defaultImage) ? 'Yes' : 'No',
        ];
        
        // Add pricing details based on type
        if ($this->productData['price_type'] === 'fixed') {
            $humanReadableData['Fixed Pricing'] = [
                'East Malaysia' => 'RM ' . number_format($this->productData['east_zone_price'] ?? 0, 2),
                'West Malaysia' => 'RM ' . number_format($this->productData['west_zone_price'] ?? 0, 2),
            ];
        } elseif ($this->productData['price_type'] === 'bonus') {
            $humanReadableData['Bonus Pricing'] = [
                'East Malaysia' => [
                    'Base Price' => 'RM ' . number_format($this->productData['east_zone_price'] ?? 0, 2),
                    'Bonus Structure' => $this->formatBonusStructure('east')
                ],
                'West Malaysia' => [
                    'Base Price' => 'RM ' . number_format($this->productData['west_zone_price'] ?? 0, 2),
                    'Bonus Structure' => $this->formatBonusStructure('west')
                ]
            ];
        } elseif ($this->productData['price_type'] === 'tier' && !empty($this->preservedTierData)) {
            $humanReadableData['Tier Pricing'] = [
                'East Malaysia' => $this->formatTierStructure($this->preservedTierData['pcInfo_east']),
                'West Malaysia' => $this->formatTierStructure($this->preservedTierData['pcInfo_west'])
            ];
        }
        
        // Add batch information if available
        if (!empty($this->batchData) && is_array($this->batchData)) {
            $humanReadableData['Batch Information'] = [];
            foreach ($this->batchData as $index => $batch) {
                $batchNum = $index + 1;
                $humanReadableData['Batch Information']["Batch {$batchNum}"] = [
                    'Batch Number' => $batch['batch_number'] ?? 'Not Set',
                    'Available Stock' => $batch['available_stock'] ?? 0,
                    'Manufacturing Date' => $batch['manufacturing_date'] ?? 'Not Set',
                    'Expiry Date' => $batch['expiry_date'] ?? 'Not Set',
                ];
            }
        }
        
        // 🎯 Log with human-readable data using Spatie's structure
        activity()
            ->performedOn($product)
            ->causedBy(Auth::user())
            ->withProperties([
                'old' => [], // No previous state since this is a new product creation by admin
                'attributes' => $humanReadableData // The new product data being created
            ])
            ->log("Admin created master product '{$product->name}' for PC: {$name} with {$humanReadableData['Pricing Type']} pricing structure");
            $pcDetails = User::find($this->pcId);    
            Mail::to($pcDetails->email)->send(new AdminCreatedNewProductForPCMail($product, $pcDetails));
            Notification::make()
            ->body("$product->name has been created by admin")
            ->actions([
                \Filament\Notifications\Actions\Action::make('view')
                    ->label('View Product')
                    ->color('info')
                    ->url(url(config('app.pc_url') . '/products/' . $product->id . "/view"))
            ])
            ->info()
            ->sendToDatabase(User::find($this->pcId));
    }

    public function saveAction(): Action
    {
        return Action::make('save')
            ->label('Add')
            ->action('create');
    }

    public function reAction(): Action
    {
        return Action::make('cancel')
            ->label('Cancel')
            ->action('resetForm');
    }



    public function resetForm()
    {
        $this->data = [
            'price_type' => 'fixed',
        ];
        $this->form->fill($this->data);
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }
}
