<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use App\Models\ProductBatch;
use Filament\Actions\Action;
use App\Models\ProductRelation;
use App\Jobs\BulkStockUpdateJob;
use Filament\Resources\Pages\Page;
use Filament\Notifications\Notification;
use App\Filament\Admin\Resources\ProductResource;
use App\Filament\Admin\Resources\ProductResource\Widgets\NormalProducts;
use App\Filament\Admin\Resources\ProductResource\Widgets\BatchWiseProducts;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ProductBulkStockUpdate extends Page
{
    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.product-bulk-stock-update';

    protected static ?string $header = 'Bulk Stock Update';

    protected static ?string $widgetSpace = 'full';

    public function getTitle(): string|Htmlable
    {
        return 'Bulk Stock Update';
    }


    protected function getHeaderActions(): array
    {
        return [
            Action::make('cancel')
                ->action(function () {
                    session()->forget('data');
                })
                ->label(fn() => new HtmlString("<div class='font-medium text-gray-700'>Cancel</div>"))
                ->color('white'),
            Action::make('save')
                ->action(function (array $data) {
                    // dd(session()->get('data'));
                    $batchData = session()->get('data')['batch_data_'] ?? [];
                    $normalData = session()->get('data')['normal'] ?? [];
                    $userId = auth()->id();

                    if (!empty($batchData) || !empty($normalData)) {
                        BulkStockUpdateJob::dispatch($batchData, $normalData, $userId);
                        Notification::make()
                            // ->title('Bulk Stock Update')
                            ->title('Bulk Update process is started, Please check the status in the notification.')
                            ->success()
                            ->send();
                    }

                    session()->forget('data');
                    // $this->dispatch('refresh');
                    // $batchData = session()->get('data')['batch_data_'] ?? [];
                    // $normalData = session()->get('data')['normal'] ?? [];

                    // if (!empty($batchData)) {
                    //     foreach ($batchData as $productId => $batch) {
                    //         $productRelationId = ProductRelation::whereProductId($productId)->value('id');
                    //         ProductBatch::where('product_id', $productId)->where('products_relation_id', $productRelationId)->delete();
                    //         $batches = $batch['batches'];
                    //         foreach ($batches as $batch) {
                    //             $batchToInsert = [
                    //                 'product_id' => $productId,
                    //                 'batch_name' => $batch['batch_name'],
                    //                 'user_id' => auth()->user()->id,
                    //                 'products_relation_id' => $productRelationId,
                    //                 'available_stock' => $batch['available_stock'],
                    //                 'expiry_date' => $batch['expiry_date'],
                    //             ];
                    //             ProductBatch::create($batchToInsert);
                    //         }
                    //     }
                    // }

                    // if (!empty($normalData)) {
                    //     foreach ($normalData as $productId => $productData) {
                    //         // dd($productData);
                    //         ProductRelation::whereProductId($productId)->update([
                    //             'stock' => $productData,
                    //         ]);
                    //     }
                    // }
                    // session()->forget('data');
                    // $this->dispatch('refresh');
                }),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            BatchWiseProducts::class,
            // NormalProducts::class,
        ];
    }
}
