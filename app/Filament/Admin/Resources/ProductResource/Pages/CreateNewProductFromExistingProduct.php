<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use Carbon\Carbon;
use App\Models\Unit;
use App\Models\User;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\PcDetail;
use Filament\Forms\Form;
use App\Models\Container;
use App\Models\DosageForm;
use Illuminate\Support\Str;
use App\Models\ProductBatch;
use Filament\Actions\Action;
use Illuminate\Http\Request;
use App\Models\ProductRelation;
use Illuminate\Validation\Rule;
use Awcodes\TableRepeater\Header;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Component\AttributeSection;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Cache;
use App\Service\TierValidationService;
use Filament\Forms\Components\Section;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use App\Services\ProductAttributeService;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\RichEditor;
use App\Services\PostSaveAttributeService;
use Filament\Forms\Components\Placeholder;
use App\Filament\Admin\Resources\UserResource;
use Illuminate\Validation\ValidationException;
use Filament\Forms\Concerns\InteractsWithForms;
use App\Filament\Admin\Resources\ProductResource;
use Awcodes\TableRepeater\Components\TableRepeater;

class CreateNewProductFromExistingProduct extends Page implements HasForms
{
    use InteractsWithForms;

    public $data;

    public $commission;

    public $finalCommission;

    public $finalCommissionType;

    public $pcId;

    public $pharmaSupplier;

    public $sku;

    public $images;

    protected ?string $heading = "Add Product";

    public ?string $packaging = null;

    public $wholeSalePackSize = null;

    public $stockType = null;

    /**
     * Get cached DosageForm by ID to avoid duplicate queries
     * Cache for 5 minutes and invalidate when DosageForm data changes
     */
    protected function getCachedDosageForm($id)
    {
        if (empty($id)) {
            return null;
        }

        return Cache::remember("dosage_form_{$id}", 300, function () use ($id) {
            return DosageForm::find($id);
        });
    }

    /**
     * Get cached Unit options to avoid duplicate queries
     * Cache for 5 minutes and invalidate when Unit data changes
     */
    protected function getCachedUnitOptions()
    {
        return Cache::remember('unit_options', 300, function () {
            return Unit::all()->pluck('name', 'id');
        });
    }

    /**
     * Get cached DosageForm options to avoid duplicate queries
     * Cache for 5 minutes and invalidate when DosageForm data changes
     */
    protected function getCachedDosageFormOptions()
    {
        return Cache::remember('dosage_form_options', 300, function () {
            return DosageForm::all()->pluck('name', 'id');
        });
    }

    /**
     * Clear all form-related caches
     * Useful for manual cache clearing when needed
     */
    public function clearFormCaches()
    {
        Cache::forget('unit_options');
        Cache::forget('dosage_form_options');
        Cache::tags(['units', 'dosage_forms'])->flush();
    }

    /**
     * Refresh all cached data
     * Clears cache and re-fetches data
     */
    public function refreshCachedData()
    {
        $this->clearFormCaches();

        // Pre-warm the cache with fresh data
        $this->getCachedUnitOptions();
        $this->getCachedDosageFormOptions();
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Products',
            "#" => 'Add Product',
        ];
    }

    public function mount($record): void
    {
        $pc_id = request()->query('user_id');
        $this->pharmaSupplier = User::find($pc_id);

        $this->pcId = $pc_id;
        $this->data = Product::where('id', $record)
            ->with('category', 'subcategory', 'brand', 'unit', 'foam', 'productData', 'container', 'distributors')
            ->first();

        $globalCommission = DB::table('global_settings')->whereIn('name', ['commission', 'commission_type'])->get();
        $pcCommission = PcDetail::where('user_id', $this->pcId)->first();
        $productCommission = !empty($this->data['product_data']['commission_amount']) ? $this->data['product_data']['commission_amount'] : 0;
        $this->commission = $pcCommission?->commission_percentage ?? 0;
        $this->finalCommissionType = $pcCommission->commission_type;
        $this->finalCommission = $this->commission;
        $this->sku = mb_substr($this->data['name'], 0, 4) . "_"  . rand(100000, 999999);
        $this->data['admin_fees'] = $this->finalCommission;
        $this->data['commission_type'] = $this->finalCommissionType;
        $this->images = $this->data->getMedia('product-images')->map(function ($image) {
            return $image->getFullUrl();
        });
        $this->data['pcInfo_east'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);

        $this->data['pcInfo_west'] =  array_fill(0, 1, [
            'min_quantity' => null,
            'max_quantity' => null,
            'price' => null,
            'admin_fees' => null,
            'net_earnings' => null,
        ]);

        $this->data['sku'] = $this->sku;
        $this->form->fill($this->data->toArray());
        Notification::make()
            ->title('Creating Product for ' . $this->pharmaSupplier->name)
            // ->body('Product is being created')
            ->info()
            ->send();
    }
    protected static string $resource = ProductResource::class;

    protected static string $view = 'filament.pc.resources.product-resource.pages.create-new-product-from-existing-product';

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('create', ['user_id' => $this->pcId])),
        ];
    }

    public function form(Form $form): Form
    {

        return $form
            ->extraAttributes(['x-data' => '{ showDetails: false }'])
            ->schema([
                TextInput::make('admin_fees')->hidden(),
                TextInput::make('commission_type')->hidden(),
                Hidden::make('has_variants')
                    ->default(false),
                Hidden::make('attribute_data')
                    ->default(null),
                Grid::make(3)->schema([
                    Section::make('Product Details')
                        ->schema([
                            Grid::make(2)->schema([

                                Group::make()->schema([
                                    TextInput::make('name')
                                        ->label('Product Name')
                                        ->disabled(),
                                    TextInput::make('sku')
                                        ->validationAttribute('SKU')
                                        ->label('Stock Keeping Unit (SKU)')
                                        ->required(),
                                    Placeholder::make('category.name')
                                        ->label('Category')
                                        ->content($this->data['category']['name'])
                                        ->columnSpan(1)
                                        ->extraAttributes(['class' => 'inline-block']),

                                    Placeholder::make('sub_category')
                                        ->label('Sub Category')
                                        ->content(fn($get) => $this->data['subcategory']['name'] ?? "-")
                                        ->columnSpan(1),
                                        Placeholder::make('brand.name')
                                        ->label('Brand')
                                        ->content($this->data['brand']['name']),
                                    Placeholder::make('generic.name')
                                        ->label('Generic Name')
                                        ->content($this->data['generic']['name']),
                                    Placeholder::make("distributors")
                                        ->label("Distributors")
                                        ->content(function () {
                                            $destributors = $this->data['distributors'];
                                            $names = [];
                                            foreach ($destributors as $dist) {
                                                $names[] = $dist['name'] ?? '-';
                                            }
                                            return implode(' | ', $names);
                                        }),
                                    Placeholder::make('is_prescription_required')
                                        ->label('Prescription Required')
                                        ->content(fn($get) => $this->data['is_prescription_required'] ? 'Yes' : 'No'),

                                    Group::make()
                                        ->schema([
                                            Placeholder::make('view_more')
                                                ->label('')
                                                ->content(new HtmlString('
                                                    <button
                                                        type="button"
                                                        class="flex items-center gap-1 text-primary-500 hover:underline"
                                                        x-on:click="showDetails = !showDetails"
                                                    >
                                                        <span>View more details</span>
                                                        <!-- Arrow Down Icon (visible when closed) -->
                                                        <svg x-show="!showDetails" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                        <!-- Arrow Up Icon (visible when open) -->
                                                        <svg x-show="showDetails" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
                                                        </svg>
                                                    </button>
                                                ')),
                                        ]),
                                ])->columns(2)->columnSpanFull()
                            ]),
                        ])->columnSpan(2),
                    Section::make('Product Images')
                        ->schema([
                            ViewField::make('images')
                                ->view('forms.components.drag-drop-view')
                                ->hiddenLabel()
                                ->viewData([
                                    'images' => $this->images,
                                ])
                        ])->columnSpan(1),
                ])->columnSpanFull(),
                Section::make()
                    ->heading('Product Description')

                    ->extraAttributes(['x-show' => 'showDetails', 'x-cloak' => true])
                    ->schema([
                        Group::make()
                            ->label('Product Details')
                            ->schema([
                                Tabs::make(label: 'Product Details')
                                    // ->label(new HtmlString('<div class="text-green-800">Product Details</div>'))
                                    ->schema([
                                        Tab::make('Product Description')
                                            ->schema([
                                                RichEditor::make('product_description') // need to add migration
                                            ]),
                                        Tab::make('Key Ingredients')
                                            ->schema([
                                                RichEditor::make('description_ingredients') // need to add migration
                                            ]),
                                        Tab::make('Storage Instructions')
                                            ->schema([
                                                RichEditor::make('description_storage_instructions')  // need to add migration
                                            ]),
                                        Tab::make('Usage/Indication')
                                            ->schema([
                                                RichEditor::make('description_indications')
                                            ]),
                                        Tab::make('Contradiction')
                                            ->schema([
                                                RichEditor::make('description_contradictions')
                                            ]),
                                        Tab::make('How to Use')
                                            ->schema([
                                                RichEditor::make('description_how_to_use') // need to add migration
                                            ]),
                                        Tab::make('Safety Information/Pregnancy')
                                            ->schema([
                                                RichEditor::make('description_safety_information') // need to add migration
                                            ]),
                                        Tab::make('Dosage Information')
                                            ->schema([
                                                RichEditor::make('description_dosage')
                                            ]),
                                        Tab::make('Side Effects')
                                            ->schema([
                                                RichEditor::make('description_side_effects')
                                            ]),
                                    ]),
                            ])
                    ])->columnSpanFull(),
                // AttributeSection::make(),
                Section::make('Stock Details')
                    ->schema([
                        Group::make()
                            ->schema([
                                Radio::make('productData.is_batch_wise_stock')
                                    ->validationAttribute('Is Batch Wise Stock')
                                    ->live()
                                    ->rules(['required'])
                                    // ->formatStateUsing(function () {
                                    //     return false;
                                    // })
                                    ->label('Manage Product')
                                    ->options([
                                        false => 'Without Batch Number',
                                        true => 'By Batch',
                                    ])
                                    ->formatStateUsing(fn() => true)
                                    ->inline()
                                    ->columnSpan(2),
                                Radio::make('stock_type')
                                    // ->label(new HtmlString('Stock Type<span class="text-red-500" style="color:red;">*</span>'))
                                    ->options([
                                        'unit' => 'Unit',
                                        'wps' => new HtmlString('Wholesale Pack<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Indicates the quantity of items included in one wholesale unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>'),
                                    ])
                                    ->inline()
                                    ->live()
                                    ->rules(['required'])
                                    ->validationAttribute('stock type')
                                    ->formatStateUsing(fn() => 'unit')
                                    ->columnSpan(2),
                            ])
                            ->columnSpanFull(),


                        Select::make('in_stock')
                            ->label(new HtmlString('Stock Status<span class="text-red-500" style="color:red;">*</span>'))
                            ->validationAttribute('Stock Status')
                            ->rules(['required'])
                            ->live()
                            ->formatStateUsing(fn() => 'yes')
                            ->options([
                                'yes' => 'In Stock',
                                'no' => 'Out of Stock',
                            ]),
                        TextInput::make('container.name')
                            ->rules(['required'])
                            ->validationAttribute('packaging')
                            ->afterStateUpdated(function (Get $get) {
                                $this->packaging = $get('container.name');
                            })
                            ->live()
                            ->label(PackagingToolTip::tooltip())
                            ->disabled(),
                        TextInput::make('stock')
                            ->validationAttribute('Stock')
                            ->rules(function (Get $get) {
                                $lowStock = $get('low_stock');
                                if ($get('in_stock') == 'yes' && !$get('productData.is_batch_wise_stock')) {
                                    return ['required', 'numeric', 'gt:' . $lowStock];
                                }
                                return ['sometimes', 'numeric', 'gt:' . $lowStock];
                            })
                            ->visible(function (Get $get) {
                                if (!$get('productData.is_batch_wise_stock') && $get('in_stock') == 'yes') {
                                    return true;
                                }
                                return false;
                            })

                            ->label(function (Get $get, $record) {
                                if (!$get('productData.is_batch_wise_stock')) {

                                    return new HtmlString("Stock by by {$get('container.name')}<span class='text-red-500' style='color:red;'>*</span>");
                                }
                                return 'Stock';
                            }),
                        TextInput::make('quantity_per_unit')
                            ->disabled()
                            ->live()
                            ->dehydrated(false)
                            ->label(function (Get $get) {
                                if ((!empty($get('container.name')))) {
                                    return new HtmlString("Volume by {$get('container.name')}<span class='text-red-500' style='color:red;'>*</span>");
                                }
                                return new HtmlString('Volume<span class="text-red-500" style="color:red;">*</span>');
                            })
                            ->validationAttribute('Vlume')
                            ->rules(['required', 'gt:0']),
                        Select::make('unit_id')
                            ->disabled()
                            ->rules(['required', 'gt:0'])
                            ->default(null)
                            ->placeholder('select unit')
                            ->label(new HtmlString('Volume Unit<span class="text-red-500" style="color:red;">*</span>'))
                            ->validationAttribute('Unit')
                            ->options(fn() => $this->getCachedUnitOptions()),
                        Select::make('dosage_foams_id')
                            ->live()
                            ->label(new HtmlString('Product Form<span class="text-red-500" style="color:red;">*</span>'))
                            ->rules(['required'])
                            ->validationAttribute('product form')
                            ->disabled()
                            ->options(fn() => $this->getCachedDosageFormOptions()),
                        TextInput::make('low_stock')
                            ->placeholder('Low Stock trigger value')
                            ->numeric()
                            ->rules(['required', 'numeric', 'max:99999999', 'gt:0'])
                            ->validationAttribute('Low Stock trigger value')
                            ->visible(function (Get $get) {
                                if ($get('in_stock') == 'yes') {
                                    return true;
                                }
                                return false;
                            })
                            ->label(function (Get $get) {
                                if($get('stock_type') == 'unit'){
                                    return new HtmlString("Low Stock trigger<span class='text-red-500' style='color:red;'>*</span>");
                                } else{
                                    return new HtmlString("Low Stock trigger value by<span class='text-red-500' style='color:red;'>*</span>");
                                }
                            })
                            ->formatStateUsing(function ($record) {
                                return null;
                            }),
                        DatePicker::make('expires_on_after')
                            ->placeholder('Select the expiry date')
                            ->label(function (Get $get) {
                                if ($get('in_stock') == 'yes' && !$get('productData.is_batch_wise_stock')) {
                                    return new HtmlString('Expiry Date<span class="text-red-500" style="color:red;">*</span>');
                                }
                                return 'Expiry Date';
                            })
                            ->visible(function (Get $get) {
                                if (!$get('productData.is_batch_wise_stock') && $get('in_stock') == 'yes') {
                                    return true;
                                }
                                return false;
                            })
                            ->validationAttribute('Expiry Date')
                            ->rules(function (Get $get) {
                                if ($get('in_stock') == 'yes' && !$get('productData.is_batch_wise_stock')) {
                                    return ['required', 'date', 'after_or_equal:today'];
                                }
                                return ['sometimes', 'date', 'after_or_equal:today'];
                            })
                            ->minDate(today())
                            ->formatStateUsing(function ($record) {
                                return null;
                            }),
                        TextInput::make('weight')
                            ->label('Weight (gms)')
                            ->disabled()
                            ->formatStateUsing(fn($get) => $this->data['weight'] ?: "-"),
                        TextInput::make('whole_sale_pack_size')
                            ->label(new HtmlString('Whole Sale Pack Size<span class="text-sm text-red-500">*</span>'))
                            ->validationAttribute('Whole Sale Pack Size')
                            ->numeric()
                            ->visible(function (Get $get) {
                                if ($get('stock_type') == 'wps') {
                                    return true;
                                }
                                return false;
                            })
                            ->rules(function (Get $get) {
                                if ($get('stock_type') == 'wps') {
                                    return ['required', 'numeric','integer', 'gt:1'];
                                }
                                return ['numeric', 'integer', 'gt:0'];
                            })
                            ->live()
                            ->prefix(function (Get $get) {
                                if (!empty($get('container.name'))) {
                                    $containerName = Str::plural($get('container.name'));
                                    return new HtmlString("<div class='text-sm font-semibold text-gray-600'>$containerName of</div>");
                                }
                                return '';
                            })
                            ->suffix(function (Get $get) {
                                $qty = $get('quantity_per_unit');
                                if (!empty($get('whole_sale_pack_size')) && $get('whole_sale_pack_size') > 0) {
                                    $qty = $qty * (int) $get('whole_sale_pack_size');
                                }
                                $foam = $this->getCachedDosageForm($get('dosage_foams_id'))?->name;
                                $foam = Str::plural($foam);
                                return new HtmlString("<div class='text-sm font-semibold text-gray-600'>{$qty} {$foam}</div>");
                            })
                    ])->columns(3),
                Section::make()
                    ->heading(fn(Get $get) => new HtmlString(
                        '<div class="flex items-center justify-between w-full">
        <span class="text-base font-bold text-gray-900">Manage Batch</span>
        <span class="text-sm font-semibold text-gray-800">Total Stock: ' . number_format(
                            collect($get('batches'))->sum(fn($batch) => (float) $batch['available_stock'] ?? 0)
                        ) . '</span>
    </div>'
                    ))
                    ->visible(function (Get $get) {
                        return $get('productData.is_batch_wise_stock') && $get('in_stock') == 'yes';
                    })
                    ->schema([
                        TableRepeater::make('batches')
                            ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                return $action->label(new HtmlString('<span class="font-bold text-blue-950">+ Add New Batch</span>'))
                                    ->extraAttributes([
                                        'style' => 'border: none !important; box-shadow: none !important;'
                                    ]);
                            })
                            ->formatStateUsing(function () {
                                return [
                                    [
                                        'batch_name' => '',
                                        'available_stock' => '',
                                        'expiry_date' => null,
                                    ]
                                ];
                            })

                            ->defaultItems(1)
                            ->reorderable(false)
                            ->addActionAlignment(Alignment::End)
                            ->headers([
                                Header::make('Batch Number'),
                                Header::make('Stock By Packaging')->label(function () {
                                    if (!empty($this->packaging)) {
                                        return new HtmlString("Stock by {$this->packaging}");
                                    }
                                    return new HtmlString('Stock by Packaging');
                                }),
                                Header::make('Expiry Date'),
                                Header::make('Action'),
                            ])
                            ->schema([
                                TextInput::make('batch_name')
                                    ->placeholder('Batch Number')
                                    ->label('Name')
                                    ->rules(function (Get $get) {
                                        $inStock = $get('../../in_stock');
                                        return $get('../../productData.is_batch_wise_stock') && $inStock == 'yes' ? ['required', Rule::unique('products_batch', 'batch_name')] : [];
                                    })
                                    ->label('Batch Name'),
                                TextInput::make('available_stock')
                                    ->live(onBlur: true)
                                    ->placeholder('Stock By Packaging')
                                    ->extraAttributes(fn() => self::numericValueValidationRule())
                                    ->rules(function (Get $get) {
                                        $inStock = $get('../../in_stock');
                                        return $get('../../productData.is_batch_wise_stock') && $inStock == 'yes' ? ['required'] : [];
                                    })
                                    ->label('Available Stock'),
                                DatePicker::make('expiry_date')
                                    ->placeholder('Select the expiry date')
                                    ->displayFormat('M d,Y')
                                    ->rules(function (Get $get) {
                                        $inStock = $get('../../in_stock');
                                        return $get('../../productData.is_batch_wise_stock') && $inStock == 'yes' ? ['required', 'date', 'after_or_equal:today'] : [];
                                    })
                                    ->minDate(today())
                                    ->placeholder('Select the expiry date')
                                    ->label('Expiry Date'),
                            ])
                    ]),
                Hidden::make('price_hidden')
                    ->live()
                    ->dehydrated(false)
                    ->formatStateUsing(fn($state) => true)
                    ->default(true),
                Section::make('Price')
                    ->visible(function (Get $get) {
                        $condition1 = Auth::user()->hasRole('Super Admin') || Auth::user()->can('pharmaceutical-suppliers_pricing');
                        $condition2 = $get('price_hidden');
                        // dd($condition1);
                        return $condition1 && $condition2;
                    })
                    ->heading(function (Get $get) {
                        $text = "Pricing";

                        $rawQty = $get('quantity_per_unit');
                        $wholeSalePackSize = $get('whole_sale_pack_size');
                        $stockType = $get('stock_type');
                        $wholeSalePackSize = $stockType == 'wps' ? $get('whole_sale_pack_size') : 1;
                        $qty = $rawQty;

                        if (!empty($rawQty)) {
                            $text .= " {$wholeSalePackSize} ";
                        }

                        if (!empty($wholeSalePackSize) && $wholeSalePackSize > 0 && !empty($rawQty)) {
                            $qty = $rawQty * (int) $wholeSalePackSize;
                        }

                        $containerName = $get('container.name');
                        if (!empty($containerName)) {
                            $text .= $containerName . ' ';
                        }

                        $foamName = $this->getCachedDosageForm($get('dosage_foams_id'))?->name;
                        $foam = !empty($foamName) ? Str::plural($foamName) : null;
                        // if($stockType == 'unit'){
                        //    $text = "Pricing - {$containerName}";
                        // }
                        if (!empty($qty) && !empty($foam)) {
                            $text .= "( {$qty} {$foam} )";
                        }
                        // dd($text);
                        return new HtmlString("<div class='font-bold text-gray-600'>{$text}</div>");
                    })
                    ->schema([
                        Radio::make('price_type')
                            ->label('Select Pricing Structure')
                            ->rules(['required'])
                            ->live()
                            ->options([
                                'fixed' => new HtmlString('Fixed &nbsp;
                                <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sets a fixed price that does not vary with quantity or duration.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>
                                '),
                    
                                    'bonus' => new HtmlString('Bonus &nbsp;
                                <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>
                                
                                
                                '),
                    
                                    'tier' => new HtmlString('Tier &nbsp;
                                <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Applies different prices based on quantity purchased — more units may cost less per unit.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>
                                '),
                            ])
                            ->formatStateUsing(function ($record) {
                                if (!empty($this->data['product_data'])) {
                                    if ($this->data['product_data']['is_bonus_pricing']) {
                                        return 'bonus';
                                    }
                                    if ($this->data['product_data']['is_tier_pricing']) {
                                        return 'tier';
                                    }
                                }
                                return 'fixed';
                            })
                            ->inline(),
                        Section::make('Fixed')
                            ->extraAttributes(['style' => 'padding:0px !important;'])
                            ->label('Fixed')
                            ->visible(fn(Get $get) => $get('price_type') === 'fixed')
                            ->extraAttributes(['style' => 'padding:0px !important;'])
                            ->schema([
                                Group::make()
                                    ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px;', 'class' => 'w-full pt-2 pb-2 pl-1 pr-1'])
                                    ->schema([
                                        PlaceHolder::make('Region'),
                                        PlaceHolder::make('price')->label("Price by"),
                                        PlaceHolder::make('admin_fess')->label('Admin Fees'),
                                        PlaceHolder::make('net_earnings')->label('Net Earnings'),
                                    ])->columns(4),
                                Group::make()
                                    ->schema([
                                        PlaceHolder::make('west_region')
                                            ->label("")
                                            ->content('West Malaysia')
                                            ->extraAttributes(['class' => 'mt-4']),
                                        TextInput::make('west_zone_price')
                                            ->placeholder('West zone price')
                                            ->validationMessages([
                                                'required' => 'The westset zone price is required',
                                                'gt' => 'The west zone price must be greater than 0.',
                                                'numeric' => 'The west zone price must be a number.',
                                            ])
                                            ->extraAttributes(fn() => self::numericValueValidationRule())
                                            ->live()
                                            ->rules(function (Get $get) {
                                                if ($get('price_type') === 'fixed') {
                                                    return ['required', 'numeric', 'gt:0'];
                                                }
                                                return [];
                                            })
                                            ->label(""),
                                        PlaceHolder::make('10')
                                            ->label("")
                                            ->content(function (Get $get) {
                                                if ($get('commission_type') == 'percentage') {
                                                    return $get('admin_fees') . "%";
                                                }
                                                return 'RM ' . $get('admin_fees');
                                            })
                                            ->extraAttributes(['class' => 'mt-3']),
                                        PlaceHolder::make('25')
                                            ->reactive()
                                            ->label("")
                                            ->content(function (Get $get) {
                                                if ($get('commission_type') == 'percentage') {
                                                    $commission = (float)$get('west_zone_price') * (float)$get('admin_fees') / 100;
                                                    $earnings = (float)$get('west_zone_price') - $commission;
                                                } else {
                                                    $earnings = (float)$get('west_zone_price') - (float)$get('admin_fees');
                                                }
                                                $earnings = $earnings < 0 ? 0 : $earnings;
                                                return 'RM ' . number_format((float)$earnings, 2);
                                            })
                                            ->extraAttributes(['class' => 'mt-3']),
                                        PlaceHolder::make('east_region')
                                            ->content('East Malaysia')
                                            ->label("")
                                            ->extraAttributes(['class' => 'mt-3']),
                                        TextInput::make('east_zone_price')
                                            ->validationMessages([
                                                'required' => 'The east zone price is required',
                                                'gt' => 'The east zone price must be greater than 0.',
                                                'numeric' => 'The east zone price must be a number.',
                                            ])
                                            ->placeholder('East zone price')
                                            ->extraAttributes(fn() => self::numericValueValidationRule())
                                            ->live()
                                            ->rules(function (Get $get) {
                                                if ($get('price_type') === 'fixed') {
                                                    return ['required', 'numeric', 'gt:0'];
                                                }
                                            })
                                            ->label(""),
                                        PlaceHolder::make('10')
                                            ->label("")
                                            ->content(function (Get $get) {
                                                if ($get('commission_type') == 'percentage') {
                                                    return $get('admin_fees') . "%";
                                                }
                                                return 'RM ' . $get('admin_fees');
                                            })
                                            ->extraAttributes(['class' => 'mt-3']),
                                        PlaceHolder::make('25')
                                            ->reactive()
                                            ->label("")
                                            ->content(function (Get $get) {
                                                if ($get('commission_type') == 'percentage') {
                                                    $commission = (float)$get('east_zone_price') * (float)$get('admin_fees') / 100;
                                                    $earnings = (float)$get('east_zone_price') - $commission;
                                                } else {
                                                    $earnings = (float)$get('east_zone_price') - (float)$get('admin_fees');
                                                }
                                                $earnings = $earnings < 0 ? 0 : $earnings;
                                                return 'RM ' . number_format((float)$earnings, 2);
                                            })
                                            ->extraAttributes(['class' => 'mt-3']),
                                    ])->columns(4),
                            ]),
                        Group::make()
                            ->visible(fn(Get $get) => $get('price_type') === 'bonus')
                            ->schema([
                                Section::make('east_malasiya')
                                    ->heading('East Malaysia')
                                    ->schema([
                                        Group::make()->schema([
                                            TextInput::make('east_zone_price')
                                                ->placeholder('250.00')
                                                ->validationAttribute('east bonus 1 base price')
                                                ->rules(function (Get $get) {
                                                    if ($get('price_type') == 'bonus') {
                                                        return ['required', 'numeric', 'gt:0'];
                                                    }
                                                    return ['numeric', 'min:0'];
                                                })
                                                ->calculateNetEarnings(
                                                    commission: $this->finalCommission,
                                                    commissionType: $this->finalCommissionType,
                                                    fieldId: 'data.east_net_earnings',
                                                    currentField: 'data.east_zone_price',
                                                )
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->rules(['required'])
                                                ->prefix('RM')
                                                ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                                            TextInput::make('east_admin_fees')
                                                ->disabled()
                                                ->formatStateUsing(function (Get $get) {
                                                    return $get('admin_fees');
                                                })
                                                ->prefix(function (Get $get) {
                                                    if ($get('commission_type') == 'percentage') {
                                                        return '%';
                                                    } else {
                                                        return 'RM';
                                                    }
                                                })
                                                ->label('Admin Fees'),
                                            TextInput::make('east_net_earnings')
                                                ->disabled()
                                                ->prefix('RM')
                                                ->label('Net Earnings'),
                                        ])->columns(3),
                                        Group::make()->schema([
                                            TextInput::make('east_bonus_1_quantity')
                                                ->placeholder('Enter quantity')
                                                ->validationAttribute('east bonus 1 quantity')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->rules(function (Get $get) {
                                                    if ($get('price_type') === 'bonus') {
                                                        return ['required', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                    }
                                                    return ['nullable', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                })
                                                ->label('Quantity'),
                                            TextInput::make('east_bonus_1_quantity_value')
                                                ->validationAttribute('east bonus 1 quantity value')
                                                ->placeholder('Enter free quantity')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->rules(function (Get $get) {
                                                    if ($get('price_type') === 'bonus') {
                                                        return ['required', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                    }
                                                    return ['nullable', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                })
                                                ->label('Bonus Quantity'),


                                            TextInput::make('east_bonus_2_quantity')
                                                ->validationAttribute('east bonus 2 quantity')
                                                ->placeholder('Enter quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Quantity'),
                                            TextInput::make('east_bonus_2_quantity_value')
                                                ->placeholder('Enter free quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->validationAttribute('east bonus 2 quantity value')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Bonus Quantity'),

                                            TextInput::make('east_bonus_3_quantity')
                                                ->validationAttribute('east bonus 3 quantity')
                                                ->placeholder('Enter quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Quantity'),
                                            TextInput::make('east_bonus_3_quantity_value')
                                                ->placeholder('Enter free quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->validationAttribute('east bonus 3 quantity value')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Bonus Quantity'),
                                        ])->columns(2),
                                    ]),
                                Section::make('west_malasiya')
                                    ->heading('West Malaysia')
                                    ->schema([
                                        Group::make()->schema([
                                            TextInput::make('west_zone_price')
                                                ->placeholder('250.00')
                                                ->calculateNetEarnings(
                                                    commission: $this->finalCommission,
                                                    commissionType: $this->finalCommissionType,
                                                    fieldId: 'data.west_net_earnings',
                                                    currentField: 'data.west_zone_price',
                                                )
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->prefix('RM')
                                                ->rules(function (Get $get) {
                                                    if ($get('price_type') === 'bonus') {
                                                        return ['required', 'numeric', 'gt:0', 'price'];
                                                    }
                                                    return ['nullable', 'numeric', 'gt:0', 'price'];
                                                })
                                                ->validationAttribute('west bonus 1 base price')
                                                ->label(new HtmlString('Base Price<span style="color:red;">*</span>')),
                                            TextInput::make('west_admin_fees')
                                                ->disabled()
                                                ->formatStateUsing(function (Get $get) {
                                                    return $get('admin_fees');
                                                })
                                                ->prefix(function (Get $get) {
                                                    if ($get('commission_type') == 'percentage') {
                                                        return '%';
                                                    } else {
                                                        return 'RM';
                                                    }
                                                })
                                                ->label('Admin Fees'),
                                            TextInput::make('west_net_earnings')
                                                ->disabled()
                                                ->prefix('RM')
                                                ->label('Net Earnings'),
                                        ])->columns(3),
                                        Group::make()->schema([
                                            TextInput::make('west_bonus_1_quantity')
                                                ->placeholder('Enter quantity')
                                                ->validationAttribute('west bonus 1 quantity')
                                                ->rules(function (Get $get) {
                                                    if ($get('price_type') == 'bonus') {
                                                        return ['required', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                    }
                                                    return ['nullable', 'numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'];
                                                })
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Quantity')
                                                ->requiredWith('west_bonus_1_base_price'),
                                            TextInput::make('west_bonus_1_quantity_value')
                                                ->placeholder('Enter free  quantity')
                                                ->rules(['numeric', 'gt:0', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->validationAttribute('west bonus 1 quantity value')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->requiredWith('west_bonus_1_base_price')
                                                ->label('Bonus Quantity'),

                                            TextInput::make('west_bonus_2_quantity')
                                                ->placeholder('Enter quantity')
                                                ->validationAttribute('west bonus 2 quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Quantity'),
                                            TextInput::make('west_bonus_2_quantity_value')
                                                ->placeholder('Enter free  quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->validationAttribute('west bonus 2 quantity value')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Bonus Quantity'),

                                            TextInput::make('west_bonus_3_quantity')
                                                ->placeholder('Enter quantity')
                                                ->validationAttribute('west bonus 3 quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Quantity'),
                                            TextInput::make('west_bonus_3_quantity_value')
                                                ->placeholder('Enter free  quantity')
                                                ->rules(['numeric', 'gt:1', 'max:999999', 'integer', 'regex:/^\d+$/'])
                                                ->validationAttribute('west bonus 3 quantity value')
                                                ->extraAttributes(fn() => self::numericValueValidationRule())
                                                ->label('Bonus Quantity'),
                                        ])->columns(2),
                                    ])
                            ]),

                        Group::make()
                            ->visible(fn(Get $get) => $get('price_type') === 'tier')

                            ->schema(function ($get) {

                                return [
                                    Section::make('East Malaysia')->schema([
                                        TableRepeater::make('pcInfo_east')
                                            ->maxItems(3)
                                            ->minItems(function (Get $get) {
                                                return $get('price_type') === 'tier' ? 1 : 0;
                                            })
                                            ->required(function (Get $get) {
                                                return $get('price_type') === 'tier';
                                            })
                                            ->validationMessages([
                                                'required' => 'The east zone tier price is required.',
                                                'min_items' => 'At least one iteration of zone tier price is required.',
                                            ])
                                            ->deletable(function (Get $get) {
                                                if (count($get('pcInfo_east')) > 1) {
                                                    return true;
                                                }
                                                return false;
                                            })
                                            ->live()
                                            ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) {

                                                    $tiers = $component->getState();
                                                    $westTiers = $get('pcInfo_west');
                                                    if (!empty($tiers)) {
                                                        unset($tiers[$arguments['item']]);
                                                        foreach ($tiers as $key => $tier) {
                                                            if (isset($tier['max_quantity'])) {
                                                                $maxQtyStr = (string)$tier['max_quantity'];

                                                                $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                            }

                                                            // Do the same for min_quantity and price
                                                            if (isset($tier['min_quantity'])) {
                                                                $minQtyStr = (string)$tier['min_quantity'];
                                                                $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                            }

                                                            if (isset($tier['price'])) {
                                                                $priceStr = (string)$tier['price'];
                                                                $tiers[$key]['price'] = (float)$priceStr;
                                                            }
                                                        }

                                                        $tierKeys = array_keys($tiers);
                                                        $tiersArray = array_values($tiers);

                                                        for ($i = 0; $i < count($tiersArray); $i++) {
                                                            $tierNumber = $i + 1;
                                                            $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                            $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);

                                                            if ($i == 0) {
                                                                $tiersArray[$i]['min_quantity'] = 1;
                                                            } else {
                                                                $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                    (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                                if ($previousMax > 0) {
                                                                    $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                                }
                                                            }
                                                        }
                                                        foreach ($westTiers as $key => $tier) {
                                                            $westTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                        }
                                                        $updatedTiers = [];
                                                        foreach ($tierKeys as $index => $key) {
                                                            if (isset($tiersArray[$index])) {
                                                                $updatedTiers[$key] = $tiersArray[$index];
                                                            }
                                                        }
                                                        $set('pcInfo_west', $westTiers);
                                                        $set('pcInfo_east', $updatedTiers);
                                                    }
                                                });
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $tiers = $get('pcInfo_east');
                                                $westTiers = $get('pcInfo_west');
                                                if (!empty($tiers)) {
                                                    // Iterate over tiers without modifying keys
                                                    $tierKeys = array_keys($tiers);
                                                    $tiersArray = array_values($tiers);

                                                    for ($i = 1; $i < count($tiersArray); $i++) {
                                                        $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                            (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                        if ($previousMax > 0) {
                                                            // Update min_quantity without changing the key
                                                            $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                                        }
                                                        $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);
                                                    }
                                                    foreach ($westTiers as $key => $tier) {
                                                        $westTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                    }
                                                    // Update the state without modifying keys
                                                    $set('pcInfo_west', $westTiers);
                                                    $set('pcInfo_east', $tiers);
                                                }
                                            })
                                            ->reorderable(false)
                                            ->headers([
                                                Header::make('Type'),
                                                Header::make('min_quantity')->label("Min Qty"),
                                                Header::make('max_qty')->label("Max Qty"),
                                                Header::make('price')->label("Price per Unit"),
                                                Header::make('admin_fees')->label("Admin Fees"),
                                                Header::make('net_earnings')->label("Net Earnings")
                                            ])
                                            ->schema([
                                                TextInput::make('type')
                                                    ->label('')
                                                    ->disabled()
                                                    ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                        // Find this item's position in the repeater
                                                        $rowUuid = $component->getStatePath();
                                                        $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                                        $tiers = $get('pcInfo_east');
                                                        if (empty($tiers)) {
                                                            return "Tier 1";
                                                        } else {
                                                            return "Tier " . count($tiers) + 1;
                                                        }

                                                        $keys = array_keys($tiers);
                                                        $position = array_search($rowUuid, $keys);

                                                        if ($position === false) return "Tier ?";
                                                        return "Tier " . ($position + 1);
                                                    }),

                                                TextInput::make('min_quantity')
                                                    ->label('')
                                                    ->formatStateUsing(fn() => 1)
                                                    ->placeholder('Min quantity')
                                                    ->numeric()
                                                    ->disabled()
                                                    ->dehydrated(true),

                                                TextInput::make('max_quantity')
                                                    ->label('')
                                                    ->placeholder('and above')
                                                    ->numeric()
                                                    ->dehydrated(true)
                                                    ->readOnly(function (Get $get, Component $component) {
                                                        $rowPath = $component->getStatePath();
                                                        $pathParts = explode('.', $rowPath);
                                                        $rowKey = $pathParts[2] ?? null;
                                                        $tiers = $get('../../pcInfo_east') ?? [];
                                                        $keys = array_keys($tiers);
                                                        $index = array_search($rowKey, $keys);
                                                        return $index === 2;
                                                    })
                                                    ->live('blur')
                                                    ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                        // When max_quantity is updated, we'll trigger the parent repeater's
                                                        // afterStateUpdated to recalculate min quantities for all rows
                                                        $tiers = $get('pcInfo_east');
                                                        $set('pcInfo_east', $tiers);
                                                    })
                                                    ->validationAttribute('Maximum quantity'),

                                                TextInput::make('price')
                                                    ->label('')
                                                    ->placeholder('Price')
                                                    ->validationAttribute('Price')
                                                    ->calculateNetEarningsForRepeater(
                                                        $commission = $this->finalCommission,
                                                        $commissionType = $this->finalCommissionType,
                                                    )
                                                    ->numeric()
                                                    ->rules(function () {
                                                        return ['required', 'numeric', 'gt:0', 'price'];
                                                    })
                                                    ->prefix('RM'),

                                                TextInput::make('admin_fees')
                                                    ->label('')
                                                    ->disabled()
                                                    ->formatStateUsing(function () use ($get) {
                                                        return (int)$get('admin_fees');
                                                    })
                                                    ->placeholder('Admin fees')
                                                    ->numeric()
                                                    ->prefix(function () {
                                                        if ($this->finalCommissionType === 'percentage') {
                                                            return '%';
                                                        }
                                                        return 'RM';
                                                    })
                                                    ->live(),

                                                TextInput::make('net_earnings')
                                                    ->label('')
                                                    ->placeholder('Net earnings')
                                                    ->reactive()
                                                    ->disabled()
                                                    ->prefix('RM')
                                                    ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                        $rowUuid = $component->getStatePath();
                                                        $lastDotPos = strrpos($rowUuid, '.');
                                                        $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                                        $tiers = $get('pcInfo_east');
                                                        if (!isset($tiers[$rowUuid])) return 0;

                                                        $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                                        $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;
                                                        if ($this->finalCommissionType == 'percentage') {
                                                            $adminFees = $price * $adminFees / 100;
                                                        }
                                                        return number_format($price - $adminFees, 2);
                                                    })
                                            ])
                                            ->defaultItems(1)
                                            ->label("")
                                            ->addActionAlignment(Alignment::End)
                                            ->addActionLabel("")
                                            ->addAction(
                                                fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                                    ->label('+ Add Tier')
                                                    ->extraAttributes([
                                                        'style' => 'border: none !important; box-shadow: none !important;'
                                                    ])
                                                    ->action(function (Get $get, Set $set, TierValidationService $validation) {
                                                        $tiers = $get('pcInfo_east');
                                                        $isValid = $validation->validateTierCompletion($tiers);
                                                        if (!$isValid) {
                                                            \Filament\Notifications\Notification::make()
                                                                ->danger()
                                                                // ->title('Invalid Tier Addition')
                                                                ->title('Please complete the current tier before adding a new one.')
                                                                ->send();
                                                            return;
                                                        }
                                                        if (empty($tiers)) {
                                                            // First tier starts at 1
                                                            $minQty = 1;
                                                        } else {
                                                            // Find the max quantity of the last tier
                                                            $lastTier = end($tiers);
                                                            $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                                        }
                                                        foreach ($tiers as $key => $tier) {

                                                            $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                        }

                                                        $set('pcInfo_east', [
                                                            ...$tiers,
                                                            [
                                                                'type' => 'Tier ' . count($tiers) + 1,
                                                                'min_quantity' => $minQty ?? 1,
                                                                'max_quantity' => null,
                                                                'price' => null,
                                                                'admin_fees' => (int)$get('admin_fees')
                                                            ]
                                                        ]);
                                                    })
                                            ),

                                    ]),

                                    Section::make('West Malaysia')->schema([
                                        TableRepeater::make('pcInfo_west')
                                            ->maxItems(3)
                                            ->live()
                                            ->minItems(function (Get $get) {
                                                return $get('price_type') === 'tier' ? 1 : 0;
                                            })
                                            ->required(function (Get $get) {
                                                return $get('price_type') === 'tier';
                                            })
                                            ->validationMessages([
                                                'required' => 'The east zone tier price is required.',
                                                'min_items' => 'At least one iteration of zone tier price is required.',
                                            ])
                                            ->deletable(function (Get $get) {
                                                if (count($get('pcInfo_west')) > 1) {
                                                    return true;
                                                }
                                                return false;
                                            })
                                            ->deleteAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                                return $action->action(function (Get $get, Set $set, array $arguments, TableRepeater $component) {

                                                    $tiers = $component->getState();
                                                    $eastTiers = $get('pcInfo_east');
                                                    if (!empty($tiers)) {
                                                        unset($tiers[$arguments['item']]);
                                                        foreach ($tiers as $key => $tier) {
                                                            if (isset($tier['max_quantity'])) {
                                                                $maxQtyStr = (string)$tier['max_quantity'];

                                                                $tiers[$key]['max_quantity'] = (int)$maxQtyStr;
                                                            }

                                                            // Do the same for min_quantity and price
                                                            if (isset($tier['min_quantity'])) {
                                                                $minQtyStr = (string)$tier['min_quantity'];
                                                                $tiers[$key]['min_quantity'] = (int)$minQtyStr;
                                                            }

                                                            if (isset($tier['price'])) {
                                                                $priceStr = (string)$tier['price'];
                                                                $tiers[$key]['price'] = (float)$priceStr;
                                                            }
                                                        }

                                                        $tierKeys = array_keys($tiers);
                                                        $tiersArray = array_values($tiers);

                                                        for ($i = 0; $i < count($tiersArray); $i++) {
                                                            $tierNumber = $i + 1;
                                                            $tiersArray[$i]['type'] = 'Tier ' . $tierNumber;
                                                            $tiersArray[$i]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);

                                                            if ($i == 0) {
                                                                $tiersArray[$i]['min_quantity'] = 1;
                                                            } else {
                                                                $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                                    (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                                if ($previousMax > 0) {
                                                                    $tiersArray[$i]['min_quantity'] = $previousMax + 1;
                                                                }
                                                            }
                                                        }
                                                        foreach ($eastTiers as $key => $tier) {
                                                            $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                        }
                                                        $updatedTiers = [];
                                                        foreach ($tierKeys as $index => $key) {
                                                            if (isset($tiersArray[$index])) {
                                                                $updatedTiers[$key] = $tiersArray[$index];
                                                            }
                                                        }
                                                        $set('pcInfo_east', $eastTiers);
                                                        $set('pcInfo_west', $updatedTiers);
                                                    }
                                                });
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $tiers = $get('pcInfo_west');
                                                $eastTiers = $get('pcInfo_east');
                                                if (!empty($tiers)) {
                                                    // Iterate over tiers without modifying keys
                                                    $tierKeys = array_keys($tiers);
                                                    $tiersArray = array_values($tiers);

                                                    for ($i = 1; $i < count($tiersArray); $i++) {
                                                        $previousMax = isset($tiersArray[$i - 1]['max_quantity']) ?
                                                            (int)$tiersArray[$i - 1]['max_quantity'] : 0;

                                                        if ($previousMax > 0) {
                                                            // Update min_quantity without changing the key
                                                            $tiers[$tierKeys[$i]]['min_quantity'] = $previousMax + 1;
                                                        }
                                                        $tiers[$tierKeys[$i]]['net_earnings'] = self::netEarnings($tiersArray[$i]['price'], $tiersArray[$i]['admin_fees'], $this->finalCommissionType);
                                                    }
                                                    foreach ($eastTiers as $key => $tier) {
                                                        $eastTiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                    }

                                                    // Update the state without modifying keys
                                                    $set('pcInfo_east', $eastTiers);
                                                    $set('pcInfo_west', $tiers);
                                                }
                                            })
                                            ->reorderable(false)
                                            ->headers([
                                                Header::make('Type'),
                                                Header::make('min_quantity')->label("Min Qty"),
                                                Header::make('max_qty')->label("Max Qty"),
                                                Header::make('price')->label("Price per Unit"),
                                                Header::make('admin_fees')->label("Admin Fees"),
                                                Header::make('net_earnings')->label("Net Earnings")
                                            ])
                                            ->schema([
                                                TextInput::make('type')
                                                    ->label('')
                                                    ->disabled()
                                                    ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                        // Find this item's position in the repeater
                                                        $rowUuid = $component->getStatePath();
                                                        $rowUuid = substr($rowUuid, strrpos($rowUuid, '.') + 1);

                                                        $tiers = $get('pcInfo_west');
                                                        if (empty($tiers)) {
                                                            return "Tier 1";
                                                        } else {
                                                            return "Tier " . count($tiers) + 1;
                                                        }

                                                        $keys = array_keys($tiers);
                                                        $position = array_search($rowUuid, $keys);

                                                        if ($position === false) return "Tier ?";
                                                        return "Tier " . ($position + 1);
                                                    }),

                                                TextInput::make('min_quantity')
                                                    ->label('')
                                                    ->formatStateUsing(fn() => 1)
                                                    ->placeholder('Min quantity')
                                                    ->numeric()
                                                    ->disabled()
                                                    ->dehydrated(true),

                                                TextInput::make('max_quantity')
                                                    ->label('')
                                                    ->placeholder('and above')
                                                    ->numeric()
                                                    ->dehydrated(true)
                                                    ->readOnly(function (Get $get, Component $component) {
                                                        $rowPath = $component->getStatePath();
                                                        $pathParts = explode('.', $rowPath);
                                                        $rowKey = $pathParts[2] ?? null;
                                                        $tiers = $get('../../pcInfo_west') ?? [];
                                                        $keys = array_keys($tiers);
                                                        $index = array_search($rowKey, $keys);
                                                        return $index === 2;
                                                    })
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(function ($state, Set $set, Get $get) {
                                                        $tiers = $get('pcInfo_west');
                                                        $set('pcInfo_west', $tiers);
                                                    })
                                                    ->validationAttribute('Maximum quantity'),

                                                TextInput::make('price')
                                                    ->label('')
                                                    ->placeholder('Price')
                                                    ->validationAttribute('Price')
                                                    ->calculateNetEarningsForRepeater(
                                                        $commission = $this->finalCommission,
                                                        $commissionType = $this->finalCommissionType,
                                                    )
                                                    ->numeric()
                                                    ->rules(function () {
                                                        return ['required', 'numeric', 'gt:0', 'price'];
                                                    })
                                                    ->prefix('RM'),

                                                TextInput::make('admin_fees')
                                                    ->label('')
                                                    ->disabled()
                                                    ->formatStateUsing(function () use ($get) {
                                                        return (int)$get('admin_fees');
                                                    })
                                                    ->placeholder('Admin fees')
                                                    ->numeric()
                                                    ->prefix(function () {
                                                        if ($this->finalCommissionType === 'percentage') {
                                                            return '%';
                                                        }
                                                        return 'RM';
                                                    }),

                                                TextInput::make('net_earnings')
                                                    ->label('')
                                                    ->placeholder('Net earnings')
                                                    ->disabled()
                                                    ->prefix('RM')
                                                    ->formatStateUsing(function ($state, Set $set, Get $get, $component) {
                                                        $rowUuid = $component->getStatePath();
                                                        $lastDotPos = strrpos($rowUuid, '.');
                                                        $rowUuid = substr($rowUuid, $lastDotPos + 1);

                                                        $tiers = $get('pcInfo_west');
                                                        if (!isset($tiers[$rowUuid])) return 0;

                                                        $price = isset($tiers[$rowUuid]['price']) ? (float)$tiers[$rowUuid]['price'] : 0;
                                                        $adminFees = isset($tiers[$rowUuid]['admin_fees']) ? (float)$tiers[$rowUuid]['admin_fees'] : 0;

                                                        return number_format($price - $adminFees, 2);
                                                    })
                                            ])
                                            ->defaultItems(1)
                                            ->label("")
                                            ->addActionAlignment(Alignment::End)
                                            ->addActionLabel("")
                                            ->addAction(
                                                fn(\Filament\Forms\Components\Actions\Action $action) => $action

                                                    ->label('+ Add Tier')
                                                    ->extraAttributes([
                                                        'style' => 'border: none !important; box-shadow: none !important;'
                                                    ])
                                                    ->action(function (Get $get, Set $set, TierValidationService $validation) {
                                                        $tiers = $get('pcInfo_west');
                                                        $isValid = $validation->validateTierCompletion($tiers);
                                                        if (!$isValid) {
                                                            Notification::make()
                                                                ->danger()
                                                                // ->title('Invalid Tier Addition')
                                                                ->title('Please complete the current tier before adding a new one.')
                                                                ->send();
                                                            return;
                                                        }
                                                        if (empty($tiers)) {
                                                            $minQty = 1;
                                                        } else {
                                                            $lastTier = end($tiers);
                                                            $minQty = isset($lastTier['max_quantity']) ? (int)$lastTier['max_quantity'] + 1 : 1;
                                                        }
                                                        foreach ($tiers as $key => $tier) {

                                                            $tiers[$key]['net_earnings'] = self::netEarnings($tier['price'], $tier['admin_fees'], $this->finalCommissionType);
                                                        }
                                                        $set('pcInfo_west', [
                                                            ...$tiers,
                                                            [
                                                                'type' => 'Tier ' . count($tiers) + 1,
                                                                'min_quantity' => $minQty ?? 1,
                                                                'max_quantity' => null,
                                                                'price' => null,
                                                                'admin_fees' => (int)$get('admin_fees')
                                                            ]
                                                        ]);
                                                    })
                                            ),

                                    ])
                                ];
                            })
                    ]),



            ])->statePath('data');
    }

    public static function netEarnings($price, $finalCommission, $finalCommissionType)
    {

        if ($finalCommissionType == 'percentage') {
            $commission = (int)$finalCommission * (int)$price / 100;
            $earning = (int)$price - (int)$commission;
        } else {
            $earning =  (int)$price - (int)$finalCommission;
            // $earning = $price - $commission;
        }

        return number_format($earning, 2);
    }

    public function submitAction(): Action
    {
        return Action::make('add')
            ->label('Add')
            ->action('createProduct');
    }

    public function cancelAction(): Action
    {
        return Action::make('cancel')
            ->outlined()
            ->action(function(){
                return redirect()->to(ProductResource::getUrl('create', ['user_id' => $this->pcId]));
            })
            ->label('Cancel');
    }

    public function createProduct()
    {
        $this->form->getState();
        $this->validate();
        $productData = $this->form->getState();
        
        $this->wholeSalePackSize = $productData['whole_sale_pack_size'] ?? 1;
        $this->stockType = $productData['stock_type'] ?? 'unit';
        
        // 🎯 Preserve important data for activity logging before they get unset
        $preservedStockData = [
            'stock_type' => $productData['stock_type'] ?? 'unit',
            'whole_sale_pack_size' => $productData['whole_sale_pack_size'] ?? 1
        ];
        
        unset($productData['stock_type']);
        unset($productData['whole_sale_pack_size']);
        $this->sku = $productData['sku'];
        $batches = $productData['batches'] ?? [];
        $exists = \App\Services\ProductRelationCacheService::hasProductRelation($this->data['id'], $this->pcId);
        if ($exists) {
            Notification::make()
                // ->title('Product already exists')
                ->title('Product already exists')
                ->danger()
                ->send();
            return;
        }
        $batchStock = 0;
        $batches = $productData['batches'] ?? [];
        $batchNames = array_column($batches, 'batch_name');
        $batchNames = array_filter($batchNames, fn($name) => is_string($name) && trim($name) !== '');

        $duplicates = array_filter(array_count_values($batchNames), fn($count) => $count > 1);
        $isDuplicateName = count($duplicates) > 0;
        if ($isDuplicateName) {
            Notification::make()
                // ->title('Duplicate Batch Name')
                ->title('Duplicate batch name found. Please check the batch names.')
                ->danger()
                ->send();
            return;
        }

        if ($productData['in_stock'] == 'yes') {
            if ($productData['productData']['is_batch_wise_stock']) {
                foreach ($batches as $key => $batch) {
                    $batchStock += $batch['available_stock'];
                }
            } else {
                $batchStock = $productData['stock'] ?? null;
            }
            if ($productData['low_stock'] > $batchStock) {
                Notification::make()
                    // ->title('Low Stock')
                    ->title('The stock is lower than the low stock value')
                    ->danger()
                    ->send();
                return;
            }
        }
        // $productData['price_type'] = !empty($productData['price_type']) ? $productData['price_type'] : null;
        if ($productData['price_type'] == 'bonus') {
            // East bonus quantity validation
            $bonusEastQuantity = [];
            for ($i = 1; $i < 4; $i++) {
                if (!empty($productData["east_bonus_{$i}_quantity"])) {
                    $bonusEastQuantity[$i] = $productData["east_bonus_{$i}_quantity"];
                }
            }

            $eastIndexes = array_keys($bonusEastQuantity);
            $expectedEast = range(1, count($bonusEastQuantity));

            if ($eastIndexes !== $expectedEast) {
                Notification::make()
                    ->body('Please enter east bonus quantities in sequential order (no gaps).')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'east_bonus_quantity' => ['Please fill East quantities in order (no skipping).'],
                ]);
            }

            $eastUnique = array_unique($bonusEastQuantity);
            if (count($bonusEastQuantity) !== count($eastUnique)) {
                Notification::make()
                    ->body('Please make sure that all east quantities are unique.')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'east_bonus_quantity' => ['East quantities must be unique.'],
                ]);
            }

            // West bonus quantity validation
            $bonusWestQuantity = [];
            for ($i = 1; $i < 4; $i++) {
                if (!empty($productData["west_bonus_{$i}_quantity"])) {
                    $bonusWestQuantity[$i] = $productData["west_bonus_{$i}_quantity"];
                }
            }

            $westIndexes = array_keys($bonusWestQuantity);
            $expectedWest = range(1, count($bonusWestQuantity));

            if ($westIndexes !== $expectedWest) {
                Notification::make()
                    ->body('Please enter west bonus quantities in sequential order (no gaps).')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'west_bonus_quantity' => ['Please fill West quantities in order (no skipping).'],
                ]);
            }

            $westUnique = array_unique($bonusWestQuantity);
            if (count($bonusWestQuantity) !== count($westUnique)) {
                Notification::make()
                    ->body('Please make sure that all west quantities are unique.')
                    ->danger()
                    ->send();

                throw ValidationException::withMessages([
                    'west_bonus_quantity' => ['West quantities must be unique.'],
                ]);
            }
        }

        // 🎯 Initialize preserved tier data
        $preservedTierData = [
            'pcInfo_east' => [],
            'pcInfo_west' => []
        ];
        
        if ($productData['price_type'] == 'tier') {
            $eastTierPriceInfo = $productData['pcInfo_east'] ?? [];
            $westTierPriceInfo = $productData['pcInfo_west'] ?? [];
            
            // 🎯 Preserve tier data for activity logging before transformation
            $preservedTierData = [
                'pcInfo_east' => $eastTierPriceInfo,
                'pcInfo_west' => $westTierPriceInfo
            ];
            
            $eastTierPrice = [];
            $westTierPrice = [];
            foreach ($eastTierPriceInfo as $key => $value) {
                $count = $key + 1;
                $eastTierPrice["east_tier_{$count}_min_quantity"] = $value['min_quantity'];
                $eastTierPrice["east_tier_{$count}_max_quantity"] = $value['max_quantity'];
                $eastTierPrice["east_tier_{$count}_base_price"] = $value['price'];
            }
            foreach ($westTierPriceInfo as $key => $value) {
                $count = $key + 1;
                $westTierPrice["west_tier_{$count}_min_quantity"] = $value['min_quantity'];
                $westTierPrice["west_tier_{$count}_max_quantity"] = $value['max_quantity'];
                $westTierPrice["west_tier_{$count}_base_price"] = $value['price'];
            }
            $productData = array_merge($productData, $eastTierPrice, $westTierPrice);
            unset($productData['pcInfo_east']);
            unset($productData['pcInfo_west']);
            
            // 🎯 Restore tier data for activity logging
            $productData['pcInfo_east'] = $preservedTierData['pcInfo_east'];
            $productData['pcInfo_west'] = $preservedTierData['pcInfo_west'];
        }
        DB::transaction(function () use ($productData, $batches, $preservedStockData, $preservedTierData, $batchStock) {
            $productRelation = ProductRelation::create([
                'product_id' => $this->data['id'],
                'price_type' => $productData['price_type'],
                'user_id' => $this->pcId,
                'admin_approval' => true,
                'requested_by' => $this->pcId,
                'quantity_per_unit' => $productData['productData']['quantity_per_unit'] ?? null,
                'unit_id' => $productData['productData']['unit_id'] ?? null,
                'dosage_foams_id' => $productData['productData']['dosage_foams_id'] ?? null,
            ]);
            $totalStock = 0;
            $productRelation->update(['sku' => $this->sku]);
            
            // Images are already associated with the main Product model, 
            // no need to copy them since this is a relation to existing product
            
            if (!empty($batches)) {
                foreach ($batches as $batch) {
                    $totalStock += $batch['available_stock'];
                    // Use create method instead of insert for proper handling
                    ProductBatch::create([
                        'products_relation_id' => $productRelation->id,
                        'user_id' => $this->pcId,
                        'product_id' => $this->data['id'],
                        'batch_name' => $batch['batch_name'],
                        'available_stock' => $batch['available_stock'],
                        'expiry_date' => $batch['expiry_date'],
                    ]);
                }
            }

            //Price as per new DB structure
            DB::table('product_relation_prices')->insert([
                'product_relation_id' => $productRelation->id,

                //tier pricing
                'east_tier_1_base_price' => $productData['east_tier_1_base_price'] ?? null,
                'west_tier_1_base_price' => $productData['west_tier_1_base_price'] ?? null,
                'east_tier_2_base_price' => $productData['east_tier_2_base_price'] ?? null,
                'west_tier_2_base_price' => $productData['west_tier_2_base_price'] ?? null,
                'east_tier_3_base_price' => $productData['east_tier_3_base_price'] ?? null,
                'west_tier_3_base_price' => $productData['west_tier_3_base_price'] ?? null,
                'east_tier_1_min_quantity' => $productData['east_tier_1_min_quantity'] ?? null,
                'west_tier_1_min_quantity' => $productData['west_tier_1_min_quantity'] ?? null,
                'east_tier_2_min_quantity' => $productData['east_tier_2_min_quantity'] ?? null,
                'west_tier_2_min_quantity' => $productData['west_tier_2_min_quantity'] ?? null,
                'east_tier_3_min_quantity' => $productData['east_tier_3_min_quantity'] ?? null,
                'west_tier_3_min_quantity' => $productData['west_tier_3_min_quantity'] ?? null,
                'east_tier_1_max_quantity' => $productData['east_tier_1_max_quantity'] ?? null,
                'west_tier_1_max_quantity' => $productData['west_tier_1_max_quantity'] ?? null,
                'east_tier_2_max_quantity' => $productData['east_tier_2_max_quantity'] ?? null,
                'west_tier_2_max_quantity' => $productData['west_tier_2_max_quantity'] ?? null,
                'east_tier_3_max_quantity' => $productData['east_tier_3_max_quantity'] ?? null,
                'west_tier_3_max_quantity' => $productData['west_tier_3_max_quantity'] ?? null,

                //bonus pricing
                'west_bonus_1_quantity' => $productData['west_bonus_1_quantity'] ?? null,
                'west_bonus_2_quantity' => $productData['west_bonus_2_quantity'] ?? null,
                'west_bonus_3_quantity' => $productData['west_bonus_3_quantity'] ?? null,
                'east_bonus_1_quantity_value' => $productData['east_bonus_1_quantity_value'] ?? null,
                'east_bonus_2_quantity_value' => $productData['east_bonus_2_quantity_value'] ?? null,
                'east_bonus_3_quantity_value' => $productData['east_bonus_3_quantity_value'] ?? null,
                'west_bonus_1_quantity_value' => $productData['west_bonus_1_quantity_value'] ?? null,
                'west_bonus_2_quantity_value' => $productData['west_bonus_2_quantity_value'] ?? null,
                'west_bonus_3_quantity_value' => $productData['west_bonus_3_quantity_value'] ?? null,
                'east_bonus_1_quantity' => $productData['east_bonus_1_quantity'] ?? null,
                'east_bonus_2_quantity' => $productData['east_bonus_2_quantity'] ?? null,
                'east_bonus_3_quantity' => $productData['east_bonus_3_quantity'] ?? null,
                'east_bonus_1_base_price' => $productData['east_bonus_1_base_price'] ?? null,
                'east_bonus_2_base_price' => $productData['east_bonus_2_base_price'] ?? null,
                'east_bonus_3_base_price' => $productData['east_bonus_3_base_price'] ?? null,
                'west_bonus_1_base_price' => $productData['west_bonus_1_base_price'] ?? null,
                'west_bonus_2_base_price' => $productData['west_bonus_2_base_price'] ?? null,
                'west_bonus_3_base_price' => $productData['west_bonus_3_base_price'] ?? null,
                'east_zone_price' => $productData['east_zone_price'] ?? null,
                'west_zone_price' => $productData['west_zone_price'] ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            DB::table('product_relation_stocks')->insert([
                'product_relation_id' => $productRelation->id,
                'is_batch_wise_stock' =>  $productData['productData']['is_batch_wise_stock'] ?? false,
                'wholesale_pack_size' => $this->wholeSalePackSize,
                'low_stock' => $productData['low_stock'] ?? null,
                'stock' => $productData['stock'] ?? null,
                'expiry_date' => !empty($productData['expires_on_after']) ? Carbon::parse($productData['expires_on_after'])->setTime(23, 59, 0) : null,
                'stock_type' => $this->stockType ?? 'unit',
                'total_stock' => $productData['productData']['is_batch_wise_stock'] ? $totalStock : ($productData['stock'] ?? null),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            
            // Process attribute data and create variants if any attributes are selected
            $product = Product::find($this->data['id']);
            
            // Log attribute processing for debugging
            \Illuminate\Support\Facades\Log::info('Processing attribute data for product', [
                'product_id' => $product->id,
                'has_variants' => $productData['has_variants'] ?? false,
                'has_attribute_data' => !empty($productData['attribute_data'])
            ]);
            
            // Validate variant configuration consistency
            $hasVariants = $productData['has_variants'] ?? false;
            $attributeData = $productData['attribute_data'] ?? null;
            
            if ($hasVariants && (empty($attributeData) || !is_array($attributeData) || empty($attributeData['attribute_ids']))) {
                \Illuminate\Support\Facades\Log::warning('Inconsistent variant configuration detected', [
                    'product_id' => $product->id,
                    'has_variants' => $hasVariants,
                    'attribute_data_empty' => empty($attributeData),
                    'message' => 'has_variants is true but no valid attribute_data provided'
                ]);
                // Reset has_variants to false to maintain consistency
                $productData['has_variants'] = false;
            }
            
            // try {
                
            //     $attributeService = new PostSaveAttributeService($this->pcId);
            //     $attributeResults = $attributeService->processAttributeData($product, $productData);
                
            //     if ($attributeResults['status'] === 'success' && $attributeResults['variants_created'] > 0) {
            //         \Illuminate\Support\Facades\Log::info('Variants created successfully', [
            //             'product_id' => $product->id,
            //             'variants_created' => $attributeResults['variants_created']
            //         ]);
            //     }
            // } catch (\Exception $e) {
            //     \Illuminate\Support\Facades\Log::error('Failed to save variants for product', [
            //         'product_id' => $product->id,
            //         'error' => $e->getMessage()
            //     ]);
            //     // Don't throw the exception - let the product creation succeed even if variants fail
            // }
            Notification::make()
                ->title('Product Created')
                ->body("$product->name has been created by admin.")
                ->info()
                ->actions([
                    \Filament\Notifications\Actions\Action::make('view')
                        ->label('View Product')
                        ->url(url(config('app.pc_url') . '/products/' . $product->id . "/view"))
                ])
                ->sendToDatabase(User::find($this->pcId));
            $name = User::find($this->pcId)->name;
            $adminName = Auth::user()->name;
            
            // 🎯 Restore preserved data for activity logging
            $productData['stock_type'] = $preservedStockData['stock_type'];
            $productData['whole_sale_pack_size'] = $preservedStockData['whole_sale_pack_size'];
            
            // 🎯 Create human-readable activity data
            $humanReadableData = [
                // Basic Product Info
                'Product Name' => Str::upper($product->name),
                'SKU' => $productData['sku'] ?? $this->sku,
                'Created By Admin' => $adminName,
                'Created For PC' => $name,
                'Admin ID' => Auth::user()->id,
                'PC ID' => $this->pcId,
                
                // Product Details (Human Readable)
                'Category' => $product->category?->name ?? 'Unknown Category',
                'Sub Category' => $product->subcategory?->name ?? 'No Sub Category',
                'Brand' => $product->brand?->name ?? 'Unknown Brand',
                'Unit' => $product->unit?->name ?? 'Unknown Unit',
                'Dosage Form' => $product->foam?->name ?? 'Unknown Form',
                'Volume Per Unit' => $product->quantity_per_unit . ' ' . ($product->unit?->name ?? 'units'),
                
                // Stock Configuration
                'Stock Management' => $productData['productData']['is_batch_wise_stock'] ? 'By Batch' : 'Without Batch',
                'Stock Type' => $productData['stock_type'] === 'wps' ? 'Wholesale Pack' : 'Unit',
                'Wholesale Pack Size' => $productData['whole_sale_pack_size'],
                'Low Stock Trigger' => $productData['low_stock'] ?? 'Not Set',
                'Total Stock' => $productData['productData']['is_batch_wise_stock'] ? $batchStock : ($productData['stock'] ?? 'Not Set'),
                
                // Pricing Structure
                'Pricing Type' => match($productData['price_type']) {
                    'fixed' => 'Fixed Pricing',
                    'bonus' => 'Bonus Pricing',
                    'tier' => 'Tier Pricing',
                    default => 'Unknown Pricing'
                },
                
                // Regional Pricing (if fixed)
                'West Malaysia Price' => $productData['price_type'] === 'fixed' ? 'RM ' . ($productData['west_zone_price'] ?? '0') : null,
                'East Malaysia Price' => $productData['price_type'] === 'fixed' ? 'RM ' . ($productData['east_zone_price'] ?? '0') : null,
                
                // Commission Info
                'Commission Type' => $this->finalCommissionType === 'percentage' ? 'Percentage' : 'Fixed Amount',
                'Commission Value' => $this->finalCommission . ($this->finalCommissionType === 'percentage' ? '%' : ' RM'),
                
                // Timestamps
                'Created At' => now()->format('d/m/Y H:i:s'),
            ];
            
            // Add batch information if batch-wise stock
            if ($productData['productData']['is_batch_wise_stock'] && !empty($batches)) {
                $batchInfo = [];
                foreach ($batches as $index => $batch) {
                    $batchInfo[] = [
                        'Batch Number' => $batch['batch_name'],
                        'Stock' => $batch['available_stock'],
                        'Expiry Date' => $batch['expiry_date'] ? Carbon::parse($batch['expiry_date'])->format('d/m/Y') : 'Not Set'
                    ];
                }
                $humanReadableData['Batches'] = $batchInfo;
                $humanReadableData['Total Batches'] = count($batchInfo);
            }
            
            // Add bonus pricing details if applicable
            if ($productData['price_type'] === 'bonus') {
                $bonusInfo = [];
                
                // East Malaysia Bonus
                for ($i = 1; $i <= 3; $i++) {
                    if (!empty($productData["east_bonus_{$i}_quantity"])) {
                        $bonusInfo['East Malaysia'][] = [
                            'Buy Quantity' => $productData["east_bonus_{$i}_quantity"],
                            'Get Free' => $productData["east_bonus_{$i}_quantity_value"] ?? 0,
                            'Description' => "Buy {$productData["east_bonus_{$i}_quantity"]}, Get {$productData["east_bonus_{$i}_quantity_value"]} Free"
                        ];
                    }
                }
                
                // West Malaysia Bonus
                for ($i = 1; $i <= 3; $i++) {
                    if (!empty($productData["west_bonus_{$i}_quantity"])) {
                        $bonusInfo['West Malaysia'][] = [
                            'Buy Quantity' => $productData["west_bonus_{$i}_quantity"],
                            'Get Free' => $productData["west_bonus_{$i}_quantity_value"] ?? 0,
                            'Description' => "Buy {$productData["west_bonus_{$i}_quantity"]}, Get {$productData["west_bonus_{$i}_quantity_value"]} Free"
                        ];
                    }
                }
                
                $humanReadableData['Bonus Pricing'] = $bonusInfo;
            }
            
            // Add tier pricing details if applicable
            if ($productData['price_type'] === 'tier') {
                $tierInfo = [
                    'East Malaysia' => [],
                    'West Malaysia' => []
                ];
                
                // Process East tiers
                if (!empty($productData['pcInfo_east'])) {
                    foreach ($productData['pcInfo_east'] as $index => $tier) {
                        $tierInfo['East Malaysia'][] = [
                            'Tier Number' => $index + 1,
                            'Quantity Range' => $tier['min_quantity'] . ' - ' . ($tier['max_quantity'] ?? 'unlimited'),
                            'Price Per Unit' => 'RM ' . $tier['price'],
                            'Description' => "Tier " . ($index + 1) . ": {$tier['min_quantity']}" . ($tier['max_quantity'] ? " to {$tier['max_quantity']}" : '+') . " units at RM {$tier['price']}"
                        ];
                    }
                }
                
                // Process West tiers
                if (!empty($productData['pcInfo_west'])) {
                    foreach ($productData['pcInfo_west'] as $index => $tier) {
                        $tierInfo['West Malaysia'][] = [
                            'Tier Number' => $index + 1,
                            'Quantity Range' => $tier['min_quantity'] . ' - ' . ($tier['max_quantity'] ?? 'unlimited'),
                            'Price Per Unit' => 'RM ' . $tier['price'],
                            'Description' => "Tier " . ($index + 1) . ": {$tier['min_quantity']}" . ($tier['max_quantity'] ? " to {$tier['max_quantity']}" : '+') . " units at RM {$tier['price']}"
                        ];
                    }
                }
                
                $humanReadableData['Tier Pricing'] = $tierInfo;
            }
            
            // 🎯 Log with human-readable data using Spatie convention
            activity()
                ->performedOn($product)
                ->causedBy(Auth::user())
                ->withProperties([
                    'old' => [], // No previous state since this is a new product creation
                    'attributes' => $humanReadableData // The new product data being created
                ])
                ->log("Product '{$product->name}' created by Admin {$adminName} for PC {$name} with {$humanReadableData['Pricing Type']} structure");
            Notification::make()
                // ->title('Product Created')
                ->title('Product has been created successfully')
                ->success()
                ->send();

            // Clear form caches after successful product creation
            // $this->clearFormCaches();

            return redirect()->to(ProductResource::getUrl('index'));
        });
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }
}
