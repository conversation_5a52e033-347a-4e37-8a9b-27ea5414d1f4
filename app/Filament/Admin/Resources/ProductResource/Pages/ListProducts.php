<?php

namespace App\Filament\Admin\Resources\ProductResource\Pages;

use App\Filament\Admin\Resources\ImportResource;
use Closure;
use Filament\Actions;
use App\Models\Product;
use Illuminate\View\View;
use Filament\Actions\Action;
use App\Models\ProductRelation;
use Livewire\Attributes\Session;
use Filament\Actions\ActionGroup;
use Filament\Actions\CreateAction;
use Illuminate\Support\Facades\Cache;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Admin\Resources\ProductResource;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    #[Session]
    public bool $isPending = false;

    #[Session]
    public bool $isAllProducts = true;

    #[Session]
    public bool $isRejectedProducts = false;

    public function mount(): void
    {
        if (request()->get('tab') == 'all-products') {
            $this->isPending = false;
            $this->isAllProducts = true;
            $this->isRejectedProducts = false;
        } elseif (request()->get('tab') == 'pending-approval') {
            $this->isPending = true;
            $this->isAllProducts = false;
            $this->isRejectedProducts = false;
        } elseif (request()->get('tab') == 'rejected') {
            $this->isPending = false;
            $this->isAllProducts = false;
            $this->isRejectedProducts = true;
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            // $this->bulkStockUpdateAction(),
            //$this->ViewImportProductsAction(),
            $this->importProductsAction(),
            $this->addProductAction(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => 'My Products',
            // 2 => 'List',
        ];
    }

    public function getHeader(): ?View
    {
        return $this->getHeaderHeading();
    }

    public function pendingApprovals()
    {
        $this->isPending = true;
        $this->isAllProducts = false;
        $this->isRejectedProducts = false;
        $this->dispatch('refreshTable');
    }

    public function allProducts()
    {
        $this->isPending = false;
        $this->isRejectedProducts = false;
        $this->isAllProducts = true;
        $this->dispatch('refreshTable');
    }

    public function rejectedProducts()
    {
        $this->isPending = false;
        $this->isAllProducts = false;
        $this->isRejectedProducts = true;
        $this->dispatch('refreshTable');
    }

    private function getHeaderHeading()
    {
        return view('custom-view.header-action', [
            'title' => 'Product Catalog',
            'headerActions' => $this->getHeaderActions(),
            'isAllProducts' => $this->isAllProducts,
            'isRejectedProducts' => $this->isRejectedProducts,
            'isPending' => $this->isPending,
            'pending' => Product::query()->pendingApprovalsForAdmin()->count(),
        ]);
    }

    protected function getTableQuery(): ?Builder
    {

        if ($this->isRejectedProducts) {
            // Rejected products
            return Product::query()
                ->where('status', 'rejected')
                ->with(['category', 'subcategory', 'productData', 'batches']);
        }

        if ($this->isPending) {
            // Pending approvals
            return Product::query()
                ->pendingApprovalsForAdmin()
                ->with(['category', 'subcategory', 'productData', 'batches']);
        }

        // Default: all products
        return Product::query()
            ->withoutRejectedForAdmin()
            ->with(['category', 'subcategory', 'productData', 'batches']);
    }

    public function importProductsAction(): Action
    {
        return Action::make('import_products')
            ->outlined()
            ->url(ImportResource::getUrl('import-products'))
            ->label('Import Products')
            ->icon('heroicon-o-arrow-up-tray');
    }




    public function ViewImportProductsAction(): Action
    {
        return Action::make('index')
            ->outlined()
            ->url(ImportResource::getUrl('index'))
            ->label('View Import Logs')
            ->icon('heroicon-o-eye');
    }

    public function bulkStockUpdateAction(): Action
    {
        return Action::make('bulk_stock_update')
            ->outlined()
            ->url(ProductResource::getUrl('product-bulk-stock-update'))
            ->label('Bulk Stock Update');
    }

    public function addProductAction(): Action
    {
        return Action::make('add_product')
            ->icon('heroicon-o-plus')
            ->url(ProductResource::getUrl('create-new'))
            ->visible(fn() => $this->isAllProducts && auth()->user()->hasRole('Super Admin') || $this->isAllProducts && auth()->user()->can('products_create'))
            ->label('Add Product');
    }
}
