<?php

namespace App\Filament\Admin\Resources\OutstandingPaymentResource\Pages;

use App\Models\ClinicDetail;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use App\Filament\Admin\Resources\ClinicResource\Pages;
use App\Filament\Admin\Resources\FullPayoutResource;
use App\Filament\Admin\Resources\FullPayoutResource\Widgets\FullPayoutItemsTable;
use App\Filament\Admin\Resources\OutstandingPaymentResource;
use App\Filament\Admin\Resources\OutstandingPaymentResource\Widgets\OutstandingFullPayoutItemsTable;
use App\Models\Payout;

class ViewOutstandingPayment extends ViewRecord
{
    protected static string $resource = OutstandingPaymentResource::class;

    // protected ?string $heading = 'Full Payouts';

    // protected static string $view = 'filament.admin.resources.clinic-resource.pages.view-clinic-onboarding-page';

    public ?array $data = [];

    public User $user;

    public Payout $payout;
    public $userAddress;
    public $clinicDetail;
    public $clinicAccountType;
    public $clinicPcDetail;

    // public function mount(int|string $record): void
    // {


    //     $this->payout = Payout::find($record);


    // }
    public function getTitle(): string
    {
        return '#' . $this->getRecord()->id ?? '-';
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Payouts History",
            $this->getResource()::getUrl('index') => "Outstanding Payments",
            3 => "Payout Details",
        ];
    }
    protected function getFooterWidgets(): array
    {
        return [
            OutstandingFullPayoutItemsTable::class,
        ];
    }

    public function getHeaderActions(): array
    {

        return [ Action::make('back')
        ->label('Back')
        ->color('gray')
        ->url(OutstandingPaymentResource::getUrl('index'))];

    }



}
