<?php

namespace App\Filament\Admin\Resources\OutstandingPaymentResource\Pages;

use App\Filament\Admin\Resources\OutstandingPaymentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOutstandingPayments extends ListRecords
{
    protected static string $resource = OutstandingPaymentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Payouts History",
            // $this->getResource()::getUrl('index') => "Outstanding Payments",
            // 3 => "List",
        ];
    }
}
