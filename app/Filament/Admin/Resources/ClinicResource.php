<?php

namespace App\Filament\Admin\Resources;

use Carbon\Carbon;
use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\ApprovalStep;
use App\Models\Order;
use App\Models\ZipCode;
use App\OnboardingStep;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Livewire\Component;
use App\Models\Approval;
use App\Models\PcDetail;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\UserAddress;
use Illuminate\Support\Arr;
use App\Models\BusinessType;
use App\Models\ClinicDetail;
use App\Models\DpharmaPoint;
use Filament\Actions\Action;
use Illuminate\Http\Request;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Illuminate\Validation\Rule;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use App\Mail\ChangesApprovedMail;
use App\Mail\ChangesRejectedMail;
use App\Models\ClinicAccountType;
use Livewire\Attributes\Reactive;
use App\Rules\PhoneWithPrefixRule;
use Illuminate\Support\HtmlString;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\Facades\Bus;
use Livewire\Component as Livewire;
use App\Models\ClinicPharmaSupplier;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Mail;
use App\Models\ClinicCertificateFile;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Filament\Support\Enums\Alignment;
use Illuminate\Validation\Rules\File;
use Filament\Notifications\Collection;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Repeater;
use Filament\Infolists\Components\Tabs;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\ViewField;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\BadgeColumn;
use App\Forms\Components\PhoneWithPrefix;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Wizard\Step;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs\Tab;
use App\Service\ClinicProfileByAdminService;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\ImageEntry;
use App\Http\Requests\ClinicLegalDocumentRequest;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Infolists\Components\Group as InfoGroup;
use App\Filament\Admin\Resources\ClinicResource\Pages;
use Filament\Infolists\Components\Section as InfoSection;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use App\Filament\Admin\Resources\ClinicResource\RelationManagers;
use Filament\Infolists\Components\Placeholder as InfoPlaceholder;

class ClinicResource extends Resource
{
    protected static ?string $model = ClinicDetail::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facility_view')
            ||  $user->can('facility_create') || $user->can('facility_update');
    }
    protected static ?string $label = 'Facilities';

    // public static function getRelations(): array
    // {
    //     return [
    //         RelationManagers\DpharmapointsRelationManager::class
    //     ];
    // }

    //     public static function infolist(Infolist $infolist): Infolist
    // {
    //     return $infolist
    //         ->schema(function ($record) {
    //             return [
    //                 Tabs::make()
    //                     ->tabs([
    //                         Tab::make('General Details')
    //                             ->schema([
    //                                 // Schema for General Details tab
    //                             ]),
    //                         Tab::make('Reward Points')
    //                             ->schema([
    //                                 // Schema for Reward Points tab
    //                             ])
    //                             ->visible(function ($record) {
    //                                 return $record->verification_status == 'approved' || $record->verification_status == null || $record->verification_status == 'pending';
    //                             }),
    //                     ])
    //                     // ->activeTab(static::$activeTab)
    //                     ->columnSpanFull(),
    //                     // ->activeTab('Reward Points'), // Set the default active tab
    //             ];
    //         });
    // }
    public static function infolist(Infolist $infolist): Infolist
    {
        $approvals = (new static())->getApprovalChanges($infolist->getRecord()->id, OnboardingStep::BASIC_INFO);
        $changes = (new static())->organizeApprovalChanges($approvals);

        $addressApprovals = (new static())->getApprovalChanges($infolist->getRecord()->id, OnboardingStep::ADDRESS);
        $billingAddressChanges = (new static())->getBillingAddressChanges($addressApprovals);


        $addressApprovals = (new static())->getShippingApprovalChanges($infolist->getRecord()->id, OnboardingStep::ADDRESS);
        $shippingAddressChanges = (new static())->getShippingAddressChanges($addressApprovals);
        // dd($shippingAddressChanges);
        $doctorApprovals = (new static())->getApprovalChanges($infolist->getRecord()->id, OnboardingStep::DOCTOR_INCHARGE);
        $doctorChanges = (new static())->organizeApprovalChanges($doctorApprovals);

        $documentsApprovals = (new static())->getApprovalChanges($infolist->getRecord()->id, 4);
        $documentChanges = (new static())->organizeDocumentChanges($documentsApprovals);
        // dd($documentChanges);
        return $infolist
            ->schema(function ($record) use ($changes, $billingAddressChanges, $shippingAddressChanges, $doctorChanges, $documentChanges) {
                $record['billingAddress'] = UserAddress::where(['user_id' => $record->id, 'address_type' => 'billing'])->first();
                $record['shippingAddress'] = UserAddress::where(['user_id' => $record->id, 'address_type' => 'shipping'])->get();
                return [
                    InfoSection::make('Facility Details')
                        ->headerActions([
                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                // ->slideOver()
                                ->icon('heroicon-o-pencil-square')
                                ->color('gray')
                                ->visible(function ($record) {
                                    $user = auth()->user();
                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                })
                                ->outlined()
                                ->button()
                                ->form(function ($record) {
                                    return [
                                        Group::make()->schema([
                                            TextInput::make('clinicData.clinic_name')
                                                ->label('Facility Name')
                                                ->autofocus(false)
                                                ->rules(function ($record) {
                                                    if (!empty($record->id)) {
                                                        return ['required', 'string', 'max:100', Rule::unique('clinic_details', 'clinic_name')->ignore($record->id, 'user_id')];
                                                    }
                                                    return ['required', 'string', 'max:100', 'unique:clinic_details,clinic_name'];
                                                })
                                                ->default($record->clinicData->clinic_name),
                                            // TextInput::make('clinicData.clinic_owner')->default($record->clinicData->clinic_owner)->rules(['required', 'string', 'max:100']),
                                            // TextInput::make('email')
                                            //     ->rules(function ($record) {
                                            //         if (!empty($record->id)) {
                                            //             return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', Rule::unique('users', 'email')->ignore($record->id, 'id')];
                                            //         }
                                            //         return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', 'unique:users,email'];
                                            //     })
                                            //     ->readOnly()
                                            //     ->default($record->email),
                                            TextInput::make('clinicData.mobile_number')
                                                ->prefix('+60')
                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Mobile number </span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Please enter your active mobile number in this format (e.g., ************). Must be 8-12 digits.`" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>
                                                </span>'))
                                                ->validationMessages([
                                                    'required' => 'The Mobile number field is required.',
                                                    'digits_between' => 'The Mobile number must be between 8 and 12 digits long.'
                                                ])
                                                ->live()
                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                ->mask('************')
                                                ->stripCharacters(['-'])
                                                ->extraAttributes([
                                                    'inputmode' => 'numeric',
                                                    'maxlength' => '12'
                                                ])
                                                ->default($record->clinicData->mobile_number)
                                                ->rules(['nullable', 'digits_between:8,12']),

                                            // Toggle::make('is_active')->default($record->is_active),
                                        ])->columns(3)
                                    ];
                                })
                                ->action(function ($record, $data, Form $form) {
                                    $form->fill();
                                    // $record->update(Arr::only($data, ['email']));
                                    User::where('id', $record->id)->update([
                                        'email' => $data['email'] ?? $record->email,
                                        'name' => $data['clinicData']['clinic_name'] ?? $record->name,
                                    ]);
                                    $record->clinicData->updateOrCreate([
                                        'user_id' => $record->id
                                    ], Arr::only($data['clinicData'], [
                                        'clinic_name',
                                        // 'clinic_owner',
                                        'mobile_number',
                                    ]));

                                    //Activity Log Start
                                    // activity()
                                    //     ->causedBy(auth()->user())
                                    //     ->useLog('clinic_edit')
                                    //     ->performedOn($record->clinicData ?? $record)
                                    //     ->withProperties([
                                    //         'old' => array_filter([
                                    //             'clinic_name' => $data['clinicData']['clinic_name'] !== ($record->clinicData?->clinic_name ?? null) ? $record->clinicData?->clinic_name : null,
                                    //             'clinic_owner' => $data['clinicData']['clinic_owner'] !== ($record->clinicData?->clinic_owner ?? null) ? $record->clinicData?->clinic_owner : null,
                                    //             'mobile_number' => $data['clinicData']['mobile_number'] !== ($record->clinicData?->mobile_number ?? null) ? $record->clinicData?->mobile_number : null,
                                    //         ], fn($value) => !is_null($value)),
                                    //         'attributes' => array_filter([
                                    //             'clinic_name' => $data['clinicData']['clinic_name'] !== ($record->clinicData?->clinic_name ?? null) ? $data['clinicData']['clinic_name'] : null,
                                    //             'clinic_owner' => $data['clinicData']['clinic_owner'] !== ($record->clinicData?->clinic_owner ?? null) ? $data['clinicData']['clinic_owner'] : null,
                                    //             'mobile_number' => $data['clinicData']['mobile_number'] !== ($record->clinicData?->mobile_number ?? null) ? $data['clinicData']['mobile_number'] : null,
                                    //         ], fn($value) => !is_null($value)),
                                    //     ])
                                    //     ->log("Facility '{$record->clinicData?->clinic_name}' has been updated");
                                    //Activity Log End
                                }),
                            // \Filament\Infolists\Components\Actions\Action::make('accept')
                            //     ->color('success')
                            //     ->outlined()
                            //     ->button()
                            //     ->requiresConfirmation()
                            //     ->visible(fn ($record) => Approval::pendingForSteps(OnboardingStep::BASIC_INFO)->where('approvalable_id', $record->id)->exists())
                            //     ->modalHeading('Confirm Approval')
                            //     ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                            //     ->modalSubmitActionLabel('Confirm')
                            //     ->action(function ($record) {
                            //         $userId = $record->id;
                            //         $steps = 1;

                            //         $approvalData = Approval::where('approvalable_id', $userId)
                            //             ->where('approvalable_type', 'App\Models\User')
                            //             ->where('steps', $steps)
                            //             ->where('approved_by', null)
                            //             ->where('approved_at', null)
                            //             ->where('rejected_at', null)
                            //             ->where('rejected_by', null)
                            //             ->latest()
                            //             ->first();

                            //         if ($approvalData) {
                            //             $newData = json_decode($approvalData->new_data, true);

                            //             // Update clinic_details table
                            //             $record->clinicData->updateOrCreate(
                            //                 ['user_id' => $userId],
                            //                 Arr::only($newData, [
                            //                     'clinic_name',
                            //                     'clinic_owner',
                            //                     'mobile_number',
                            //                 ])
                            //             );

                            //             $approvalData->update([
                            //                 'approved_at' => now(),
                            //                 'approved_by' => auth()->id(),
                            //             ]);
                            //         }

                            //         // Update verification status
                            //         // $record->update(['verification_status' => 'approved']);

                            //         Notification::make()
                            //             ->title('Changes Approved Successfully')
                            //             ->success()
                            //             ->send();
                            //     }),
                            // \Filament\Infolists\Components\Actions\Action::make('reject')
                            //     ->color('danger')
                            //     ->outlined()
                            //     ->button()
                            //     ->requiresConfirmation()
                            //     ->visible(fn ($record) => Approval::pendingForSteps(OnboardingStep::BASIC_INFO)->where('approvalable_id', $record->id)->exists())
                            //     ->modalHeading('Confirm Rejection')
                            //     ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                            //     ->modalSubmitActionLabel('Confirm')
                            //     ->action(function ($record) {
                            //         $userId = $record->id;
                            //         $steps = 1;

                            //         $approvalData = Approval::where('approvalable_id', $userId)
                            //             ->where('approvalable_type', 'App\Models\User')
                            //             ->where('steps', $steps)
                            //             ->where('approved_by', null)
                            //             ->where('approved_at', null)
                            //             ->where('rejected_at', null)
                            //             ->where('rejected_by', null)
                            //             ->latest()
                            //             ->first();

                            //         if ($approvalData) {
                            //             $approvalData->update([
                            //                 'rejected_at' => now(),
                            //                 'rejected_by' => auth()->id(),
                            //             ]);
                            //             Notification::make()
                            //             ->title('Changes Rejected Successfully')
                            //             ->success()
                            //             ->send();
                            //         } else {
                            //             Notification::make()
                            //             ->title('No Data Found For Rejection')
                            //             ->danger()
                            //             ->send();
                            //         }

                            //     }),
                        ])
                        ->schema([
                            \Filament\Infolists\Components\Grid::make([
                                'default' => 5,
                                'sm' => 5,
                            ])
                                ->columnSpan('full')
                                ->extraAttributes(['class' => 'gap-0'])
                                ->schema([
                                    ImageEntry::make('company_image')
                                        ->columnSpan(1)
                                        ->label('')
                                        ->default($record->photo ? Storage::disk('s3')->url('users/' . $record->photo) : asset('/images/user-avatar.png'))
                                        ->circular(),

                                    \Filament\Infolists\Components\Grid::make([
                                        'default' => 4,
                                        'sm' => 4,
                                    ])
                                        ->columnSpan(4)
                                        ->schema([
                                            TextEntry::make('clinicData.clinic_name')
                                                ->formatStateUsing(function ($state, $record) use ($changes) {
                                                    $output = " $state <br>";
                                                    if (isset($changes['clinic_name'])) {
                                                        foreach ($changes['clinic_name'] as $change) {
                                                            $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                        }
                                                    }

                                                    return new HtmlString($output);
                                                })
                                                ->label('Facility Name'),
                                            // TextEntry::make('clinicData.clinic_owner')
                                            //     ->label('Owner'),
                                            TextEntry::make('email')
                                                ->label('Email')
                                                ->icon('heroicon-m-envelope'),
                                            TextEntry::make('clinicData.mobile_number')
                                                ->label('Phone Number')
                                                ->prefix('+60 ')
                                                ->icon('heroicon-m-phone'),
                                            TextEntry::make('created_at')
                                                ->label('Created On')
                                                ->dateTime('M d, Y | H:i'),
                                            // TextEntry::make('clinicData.status')
                                            //     ->label('Status')
                                            //     ->badge()
                                            //     ->formatStateUsing(fn (bool $state): string =>  $state ? 'Active' : 'Inactive')
                                            //     ->color(fn (bool $state): string => $state ? 'success' : 'danger'),
                                            TextEntry::make('verification_status')
                                                ->label('Status')
                                                ->badge()
                                                ->icon(fn($state): string => match ($state) {
                                                    'approved' => 'heroicon-s-check-circle',
                                                    'rejected' => 'heroicon-s-x-circle',
                                                    'pending' => 'heroicon-s-clock',
                                                    default => 'heroicon-s-check-circle',
                                                })
                                                ->formatStateUsing(fn($state): string => match ($state) {
                                                    'send_for_approval' => 'Pending',
                                                    'approved' => 'Accepted',
                                                    default => ucfirst($state),
                                                })
                                                ->color(function ($state) {
                                                    $st = match ($state) {
                                                        'pending' => 'warning',
                                                        'approved' => 'success',
                                                        'rejected' => 'danger',
                                                        default => 'warning',
                                                    };
                                                    return $st;
                                                }),
                                            TextEntry::make('clinicData.tier')
                                                ->label('Tier')
                                                ->badge()
                                                ->formatStateUsing(fn(string $state): string =>  !empty($state) ? ucfirst($state) : '-')
                                                ->color(fn(string $state): string => match ($state) {
                                                    'gold' => 'warning',
                                                    'silver' => 'primary',
                                                    'bronze' => 'danger',
                                                    default => 'default',
                                                })
                                                ->icon(fn(string $state): string => match ($state) {
                                                    'gold' => 'heroicon-s-currency-dollar',
                                                    'silver' => 'heroicon-s-currency-dollar',
                                                    'bronze' => 'heroicon-s-currency-dollar',
                                                    default => 'heroicon-s-currency-dollar',
                                                }),
                                            TextEntry::make('admin_verified_on')
                                                ->label('Approved On')
                                                ->formatStateUsing(function ($state): string {
                                                    if (empty($state)) {
                                                        return '-';
                                                    }

                                                    $userTimezone = auth()->user()->timezone;
                                                    $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                                                    return $convertedDate->format('M d, Y | H:i');
                                                }),

                                        ])
                                ])
                        ]),

                    ViewEntry::make('clinic-resource-relation-manager')
                        ->view('filament.admin.resources.clinic-resource.pages.relation-manager')
                        ->columnSpanFull(),
                    Tabs::make()
                        ->tabs([
                            Tab::make('General Details')
                                ->schema([
                                    InfoSection::make('Basic Details')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                // ->slideOver()
                                                ->icon('heroicon-o-pencil-square')
                                                ->color('gray')
                                                ->visible(function ($record) {
                                                    $user = auth()->user();
                                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                })

                                                ->outlined()
                                                ->button()
                                                // \Filament\Infolists\Components\Actions\Action::make('accept')
                                                // ->color('success')
                                                // ->outlined()
                                                // ->button(),
                                                // \Filament\Infolists\Components\Actions\Action::make('reject')
                                                // ->color('danger')
                                                // ->outlined()
                                                // ->button()
                                                ->form(function ($record) {
                                                    $clinicData = $record->clinicData?->toArray();
                                                    // dd($clinicData);
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('email')
                                                                ->rules(function ($record) {
                                                                    if (!empty($record->id)) {
                                                                        return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', Rule::unique('users', 'email')->ignore($record->id, 'id')];
                                                                    }
                                                                    return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', 'unique:users,email'];
                                                                })
                                                                ->readOnly()
                                                                ->default($record->email),


                                                            TextInput::make('clinicData.clinic_name')
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Facility Name</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                                                ->placeholder('Enter facility name')
                                                                ->rules(['required'])
                                                                ->validationMessages([
                                                                    'required' => 'The facility name field is required.',
                                                                ])->default($record->clinicData->clinic_name),
                                                            TextInput::make('clinicData.clinic_number')
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Registration Number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                                                ->placeholder('Enter registration number')
                                                                ->autofocus(false)
                                                                ->validationMessages([
                                                                    'required' => 'The registration number field is required.',
                                                                ])
                                                                ->rules(function ($record) {
                                                                    if (!empty($record->id)) {
                                                                        return ['required', 'string', 'max:100', Rule::unique('clinic_details', 'clinic_number')->ignore($record->id, 'user_id')];
                                                                    }
                                                                    return ['required', 'string', 'max:100', 'unique:clinic_details,clinic_number'];
                                                                })
                                                                ->default($record->clinicData->clinic_number),

                                                            TextInput::make('clinicData.mobile_number')
                                                                ->prefix('+60')->placeholder('Enter facility mobile number')
                                                                ->default($record->clinicData->mobile_number)
                                                                ->live()
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Mobile Number</span> '))
                                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                                ->mask('************')
                                                                ->stripCharacters(['-'])
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '12'
                                                                ])
                                                                ->rules(['nullable', 'digits_between:8,12']),
                                                            // TextInput::make('clinicData.landline_number')->prefix('+03')->label('Facility Landline Number')
                                                            //     ->rules(['digits_between:7,8'])->placeholder('Enter facility landline number')
                                                            //     ->mask('9-9999999')
                                                            //     ->stripCharacters(['-'])
                                                            //     ->extraAttributes([
                                                            //         'inputmode' => 'numeric',
                                                            //         'maxlength' => '8'
                                                            //     ])
                                                            //     ->default($record->clinicData->landline_number),

                                                            PhoneWithPrefix::make('landline_number')
                                                                ->label("Landline Number")
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '8'
                                                                ])
                                                                ->default(function (Get $get) use ($clinicData) {
                                                                    if (!empty($clinicData)) {
                                                                        $prefix = $clinicData["landline_code"] ?? null;
                                                                        $number = $clinicData["landline_number"] ?? null;
                                                                    } else {
                                                                        $prefix = $get('addresses')[0]["landline_code"] ?? null;
                                                                        $number = $get('addresses')[0]["landline_number"] ?? null;
                                                                    }

                                                                    return [
                                                                        "prefix" => $prefix ?? '', // can fallback to first City landline code if needed
                                                                        "number" => $number,
                                                                    ];
                                                                })
                                                                ->prefixOptions(function ($get, $set) {
                                                                    if (empty($get('landline_number'))) {
                                                                        return [];
                                                                    }
                                                                    $query = City::whereNotNull('landline_code')
                                                                        ->where('landline_code', '!=', '');
                                                                    $stateId = $get('state_id');
                                                                    $cityId = $get('city_id');
                                                                    if ($stateId) {
                                                                        $query->where('state_id', $stateId);

                                                                        if ($cityId) {
                                                                            $query->where('id', $cityId);
                                                                        }
                                                                    }


                                                                    $data = $query
                                                                        ->distinct('landline_code')
                                                                        ->pluck('landline_code', 'landline_code')
                                                                        ->toArray();
                                                                    if (empty($data)) {
                                                                        $data = City::whereNotNull('landline_code')
                                                                            ->where('landline_code', '!=', '')
                                                                            ->distinct('landline_code')
                                                                            ->pluck('landline_code', 'landline_code')
                                                                            ->toArray();
                                                                    }
                                                                    // FacadesLog::info($get('addresses'));
                                                                    if (isset($get('addresses')["landline_code"]) && $get('addresses')["landline_code"] != null) {
                                                                        $set('landline_code.prefix', $get("addresses")["landline_code"]);
                                                                    }
                                                                    return $data;
                                                                })
                                                                ->rules([new PhoneWithPrefixRule()])
                                                                ->afterStateHydrated(function (Get $get, Set $set) {
                                                                    $set("landline_code", implode(" ", $get("landline_number")));
                                                                })
                                                                ->afterStateUpdated(function (Get $get, Set $set) {
                                                                    $set("landline_code", implode(" ", $get("landline_number")));
                                                                })
                                                                ->formatStateUsing(function ($state) use ($clinicData) {

                                                                    if (is_array($clinicData)) {
                                                                        if (isset($clinicData['landline_code'])) {
                                                                            $data['prefix'] = $clinicData['landline_code'];
                                                                        }
                                                                        if (isset($clinicData['landline_number'])) {
                                                                            $data['number'] = $clinicData['landline_number'];
                                                                        }
                                                                    }
                                                                    // Or if $address is an object
                                                                    elseif (is_object($clinicData)) {
                                                                        if (isset($clinicData->landline_code)) {
                                                                            $data['prefix'] = $clinicData->landline_code;
                                                                        }
                                                                        if (isset($clinicData->landline_number)) {
                                                                            $data['number'] = $clinicData->landline_number;
                                                                        }
                                                                    }
                                                                    // dd($data);
                                                                    return is_array($state) ? $state : $data;
                                                                })
                                                                ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                                            // Select::make('clinicData.clinic_account_type_id')
                                                            //     ->disabled()
                                                            //     ->label('Facility Type')->placeholder('Select facility type')
                                                            //     ->options(ClinicAccountType::where('status', true)->pluck('name', 'id')->toArray())
                                                            //     ->rules(['required'])->default($record->clinicData->clinic_account_type_id),
                                                            Select::make('clinicData.business_type_id')
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Type of Business <span class="text-danger-600 dark:text-danger-400 font-medium">*</span></span>'))
                                                                ->placeholder('Select business type')
                                                                ->options(BusinessType::where('status', true)->pluck('name', 'id')->toArray())
                                                                ->rules(['required'])
                                                                ->validationMessages([
                                                                    'required' => 'The business type field is required.',
                                                                ])
                                                                ->default($record->clinicData->business_type_id)
                                                                ->reactive(),

                                                            TextInput::make('clinicData.company_name')
                                                                ->label(function (Get $get) use ($record) {
                                                                    $businessTypeId = $get('clinicData.business_type_id') ?? $record->clinicData->business_type_id ?? null;
                                                                    $businessType = $businessTypeId ? BusinessType::find($businessTypeId) : null;
                                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                                    return new HtmlString(
                                                                        "Company Name" . (!$isSoleProprietary ? " <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>" : "")
                                                                    );
                                                                })
                                                                ->placeholder('Enter company name')
                                                                ->rules(function (Get $get) use ($record) {
                                                                    $businessTypeId = $get('clinicData.business_type_id') ?? $record->clinicData->business_type_id ?? null;
                                                                    $businessType = $businessTypeId ? BusinessType::find($businessTypeId) : null;
                                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                                    if ($isSoleProprietary) {
                                                                        return ['nullable', 'string', 'max:50'];
                                                                    }
                                                                    if (!empty($get('user_id')) && $get('clinicData.company_name') != $record->clinicData->company_name) {
                                                                        return ['required', 'string', 'max:50', Rule::unique('clinic_details', 'company_name')->ignore($get('user_id'), 'user_id')];
                                                                    }
                                                                    return ['required', 'string', 'max:50'];
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The Company Name field is required.',
                                                                    'max' => 'The Company Name may not be greater than 50 characters.',
                                                                    'string' => 'The Company Name must be a string.',
                                                                    'unique' => 'The Company Name has already been taken.',
                                                                ])
                                                                ->default($record->clinicData->company_name)
                                                                ->reactive(),

                                                            TextInput::make('clinicData.company_number')
                                                                ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->live()
                                                                ->label(function (Get $get) use ($record) {
                                                                    $businessTypeId = $get('clinicData.business_type_id') ?? $record->clinicData->business_type_id ?? null;
                                                                    $businessType = $businessTypeId ? BusinessType::find($businessTypeId) : null;
                                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                                    // return new HtmlString(
                                                                    //     '<span style="font-size: 14px !important;">Company Registration Number' .
                                                                    //     (!$isSoleProprietary ? ' <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>' : '') .
                                                                    //     '</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                                    //         <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    //             <circle cx="12" cy="12" r="10"></circle>
                                                                    //             <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                    //             <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                    //         </svg>
                                                                    //     </span>'
                                                                    // );

                                                                    return new HtmlString(
                                                                        'Company Registration Number ' .
                                                                            (!$isSoleProprietary ? ' <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>' : '') .
                                                                            '<svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                    );
                                                                })
                                                                ->placeholder('Enter company registration number')
                                                                ->rules(function (Get $get) use ($record) {
                                                                    $businessTypeId = $get('clinicData.business_type_id') ?? $record->clinicData->business_type_id ?? null;
                                                                    $businessType = $businessTypeId ? BusinessType::find($businessTypeId) : null;
                                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                                    if ($isSoleProprietary) {
                                                                        return ['nullable', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                                                    }
                                                                    return ['required', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                                                })

                                                                ->validationMessages([
                                                                    'required' => 'The Company Registration Number field is required.',
                                                                    'regex' => 'The Company Registration Number must be alphanumeric.',
                                                                    'max' => 'The Company Registration Number may not be greater than 20 characters.',
                                                                    'min' => 'The Company Registration Number must be at least 2 characters.',
                                                                ])
                                                                ->default($record->clinicData->company_number)
                                                                ->reactive(),
                                                            // TextInput::make('clinicData.clinic_owner')->label(new HtmlString('<span style="font-size: 14px !important;">Facility Owner</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))->placeholder('Enter facility owner')
                                                            //     ->rules(['required'])
                                                            //     ->validationMessages([
                                                            //         'required' => 'The facility owner field is required.',
                                                            //     ])->default($record->clinicData->clinic_owner),

                                                            Select::make('clinicData.clinic_year')
                                                                ->label('Establishment Year')->placeholder('Select commencement year')
                                                                ->options(self::getYearOptions())
                                                                ->searchable()
                                                                ->rules(['required'])->required()->default($record->clinicData->clinic_year),
                                                            TextInput::make('clinicData.tin_number')
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->live()
                                                                ->label(new HtmlString(
                                                                    'TIN Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Tax Identification Number (TIN) is a unique identifier assigned by the tax authority for businesses or individuals to track tax obligations.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                                                ))
                                                                ->placeholder('Enter TIN Number')
                                                                ->rules(['required', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])->validationMessages([
                                                                    'max' => 'The TIN Number field must not be greater than 20 characters.',
                                                                    'regex' => 'The TIN Number must only can  contain numbers and letters.',
                                                                    'min' => 'The TIN Number field must be at least 1 characters.',
                                                                    'required' => 'The TIN Number field is required.',

                                                                ])->default($record->clinicData->tin_number),
                                                            TextInput::make('clinicData.sst_number')
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->live()
                                                                ->label(new HtmlString(
                                                                    'SST Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sales and Services Tax (SST) Registration Number is a unique code issued to businesses in Malaysia registered for SST compliance.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->placeholder('Enter SST registration number')->rules(['nullable', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])
                                                                ->validationMessages([
                                                                    'required' => 'The SST registration number field is required.',
                                                                    'max' => 'The SST registration number field must not be greater than 20 characters.',
                                                                    'regex' => 'The SST registration number must only can  contain numbers and letters.',
                                                                    'min' => 'The SST registration number field must be at least 1 characters.',
                                                                ])->default($record->clinicData->sst_number)

                                                            // Toggle::make('is_active')->default($record->is_active),
                                                        ])->columns(3)
                                                    ];
                                                })
                                                ->action(function ($record, $data, Form $form) {
                                                    $landlineNumber = $data['landline_number']['number'] ?? null;
                                                    $landlineCode = $data['landline_number']['prefix'] ?? null;
                                                    $data['clinicData']['landline_number'] = $landlineNumber;
                                                    $data['clinicData']['landline_code'] = $landlineCode;
                                                    $form->fill();
                                                    // $record->update(Arr::only($data, ['name', 'is_active', 'email', 'phone']));

                                                    //Activity Log start
                                                    // $oldValues = [
                                                    //     'email' => $record->email,
                                                    //     'business_type_id' =>  BusinessType::find($record->clinicData?->business_type_id)->name,
                                                    //     'clinic_name' => $record->clinicData?->clinic_name,
                                                    //     'clinic_number' => $record->clinicData?->clinic_number,
                                                    //     'mobile_number' => $record->clinicData?->mobile_number,
                                                    //     'landline_number' => $record->clinicData?->landline_number,
                                                    //     'landline_code' => $record->clinicData?->landline_code,
                                                    //     'company_name' => $record->clinicData?->company_name,
                                                    //     'company_number' => $record->clinicData?->company_number,
                                                    //     'clinic_owner' => $record->clinicData?->clinic_owner,
                                                    //     'clinic_year' => $record->clinicData?->clinic_year,
                                                    //     'tin_number' => $record->clinicData?->tin_number,
                                                    //     'sst_number' => $record->clinicData?->sst_number,
                                                    // ];
                                                    //Activity Log End

                                                    $record->clinicData->updateOrCreate([
                                                        'user_id' => $record->id
                                                    ], Arr::only($data['clinicData'], [
                                                        'company_name',
                                                        'company_number',
                                                        // 'clinic_owner',
                                                        'clinic_year',
                                                        'tin_number',
                                                        'sst_number',
                                                        'mobile_number',
                                                        'landline_number',
                                                        'landline_code',
                                                        'clinic_number',
                                                        'clinic_account_type_id',
                                                        'business_type_id',
                                                        'clinic_name'
                                                    ]));
                                                    // Check if clinic_name is set and not empty before updating
                                                    if (!empty($data['clinicData']['clinic_name'])) {
                                                        User::where('id', $record->id)->update([
                                                            'name' => $data['clinicData']['clinic_name'],
                                                        ]);
                                                    }

                                                    //Activity Log Start
                                                    // $newValues = [
                                                    //     'email' => $data['email'],
                                                    //     'business_type_id' => BusinessType::find($data['clinicData']['business_type_id'])?->name,
                                                    //     'clinic_name' => $data['clinicData']['clinic_name'],
                                                    //     'clinic_number' => $data['clinicData']['clinic_number'],
                                                    //     'mobile_number' => $data['clinicData']['mobile_number'],
                                                    //     'landline_number' => $data['clinicData']['landline_number'],
                                                    //     'landline_code' => $data['clinicData']['landline_code'],
                                                    //     'company_name' => $data['clinicData']['company_name'],
                                                    //     'company_number' => $data['clinicData']['company_number'],
                                                    //     'clinic_owner' => $data['clinicData']['clinic_owner'],
                                                    //     'clinic_year' => $data['clinicData']['clinic_year'],
                                                    //     'tin_number' => $data['clinicData']['tin_number'],
                                                    //     'sst_number' => $data['clinicData']['sst_number'],
                                                    // ];

                                                    // $changedOld = [];
                                                    // $changedNew = [];
                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     if ($oldValue != $newValues[$key]) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValues[$key];
                                                    //     }
                                                    // }

                                                    // if (!empty($changedOld)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('clinic_edit')
                                                    //         ->performedOn($record->clinicData ?? $record)
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Facility '{$record->clinicData?->clinic_name}' has been updated");
                                                    // }
                                                    //Activity Log End
                                                }),

                                            static::makeAcceptAction(OnboardingStep::BASIC_INFO->value),
                                            static::makeRejectAction(OnboardingStep::BASIC_INFO->value),
                                            // \Filament\Infolists\Components\Actions\Action::make('accept')
                                            //     ->color('success')
                                            //     ->label('Accept Basic/Facility Details')
                                            //     ->outlined()
                                            //     ->button()
                                            //     ->requiresConfirmation()
                                            //     ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::BASIC_INFO)->where('approvalable_id', $record->id)->exists())
                                            //     ->modalHeading('Confirm Approval')
                                            //     ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                                            //     ->modalSubmitActionLabel('Confirm')
                                            //     ->action(function ($record) {
                                            //         $userId = $record->id;
                                            //         $steps = 1;

                                            //         $approvalData = Approval::where('approvalable_id', $userId)
                                            //             ->where('approvalable_type', 'App\Models\User')
                                            //             ->where('steps', $steps)
                                            //             ->where('approved_by', null)
                                            //             ->where('approved_at', null)
                                            //             ->where('rejected_at', null)
                                            //             ->where('rejected_by', null)
                                            //             ->latest()
                                            //             ->first();

                                            //         if ($approvalData) {
                                            //             $newData = json_decode($approvalData->new_data, true);

                                            //             // Update clinic_details table
                                            //             $record->clinicData->updateOrCreate(
                                            //                 ['user_id' => $userId],
                                            //                 Arr::only($newData, [
                                            //                     'company_name',
                                            //                     'company_number',
                                            //                     'clinic_owner',
                                            //                     'clinic_year',
                                            //                     'tin_number',
                                            //                     'sst_number',
                                            //                     'mobile_number',
                                            //                     'landline_number',
                                            //                     'clinic_number',
                                            //                     'clinic_account_type_id',
                                            //                     'clinic_name',
                                            //                     'clinic_owner',
                                            //                     'mobile_number',
                                            //                 ])
                                            //             );

                                            //             // Update approvals table with approved_at and approved_by
                                            //             $approvalData->where('approvalable_id', $userId)->where('steps', $steps)->update([
                                            //                 'approved_at' => now(),
                                            //                 'approved_by' => auth()->id(), // Assuming you're using authentication
                                            //             ]);
                                            //         }
                                            //         Mail::to($record->email)->send(new ChangesApprovedMail($record));
                                            //         // Update verification status
                                            //         // $record->update(['verification_status' => 'approved']);

                                            //         Notification::make()
                                            //             ->title('Changes Approved Successfully')
                                            //             ->success()
                                            //             ->send();
                                            //     }),
                                            // \Filament\Infolists\Components\Actions\Action::make('reject')
                                            //     ->color('danger')
                                            //     ->outlined()
                                            //     ->label('Reject Basic/Facility Details')
                                            //     ->button()
                                            //     ->requiresConfirmation()
                                            //     ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::BASIC_INFO)->where('approvalable_id', $record->id)->exists())
                                            //     ->modalHeading('Confirm Rejection')
                                            //     ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                                            //     ->modalSubmitActionLabel('Confirm')
                                            //     ->action(function ($record) {
                                            //         $userId = $record->id;
                                            //         $steps = 1;

                                            //         $approvalData = Approval::where('approvalable_id', $userId)
                                            //             ->where('approvalable_type', 'App\Models\User')
                                            //             ->where('steps', $steps)
                                            //             ->where('approved_by', null)
                                            //             ->where('approved_at', null)
                                            //             ->where('rejected_at', null)
                                            //             ->where('rejected_by', null)
                                            //             ->latest()
                                            //             ->first();

                                            //         if ($approvalData) {
                                            //             $approvalData->update([
                                            //                 'rejected_at' => now(),
                                            //                 'rejected_by' => auth()->id(),
                                            //             ]);
                                            //             Mail::to($record->email)->send(new ChangesRejectedMail($record));
                                            //             Notification::make()
                                            //                 ->title('Changes Rejected Successfully')
                                            //                 ->success()
                                            //                 ->send();
                                            //         } else {
                                            //             Notification::make()
                                            //                 ->title('No Data Found For Rejection')
                                            //                 ->danger()
                                            //                 ->send();
                                            //         }
                                            //     }),
                                        ])
                                        ->schema([
                                            \Filament\Infolists\Components\Grid::make([
                                                'default' => 4,
                                                'sm' => 4,
                                            ])
                                                ->columnSpan('full')
                                                ->extraAttributes(['class' => 'gap-0'])
                                                ->schema([
                                                    TextEntry::make('clinicData.email')
                                                        ->label('Email')
                                                        ->default($record->email)
                                                        ->icon('heroicon-m-envelope'),

                                                    TextEntry::make('clinicData.businessName.name')
                                                        ->label('Type of Business')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";
                                                            if (isset($changes['business_type_id'])) {
                                                                foreach ($changes['business_type_id'] as $change) {
                                                                    $businessName = BusinessType::find($change['new_value'])?->name;
                                                                    if ($businessName) {
                                                                        $output .= "<span style='background-color: yellow;'>{$businessName}</span><br>";
                                                                    }
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    TextEntry::make('clinicData.clinic_name')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";
                                                            if (isset($changes['clinic_name'])) {
                                                                foreach ($changes['clinic_name'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Facility Name'),
                                                    TextEntry::make('clinicData.clinic_number')
                                                        ->label('Registration Number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['clinic_number'])) {
                                                                foreach ($changes['clinic_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    TextEntry::make('clinicData.mobile_number')
                                                        ->prefix('+60 ')
                                                        ->label('Mobile Number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            // $state = substr($state, 0, 2) . '-' . substr($state, 2);
                                                            $output = " $state <br>";

                                                            if (isset($changes['mobile_number'])) {
                                                                foreach ($changes['mobile_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    TextEntry::make('clinicData.landline_number')
                                                        // ->prefix($record->clinicData->landline_code ? "+{$record->clinicData->landline_code} " : '+03 ')
                                                        ->label('Landline Number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $currentValue = ($record->clinicData->landline_code ? '+' . $record->clinicData->landline_code . ' ' : '') .
                                                                ($state ?? '-');

                                                            $landlineChanges = [
                                                                'number' => $changes['landline_number'] ?? null,
                                                                'code' => $changes['landline_code'] ?? null
                                                            ];

                                                            return static::formatLandLinePendingChanges(
                                                                $currentValue,
                                                                $landlineChanges
                                                            );
                                                        }),
                                                    // ->formatStateUsing(function ($state, $record) use ($changes) {
                                                    //     // $state = substr($state, 0, 1) . '-' . substr($state, 1);
                                                    //     $output = " $state <br>";

                                                    //     if (isset($changes['landline_number'])) {
                                                    //         foreach ($changes['landline_number'] as $change) {
                                                    //             $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                    //         }
                                                    //     }

                                                    //     return new HtmlString($output);
                                                    // }),


                                                    TextEntry::make('clinicData.company_name')
                                                        ->label('Company Name')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['company_name'])) {
                                                                foreach ($changes['company_name'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    TextEntry::make('clinicData.company_number')
                                                        ->label('Company Registration Number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['company_number'])) {
                                                                foreach ($changes['company_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    // TextEntry::make('clinicData.clinic_owner')
                                                    //     ->label('Facility Owner')
                                                    //     ->formatStateUsing(function ($state, $record) use ($changes) {
                                                    //         $output = " $state <br>";

                                                    //         if (isset($changes['clinic_owner'])) {
                                                    //             foreach ($changes['clinic_owner'] as $change) {
                                                    //                 $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                    //             }
                                                    //         }

                                                    //         return new HtmlString($output);
                                                    //     }),
                                                    TextEntry::make('clinicData.clinic_year')
                                                        ->label('Establishment Year')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['clinic_year'])) {
                                                                foreach ($changes['clinic_year'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),
                                                    TextEntry::make('clinicData.tin_number')
                                                        ->label('TIN Number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['tin_number'])) {
                                                                foreach ($changes['tin_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        }),


                                                    // TextEntry::make('clinicData.clinicAccountType.name')
                                                    //     ->label('Type of Facility')
                                                    //     ->formatStateUsing(function ($state, $record) use ($changes) {
                                                    //         $output = " $state <br>";

                                                    //         if (isset($changes['name'])) {
                                                    //             foreach ($changes['name'] as $change) {
                                                    //                 $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                    //             }
                                                    //         }

                                                    //         return new HtmlString($output);
                                                    //     }),

                                                    TextEntry::make('clinicData.sst_number')
                                                        ->formatStateUsing(function ($state, $record) use ($changes) {
                                                            $output = " $state <br>";

                                                            if (isset($changes['sst_number'])) {
                                                                foreach ($changes['sst_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('SST Registration Number'),


                                                ])
                                        ])->columns(2),

                                    InfoSection::make('Billing Address')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                // ->slideOver()
                                                ->icon('heroicon-o-pencil-square')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->visible(function ($record) {
                                                    $user = auth()->user();
                                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                })
                                                ->form(function ($record) {
                                                    return [
                                                        Group::make()->schema([
                                                            // TextInput::make('billingAddress.billing_nick_name')
                                                            //     ->label(new HtmlString('Nick Name <span style="color:red">*</span>'))
                                                            //     ->rules(['required', 'max:50'])
                                                            //     ->validationMessages([
                                                            //         'required' => 'The nick name field is required.',
                                                            //         'max' => 'The nick name must not exceed 50 characters.',
                                                            //         // 'regex' => 'Only letters, numbers and spaces are allowed.',
                                                            //     ])
                                                            //     ->placeholder('Enter Nick Name')
                                                            //     ->live()->default($record->billingAddress->nick_name),
                                                            TextInput::make('billingAddress.billing_address_1')->label('Address line 1')->placeholder('Enter address line 1')->required()
                                                                ->live()->default($record->billingAddress->address_1),
                                                            TextInput::make('billingAddress.billing_address_2')->label('Address line 2')->placeholder('Enter address line 2')
                                                                ->live()->default($record->billingAddress->address_2),

                                                            Select::make('billingAddress.billing_state_id')
                                                                ->label('State')->placeholder('Select State')->searchable()
                                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                    return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                        ->where('country_id', 132)
                                                                        ->pluck('name', 'id')
                                                                        ->toArray();
                                                                })
                                                                ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {

                                                                    $set('billingAddress.billing_city_id', null);
                                                                    $set('billingAddress.billing_postal_code', null);
                                                                })
                                                                ->required()->default($record->billingAddress->state_id)->live(),

                                                            Select::make('billingAddress.billing_city_id')->label('City')->placeholder('Select City')
                                                                ->options(function (Get $get, $record) {
                                                                    // dd($record->billingAddress->state_id);
                                                                    if (!empty($get('billingAddress.billing_state_id'))) {
                                                                        return City::where('state_id', $get('billingAddress.billing_state_id'))->pluck('name', 'id')->toArray();
                                                                    }
                                                                    return [];
                                                                })
                                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {

                                                                    $set('billingAddress.billing_postal_code', null);
                                                                })
                                                                ->getSelectedRecordUsing(function (Get $get) {
                                                                    return $get('billing_city_id') ?? '';
                                                                })->default($record->billingAddress->city_id)->required()
                                                                ->live(onBlur: true)
                                                                ->loadingMessage('Loading cities...')
                                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                    return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                        ->where('state_id', $get('billingAddress.billing_state_id'))
                                                                        ->pluck('name', 'id')
                                                                        ->toArray();
                                                                })
                                                                ->searchable(),
                                                            Select::make('billingAddress.billing_postal_code')->label('Postal Code')->placeholder('Select postal code')
                                                                ->options(function (Get $get) {

                                                                    if (!empty($get('billingAddress.billing_city_id'))) {

                                                                        return ZipCode::where('city_id', $get('billingAddress.billing_city_id'))->pluck('code', 'code');
                                                                    }
                                                                    return [];
                                                                })
                                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                    if ($get('billingAddress.billing_city_id')) {
                                                                        return ZipCode::where('city_id', $get('billingAddress.billing_city_id'))
                                                                            ->where('code', 'like', "%{$search}%")
                                                                            ->pluck('code', 'code')
                                                                            ->toArray();
                                                                    }
                                                                    return [];
                                                                })
                                                                ->default($record->billingAddress->postal_code)->live()
                                                                ->required()
                                                                ->optionsLimit(100)
                                                                ->live(onBlur: true)
                                                                ->loadingMessage('Loading postal code...')
                                                                ->searchable(),
                                                            TextInput::make('billingAddress.billing_country_id')
                                                                ->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                                                            // TextInput::make('billingAddress.billing_postal_code')->label('Postal Code')
                                                            //     ->suffixIcon(fn($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                                            //     ->suffixIconColor(fn($state) => strlen($state) === 5 ? 'success' : null)
                                                            //     ->rules(['required', 'digits:5'])->required()->default($record->billingAddress->postal_code)
                                                            //     ->placeholder('Enter postal code')->live(),

                                                            // Toggle::make('is_active')->default($record->is_active),
                                                        ])->columns(3)
                                                    ];
                                                })

                                                ->action(function ($record, $data, Form $form) {
                                                    $form->fill();
                                                    // $billingAddressId = $record->clinicData->billing_addresses_id;

                                                    $billingAddress = [
                                                        'address_1' => $data['billingAddress']['billing_address_1'] ?? '',
                                                        'address_2' => $data['billingAddress']['billing_address_2'] ?? '',
                                                        'state_id' => $data['billingAddress']['billing_state_id'] ?? '',
                                                        'city_id' => $data['billingAddress']['billing_city_id'] ?? '',
                                                        'country_id' => 132, //$data['billingAddress']['billing_country_id'] ?? '',
                                                        'postal_code' => $data['billingAddress']['billing_postal_code'] ?? '',
                                                        // 'nick_name' => $data['billingAddress']['billing_nick_name'] ?? '',
                                                    ];

                                                    $record->userAddresses()->updateOrCreate(
                                                        ['user_id' => $record->id, 'address_type' => 'billing'],
                                                        $billingAddress
                                                    );
                                                    $record->userAddresses()->where('address_type', 'shipping')->where('is_same_as_billing', true)->update([
                                                        'address_1' => $billingAddress['address_1'],
                                                        'address_2' => $billingAddress['address_2'],
                                                        'state_id' => $billingAddress['state_id'],
                                                        'city_id' => $billingAddress['city_id'],
                                                        'country_id' => $billingAddress['country_id'],
                                                        'postal_code' => $billingAddress['postal_code'],
                                                        // 'nick_name' => $billingAddress['nick_name']
                                                    ]);

                                                    //Activity Log Start
                                                    // activity()
                                                    //     ->causedBy(auth()->user())
                                                    //     ->performedOn($record->clinicData ?? $record)
                                                    //     ->useLog('contact_details_update')
                                                    //     ->withProperties([
                                                    //         'old' => collect([
                                                    //             'address_1' => $record->clinicData?->billing_address_1 ?? '',
                                                    //             'address_2' => $record->clinicData?->address_2 ?? '',
                                                    //             'state_id' => $record->clinicData?->state_id ?? '',
                                                    //             'city_id' => $record->clinicData?->city_id ?? '',
                                                    //             'country_id' => $record->clinicData?->country_id ?? '',
                                                    //             'postal_code' => $record->clinicData?->postal_code ?? '',
                                                    //             'nick_name' => $record->clinicData?->nick_name ?? ''
                                                    //         ])
                                                    //             ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                    //             ->merge(self::getCityState($billingAddress['state_id'] ?? null, $billingAddress['city_id'] ?? null)),
                                                    //         'attributes' => collect($billingAddress)
                                                    //             ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                    //             ->merge(self::getCityState($billingAddress['state_id'] ?? null, $billingAddress['city_id'] ?? null)),

                                                    //     ])
                                                    //     ->log("Facility '{$record->clinicData?->clinic_name}' has been updated");
                                                    //Activity Log End

                                                    Notification::make()
                                                        ->title('Changes saved successfully')
                                                        ->success()->send();
                                                    return redirect(ClinicResource::getUrl('view', ['record' => $record]));
                                                }),
                                            // static::makeAcceptAction(OnboardingStep::ADDRESS->value),
                                            // static::makeRejectAction(OnboardingStep::ADDRESS->value),
                                            \Filament\Infolists\Components\Actions\Action::make('accept')
                                                ->color('success')
                                                ->outlined()
                                                ->button()
                                                ->requiresConfirmation()
                                                ->visible(function ($record) {
                                                    $pendingApprovals = Approval::pendingForSteps(OnboardingStep::ADDRESS)
                                                        ->where('approvalable_id', $record->id)
                                                        ->get();

                                                    foreach ($pendingApprovals as $approval) {
                                                        $data = json_decode($approval->new_data, true);
                                                        if (isset($data['address_type']) && $data['address_type'] === 'billing') {
                                                            return true;
                                                        }
                                                    }
                                                    return false;
                                                })

                                                // ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::ADDRESS)->where('approvalable_id', $record->id)->exists())
                                                ->modalHeading('Confirm Approval')
                                                ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                                                ->modalSubmitActionLabel('Confirm')
                                                ->action(function ($record) {
                                                    $userId = $record->id;
                                                    $steps = 2;

                                                    $approvalData = Approval::where('approvalable_id', $userId)
                                                        ->where('approvalable_type', 'App\Models\User')
                                                        ->where('steps', $steps)
                                                        ->where('approved_by', null)
                                                        ->where('approved_at', null)
                                                        ->where('rejected_at', null)
                                                        ->where('rejected_by', null)
                                                        ->latest()
                                                        ->first();

                                                    if ($approvalData) {
                                                        $newData = json_decode($approvalData->new_data, true);
                                                        if (isset($newData['address_type']) && $newData['address_type'] === 'billing') {
                                                            $billingAddress = [];

                                                            $fields = ['address_1', 'address_2', 'state_id', 'city_id', 'country_id', 'postal_code'];

                                                            foreach ($fields as $field) {
                                                                if (isset($newData[$field])) {
                                                                    $billingAddress[$field] = $newData[$field];
                                                                }
                                                            }
                                                            $billingAddress['address_type'] = 'billing';

                                                            //Activity Log Start
                                                            // $oldBillingAddress = $record->userAddresses()
                                                            //     ->where('address_type', 'billing')
                                                            //     ->first();

                                                            // $oldData = [
                                                            //     'address_1' => $oldBillingAddress?->address_1 ?? '',
                                                            //     'address_2' => $oldBillingAddress?->address_2 ?? '',
                                                            //     'state_id' => $oldBillingAddress?->state_id ?? '',
                                                            //     'city_id' => $oldBillingAddress?->city_id ?? '',
                                                            //     'country_id' => $oldBillingAddress?->country_id ?? '',
                                                            //     'postal_code' => $oldBillingAddress?->postal_code ?? '',
                                                            //     'nick_name' => $oldBillingAddress?->nick_name ?? ''
                                                            // ];

                                                            // $changedData = collect($billingAddress)
                                                            //     ->only($fields)
                                                            //     ->filter(function ($value, $key) use ($oldData) {
                                                            //         return $value !== $oldData[$key];
                                                            //     });

                                                            // $oldCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                            //     ? self::getCityState($oldData['state_id'] ?? null, $oldData['city_id'] ?? null)
                                                            //     : [];
                                                            // $newCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                            //     ? self::getCityState($billingAddress['state_id'] ?? null, $billingAddress['city_id'] ?? null)
                                                            //     : [];
                                                            //Activity Log End


                                                            $updatedBillingAddress =  $record->userAddresses()->updateOrCreate(
                                                                ['user_id' => $record->id, 'address_type' => 'billing'],
                                                                $billingAddress
                                                            );
                                                            // Find and update any shipping addresses marked as same_as_billing
                                                            $record->userAddresses()
                                                                ->where('address_type', 'shipping')
                                                                ->where('is_same_as_billing', true)
                                                                ->each(function ($shippingAddress) use ($updatedBillingAddress, $fields) {
                                                                    $updateData = [];
                                                                    foreach ($fields as $field) {
                                                                        $updateData[$field] = $updatedBillingAddress->$field;
                                                                    }
                                                                    $shippingAddress->update($updateData);
                                                                });

                                                            //Activity Log Start
                                                            // if ($changedData->isNotEmpty()) {
                                                            //     activity()
                                                            //         ->causedBy(auth()->user())
                                                            //         ->performedOn($record->clinicData ?? $record)
                                                            //         ->useLog('billing_address_approval')
                                                            //         ->withProperties([
                                                            //             'old' => collect($oldData)
                                                            //                 ->only($changedData->keys())
                                                            //                 ->merge($oldCityState)
                                                            //                 ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                            //                 ->filter(),
                                                            //             'attributes' => collect($billingAddress)
                                                            //                 ->only($changedData->keys())
                                                            //                 ->merge($newCityState)
                                                            //                 ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                            //                 ->filter(),
                                                            //         ])
                                                            //         ->log("Billing address changes have been approved for facility '{$record->clinicData?->clinic_name}'");
                                                            // }

                                                            //Activity Log End
                                                        }
                                                        $approvalData->where('id', $approvalData->id)->where('approvalable_id', $record->id)->where('steps', $steps)->update([
                                                            'approved_at' => now(),
                                                            'approved_by' => auth()->id(),
                                                        ]);
                                                    }
                                                    Mail::to($record->email)->send(new ChangesApprovedMail($record));
                                                    Notification::make()
                                                        ->title('Changes Approved Successfully')
                                                        ->success()
                                                        ->send();
                                                }),
                                            \Filament\Infolists\Components\Actions\Action::make('reject')
                                                ->color('danger')
                                                ->outlined()
                                                ->button()
                                                ->requiresConfirmation()
                                                ->visible(function ($record) {
                                                    $pendingApprovals = Approval::pendingForSteps(OnboardingStep::ADDRESS)
                                                        ->where('approvalable_id', $record->id)
                                                        ->get();

                                                    foreach ($pendingApprovals as $approval) {
                                                        $data = json_decode($approval->new_data, true);
                                                        if (isset($data['address_type']) && $data['address_type'] === 'billing') {
                                                            return true;
                                                        }
                                                    }
                                                    return false;
                                                })
                                                // ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::ADDRESS)->where('approvalable_id', $record->id)->exists())
                                                ->modalHeading('Confirm Rejection')
                                                ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                                                ->modalSubmitActionLabel('Confirm')
                                                ->action(function ($record) {
                                                    $userId = $record->id;
                                                    $steps = 2;

                                                    $approvalData = Approval::where('approvalable_id', $userId)
                                                        ->where('approvalable_type', 'App\Models\User')
                                                        ->where('steps', $steps)
                                                        ->where('approved_by', null)
                                                        ->where('approved_at', null)
                                                        ->where('rejected_at', null)
                                                        ->where('rejected_by', null)
                                                        ->latest()
                                                        ->first();

                                                    if ($approvalData) {
                                                        $newData = json_decode($approvalData->new_data, true);

                                                        if (isset($newData['address_type']) && $newData['address_type'] === 'billing') {

                                                            //Activity Log Start
                                                            // $fields = ['address_1', 'address_2', 'state_id', 'city_id', 'country_id', 'postal_code', 'nick_name'];
                                                            // $rejectedValues = collect($newData)->only($fields)->toArray();
                                                            // $currentAddress = $record->billingAddress()->where('address_type', 'billing')->first();
                                                            // $currentValues = collect([
                                                            //     'address_1' => $currentAddress?->address_1 ?? '',
                                                            //     'address_2' => $currentAddress?->address_2 ?? '',
                                                            //     'state_id' => $currentAddress?->state_id ?? '',
                                                            //     'city_id' => $currentAddress?->city_id ?? '',
                                                            //     'country_id' => $currentAddress?->country_id ?? '',
                                                            //     'postal_code' => $currentAddress?->postal_code ?? '',
                                                            //     'nick_name' => $currentAddress?->nick_name ?? '',
                                                            // ])->only(array_keys($rejectedValues))->toArray();

                                                            // $oldCityState = !empty($rejectedValues['state_id']) || !empty($rejectedValues['city_id'])
                                                            //     ? self::getCityState($rejectedValues['state_id'] ?? null, $rejectedValues['city_id'] ?? null)
                                                            //     : [];
                                                            // $newCityState = !empty($currentValues['state_id']) || !empty($currentValues['city_id'])
                                                            //     ? self::getCityState($currentValues['state_id'] ?? null, $currentValues['city_id'] ?? null)
                                                            //     : [];

                                                            // $clinicName = $record->clinicData?->clinic_name ?? 'Unknown';
                                                            // activity()
                                                            //     ->causedBy(auth()->user())
                                                            //     ->performedOn($record->clinicData ?? $record)
                                                            //     ->useLog('shipping_address_rejection')
                                                            //     ->withProperties([
                                                            //         'old' => collect($rejectedValues)->except(['state_id', 'city_id'])->merge($oldCityState)->toArray(),
                                                            //         'attributes' => collect($currentValues)->except(['state_id', 'city_id'])->merge($newCityState)->toArray(),
                                                            //     ])
                                                            // ->log("Billing address changes has been rejected for facility '$clinicName'");

                                                            //Activity Log End


                                                            $approvalData->where('approvalable_id', $userId)->where('steps', $steps)->update([
                                                                'rejected_at' => now(),
                                                                'rejected_by' => auth()->id(),
                                                            ]);
                                                            Mail::to($record->email)->send(new ChangesRejectedMail($record));
                                                            Notification::make()
                                                                ->title('Billing Address Changes Rejected Successfully')
                                                                ->success()
                                                                ->send();
                                                        } else {
                                                            Notification::make()
                                                                ->title('No Billing Address Found For Rejection')
                                                                ->danger()
                                                                ->send();
                                                        }
                                                    } else {
                                                        Notification::make()
                                                            ->title('No Data Found For Rejection')
                                                            ->danger()
                                                            ->send();
                                                    }
                                                }),
                                        ])
                                        ->schema([
                                            \Filament\Infolists\Components\Grid::make([
                                                'default' => 4,
                                                'sm' => 4,
                                            ])
                                                ->columnSpan('full')
                                                ->extraAttributes(['class' => 'gap-0'])
                                                ->schema([
                                                    // TextEntry::make('billingAddress.nick_name')
                                                    //     ->default(fn($record) => $record->billingAddress?->nick_name ?: '-')                                                    // ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                    //     //     $output = " $state <br>";

                                                    //     //         if (isset($billingAddressChanges['nick_name'])) {
                                                    //     //             foreach ($billingAddressChanges['nick_name'] as $change) {
                                                    //     //                 $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                    //     //             }
                                                    //     //         }

                                                    //     //         return new HtmlString($output);
                                                    //     //     })
                                                    //     ->label('Nick Name'),
                                                    TextEntry::make('billingAddress.address_1')
                                                        ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($billingAddressChanges['address_1'])) {
                                                                foreach ($billingAddressChanges['address_1'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Address Line 1'),
                                                    TextEntry::make('billingAddress.address_2')
                                                        ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($billingAddressChanges['address_2'])) {
                                                                foreach ($billingAddressChanges['address_2'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Address Line 2'),
                                                    TextEntry::make('billingAddress.city.name')
                                                        ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($billingAddressChanges['city_id'])) {
                                                                foreach ($billingAddressChanges['city_id'] as $change) {
                                                                    $cityName = City::find($change['new_value'])?->name;
                                                                    if ($cityName) {
                                                                        $output .= "<span style='background-color: yellow;'>{$cityName}</span><br>";
                                                                    }
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('City'),
                                                    TextEntry::make('billingAddress.state.name')
                                                        ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($billingAddressChanges['state_id'])) {
                                                                foreach ($billingAddressChanges['state_id'] as $change) {
                                                                    $stateName = State::find($change['new_value'])?->name;
                                                                    if ($stateName) {
                                                                        $output .= "<span style='background-color: yellow;'>{$stateName}</span><br>";
                                                                    }
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('State'),
                                                    TextEntry::make('billingAddress.country.name')
                                                        ->label('Country'),
                                                    TextEntry::make('billingAddress.postal_code')
                                                        ->formatStateUsing(function ($state, $record) use ($billingAddressChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($billingAddressChanges['postal_code'])) {
                                                                foreach ($billingAddressChanges['postal_code'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Postal Code'),



                                                ])
                                        ])->columns(2),
                                    InfoSection::make('Shipping Address')
                                        ->description(function ($record) use ($shippingAddressChanges) {
                                            $hasNewAddresses = collect($shippingAddressChanges)->keys()->filter(function ($key) {
                                                return str_starts_with($key, 'new_');
                                            })->isNotEmpty();


                                            // Check for default address changes in approvals
                                            $hasDefaultAddressChanges = false;
                                            $pendingApprovals = Approval::pendingForSteps(OnboardingStep::ADDRESS)
                                                ->where('approvalable_id', $record->id)
                                                ->get();

                                            foreach ($pendingApprovals as $approval) {
                                                $data = json_decode($approval->new_data, true);
                                                if (isset($data['address_type']) && $data['address_type'] === 'shipping') {
                                                    // Check if any address has is_default changed to true
                                                    foreach ($data as $key => $addressData) {
                                                        if (is_numeric($key) && isset($addressData['is_default']) && $addressData['is_default'] === true) {
                                                            $hasDefaultAddressChanges = true;
                                                            break 2; // Break both loops
                                                        }
                                                    }
                                                }
                                            }

                                            if ($hasNewAddresses) {
                                                return new \Illuminate\Support\HtmlString(' ⚠️ <strong>Note:</strong> 
                                                <span style="background-color: yellow;">  New Shipping Address Added by clinic - you can approve or reject.</span> ');
                                            }

                                            if ($hasDefaultAddressChanges) {
                                                return new \Illuminate\Support\HtmlString(' ⚠️ <strong>Note:</strong> 
                                                <span style="background-color: yellow;">  Default Shipping Address Changed by clinic - you can approve or reject.</span> '); //"⚠️ Default Shipping Address Changed by clinic - you can approve or reject";
                                            }

                                            // Default description if no changes
                                            return "";
                                        })
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                // ->slideOver()
                                                ->icon('heroicon-o-pencil-square')
                                                ->color('gray')
                                                ->outlined()

                                                ->visible(false)
                                                ->button()
                                                ->form(function ($record) {
                                                    $defaultAddress = $record->shippingAddresses
                                                        ->where('address_type', 'shipping')
                                                        ->where('is_default', true)
                                                        ->first() ?? (
                                                            $record->clinicData &&
                                                            $record->clinicData->is_same_as_billing == $record->clinicData->shipping_address_id
                                                            ? $record->clinicData->billingAddress
                                                            : null
                                                        );
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('shippingAddress.shipping_nick_name')
                                                                ->label(new HtmlString('Nick Name <span style="color:red">*</span>'))
                                                                ->rules(['required', 'max:50'])
                                                                ->validationMessages([
                                                                    'required' => 'The nick name field is required.',
                                                                    'max' => 'The nick name must not exceed 50 characters.',
                                                                    // 'regex' => 'Only letters, numbers and spaces are allowed.',
                                                                ])
                                                                ->placeholder('Enter Nick Name')
                                                                ->live()->default($defaultAddress->nick_name),
                                                            TextInput::make('shippingAddress.shipping_address_1')
                                                                ->label('Address line 1')
                                                                ->placeholder('Enter address line 1')
                                                                ->required()
                                                                ->live()
                                                                ->default($defaultAddress->address_1 ?? ''),
                                                            TextInput::make('shippingAddress.shipping_address_2')->label('Address line 2')->placeholder('Enter address line 2')
                                                                ->live()
                                                                ->default($defaultAddress->address_2 ?? ''),
                                                            TextInput::make('shippingAddress.shipping_country_id')
                                                                ->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                                                            Select::make('shippingAddress.shipping_state_id')
                                                                ->label('State')->placeholder('Select State')
                                                                ->searchable()
                                                                ->live()
                                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                    return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                        ->where('country_id', 132)
                                                                        ->pluck('name', 'id')
                                                                        ->toArray();
                                                                })
                                                                ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {

                                                                    $set('shippingAddress.shipping_city_id', null);
                                                                    $set('shippingAddress.shipping_postal_code', null);
                                                                })
                                                                ->required()->default($defaultAddress->state_id ?? '')->live(),
                                                            Select::make('shippingAddress.shipping_city_id')->label('City')->placeholder('Select City')
                                                                ->options(function (Get $get, $record) {
                                                                    // dd($record->billingAddress->state_id);
                                                                    if (!empty($get('shippingAddress.shipping_state_id'))) {

                                                                        return City::where('state_id', $get('shippingAddress.shipping_state_id'))->pluck('name', 'id');
                                                                    }
                                                                    return [];
                                                                })
                                                                ->getSelectedRecordUsing(function (Get $get) {
                                                                    return $get('shipping_city_id') ?? '';
                                                                })->default($defaultAddress->city_id ?? '')->required()
                                                                ->live(onBlur: true)
                                                                ->loadingMessage('Loading cities...')
                                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                    return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                        ->where('state_id', $get('shippingAddress.shipping_state_id'))
                                                                        ->pluck('name', 'id')
                                                                        ->toArray();
                                                                })
                                                                ->searchable(),

                                                            TextInput::make('shippingAddress.shipping_postal_code')->label('Postal Code')
                                                                ->live()
                                                                ->suffixIcon(fn($state) => strlen($state) === 5 ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => strlen($state) === 5 ? 'success' : null)
                                                                ->rules(['required', 'digits:5'])->required()->default($defaultAddress->postal_code ?? '')
                                                                ->placeholder('Enter postal code')->live(),

                                                            // Toggle::make('is_active')->default($record->is_active),
                                                        ])->columns(3)
                                                    ];
                                                })
                                                ->action(function ($record, $data, Form $form) {
                                                    $form->fill();
                                                    // dd($data);
                                                    // $record->update(Arr::only($data, ['name', 'is_active', 'email', 'phone']));
                                                    // $record->shippingAddress->updateOrCreate([
                                                    //     'user_id' => $record->id
                                                    // ], Arr::only($data['shippingAddress'], [
                                                    //     'address_1', 'address_2', 'country_id', 'state_id', 'city_id', 'postal_code'
                                                    // ]));


                                                    $shippingAddress = [
                                                        'address_1' => $data['shippingAddress']['shipping_address_1'] ?? '',
                                                        'address_2' => $data['shippingAddress']['shipping_address_2'] ?? '',
                                                        'state_id' => $data['shippingAddress']['shipping_state_id'] ?? '',
                                                        'city_id' => $data['shippingAddress']['shipping_city_id'] ?? '',
                                                        'country_id' => 132, //$data['billingAddress']['billing_country_id'] ?? '',
                                                        'postal_code' => $data['shippingAddress']['shipping_postal_code'] ?? '',
                                                        'nick_name' => $data['shippingAddress']['shipping_nick_name'] ?? ''
                                                    ];

                                                    $record->shippingAddresses()->updateOrCreate(
                                                        ['user_id' => $record->id, 'address_type' => 'shipping', 'is_default' => true],
                                                        $shippingAddress
                                                    );

                                                    //Activity Log Start
                                                    // $oldShippingAddress = $record->userAddresses()
                                                    //     ->where('address_type', 'shipping')
                                                    //     ->where('is_default', true)
                                                    //     ->first();

                                                    // $oldData = [
                                                    //     'address_1' => $oldShippingAddress?->address_1 ?? '',
                                                    //     'address_2' => $oldShippingAddress?->address_2 ?? '',
                                                    //     'state_id' => $oldShippingAddress?->state_id ?? '',
                                                    //     'city_id' => $oldShippingAddress?->city_id ?? '',
                                                    //     'country_id' => $oldShippingAddress?->country_id ?? '',
                                                    //     'postal_code' => $oldShippingAddress?->postal_code ?? '',
                                                    //     'nick_name' => $oldShippingAddress?->nick_name ?? ''
                                                    // ];

                                                    // $changedData = collect($shippingAddress)
                                                    //     ->only(array_keys($oldData))
                                                    //     ->filter(function ($value, $key) use ($oldData) {
                                                    //         return $value !== $oldData[$key];
                                                    //     });

                                                    // $oldCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                    //     ? self::getCityState($oldData['state_id'] ?? null, $oldData['city_id'] ?? null)
                                                    //     : [];
                                                    // $newCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                    //     ? self::getCityState($shippingAddress['state_id'] ?? null, $shippingAddress['city_id'] ?? null)
                                                    //     : [];

                                                    // // Only log if there are changes
                                                    // if ($changedData->isNotEmpty()) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->performedOn($record->clinicData ?? $record)
                                                    //         ->useLog('contact_details_update')
                                                    //         ->withProperties([
                                                    //             'old' => collect($oldData)
                                                    //                 ->only($changedData->keys())
                                                    //                 ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                    //                 ->merge($oldCityState)
                                                    //                 ->filter(),
                                                    //             'attributes' => $changedData
                                                    //                 ->only($changedData->keys())
                                                    //                 ->except(['id', 'user_id', 'created_at', 'updated_at', 'state_id', 'city_id'])
                                                    //                 ->merge($newCityState)
                                                    //                 ->filter(),
                                                    //         ])
                                                    //         ->log("Shipping address of facility '{$record->clinicData?->clinic_name}' have been updated");
                                                    // }
                                                    //Activity Log End


                                                    Notification::make()
                                                        ->title('Changes saved successfully')
                                                        ->success()->send();
                                                    return redirect(ClinicResource::getUrl('view', ['record' => $record]));
                                                }),
                                            \Filament\Infolists\Components\Actions\Action::make('accept')
                                                ->color('success')
                                                ->outlined()
                                                ->button()
                                                ->requiresConfirmation()
                                                ->visible(function ($record) {
                                                    $pendingApprovals = Approval::pendingForSteps(OnboardingStep::ADDRESS)
                                                        ->where('approvalable_id', $record->id)
                                                        ->get();
                                                    foreach ($pendingApprovals as $approval) {
                                                        $data = json_decode($approval->new_data, true);
                                                        if (isset($data['address_type']) && $data['address_type'] === 'shipping') {
                                                            return true;
                                                        }
                                                    }
                                                    return false;
                                                })
                                                // ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::ADDRESS)->where('approvalable_id', $record->id)->exists())
                                                ->modalHeading('Confirm Approval')
                                                ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                                                ->modalSubmitActionLabel('Confirm')
                                                ->action(function ($record) {
                                                    $userId = $record->id;
                                                    $steps = 2;

                                                    $approvals = Approval::where('approvalable_id', $userId)
                                                        ->where('approvalable_type', 'App\Models\User')
                                                        ->where('steps', $steps)
                                                        ->where('approved_by', null)
                                                        ->where('approved_at', null)
                                                        ->where('rejected_at', null)
                                                        ->where('rejected_by', null)
                                                        ->latest()
                                                        ->get();
                                                    if ($approvals->isNotEmpty()) {

                                                        //Activity Log Start
                                                        // $oldData = [
                                                        //     'address_1' => $currentAddress?->address_1 ?? '',
                                                        //     'address_2' => $currentAddress?->address_2 ?? '',
                                                        //     'state_id' => $currentAddress?->state_id ?? '',
                                                        //     'city_id' => $currentAddress?->city_id ?? '',
                                                        //     'country_id' => $currentAddress?->country_id ?? '',
                                                        //     'postal_code' => $currentAddress?->postal_code ?? '',
                                                        //     'nick_name' => $currentAddress?->nick_name ?? '',
                                                        // ];
                                                        //Activity Log End

                                                        foreach ($approvals as $approval) {
                                                            $newData = json_decode($approval->new_data, true);
                                                            // dd($newData);
                                                            if (isset($newData['address_type']) && $newData['address_type'] === 'shipping') {
                                                                $record?->userAddresses()
                                                                    ->where('address_type', 'shipping')
                                                                    ->update(['is_default' => false]);
                                                                foreach ($newData as $key => $addressData) {
                                                                    if (!is_numeric($key)) {
                                                                        continue;
                                                                    }
                                                                    $shippingAddress = [];
                                                                    $fields = ['address_1', 'address_2', 'state_id', 'city_id', 'country_id', 'postal_code', 'is_same_as_billing', 'is_default'];

                                                                    foreach ($fields as $field) {
                                                                        if (isset($addressData[$field])) {
                                                                            $shippingAddress[$field] = $addressData[$field];
                                                                        }
                                                                    }

                                                                    $shippingAddress['address_type'] = 'shipping';

                                                                    //Activity Log Start
                                                                    // $newAddressData = Arr::only($shippingAddress, array_keys($oldData));
                                                                    // $changedData = collect($newAddressData)
                                                                    //     ->filter(function ($value, $key) use ($oldData) {
                                                                    //         return $value !== $oldData[$key];
                                                                    //     });

                                                                    // if ($changedData->isNotEmpty()) {
                                                                    //     $oldCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                                    //         ? self::getCityState($oldData['state_id'] ?? null, $oldData['city_id'] ?? null)
                                                                    //         : [];
                                                                    //     $newCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                                    //         ? self::getCityState($newAddressData['state_id'] ?? null, $newAddressData['city_id'] ?? null)
                                                                    //         : [];

                                                                    //     activity()
                                                                    //         ->causedBy(auth()->user())
                                                                    //         ->performedOn($record->clinicData ?? $record)
                                                                    //         ->useLog('shipping_address_approval')
                                                                    //         ->withProperties([
                                                                    //             'old' => collect($oldData)
                                                                    //                 ->only($changedData->keys())
                                                                    //                 ->except(['state_id', 'city_id'])
                                                                    //                 ->merge($oldCityState)
                                                                    //                 ->filter(),
                                                                    //             'attributes' => $changedData
                                                                    //                 ->except(['state_id', 'city_id'])
                                                                    //                 ->merge($newCityState)
                                                                    //                 ->filter(),
                                                                    //         ])
                                                                    //         ->log("Shipping address changes have been approved for facility '{$record->clinicData?->clinic_name}'");
                                                                    // }
                                                                    //Activity Log End


                                                                    if (!isset($addressData['id'])) {
                                                                        $shippingAddress['is_onboarding'] = true;
                                                                        $record?->userAddresses()?->create($shippingAddress);
                                                                    } else {
                                                                        $record?->userAddresses()?->updateOrCreate(
                                                                            ['user_id' => $record->id, 'id' => $addressData['id'] ?? null],
                                                                            $shippingAddress
                                                                        );
                                                                    }
                                                                }
                                                            }

                                                            $approval->update([
                                                                'approved_at' => now(),
                                                                'approved_by' => auth()->id(),
                                                            ]);
                                                        }
                                                        Mail::to($record->email)->send(new ChangesApprovedMail($record));
                                                        Notification::make()
                                                            ->title('Changes Approved Successfully')
                                                            ->success()
                                                            ->send();
                                                    } else {
                                                        Notification::make()
                                                            ->title('No pending approvals found')
                                                            ->warning()
                                                            ->send();
                                                    }
                                                }),
                                            \Filament\Infolists\Components\Actions\Action::make('reject')
                                                ->color('danger')
                                                ->outlined()
                                                ->button()
                                                ->requiresConfirmation()
                                                ->visible(function ($record) {
                                                    $pendingApprovals = Approval::pendingForSteps(OnboardingStep::ADDRESS)
                                                        ->where('approvalable_id', $record->id)
                                                        ->get();
                                                    foreach ($pendingApprovals as $approval) {
                                                        $data = json_decode($approval->new_data, true);
                                                        if (isset($data['address_type']) && $data['address_type'] === 'shipping') {
                                                            return true;
                                                        }
                                                    }
                                                    return false;
                                                })
                                                // ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::ADDRESS)->where('approvalable_id', $record->id)->exists())
                                                ->modalHeading('Confirm Rejection')
                                                ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                                                ->modalSubmitActionLabel('Confirm')
                                                ->action(function ($record) {
                                                    $userId = $record->id;
                                                    $steps = 2;

                                                    $approvals = Approval::where('approvalable_id', $userId)
                                                        ->where('approvalable_type', 'App\Models\User')
                                                        ->where('steps', $steps)
                                                        ->where('approved_by', null)
                                                        ->where('approved_at', null)
                                                        ->where('rejected_at', null)
                                                        ->where('rejected_by', null)
                                                        ->latest()
                                                        ->get();
                                                    if ($approvals->isNotEmpty()) {
                                                        $rejectedCount = 0;

                                                        foreach ($approvals as $approval) {
                                                            $newData = json_decode($approval->new_data, true);

                                                            if (isset($newData['address_type']) && $newData['address_type'] === 'shipping') {
                                                                $hasShippingAddresses = false;

                                                                // Check if there are any shipping addresses
                                                                foreach ($newData as $key => $addressData) {
                                                                    if (is_numeric($key)) {
                                                                        $hasShippingAddresses = true;
                                                                        break;
                                                                    }
                                                                }

                                                                if ($hasShippingAddresses) {

                                                                    //Activity Log Start
                                                                    // $fields = ['address_1', 'address_2', 'state_id', 'city_id', 'country_id', 'postal_code', 'nick_name'];
                                                                    // $rejectedValues = collect($newData)->only($fields)->filter()->toArray();

                                                                    // // Get current shipping address for comparison
                                                                    // $currentAddress = $record->userAddresses()
                                                                    //     ->where('address_type', 'shipping')
                                                                    //     ->where('is_default', true)
                                                                    //     ->first();

                                                                    // $currentValues = collect([
                                                                    //     'address_1' => $currentAddress?->address_1 ?? '',
                                                                    //     'address_2' => $currentAddress?->address_2 ?? '',
                                                                    //     'state_id' => $currentAddress?->state_id ?? '',
                                                                    //     'city_id' => $currentAddress?->city_id ?? '',
                                                                    //     'country_id' => $currentAddress?->country_id ?? '',
                                                                    //     'postal_code' => $currentAddress?->postal_code ?? '',
                                                                    //     'nick_name' => $currentAddress?->nick_name ?? '',
                                                                    // ])->only(array_keys($rejectedValues))->filter()->toArray();

                                                                    // $changedData = collect($rejectedValues)
                                                                    //     ->filter(function ($value, $key) use ($currentValues) {
                                                                    //         return $value !== ($currentValues[$key] ?? null);
                                                                    //     });

                                                                    // if ($changedData->isNotEmpty()) {
                                                                    //     $oldCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                                    //         ? self::getCityState($rejectedValues['state_id'] ?? null, $rejectedValues['city_id'] ?? null)
                                                                    //         : [];
                                                                    //     $newCityState = $changedData->has('state_id') || $changedData->has('city_id')
                                                                    //         ? self::getCityState($currentValues['state_id'] ?? null, $currentValues['city_id'] ?? null)
                                                                    //         : [];

                                                                    //     activity()
                                                                    //         ->causedBy(auth()->user())
                                                                    //         ->performedOn($record->clinicData ?? $record)
                                                                    //         ->useLog('shipping_address_rejection')
                                                                    //         ->withProperties([
                                                                    //             'old' => collect($rejectedValues)
                                                                    //                 ->only($changedData->keys())
                                                                    //                 ->except(['state_id', 'city_id'])
                                                                    //                 ->merge($oldCityState)
                                                                    //                 ->filter(),
                                                                    //             'attributes' => collect($currentValues)
                                                                    //                 ->only($changedData->keys())
                                                                    //                 ->except(['state_id', 'city_id'])
                                                                    //                 ->merge($newCityState)
                                                                    //                 ->filter(),
                                                                    //         ])
                                                                    //         ->log("Shipping address changes have been rejected for facility '{$record->clinicData?->clinic_name}'");
                                                                    // }

                                                                    //Activity Log End


                                                                    $approval->where('approvalable_id', $userId)->where('steps', $steps)->update([
                                                                        'rejected_at' => now(),
                                                                        'rejected_by' => auth()->id(),
                                                                    ]);
                                                                    $rejectedCount++;
                                                                }
                                                            }
                                                        }

                                                        if ($rejectedCount > 0) {
                                                            Mail::to($record->email)->send(new ChangesRejectedMail($record));
                                                            Notification::make()
                                                                ->title("Successfully rejected shipping address changes")
                                                                ->success()
                                                                ->send();
                                                        } else {
                                                            Notification::make()
                                                                ->title('No shipping address changes found to reject')
                                                                ->warning()
                                                                ->send();
                                                        }
                                                    } else {
                                                        Notification::make()
                                                            ->title('No pending approvals found to reject')
                                                            ->danger()
                                                            ->send();
                                                    }
                                                }),
                                        ])
                                        ->schema([
                                            RepeatableEntry::make('shippingAddresses')
                                                ->getStateUsing(
                                                    fn($record) => $record->shippingAddresses->where('address_type', 'shipping')->count() > 0
                                                        ? $record->shippingAddresses->where('address_type', 'shipping')
                                                        ->merge(
                                                            // Add billing address as shipping if IDs match
                                                            $record->clinicData &&  $record->clinicData->billing_address_id !== null &&
                                                                $record->clinicData->billing_address_id == $record->clinicData->shipping_address_id
                                                                ? [
                                                                    new UserAddress([
                                                                        'address_1' => $record->clinicData?->billingAddress?->address_1 ?? '',
                                                                        'address_2' => $record->clinicData?->billingAddress?->address_2 ?? '',
                                                                        'state_id' => $record->clinicData?->billingAddress?->state_id ?? null,
                                                                        'city_id' => $record->clinicData?->billingAddress?->city_id ?? null,
                                                                        'postal_code' => $record->clinicData?->billingAddress?->postal_code ?? '',
                                                                        'is_default' => false,
                                                                        'address_type' => 'shipping',
                                                                        'nick_name' => $record->clinicData?->billingAddress?->nick_name ?? null,
                                                                        // Add other required fields
                                                                    ])
                                                                ]
                                                                : []
                                                        )
                                                        : [
                                                            [
                                                                'shipping_address_1' => $record->clinicData?->billingAddress?->address_1 ?? '',
                                                                'shipping_address_2' => $record->clinicData?->billingAddress?->address_2 ?? '',
                                                                'shipping_state_id' => $record->clinicData?->billingAddress?->state_id ?? null,
                                                                'shipping_city_id' => $record->clinicData?->billingAddress?->city_id ?? null,
                                                                'shipping_postal_code' => $record->clinicData?->billingAddress?->postal_code ?? '',
                                                                'shipping_nick_name' => $record->clinicData?->billingAddress?->nick_name ?? null,
                                                                'is_default' => true,
                                                                'shipping_country_id' => 'Malaysia',
                                                            ]
                                                        ]
                                                )
                                                ->label('')
                                                ->schema([
                                                    \Filament\Infolists\Components\Actions::make([
                                                        \Filament\Infolists\Components\Actions\Action::make('editShippingAddress')
                                                            ->icon('heroicon-o-pencil-square')
                                                            ->label('Edit')
                                                            ->color('gray')
                                                            ->outlined()
                                                            ->button()
                                                            ->visible(function ($record) {
                                                                $user = auth()->user();
                                                                return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                            })
                                                            ->extraAttributes(['class' => 'ml-auto'])
                                                            ->form(function ($record, Get $get, $component) {
                                                                $address = $component->getRecord(); // Get the current address record

                                                                return [
                                                                    Group::make()->schema([
                                                                        TextInput::make('nick_name')
                                                                            ->label(new HtmlString('Nick Name <span style="color:red">*</span>'))
                                                                            ->rules(['required', 'max:50'])
                                                                            ->validationMessages([
                                                                                'required' => 'The nick name field is required.',
                                                                                'max' => 'The nick name must not exceed 50 characters.',
                                                                                // 'regex' => 'Only letters, numbers and spaces are allowed.',
                                                                            ])
                                                                            ->default($address->nick_name),
                                                                        TextInput::make('address_1')
                                                                            ->rules(['required', 'max:100'])
                                                                            ->label(new HtmlString('Address Line 1 <span style="color:red">*</span>'))->placeholder('Enter address line 1')
                                                                            ->validationMessages([
                                                                                'max' => 'The Address Line 1 must be at most 100 characters long.',
                                                                                'required' => 'The Address Line 1 field is required.'
                                                                            ])
                                                                            ->default($address->address_1),
                                                                        TextInput::make('address_2')
                                                                            ->label('Address line 2')
                                                                            ->rules(['nullable', 'max:100'])
                                                                            ->validationMessages([
                                                                                'max' => 'The Address Line 2 must be at most 100 characters long.',
                                                                            ])
                                                                            ->default($address->address_2),
                                                                        // TextInput::make('country_id')
                                                                        //     ->label('Country')
                                                                        //     ->default('Malaysia')
                                                                        //     ->readOnly(),
                                                                        Select::make('state_id')
                                                                            ->label(new HtmlString('State <span style="color:red">*</span>'))
                                                                            ->rules(['required'])
                                                                            ->searchable()
                                                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                                return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                                    ->where('country_id', 132)
                                                                                    ->pluck('name', 'id')
                                                                                    ->toArray();
                                                                            })
                                                                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                                                if ($state) {
                                                                                    $set('postal_code', null);
                                                                                }
                                                                                if (blank($state)) {
                                                                                    $set('city_id', null);
                                                                                    $set('postal_code', null);
                                                                                }
                                                                            })
                                                                            ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                                            ->validationMessages([
                                                                                'required' => 'The state field is required.',
                                                                            ])
                                                                            ->default($address->state_id)
                                                                            ->live(),
                                                                        Select::make('city_id')
                                                                            ->label(new HtmlString('City <span style="color:red">*</span>'))
                                                                            ->rules(['required'])
                                                                            ->validationMessages([
                                                                                'required' => 'The City field is required.',
                                                                            ])
                                                                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                                                if ($state) {
                                                                                    $set('postal_code', null);
                                                                                }
                                                                                if (blank($state)) {
                                                                                    $set('postal_code', null);
                                                                                }
                                                                            })
                                                                            ->options(function (Get $get) {
                                                                                if ($get('state_id')) {
                                                                                    return City::where('state_id', $get('state_id'))->pluck('name', 'id');
                                                                                }
                                                                                return [];
                                                                            })
                                                                            ->live()
                                                                            ->searchable()
                                                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                                return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                                                    ->where('state_id', $get('state_id'))
                                                                                    ->pluck('name', 'id')
                                                                                    ->toArray();
                                                                            })
                                                                            ->default($address->city_id),
                                                                        Select::make('postal_code')
                                                                            ->label(new HtmlString('Postal code <span style="color:red">*</span>'))
                                                                            ->rules(['required'])
                                                                            ->validationMessages([
                                                                                'required' => 'The Postal code field is required.',
                                                                            ])
                                                                            ->placeholder('Select postal code')
                                                                            ->options(function (Get $get) {

                                                                                if (!empty($get('city_id'))) {

                                                                                    return ZipCode::where('city_id', $get('city_id'))->pluck('code', 'code');
                                                                                }
                                                                                return [];
                                                                            })
                                                                            ->getSearchResultsUsing(function (string $search, Get $get) {
                                                                                if ($get('city_id')) {
                                                                                    return ZipCode::where('city_id', $get('city_id'))
                                                                                        ->where('code', 'like', "%{$search}%")
                                                                                        ->pluck('code', 'code')
                                                                                        ->toArray();
                                                                                }
                                                                                return [];
                                                                            })
                                                                            ->default($address->postal_code)->live()
                                                                            ->live(onBlur: true)
                                                                            ->optionsLimit(100)
                                                                            ->loadingMessage('Loading postal code...')
                                                                            ->searchable(),
                                                                        // TextInput::make('postal_code')
                                                                        //     ->rules(['required', 'digits:5'])
                                                                        //     ->label(new HtmlString('Postal Code <span style="color:red">*</span>'))
                                                                        //     ->live()
                                                                        //     ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                        //     ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                                                        //     ->extraAttributes([
                                                                        //         'inputmode' => 'numeric',
                                                                        //         'pattern' => '[0-9]*',
                                                                        //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                                        //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                                                        //     ])
                                                                        //     ->validationMessages([
                                                                        //         'required' => 'The Postal Code field is required.',
                                                                        //         'digits' => 'The Postal Code must be exactly 5 digits.',
                                                                        //     ])
                                                                        //     ->default($address->postal_code),
                                                                        // Toggle::make('is_default')
                                                                        //     ->label('Default Address')
                                                                        //     ->default($address->is_default),
                                                                    ])->columns(3)
                                                                ];
                                                            })
                                                            ->action(function ($record, $data, $component) {
                                                                $address = $component->getRecord();
                                                                $address->update($data);

                                                                Notification::make()
                                                                    ->title('Address updated successfully')
                                                                    ->success()
                                                                    ->send();
                                                            })
                                                    ])->columnSpan(['md' => 6])
                                                        ->alignEnd(),
                                                    \Filament\Infolists\Components\Grid::make([
                                                        'default' => 4,
                                                        'sm' => 4,
                                                    ])
                                                        ->columnSpan('full')
                                                        ->extraAttributes(['class' => 'gap-0'])
                                                        ->schema([
                                                            TextEntry::make('is_default')
                                                                ->label('')
                                                                ->badge()
                                                                ->formatStateUsing(function ($state) {
                                                                    if ($state == 1) {
                                                                        return new HtmlString('
                                                                            <div class="p-chip p-component bg-warning/10 text-warning-600" aria-label="Set as default" data-pc-name="chip" pc314="" data-pc-section="root" data-p=""><!----><div class="p-chip-label" data-pc-section="label">Default Shipping Address</div><!----></div>');
                                                                    }
                                                                    return '';
                                                                })
                                                                ->columnSpan(1),
                                                            TextEntry::make('is_same_as_billing')
                                                                ->label('')
                                                                ->badge()
                                                                ->formatStateUsing(function ($state) {
                                                                    if ($state) {
                                                                        return new HtmlString('
                                                                            <div class="p-chip p-component bg-primary-100 text-primary-800 mr-2"  style="display: inline-block; margin-right: 0.5rem;">
                                                                                <div class="p-chip-label">Same as Billing Address</div>
                                                                            </div>');
                                                                    }
                                                                    return '';
                                                                })
                                                                ->columnSpan(3),
                                                            TextEntry::make('nick_name')
                                                                // ->default(fn($record) =>  $record->shippingAddresses->isNotEmpty() 
                                                                // ? ($record->shippingAddresses->first()->nick_name ?? '-') 
                                                                // : '-')
                                                                ->default(
                                                                    fn($record) =>
                                                                    $record->shippingAddresses && $record->shippingAddresses->isNotEmpty()
                                                                        ? ($record->shippingAddresses->first()->nick_name ?? '-')
                                                                        : '-'
                                                                )
                                                                // ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                //     $output = "{$state} <br>";
                                                                //     // dd($shippingAddressChanges);
                                                                //     $address = $component->getContainer()->getParentComponent()->getRecord();
                                                                //     if (isset($shippingAddressChanges[$address->id]['nick_name'])) {
                                                                //         foreach ($shippingAddressChanges[$address->id]['nick_name'] as $change) {
                                                                //             $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                //         }
                                                                //     }

                                                                //     return new HtmlString($output);
                                                                // })
                                                                ->label('Nick Name'),
                                                            TextEntry::make('address_1')
                                                                ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                    $output = "{$state} <br>";
                                                                    // dd($shippingAddressChanges);
                                                                    $address = $component->getContainer()->getParentComponent()->getRecord();
                                                                    if (isset($shippingAddressChanges[$address->id]['address_1'])) {
                                                                        foreach ($shippingAddressChanges[$address->id]['address_1'] as $change) {
                                                                            $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                        }
                                                                    }

                                                                    return new HtmlString($output);
                                                                })
                                                                ->label('Address Line 1'),
                                                            TextEntry::make('address_2')
                                                                ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                    $output = "{$state} <br>";
                                                                    $address = $component->getContainer()->getParentComponent()->getRecord();

                                                                    if (isset($shippingAddressChanges[$address->id]['address_2'])) {
                                                                        foreach ($shippingAddressChanges[$address->id]['address_2'] as $change) {
                                                                            $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                        }
                                                                    }

                                                                    return new HtmlString($output);
                                                                })
                                                                ->label('Address Line 2'),
                                                            TextEntry::make('city.name')
                                                                ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                    $output = " $state <br>";


                                                                    $address = $component->getContainer()->getParentComponent()->getRecord();

                                                                    if (isset($shippingAddressChanges[$address->id]['city_id'])) {
                                                                        foreach ($shippingAddressChanges[$address->id]['city_id'] as $change) {
                                                                            $cityName = City::find($change['new_value'])?->name;
                                                                            if ($cityName) {
                                                                                $output .= "<span style='background-color: yellow;'>{$cityName}</span><br>";
                                                                            }
                                                                        }
                                                                    }
                                                                    return new HtmlString($output);
                                                                })
                                                                ->label('City'),
                                                            TextEntry::make('state.name')
                                                                ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                    $output = " $state <br>";
                                                                    $address = $component->getContainer()->getParentComponent()->getRecord();

                                                                    if (isset($shippingAddressChanges[$address->id]['state_id'])) {
                                                                        foreach ($shippingAddressChanges[$address->id]['state_id'] as $change) {
                                                                            $stateName = State::find($change['new_value'])?->name;
                                                                            if ($stateName) {
                                                                                $output .= "<span style='background-color: yellow;'>{$stateName}</span><br>";
                                                                            }
                                                                        }
                                                                    }

                                                                    return new HtmlString($output);
                                                                })
                                                                ->label('State'),
                                                            TextEntry::make('country.name')
                                                                ->label('Country'),
                                                            TextEntry::make('postal_code')
                                                                ->formatStateUsing(function ($state, $record, $component) use ($shippingAddressChanges) {
                                                                    $output = "{$state} <br>";
                                                                    $address = $component->getContainer()->getParentComponent()->getRecord();

                                                                    if (isset($shippingAddressChanges[$address->id]['postal_code'])) {
                                                                        foreach ($shippingAddressChanges[$address->id]['postal_code'] as $change) {
                                                                            $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                        }
                                                                    }

                                                                    return new HtmlString($output);
                                                                })
                                                                ->label('Postal Code'),

                                                        ]),
                                                ])->columns(['md' => 12])
                                                ->columns(2)
                                            // ->collapsible()
                                            // ->itemLabel(fn (array $state): ?string => $state['address_1'] ?? null)
                                        ]),
                                    // InfoSection::make('Doctor In Charge')
                                    InfoSection::make(function ($record) {
                                        $clinicAccountTypeId = $record->clinicData->clinic_account_type_id ?? null;
                                        return ($clinicAccountTypeId == 4)
                                            ? 'Pharmacy In Charge'
                                            : 'Doctor In Charge';
                                    })
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                // ->slideOver()
                                                ->icon('heroicon-o-pencil-square')
                                                ->color('gray')
                                                ->outlined()
                                                ->button()
                                                ->visible(function ($record) {
                                                    $user = auth()->user();
                                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                })
                                                ->form(function ($record) {
                                                    $clinicData = $record->clinicData?->toArray();

                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('dc_name')
                                                                ->label('Full Name')
                                                                ->placeholder('Enter full name')
                                                                ->required()->default($record->clinicData->dc_name),
                                                            TextInput::make('dc_nric')
                                                                ->placeholder('Enter NRIC / Passport Number')
                                                                ->live()
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">NRIC / Passport Number</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                </svg>
                                                            </span>'))
                                                                ->rules(['nullable', 'max:50'])->validationMessages([
                                                                    'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                                                ])->default($record->clinicData->dc_nric),
                                                            TextInput::make('dc_mmc_number')
                                                                ->live()
                                                                ->suffixIcon(fn($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->placeholder('Enter MMC registration number')
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">MMC Registration Number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                </svg>
                                                            </span>'))
                                                                ->rules(['required', 'regex:/^[a-zA-Z0-9]+$/', 'max:10', 'min:1'])->validationMessages([
                                                                    'required' => 'The MMC Registration Number field is required.',
                                                                    'regex' => 'The MMC Registration  Number must be an alphanumeric string.',
                                                                    'max' => 'The MMC Registration Number may not be greater than 10 characters.',
                                                                    'min' => 'The MMC Registration Number must be at least 1 characters.',
                                                                ])->default($record->clinicData->dc_mmc_number),
                                                            TextInput::make('dc_apc_number')
                                                                ->live()
                                                                ->visible(fn($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                                                ->suffixIcon(fn($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                                                ->placeholder('Enter current APC No')
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Current APC Number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                                    <circle cx="12" cy="12" r="10"></circle>
                                                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                                </svg>
                                                            </span>'))
                                                                ->rules(['required', 'regex:/^[a-zA-Z0-9]+$/', 'max:10', 'min:1'])->validationMessages([
                                                                    'required' => 'The Current APC No field is required.',
                                                                    'regex' => 'The Current APC  Number must be an alphanumeric string.',
                                                                    'max' => 'The Current APC Number may not be greater than 10 characters.',
                                                                    'min' => 'The Current APC Number must be at least 1 characters.',
                                                                ])->default($record->clinicData->dc_apc_number),
                                                            Select::make('clinicData.apc_certificate_expired_date')
                                                                ->visible(fn($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                                                ->label('Expiry Year of APC')->placeholder('Select commencement year')
                                                                ->options(self::getExpiryYearOptions())
                                                                ->searchable()
                                                                ->rules(['required'])->required()->default($record->clinicData->apc_certificate_expired_date),
                                                            TextInput::make('dc_phone_number')
                                                                ->numeric()
                                                                ->prefix('+60')
                                                                ->mask('************')
                                                                ->stripCharacters(['-'])
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '12'
                                                                ])
                                                                ->validationMessages([
                                                                    'digits_between' => 'The phone number must be between :min and :max characters.',
                                                                    'required' => 'The phone number field is required.',
                                                                ])
                                                                ->label(new HtmlString('<span style="font-size: 14px !important;">Mobile number</span> <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                                                ->placeholder('Mobile Number')
                                                                ->default($record->clinicData->dc_phone_number)
                                                                ->rules(['required', 'digits_between:8,12'])
                                                                ->live()
                                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null),
                                                            // TextInput::make('dc_landline_number')
                                                            //     ->numeric()
                                                            //     ->prefix('+03')
                                                            //     ->placeholder('Enter landline number')
                                                            //     ->mask('9-9999999')
                                                            //     ->stripCharacters(['-'])
                                                            //     ->extraAttributes([
                                                            //         'inputmode' => 'numeric',
                                                            //         'maxlength' => '8'
                                                            //     ])
                                                            //     ->rules(['digits_between:7,8'])
                                                            //     ->label('Landline Number')->default($record->clinicData->dc_landline_number),


                                                            PhoneWithPrefix::make('dc_landline_number')
                                                                ->label("Landline Number")
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '8'
                                                                ])
                                                                ->default(function (Get $get) use ($clinicData) {
                                                                    if (!empty($clinicData)) {
                                                                        $prefix = $clinicData["dc_landline_code"] ?? null;
                                                                        $number = $clinicData["dc_landline_number"] ?? null;
                                                                    } else {
                                                                        $prefix = $get('addresses')[0]["landline_code"] ?? null;
                                                                        $number = $get('addresses')[0]["dc_landline_number"] ?? null;
                                                                    }

                                                                    return [
                                                                        "prefix" => $prefix ?? '', // can fallback to first City landline code if needed
                                                                        "number" => $number,
                                                                    ];
                                                                })
                                                                ->prefixOptions(function ($get, $set) {
                                                                    if (empty($get('dc_landline_number'))) {
                                                                        return [];
                                                                    }
                                                                    $query = City::whereNotNull('landline_code')
                                                                        ->where('landline_code', '!=', '');
                                                                    $stateId = $get('state_id');
                                                                    $cityId = $get('city_id');
                                                                    if ($stateId) {
                                                                        $query->where('state_id', $stateId);

                                                                        if ($cityId) {
                                                                            $query->where('id', $cityId);
                                                                        }
                                                                    }


                                                                    $data = $query
                                                                        ->distinct('landline_code')
                                                                        ->pluck('landline_code', 'landline_code')
                                                                        ->toArray();
                                                                    if (empty($data)) {
                                                                        $data = City::whereNotNull('landline_code')
                                                                            ->where('landline_code', '!=', '')
                                                                            ->distinct('landline_code')
                                                                            ->pluck('landline_code', 'landline_code')
                                                                            ->toArray();
                                                                    }
                                                                    // FacadesLog::info($get('addresses'));
                                                                    if (isset($get('addresses')["landline_code"]) && $get('addresses')["dc_landline_code"] != null) {
                                                                        $set('dc_landline_code.prefix', $get("addresses")["dc_landline_code"]);
                                                                    }
                                                                    return $data;
                                                                })
                                                                ->rules([new PhoneWithPrefixRule()])
                                                                ->afterStateHydrated(function (Get $get, Set $set) {
                                                                    $set("dc_landline_code", implode(" ", $get("dc_landline_number")));
                                                                })
                                                                ->afterStateUpdated(function (Get $get, Set $set) {
                                                                    $set("dc_landline_code", implode(" ", $get("dc_landline_number")));
                                                                })
                                                                ->formatStateUsing(function ($state) use ($clinicData) {

                                                                    if (is_array($clinicData)) {
                                                                        if (isset($clinicData['dc_landline_code'])) {
                                                                            $data['prefix'] = $clinicData['dc_landline_code'];
                                                                        }
                                                                        if (isset($clinicData['dc_landline_number'])) {
                                                                            $data['number'] = $clinicData['dc_landline_number'];
                                                                        }
                                                                    }
                                                                    // Or if $address is an object
                                                                    elseif (is_object($clinicData)) {
                                                                        if (isset($clinicData->landline_code)) {
                                                                            $data['prefix'] = $clinicData->landline_code;
                                                                        }
                                                                        if (isset($clinicData->landline_number)) {
                                                                            $data['number'] = $clinicData->landline_number;
                                                                        }
                                                                    }
                                                                    // dd($data);
                                                                    return is_array($state) ? $state : $data;
                                                                })
                                                                ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                                            // Radio::make('signature_type')->options([
                                                            //     1 => 'Digital',
                                                            //     0 => 'Image',
                                                            // ])->live()
                                                            //     ->rules(['required'])
                                                            //     ->required()
                                                            //     ->inline()
                                                            //     ->label('Signature Type')
                                                            //     ->default($record->clinicData?->signature_type ? 1 : 0)
                                                            //     ->columnSpan('full'),

                                                            ViewField::make('previous_signature')
                                                                ->view('filament.admin.resources.clinic-resource.pages.view-previous-sginature', [
                                                                    // 'signatureData' => asset('storage/images/clinic/' . $record->clinicData->id . '/' . $record->clinicData->dc_signature),
                                                                    'signatureData' => getImage($record->clinicData->dc_signature, '/images/clinic/' . $record->clinicData->id)
                                                                ]),


                                                            // SignaturePad::make('signature')
                                                            //     ->visible(fn($get) => $get('signature_type'))
                                                            //     ->undoable(false)     // Background color on export (defaults to backgroundColor)
                                                            //     ->filename('autograph')             // Filename of the downloaded file (defaults to 'signature')
                                                            //     ->required(fn($get, $record) => $get('signature_type') == 1 && !$record->clinicData?->dc_signature)
                                                            //     ->rules(['required_if:signature_type,1']),
                                                            FileUpload::make('dc_signature')
                                                                // ->visible(fn($get) => $get('signature_type') == 0)
                                                                // ->label('Signature of Doctor In-charge')

                                                                ->label(function ($record) {
                                                                    $clinicAccountTypeId = $record->clinicData->clinic_account_type_id ?? null;
                                                                    $label = ($clinicAccountTypeId == 4)
                                                                        ? 'Signature of Pharmacy In-charge'
                                                                        : 'Signature of Doctor In-charge';

                                                                    return $label;
                                                                })
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id . '/';
                                                                }) // This specifies the storage directory
                                                                ->getUploadedFileNameForStorageUsing(
                                                                    fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                                                        ->prepend('dc_signature_'),
                                                                )
                                                                ->previewable()
                                                                ->required(empty($record->clinicData->dc_signature))
                                                                ->rules(['required'])->default($record->clinicData->dc_signature),

                                                            // Toggle::make('is_active')->default($record->is_active),
                                                        ])->columns(3)
                                                    ];
                                                })
                                                ->action(function ($record, $data, Form $form) {
                                                    $landlineNumber = $data['dc_landline_number']['number'] ?? null;
                                                    $landlineCode = $data['dc_landline_number']['prefix'] ?? null;
                                                    $data['dc_landline_number'] = $landlineNumber;
                                                    $data['dc_landline_code'] = $landlineCode;
                                                    $form->fill();

                                                    if (isset($data['dc_signature']) && str_contains($data['dc_signature'], '/')) {
                                                        $data['dc_signature'] = basename($data['dc_signature']);
                                                    } else {
                                                        // No new signature - keep existing one
                                                        $data['dc_signature'] = $record->clinicData->dc_signature;
                                                    }
                                                    // $record->update(Arr::only($data, ['name', 'is_active', 'email', 'phone']));
                                                    $clinicData = Arr::only($data, [
                                                        'company_name',
                                                        'dc_name',
                                                        'dc_nric',
                                                        'dc_mmc_number',
                                                        'dc_apc_number',
                                                        'dc_phone_number',
                                                        'dc_landline_number',
                                                        'dc_landline_code',
                                                        'signature_type',
                                                        'dc_signature'
                                                    ]);

                                                    //Activity Log Start
                                                    // $oldValues = [
                                                    //     'dc_name' => $record->clinicData?->dc_name,
                                                    //     'dc_nric' => $record->clinicData?->dc_nric,
                                                    //     'dc_mmc_number' => $record->clinicData?->dc_mmc_number,
                                                    //     'dc_apc_number' => $record->clinicData?->dc_apc_number,
                                                    //     'dc_phone_number' => $record->clinicData?->dc_phone_number,
                                                    //     'dc_landline_number' => $record->clinicData?->dc_landline_number,
                                                    //     'dc_landline_code' => $record->clinicData?->dc_landline_code,
                                                    // ];
                                                    //Activity Log End


                                                    if (!empty($clinicData)) {
                                                        $record->clinicData->updateOrCreate([
                                                            'user_id' => $record->id
                                                        ], $clinicData);
                                                    }

                                                    //Activity Log Start

                                                    // $changedOld = [];
                                                    // $changedNew = [];

                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     $newValue = $data[$key] ?? null;
                                                    //     if ((string) $oldValue !== (string) $newValue) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValue;
                                                    //     }
                                                    // }
                                                    // if (!empty($changedNew)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('contact_details_update')
                                                    //         ->performedOn($record->clinicData ?? $record)
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Doctor in charge of facility {$record->clinicData?->clinic_name} have been updated");
                                                    // }
                                                    //Activity Log End

                                                }),

                                            static::makeAcceptAction(OnboardingStep::DOCTOR_INCHARGE->value),
                                            static::makeRejectAction(OnboardingStep::DOCTOR_INCHARGE->value),
                                            // \Filament\Infolists\Components\Actions\Action::make('accept')
                                            //     ->label('Accept Admin/Doctor In-charge')
                                            //     ->color('success')
                                            //     ->outlined()
                                            //     ->button()
                                            //     ->requiresConfirmation()
                                            //     ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::DOCTOR_INCHARGE)->where('approvalable_id', $record->id)->exists())
                                            //     ->modalHeading('Confirm Approval')
                                            //     ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                                            //     ->modalSubmitActionLabel('Confirm')
                                            //     ->action(function ($record) {
                                            //         $userId = $record->id;
                                            //         $steps = 3;

                                            //         $approvalData = Approval::where('approvalable_id', $userId)
                                            //             ->where('approvalable_type', 'App\Models\User')
                                            //             ->where('steps', $steps)
                                            //             ->where('approved_by', null)
                                            //             ->where('approved_at', null)
                                            //             ->where('rejected_at', null)
                                            //             ->where('rejected_by', null)
                                            //             ->latest()
                                            //             ->first();

                                            //         if ($approvalData) {
                                            //             $newData = json_decode($approvalData->new_data, true);

                                            //             // Update clinic_details table
                                            //             $record->clinicData->updateOrCreate(
                                            //                 ['user_id' => $userId],
                                            //                 Arr::only($newData, [
                                            //                     'dc_name',
                                            //                     'dc_nric',
                                            //                     'dc_mmc_number',
                                            //                     'dc_apc_number',
                                            //                     'dc_phone_number',
                                            //                     'dc_landline_number',
                                            //                     'signature_type',
                                            //                     'ac_name',
                                            //                     'ac_nric',
                                            //                     'ac_phone_number',
                                            //                     'ac_landline_number',
                                            //                 ])
                                            //             );

                                            //             // Update approvals table with approved_at and approved_by
                                            //             $approvalData->update([
                                            //                 'approved_at' => now(),
                                            //                 'approved_by' => auth()->id(), // Assuming you're using authentication
                                            //             ]);
                                            //         }

                                            //         // Update verification status
                                            //         // $record->update(['verification_status' => 'approved']);
                                            //         Mail::to($record->email)->send(new ChangesApprovedMail($record));
                                            //         Notification::make()
                                            //             ->title('Changes Approved Successfully')
                                            //             ->success()
                                            //             ->send();
                                            //     }),
                                            // \Filament\Infolists\Components\Actions\Action::make('reject')
                                            //     ->label('Reject Admin/Doctor In-charge')
                                            //     ->color('danger')
                                            //     ->outlined()
                                            //     ->button()
                                            //     ->requiresConfirmation()
                                            //     ->visible(fn($record) => Approval::pendingForSteps(OnboardingStep::DOCTOR_INCHARGE)->where('approvalable_id', $record->id)->exists())
                                            //     ->modalHeading('Confirm Rejection')
                                            //     ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                                            //     ->modalSubmitActionLabel('Confirm')
                                            //     ->action(function ($record) {
                                            //         $userId = $record->id;
                                            //         $steps = 3;

                                            //         $approvalData = Approval::where('approvalable_id', $userId)
                                            //             ->where('approvalable_type', 'App\Models\User')
                                            //             ->where('steps', $steps)
                                            //             ->where('approved_by', null)
                                            //             ->where('approved_at', null)
                                            //             ->where('rejected_at', null)
                                            //             ->where('rejected_by', null)
                                            //             ->latest()
                                            //             ->first();

                                            //         if ($approvalData) {
                                            //             $approvalData->update([
                                            //                 'rejected_at' => now(),
                                            //                 'rejected_by' => auth()->id(),
                                            //             ]);
                                            //             Mail::to($record->email)->send(new ChangesRejectedMail($record));
                                            //             Notification::make()
                                            //                 ->title('Changes Rejected Successfully')
                                            //                 ->success()
                                            //                 ->send();
                                            //         } else {
                                            //             Notification::make()
                                            //                 ->title('No Data Found For Rejection')
                                            //                 ->danger()
                                            //                 ->send();
                                            //         }
                                            //     }),
                                        ])
                                        ->schema([
                                            \Filament\Infolists\Components\Grid::make([
                                                'default' => 4,
                                                'sm' => 4,
                                            ])
                                                ->columnSpan('full')
                                                ->extraAttributes(['class' => 'gap-0'])
                                                ->schema([

                                                    TextEntry::make('clinicData.dc_name')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $output = " $state <br>";
                                                            // dd($doctorChanges);
                                                            if (isset($doctorChanges['dc_name'])) {
                                                                foreach ($doctorChanges['dc_name'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Full Name'),
                                                    TextEntry::make('clinicData.dc_nric')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['dc_nric'])) {
                                                                foreach ($doctorChanges['dc_nric'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('NRIC / Passport Number'),
                                                    TextEntry::make('clinicData.dc_mmc_number')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['dc_mmc_number'])) {
                                                                foreach ($doctorChanges['dc_mmc_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('MMC Registration Number'),
                                                    TextEntry::make('clinicData.dc_apc_number')
                                                        ->visible(fn($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            // $state = substr($state, 0, 4) . '/' . substr($state, 4);

                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['dc_apc_number'])) {
                                                                foreach ($doctorChanges['dc_apc_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Current APC Number'),
                                                    TextEntry::make('clinicData.apc_certificate_expired_date')
                                                        ->visible(fn($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            // $state = substr($state, 0, 4) . '/' . substr($state, 4);

                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['apc_certificate_expired_date'])) {
                                                                foreach ($doctorChanges['apc_certificate_expired_date'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Expiry Year of APC'),
                                                    TextEntry::make('clinicData.dc_phone_number')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            // $state = substr($state, 0, 2) . '-' . substr($state, 2);
                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['dc_phone_number'])) {
                                                                foreach ($doctorChanges['dc_phone_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->prefix('+60 ')
                                                        ->label('Mobile Number'),
                                                    TextEntry::make('clinicData.dc_landline_number')
                                                        // ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                        //     // $state = substr($state, 0, 1) . '-' . substr($state, 1);
                                                        //     $output = " $state <br>";

                                                        //     if (isset($doctorChanges['dc_landline_number'])) {
                                                        //         foreach ($doctorChanges['dc_landline_number'] as $change) {
                                                        //             $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                        //         }
                                                        //     }

                                                        //     return new HtmlString($output);
                                                        // })
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $currentValue = ($record->clinicData->dc_landline_code ? '+' . $record->clinicData->dc_landline_code . ' ' : '') .
                                                                ($state ?? '-');

                                                            $landlineChanges = [
                                                                'number' => $doctorChanges['dc_landline_number'] ?? null,
                                                                'code' => $doctorChanges['dc_landline_code'] ?? null
                                                            ];

                                                            return static::formatLandLinePendingChanges(
                                                                $currentValue,
                                                                $landlineChanges
                                                            );
                                                        })
                                                        // ->prefix($record->clinicData->dc_landline_code ? "+{$record->clinicData->dc_landline_code} " : '+03 ')
                                                        ->label('Landline Number'),
                                                    TextEntry::make('clinicData.dc_signature')
                                                        // ->label('Signature')
                                                        ->label(function ($record) {
                                                            $clinicAccountTypeId = $record->clinicData->clinic_account_type_id ?? null;
                                                            $label = ($clinicAccountTypeId == 4)
                                                                ? 'Signature of Pharmacy In Charge'
                                                                : 'Signature of Doctor In Charge';

                                                            return $label;
                                                        })
                                                        ->formatStateUsing(function ($state, $record) {
                                                            // dd($record->clinicData->id);
                                                            return view('filament.admin.resources.clinic-resource.pages.view-signature', [
                                                                //'signatureData' => asset('storage/images/clinic/' . $record->clinicData->id . '/' . $record->clinicData->dc_signature),
                                                                'signatureData' => getImage($record->clinicData->dc_signature, '/images/clinic/' . $record->clinicData->id),

                                                                'altText' => 'Doctor Signature'
                                                            ]);
                                                        })
                                                        ->html(),
                                                ])
                                        ])->columns(2),
                                    InfoSection::make('Admin In Charge')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                // ->slideOver()
                                                ->icon('heroicon-o-pencil-square')
                                                ->color('gray')
                                                ->visible(function ($record) {
                                                    $user = auth()->user();
                                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                })
                                                ->outlined()
                                                ->button()
                                                ->form(function ($record) {
                                                    $clinicData = $record->clinicData?->toArray();
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('ac_name')
                                                                ->label(new HtmlString('Full Name <span style="color:red">*</span>'))
                                                                ->rules(['required'])
                                                                ->validationMessages([
                                                                    'required' => 'The full name field is required.',
                                                                ])
                                                                ->placeholder('Enter full name')
                                                                ->default($record->clinicData->ac_name),
                                                            TextInput::make('ac_nric')
                                                                ->placeholder('Enter NRIC / Passport Number')
                                                                ->live()
                                                                ->suffixIcon(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                                                ->label(new HtmlString(
                                                                    'NRIC / Passport Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`National Registration Identity Card (NRIC) Number is a unique personal identification number assigned to Malaysian citizens and permanent residents.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->rules(['nullable', 'max:50'])->validationMessages([
                                                                    'max' => 'The NRIC / Passport Number may not be more than 50 characters.',

                                                                ])->default($record->clinicData->ac_nric),

                                                            TextInput::make('ac_phone_number')
                                                                ->numeric()
                                                                ->prefix('+60')
                                                                ->mask('************')
                                                                ->stripCharacters(['-'])
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '12'
                                                                ])
                                                                ->live()
                                                                ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                                                ->label(new HtmlString('Mobile number <span style="color:red">*</span>'))
                                                                ->placeholder('Mobile Number')
                                                                ->validationMessages([
                                                                    'required' => 'The Mobile Number field is required.',
                                                                    'digits_between' => 'The Mobile Number must be between 8 and 12 digits.',
                                                                ])
                                                                ->default($record->clinicData->ac_phone_number)
                                                                ->rules(['required', 'digits_between:8,12']),
                                                            // TextInput::make('ac_landline_number')
                                                            //     ->numeric()->rules(['digits_between:7,8'])
                                                            //     ->mask('9-9999999')
                                                            //     ->prefix('+03')
                                                            //     ->stripCharacters(['-'])
                                                            //     ->extraAttributes([
                                                            //         'inputmode' => 'numeric',
                                                            //         'maxlength' => '8'
                                                            //     ])
                                                            //     ->validationMessages([
                                                            //         'digits_between' => 'The Landline Number must be between 7 and 8 digits.',
                                                            //     ])
                                                            //     ->placeholder('Enter landline number')
                                                            //     ->label('Landline Number')->default($record->clinicData->ac_landline_number),

                                                            PhoneWithPrefix::make('ac_landline_number')
                                                                ->label("Landline Number")
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'maxlength' => '8'
                                                                ])
                                                                ->default(function (Get $get) use ($clinicData) {
                                                                    if (!empty($clinicData)) {
                                                                        $prefix = $clinicData["ac_landline_code"] ?? null;
                                                                        $number = $clinicData["ac_landline_number"] ?? null;
                                                                    } else {
                                                                        $prefix = $get('addresses')[0]["ac_landline_code"] ?? null;
                                                                        $number = $get('addresses')[0]["ac_landline_number"] ?? null;
                                                                    }

                                                                    return [
                                                                        "prefix" => $prefix ?? '', // can fallback to first City landline code if needed
                                                                        "number" => $number,
                                                                    ];
                                                                })
                                                                ->prefixOptions(function ($get, $set) {
                                                                    if (empty($get('ac_landline_number'))) {
                                                                        return [];
                                                                    }
                                                                    $query = City::whereNotNull('landline_code')
                                                                        ->where('landline_code', '!=', '');
                                                                    $stateId = $get('state_id');
                                                                    $cityId = $get('city_id');
                                                                    if ($stateId) {
                                                                        $query->where('state_id', $stateId);

                                                                        if ($cityId) {
                                                                            $query->where('id', $cityId);
                                                                        }
                                                                    }


                                                                    $data = $query
                                                                        ->distinct('landline_code')
                                                                        ->pluck('landline_code', 'landline_code')
                                                                        ->toArray();
                                                                    if (empty($data)) {
                                                                        $data = City::whereNotNull('landline_code')
                                                                            ->where('landline_code', '!=', '')
                                                                            ->distinct('landline_code')
                                                                            ->pluck('landline_code', 'landline_code')
                                                                            ->toArray();
                                                                    }
                                                                    // FacadesLog::info($get('addresses'));
                                                                    if (isset($get('addresses')["landline_code"]) && $get('addresses')["ac_landline_code"] != null) {
                                                                        $set('ac_landline_code.prefix', $get("addresses")["ac_landline_code"]);
                                                                    }
                                                                    return $data;
                                                                })
                                                                ->rules([new PhoneWithPrefixRule()])
                                                                ->afterStateHydrated(function (Get $get, Set $set) {
                                                                    $set("ac_landline_code", implode(" ", $get("ac_landline_number")));
                                                                })
                                                                ->afterStateUpdated(function (Get $get, Set $set) {
                                                                    $set("ac_landline_code", implode(" ", $get("ac_landline_number")));
                                                                })
                                                                ->formatStateUsing(function ($state) use ($clinicData) {

                                                                    if (is_array($clinicData)) {
                                                                        if (isset($clinicData['ac_landline_code'])) {
                                                                            $data['prefix'] = $clinicData['ac_landline_code'];
                                                                        }
                                                                        if (isset($clinicData['ac_landline_number'])) {
                                                                            $data['number'] = $clinicData['ac_landline_number'];
                                                                        }
                                                                    }
                                                                    // Or if $address is an object
                                                                    elseif (is_object($clinicData)) {
                                                                        if (isset($clinicData->ac_landline_code)) {
                                                                            $data['prefix'] = $clinicData->ac_landline_code;
                                                                        }
                                                                        if (isset($clinicData->ac_landline_number)) {
                                                                            $data['number'] = $clinicData->ac_landline_number;
                                                                        }
                                                                    }
                                                                    // dd($data);
                                                                    return is_array($state) ? $state : $data;
                                                                })
                                                                ->suffixIcon(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                                ->suffixIconColor(fn($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null),
                                                            // Toggle::make('is_active')->default($record->is_active),
                                                        ])->columns(2)
                                                    ];
                                                })
                                                ->action(function ($record, $data, Form $form) {
                                                    $form->fill();
                                                    $landlineNumber = $data['ac_landline_number']['number'] ?? null;
                                                    $landlineCode = $data['ac_landline_number']['prefix'] ?? null;
                                                    $data['ac_landline_number'] = $landlineNumber;
                                                    $data['ac_landline_code'] = $landlineCode;
                                                    // $record->update(Arr::only($data, ['name', 'is_active', 'email', 'phone']));
                                                    $clinicData = Arr::only($data, [
                                                        'ac_name',
                                                        'ac_nric',
                                                        'ac_phone_number',
                                                        'ac_landline_number',
                                                        'ac_landline_code',
                                                    ]);

                                                    //Activity Log Start
                                                    // $oldValues = [
                                                    //     'ac_name' => $record->clinicData?->ac_name,
                                                    //     'ac_nric' => $record->clinicData?->ac_nric,
                                                    //     'ac_phone_number' => $record->clinicData?->ac_phone_number,
                                                    //     'ac_landline_number' => $record->clinicData?->ac_landline_number,
                                                    //     'ac_landline_code' => $record->clinicData?->ac_landline_code,
                                                    // ];
                                                    //Activity Log End

                                                    if (!empty($clinicData)) {
                                                        $record->clinicData->updateOrCreate([
                                                            'user_id' => $record->id
                                                        ], $clinicData);
                                                    }

                                                    //Activity Log Start
                                                    // $changedOld = [];
                                                    // $changedNew = [];

                                                    // foreach ($oldValues as $key => $oldValue) {
                                                    //     $newValue = $data[$key] ?? null;
                                                    //     if ((string) $oldValue !== (string) $newValue) {
                                                    //         $changedOld[$key] = $oldValue;
                                                    //         $changedNew[$key] = $newValue;
                                                    //     }
                                                    // }
                                                    // if (!empty($changedNew)) {
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('admin_incharge_update')
                                                    //         ->performedOn($record->clinicData ?? $record)
                                                    //         ->withProperties([
                                                    //             'old' => $changedOld,
                                                    //             'attributes' => $changedNew,
                                                    //         ])
                                                    //         ->log("Admin in charge of facility {$record->clinicData?->clinic_name} have been updated");
                                                    // }
                                                    //Activity Log End

                                                }),
                                            // \Filament\Infolists\Components\Actions\Action::make('accept')
                                            // ->color('success')
                                            // ->outlined()
                                            // ->button()
                                            // ->requiresConfirmation()
                                            // ->modalHeading('Confirm Approval')
                                            // ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
                                            // ->modalSubmitActionLabel('Confirm')
                                            // ->action(function ($record) {
                                            //     $userId = $record->id;
                                            //     $steps = 3;

                                            //     $approvalData = Approval::where('approvalable_id', $userId)
                                            //         ->where('approvalable_type', 'App\Models\User')
                                            //         ->where('steps', $steps)
                                            //         ->where('approved_by', null)
                                            //         ->where('approved_at', null)
                                            //         ->where('rejected_at', null)
                                            //         ->where('rejected_by', null)
                                            //         ->latest()
                                            //         ->first();

                                            //     if ($approvalData) {
                                            //         $newData = json_decode($approvalData->new_data, true);

                                            //         // Update clinic_details table
                                            //         $record->clinicData->updateOrCreate(
                                            //             ['user_id' => $userId],
                                            //             Arr::only($newData, [
                                            //                 'ac_name',
                                            //                 'ac_nric',
                                            //                 'ac_phone_number',
                                            //                 'ac_landline_number',
                                            //             ])
                                            //         );

                                            //         // Update approvals table with approved_at and approved_by
                                            //         $approvalData->update([
                                            //             'approved_at' => now(),
                                            //             'approved_by' => auth()->id(), // Assuming you're using authentication
                                            //         ]);
                                            //     }

                                            //     // Update verification status
                                            //     // $record->update(['verification_status' => 'approved']);

                                            //     Notification::make()
                                            //         ->title('Changes Approved Successfully')
                                            //         ->success()
                                            //         ->send();
                                            // }),
                                            // \Filament\Infolists\Components\Actions\Action::make('reject')
                                            //     ->color('danger')
                                            //     ->outlined()
                                            //     ->button()
                                            //     ->requiresConfirmation()
                                            //     ->modalHeading('Confirm Rejection')
                                            //     ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
                                            //     ->modalSubmitActionLabel('Confirm')
                                            //     ->action(function ($record) {
                                            //         $userId = $record->id;
                                            //         $steps = 3;

                                            //         $approvalData = Approval::where('approvalable_id', $userId)
                                            //             ->where('approvalable_type', 'App\Models\User')
                                            //             ->where('steps', $steps)
                                            //             ->where('approved_by', null)
                                            //             ->where('approved_at', null)
                                            //             ->where('rejected_at', null)
                                            //             ->where('rejected_by', null)
                                            //             ->latest()
                                            //             ->first();

                                            //         if ($approvalData) {
                                            //             $approvalData->update([
                                            //                 'rejected_at' => now(),
                                            //                 'rejected_by' => auth()->id(),
                                            //             ]);
                                            //             Notification::make()
                                            //             ->title('Changes Rejected Successfully')
                                            //             ->success()
                                            //             ->send();
                                            //         } else {
                                            //             Notification::make()
                                            //             ->title('No Data Found For Rejection')
                                            //             ->danger()
                                            //             ->send();
                                            //         }

                                            //     }),
                                        ])
                                        ->schema([
                                            \Filament\Infolists\Components\Grid::make([
                                                'default' => 4,
                                                'sm' => 4,
                                            ])
                                                ->columnSpan('full')
                                                ->extraAttributes(['class' => 'gap-0'])
                                                ->schema([
                                                    TextEntry::make('clinicData.is_admin_in_charge')
                                                        ->label('')
                                                        ->badge()
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            if ($state) {
                                                                return new HtmlString('
                                                                    <div class="p-chip p-component bg-primary-100 text-primary-800 mr-2"  style="display: inline-block; margin-right: 0.5rem;">
                                                                        <div class="p-chip-label">Same as Doctor in charge</div>
                                                                    </div>');
                                                            }
                                                            return '';
                                                        })
                                                        ->columnSpan('full'),
                                                    TextEntry::make('clinicData.ac_name')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $output = " $state <br>";
                                                            // dd($adminChanges);
                                                            if (isset($doctorChanges['ac_name'])) {
                                                                foreach ($doctorChanges['ac_name'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('Full Name'),
                                                    TextEntry::make('clinicData.ac_nric')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['ac_nric'])) {
                                                                foreach ($doctorChanges['ac_nric'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('NRIC / Passport Number'),
                                                    TextEntry::make('clinicData.ac_phone_number')
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            // $state = substr($state, 0, 2) . '-' . substr($state, 2);
                                                            $output = " $state <br>";

                                                            if (isset($doctorChanges['ac_phone_number'])) {
                                                                foreach ($doctorChanges['ac_phone_number'] as $change) {
                                                                    $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                                }
                                                            }

                                                            return new HtmlString($output);
                                                        })
                                                        ->label('NRIC')
                                                        ->prefix('+60 ')->label('Mobile Number'),
                                                    TextEntry::make('clinicData.ac_landline_number')
                                                        // ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                        //     // $state = substr($state, 0, 1) . '-' . substr($state, 1);
                                                        //     $output = " $state <br>";

                                                        //     if (isset($doctorChanges['ac_landline_number'])) {
                                                        //         foreach ($doctorChanges['ac_landline_number'] as $change) {
                                                        //             $output .= "<span style='background-color: yellow;'>{$change['new_value']}</span><br>";
                                                        //         }
                                                        //     }

                                                        //     return new HtmlString($output);
                                                        // })
                                                        ->formatStateUsing(function ($state, $record) use ($doctorChanges) {
                                                            $currentValue = ($record->clinicData->ac_landline_code ? '+' . $record->clinicData->ac_landline_code . ' ' : '') .
                                                                ($state ?? '-');

                                                            $landlineChanges = [
                                                                'number' => $doctorChanges['ac_landline_number'] ?? null,
                                                                'code' => $doctorChanges['ac_landline_code'] ?? null
                                                            ];

                                                            return static::formatLandLinePendingChanges(
                                                                $currentValue,
                                                                $landlineChanges
                                                            );
                                                        })
                                                        // ->prefix($record->clinicData->ac_landline_code ? "+{$record->clinicData->ac_landline_code} " : '+03 ')

                                                        ->label('Landline Number'),
                                                ])
                                        ])->columns(2),
                                    InfoSection::make('Uploaded Documents')
                                        ->description(function ($record) use ($documentChanges) {
                                            $pendingFiles = (new static())->getPendingFilenames($documentChanges['other_relevant_documents'] ?? []);
                                            $existingFiles = $record->clinicData?->otherRelevantDocuments?->pluck('name')->all() ?? [];

                                            if (!empty($pendingFiles) && count($existingFiles) === 0) {
                                                return '⚠️  The facility has been uploaded additional documents under "Other Relevant Documents".';
                                            }

                                            return null;
                                        })
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('edit')
                                                ->color('gray')
                                                ->outlined()
                                                ->visible(function ($record) {
                                                    $user = auth()->user();
                                                    return $user->hasRole('Super Admin') || $user->can('facility_update');
                                                })
                                                ->button()
                                                ->icon('heroicon-o-pencil-square')
                                                ->action(function ($record, $data) {
                                                    $clinicData = $record->clinicData;
                                                    $types = [
                                                        'borang_certificate',
                                                        'mmc_certificate',
                                                        'apc_certificate',
                                                        'arc_certificate',
                                                        'poison_license',
                                                        'other_relevant_documents',
                                                    ];

                                                    foreach ($types as $type) {
                                                        if (isset($data['clinicData'][$type])) {
                                                            // Deactivate all previous active files for this type
                                                            ClinicCertificateFile::where('user_id', $record->id)
                                                                ->where('type', $type)
                                                                ->where('status', 'active')
                                                                ->update(['status' => 'inactive']);

                                                            $files = $data['clinicData'][$type];
                                                            // Ensure $files is always an array
                                                            if (!is_array($files)) {
                                                                $files = [$files];
                                                            }
                                                            foreach ($files as $filePath) {
                                                                if ($filePath) {
                                                                    $fileName = basename($filePath);
                                                                    ClinicCertificateFile::create([
                                                                        'user_id' => $record->id,
                                                                        'type' => $type,
                                                                        'name' => $fileName,
                                                                        'status' => 'active',
                                                                    ]);
                                                                }
                                                            }
                                                        }
                                                    }

                                                    //Activity Log Start
                                                    // foreach ($types as $type) {
                                                    //     if (isset($data['clinicData'][$type])) {
                                                    //         $filePath = $data['clinicData'][$type];
                                                    //         $fileName = basename($filePath);

                                                    //         $currentFileName = ClinicCertificateFile::where('user_id', $record->id)
                                                    //             ->where('type', $type)
                                                    //             ->where('status', 'active')
                                                    //             ->value('name');

                                                    //         if ($currentFileName !== $fileName) {
                                                    //             ClinicCertificateFile::where('user_id', $record->id)
                                                    //                 ->where('type', $type)
                                                    //                 ->where('status', 'active')
                                                    //                 ->update(['status' => 'inactive']);

                                                    //             ClinicCertificateFile::create([
                                                    //                 'user_id' => $record->id,
                                                    //                 'type' => $type,
                                                    //                 'name' => $fileName,
                                                    //                 'status' => 'active',
                                                    //             ]);

                                                    //             $changedFields[] = $certificateLabels[$type];
                                                    //         }
                                                    //     }
                                                    // }

                                                    // if (!empty($changedFields)) {
                                                    //     $clinicName = $record->clinicData ? $record->clinicData->clinic_name : ' ';
                                                    //     $changedFieldsList = implode(', ', $changedFields);
                                                    //     activity()
                                                    //         ->causedBy(auth()->user())
                                                    //         ->useLog('certificate_update')
                                                    //         ->performedOn($record->clinicData ?? $record)
                                                    //         ->log("Updated certificates for facility $clinicName: $changedFieldsList");
                                                    // }
                                                    //Activity Log End
                                                })
                                                ->form(function ($record) {
                                                    // Fetch all active files for each type as arrays
                                                    $borangCertificates = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'borang_certificate')->pluck('name')->all();
                                                    $mmcCertificates = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'mmc_certificate')->pluck('name')->all();
                                                    $apcCertificates = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'apc_certificate')->pluck('name')->all();
                                                    $arcCertificates = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'arc_certificate')->pluck('name')->all();
                                                    $poisonLicenseCertificates = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'poison_license')->pluck('name')->all();
                                                    $otherRelevantDocuments = ClinicCertificateFile::where('user_id', $record->id)->where('status', 'active')->where('type', 'other_relevant_documents')->pluck('name')->all();
                                                    $clinic_account_type_id =  $record->clinicData?->clinic_account_type_id;

                                                    return [
                                                        Group::make()->schema([
                                                            FileUpload::make('clinicData.borang_certificate')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->label(new HtmlString(
                                                                    'Certificate of Registration (Account Holder as per Form B/F.) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Borang B” and “Borang F” are official certificates under the Private Healthcare Facilities and Services Act 1998 in Malaysia. They verify that your healthcare facility is properly registered with the Ministry of Health. Please upload a valid copy of the relevant certificate (Borang B or Borang F) to confirm your facility’s registration status.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The borang certificate is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF, MP4, MOV, AVI files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])->required()
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $borangCertificates
                                                                    )
                                                                ),

                                                            FileUpload::make('clinicData.mmc_certificate')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->visible(in_array($clinic_account_type_id, [1, 2, 6]))
                                                                ->label(new HtmlString(
                                                                    'MMC Registration Certificate for Person In-charge <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Malaysian Medical Council (MMC) Number is assigned to licensed medical practitioners in Malaysia for professional recognition.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The mmc certificate is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])->required()
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $mmcCertificates
                                                                    )
                                                                ),

                                                            FileUpload::make('clinicData.apc_certificate')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->visible(in_array($clinic_account_type_id, [1, 2, 3, 7, 6, 8]))
                                                                ->label(new HtmlString(
                                                                    'Current Annual Practicing Certificate (APC) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`The Current Annual Practicing Certificate (APC) is a mandatory document certifying that a medical professional is licensed to practice for the ongoing year.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The apc certificate is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])->required()
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $apcCertificates
                                                                    )
                                                                ),

                                                            FileUpload::make('clinicData.arc_certificate')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->visible(in_array($clinic_account_type_id, [4, 5]))
                                                                ->label(new HtmlString(
                                                                    'Annual Retention Certificate (ARC) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Annual Retention Certificate (ARC)`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The arc certificate is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])->required()
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $arcCertificates
                                                                    )
                                                                ),

                                                            FileUpload::make('clinicData.poison_license')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->visible(in_array($clinic_account_type_id, [4, 5]))
                                                                ->label(new HtmlString(
                                                                    'Poison License <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Poison  License`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The poison license certificate is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])->required()
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $poisonLicenseCertificates
                                                                    )
                                                                ),

                                                            FileUpload::make('clinicData.other_relevant_documents')
                                                                ->multiple()
                                                                ->maxFiles(3)
                                                                ->rules([
                                                                    File::types(['jpg', 'jpeg', 'png', 'pdf'])
                                                                        ->max(25 * 1024) // 25MB
                                                                ])
                                                                ->label(new HtmlString(
                                                                    'Other Relevant Documents <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Other Relevant Documents`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                                    </svg>'
                                                                ))
                                                                ->downloadable()
                                                                ->directory(function (Get $get, $record) {
                                                                    return config('constants.api.media.clinic_medias') . $record->clinicData->id;
                                                                })
                                                                ->validationMessages([
                                                                    'required' => 'The other relevant documents is required',
                                                                    'mimetypes' => 'Only JPG, PNG, PDF files are allowed',
                                                                    'max' => 'File size must not exceed 25MB'
                                                                ])
                                                                ->acceptedFileTypes([
                                                                    'image/jpeg',
                                                                    'image/png',
                                                                    'application/pdf',
                                                                ])
                                                                ->default(
                                                                    array_map(
                                                                        fn($name) => 'images/clinic/' . $record->clinicData->id . '/' . $name,
                                                                        $otherRelevantDocuments
                                                                    )
                                                                ),
                                                        ])->columns(2)
                                                    ];
                                                }),
                                            static::makeAcceptAction(OnboardingStep::DOCUMENTS->value),
                                            static::makeRejectAction(OnboardingStep::DOCUMENTS->value),
                                        ])
                                        ->schema(function ($record) use ($documentChanges) {
                                            $clinic_account_type_id =  $record->clinicData?->clinic_account_type_id;
                                            return [
                                                ViewEntry::make('clinicData.borang_certificate')
                                                    ->visible(function ($record) {
                                                        $filePaths = $record->clinicData?->borangCertificates?->pluck('name')->all() ?? [];
                                                        return count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->borangCertificates?->pluck('name')->all(),
                                                        'name' => 'borang_certificate',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['borang_certificate'] ?? []),
                                                    ])
                                                    ->label('Upload Certification of Registration(Borang B Borang F)'),

                                                ViewEntry::make('clinicData.mmc_certificate')
                                                    ->visible(function ($record) use ($clinic_account_type_id) {
                                                        $hasRequiredType = in_array($clinic_account_type_id, [1, 2, 6]);
                                                        $filePaths = $record->clinicData?->mmcCertificates?->pluck('name')->all() ?? [];
                                                        return $hasRequiredType && count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->mmcCertificates?->pluck('name')->all(),
                                                        'name' => 'mmc_certificate',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['mmc_certificate'] ?? []),
                                                    ])
                                                    ->label('MMC Full Registration Certificate for a Person In-Charge'),

                                                ViewEntry::make('clinicData.apc_certificate')
                                                    ->visible(function ($record) use ($clinic_account_type_id) {
                                                        $hasRequiredType = in_array($clinic_account_type_id, [1, 2, 3, 7, 6, 8]);
                                                        $filePaths = $record->clinicData?->apcCertificates?->pluck('name')->all() ?? [];
                                                        return $hasRequiredType && count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->apcCertificates?->pluck('name')->all(),
                                                        'name' => 'apc_certificate',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['apc_certificate'] ?? []),
                                                    ])
                                                    ->label('Current Annual Practicing Certificate (APC)'),
                                                ViewEntry::make('clinicData.arc_certificate')
                                                    ->visible(function ($record) {
                                                        $filePaths = $record->clinicData?->arcCertificates?->pluck('name')->all() ?? [];
                                                        return count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->arcCertificates?->pluck('name')->all(),
                                                        'name' => 'arc_certificate',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['arc_certificate'] ?? []),
                                                    ])
                                                    ->label('Annual Retention Certificate (ARC)'),
                                                ViewEntry::make('clinicData.poison_license')
                                                    ->visible(function ($record) {
                                                        $filePaths = $record->clinicData?->licenseCertificates?->pluck('name')->all() ?? [];
                                                        return count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->licenseCertificates?->pluck('name')->all(),
                                                        'name' => 'poison_license',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['poison_license'] ?? []),
                                                    ])
                                                    ->label('Poison License'),
                                                ViewEntry::make('clinicData.other_relevant_documents')
                                                    ->visible(function ($record) {
                                                        $filePaths = $record->clinicData?->otherRelevantDocuments?->pluck('name')->all() ?? [];
                                                        return count($filePaths) > 0;
                                                    })
                                                    ->view('filament.admin.resources.user-resource.pages.clinic-pdf-image')
                                                    ->viewData([
                                                        'filePaths' => $record->clinicData?->otherRelevantDocuments?->pluck('name')->all(),
                                                        'name' => 'other_relevant_documents',
                                                        'record' => $record,
                                                        'changes' => (new static())->getPendingFilenames($documentChanges['other_relevant_documents'] ?? []),
                                                    ])
                                                    ->label('Other Relevant Documents'),
                                            ];
                                        })->columns(2),




                                ]),
                            Tab::make('Reward Points')
                                ->visible(function ($record) {
                                    return $record->verification_status == 'approved' || $record->verification_status == null || $record->verification_status == 'pending';
                                })
                                ->schema([
                                    InfoSection::make('Rewards Points Distribution')
                                        ->headerActions([
                                            \Filament\Infolists\Components\Actions\Action::make('Allocate Dpharma Points')
                                                ->extraAttributes(['class' => 'mb-4 bg-primary-500 text-white',])
                                                ->outlined()
                                                ->visible(fn() => auth()->user()?->can('facility_allocate dphamra points') || auth()->user()?->hasRole('Super Admin'))
                                                ->button()
                                                ->form(function ($record) {
                                                    return [
                                                        Group::make()->schema([
                                                            TextInput::make('dpharma_points')
                                                                // ->label('Dpharma Points <span style="color: red;">*</span>')
                                                                ->label(fn() => new HtmlString('Dpharma Points <span style="color: red;">*</span>'))
                                                                ->rules(['required'])
                                                                ->minValue(0)
                                                                ->extraAttributes([
                                                                    'inputmode' => 'numeric',
                                                                    'pattern' => '[0-9]*',
                                                                    'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                                    'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                                                ])
                                                                ->validationMessages([
                                                                    'required' => 'The dpharma Points field is required. ',
                                                                ])
                                                                ->placeholder('Enter Dpharma Points'),
                                                        ]),
                                                    ];
                                                })
                                                ->action(function ($record, $data, $action) {

                                                    $previousBalance = DpharmaPoint::select('balance')->where('user_id', $record->id)
                                                        ->latest('created_at')->first()->balance ?? 0;
                                                    $newBalance = $previousBalance + $data['dpharma_points'];
                                                    DpharmaPoint::create([
                                                        'user_id' => $record->id,
                                                        'description' => 'Points have been successfully allocated.',
                                                        'points' => $data['dpharma_points'],
                                                        'balance' => $newBalance
                                                    ]);

                                                    Notification::make()
                                                        ->title('Dpharma Points Updated Successfully')
                                                        ->success()
                                                        ->send();
                                                    $action->redirect(ClinicResource::getUrl('view', ['record' => $record->id]));
                                                })->modalSubmitActionLabel('Save')
                                                ->label('Allocate Dpharma Points'),
                                        ])
                                        ->schema([
                                            \Filament\Infolists\Components\Grid::make([
                                                'default' => 4,
                                                'sm' => 4,
                                            ])
                                                ->columnSpan('full')
                                                ->extraAttributes(['class' => 'gap-0'])
                                                ->schema([

                                                    TextEntry::make('clinicData.created_at')
                                                        ->label('Registration Date')
                                                        ->formatStateUsing(fn($state) => \Carbon\Carbon::parse($state)->format('F d, Y')),
                                                    TextEntry::make('tier')
                                                        ->label('Tier')
                                                        ->html()
                                                        ->getStateUsing(function ($record) {
                                                            $tier = ClinicDetail::where('user_id', $record->id)->first()?->tier;

                                                            if ($tier == 'gold') {
                                                                return '<span class="badge badge-warning" style="background-color: #ceb631; color: black; padding: 4px 8px; border-radius: 4px;">' . ucfirst($tier) . '</span>';
                                                            } else {
                                                                return ucfirst($tier);
                                                            }
                                                        }),
                                                    TextEntry::make('dpharma_points')
                                                        ->label('Total Reward Points Earned')
                                                        ->getStateUsing(function ($record) {
                                                            return $previousBalance = DpharmaPoint::select('balance')->where('user_id', $record->id)
                                                                ->latest('created_at')->first()?->balance ?? 0;
                                                        }),
                                                    TextEntry::make('amount')
                                                        ->label('Facility Annual Purchase Amount')
                                                        ->getStateUsing(function ($record) {
                                                            return 'RM ' . (new static())->getAnnualPurchaseAmount($record);
                                                        }),
                                                    TextEntry::make('profit_share')
                                                        ->label('Facility Share of Profit')
                                                        ->getStateUsing(function ($record) {
                                                            return 'RM 0';
                                                        }),
                                                ])
                                        ])->columns(2),
                                    // InfoSection::make('Audit Trail Of Reward Points')
                                    //         ->headerActions([
                                    //             \Filament\Infolists\Components\Actions\Action::make('Allocate Dpharma Points')
                                    //             ->extraAttributes(['class' => 'mb-4 bg-primary-500 text-white',])
                                    //             ->outlined()
                                    //             ->button()
                                    //             ->form(function ($record) {
                                    //                 return [
                                    //                     Group::make()->schema([
                                    //                         TextInput::make('dpharma_points')
                                    //                             ->label('Dpharma Points')
                                    //                             ->rules(['required'])
                                    //                             ->minValue(0)
                                    //                             ->extraAttributes([
                                    //                                 'inputmode' => 'numeric',
                                    //                                 'pattern' => '[0-9]*',
                                    //                                 'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                    //                                 'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")'
                                    //                             ])
                                    //                             ->placeholder('Enter Dpharma Points'),
                                    //                     ]),
                                    //                 ];
                                    //             })
                                    //             ->action(function ($record, $data) {
                                    //                 $previousBalance = DpharmaPoint::select('balance')->where('user_id', $record->id)
                                    //                 ->latest('created_at')->first()->balance ?? 0;
                                    //                 $newBalance = $previousBalance + $data['dpharma_points'];
                                    //                 DpharmaPoint::create([
                                    //                     'user_id' => $record->id,
                                    //                     'points' => $data['dpharma_points'],
                                    //                     'balance' => $newBalance
                                    //                 ]);

                                    //                 Notification::make()
                                    //                     ->title('Dpharma Points Updated Successfully')
                                    //                     ->success()
                                    //                     ->send();
                                    //             })
                                    //             ->label('Allocate Dpharma Points'),
                                    //         ])
                                    //         ->schema([
                                    //             \Filament\Infolists\Components\Grid::make([
                                    //                 'default' => 4,
                                    //                 'sm' => 4,
                                    //             ])
                                    //             ->columnSpan('full')
                                    //             ->extraAttributes(['class' => 'gap-0'])
                                    //             ->schema([
                                    //                     TextEntry::make('clinicData.created_at')
                                    //                         ->label('Registration Date')
                                    //                         ->formatStateUsing(fn ($state) => \Carbon\Carbon::parse($state)->format('F d, Y')),
                                    //                     TextEntry::make('tier')
                                    //                         ->label('Tier')
                                    //                         ->html()
                                    //                         ->getStateUsing(function ($record) {
                                    //                             $tier = ClinicDetail::where('user_id', $record->id)->first()->tier;

                                    //                             if ($tier == 'gold') {
                                    //                                 return '<span class="badge badge-warning" style="background-color: #ceb631; color: black; padding: 4px 8px; border-radius: 4px;">' . ucfirst($tier) . '</span>';
                                    //                             } else {
                                    //                                 return ucfirst($tier);
                                    //                             }
                                    //                         }),
                                    //                     TextEntry::make('dpharma_points')
                                    //                         ->label('Total Reward Points Earned')
                                    //                         ->getStateUsing(function ($record) {
                                    //                             return $previousBalance = DpharmaPoint::select('balance')->where('user_id', $record->id)
                                    //                             ->latest('created_at')->first()->balance ?? 0;
                                    //                         }),
                                    //                     TextEntry::make('amount')
                                    //                         ->label('Facility Annual Purchase Amount')
                                    //                         ->getStateUsing(function ($record) {
                                    //                             return 'RM ' . (new static)->getAnnualPurchaseAmount($record);
                                    //                         }),
                                    //                     TextEntry::make('profit_share')
                                    //                         ->label('Facility Share of Profit')
                                    //                         ->getStateUsing(function ($record) {
                                    //                             return 'RM 0' ;
                                    //                         }),
                                    //             ])
                                    //         ])->columns(2),

                                ])
                        ])->columnSpanFull(),
                ];
            });
    }

    public static function form(Form $form): Form
    {
        return $form->schema([

            Wizard::make()
                // ->startOnStep(4)
                ->previousAction(function ($action) {
                    return $action->label('Back')->extraAttributes([
                        'class' => 'bg-white'
                    ]);
                })
                ->nextAction(function ($action) {
                    return $action->label('Save & Continue')->color('create_button');
                })
                ->schema([
                    Step::make('Facility Info')
                        ->icon('phosphor-building')
                        ->schema([
                            Group::make()->schema([
                                Fieldset::make('Facility Information')->label(new HtmlString('<span style="font-size: 28px !important;">Facility Information </span>'))
                                    ->schema([
                                        FileUpload::make('photo')
                                            ->label('Company Logo')
                                            ->image()
                                            ->avatar()
                                            ->directory('users')
                                            ->rules(['nullable', File::types(['jpeg', 'png', 'jpg'])->max(2 * 1024)])
                                            ->helperText('Supported formats: JPEG, JPG, PNG (Max 2MB)')->columnSpanFull(),

                                        TextInput::make('clinic_name')
                                            ->rules(['max:150', 'regex:/^[a-zA-Z0-9\s]+$/'])
                                            ->validationMessages([
                                                'max' => 'The Facility Name may not be greater than 150 characters.',
                                                'regex' => 'The Facility Name must be a alphabetical.',
                                            ])
                                            ->label('Facility Name')->placeholder('Enter facility name')->required(),
                                        TextInput::make('clinic_number')
                                            // ->hintIcon('heroicon-s-information-circle', tooltip: 'Need some more information?')
                                            // ->label('Facility Registration Number')
                                            ->label(new HtmlString(
                                                'Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->placeholder('Enter facility registration number')
                                            ->validationAttribute('Facility Registration Number')
                                            ->rules(function (Get $get) {
                                                if (!empty($get('user_id'))) {
                                                    return ['required', Rule::unique('clinic_details', 'clinic_number')->ignore($get('user_id'), 'user_id'), 'min:2', 'max:20', 'regex:/^[a-zA-Z0-9]+$/'];
                                                }
                                                return ['required', 'unique:clinic_details,clinic_number', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                            })
                                            ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->required()->validationMessages([
                                                'required' => 'The Facility Registration Number field is required.',
                                                'max' => 'The Registration Number must be at most 20 characters long.',
                                                'unique' => 'The Facility Registration Number has already been taken.',
                                                'regex' => 'The Registration Number must contain only letters and numbers.',
                                                'min' => 'The Registration Number must be at least 2 characters long.',
                                            ]),
                                        TextInput::make('mobile_number')->prefix('+60')->label('Mobile Number')
                                            ->mask('************')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])->live()
                                            ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->rules(['nullable', 'digits_between:8,12'])->placeholder('Enter facility mobile number'),
                                        // TextInput::make('landline_number')->prefix('+03')->label('Facility Landline Number')
                                        //     ->mask('9-9999999')
                                        //     ->stripCharacters(['-'])
                                        //     ->extraAttributes([
                                        //         'inputmode' => 'numeric',
                                        //         'maxlength' => '8'
                                        //     ])
                                        //     ->rules(['digits_between:7,8'])->placeholder('Enter facility landline number'),

                                        PhoneWithPrefix::make('landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8'
                                            ])
                                            ->prefixOptions(function ($get, $set) {
                                                if (empty($get('landline_number'))) {
                                                    return [];
                                                }
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');

                                                if ($stateId && $cityId) {
                                                    $query->where('state_id', $stateId)->where('id', $cityId);
                                                }

                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (empty($data)) {
                                                    $data = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                // FacadesLog::info($get('addresses'));
                                                if ($get("landline_number")["prefix"] === "") {
                                                    $set('landline_number.prefix', $data[array_key_first($data)] ?? '');
                                                }
                                                return $data;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {

                                                if (isset($get('addresses')[0]["landline_code"]) && $get('addresses')[0]["landline_code"] != null) {
                                                    $set("landline_number.prefix", $get('addresses')[0]["landline_code"]);
                                                    $set("landline_number.number", $get('addresses')[0]["landline_number"]);
                                                } else {
                                                    $set("landline_number", ["prefix" => "", "number" => ""]);
                                                }
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set("landline_code", implode(" ", $get("landline_number")));
                                            })->formatStateUsing(function ($get, $set, $state) {
                                                $data = ['prefix' => '', 'number' => ''];
                                                if ($get("landline_code")) {
                                                    $data["prefix"] = $get("landline_code");
                                                }
                                                if ($get("landline_number")) {
                                                    $data["number"] = $get("landline_number");
                                                }
                                                return is_array($state) ? $state : $data;
                                            }),
                                        TextInput::make('clinic_email')->regex('/^[^@]+@[^@]+\.[a-zA-Z]{2,}$/')->label('Email')->placeholder('Enter facility email')
                                            ->rules(['required', 'email', 'max:255'])
                                            ->required()->email(),
                                        Select::make('clinic_account_type_id')
                                            ->label('Facility Type')->placeholder('Select facility type')
                                            ->options(ClinicAccountType::where('status', true)->pluck('name', 'id')->toArray())->required(),
                                    ]),
                                Fieldset::make('Company Information')->label(new HtmlString('<span style="font-size: 28px !important;">Company Information </span>'))
                                    ->schema([
                                        Select::make('business_type_id')
                                            ->label(new \Illuminate\Support\HtmlString('Type of Business <span class="text-danger-600 dark:text-danger-400 font-medium">*</span>'))
                                            ->placeholder('Select business type')
                                            ->options(BusinessType::where('status', true)->pluck('name', 'id')->toArray())
                                            ->rules(['required'])
                                            ->validationMessages([
                                                'required' => 'The Type of Business field is required.',
                                            ])
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, callable $set) {
                                                // Only proceed if $state is not empty and is numeric
                                                if (empty($state) || !is_numeric($state)) {
                                                    return;
                                                }
                                                $businessType = BusinessType::find($state);
                                                $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                if ($isSoleProprietary) {
                                                    $set('company_name', '');
                                                    $set('company_number', '');
                                                }
                                            }),

                                        TextInput::make('company_name')
                                            ->label(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                // Always show red star unless sole-proprietary is selected
                                                return new HtmlString(
                                                    'Company Name '
                                                );
                                            })
                                            ->placeholder('Enter company name')
                                            ->rules(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                $rules = ['string', 'max:50', 'regex:/^[\pL\s]+$/u'];
                                                if (!$isSoleProprietary) {
                                                    if (!empty($get('user_id'))) {
                                                        $rules[] = Rule::unique('clinic_details', 'company_name')->ignore($get('user_id'), 'user_id');
                                                    } else {
                                                        $rules[] = 'unique:clinic_details,company_name';
                                                    }
                                                    $rules[] = 'required';
                                                }
                                                return $rules;
                                            })
                                            ->required(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                if (empty($businessTypeId)) {
                                                    // Show required on first load
                                                    return true;
                                                }
                                                $businessType = BusinessType::find($businessTypeId);
                                                return $businessType && $businessType->key !== 'sole-proprietary';
                                            })
                                            ->validationMessages([
                                                'required' => 'The Company field is required.',
                                                'max' => 'The Company Name must be at most 50 characters long',
                                                'regex' => 'The company name can only contain letters and spaces.',
                                            ]),

                                        TextInput::make('company_number')
                                            ->suffixIcon(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->label(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                // Always show red star unless sole-proprietary is selected
                                                return new HtmlString(
                                                    'Company Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg> '
                                                );
                                            })
                                            ->rules(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                $rules = ['max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                                if (!$isSoleProprietary) {
                                                    $rules[] = 'required';
                                                }
                                                return $rules;
                                            })
                                            ->required(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                if (empty($businessTypeId)) {
                                                    // Show required on first load
                                                    return true;
                                                }
                                                $businessType = BusinessType::find($businessTypeId);
                                                return $businessType && $businessType->key !== 'sole-proprietary';
                                            })
                                            ->placeholder('Enter company registration number')
                                            ->validationMessages([
                                                'required' => 'The Company Registration Number field is required.',
                                                'max' => 'The Registration Number must be at most 20 characters long.',
                                                'min' => 'The Registration Number field must be at least 2 characters.',
                                                'regex' => 'The company registration number can only contain numbers and letters.',
                                            ]),
                                        // TextInput::make('clinic_owner')
                                        //     ->rules('regex:/^[\pL\s]+$/u', 'max:64')
                                        //     ->label(new HtmlString('<span style="font-size: 14px !important;">Facility Owner</span>'))->placeholder('Enter facility owner')->required()->validationMessages([
                                        //         'required' => 'The facility owner field is required.',
                                        //         'regex' => 'The facility owner can only contain letters and spaces.',
                                        //         'max' => 'The facility owner must be at most 64 characters long.',
                                        //     ]),
                                        Select::make('clinic_year')
                                            ->label('Establishment Year')->placeholder('Select commencement year')
                                            ->options(self::getYearOptions())
                                            ->searchable()
                                            ->rules(['required'])->required(),
                                        TextInput::make('tin_number')
                                            ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->label(new HtmlString(
                                                'TIN Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Tax Identification Number (TIN) is a unique identifier assigned by the tax authority for businesses or individuals to track tax obligations.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                            ))
                                            ->placeholder('Enter TIN Number')
                                            ->rules(['required', 'min:1', 'regex:/^[a-zA-Z0-9]+$/', 'max:20'])->validationMessages([
                                                'required' => 'The TIN Number field is required.',
                                                'max' => 'The TIN Number field must not be greater than 20 characters.',
                                                'min' => 'The TIN Number field must be at least 1 characters.',
                                                'regex' => 'The TIN Number can only contain numbers and letters.',
                                            ]),
                                        TextInput::make('sst_number')
                                            ->suffixIcon(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->label(new HtmlString(
                                                'SST Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sales and Services Tax (SST) Registration Number is a unique code issued to businesses in Malaysia registered for SST compliance.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->placeholder('Enter SST registration number')->rules(['nullable', 'min:1', 'regex:/^[a-zA-Z0-9]+$/',  'max:20'])
                                            ->validationMessages([
                                                'max' => 'The SST registration number field must not be greater than 20 characters.',
                                                'min' => 'The SST Number field must be at least 1 characters.',
                                                'regex' => 'The SST Number can only contain numbers and letters.',
                                            ]),
                                    ])
                            ])->columns(2)
                        ])->afterValidation(function (Get $get, Set $set, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['clinic_name'] = $get('clinic_name');
                            $email = $get('clinic_email');
                            $data['clinic_number'] = $get('clinic_number');
                            $data['clinic_account_type_id'] = $get('clinic_account_type_id');
                            $data['mobile_code'] = '60';
                            // $data['landline_code'] = '03';
                            $data['mobile_number'] = getUnMaskNumber($get('mobile_number'));
                            // $data['landline_number'] = getUnMaskNumber($get('landline_number'));
                            $data['landline_number'] = $get('landline_number')["number"] ?? "";
                            $data['landline_code'] = $get('landline_number')["prefix"] ?? "";
                            $data['business_type_id'] = $get('business_type_id');
                            $data['company_name'] = $get('company_name');
                            $data['company_number'] = $get('company_number');
                            // $data['clinic_owner'] = $get('clinic_owner');
                            $data['clinic_year'] = $get('clinic_year');
                            $data['tin_number'] = $get('tin_number');
                            $data['sst_number'] = $get('sst_number');

                            $result = $service->storeFacelityInformations($data, 1, $email);
                            $uploadedFile = $get('photo');
                            $uploadedFile['user_id'] = $result['id'];
                            uploadUserImage($uploadedFile);
                            if (!empty($result)) {
                                $set('user_id', $result['user_id']);
                                $set('clinic_id', $result['id']);
                                return redirect()->to(self::getUrl('edit', ['record' => $result]));
                            }
                        }),
                    Step::make('Manage Address')
                        ->icon('heroicon-o-phone')
                        ->schema([
                            Group::make()->schema([
                                Fieldset::make('Billing Address')->label(new HtmlString('<span style="font-size: 28px !important;">Billing Address </span>'))
                                    ->schema(function (Get $get) {
                                        return [
                                            TextInput::make('billing_address_1')
                                                ->rules(['required', 'max:100'])
                                                ->label('Address line 1')->placeholder('Enter address line 1')->required()
                                                ->validationMessages([
                                                    'max' => 'The Address Line 1 must be at most 100 characters long.',
                                                ])
                                                ->afterStateUpdated(function (callable $set, $state) {

                                                    if (!empty($state)) {
                                                        $set('is_same_as_billing', false);
                                                    }
                                                })->live(),
                                            TextInput::make('billing_address_2')
                                                ->rules(['nullable', 'max:100'])
                                                ->validationMessages([
                                                    'max' => 'The Address Line 2 must be at most 100 characters long.',
                                                ])
                                                ->label('Address line 2')->placeholder('Enter address line 2')
                                                ->afterStateUpdated(function (callable $set, $state) {

                                                    if (!empty($state)) {
                                                        $set('is_same_as_billing', false);
                                                    }
                                                })->live(),
                                            TextInput::make('billing_country_id')
                                                ->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                                            Select::make('billing_state_id')
                                                ->label('State')->placeholder('Select State')->searchable()
                                                ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                    if ($state) {
                                                        $set('is_same_as_billing', false);
                                                        $set('billing_city_id', null);
                                                    }
                                                    if (blank($state)) {
                                                        $set('billing_city_id', null); // <-- Clear city if state is cleared
                                                    }
                                                })
                                                ->required()->live(),
                                            Select::make('billing_city_id')->label('City')->placeholder('Select City')
                                                ->options(function (Get $get) {

                                                    if (!empty($get('billing_state_id'))) {

                                                        return City::where('state_id', $get('billing_state_id'))->pluck('name', 'id');
                                                    }
                                                    return [];
                                                })
                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                    if ($state) {
                                                        $set('is_same_as_billing', false);
                                                    }
                                                })
                                                ->getSelectedRecordUsing(function (Get $get) {
                                                    return $get('billing_city_id') ?? '';
                                                })->required()
                                                ->live(onBlur: true)
                                                ->loadingMessage('Loading cities...')
                                                ->searchable(),

                                            TextInput::make('billing_postal_code')->label('Postal Code')
                                                ->rules(['required', 'digits:5'])->required()
                                                ->extraAttributes([
                                                    'inputmode' => 'numeric',
                                                    'pattern' => '[0-9]*',
                                                    'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                    'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                                ])
                                                ->placeholder('Enter postal code')->afterStateUpdated(function (callable $set, $state) {

                                                    if (!empty($state)) {
                                                        $set('is_same_as_billing', false);
                                                    }
                                                })->live(),
                                        ];
                                    }),
                            ]),
                            // Group::make()->schema([
                            //     Checkbox::make('is_default_shipping')
                            //     ->label('Set as default')
                            //     ->default(false)
                            //     ->live()
                            //     ->afterStateUpdated(function ($state, Set $set, Get $get) {
                            //         if ($state) {
                            //             // Uncheck all repeater defaults when main is checked
                            //             $addresses = $get('shipping_addresses') ?? [];
                            //             foreach ($addresses as $key => $address) {
                            //                 $set("shipping_addresses.{$key}.is_default_shipping_new", false);
                            //             }
                            //         }
                            //     })
                            //     ->columnSpan(1),
                            //     Checkbox::make('is_same_as_billing')
                            //     ->live()
                            //     ->afterStateUpdated(function (Get $get, Set $set, $state) {
                            //         if ($state) {
                            //             $set('shipping_address_1', $get('billing_address_1'));
                            //             $set('shipping_address_2', $get('billing_address_2'));
                            //             $set('shipping_country_id', $get('billing_country_id'));
                            //             $set('shipping_state_id', $get('billing_state_id'));
                            //             // dd($get('billing_city_id'));
                            //             // Set billing city ID only after setting billing state ID
                            //             $set('shipping_city_id', $get('billing_city_id'));

                            //             $set('shipping_postal_code', $get('billing_postal_code'));
                            //         } else {
                            //             $set('shipping_address_1', '');
                            //             $set('shipping_address_2', '');
                            //             $set('shipping_state_id', '');
                            //             $set('shipping_city_id', '');
                            //             $set('shipping_postal_code', '');
                            //         }
                            //     })
                            //     ->label('Same as billing address'),

                            // ])->columns(2),

                            // Fieldset::make('Shipping Address')->label(new HtmlString('<span style="font-size: 28px !important;">Shipping Address 1 </span>'))
                            // ->schema([
                            //     TextInput::make('user_id')->visible(false),
                            //     TextInput::make('clinic_id')->visible(false),
                            //     TextInput::make('shipping_address_1')
                            //     ->rules(['required', 'max:100'])
                            //     ->validationMessages([
                            //         'max' => 'The Address Line 1 must be at most 100 characters long.',
                            //     ])
                            //     ->label('Address line 1')->placeholder('Enter address line 1')->required(),
                            //     TextInput::make('shipping_address_2')
                            //     ->rules(['required', 'max:100'])
                            //     ->validationMessages([
                            //         'max' => 'The Address Line 2 must be at most 100 characters long.',
                            //     ])
                            //     ->label('Address line 2')->placeholder('Enter address line 2')->required(),

                            //     TextInput::make('shipping_country_id')->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                            //     Select::make('shipping_state_id')
                            //     ->label('State')->placeholder('Select State')->searchable()
                            //     ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())->required()->live(),


                            //     Select::make('shipping_city_id')->label('City')->placeholder('Select City')
                            //     ->options(function (Get $get) {

                            //         if (!empty($get('billing_state_id'))) {
                            //             return City::where('state_id', $get('billing_state_id'))->pluck('name', 'id')->toArray();
                            //         }
                            //         return [];
                            //     })->live(onBlur: true)->required()
                            //     ->loadingMessage('Loading cities...')
                            //     ->searchable(),
                            //     TextInput::make('shipping_postal_code')
                            //     ->extraAttributes([
                            //         'inputmode' => 'numeric',
                            //         'pattern' => '[0-9]*',
                            //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                            //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                            //     ])
                            //     ->rules(['required','digits:5'])->required()
                            //     ->label('Postal Code')->placeholder('Enter postal code'),
                            // ]),
                            Group::make()->schema([
                                // Repeater for Multiple Shipping Addresses
                                Repeater::make('shipping_addresses')
                                    ->label('')
                                    ->live()

                                    ->schema([
                                        Hidden::make('default_shipping_index'),
                                        Group::make()->schema([
                                            Checkbox::make('is_default')
                                                ->label('Set as default')
                                                ->live()
                                                ->afterStateUpdated(function ($state, Set $set, Get $get, $component) {
                                                    $currentPath = $component->getStatePath();
                                                    if (preg_match('/shipping_addresses\.([^.]+)\.is_default/', $currentPath, $matches)) {
                                                        $uuid = $matches[1];
                                                        $currentIndex = $matches[1] ?? null;
                                                        if (is_null($currentIndex)) {
                                                            return;
                                                        }
                                                    }

                                                    if ($state) {
                                                        $set('../../default_shipping_index', $currentIndex);

                                                        $addresses = $get('../../shipping_addresses') ?? [];
                                                        foreach ($addresses as $key => $address) {
                                                            $addresses[$key]['is_default'] = ((string)$key === (string)$uuid);
                                                        }
                                                        $set('../../shipping_addresses', $addresses);
                                                        $set('../../default_shipping_index', $currentIndex);
                                                    } else {
                                                        if ($get('../../default_shipping_index') == $currentIndex) {
                                                            $set('../../default_shipping_index', null);
                                                        }
                                                    }
                                                })

                                                ->columnSpan(1),

                                            Checkbox::make('is_same_as_billing')
                                                ->live()
                                                ->afterStateUpdated(function (Get $get, Set $set, $state, $component) {

                                                    $currentPath = $component->getStatePath();

                                                    if (preg_match('/shipping_addresses\.([^.]+)\.is_same_as_billing/', $currentPath, $matches)) {
                                                        $uuid = $matches[1];
                                                        $currentIndex = $matches[1] ?? null;
                                                        if (is_null($currentIndex)) {
                                                            return;
                                                        }
                                                    }
                                                    $rootData = $get('../../');
                                                    if ($state) {
                                                        $set('../../billing_address_same_index', $currentIndex);
                                                        $set('shipping_address_1', $rootData['billing_address_1']);
                                                        $set('shipping_address_2', $rootData['billing_address_2']);
                                                        $set('shipping_country_id', $rootData['billing_country_id']);
                                                        $set('shipping_state_id', $rootData['billing_state_id']);
                                                        $set('shipping_city_id', $rootData['billing_city_id']);
                                                        $set('shipping_postal_code', $rootData['billing_postal_code']);

                                                        $addresses = $get('../../shipping_addresses') ?? [];
                                                        foreach ($addresses as $key => $address) {
                                                            $addresses[$key]['is_same_as_billing'] = ((string)$key === (string)$uuid);
                                                        }
                                                    } else {
                                                        if ($get('../../billing_address_same_index') == $currentIndex) {
                                                            $set('../../billing_address_same_index', null);
                                                        }

                                                        $set('shipping_address_1', '');
                                                        $set('shipping_address_2', '');
                                                        $set('shipping_state_id', '');
                                                        $set('shipping_city_id', '');
                                                        $set('shipping_postal_code', '');
                                                    }
                                                })

                                                ->label('Same as billing address'),
                                        ])->columns(2),


                                        Fieldset::make(function ($get, $set, $state, $component) {
                                            $items = $component->getContainer()->getParentComponent()->getState();
                                            $currentKey = $component->getContainer()->getStatePath();
                                            preg_match('/shipping_addresses\.([^\.]+)/', $currentKey, $matches);
                                            $currentItemKey = $matches[1] ?? null;

                                            $position = 0;
                                            foreach (array_keys($items ?? []) as $key) {
                                                if ($key === $currentItemKey) {
                                                    break;
                                                }
                                                $position++;
                                            }
                                            return new HtmlString('<span style="font-size: 28px !important;">Shipping Address ' . ($position + 1) . '</span>');
                                        })
                                            ->schema([
                                                TextInput::make('user_id')->visible(false),
                                                TextInput::make('clinic_id')->visible(false),
                                                TextInput::make('shipping_address_1')
                                                    ->label('Address line 1')
                                                    ->rules(['required', 'max:100'])
                                                    ->validationMessages([
                                                        'max' => 'The Address Line 1 must be at most 100 characters long.',
                                                    ])
                                                    ->placeholder('Enter address line 1')
                                                    ->required(),
                                                TextInput::make('shipping_address_2')
                                                    ->label('Address line 2')
                                                    ->rules(['required', 'max:100'])
                                                    ->validationMessages([
                                                        'max' => 'The Address Line 2 must be at most 100 characters long.',
                                                    ])
                                                    ->placeholder('Enter address line 2')
                                                    ->required(),
                                                TextInput::make('shipping_country_id')
                                                    ->placeholder('Malaysia')
                                                    ->label('Country')
                                                    ->default('Malaysia')
                                                    ->readOnly(),
                                                Select::make('shipping_state_id')
                                                    ->label('State')
                                                    ->placeholder('Select State')
                                                    ->searchable()
                                                    ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                    ->required()
                                                    ->live()
                                                    ->afterStateUpdated(function ($state, Set $set) {
                                                        if (blank($state)) {
                                                            $set('shipping_city_id', null); // <-- Clear city if state is cleared
                                                        }
                                                    }),

                                                Select::make('shipping_city_id')
                                                    ->label('City')
                                                    ->placeholder('Select City')
                                                    ->options(function (Get $get) {
                                                        if (!empty($get('shipping_state_id'))) {
                                                            return City::where('state_id', $get('shipping_state_id'))->pluck('name', 'id');
                                                        }
                                                        return [];
                                                    })
                                                    ->required()
                                                    ->live(onBlur: true)
                                                    ->loadingMessage('Loading cities...')
                                                    ->searchable(),
                                                TextInput::make('shipping_postal_code')
                                                    ->label('Postal Code')
                                                    ->rules(['required', 'digits:5'])
                                                    ->required()
                                                    ->extraAttributes([
                                                        'inputmode' => 'numeric',
                                                        'pattern' => '[0-9]*',
                                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                                    ])
                                                    ->placeholder('Enter postal code'),
                                            ]),
                                    ])
                                    ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                        return $action->label(new HtmlString('<span class="font-bold text-blue-950" style="color:rgb(0,63,94)">+ Add Shipping Address</span>'))
                                            ->extraAttributes([
                                                'style' => 'border: none !important; box-shadow: none !important;'
                                            ]);
                                    })
                                    ->addActionAlignment(Alignment::End)
                                    ->defaultItems(1)

                                    ->deleteAction(
                                        fn($action) => $action
                                            ->hidden(function (Get $get, $component) {
                                                // Get the current state of shipping addresses
                                                $shippingAddresses = $get('shipping_addresses') ?? [];

                                                // Hide delete button if there's only one address left
                                                return count($shippingAddresses) <= 1;
                                            })
                                    )

                            ])->columns(1)
                        ])->afterValidation(function (Get $get, ClinicProfileByAdminService $service) {
                            $data = [];
                            // dd($data);
                            // dd($get('billing_address_same_index'));
                            $data['user_id'] = $get('user_id');
                            $data['shipping_addresses'] = $get('shipping_addresses');
                            $data['shipping_address_1'] = $get('shipping_address_1');
                            $data['shipping_address_2'] = $get('shipping_address_2');
                            $data['shipping_state_id'] = $get('shipping_state_id');
                            $data['shipping_city_id'] = $get('shipping_city_id');
                            $data['shipping_country_id'] = $get('shipping_country_id');
                            $data['shipping_postal_code'] = $get('shipping_postal_code');
                            $data['billing_address_1'] = $get('billing_address_1');
                            $data['billing_address_2'] = $get('billing_address_2');
                            $data['billing_state_id'] = $get('billing_state_id');
                            $data['billing_city_id'] = $get('billing_city_id');
                            $data['billing_country_id'] = $get('billing_country_id');
                            $data['billing_postal_code'] = $get('billing_postal_code');
                            $data['is_same_as_billing'] = $get('is_same_as_billing');
                            $data['is_default'] = $get('is_default');
                            $service->storeAddressDetails($data, 2);
                        }),
                    Step::make('Pharma Suppliers')
                        ->icon('heroicon-s-building-storefront')

                        ->schema([
                            Group::make()->schema([
                                Placeholder::make('suppliers_table')->label('')
                                    ->content(function ($record, Get $get) {
                                        $suppliers = ClinicPharmaSupplier::where('clinic_id', $get('user_id'))
                                            ->with(['pcDetail', 'pcDetail.pcDetails'])
                                            ->get();
                                        // dd($suppliers);

                                        if ($suppliers->isEmpty()) {
                                            return 'No suppliers added yet.';
                                        }

                                        return view('clinic.suppliers-table', [
                                            'suppliers' => $suppliers,
                                            'record_id' => $get('user_id'),
                                        ]);
                                    })
                                    ->columnSpanFull(),
                                Forms\Components\Actions::make([
                                    Forms\Components\Actions\Action::make('addSupplier')
                                        ->label('+ Add Supplier')
                                        ->modalHeading('Add Supplier')
                                        // ->modalSubmitActionLabel('Confirm & Submit')
                                        // ->buttonColor('none')
                                        // ->icon('heroicon-o-plus')
                                        // ->extraIconAttributes(['class' => 'text-primary-600'])
                                        ->form([
                                            Select::make('pc_suplier')
                                                ->label(new HtmlString('Supplier Name <span  style="color:red">*</span>'))
                                                ->placeholder('Select supplier name')
                                                ->validationMessages([
                                                    'required' => 'The Supplier Name field is required.',
                                                ])
                                                ->options(PcDetail::pluck('business_name', 'user_id')->toArray())
                                                ->searchable()
                                                ->rules(['required'])
                                                ->live(),
                                            TextInput::make('account_number')
                                                ->label(new HtmlString('Account Number <span style="color:red">*</span>'))
                                                ->validationMessages([
                                                    'required' => 'The Account Number field is required.',
                                                    'regex' => 'The Account Number must be a alphabetical.',
                                                    'max' => 'The Account Number may not be greater than 20 characters.',
                                                    'min' => 'The Account Number field must be at least 1 characters.',
                                                ])
                                                ->placeholder('Enter account number')
                                                ->validationAttribute('Account Number')
                                                ->rules(['required', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])

                                        ])->modalSubmitActionLabel('Add Supplier')
                                        ->action(function (array $data, Get $get, ClinicProfileByAdminService $service) {
                                            $data['clinic_id'] = $get('clinic_id');
                                            $data['user_id'] = $get('user_id');

                                            $service->storeClinicPharmaSuplier($data, 3);
                                        })
                                        ->extraAttributes(['style' => 'margin-left:auto; color: rgb(0,63,94); background-color:transparent; hover:background-color:transparent;'])
                                ])
                            ])->columns(1)
                        ]),
                    Step::make('Person In Charge')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([
                                TextInput::make('dc_name')
                                    ->label('Full Name')
                                    ->placeholder('Enter full name')
                                    ->maxLength(50)
                                    ->validationMessages([
                                        'required' => 'The Full Name field is required.',
                                        'regex' => 'Only alphabetical characters and spaces are allowed.',
                                    ])
                                    ->rules(['required', 'regex:/^[a-zA-Z\s]+$/'])
                                    ->required(),
                                TextInput::make('dc_nric')
                                    ->placeholder('Enter NRIC')
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">NRIC / Passport Number</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                </span>'))
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12',
                                        'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                        'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                    ])
                                    ->required()->rules(['required', 'digits:12'])->validationMessages([
                                        'required' => 'The NRIC field is required.',
                                        'digits' => 'The NRIC Number must be exactly 12 digits long.'
                                    ]),
                                TextInput::make('dc_mmc_number')
                                    ->placeholder('Enter MMC registration number')
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">MMC Registration Number</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                </span>'))
                                    ->required()->rules(['required', 'numeric', 'regex:/^\d{8,8}$/'])->validationMessages([
                                        'required' => 'The MMC Registration Number field is required.',
                                        'numeric' => 'The MMC Registration Number must be numeric.',
                                        'regex'    => 'The MMC Registration Number must be 8 digits long.',
                                    ]),
                                TextInput::make('dc_apc_number')
                                    ->placeholder('Enter current APC No')
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">Current APC No</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                </span>'))
                                    ->required()->rules(['required', 'numeric', 'regex:/^\d{8,8}$/'])->validationMessages([
                                        'required' => 'The Current APC Number field is required.',
                                        'numeric' => 'The Current APC Number must be numeric.',
                                        'regex'    => 'The Current APC Number must be 8 digits long.',
                                    ]),
                                Select::make('apc_certificate_expired_date')
                                    ->label('APC Expiry Year')->placeholder('Select commencement year')
                                    ->options(self::getYearOptions())
                                    ->searchable()
                                    ->rules(['required'])->required()
                                    ->validationMessages([
                                        'required' => 'The APC Expiry Year field is required.',
                                    ]),
                                TextInput::make('dc_phone_number')
                                    ->numeric()->prefix('+60')
                                    ->placeholder('Phone Number')
                                    ->mask('99-99999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '10'
                                    ])
                                    ->rules(['required', 'digits_between:9,10'])
                                    ->label('Enter phone number')
                                    ->required()->validationMessages([
                                        'required' => 'The phone number field is required.',
                                        'digits_between' => 'The phone number must be between 9 and 10 digits long.'
                                    ]),
                                TextInput::make('dc_landline_number')->prefix('+03')
                                    ->numeric()->rules(['nullable', 'digits_between:7,8'])
                                    ->placeholder('Enter landline number')
                                    ->label('Landline Number')->validationMessages([
                                        'digits_between' => 'The landline number must be between 7 and 8 digits long.'
                                    ])
                                    ->mask('9-9999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '8'
                                    ]),

                                Radio::make('signature_type')->options([
                                    true => 'Digital',
                                    false => 'Image',
                                ])->live()
                                    ->rules(['required'])
                                    ->required()
                                    ->inline()
                                    ->label('Signature Type')
                                    ->default(1)->columnSpan('full'),

                                SignaturePad::make('signature')
                                    ->visible(fn($get) => $get('signature_type') == 1)
                                    ->undoable(false)
                                    ->required()->rules(['required_without:dc_signature']),
                                FileUpload::make('dc_signature')
                                    ->visible(fn($get) => $get('signature_type') == 0)
                                    // ->label('Signature of Doctor In-charge')
                                    ->label(function ($record) {
                                        $clinicAccountTypeId = $record->clinicData->clinic_account_type_id ?? null;
                                        $label = ($clinicAccountTypeId == 4)
                                            ? 'Signature of Pharmacy In-charge'
                                            : 'Signature of Doctor In-charge';

                                        return $label;
                                    })
                                    ->rules(['required', 'mimes:png,jpeg,jpg,pdf'])
                                    ->directory(fn(Get $get) => config('constants.api.media.clinic_medias') . $get('id') . '/')
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => 'dc_signature_' . $file->getClientOriginalName()
                                    )
                                    ->disk('public'),
                                Checkbox::make('is_admin_in_charge')->label('Same as the Doctor In Charge')
                                    ->live()
                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if ($state) {
                                            $rules = [
                                                'dc_name' => ['required', 'string', 'max:50'],
                                                'dc_nric' => ['required', 'alpha_num', 'max:14'],
                                                'dc_phone_number' => ['required', 'digits:8'],
                                                'dc_landline_number' => ['nullable', 'digits:8'],
                                            ];

                                            $validator = Validator::make([
                                                'dc_name' => $get('dc_name'),
                                                'dc_nric' => $get('dc_nric'),
                                                'dc_phone_number' => $get('dc_phone_number'),
                                                'dc_landline_number' => $get('dc_landline_number'),
                                            ], $rules);
                                            // dd($validator->fails());
                                            if (!$validator->fails()) {
                                                $set('is_admin_in_charge', false);

                                                // Highlight problematic fields
                                                foreach ($validator->errors()->keys() as $field) {
                                                    $set("{$field}Error", $validator->errors()->first($field));
                                                }

                                                Notification::make()
                                                    ->title('Please complete Doctor In Charge details')
                                                    ->body('Some required fields are missing or invalid')
                                                    ->danger()
                                                    ->send();
                                            } else {
                                                // Copy all values
                                                $set('ac_name', $get('dc_name'));
                                                $set('ac_nric', $get('dc_nric'));
                                                $set('ac_phone_number', $get('dc_phone_number'));
                                                $set('ac_landline_number', $get('dc_landline_number'));
                                            }
                                        } else {
                                            // Clear all admin fields
                                            $set('ac_name', '');
                                            $set('ac_nric', '');
                                            $set('ac_phone_number', '');
                                            $set('ac_landline_number', '');
                                        }
                                    })->columnSpan('full'),
                                Fieldset::make('Admin In Charge')->label(new HtmlString('<span style="font-size: 28px !important;">Admin In Charge </span>'))
                                    ->schema([

                                        TextInput::make('ac_name')
                                            ->placeholder('Enter Full Name')
                                            ->label('Full Name')
                                            ->maxLength(50)
                                            ->required(),
                                        TextInput::make('ac_nric')
                                            ->placeholder('Enter NRIC')
                                            ->label(new HtmlString('<span style="font-size: 14px !important;">NRIC Number</span> <span class="tooltip tooltip-right" data-tooltip="This is your full name.">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <circle cx="12" cy="12" r="10"></circle>
                                                    <line x1="12" y1="16" x2="12" y2="12"></line>
                                                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                </svg>
                                            </span>'))
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12',
                                                'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                            ])
                                            ->required()->rules(['required', 'digits:12'])->validationMessages([
                                                'required' => 'The NRIC field is required.',
                                                'digits' => 'The NRIC Number must be exactly 12 digits long.'
                                            ]),
                                        TextInput::make('ac_phone_number')
                                            ->numeric()->prefix('+60')
                                            ->rules(['digits_between:9,10']) // Ensures exactly 10 digits (optional)
                                            ->required()
                                            ->mask('99-99999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '10'
                                            ])
                                            ->placeholder('Enter mobile number')
                                            ->label('Mobile Number'),

                                        TextInput::make('ac_landline_number')
                                            ->numeric()->prefix('+03')->rules(['nullable', 'digits_between:7,8'])
                                            ->placeholder('Enter landline number')
                                            ->label('Landline Number')
                                            ->mask('9-9999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8'
                                            ]),
                                    ])

                            ])->columns(2)
                        ])->afterValidation(function (Get $get, Set $set, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['id'] = ClinicDetail::where('user_id', $get('user_id'))->first()->id;
                            $data['user_id'] = $get('user_id');
                            $data['signature_type'] = $get('signature_type');
                            $data['dc_name'] = $get('dc_name');
                            $data['dc_nric'] = $get('dc_nric');
                            $data['dc_mmc_number'] = $get('dc_mmc_number');
                            $data['dc_apc_number'] = $get('dc_apc_number');
                            $data['dc_phone_code'] = '+60';
                            $data['dc_landline_code'] = '+03';
                            $data['dc_phone_number'] = $get('dc_phone_number');
                            $data['dc_landline_number'] = $get('dc_landline_number');
                            if ($get('signature_type') == 1 && !empty($get('signature'))) {
                                // $filename = 'signature_'.time().'.png';

                                $data['dc_signature'] = $get('signature');
                            } else {
                                // Image upload
                                $data['dc_signature'] = $get('dc_signature');
                            }

                            $signature = !empty($get('signature')) ? true : false;
                            $data['ac_name'] = $get('ac_name');
                            $data['ac_phone_code'] = '+60';
                            $data['ac_landline_code'] = '+03';
                            $data['ac_phone_number'] = $get('ac_phone_number');
                            $data['ac_landline_number'] = $get('ac_landline_number');
                            $data['ac_nric'] = $get('ac_nric');
                            $data['is_admin_in_charge'] = $get('is_admin_in_charge');
                            // $set('dc_signature', $get('dc_signature'));
                            $data['apc_certificate_expired_date'] = $get('apc_certificate_expired_date');
                            $set('id', $data['id']);
                            $service->storeDoctorInCharge($data, 4, $signature);
                        }),

                    Step::make('Documents')
                        ->icon('heroicon-o-document-text')
                        ->schema([
                            Group::make()->schema([
                                FileUpload::make('dc_signature')
                                    ->visible(false)
                                    // ->label('Signature of Doctor In-charge')
                                    ->label(function ($record) {
                                        $clinicAccountTypeId = $record->clinicData->clinic_account_type_id ?? null;
                                        $label = ($clinicAccountTypeId == 4)
                                            ? 'Signature of Pharmacy In-charge'
                                            : 'Signature of Doctor In-charge';

                                        return $label;
                                    })
                                    ->required()
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.clinic_medias') . $record->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('dc_signature_'),
                                    )
                                    ->disk('public')
                                    ->rules(['required']),
                                FileUpload::make('borang_certificate')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(10 * 1024)])
                                    ->directory(function (Get $get, $record) {
                                        // dd($get('id'));
                                        return config('constants.api.media.clinic_medias') . $get('id') . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('borang_certificate_'),
                                    )
                                    ->label('Upload Certification of Registration(Borang B Borang F)')
                                    ->helperText('Only jpg, jpeg, png, pdf files are allowed')
                                    ->required()
                                    ->disk('public'),
                                FileUpload::make('mmc_certificate')
                                    ->visible(fn($get) => in_array($get('clinic_account_type_id'), [1, 2, 6]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(10 * 1024)])
                                    ->helperText('Only jpg, jpeg, png, pdf files are allowed')
                                    ->label('MMC Full Registration Certificate for a Person In-Charge')
                                    ->validationAttribute('MMC Full Registration Certificate')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.clinic_medias') . $get('id') . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('mmc_certificate_'),
                                    )
                                    ->disk('public')->required(),
                                FileUpload::make('apc_certificate')
                                    ->visible(fn($get) => in_array($get('clinic_account_type_id'), [1, 2, 3, 7, 6, 8]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(10 * 1024)])
                                    ->helperText('Only jpg, jpeg, png, pdf files are allowed')
                                    ->label('Current Annual Practicing Certificate (APC)')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.clinic_medias') . $get('id') . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('apc_certificate_'),
                                    )
                                    ->disk('public')->required(),

                                FileUpload::make('arc_certificate')
                                    ->visible(fn($get) => in_array($get('clinic_account_type_id'), [4, 5]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(10 * 1024)])
                                    ->helperText('Only jpg, jpeg, png, pdf files are allowed')
                                    ->label('Annual Retention Certificate (ARC)')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.clinic_medias') . $get('id') . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('arc_certificate_'),
                                    )
                                    ->disk('public')->required(),

                                FileUpload::make('poison_license')
                                    ->visible(fn($get) => in_array($get('clinic_account_type_id'), [4, 5]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(10 * 1024)])
                                    ->helperText('Only jpg, jpeg, png, pdf files are allowed')
                                    ->label('Poison License')
                                    ->directory(function (Get $get, $record) {
                                        return config('constants.api.media.clinic_medias') . $get('id') . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn(TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('poison_license_'),
                                    )
                                    ->disk('public')->required(),

                                Select::make('apc_certificate_expired_date')
                                    ->label('Expiry Year')->placeholder('Select year')
                                    ->options(self::getExpiryYearOptions())
                                    ->searchable()
                                    ->rules(['required'])->required(),
                            ])->columns(2),
                        ])
                ])
                ->submitAction(
                    Action::make('create')->label('Create')->action('storeData')
                )

                ->columnSpanFull()
            // ->startOnStep($this->user->pcDetails->step),
        ])->statePath('data');
    }


    // protected static function getExpiryYearOptions(): array
    // {
    //     $currentYear = now()->year;
    //     $startYear = $currentYear; // Adjust the range as needed
    //     $endYear = $currentYear + 10; // Adjust future years if necessary

    //     return array_combine(
    //         range($startYear, $endYear),
    //         range($startYear, $endYear)
    //     );
    // }

    protected static function getExpiryYearOptions(): array
    {
        $currentYear = now()->year;
        $startYear = $currentYear; // Adjust the range as needed
        $endYear = $currentYear + 1; // Adjust future years if necessary

        return array_combine(
            range($startYear, $endYear),
            range($startYear, $endYear)
        );
    }

    protected static function getYearOptions(): array
    {
        $currentYear = now()->year;
        $startYear = $currentYear; // Adjust the range as needed
        $endYear = $currentYear - 50; // Adjust future years if necessary

        return array_combine(
            range($startYear, $endYear),
            range($startYear, $endYear)
        );
    }



    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return User::query()->role('Clinic')->with('clinicDetails', 'address.city', 'address.state', 'billingAddress.city', 'billingAddress.state');
            })->recordClasses(function (Model $record) {
                return match ($record->verification_status) {
                    'approved' => 'bg-green-50',
                    'pending', 'send_for_approval' => 'bg-yellow-50',
                    'rejected' => 'bg-red-50',
                    default => 'bg-gray-50',
                };
            })
            ->columns([
                TextColumn::make('id')->label('Facility Id')->sortable()->searchable()->toggleable(),
                TextColumn::make('clinicDetails.clinic_name')->label('Facility Name')->searchable()->sortable()->toggleable(),
                TextColumn::make('clinicDetails.clinicAccountType.name')->label('Facility Type')->searchable()->toggleable(),
                TextColumn::make('billingAddress.city.name')->searchable()->toggleable(),
                TextColumn::make('billingAddress.state.name')->searchable()->toggleable(),
                TextColumn::make('email')->searchable()->toggleable(),
                TextColumn::make('clinicDetails.tier')
                    ->label('Tier')
                    ->searchable()->toggleable()
                    ->badge()
                    ->default('-')
                    ->formatStateUsing(fn($state): string => ucfirst($state))
                    ->color(function ($state) {
                        $st = match ($state) {
                            'gold' => 'warning',
                            'silver' => 'success',
                            'bronze' => 'danger',
                            default =>  null,
                        };
                        return $st;
                    }),

                TextColumn::make('created_at')->label('Registered On')->dateTime('M d, Y')->searchable()->toggleable(),
                // TextColumn::make('company_name')->sortable()->searchable()->toggleable(),
                TextColumn::make('verification_status')
                    ->label('Verification Status')
                    ->searchable()
                    ->toggleable()
                    ->badge()
                    ->formatStateUsing(fn($state): string => match ($state) {
                        'send_for_approval' => 'Pending',
                        'approved' => 'Accepted',
                        default => ucfirst($state),
                    })
                    ->color(fn($state) => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'warning',
                    }),

                ToggleColumn::make('status')->label('Status')->toggleable()
                    ->afterStateUpdated(function () {
                        Notification::make()
                            ->success()
                            ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                            ->duration(1000)
                            ->body('Status has been updated successfully.')
                            ->send();
                    })->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),

            ])->defaultSort('id', 'desc')
            ->filters([

                SelectFilter::make('clinicDetails.clinic_name')
                    ->label('Facility Name')
                    ->relationship('clinicDetails', 'clinic_name', fn(Builder $query) => $query->whereNotNull('clinic_name'))
                    ->searchable()
                    ->preload(),

                SelectFilter::make('clinicDetails.clinic_account_type_id') // or whatever the foreign key is named
                    ->label('Facility Type')
                    ->relationship('clinicDetails.clinicAccountType', 'name', fn(Builder $query) => $query->whereNotNull('name'))
                    ->searchable()
                    ->preload(),

                SelectFilter::make('state')
                    ->label('State')
                    ->options(function () {
                        return UserAddress::whereHas('clinicDetails')
                            ->with('state')
                            ->get()
                            ->pluck('state.name', 'state.id')
                            ->filter()
                            ->unique()
                            ->sort()
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) return;
                        $query->whereHas('clinicDetails.billingAddress', function ($q) use ($state) {
                            $q->where('state_id', $state['value']);
                        });
                    }),
                SelectFilter::make('city')
                    ->label('City')
                    ->options(function () {
                        return UserAddress::whereHas('clinicDetails')
                            ->with('city')
                            ->get()
                            ->pluck('city.name', 'city.id')
                            ->filter()
                            ->unique()
                            ->sort()
                            ->toArray();
                    })
                    ->searchable()
                    ->preload()
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) return;

                        $query->whereHas('clinicDetails.billingAddress', function ($q) use ($state) {
                            $q->where('city_id', $state['value']);
                        });
                    }),
                Filter::make('created_at')
                    ->label('Registered On')
                    ->form([
                        DatePicker::make('created_from')
                            ->maxDate(fn($get) => $get('created_until') ?? now())
                            ->closeOnDateSelection()->label('Registered From'),
                        DatePicker::make('created_until')
                            ->minDate(fn($get) => $get('created_from'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->label('Registered Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),
                SelectFilter::make('verification_status')
                    ->label('Verification Status')
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Accepted',
                        'rejected' => 'Rejected',
                    ])
                    ->searchable()
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) {
                            return;
                        }
                        if ($state['value'] === 'pending') {
                            // Show both 'pending' and 'send_for_approval' as 'Pending'
                            $query->whereIn('verification_status', ['pending', 'send_for_approval']);
                        } else {
                            $query->where('verification_status', $state['value']);
                        }
                    }),
                SelectFilter::make('tier')
                    ->label('Tier')
                    ->options([
                        'gold' => 'Gold',
                        'silver' => 'Silver',
                        'bronze' => 'Bronze',
                    ])
                    ->query(function (Builder $query, $state) {
                        if (!$state['value']) {
                            return;
                        }
                        $query->whereHas('clinicDetails', function ($query) use ($state) {
                            $query->whereRaw('LOWER(tier) = ?', [strtolower($state['value'])]);
                        });
                    }),

                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ])
                    ->query(function (Builder $query, $state) {
                        if ($state['value'] === null) return;
                        if ($state['value']) {
                            $query->where('status', true);
                        } else {
                            $query->where(function ($query) {
                                $query->where('status', false)
                                    ->orWhereNull('status'); // Include null values as inactive
                            });
                        }
                    }),
            ])->filtersFormWidth('2xl')
            ->filtersFormColumns(2)
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->tooltip(function (Model $record) {
                        return  "Edit";
                    })
                    ->visible(function () {

                        return  auth()->user()->hasRole('Super Admin') || auth()->user()->can('facility_update');
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),

                // Tables\Actions\DeleteAction::make()->iconButton(),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip(function (Model $record) {
                        return $record->clinicDetails?->is_submitted == false ? "Review" : "View";
                    })
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
               
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            ->title('Facilities Activated')
                            ->body('The selected facilities have been activated successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => false]);
                        });
                        Notification::make()
                            ->title('Facilities Deactivated')
                            ->body('The selected facilities have been deactivated successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }



    // public static function getRelations(): array
    // {
    //     return [
    //         //
    //     ];
    // }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListClinics::route('/'),
            'create' => Pages\CreateClinic::route('/create'),
            // 'view' => Pages\ViewClinicOnboardingPage::route('/{record}/view'),
            'view' => Pages\ViewClinic::route('/{record}'),
            'edit' => Pages\ClinicOnboardingPage::route('/{record}/edit'),
        ];
    }

    public function deleteSupplier($supplierId)
    {
        $supplier = ClinicPharmaSupplier::find($supplierId);

        if ($supplier && $supplier->clinic_id == $this->record) {
            $supplier->delete();
            Notification::make()
                ->title('Supplier deleted successfully')
                ->success()
                ->send();
        }
    }

    public function getAnnualPurchaseAmount($record)
    {
        $TotalAnnualPurchases = Order::where('user_id', $record->id)->where('status', 'delivered')->sum('amount');
        return $TotalAnnualPurchases;
    }

    public function getPrefixCode($cityId = null)
    {
        $codes = $cityId
            ? City::where('id', $cityId)->pluck('landline_code', 'landline_code')
            : City::selectRaw('landline_code')
            ->whereNotNull('landline_code')
            ->groupBy('landline_code')
            ->pluck('landline_code', 'landline_code');

        return $codes;
    }

    public function getApprovalChanges($userId, $steps)
    {

        $data =  Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('approved_by', null)
            ->where('approved_at', null)
            ->where('rejected_at', null)
            ->where('rejected_by', null)
            ->orderBy('created_at', 'desc')
            ->select('new_data', 'original_data')
            ->first();

        // $data->transform(function ($item) {
        //     $item->new_data = json_decode($item->new_data, true);
        //     $item->original_data = json_decode($item->original_data, true);
        //     return $item;
        // });

        if ($data) {
            $data->new_data = json_decode($data->new_data, true);
            $data->original_data = json_decode($data->original_data, true);
        }
        // dd($data);
        return $data;
    }


    public function organizeApprovalChanges($approvals)
    {
        if (!$approvals) {
            return [];
        }
        $changes = [];

        // foreach ($approvals as $approval) {
        $newData = $approvals->new_data;
        $originalData = $approvals->original_data;

        foreach ($newData as $key => $value) {
            if (!isset($changes[$key])) {
                $changes[$key] = [];
            }

            $changes[$key][] = [
                'new_value' => $value,
                'original_value' => $originalData[$key] ?? 'N/A',
            ];
        }
        // }

        return $changes;
    }

    public function getBillingAddressChanges($approvals)
    {
        if (!$approvals) {
            return [];
        }
        $billingChanges = [];

        // foreach ($approvals as $approval) {
        $newData = $approvals->new_data;
        $originalData = $approvals->original_data;

        if (isset($newData['address_type']) && $newData['address_type'] === 'billing') {
            foreach ($newData as $key => $value) {
                if (!isset($billingChanges[$key])) {
                    $billingChanges[$key] = [];
                }

                $billingChanges[$key][] = [
                    'new_value' => $value,
                    'original_value' => $originalData[$key] ?? 'N/A',
                ];
            }
        }
        // }

        return $billingChanges;
    }

    // public function getShippingAddressChanges($approvals)
    // {
    //     $shippingChanges = [];

    //     foreach ($approvals as $approval) {
    //         $newData = $approval->new_data;
    //         $originalData = $approval->original_data;

    //         // Check if the address type is 'shipping'
    //         if (isset($newData['address_type']) && $newData['address_type'] === 'shipping') {
    //             // Loop through $newData to find nested address data
    //             foreach ($newData as $key => $value) {
    //                 // Check if the key is numeric (indicating nested address data)
    //                 if (is_numeric($key) && is_array($value)) {
    //                     // Loop through the nested address data
    //                     foreach ($value as $field => $newValue) {
    //                         // Initialize the field in $shippingChanges if it doesn't exist
    //                         if (!isset($shippingChanges[$field])) {
    //                             $shippingChanges[$field] = [];
    //                         }

    //                         // Add the changes for this field
    //                         $shippingChanges[$field][] = [
    //                             'new_value' => $newValue,
    //                             'original_value' => $originalData[$key][$field] ?? 'N/A',
    //                         ];
    //                     }
    //                 }
    //             }
    //         }
    //     }

    //     return $shippingChanges;
    // }

    public function getShippingAddressChanges($approvals)
    {
        if (!$approvals) {
            return [];
        }

        $shippingChanges = [];
        $newAddressCounter = 0; // To track new addresses without IDs

        foreach ($approvals as $approval) {
            $newData = $approval->new_data;
            $originalData = $approval->original_data;

            if (isset($newData['address_type']) && $newData['address_type'] === 'shipping') {
                foreach ($newData as $key => $value) {
                    if (is_numeric($key) && is_array($value)) {
                        // Handle both cases - with ID and without ID
                        if (isset($value['id'])) {
                            $addressId = $value['id'];
                        } else {
                            $addressId = 'new_' . $newAddressCounter++;
                        }

                        foreach ($value as $field => $newValue) {
                            if (!in_array($field, ['id', 'address_type'])) {
                                if (!isset($shippingChanges[$addressId][$field])) {
                                    $shippingChanges[$addressId][$field] = [];
                                }

                                // Get original value - handle both nested and flat structures
                                $originalValue = 'N/A';
                                if (isset($originalData[$key])) {
                                    $originalValue = $originalData[$key][$field] ?? 'N/A';
                                } elseif (isset($originalData[$field])) {
                                    $originalValue = $originalData[$field] ?? 'N/A';
                                }

                                $shippingChanges[$addressId][$field][] = [
                                    'new_value' => $newValue,
                                    'original_value' => $originalValue,
                                ];
                            }
                        }
                    }
                }
            }
        }

        return $shippingChanges;
    }
    protected function organizeDocumentChanges($documentApprovals)
    {
        if (!$documentApprovals) {
            return [];
        }
        $documentChanges = [];

        // foreach ($documentApprovals as $approval) {
        $newData = $documentApprovals->new_data; // Assuming this is already decoded as array
        $originalData = $documentApprovals->original_data ?? [];

        foreach (['borang_certificate', 'mmc_certificate', 'apc_certificate', 'arc_certificate', 'poison_license', 'other_relevant_documents'] as $docType) {
            if (isset($newData[$docType])) {
                if (!isset($documentChanges[$docType])) {
                    $documentChanges[$docType] = [];
                }

                // Extract just the filenames from new_data
                $newFiles = array_map(function ($file) {
                    return $file['name'] ?? null;
                }, $newData[$docType]);

                // Extract just the filenames from original_data if needed
                $originalFiles = array_map(function ($file) {
                    return $file['name'] ?? null;
                }, $originalData[$docType] ?? []);

                $documentChanges[$docType][] = [
                    'new_value' => $newFiles,  // Array of just filenames
                    'original_value' => $originalFiles, // Array of just filenames
                ];
            }
        }

        foreach ($newData as $key => $value) {
            if (str_starts_with($key, 'removed_')) {
                $docType = str_replace('removed_', '', $key);

                if (!isset($documentChanges[$docType])) {
                    $documentChanges[$docType] = [];
                }
                // dd($value);
                // Get filenames from clinic_certificate_files table
                $removedFiles = ClinicCertificateFile::whereIn('id', $value)
                    ->pluck('name')
                    ->toArray();
                // $removedFiles = ClinicCertificateFile::whereIn('id', $value);
                // dd($removedFiles);
                // Get original files (if any)
                $originalFiles = array_map(function ($file) {
                    return $file['name'] ?? null;
                }, $originalData[$docType] ?? []);

                $documentChanges[$docType][] = [
                    'new_value' => $removedFiles, // Empty array since files were removed
                    'original_value' => $removedFiles, // The files that were removed
                ];
            }
        }
        // }
        // dd($documentChanges);
        return $documentChanges;
    }

    public function getPendingFilenames(array $changes): array
    {
        $filenames = [];

        foreach ($changes as $change) {
            // if ($change['status'] === 'pending') {
            foreach ($change['new_value'] as $file) {
                if (is_array($file)) {
                    $filenames[] = $file['name'] ?? null;
                } else {
                    $filenames[] = $file;
                }
            }
            // }
        }
        // dd($filenames);
        return $filenames;
    }

    protected static function makeAcceptAction(string $step): \Filament\Infolists\Components\Actions\Action
    {
        return \Filament\Infolists\Components\Actions\Action::make('accept')
            ->color('success')
            ->label(function () use ($step) {
                $stepEnum = is_string($step) ? OnboardingStep::from($step) : $step;

                return match ($stepEnum) {
                    OnboardingStep::DOCTOR_INCHARGE => 'Accept Admin/Doctor In-charge',
                    OnboardingStep::BASIC_INFO => 'Accept Basic/Facility Details',
                    OnboardingStep::DOCUMENTS => 'Accept Documents',
                    default => 'Accept'
                };
            })
            ->outlined()
            ->button()
            ->requiresConfirmation()
            ->visible(fn($record) => Approval::pendingForSteps($step)
                ->where('user_type', 'facility')
                ->where('approvalable_id', $record->id)
                ->exists())
            ->modalHeading('Confirm Approval')
            ->modalDescription('Are you sure you want to approve these changes? This action cannot be undone.')
            ->modalSubmitActionLabel('Confirm')
            ->action(function ($record) use ($step) {
                $approvalData = static::getPendingApproval($record->id, $step);
                if (!$approvalData) {
                    Notification::make()
                        ->title('No pending approval found')
                        ->danger()
                        ->send();
                    return;
                }

                $newData = json_decode($approvalData->new_data, true);
                // dd(OnboardingStep::ADDRESS);
                // Handle different approval steps
                switch ($step) {
                    case OnboardingStep::BASIC_INFO->value:
                        // $record->clinicData->update(['is_version_pending' => false, 'is_restricted' => false]);
                        $record->clinicData->update(
                            Arr::only($newData, [
                                'company_name',
                                'company_number',
                                'clinic_owner',
                                'clinic_year',
                                'tin_number',
                                'sst_number',
                                'mobile_number',
                                'landline_number',
                                'clinic_number',
                                'clinic_account_type_id',
                                'clinic_name',
                                'clinic_owner',
                                'mobile_number',
                                'business_type_id',
                                'landline_code'
                            ])
                        );
                        break;

                    case OnboardingStep::ADDRESS->value:
                        $record->pcDetails->update(['is_version_pending' => false, 'is_restricted' => false]);
                        if ($record->warehouses()->exists()) {
                            $warehouse = $record->warehouses()->first();
                            if ($newData['warehouse_type'] === 'dpharma') {
                                $updateData = [
                                    'address_1' => $newData['warehouse_address_1'] ?? $warehouse->address_1,
                                    'address_2' => $newData['warehouse_address_2'] ?? $warehouse->address_2,
                                    'postal_code' => $newData['warehouse_postal_code'] ?? $warehouse->postal_code,
                                    'state_id' => $newData['warehouse_state'],
                                    'city_id' => $newData['warehouse_city'],
                                    'warehouse_type' => $newData['warehouse_type'],
                                ];
                                if (isset($newData['warehouse_state'])) {
                                    $state = State::where('name', $newData['warehouse_state'])->first();
                                    if ($state) {
                                        $updateData['state_id'] = $state->id;
                                    }
                                }
                                $record->pcDetails->update(['delivery_days' => null, 'delivery_days_west' => null, 'min_order_value' => null]);
                            } else {
                                $updateData = [
                                    'address_1' => null,
                                    'address_2' => null,
                                    'city_id' => null,
                                    'state_id' => null,
                                    'ware_region' => null,
                                    'postal_code' => null,
                                    'warehouse_type' => $newData['warehouse_type'],
                                ];
                            }
                            $warehouse->update($updateData);
                        }
                        break;

                    case OnboardingStep::DOCUMENTS->value:
                        // $record->clinicData->update(['is_version_pending' => false, 'is_restricted' => false]);

                        $certificateTypes = [
                            'borang_certificate',
                            'mmc_certificate',
                            'apc_certificate',
                            'arc_certificate',
                            'poison_license',
                            'license_permit',
                            'company_registration_certificate',
                            'other_relevant_documents'
                        ];
                        foreach ($certificateTypes as $type) {
                            if (isset($newData[$type])) {
                                $newFiles = array_map(function ($file) {
                                    return is_array($file) ? ($file['name'] ?? null) : $file;
                                }, is_array($newData[$type]) ? $newData[$type] : [$newData[$type]]);

                                // Deactivate existing active records for this type
                                // ClinicCertificateFile::where([
                                //     'user_id' => $record->id,
                                //     'type' => $type
                                // ])->delete();
                                ClinicCertificateFile::where([
                                    'user_id' => $record->id,
                                    'type' => $type,

                                ])->whereIn('name', $newFiles) // Only target files not being kept
                                    ->update(['status' => 'inactive']);
                                // Handle both single and multiple files
                                $files = is_array($newData[$type]) ? $newData[$type] : [$newData[$type]];

                                foreach ($files as $fileData) {
                                    // Handle both array format (from approval) and direct filename
                                    $filename = is_array($fileData) ? ($fileData['name'] ?? null) : $fileData;

                                    if ($filename) {
                                        ClinicCertificateFile::create([
                                            'user_id' => $record->id,
                                            'type' => $type,
                                            'name' => $filename,
                                            'status' => 'active'
                                        ]);
                                    }
                                }
                            }

                            $removedKey = "removed_{$type}";
                            if (isset($newData[$removedKey])) {
                                $removedIds = is_array($newData[$removedKey]) ? $newData[$removedKey] : [$newData[$removedKey]];
                                ClinicCertificateFile::where([
                                    'user_id' => $record->id,
                                    'type' => $type
                                ])
                                    ->whereIn('id', $removedIds)
                                    ->update(['status' => 'inactive']);
                            }
                        }
                        break;

                    case OnboardingStep::DOCTOR_INCHARGE->value:
                        // Update clinic_details table
                        $record->clinicData->updateOrCreate(
                            ['user_id' => $record->id],
                            Arr::only($newData, [
                                'dc_name',
                                'dc_nric',
                                'dc_mmc_number',
                                'dc_apc_number',
                                'dc_phone_number',
                                'dc_landline_number',
                                'signature_type',
                                'ac_name',
                                'ac_nric',
                                'ac_phone_number',
                                'ac_landline_number',
                                'ac_landline_code',
                                'dc_landline_code',
                            ])
                        );
                        break;
                }

                $approvalData->approve($record->id, $step);
                Mail::to($record->email)->send(new ChangesApprovedMail($record));

                Notification::make()
                    ->title('Changes Approved Successfully')
                    ->success()
                    ->send();
            });
    }


    protected static function makeRejectAction(string $step): \Filament\Infolists\Components\Actions\Action
    {
        return \Filament\Infolists\Components\Actions\Action::make('reject')
            ->color('danger')
            ->outlined()
            // ->label($step == OnboardingStep::DOCTOR_INCHARGE->value ? 'Reject Admin/Doctor In-charge' : 'Reject')
            ->label(function () use ($step) {
                $stepEnum = is_string($step) ? OnboardingStep::from($step) : $step;

                return match ($stepEnum) {
                    OnboardingStep::DOCTOR_INCHARGE => 'Reject Admin/Doctor In-charge',
                    OnboardingStep::BASIC_INFO => 'Reject Basic/Facility Details',
                    OnboardingStep::DOCUMENTS => 'Reject Documents',
                    default => 'Reject'
                };
            })
            ->button()
            ->requiresConfirmation()
            ->visible(fn($record) => Approval::pendingForSteps($step)
                ->where('user_type', 'facility')
                ->where('approvalable_id', $record->id)
                ->exists())
            ->modalHeading('Confirm Rejection')
            ->modalDescription('Are you sure you want to reject these changes? This action cannot be undone.')
            ->modalSubmitActionLabel('Confirm')
            ->action(function ($record) use ($step) {
                $approvalData = static::getPendingApproval($record->id, $step);

                if (!$approvalData) {
                    Notification::make()
                        ->title('No Data Found For Rejection')
                        ->danger()
                        ->send();
                    return;
                }

                $approvalData->reject(auth()->id(), $step);
                Mail::to($record->email)->send(new ChangesRejectedMail($record));
                Notification::make()
                    ->title('Changes Rejected Successfully')
                    ->success()
                    ->send();
            });
    }


    protected static function getPendingApproval($userId, $steps)
    {
        return Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('user_type', 'facility')
            ->pending()
            ->latest()
            ->first();
    }

    public function getShippingApprovalChanges($userId, $steps)
    {

        $data =  Approval::where('approvalable_id', $userId)
            ->where('approvalable_type', 'App\Models\User')
            ->where('steps', $steps)
            ->where('approved_by', null)
            ->where('approved_at', null)
            ->where('rejected_at', null)
            ->where('rejected_by', null)
            ->orderBy('created_at', 'desc')
            ->select('new_data', 'original_data')
            ->get();

        $data->transform(function ($item) {
            $item->new_data = json_decode($item->new_data, true);
            $item->original_data = json_decode($item->original_data, true);
            return $item;
        });

        // if ($data) {
        //     $data->new_data = json_decode($data->new_data, true);
        //     $data->original_data = json_decode($data->original_data, true);
        // }
        // dd($data);
        return $data;
    }

    protected static function formatLandlinePendingChanges($currentValue, $changes = null)
    {
        $output = "{$currentValue}<br>";

        if ($changes) {
            // Handle landline number changes
            if (isset($changes['number'])) {
                foreach ($changes['number'] as $change) {
                    if ($change['new_value'] != $currentValue) {
                        $newNumber = $change['new_value'];
                        $newCode = $changes['code'][0]['new_value'] ?? '';
                        $formatted = $newCode ? '+' . $newCode . ' ' . $newNumber : $newNumber;
                        $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                    }
                }
            }

            // Handle case where only code changes
            if (isset($changes['code']) && !isset($changes['number'])) {
                foreach ($changes['code'] as $change) {
                    $currentNumber = explode(' ', $currentValue)[1] ?? '';
                    $formatted = '+' . $change['new_value'] . ' ' . $currentNumber;
                    $output .= "<span style='background-color: yellow;'>{$formatted}</span><br>";
                }
            }
        }

        return new HtmlString($output);
    }

    protected static function getCityState($stateId, $cityId)
    {
        $stateName = null;
        $cityName = null;

        if ($stateId) {
            $stateName = State::where('id', $stateId)->value('name');
        }

        if ($cityId) {
            $cityName = City::where('id', $cityId)->value('name');
        }

        return [
            'state_name' => $stateName,
            'city_name' => $cityName
        ];
    }
}
