<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\GenericNameResource\Pages;
use App\Filament\Admin\Resources\GenericNameResource\RelationManagers;
use App\Models\DosageForm;
use App\Models\GenericName;
use App\Models\Product;
use App\Models\ProductRelation;
use App\Rules\CaseSensitiveUnique;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Support\Collection;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class GenericNameResource extends Resource
{
    protected static ?string $model = GenericName::class;

    protected static ?string $navigationGroup = 'Master';
    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('generic-names_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('generic-names_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('generic-names_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('generic-names_delete');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    // Section::make()->schema([
                    TextInput::make('name')
                        ->label(new HtmlString("Generic Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->maxLength(100)
                        ->placeholder('Enter Generic Name')
                        ->rules([
                            'required',
                            // 'regex:/^[\w\s\p{P}]+$/u', 
                            'max:100',
                            fn(Get $get) => new CaseSensitiveUnique(GenericName::class, 'name', $get('id'))
                        ])
                        ->validationMessages([
                            'required' => __('message.generic_name.required'),
                            // 'regex' => __('message.generic_name.regex'),
                            'max' => __('message.generic_name.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.generic_name.case_sensitive_unique'),
                        ]),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Generic Name')->sortable()->searchable()->toggleable(),
                ToggleColumn::make('status')
                    ->label('Status')
                    ->toggleable()
                    ->sortable()
                    ->disabled(function ($record) {
                        return Product::query()->where('generic_name_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('generic_name_id', $record->id)->exists()) {

                            Notification::make()
                                ->danger() // Changed to danger from warning to match your second example
                                // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                                ->title(__('message.generic_name.status_warning', ['names' => $record->name]))
                                ->send();
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                            ->duration(1000)
                            ->title(__('message.generic_name.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ])

            ])

            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !Product::query()->where('generic_name_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        
                            $record->delete();
                            Notification::make()
                                ->success()
                                // ->title(__('message.generic_name.title.deleted'))
                            ->title(__('message.generic_name.delete_success'))
                            ->send();

                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = DB::select("SELECT * FROM products WHERE generic_name_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('generic_name_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name; // Assuming "name" is the attribute for display
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.generic_name.title.deletion_completed'))
                                ->title(__('message.generic_name.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.generic_name.title.partial_deleted'))
                                ->title(__('message.generic_name.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.generic_name.title.activated'))
                            ->title(__('message.generic_name.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('generic_name_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name; // Assuming 'name' is the field you want to show
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.generic_name.title.deactivated'))
                                ->title(__('message.generic_name.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.generic_name.title.partial_inactivated'))
                                ->title(__('message.generic_name.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })
                    ->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGenericNames::route('/'),
            'create' => Pages\CreateGenericName::route('/create'),
            'edit' => Pages\EditGenericName::route('/{record}/edit'),
        ];
    }
}
