<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ShippingAddressRequestResource\Pages;
use App\Filament\Admin\Resources\ShippingAddressRequestResource\RelationManagers;
use App\Mail\AddressApprovedMail;
use App\Mail\AddressRejectedMail;
use App\Models\ClinicCertificateFile;
use App\Models\ClinicDetail;
use App\Models\ShippingAddressRequest;
use App\Models\UserAddress;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class ShippingAddressRequestResource extends Resource
{
    protected static ?string $model = UserAddress::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Pending Requests';
    protected static ?string $navigationLabel = 'Shipping Address';
    protected static ?int $navigationSort = 2;

    public static function canAccess(): bool
    {

        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_view shipping address');
    }

    public static function canApprove(): bool
    {

        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_shipping address approve');
    }

    public static function canReject(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_shipping address reject');
    }

    public static function canViewDocument(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_view document');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(UserAddress::Select('user_id', DB::raw('MAX(id) as id'), DB::raw('MAX(created_at) as requested_date'))->where('is_requested', true)->where('is_approved', false)
                ->where('status', 'pending')->with(['clinicDetail'])->groupBy('user_id')->orderBy('id', 'desc'))
            ->columns([
                TextColumn::make('clinicDetail.clinic_number')
                    ->label('Facility ID')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(fn($state): string => '#' . $state),
                TextColumn::make('user.name')
                    ->label('Facility Name')->searchable(),
                TextColumn::make('clinicDetail.clinicAccountType.name')
                    ->label('Facility Type'),
                TextColumn::make('requested_date')
                    ->label('Requested Date')
                    ->formatStateUsing(function ($state): string {
                        if (empty($state)) {
                            return '-';
                        }
                        $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                        $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                        return $convertedDate->format('M d, Y | h:i A');
                    })
                    ->sortable()
                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('user.name')
                    ->relationship('user', 'name')
                    ->label('Facility Name')
                    ->searchable(),
                SelectFilter::make('clinicDetail.clinicAccountType.name')
                    ->relationship('clinicDetail.clinicAccountType', 'name')
                    ->label('Facility Type'),
                Filter::make('requested_date')
                    ->label('Requested Date')
                    ->form([
                        DatePicker::make('created_from')->label('Requested From')->closeOnDateSelection()->reactive(),
                        DatePicker::make('created_until')->label('Requested Until')->closeOnDateSelection()->minDate(function ($get) {
                            return $get('created_from') ? Carbon::parse($get('created_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                    }),
            ])
            ->actions([
                Action::make('viewDocuments')
                    ->visible(fn() => auth()->user()->hasAnyRole(['Super Admin', 'Pharmaceutical Company']) || auth()->user()->can('pending-requests_can view documents'))
                    ->label('')
                    ->tooltip('View Documents')
                    ->visible(fn($record) => static::canViewDocument($record))
                    ->icon('heroicon-o-document-text')->size('sm')->iconButton()
                    ->color('primary')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-800', 'style' => 'margin-left: inherit; border-color:rgb(86, 86, 94);'])
                    ->modalHeading('Documents')
                    ->modalContent(function (UserAddress $record) {
                        $borangCertificate = ClinicCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->where('type', 'borang_certificate')->first();
                        $mmcCertificate = ClinicCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->where('type', 'mmc_certificate')->first();
                        $apcCertificate = ClinicCertificateFile::where('user_id', $record->user_id)->where('status', 'active')->where('type', 'apc_certificate')->first();

                        // Get the image paths from the two columns
                        $borangCertificate = $borangCertificate ? $borangCertificate->name : null;
                        $mmcCertificate = $mmcCertificate ? $mmcCertificate->name : null;
                        $apcCertificate = $apcCertificate ? $apcCertificate->name : null;



                        return view('filament.admin.resources.shipping-address-request-resource.pages.document-modal-content', [
                            'borangCertificate' => $borangCertificate ? [
                                'name' => $borangCertificate,
                                'record' => $record->clinicDetail->id,
                            ] : null,
                            'mmcCertificate' => $mmcCertificate ? [
                                'name' => $mmcCertificate,
                                'record' => $record->clinicDetail->id,
                            ] : null,
                            'apcCertificate' => $apcCertificate ? [
                                'name' => $apcCertificate,
                                'record' => $record->clinicDetail->id,
                            ] : null,

                        ]);
                    })
                    ->modalActions([
                        Action::make('close')
                            ->label('Close')
                            ->color('gray')
                            ->after(function () {
                                redirect(static::getUrl('index'));
                            }),
                    ]),
                Action::make('view')
                    ->label('')
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->color('gray')
                    ->modalHeading('Shipping Address')
                    ->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->modalContent(function (UserAddress $record) {
                        $requests = UserAddress::where('is_requested', true)
                            ->where('is_approved', false)
                            ->where('status', 'pending')
                            ->where('user_id', $record->user_id)
                            ->with(['city', 'state', 'country'])
                            ->get();
                        return view('filament.admin.resources.shipping-address-request-resource.pages.modal-content', [
                            'requests' => $requests,
                        ]);
                    })
                    ->modalActions([
                        Action::make('approveAll')
                            ->label('Approve All')
                            ->tooltip('Approve All')
                            ->color('success')
                            ->visible(function ($record) {
                                if (!$record) {
                                    return false;
                                }
                                if (!static::canApprove($record)) {
                                    return false;
                                }

                                $hasPendingRequests = UserAddress::where('is_requested', true)
                                    ->where('is_approved', false)
                                    ->where('status', 'pending')
                                    ->where('user_id', $record->user_id)
                                    ->exists();

                                return $hasPendingRequests;
                            })
                            ->action(function ($record) {
                                UserAddress::where('is_requested', true)
                                    ->where('is_approved', false)
                                    ->where('status', 'pending')
                                    ->where('user_id', $record->user_id)
                                    ->update([
                                        'is_approved' => true,
                                        'status' => 'approved',
                                        'is_requested' => false
                                    ]);

                                //Activity Log Start
                                $clinicName = $record->clinicDetail->clinic_name ?? ($record->clinicDetail->name ?? 'N/A');
                                $currentUser = auth()?->user();
                                activity()
                                    ->causedBy($currentUser)
                                    ->performedOn($record)
                                    ->useLog('shipping_address_request')
                                    ->withProperties([
                                        'old' => [
                                            'status' => 'pending',
                                        ],
                                        'attributes' => array_filter([
                                            'status' => 'approved',
                                        ], fn($value) => !is_null($value)),
                                    ])
                                    ->log("All Shipping address of Facility ({$clinicName}) request has been approved");
                                //Activity Log End

                                Notification::make()
                                    ->title('Shipping Address Approved')
                                    ->success()
                                    ->send();
                                $record = $record->user;
                                Mail::to($record->email)->send(new AddressApprovedMail($record));
                                redirect(static::getUrl('index'));
                                // Optionally, you can add a notification here
                            }),
                        Action::make('cancel')
                            ->label('Cancel')
                            ->color('gray')
                            ->tooltip('Cancel')
                            ->visible(function ($record) {
                                if (!$record) {
                                    return false;
                                }
                                if (!static::canApprove($record)) {
                                    return false;
                                }

                                $hasPendingRequests = UserAddress::where('is_requested', true)
                                    ->where('is_approved', false)
                                    ->where('status', 'pending')
                                    ->where('user_id', $record->user_id)
                                    ->exists();

                                return $hasPendingRequests;
                            })
                            ->after(function () {
                                redirect(static::getUrl('index'));
                            }),
                    ]),
                Action::make('approve')
                    ->visible(fn($record) => static::canApprove($record))
                    ->label('')
                    ->color('success')
                    ->tooltip('Approve')
                    ->icon('heroicon-m-check')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->action(function ($record) {
                        $record->loadMissing('clinicDetail');
                        UserAddress::where('user_id', $record->user_id)->update([
                            'status' => 'approved',
                            'is_approved' => true,
                            'is_requested' => false
                        ]);

                        //Activity Log Start
                        // Use the correct property for clinic name; fallback to 'N/A' if not available
                        $clinicName = $record->clinicDetail->clinic_name ?? ($record->clinicDetail->name ?? 'N/A');
                        $status = $record->status ?? 'approved';

                        activity()
                            ->causedBy(auth()->user())
                            ->performedOn($record)
                            ->useLog('shipping_address_request')
                            ->withProperties([
                                'old' => [
                                    'status' => 'pending',
                                ],
                                'attributes' => array_filter([
                                    'status' => $status,
                                ], fn($value) => !is_null($value)),
                            ])
                            ->log($clinicName . "'s shipping address request has been approved");
                        //Activity Log End

                        Notification::make()
                            ->title('Shipping Address Approved')
                            ->success()
                            ->send();
                        $record = $record->user;
                        Mail::to($record->email)->send(new AddressApprovedMail($record));
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to approve this shipping address?');
                        return $action;
                    }),
                Action::make('reject')
                    // ->visible(fn() => auth()->user()->can('pending-requests_can reject') || auth()->user()->hasAnyRole(['Super Admin', 'Pharmaceutical Company']))
                    ->visible(fn($record) => static::canReject($record))
                    ->label('')
                    ->tooltip('Reject')
                    ->icon('heroicon-m-x-mark')->size('sm')->iconButton()
                    ->color('danger')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->action(function ($record) {

                        //Activity Log Start
                        // $record->loadMissing('clinicDetail');
                        //Activity Log End
                        UserAddress::where('user_id', $record->user_id)
                            ->where('is_requested', true)
                            ->where('is_approved', false)
                            ->where('status', 'pending')
                            ->update(['status' => 'rejected', 'is_approved' => true, 'is_requested' => false]);

                        //Activity Log Start
                        $clinicName = $record->clinicDetail->clinic_name ?? ($record->clinicDetail->name ?? 'N/A');

                        $status = 'rejected';

                        activity()
                            ->causedBy(auth()->user())
                            ->performedOn($record)
                            ->useLog('shipping_address_request')
                            ->withProperties([
                                'old' => [
                                    'status' => 'pending',
                                ],
                                'attributes' => array_filter([
                                    'status' => $status,
                                ], fn($value) => !is_null($value)),
                            ])
                            ->log($clinicName . "'s shipping address request has been rejected");
                        //Activity Log End

                        Notification::make()
                            ->title('Shipping Address Rejected')
                            ->success()
                            ->send();
                        $record = $record->user;
                        Mail::to($record->email)->send(new AddressRejectedMail($record));
                    })->requiresConfirmation(function (Tables\Actions\Action $action, $record) {
                        $action->modalHeading('Are you sure you want to reject this shipping address?');
                        return $action;
                    })
            ])->actionsColumnLabel('Actions')
            ->bulkActions([
                Tables\Actions\BulkAction::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $recordsNew = UserAddress::whereIn('user_id', $records->pluck('user_id'))->where('is_requested', true)->where('is_approved', false)
                            ->where('status', 'pending');
                        $recordsNew->each(function ($recordsNew) {
                            $recordsNew->update(['status' => 'approved', 'is_approved' => true, 'is_requested' => false]);
                            if ($recordsNew->user && $recordsNew->user->email) {
                                $record = $recordsNew->user;
                                Mail::to($record->email)->send(new AddressApprovedMail($record));
                            }
                        });

                        //Activity Log Start
                        // activity()
                        //     ->causedBy(auth()->user())
                        //     ->performedOn($records)
                        //     ->useLog('shipping_address_request')
                        //     ->withProperties([
                        //         'old' => [
                        //             'status' => 'pending',
                        //         ],
                        //         'attributes' => array_filter([
                        //             'status' => $records->status,
                        //         ], fn($value) => !is_null($value)),
                        //     ])
                        //     ->log("Shipping address request have been approved");
                        //Activity Log End
                        Notification::make()
                            // ->title('Shipping Address Approved')
                            ->title('The selected request have been approved successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                Tables\Actions\BulkAction::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->action(function ($records) {
                        $recordsNew = UserAddress::whereIn('user_id', $records->pluck('user_id'))->where('is_requested', true)->where('is_approved', false)
                            ->where('status', 'pending');
                        $recordsNew->each(function ($recordsNew) {
                            $recordsNew->update(['status' => 'rejected', 'is_approved' => true, 'is_requested' => false]);
                            if ($recordsNew->user && $recordsNew->user->email) {
                                $record = $recordsNew->user;
                                Mail::to($record->email)->send(new AddressRejectedMail($record));
                            }
                        });

                        //Activity Log Start
                        // activity()
                        //     ->causedBy(auth()->user())
                        //     ->performedOn($records)
                        //     ->useLog('shipping_address_request')
                        //     ->withProperties([
                        //         'old' => [
                        //             'status' => 'pending',
                        //         ],
                        //         'attributes' => array_filter([
                        //             'status' => $records->status,
                        //         ], fn($value) => !is_null($value)),
                        //     ])
                        //     ->log("Shipping address request have been rejected");
                        //Activity Log End

                        Notification::make()
                            // ->title('Shipping Address Rejected')
                            ->title('The selected request have been rejected successfully.')
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShippingAddressRequests::route('/'),
            'create' => Pages\CreateShippingAddressRequest::route('/create'),
            // 'edit' => Pages\EditShippingAddressRequest::route('/{record}/edit'),
        ];
    }
}
