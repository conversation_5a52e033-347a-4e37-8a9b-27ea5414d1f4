<?php

namespace App\Filament\Admin\Resources;

use Closure;
use Filament\Tables;
use App\Models\Product;
use Filament\Forms\Get;
use App\Models\Category;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use App\Filament\Admin\Resources\SubCategoryResource\Pages;
use Filament\Forms\Components\Grid;
use Filament\Support\Enums\Alignment;
use Filament\Tables\Columns\TextInputColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class SubCategoryResource extends Resource
{
    protected static ?string $model = Category::class;

    // protected static ?string $navigationIcon = 'heroicon-o-squares-2x2';

    protected static ?string $navigationGroup = 'Master';

    protected static ?string $label = 'Sub Category';

    protected static ?int $navigationSort = 2;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('sub-categories_view');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('sub-categories_create');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('sub-categories_update');
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('sub-categories_delete');
    }

    public static function canUpdate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('sub-categories_update');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    Select::make('parent_id')
                        ->rules(['required'])
                        ->validationMessages([
                            'required' => __('message.sub-category.parent_required'),
                        ])
                        ->options(
                            Category::whereNull('parent_id')
                                ->where('status', true)
                                ->pluck('name', 'id')
                        )
                        ->searchable()
                        ->label(new HtmlString("Parent Category <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>")),
                    Grid::make(2)->schema([
                        TextInput::make('name')
                            ->maxLength(100)
                            ->label(new HtmlString("Sub Category Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                            ->rules([
                                'required',
                                'max:100',
                                // 'regex:/^[\w\s\p{P}]+$/u',
                                fn(Get $get): Closure => function (string $attribute, $value, Closure $fail) use ($get) {
                                    $value = strtolower($value);
                                    $query = DB::table('categories')
                                        ->whereNotNull('parent_id')
                                        ->whereRaw('LOWER(TRIM(name)) = ?', [$value])
                                        ->when($get('id'), fn($q) => $q->where('id', '!=', $get('id')));

                                    if ($query->exists()) {
                                        $record = DB::table('categories as sub')
                                            ->join('categories as parent', 'sub.parent_id', '=', 'parent.id')
                                            ->whereRaw('LOWER(TRIM(sub.name)) = ?', [$value])
                                            ->whereNotNull('sub.parent_id')
                                            ->when($get('id'), fn($q) => $q->where('sub.id', '!=', $get('id')))
                                            ->select('parent.name as parent_name', 'sub.name as sub_name')
                                            ->first();

                                        $fail(__('message.sub-category.case_sensitive_unique', [
                                            'name' => $record->sub_name ?? $value,
                                            'parent' => $record->parent_name ?? 'unknown',
                                        ]));
                                    }
                                },
                            ])
                            ->validationMessages([
                                'required' => __('message.sub-category.required'),
                                // 'regex' => __('message.sub-category.regex'),
                                'max' => __('message.sub-category.max'),
                                'App\\Rules\\CaseSensitiveUnique' => __('message.sub-category.case_sensitive_unique'),

                            ]),
                        TextInput::make('serial_number')->label("Order")
                            ->numeric()
                            ->rules([
                                'nullable',
                                'min:1'
                            ])
                            ->validationMessages([
                                'min' => __('message.category.min_order'),
                            ])
                    ]),

                    Toggle::make('is_mda')->label('Include MDA Number & Documents?')
                        ->default(true),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function () {
                return Category::query()->whereNotNull('parent_id');
            })
            ->columns([
                TextColumn::make('name')->label('Sub Category Name')->sortable()->searchable()->toggleable()->wrap()->width('20%'),
                TextInputColumn::make('serial_number')
                    ->label('Order')->type('number')->placeholder('-')->sortable()->width('80px')->searchable()->toggleable()
                    ->rules(['nullable', 'numeric', 'min:1'])->alignment(Alignment::Center)
                    ->updateStateUsing(function ($record, $state) {
                        if ($state !== null && !is_numeric($state)) {
                            Notification::make()->danger()->title('Order must be a number.')->send();
                            return $record->serial_number;
                        }
                        if ($state !== null && $state !== '' && $state < 1) {
                            Notification::make()->danger()->title('Order must be at least 1.')->send();
                            return $record->serial_number;
                        }
                        $record->update(['serial_number' => $state === '' ? null : $state]);
                        Notification::make()
                            ->success()
                            ->title('The order has been updated successfully.')
                            ->duration(2000)
                            ->send();

                        return $state;
                    }),
                TextColumn::make('parent.name')->label('Category Name')->alignment(Alignment::Center)->sortable()->searchable()->toggleable()->width('30%'),
                ToggleColumn::make('status')->label('Status')
                    ->sortable()->width('15%')
                    ->toggleable()->alignment(Alignment::Center)
                    ->disabled(function ($record) {
                        return Product::query()->where('sub_category_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('sub_category_id', $record->id)->exists()) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                // ->title(__('message.sub-category.title.warning'))
                                ->title(__('message.sub-category.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('message.sub-category.title.saved'))
                            ->duration(1000)
                            ->title(__('message.sub-category.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('parent_id')
                    ->label('Category Name')
                    ->options(Category::all()->whereNull('parent_id')->pluck('name', 'id')),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')
                    ->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !Product::query()->where('sub_category_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.sub-category.title.deleted'))
                            ->title(__('message.sub-category.delete_success'))
                            ->send();
                    }),
            ])

            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = DB::select("SELECT * FROM products WHERE sub_category_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('sub_category_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name;
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.sub-category.title.deletion_completed'))
                                ->title(__('message.sub-category.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.sub-category.title.partial_deleted'))
                                ->title(__('message.sub-category.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.sub-category.title.activated'))
                            ->title(__('message.sub-category.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('sub_category_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name; // Assuming 'name' is the field you want to show
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.sub-category.title.deactivated'))
                                ->title(__('message.sub-category.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.sub-category.title.partial_inactivated'))
                                ->title(__('message.sub-category.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSubCategories::route('/'),
            'create' => Pages\CreateSubCategory::route('/create'),
            'edit' => Pages\EditSubCategory::route('/{record}/edit'),
        ];
    }
}
