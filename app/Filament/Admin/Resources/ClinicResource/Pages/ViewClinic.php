<?php

namespace App\Filament\Admin\Resources\ClinicResource\Pages;

use App\Models\User;
use Filament\Actions;
use Mockery\Matcher\Not;
use App\Models\ClinicDetail;
use Filament\Actions\Action;
use App\Mail\SendClinicPasswordMail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use App\Filament\Admin\Resources\ClinicResource;
use App\Filament\Admin\Resources\ClinicResource\Pages;
use App\Filament\Admin\Resources\ClinicResource\RelationManagers\DpharmapointsRelationManager;

class ViewClinic extends ViewRecord
{
    protected static string $resource = ClinicResource::class;

    protected ?string $heading = 'Facility';

    // protected static string $view = 'filament.admin.resources.clinic-resource.pages.view-clinic-onboarding-page';

    public ?array $data = [];

    public User $user;

    public ClinicDetail $clinic;
    public $userAddress;
    public $clinicDetail;
    public $clinicAccountType;
    public $clinicPcDetail;

    public function mount(int|string $record): void
    {


        $this->user = User::find($record);
        // dd($this->user->clinicData);
        $this->clinicDetail = $this->user->clinicData;
        $this->clinicAccountType = $this->clinicDetail?->clinicAccountType;
        $this->userAddress = $this->user->addresses()
            ->first();
        $this->record = $this->user;
        $this->clinicPcDetail = $this->user->pharmaSuppliers;
        // dd($this->userAddress->state);

    }

    public function getCustomRelationManagers(): array
    {
        return [
            DpharmapointsRelationManager::class
        ];
    }
    public function getHeaderActions(): array
    {
        if (($this->user->clinicData?->is_submitted) && ($this->user->verification_status == 'send_for_approval')) {
            return [
                Action::make('approve')
                    ->label('Approve')
                    ->requiresConfirmation()
                    ->modalSubmitActionLabel('Approve')
                    ->modalDescription('Are you sure you want to approve this Facility?')
                    ->action(function ($record) {
                        $record->update([
                            'is_admin_verified' => true,
                            'admin_verified_by' => auth()->user()->id,
                            'admin_verified_on' => now(),
                            'verification_status' => 'approved',
                            'rejection_reason' => null
                        ]);
                        $this->updateClinicTierInfo($record->id);
                        Mail::to($record->email)->send(new \App\Mail\ClinicApproveMail($record));
                        // Activity Log Start
                        $clinicName = $record->clinicData->clinic_name ?? $record->name;
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('clinic_approval')
                            ->performedOn($record->clinicData ?? $record)
                            ->withProperties([
                                'old' => [
                                    'verification_status' => 'pending',
                                ],
                                'attributes' => [
                                    'verification_status' => 'approved',
                                ],
                            ])
                            ->log("Facility '{$clinicName}' has been approved by admin");
                        // Activity Log End
                    }),
                Action::make('reject')
                    ->label('Reject')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalSubmitActionLabel('Reject')
                    ->modalDescription('Are you sure you want to reject this Facility?')
                    ->form([
                        Textarea::make('reason')->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'is_admin_verified' => false,
                            'admin_verified_by' => auth()->user()->id,
                            'rejection_reason' => $data['reason'],
                            'verification_status' => 'rejected'
                        ]);
                        Mail::to($record->email)->send(new \App\Mail\ClinicRejectMail($record));
                        // Activity Log Start
                        $clinicName = $record->clinicData->clinic_name ?? $record->name;
                        activity()
                            ->causedBy(auth()->user())
                            ->useLog('clinic_approval')
                            ->performedOn($record->clinicData ?? $record)
                            ->withProperties([
                                'old' => [
                                    'verification_status' => 'pending',
                                ],
                                'attributes' => [
                                    'verification_status' => 'rejected',
                                    'rejection_reason' => $data['reason'],
                                ],
                            ])
                            ->log("Facility '{$clinicName}' has been rejected by admin");
                        // Activity Log End
                    }),
                Action::make('back')
                    ->label('Back')
                    ->color('gray')
                    ->url(ClinicResource::getUrl('index')),

            ];
        }
        return [
            Action::make('approval')
                ->label('Submit for Approval')
                ->requiresConfirmation()
                ->modalSubmitActionLabel('Approve')
                ->modalDescription('Are you sure you want to send approval request for this Facility?')
                ->visible(function ($record) {
                    return $record->clinicData?->is_submitted == false && $record->verification_status == 'pending';
                })
                ->action(function ($record) {
                    $record->clinicData?->update(['is_submitted' => true]);
                    $this->sendClinicCredential($record);

                    //Activity Log Start
                    // $newClinicName = $record->clinicData->clinic_name ?? $record->name; // Adjust based on your data structure
                    // activity()
                    //     ->causedBy(auth()->user())
                    //     ->useLog('clinic_approval')
                    //     ->performedOn($record->clinicData ?? $record)
                    //     ->withProperties([
                    //         'attributes' => [
                    //             'admin_verified_on' => now()->format('d-m-Y'),
                    //         ],
                    //     ])
                    //     ->log("Facility '{$newClinicName}' has been approved");
                    //Activity Log Start
                    Notification::make()
                        ->title('Facility submitted for approval')
                        ->success()
                        ->send();
                }),

            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicResource::getUrl('index')),
        ];
    }

    public function updateClinicTierInfo($userId)
    {
        $activeClinicCount = User::where(['is_admin_verified' => true, 'verification_status' => 'approved'])->count();
        $tier = '';
        if ($activeClinicCount < 100) {
            $tier = 'gold';
        } elseif ($activeClinicCount > 100 && $activeClinicCount < 300) {
            $tier = 'silver';
        } elseif ($activeClinicCount > 300 && $activeClinicCount < 500) {
            $tier = 'bronze';
        }

        ClinicDetail::where('user_id', $userId)->update(['tier' => $tier]);
        return true;
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Facilities",
            3 => $this->record->clinicData?->is_submitted == false ? "Review" : "View",
        ];
    }

    public function sendClinicCredential($data)
    {
        $userEmail = $data['email'];
        $plainPassword = generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['id'])->update(['password' => $hashedPassword, 'email_verified_at' => now(), 'status' => true, 'is_otp_verified' => 1]);
        $loginUrl = config('app.clinic_url');
        Mail::to($userEmail)->send(new SendClinicPasswordMail($plainPassword, $userEmail, $loginUrl));
    }
}
