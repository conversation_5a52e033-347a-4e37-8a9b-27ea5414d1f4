<?php

namespace App\Filament\Admin\Resources\ClinicResource\Pages;

use App\Filament\Admin\Resources\ClinicResource;
use App\Forms\Components\PhoneWithPrefix;
use App\Models\User;
use App\Models\PcDetail;
use App\Models\ClinicAccountType;
use App\Service\StoreProfileByAdminService;
use Filament\Actions\Action;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Components\Radio;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\File;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use App\Models\BusinessType;
use App\Models\ClinicCertificateFile;
use Filament\Forms\Components\Fieldset;
use App\Service\ClinicProfileByAdminService;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\Rule;
use Saade\FilamentAutograph\Forms\Components\SignaturePad;
use App\Models\ClinicDetail;
use Filament\Notifications\Notification;
use Filament\Forms\Components\ViewField;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\Section;
use App\Models\ClinicPharmaSupplier;
use App\Models\UserAddress;
use App\Models\ZipCode;
use App\Rules\PhoneWithPrefixRule;
use Dom\Text;
use Filament\Tables\Actions\DeleteAction;
use Filament\Infolists\Components\TextEntry;
use Filament\Forms\Components\Component;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\Placeholder;
use Filament\Tables\Table;
use Filament\Forms\Components\Repeater;
use Filament\Support\Enums\Alignment;
use Illuminate\Support\Facades\Mail;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Nnjeim\World\Models\Country;

class ClinicOnboardingPage extends EditRecord
{
    protected static string $resource = ClinicResource::class;

    protected ?string $heading = 'Facility';

    protected static string $view = 'filament.admin.resources.clinic-resource.pages.clinic-onboarding-page';

    public ?array $data = [];

    public User $user;

    public $signaturePath = '';
    public $clinic_name = '';
    public $borangCertificate = '';
    public $mmcCertificate = '';
    public $apcCertificate = '';
    public $arcCertificate = '';
    public $poisonLicense = '';
    public $dc_landline_code = '';
    public $ac_landline_code = '';
    public $otherRelevantDocuments = '';
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Facilities",
            3 => "Edit",
        ];
    }

    public function mount(int|string $record): void
    {

        $this->user = User::find($record);
        $signaturePath = '';
        $borangCertificate = '';
        if ($this->user->clinicData?->signature_type) {
            $signaturePath = $this->user->clinicData?->dc_signature;
        }


        $shippingAddresses = UserAddress::where('user_id', $this->user->id)
            ->where('address_type', 'shipping')->where('status', 'approved')->where('is_requested', false)
            // ->whereNotIn('id', [$this->user->clinicData->shipping_addresses_id])
            ->get()
            ->map(function ($address) {
                return [
                    'shipping_address_1' => $address->address_1,
                    'shipping_address_2' => $address->address_2,
                    'shipping_state_id' => $address->state_id,
                    'shipping_city_id' => $address->city_id,
                    'shipping_postal_code' => $address->postal_code,
                    'is_default' => $address->is_default,
                    'is_same_as_billing' => $address->is_same_as_billing,
                    'shipping_nick_name' => $address->nick_name
                ];
            })
            ->toArray();
        // dd($shippingAddresses);

        $clinicData = $this->user->clinicData;
        // dd($clinicData->shippingAddresses);
        // if ($clinicData->is_same_as_billing == true) {
        //     // Add billing address as an additional shipping address
        //     $shippingAddresses[] = [
        //         'shipping_address_1' => $clinicData->billingAddress->address_1,
        //         'shipping_address_2' => $clinicData->billingAddress->address_2,
        //         'shipping_state_id' => $clinicData->billingAddress->state_id,
        //         'shipping_city_id' => $clinicData->billingAddress->city_id,
        //         'shipping_postal_code' => $clinicData->billingAddress->postal_code,
        //         // 'is_default' => $clinicData->shippingAddresses->is_default,
        //         'is_same_as_billing' => true,
        //     ];
        // }
        // dd($shippingAddresses);
        // If no addresses exist, create one empty set
        if (empty($shippingAddresses)) {
            $shippingAddresses = [[
                'shipping_address_1' => $this->user->clinicData?->billingAddress?->address_1,
                'shipping_address_2' => $this->user->clinicData?->billingAddress?->address_2,
                'shipping_state_id' => $this->user->clinicData?->billingAddress?->state_id,
                'shipping_city_id' => $this->user->clinicData?->billingAddress?->city_id,
                'shipping_postal_code' => $this->user->clinicData?->billingAddress?->postal_code,
                'shipping_nick_name' => $this->user->clinicData?->billingAddress?->nick_name,
                'is_default' => true,
                'shipping_country_id' => 'Malaysia',
            ]];
        }

        $certificateFiles = ClinicCertificateFile::where('user_id', $this->user->id)
            ->where('status', 'active')
            ->get()
            ->groupBy('type'); // Use groupBy instead of keyBy to handle multiple files

        $this->borangCertificate = $certificateFiles->get('borang_certificate', collect())->pluck('name')->all();
        $this->mmcCertificate = $certificateFiles->get('mmc_certificate', collect())->pluck('name')->all();
        $this->apcCertificate = $certificateFiles->get('apc_certificate', collect())->pluck('name')->all();
        $this->arcCertificate = $certificateFiles->get('arc_certificate', collect())->pluck('name')->all();

        $this->poisonLicense = $certificateFiles->get('poison_license', collect())->pluck('name')->all();
        $this->otherRelevantDocuments = $certificateFiles->get('other_relevant_documents', collect())->pluck('name')->all();
        // dd($shippingAddresses);
        $this->form->fill([
            'clinic_name' => $this->user->clinicData?->clinic_name,
            'clinic_email' => $this->user?->email,
            'clinic_number' => $this->user->clinicData?->clinic_number,
            'mobile_number' => $this->user->clinicData?->mobile_number,
            'landline_number' => $this->user->clinicData?->landline_number,
            'landline_code' => $this->user->clinicData?->landline_code,
            'clinic_account_type_id' => $this->user->clinicData?->clinic_account_type_id,
            'business_type_id' => $this->user->clinicData?->business_type_id,
            'company_name' => $this->user->clinicData?->company_name,
            'company_number' => $this->user->clinicData?->company_number,
            'clinic_owner' => $this->user->clinicData?->clinic_owner,
            'clinic_year' => $this->user->clinicData?->clinic_year,
            'tin_number' => $this->user->clinicData?->tin_number,
            'sst_number' => $this->user->clinicData?->sst_number,

            'user_id' => $this->user->clinicData?->user_id,
            'clinic_id' => $this->user->clinicData?->id,
            'shipping_address_1' => $this->user->clinicData?->shippingAddress?->address_1,
            'shipping_address_2' => $this->user->clinicData?->shippingAddress?->address_2,
            'shipping_state_id' => $this->user->clinicData?->shippingAddress?->state_id,
            'shipping_city_id' => $this->user->clinicData?->shippingAddress?->city_id,
            'shipping_postal_code' => $this->user->clinicData?->shippingAddress?->postal_code,
            'shipping_nick_name' => $this->user->clinicData?->shippingAddress?->nick_name,
            'shipping_addresses' => $shippingAddresses,
            //'is_same_as_billing' => $this->user->clinicData?->is_same_as_billing,
            'billing_address_1' => $this->user->clinicData?->billingAddress?->address_1,
            'billing_address_2' => $this->user->clinicData?->billingAddress?->address_2,
            'billing_state_id' => $this->user->clinicData?->billingAddress?->state_id,
            'billing_city_id' => $this->user->clinicData?->billingAddress?->city_id,
            'billing_postal_code' => $this->user->clinicData?->billingAddress?->postal_code,
            'billing_nick_name' => $this->user->clinicData?->billingAddress?->nick_name,

            'dc_name' => $this->user->clinicData?->dc_name,
            'dc_nric' => $this->user->clinicData?->dc_nric,
            'dc_mmc_number' => $this->user->clinicData?->dc_mmc_number,
            'dc_apc_number' => $this->user->clinicData?->dc_apc_number,
            'dc_phone_number' => $this->user->clinicData?->dc_phone_number,
            'dc_landline_number' => $this->user->clinicData?->dc_landline_number,
            'dc_landline_code' => $this->user->clinicData?->dc_landline_code,

            'signature_type' => ($this->user->clinicData?->signature_type) ? $this->user->clinicData?->signature_type : false,
            'previous_signature' => $this->user->clinicData?->dc_signature,
            'signature' => $signaturePath,
            'dc_signature' => getImage($this->user->clinicData->dc_signature, '/images/clinic/' . $this->user->clinicData->id),//$this->user->clinicData?->dc_signature,
            'ac_name' => $this->user->clinicData?->ac_name,
            'ac_nric' => $this->user->clinicData?->ac_nric,
            'is_admin_in_charge' => $this->user->clinicData?->is_admin_in_charge,

            'ac_phone_number' => $this->user->clinicData?->ac_phone_number,
            'ac_landline_number' => $this->user->clinicData?->ac_landline_number,
            'ac_landline_code' => $this->user->clinicData?->ac_landline_code,

            // 'borang_certificate' => $this->user->clinicData?->borang_certificate,
            // 'mmc_certificate' => $this->user->clinicData?->mmc_certificate,
            // 'apc_certificate' => $this->user->clinicData?->apc_certificate,
            // 'arc_certificate' => $this->user->clinicData?->arc_certificate,
            // 'poison_license' => $this->user->clinicData?->poison_license,

            // 'borang_certificate' => $certificateFiles['borang_certificate']->name ?? null,
            // 'mmc_certificate' => $certificateFiles['mmc_certificate']->name ?? null,
            // 'apc_certificate' => $certificateFiles['apc_certificate']->name ?? null,
            // 'arc_certificate' => $certificateFiles['arc_certificate']->name ?? null,
            // 'poison_license' => $certificateFiles['poison_license']->name ?? null,
            'apc_certificate_expired_date' => $this->user->clinicData?->apc_certificate_expired_date,
            'photo' => $this->user->photo ? ['users/' . $this->user->photo] : null,
            'poison_license' => !empty($this->poisonLicense) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->poisonLicense) : [],
            'borang_certificate' => !empty($this->borangCertificate) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->borangCertificate) : [],
            'mmc_certificate' => !empty($this->mmcCertificate) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->mmcCertificate) : [],
            'arc_certificate' => !empty($this->arcCertificate) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->arcCertificate) : [],
            'other_relevant_documents' => !empty($this->otherRelevantDocuments) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->otherRelevantDocuments) : [],
            'apc_certificate' => !empty($this->apcCertificate) ? array_map(function($filename) {
                return (string)(config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/' . $filename);
            }, $this->apcCertificate) : [],

        ]);
    }

    // public function getHeaderActions(): array
    // {
    //     $actionArray = [];
    //     if($this->user->verification_status == 'pending') {
    //         return [
    //             Action::make('approve')
    //                 ->label('Approve')
    //                 ->requiresConfirmation()
    //                 ->modalSubmitActionLabel('Approve')
    //                 ->modalDescription('Are you sure you want to approve this Facility?')
    //                 ->action(function ($record) {
    //                     $record->update(['is_admin_verified' => true, 'admin_verified_by' => auth()->user()->id, 'admin_verified_on' => now(), 'verification_status' => 'approved', 'rejection_reason' => null]);
    //                     $this->updateClinicTierInfo($record->id);
    //                 })];
    //     } elseif($this->user->verification_status == 'pending') {
    //         return [
    //             Action::make('reject')
    //                 ->label('Reject')
    //                 ->color('danger')
    //                 ->requiresConfirmation()
    //                 ->modalSubmitActionLabel('Reject')
    //                 ->modalDescription('Are you sure you want to reject this Facility?')
    //                 ->form([
    //                     Textarea::make('reason')->required(),
    //                 ])
    //                 ->action(function ($record, array $data) {
    //                     $record->update(['is_admin_verified' => false, 'admin_verified_by' => auth()->user()->id, 'rejection_reason' => $data['reason'], 'verification_status' => 'rejected']);
    //                 })];
    //     } elseif($this->user->verification_status == 'pending' || $this->user->verification_status == 'send_for_approval') {
    //         return [
    //             Action::make('approve')
    //                 ->label('Approve')
    //                 ->requiresConfirmation()
    //                 ->modalSubmitActionLabel('Approve')
    //                 ->modalDescription('Are you sure you want to approve this Facility?')
    //                 ->action(function ($record) {
    //                     $record->update(['is_admin_verified' => true, 'admin_verified_by' => auth()->user()->id, 'admin_verified_on' => now(), 'verification_status' => 'approved', 'rejection_reason' => null]);
    //                     $this->updateClinicTierInfo($record->id);
    //                 }),

    //             Action::make('reject')
    //                 ->label('Reject')
    //                 ->color('danger')
    //                 ->requiresConfirmation()
    //                 ->modalSubmitActionLabel('Reject')
    //                 ->modalDescription('Are you sure you want to reject this Facility?')
    //                 ->form([
    //                     Textarea::make('reason')->required(),
    //                 ])
    //                 ->action(function ($record, array $data) {
    //                     $record->update(['is_admin_verified' => false, 'admin_verified_by' => auth()->user()->id, 'rejection_reason' => $data['reason'], 'verification_status' => 'rejected']);
    //                 }),

    //         ];
    //     }
    //     return [];

    // }

    public function getHeaderActions(): array
    {
        if ($this->user->clinicData->is_submitted  && $this->user->verification_status == 'send_for_approval') {
            return [

                Action::make('approve')
                    ->label('Approve')
                    ->color('success')
                    ->requiresConfirmation()
                    ->visible(function ($record) {
                        return $this->user->verification_status == 'pending' || $this->user->verification_status == 'send_for_approval';
                    })
                    ->modalSubmitActionLabel('Approve')
                    ->modalDescription('Are you sure you want to approve this Facility?')
                    ->action(function ($record) {
                        $record->update(['is_admin_verified' => true, 'admin_verified_by' => auth()->user()->id, 'admin_verified_on' => now(), 'verification_status' => 'approved', 'rejection_reason' => null]);
                        $this->updateClinicTierInfo($record->id);
                        Mail::to($record->email)->send(new \App\Mail\ClinicApproveMail($record));
                    }),

                Action::make('reject')
                    ->label('Reject')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->visible(function ($record) {
                        return $this->user->verification_status == 'pending' || $this->user->verification_status == 'send_for_approval';
                    })
                    ->modalSubmitActionLabel('Reject')
                    ->modalDescription('Are you sure you want to reject this Facility?')
                    ->form([
                        Textarea::make('reason')->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update(['is_admin_verified' => false, 'admin_verified_by' => auth()->user()->id, 'rejection_reason' => $data['reason'], 'verification_status' => 'rejected']);
                        Mail::to($record->email)->send(new \App\Mail\ClinicRejectMail($record));
                    }),
                Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicResource::getUrl('index')),
            ];
        }
        return [ Action::make('back')
        ->label('Back')
        ->color('gray')
        ->url(ClinicResource::getUrl('index')),];

    }
    protected static function getExpiryYearOptions(): array
    {
        $currentYear = now()->year;
        $startYear = $currentYear; // Adjust the range as needed
        $endYear = $currentYear + 1; // Adjust future years if necessary

        return array_combine(
            range($startYear, $endYear),
            range($startYear, $endYear)
        );
    }

    protected static function getYearOptions(): array
    {
        $currentYear = now()->year;
        $startYear = $currentYear - 50; // Adjust the range as needed
        $endYear = $currentYear; // Adjust future years if necessary

        return array_combine(
            range($startYear, $endYear),
            range($startYear, $endYear)
        );
    }

    public function updateClinicTierInfo($userId)
    {
        $activeClinicCount = User::where(['is_admin_verified' => true, 'verification_status' => 'approved'])->count();
        $tier = '';
        if ($activeClinicCount < 100) {
            $tier = 'gold';
        } elseif ($activeClinicCount > 100 && $activeClinicCount < 300) {
            $tier = 'silver';
        } elseif ($activeClinicCount > 300 && $activeClinicCount < 500) {
            $tier = 'bronze';
        }

        ClinicDetail::where('user_id', $userId)->update(['tier' => $tier]);
        return true;
    }

    public function getViewData(): array
    {
        return [
            'Actions' => $this->getHeaderActions(),
        ];
    }

    public function getRecord(): Model
    {
        return User::find($this->record) ?? abort(404);
    }

    public function form(Form $form): Form
    {
        return $form->schema([
            Wizard::make()
                ->previousAction(function ($action) {
                    return $action->label('Back')->extraAttributes([
                        'class' => 'bg-white'
                    ]);
                })
                ->nextAction(function ($action) {
                    return $action->label('Save & Continue')->color('create_button');
                })
                ->startOnStep(function () {
                    if ($this->user->clinicData->completed_step == 5 && $this->user->clinicData->is_submitted) {
                        return 1;
                    } elseif (request()->headers->get('referer') == ClinicResource::getUrl('create')) {
                        return 2;
                    } elseif (request()->getUri() == ClinicResource::getUrl('edit', ['record' => $this->record])) {
                        return ($this->user->clinicData->completed_step < 5) ? $this->user->clinicData->completed_step + 1 : ($this->user->clinicData->is_submitted ? $this->user->clinicData->completed_step : $this->user->clinicData->completed_step);
                    } elseif (request()->headers->get('referer') == ClinicResource::getUrl('index')) {
                        return 1;
                    } else {
                        return ($this->user->clinicData->completed_step < 5) ? $this->user->clinicData->completed_step + 1 : $this->user->clinicData->completed_step;
                    }
                })
                ->schema([
                    Step::make('Facility Info')
                        ->icon('phosphor-building')
                        ->schema([
                            Group::make()->schema([
                                Fieldset::make('Facility Information')->label(new HtmlString('<span style="font-size: 28px !important;">Facility Information </span>'))
                                    ->schema([
                                        FileUpload::make('photo')
                                            ->label('Company Logo')
                                            ->image()
                                            ->avatar()
                                            ->directory('users')
                                            ->rules(['nullable', File::types(['jpeg', 'png', 'jpg'])->max(2 * 1024)])
                                            ->helperText('Supported formats: JPEG, JPG, PNG (Max 2MB)')->columnSpanFull()
                                            ->default($this->user->photo ? Storage::disk('s3')->url('users/' . $this->user->photo) : asset('/images/user-avatar.png')),
                                        TextInput::make('clinic_name')
                                            ->rules(['max:150', 'regex:/^[a-zA-Z0-9\s]+$/'])
                                            ->validationMessages([
                                                'max' => 'The Facility Name may not be greater than 150 characters.',
                                                'regex' => 'The Facility Name must be a alphabetical.',
                                            ])
                                            ->label('Facility Name')->placeholder('Enter facility name')->required(),
                                        TextInput::make('clinic_number')
                                            ->label(new HtmlString(
                                                'Registration Number 
                                                    <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                                    </svg>'
                                            ))

                                            ->placeholder('Enter facility registration number')
                                            ->validationAttribute('Facility Registration Number')
                                            ->suffixIcon(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->rules(function (Get $get) {
                                                if (!empty($get('user_id'))) {
                                                    return ['required', Rule::unique('clinic_details', 'clinic_number')->ignore($get('user_id'), 'user_id'), 'min:2', 'max:20', 'regex:/^[a-zA-Z0-9]+$/'];
                                                }
                                                return ['required', 'unique:clinic_details,clinic_number', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                            })
                                            ->required()->validationMessages([
                                                'required' => 'The Facility Registration Number field is required.',
                                                'max' => 'The Registration Number must be at most 13 characters long.',
                                                'regex' => 'The Registration Number must contain only letters and numbers.',
                                                'min'
                                            ]),
                                        TextInput::make('mobile_number')->prefix('+60')->label('Mobile Number')
                                            ->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])
                                            ->live()
                                            ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->rules(['nullable', 'digits_between:8,12'])->placeholder('Enter facility mobile number'),
                                        // TextInput::make('landline_number')->prefix('+03')->label('Facility Landline Number')
                                        //     ->mask('9-9999999')
                                        //     ->stripCharacters(['-'])
                                        //     ->extraAttributes([
                                        //         'inputmode' => 'numeric',
                                        //         'maxlength' => '8'
                                        //     ])
                                        //     ->rules(['digits_between:7,8'])->placeholder('Enter facility landline number'),
                                        PhoneWithPrefix::make('landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8'
                                            ])
                                            ->prefixOptions(function ($get, $set) {
                                                if (empty($get('landline_number'))) {
                                                    return [];
                                                }
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');

                                                if ($stateId && $cityId) {
                                                    $query->where('state_id', $stateId)->where('id', $cityId);
                                                }

                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (empty($data)) {
                                                    $data = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                // FacadesLog::info($get('addresses'));
                                                if ($get("landline_number")["prefix"] === "") {
                                                    $set('landline_number.prefix', $data[array_key_first($data)] ?? '');
                                                }
                                                return $data;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {

                                                if (isset($get('addresses')[0]["landline_code"]) && $get('addresses')[0]["landline_code"] != null) {
                                                    $set("landline_number.prefix", $get('addresses')[0]["landline_code"]);
                                                    $set("landline_number.number", $get('addresses')[0]["landline_number"]);
                                                } else {
                                                    $set("landline_number", ["prefix" => "", "number" => ""]);
                                                }
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set("landline_code", implode(" ", $get("landline_number")));
                                            })->formatStateUsing(function ($get, $set, $state) {
                                                $data = ['prefix' => '', 'number' => ''];
                                                if ($get("landline_code")) {
                                                    $data["prefix"] = $get("landline_code");
                                                }
                                                if ($get("landline_number")) {
                                                    $data["number"] = $get("landline_number");
                                                }
                                                return is_array($state) ? $state : $data;
                                            }),
                                        TextInput::make('clinic_email')
                                            ->regex('/^[^@]+@[^@]+\.[a-zA-Z]{2,}$/')->label('Email')
                                            ->placeholder('Enter facility email')
                                            ->rules(
                                                [
                                                    'required',
                                                    'email',
                                                    'max:255'
                                                ]
                                            )
                                            ->required()->email()->disabled(fn () => $form->getOperation() === 'edit'),



                                        Select::make('clinic_account_type_id')
                                            ->label('Facility Type')->placeholder('Select facility type')
                                            ->options(ClinicAccountType::where('status', true)->pluck('name', 'id')->toArray())
                                            ->disabled(function () {
                                                return $this->record && ClinicDetail::where('user_id', $this->record)->exists();
                                            })
                                            ->required(),

                                    ])->columns(2),



                                Fieldset::make('Company Information')->label(new HtmlString('<span style="font-size: 28px !important;">Company Information </span>'))
                                    ->schema([
                                        Select::make('business_type_id')
                                            ->label(new HtmlString('Type of Business <span class="text-danger" style="color: #e3342f;">*</span>'))
                                            ->placeholder('Select business type')
                                            ->options(BusinessType::where('status', true)->pluck('name', 'id')->toArray())
                                            ->rules(['required'])
                                            ->validationMessages([
                                                'required' => 'The Type of Business field is required.',
                                            ])
                                            ->reactive()
                                            ->afterStateUpdated(function ($state, callable $set) {
                                                // Only proceed if $state is not empty and is numeric
                                                if (empty($state) || !is_numeric($state)) {
                                                    return;
                                                }
                                                $businessType = BusinessType::find($state);
                                                $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                if ($isSoleProprietary) {
                                                    $set('company_name', '');
                                                    $set('company_number', '');
                                                }
                                            }),

                                        TextInput::make('company_name')
                                            ->label(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId) && is_numeric($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                // Only one star, hide if sole-proprietary
                                                return new HtmlString(
                                                    'Company Name <span class="text-danger"'
                                                );
                                            })
                                            ->placeholder('Enter company name')
                                            ->rules(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId) && is_numeric($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                if ($isSoleProprietary) {
                                                    return ['nullable', 'string', 'max:50', 'regex:/^[\pL\s]+$/u'];
                                                }
                                                if (!empty($get('user_id'))) {
                                                    return ['required', 'string', Rule::unique('clinic_details', 'company_name')->ignore($get('user_id'), 'user_id'), 'max:50', 'regex:/^[\pL\s]+$/u'];
                                                }
                                                return ['required', 'string', 'unique:clinic_details,company_name', 'max:50', 'regex:/^[\pL\s]+$/u'];
                                            })
                                            ->required(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                if (empty($businessTypeId) || !is_numeric($businessTypeId)) {
                                                    return false;
                                                }
                                                $businessType = BusinessType::find($businessTypeId);
                                                return $businessType && $businessType->key !== 'sole-proprietary';
                                            })
                                            ->validationMessages([
                                                'required' => 'The Company field is required.',
                                                'max' => 'The Company Name must be at most 50 characters long',
                                                'regex' => 'The company name can only contain letters and spaces.',
                                                'unique' => 'The company name has already been taken.',
                                            ]),

                                        TextInput::make('company_number')
                                            ->suffixIcon(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => (strlen($state) >= 2 && strlen($state) <= 20 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->label(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId) && is_numeric($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                // Only one star, hide if sole-proprietary
                                                return new HtmlString(
                                                    'Company Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Enter your Healthcare Registration Number (e.g., Borang B Number). This is required for verification purposes.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                    </svg>'
                                                );
                                            })
                                            ->required(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                if (empty($businessTypeId) || !is_numeric($businessTypeId)) {
                                                    return false;
                                                }
                                                $businessType = BusinessType::find($businessTypeId);
                                                return $businessType && $businessType->key !== 'sole-proprietary';
                                            })
                                            ->rules(function (Get $get) {
                                                $businessTypeId = $get('business_type_id');
                                                $isSoleProprietary = false;
                                                if (!empty($businessTypeId) && is_numeric($businessTypeId)) {
                                                    $businessType = BusinessType::find($businessTypeId);
                                                    $isSoleProprietary = $businessType && $businessType->key === 'sole-proprietary';
                                                }
                                                if ($isSoleProprietary) {
                                                    return ['nullable', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                                }
                                                return ['required', 'max:20', 'min:2', 'regex:/^[a-zA-Z0-9]+$/'];
                                            })
                                            ->placeholder('Enter company registration number')
                                            ->validationMessages([
                                                'required' => 'The Company Registration Number field is required.',
                                                'max' => 'The Registration Number must be at most 20 characters long.',
                                                'min' => 'The Registration Number field must be at least 2 characters.',
                                                'regex' => 'The Registration Number must be alphanumeric.',
                                            ]),

                                        // Helper method for sole-proprietary check
                                        // (Place this in the class if not already present)
                                        // protected static function isSoleProprietary($businessTypeId) {
                                        //     $businessType = BusinessType::find($businessTypeId);
                                        //     return $businessType && $businessType->key === 'sole-proprietary';
                                        // }
                                        // TextInput::make('clinic_owner')
                                        //     ->rules('regex:/^[\pL\s]+$/u', 'max:64')
                                        //     ->label(new HtmlString('<span style="font-size: 14px !important;">Facility Owner</span>'))->placeholder('Enter facility owner')->required()->validationMessages([
                                        //         'required' => 'The facility owner field is required.',
                                        //         'regex' => 'The facility owner can only contain letters and spaces.',
                                        //         'max' => 'The facility owner must be at most 64 characters long.',
                                        //     ]),

                                        Select::make('clinic_year')
                                            ->label('Establishment Year')->placeholder('Select commencement year')
                                            ->options(self::getYearOptions())
                                            ->searchable()
                                            ->rules(['required'])->required(),
                                        TextInput::make('tin_number')
                                            ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()

                                            ->label(new HtmlString(
                                                'TIN Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Tax Identification Number (TIN) is a unique identifier assigned by the tax authority for businesses or individuals to track tax obligations.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg><span class="text-danger" style="color: #e3342f;">*</span>'
                                            ))
                                            ->placeholder('Enter TIN Number')
                                            ->rules(['required', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])->validationMessages([
                                                'max' => 'The TIN number may not be greater than 20 characters.',
                                                'min' => 'The TIN Number field must be at least 1 characters.',
                                                'regex' => 'The TIN Number can only contain numbers and letters.',
                                                'unique' => 'The TIN number has already been taken.',
                                                'required' => 'The TIN Number field is required.',
                                            ]),
                                        TextInput::make('sst_number')
                                            ->suffixIcon(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => ((strlen($state) <= 20) && (strlen($state) > 0) && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->live()
                                            ->label(new HtmlString(
                                                'SST Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Sales and Services Tax (SST) Registration Number is a unique code issued to businesses in Malaysia registered for SST compliance.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->placeholder('Enter SST registration number')
                                            ->rules(['nullable', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])
                                            ->validationMessages([
                                                'max' => 'The SST number may not be greater than 20 characters.',
                                                'unique' => 'The SST number has already been taken.',
                                                'min' => 'The SST Number field must be at least 1 characters.',
                                                'regex' => 'The SST Number can only contain numbers and letters.',
                                            ]),
                                    ])
                            ])->columns(2)
                        ])->afterValidation(function (Get $get, Set $set, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['clinic_name'] = $get('clinic_name');
                            $email = $get('clinic_email');
                            $data['clinic_number'] = $get('clinic_number');
                            $data['clinic_account_type_id'] = $get('clinic_account_type_id');
                            $data['mobile_number'] = $get('mobile_number');
                            // $data['landline_number'] = $get('landline_number');
                            $data['landline_number'] = $get('landline_number')["number"] ?? "";
                            $data['landline_code'] = $get('landline_number')["prefix"] ?? "";
                            $data['business_type_id'] = $get('business_type_id');
                            $data['company_name'] = $get('company_name');
                            $data['company_number'] = $get('company_number');
                            // $data['clinic_owner'] = $get('clinic_owner');
                            $data['clinic_year'] = $get('clinic_year');
                            $data['tin_number'] = $get('tin_number');
                            $data['sst_number'] = $get('sst_number');
                            $uploadedFile = $get('photo');
                            $uploadedFile['user_id'] = $this->user->id;
                            uploadUserImage($uploadedFile);
                            $result = $service->storeFacelityInformations($data, 1, $email);
                            if (!empty($result)) {
                                $set('user_id', $this->user->id);
                                $set('clinic_id', $this->user->clinicData?->id);
                            }
                        }),
                    Step::make('Manage Address')
                        ->icon('heroicon-o-phone')
                        ->schema([
                            Group::make()->schema([
                                Fieldset::make('Billing Address')->label(new HtmlString('<span style="font-size: 28px !important;">Billing Address </span>'))
                                    ->schema(function (Get $get) {
                                        return [

                                            // TextInput::make('billing_nick_name')
                                            //     ->label('Nick Name')
                                            //     ->placeholder('Enter nick name')
                                            //     ->required()
                                            //     ->rules(['required', 'max:50'])
                                            //     ->validationMessages([
                                            //         'required' => 'The nick name field is required.',
                                            //         'max' => 'The nick name must not exceed 50 characters.',
                                            //         // 'regex' => 'Only letters, numbers and spaces are allowed.',
                                            //     ]),
                                            TextInput::make('billing_address_1')
                                                ->rules(['required', 'max:100'])
                                                ->label('Address line 1')->placeholder('Enter address line 1')->required()
                                                ->validationMessages([
                                                    'max' => 'The Address Line 1 must be at most 100 characters long.',
                                                ])
                                                ->afterStateUpdated(function (callable $set, Get $get, $state) {

                                                    if (!empty($state)) {

                                                        $set('is_same_as_billing', false);
                                                        //  dd($get('is_same_as_billing'));
                                                    }
                                                })->live(),
                                            TextInput::make('billing_address_2')
                                                ->rules(['nullable', 'max:100'])
                                                ->validationMessages([
                                                    'max' => 'The Address Line 2 must be at most 100 characters long.',
                                                ])
                                                ->label('Address line 2')->placeholder('Enter address line 2')
                                                ->afterStateUpdated(function (callable $set, $state) {

                                                    if (!empty($state)) {
                                                        $set('is_same_as_billing', false);
                                                    }
                                                })->live(),

                                            Select::make('billing_state_id')
                                                ->label('State')->placeholder('Select State')->searchable()
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                        ->where('country_id', 132)
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                    if ($state) {
                                                        $set('is_same_as_billing', false);
                                                        $set('billing_city_id', null);
                                                        $set('billing_postal_code', null);
                                                    }
                                                    if (blank($state)) {
                                                        $set('billing_city_id', null);
                                                        $set('billing_postal_code', null);
                                                    }
                                                })
                                                ->required()->live(),
                                            Select::make('billing_city_id')->label('City')->placeholder('Select City')
                                                ->options(function (Get $get) {

                                                    if (!empty($get('billing_state_id'))) {

                                                        return City::where('state_id', $get('billing_state_id'))->pluck('name', 'id');
                                                    }
                                                    return [];
                                                })
                                                ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                    if ($state) {
                                                        $set('is_same_as_billing', false);
                                                        $set('shipping_city_id', $get('billing_city_id'));
                                                        $set('billing_postal_code', null);
                                                    }
                                                    if (blank($state)) {
                                                        $set('billing_postal_code', null);
                                                    }
                                                })
                                                ->getSelectedRecordUsing(function (Get $get) {
                                                    return $get('billing_city_id') ?? '';
                                                })->required()
                                                ->live(onBlur: true)
                                                ->loadingMessage('Loading cities...')
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                        ->where('state_id', $get('billing_state_id'))
                                                        ->pluck('name', 'id')
                                                        ->toArray();
                                                })
                                                ->searchable(),
                                            Select::make('billing_postal_code')->label('Postal Code')->placeholder('Select postal code')
                                                ->options(function (Get $get) {

                                                    if (!empty($get('billing_city_id'))) {

                                                        return ZipCode::where('city_id', $get('billing_city_id'))->pluck('code', 'code');
                                                    }
                                                    return [];
                                                })
                                                ->required()
                                                ->getSearchResultsUsing(function (string $search, Get $get) {
                                                    if ($get('billing_city_id')) {
                                                        return ZipCode::where('city_id', $get('billing_city_id'))
                                                        ->where('code', 'like', "%{$search}%")
                                                        ->pluck('code', 'code')
                                                        ->toArray();
                                                    }
                                                    return [];
                                                })
                                                ->live(onBlur: true)
                                                ->optionsLimit(100)
                                                ->loadingMessage('Loading postal code...')
                                                ->searchable(),
                                                TextInput::make('billing_country_id')
                                                ->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                                            // TextInput::make('billing_postal_code')->label('Postal Code')
                                            //     ->rules(['required', 'digits:5'])->required()
                                            //     ->live()
                                            //     ->suffixIcon(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                            //     ->suffixIconColor(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                            //     ->extraAttributes([
                                            //         'inputmode' => 'numeric',
                                            //         'pattern' => '[0-9]*',
                                            //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                            //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                            //     ])
                                            //     ->placeholder('Enter postal code')->afterStateUpdated(function (callable $set, $state) {

                                            //         if (!empty($state)) {
                                            //             $set('is_same_as_billing', false);
                                            //         }
                                            //     })->live(),
                                        ];
                                    }),
                            ]),
                            // Checkbox::make('is_same_as_billing')
                            // ->live()
                            // ->afterStateUpdated(function (Get $get, Set $set, $state) {
                            //     if ($state) {
                            //         $set('shipping_address_1', $get('billing_address_1'));
                            //         $set('shipping_address_2', $get('billing_address_2'));
                            //         $set('shipping_country_id', $get('billing_country_id'));
                            //         $set('shipping_state_id', $get('billing_state_id'));
                            //         // dd($get('billing_city_id'));
                            //         // Set billing city ID only after setting billing state ID
                            //         $set('shipping_city_id', $get('billing_city_id'));

                            //         $set('shipping_postal_code', $get('billing_postal_code'));
                            //     } else {
                            //         $set('shipping_address_1', '');
                            //         $set('shipping_address_2', '');
                            //         $set('shipping_state_id', '');
                            //         $set('shipping_city_id', '');
                            //         $set('shipping_postal_code', '');
                            //     }
                            // })
                            // ->label('Same as billing address'),


                            // Fieldset::make('Shipping Address')->label(new HtmlString('<span style="font-size: 28px !important;">Shipping Address 1</span>'))
                            // ->schema([
                            //     TextInput::make('user_id')->visible(false),
                            //     TextInput::make('clinic_id')->visible(false),
                            //     TextInput::make('shipping_address_1')
                            //     ->rules(['required', 'max:100'])
                            //     ->validationMessages([
                            //         'max' => 'The Address Line 1 must be at most 100 characters long.',
                            //     ])
                            //     ->label('Address line 1')->placeholder('Enter address line 1')->required(),
                            //     TextInput::make('shipping_address_2')
                            //     ->rules(['required', 'max:100'])
                            //     ->validationMessages([
                            //         'max' => 'The Address Line 2 must be at most 100 characters long.',
                            //     ])
                            //     ->label('Address line 2')->placeholder('Enter address line 2')->required(),

                            //     TextInput::make('shipping_country_id')->placeholder('Malaysia')->label('Country')->default('Malaysia')->readOnly(),
                            //     Select::make('shipping_state_id')
                            //     ->label('State')->placeholder('Select State')->searchable()
                            //     ->options(State::where('country_id', 132)->pluck('name', 'id')->toArray())->required()->live(),


                            //     Select::make('shipping_city_id')->label('City')->placeholder('Select City')
                            //     ->options(function (Get $get) {

                            //         if (!empty($get('billing_state_id'))) {
                            //             return City::where('state_id', $get('billing_state_id'))->pluck('name', 'id')->toArray();
                            //         }
                            //         return [];
                            //     })->live(onBlur: true)->required()
                            //     ->loadingMessage('Loading cities...')
                            //     ->searchable(),
                            //     TextInput::make('shipping_postal_code')
                            //     ->extraAttributes([
                            //         'inputmode' => 'numeric',
                            //         'pattern' => '[0-9]*',
                            //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                            //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                            //     ])
                            //     ->rules(['required','digits:5'])->required()
                            //     ->label('Postal Code')->placeholder('Enter postal code'),
                            // ]),
                            Group::make()->schema([
                                // Repeater for Multiple Shipping Addresses
                                Repeater::make('shipping_addresses')
                                    ->label('')
                                    // ->relationship()
                                    ->schema([
                                        Group::make()->schema([
                                            Checkbox::make('is_default')
                                                ->label('Set as default')
                                                ->live()
                                                ->fixIndistinctState()
                                                ->afterStateUpdated(function ($state, Set $set, Get $get, $component) {
                                                    $currentPath = $component->getStatePath();

                                                    if (preg_match('/shipping_addresses\.([^.]+)\.is_default/', $currentPath, $matches)) {
                                                        $uuid = $matches[1];
                                                        $currentIndex = $matches[1] ?? null;
                                                        if (is_null($currentIndex)) {
                                                            return;
                                                        }
                                                    }

                                                    $addresses = $get('../../shipping_addresses') ?? [];

                                                    // If checked: update others
                                                    if ($state) {
                                                        foreach ($addresses as $key => $address) {
                                                            $addresses[$key]['is_default'] = ((string)$key === (string)$uuid);
                                                        }

                                                        $set('../../shipping_addresses', $addresses);
                                                        $set('../../default_shipping_index', $currentIndex);
                                                    } else {
                                                        // If unchecked and it's the current default, clear it
                                                        if ((string)$get('../../default_shipping_index') === (string)$currentIndex) {
                                                            $addresses[$currentIndex]['is_default'] = false;
                                                            $set('../../shipping_addresses', $addresses);
                                                            $set('../../default_shipping_index', null);
                                                        }
                                                    }
                                                })->columnSpan(1),
                                            Checkbox::make('is_same_as_billing')
                                                ->live()
                                                ->afterStateUpdated(function (Get $get, Set $set, $state, $component) {
                                                    $currentPath = $component->getStatePath();
                                                    if (preg_match('/shipping_addresses\.([^.]+)\.is_same_as_billing/', $currentPath, $matches)) {
                                                        $uuid = $matches[1];
                                                        $currentIndex = $matches[1] ?? null;
                                                        if (is_null($currentIndex)) {
                                                            return;
                                                        }
                                                    }
                                                    $rootData = $get('../../');
                                                    if ($state) {
                                                        $set('../../billing_address_same_index', $currentIndex);

                                                        $set('shipping_address_1', $rootData['billing_address_1']);
                                                        $set('shipping_address_2', $rootData['billing_address_2']);
                                                        $set('shipping_country_id', $rootData['billing_country_id']);
                                                        $set('shipping_state_id', $rootData['billing_state_id']);
                                                        $set('shipping_city_id', $rootData['billing_city_id']);
                                                        $set('shipping_postal_code', $rootData['billing_postal_code']);
                                                        // $set('shipping_nick_name', $rootData['billing_nick_name']);

                                                        $addresses = $get('../../shipping_addresses') ?? [];
                                                        foreach ($addresses as $key => $address) {
                                                            $addresses[$key]['is_same_as_billing'] = ((string)$key === (string)$uuid);
                                                        }
                                                        $set('../../shipping_addresses', $addresses);
                                                        $set('../../billing_address_same_index', $currentIndex);
                                                    } else {
                                                        if ($get('../../billing_address_same_index') == $currentIndex) {
                                                            $set('../../billing_address_same_index', null);
                                                        }
                                                        $set('shipping_address_1', '');
                                                        $set('shipping_address_2', '');
                                                        $set('shipping_state_id', '');
                                                        $set('shipping_city_id', '');
                                                        $set('shipping_postal_code', '');
                                                        $set('shipping_nick_name', '');
                                                    }
                                                })
                                                ->label('Same as billing address')
                                                ->visible(function (Get $get, $state, $component) {
                                                    // Only show this checkbox if no other address has is_same_as_billing true
                                                    $addresses = $get('../../shipping_addresses') ?? [];
                                                    $currentPath = $component->getStatePath();
                                                    preg_match('/shipping_addresses\.([^.]+)\.is_same_as_billing/', $currentPath, $matches);
                                                    $currentIndex = $matches[1] ?? null;

                                                    // If any other address (not this one) has is_same_as_billing true, hide this checkbox
                                                    foreach ($addresses as $key => $address) {
                                                        if ((string)$key !== (string)$currentIndex && !empty($address['is_same_as_billing'])) {
                                                            return false;
                                                        }
                                                    }
                                                    return true;
                                                }),
                                        ])->columns(2),
                                        Fieldset::make(function ($get, $set, $state, $component) {
                                            $items = $component->getContainer()->getParentComponent()->getState();
                                            $currentKey = $component->getContainer()->getStatePath();
                                            preg_match('/shipping_addresses\.([^\.]+)/', $currentKey, $matches);
                                            $currentItemKey = $matches[1] ?? null;

                                            $position = 0;
                                            foreach (array_keys($items ?? []) as $key) {
                                                if ($key === $currentItemKey) {
                                                    break;
                                                }
                                                $position++;
                                            }
                                            return new HtmlString('<span style="font-size: 28px !important;">Shipping Address ' . ($position + 1) . '</span>');
                                        })
                                            ->schema([
                                                TextInput::make('user_id')->visible(false),
                                                TextInput::make('clinic_id')->visible(false),
                                                TextInput::make('shipping_nick_name')
                                                    ->label('Nick Name')
                                                    ->placeholder('Enter nick name')
                                                    ->required()
                                                    ->rules(['required', 'max:50'])
                                                    ->validationMessages([
                                                        'required' => 'The nick name field is required.',
                                                        'max' => 'The nick name must not exceed 50 characters.',
                                                        // 'regex' => 'Only letters, numbers and spaces are allowed.',
                                                    ]),
                                                TextInput::make('shipping_address_1')
                                                    ->label('Address line 1')
                                                    ->rules(['required', 'max:100'])
                                                    ->validationMessages([
                                                        'max' => 'The Address Line 1 must be at most 100 characters long.',
                                                    ])
                                                    ->placeholder('Enter address line 1')
                                                    ->required(),
                                                TextInput::make('shipping_address_2')
                                                    ->label('Address line 2')
                                                    ->rules(['nullable', 'max:100'])
                                                    ->validationMessages([
                                                        'max' => 'The Address Line 2 must be at most 100 characters long.',
                                                    ])
                                                    ->placeholder('Enter address line 2'),

                                                Select::make('shipping_state_id')
                                                    ->label('State')
                                                    ->placeholder('Select State')
                                                    ->searchable()
                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                        return State::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                            ->where('country_id', 132)
                                                            ->pluck('name', 'id')
                                                            ->toArray();
                                                    })
                                                    ->options(State::where('country_id', 132)->pluck('name', 'id'))
                                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                        if ($state) {
                                                            $set('shipping_city_id', null);
                                                            $set('shipping_postal_code', null);
                                                        }
                                                        if (blank($state)) {
                                                            $set('shipping_city_id', null);
                                                            $set('shipping_postal_code', null);
                                                        }
                                                    })
                                                    ->required()
                                                    ->live(),
                                                Select::make('shipping_city_id')
                                                    ->label('City')
                                                    ->placeholder('Select City')
                                                    ->options(function (Get $get) {
                                                        // dd($get('billing_state_id'));
                                                        if (!empty($get('shipping_state_id'))) {
                                                            // dd($get('shipping_state_id'));
                                                            return City::where('state_id', $get('shipping_state_id'))->pluck('name', 'id');
                                                        }
                                                        return [];
                                                    })
                                                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                                        if ($state) {
                                                            $set('shipping_postal_code', null);
                                                        }
                                                        if (blank($state)) {
                                                            $set('shipping_postal_code', null);
                                                        }
                                                    })
                                                    ->required()
                                                    ->live(onBlur: true)
                                                    ->loadingMessage('Loading cities...')
                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                        return City::whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%'])
                                                            ->where('state_id', $get('shipping_state_id'))
                                                            ->pluck('name', 'id')
                                                            ->toArray();
                                                    })
                                                    ->searchable(),

                                                Select::make('shipping_postal_code')->label('Postal Code')->placeholder('Select postal code')
                                                    ->options(function (Get $get) {

                                                        if (!empty($get('shipping_city_id'))) {

                                                            return ZipCode::where('city_id', $get('shipping_city_id'))->pluck('code', 'code');
                                                        }
                                                        return [];
                                                    })
                                                    ->required()
                                                    ->getSearchResultsUsing(function (string $search, Get $get) {
                                                        if ($get('shipping_city_id')) {
                                                            return ZipCode::where('city_id', $get('shipping_city_id'))
                                                            ->where('code', 'like', "%{$search}%")
                                                            ->pluck('code', 'code')
                                                            ->toArray();
                                                        }
                                                        return [];
                                                    })
                                                    ->live(onBlur: true)
                                                    ->optionsLimit(100)
                                                    ->loadingMessage('Loading postal code...')
                                                    ->searchable(),
                                                    TextInput::make('shipping_country_id')
                                                    ->placeholder('Malaysia')
                                                    ->label('Country')
                                                    ->default('Malaysia')
                                                    ->readOnly(),
                                                // TextInput::make('shipping_postal_code')
                                                //     ->label('Postal Code')
                                                //     ->rules(['required', 'digits:5'])
                                                //     ->live()
                                                //     ->suffixIcon(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'heroicon-s-check-circle' : null)
                                                //     ->suffixIconColor(fn ($state) => preg_match('/^\+?[0-9]{5,5}$/', $state) ? 'success' : null)
                                                //     ->extraAttributes([
                                                //         'inputmode' => 'numeric',
                                                //         'pattern' => '[0-9]*',
                                                //         'onkeydown' => 'if(event.key.length === 1 && !/^[0-9]$/.test(event.key)) event.preventDefault();', // Prevent non-numeric input on keydown
                                                //         'oninput' => 'this.value = this.value.replace(/[^0-9]/g, "")' // Restrict to numeric input
                                                //     ])
                                                //     ->required()
                                                //     ->placeholder('Enter postal code'),
                                            ]),
                                    ])
                                    ->addAction(function (\Filament\Forms\Components\Actions\Action $action) {
                                        return $action->label(new HtmlString('<span class="font-bold text-blue-950" style="color:rgb(0,63,94)">+ Add Shipping Address</span>'))
                                            ->extraAttributes([
                                                'style' => 'border: none !important; box-shadow: none !important;'
                                            ]);
                                    })
                                    ->addActionAlignment(Alignment::End)
                                    ->defaultItems(0)

                                    // ->deleteAction(
                                    //     fn ($action) => $action
                                    //     ->hidden(function (Get $get, $component) {
                                    //         // Get the current state of shipping addresses
                                    //         $shippingAddresses = $get('shipping_addresses') ?? [];

                                    //         // Hide delete button if there's only one address left
                                    //         return count($shippingAddresses) <= 1;
                                    //     })
                                    // )

                                    ->deleteAction(
                                        fn (\Filament\Forms\Components\Actions\Action $action) => $action
                                            ->hidden(function (array $arguments, Repeater $component) {
                                                $items = $component->getState();

                                                $activeItem = $items[$arguments['item']] ?? [];
                                                return $activeItem['is_default'] ?? false; // hide delete button if "default" is true
                                            })
                                    ),

                            ])->columns(1)
                        ])->afterValidation(function (Get $get, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['user_id'] = $this->user->id;
                            $data['shipping_addresses'] = $get('shipping_addresses');
                            $data['shipping_address_1'] = $get('shipping_address_1');
                            $data['shipping_address_2'] = $get('shipping_address_2');
                            $data['shipping_state_id'] = $get('shipping_state_id');
                            $data['shipping_city_id'] = $get('shipping_city_id');
                            $data['shipping_country_id'] = $get('shipping_country_id');
                            $data['shipping_postal_code'] = $get('shipping_postal_code');
                            $data['billing_address_1'] = $get('billing_address_1');
                            $data['billing_address_2'] = $get('billing_address_2');
                            $data['billing_state_id'] = $get('billing_state_id');
                            $data['billing_city_id'] = $get('billing_city_id');
                            $data['billing_country_id'] = $get('billing_country_id');
                            $data['billing_postal_code'] = $get('billing_postal_code');
                            $data['is_same_as_billing'] = $get('is_same_as_billing');
                            $data['billing_nick_name'] = $get('billing_nick_name');
                            $data['shipping_nick_name'] = $get('shipping_nick_name');
                            $data['is_default'] = $get('is_default');

                            $service->storeAddressDetails($data, 2);
                        }),
                    Step::make('Pharma Suppliers')
                        ->icon('heroicon-s-building-storefront')
                        ->schema([
                            Group::make()->schema([
                                Placeholder::make('suppliers_table')->label('')
                                    ->content(function ($record) {
                                        $suppliers = ClinicPharmaSupplier::where('clinic_id', $record->id)
                                            ->with(['pcDetail', 'pcDetail.pcDetails'])
                                            ->get();
                                        // dd($suppliers);

                                        if ($suppliers->isEmpty()) {
                                            return 'No suppliers added yet.';
                                        }

                                        return view('clinic.suppliers-table', [
                                            'suppliers' => $suppliers,
                                            'record_id' => $record->id,
                                        ]);
                                    })
                                    ->columnSpanFull(),
                                Forms\Components\Actions::make([
                                    Forms\Components\Actions\Action::make('addSupplier')
                                        ->label('+ Add Supplier')
                                        ->modalHeading('Add Supplier')
                                        ->icon('heroicon-o-plus')
                                        ->form([
                                            Select::make('pc_suplier')
                                                ->label(new HtmlString('Supplier Name <span style="color:red">*</span>'))
                                                ->placeholder('Select supplier name')
                                                // ->options(PcDetail::where('is_credit_line', true)->pluck('business_name', 'user_id')->toArray())
                                                ->options(function ($record) {
                                                    $clinicId = $record->id;
                                                    $associatedSupplierIds = ClinicPharmaSupplier::where('clinic_id', $clinicId)
                                                    ->pluck('pc_id')
                                                    ->toArray();

                                                    return PcDetail::where('is_credit_line', true)
                                                        ->whereNotIn('user_id', $associatedSupplierIds)
                                                        ->pluck('business_name', 'user_id')
                                                        ->toArray();
                                                })
                                                ->searchable()
                                                ->rules(['required'])
                                                ->validationMessages([
                                                    'required' => 'The Supplier Name field is required.',
                                                ])
                                                ->live(),
                                            TextInput::make('account_number')
                                                ->label(new HtmlString('Account Number <span style="color:red">*</span>'))
                                                ->validationMessages([
                                                    'required' => 'The Account Number field is required.',
                                                    'regex' => 'The Account Number must be a alphabetical.',
                                                    'max' => 'The Account Number may not be greater than 20 characters.',
                                                    'min' => 'The Account Number field must be at least 1 characters.',
                                                ])
                                                ->placeholder('Enter account number')
                                                ->validationAttribute('Account Number')
                                                ->rules(['required', 'max:20', 'min:1', 'regex:/^[a-zA-Z0-9]+$/'])

                                        ])->modalSubmitActionLabel('Add Supplier')
                                        ->action(function (array $data, Get $get, ClinicProfileByAdminService $service) {
                                            $data['clinic_id'] = $get('clinic_id');
                                            $data['user_id'] = $this->user->id;

                                            $service->storeClinicPharmaSuplier($data, 3);
                                        })->extraAttributes(['style' => 'margin-left:auto; color: rgb(0,63,94); background-color:transparent; hover:background-color:transparent;'])
                                ])
                            ])->columns(1)
                        ])->afterValidation(function (Get $get, Set $set, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['clinic_id'] = $this->user->clinicData->id;
                            $service->storeUpdateClinicStepInfo($data);
                        }),
                    Step::make('Person In Charge')
                        ->icon('heroicon-o-users')
                        ->schema([
                            Group::make()->schema([

                                // Fieldset::make('Doctor In Charge')->label(new HtmlString('<span style="font-size: 28px !important;">Doctor In Charge </span>'))
                                Fieldset::make(function ($get) {
                                    $clinicAccountTypeId = $this->user->clinicData->clinic_account_type_id ?? null;
                                    $label = ($clinicAccountTypeId == 4) ? 'Pharmacy In Charge' : 'Doctor In Charge';

                                    return new HtmlString('<span style="font-size: 28px !important;">' . $label . '</span>');
                                })
                                    ->schema([
                                        TextInput::make('dc_name')
                                            ->label('Full Name')
                                            ->placeholder('Enter full name')
                                            ->maxLength(50)
                                            ->validationMessages([
                                                'required' => 'The Full Name field is required.',
                                                'regex' => 'Only alphabetical characters and spaces are allowed.',
                                            ])
                                            ->rules(['required', 'regex:/^[a-zA-Z\s]+$/'])
                                            ->required(),
                                        TextInput::make('dc_nric')
                                            ->placeholder('Enter NRIC')
                                            ->live()
                                            ->suffixIcon(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)

                                            ->label(new HtmlString(
                                                'NRIC / Passport Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`National Registration Identity Card (NRIC) Number is a unique personal identification number assigned to Malaysian citizens and permanent residents.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->rules(['nullable', 'max:50'])->validationMessages([
                                                'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                            ]),
                                        TextInput::make('dc_mmc_number')
                                            ->live()
                                            ->suffixIcon(fn ($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->placeholder('Enter MMC registration number')
                                            ->label(new HtmlString(
                                                'MMC Registration Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`This certificate is issued by the Malaysian Medical Council (MMC) to confirm that the Person In-charge is a licensed medical practitioner in Malaysia. Please upload the current MMC Registration Certificate to demonstrate valid professional registration.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->required()->rules(['required', 'regex:/^[a-zA-Z0-9]+$/', 'max:10', 'min:1'])->validationMessages([
                                                'required' => 'The MMC Registration Number field is required.',
                                                //    'numeric' => 'The MMC Registration Number must be numeric.',
                                                'regex' => 'The MMC Registration  Number must be an alphanumeric string.',
                                                'max' => 'The MMC Registration Number may not be greater than 10 characters.',
                                                'min' => 'The MMC Registration Number must be at least 1 characters.',
                                            ]),
                                        TextInput::make('dc_apc_number')
                                            ->live()
                                            ->visible(fn ($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                            ->suffixIcon(fn ($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => (strlen($state) >= 0 && strlen($state) <= 10 && preg_match('/^[a-zA-Z0-9]+$/', $state)) ? 'success' : null)
                                            ->placeholder('Enter current APC No')
                                            ->label(new HtmlString(
                                                'Current APC Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Annual Practicing Certificate (APC) Registration Number is issued to medical professionals to certify their eligibility to practice within the specified year.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->required()->rules(['required', 'regex:/^[a-zA-Z0-9]+$/', 'max:10', 'min:1'])->validationMessages([
                                                'required' => 'The Current APC Number field is required.',
                                                'regex' => 'The Current APC  Number must be an alphanumeric string.',
                                                'max' => 'The Current APC Number may not be greater than 10 characters.',
                                                'min' => 'The Current APC Number must be at least 1 characters.',

                                            ]),
                                        // Select::make('apc_certificate_expired_date ')
                                        // ->options(ClinicDetail::where('user_id', auth()->id())->pluck('apc_certificate_expired_date', 'apc_certificate_expired_date')->toArray())
                                        // ->searchable()
                                        // ->placeholder('Select Expiry Year')
                                        // ->label('APC Expiry Year')
                                        // ->required()->validationMessages([
                                        //     'required' => 'The APC Expiry Year field is required.',
                                        // ]),
                                        Select::make('apc_certificate_expired_date')
                                            ->visible(fn ($record) => !isClinicAccountType($record, array('pharmacy', 'hospital')))
                                            ->label('Expiry Year of APC')->placeholder('Select commencement year')
                                            ->options(self::getExpiryYearOptions())
                                            ->searchable()
                                            ->rules(['required'])->required(),
                                        TextInput::make('dc_phone_number')
                                            ->numeric()->prefix('+60')
                                            ->placeholder('Phone Number')
                                            ->live()
                                            ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                            ->label('Mobile number')
                                            ->required()->rules(['required', 'digits_between:8,12'])->validationMessages([
                                                'required' => 'The Mobile number field is required.',
                                                'digits_between' => 'The Mobile number must be between 8 and 12 digits long.'
                                            ])->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ]),
                                        //    TextInput::make('dc_landline_number')->prefix('+03')
                                        //    ->numeric()->rules(['nullable','digits_between:7,8'])
                                        //    ->placeholder('Enter landline number')
                                        //    ->label('Landline Number')->validationMessages([
                                        //        'required' => 'The phone number field is required.',
                                        //        'digits_between' => 'The phone number must be between 7 and 8 digits long.'
                                        //    ])->mask('9-9999999')
                                        //    ->stripCharacters(['-'])
                                        //    ->extraAttributes([
                                        //        'inputmode' => 'numeric',
                                        //        'maxlength' => '8'
                                        //    ]),

                                        PhoneWithPrefix::make('dc_landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8',
                                                'class' => 'px-3'
                                            ])
                                            ->prefixOptions(function ($get, $set) {
                                                if (empty($get('dc_landline_number'))) {
                                                    return [];
                                                }
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');

                                                if ($stateId && $cityId) {
                                                    $query->where('state_id', $stateId)->where('id', $cityId);
                                                }

                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (empty($data)) {
                                                    $data = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                // FacadesLog::info($get('addresses'));
                                                if ($get("dc_landline_number")["prefix"] === "") {
                                                    $set('dc_landline_number.prefix', $data[array_key_first($data)] ?? '');
                                                }
                                                return $data;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {

                                                if (isset($get('addresses')[0]["dc_landline_code"]) && $get('addresses')[0]["dc_landline_code"] != null) {
                                                    $set("dc_landline_number.prefix", $get('addresses')[0]["dc_landline_code"]);
                                                    $set("dc_landline_number.number", $get('addresses')[0]["dc_landline_number"]);
                                                } else {
                                                    $set("dc_landline_number", ["prefix" => "", "number" => ""]);
                                                }
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set("dc_landline_code", implode(" ", $get("dc_landline_number")));
                                            })->formatStateUsing(function ($get, $set, $state) {

                                                $data = ['prefix' => '', 'number' => ''];
                                                if ($get("dc_landline_code")) {
                                                    $data["prefix"] = $get("dc_landline_code");
                                                }
                                                if ($get("dc_landline_number")) {
                                                    $data["number"] = $get("dc_landline_number");
                                                }
                                                return is_array($state) ? $state : $data;
                                            }),

                                        // Radio::make('signature_type')->options([
                                        //     1 => 'Digital',
                                        //     0 => 'Image',
                                        // ])->live()
                                        //     ->rules(['required'])
                                        //     ->required()
                                        //     ->inline()
                                        //     ->label('Signature Type')
                                        //     ->default(fn ($livewire) => $livewire->get('signature_type') ? 1 : 0)
                                        //     ->columnSpan('full'),

                                        // SignaturePad::make('signature')
                                        //     // ->visible(fn ($get) => $get('signature_type') == 1 && empty($this->user->clinicData->dc_signature))
                                        //     ->visible(fn ($get) => $get('signature_type'))
                                        //     ->undoable(false)
                                        //     ->filename('autograph')
                                        //     ->required()->rules(['required_without:dc_signature']),
                                        FileUpload::make('dc_signature')
                                            // ->visible(fn ($get) => $get('signature_type') == 0)

                                            ->validationAttribute(function () {
                                                $clinicAccountTypeId = $this->user->clinicData->clinic_account_type_id ?? null;
                                                $label = ($clinicAccountTypeId == 4)
                                                    ? 'Signature of Pharmacy In-charge'
                                                    : 'Signature of Doctor In-charge';

                                                return $label;
                                            })
                                            // ->label('Signature of Doctor In-charge')
                                            ->label(function () {
                                                $clinicAccountTypeId = $this->user->clinicData->clinic_account_type_id ?? null;
                                                $label = ($clinicAccountTypeId == 4)
                                                    ? 'Signature of Pharmacy In-charge'
                                                    : 'Signature of Doctor In-charge';

                                                return $label;
                                            })
                                            // ->validationAttribute('Image of Doctor In-charge')
                                            ->rules([fn ($get) => empty($this->user->clinicData->dc_signature)
                                                ? ['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])]
                                                : []])
                                            ->directory(function (Get $get) {
                                                return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                            }) // This specifies the storage directory
                                            ->getUploadedFileNameForStorageUsing(
                                                fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                                    ->prepend('dc_signature_'),
                                            )->required(empty($this->user->clinicData->dc_signature)),

                                            ViewField::make('previous_signature')
                                                ->visible(fn (): bool => $this->user->clinicData->dc_signature !== null)
                                                ->view('filament.admin.resources.clinic-resource.pages.view-previous-sginature', [
                                                    'signatureData' => getImage($this->user->clinicData->dc_signature, '/images/clinic/' . $this->user->clinicData->id),
                                                    //'signatureData' => asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->user->clinicData->dc_signature),
                                                ]),
                                    ]),
                                Checkbox::make('is_admin_in_charge')
                                    // ->label('Same as the Doctor In Charge')
                                    ->label(function () {
                                        $clinicAccountTypeId = $this->user->clinicData->clinic_account_type_id ?? null;
                                        $label = ($clinicAccountTypeId == 4)
                                            ? 'Same as the Pharmacy In Charge'
                                            : 'Same as the Doctor In Charge';

                                        return $label;
                                    })
                                    ->live()->afterStateUpdated(function (Get $get, Set $set, $state) {
                                        if ($state) {
                                            $set('ac_name', $get('dc_name'));
                                            $set('ac_nric', $get('dc_nric'));
                                            $set('ac_phone_number', $get('dc_phone_number'));
                                            $set('ac_landline_number', $get('dc_landline_number'));
                                            $set('ac_landline_code', $get('dc_landline_code'));
                                        } else {
                                            $set('ac_name', '');
                                            $set('ac_nric', '');
                                            $set('ac_phone_number', '');
                                            $defaultLandlineCode = City::whereNotNull('landline_code')
                                                ->where('landline_code', '!=', '')
                                                ->value('landline_code');

                                            $set('ac_landline_number', [
                                                'prefix' => $defaultLandlineCode ?? '',
                                                'number' => ''
                                            ]);
                                            $set('ac_landline_code', $defaultLandlineCode ?? '');
                                        }
                                    })->columnSpan('full'),

                                Fieldset::make('Admin In Charge')->label(new HtmlString('<span style="font-size: 28px !important;">Admin In Charge </span>'))
                                    ->schema([

                                        TextInput::make('ac_name')
                                            ->maxLength(50)
                                            ->placeholder('Enter Full Name')
                                            ->label('Full Name')
                                            ->required(),
                                        TextInput::make('ac_nric')
                                            ->placeholder('Enter NRIC / Passport Number')
                                            ->live()
                                            ->suffixIcon(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => ((strlen($state) <= 50) && (strlen($state) > 0)) ? 'success' : null)
                                            ->label(new HtmlString(
                                                'NRIC / Passport Number <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`National Registration Identity Card (NRIC) Number is a unique personal identification number assigned to Malaysian citizens and permanent residents.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                                </svg>'
                                            ))
                                            ->rules(['nullable', 'max:50'])->validationMessages([
                                                'max' => 'The NRIC / Passport Number may not be more than 50 characters.',
                                            ]),
                                        TextInput::make('ac_phone_number')
                                            ->numeric()->prefix('+60')
                                            ->rules(['required', 'digits_between:8,12']) // Ensures exactly 10 digits (optional)
                                            ->required()
                                            ->placeholder('Enter mobile number')
                                            ->label('Mobile Number')
                                            ->mask('999999999999')
                                            ->stripCharacters(['-'])
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '12'
                                            ])
                                            ->live()
                                            ->suffixIcon(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                            ->suffixIconColor(fn ($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null),
                                        //    TextInput::make('ac_landline_number')
                                        //    ->numeric()->prefix('+03')->rules(['nullable','digits_between:7,8'])
                                        //    ->placeholder('Enter landline number')
                                        //    ->label('Landline Number')
                                        //    ->mask('9-9999999')
                                        //    ->stripCharacters(['-'])
                                        //    ->extraAttributes([
                                        //        'inputmode' => 'numeric',
                                        //        'maxlength' => '8'
                                        //    ])

                                        PhoneWithPrefix::make('ac_landline_number')
                                            ->label("Landline Number")
                                            ->extraAttributes([
                                                'inputmode' => 'numeric',
                                                'maxlength' => '8',
                                                'class' => 'px-3'
                                            ])
                                            ->prefixOptions(function ($get, $set) {
                                                if (empty($get('ac_landline_number'))) {
                                                    return [];
                                                }
                                                $query = City::whereNotNull('landline_code')
                                                    ->where('landline_code', '!=', '');
                                                $stateId = $get('state_id');
                                                $cityId = $get('city_id');

                                                if ($stateId && $cityId) {
                                                    $query->where('state_id', $stateId)->where('id', $cityId);
                                                }

                                                $data = $query
                                                    ->distinct('landline_code')
                                                    ->pluck('landline_code', 'landline_code')
                                                    ->toArray();
                                                if (empty($data)) {
                                                    $data = City::whereNotNull('landline_code')
                                                        ->where('landline_code', '!=', '')
                                                        ->distinct('landline_code')
                                                        ->pluck('landline_code', 'landline_code')
                                                        ->toArray();
                                                }
                                                // FacadesLog::info($get('addresses'));
                                                if ($get("ac_landline_number")["prefix"] === "") {
                                                    $set('ac_landline_number.prefix', $data[array_key_first($data)] ?? '');
                                                }
                                                return $data;
                                            })
                                            ->rules([new PhoneWithPrefixRule()])
                                            ->afterStateHydrated(function (Get $get, Set $set) {

                                                if (isset($get('addresses')[0]["ac_landline_code"]) && $get('addresses')[0]["ac_landline_code"] != null) {
                                                    $set("ac_landline_number.prefix", $get('addresses')[0]["ac_landline_code"]);
                                                    $set("ac_landline_number.number", $get('addresses')[0]["ac_landline_number"]);
                                                } else {
                                                    $set("ac_landline_number", ["prefix" => "", "number" => ""]);
                                                }
                                            })
                                            ->afterStateUpdated(function (Get $get, Set $set) {
                                                $set("ac_landline_code", implode(" ", $get("ac_landline_number")));
                                            })->formatStateUsing(function ($get, $set, $state) {

                                                $data = ['prefix' => '', 'number' => ''];
                                                if ($get("ac_landline_code")) {
                                                    $data["prefix"] = $get("ac_landline_code");
                                                }
                                                if ($get("ac_landline_number")) {
                                                    $data["number"] = $get("ac_landline_number");
                                                }
                                                return is_array($state) ? $state : $data;
                                            }),
                                    ])

                            ])->columns(2)
                        ])->afterValidation(function (Get $get, Set $set, ClinicProfileByAdminService $service) {
                            $data = [];
                            $data['user_id'] = $this->user->id;
                            $data['signature_type'] = $get('signature_type');
                            $data['dc_name'] = $get('dc_name');
                            $data['dc_nric'] = $get('dc_nric');
                            $data['dc_mmc_number'] = $get('dc_mmc_number');
                            $data['dc_apc_number'] = $get('dc_apc_number');
                            $data['dc_phone_number'] = $get('dc_phone_number');
                            $data['dc_landline_number'] = $get('dc_landline_number')["number"] ?? "";
                            $data['dc_landline_code'] = $get('dc_landline_number')["prefix"] ?? "";
                            // $data['dc_signature'] = !empty($get('signature')) ? $get('signature') : $get('dc_signature');
                            if (!empty($get('dc_signature'))) {
                                // Digital signature case
                                $data['dc_signature'] = $get('dc_signature');
                            } else {
                                // No new signature - keep existing one
                                $data['dc_signature'] = $this->user->clinicData->dc_signature;
                            }
                            //$data['dc_signature'] = $get('dc_signature');
                            $signature = $data['dc_signature'];
                            $data['ac_name'] = $get('ac_name');
                            $data['ac_phone_number'] = $get('ac_phone_number');
                            // $data['ac_landline_number'] = $get('ac_landline_number');

                            $data['ac_landline_number'] = $get('ac_landline_number')["number"] ?? "";
                            $data['ac_landline_code'] = $get('ac_landline_number')["prefix"] ?? "";
                            $data['ac_nric'] = $get('ac_nric');
                            // $set('dc_signature', $get('dc_signature'));
                            $data['is_admin_in_charge'] = $get('is_admin_in_charge');
                            $data['apc_certificate_expired_date'] = $get('apc_certificate_expired_date');
                            $data['id'] = $this->user->clinicData->id;
                            // dd($data);
                            $service->storeDoctorInCharge($data, 4, $signature);
                        }),

                    Step::make('Documents')
                        ->icon('heroicon-o-document-text')
                        ->schema([
                            Group::make()->schema([
                                FileUpload::make('dc_signature')
                                    ->visible(false)
                                    // ->label('Signature of Doctor In-charge')
                                    ->label(function () {
                                        $clinicAccountTypeId = $this->user->clinicData->clinic_account_type_id ?? null;
                                        $label = ($clinicAccountTypeId == 4)
                                            ? 'Signature of Pharmacy In-charge'
                                            : 'Signature of Doctor In-charge';

                                        return $label;
                                    })
                                    ->required()
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('dc_signature_'),
                                    )
                                    ->disk('public')
                                    ->rules(['required']),
                                FileUpload::make('borang_certificate')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->validationAttribute('Borang Certificate')
                                    ->required()
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id;
                                    }) // This specifies the storage directory
                                    // ->getUploadedFileNameForStorageUsing(
                                    //     fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                    //         ->prepend('borang_certificate_'),
                                    // )
                                    
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('borang_certificate_')
                                    )
                                    ->label(new HtmlString(
                                        'Certificate of Registration (Account Holder as per Form B/F.) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Borang B” and “Borang F” are official certificates under the Private Healthcare Facilities and Services Act 1998 in Malaysia. They verify that your healthcare facility is properly registered with the Ministry of Health. Please upload a valid copy of the relevant certificate (Borang B or Borang F) to confirm your facility’s registration status.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable()
                                    // ->storeFiles(false)
                                    // ->visibility('public')
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)'), // This allows HTML in the helper text
                                // ViewField::make('borang_certificate_preview')
                                //     ->visible(fn (): bool => !empty($this->borangCertificate))
                                //     ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                //         'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->borangCertificate),
                                //         'fileName' => 'Certificate of Registration (Account Holder as per Form B/F.)',
                                //         'userId' => $this->user->clinicData->id,
                                //         'type' => 'borang'
                                //     ]),
                                FileUpload::make('mmc_certificate')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->visible(fn ($get) => in_array($get('clinic_account_type_id'), [1, 2, 6]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('mmc_certificate_'),
                                    )
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->label(new HtmlString(
                                        'MMC Registration Certificate for Person In-charge <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Malaysian Medical Council (MMC) Number is assigned to licensed medical practitioners in Malaysia for professional recognition.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->validationAttribute('MMC Full Registration Certificate')
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('apc_certificate_'),
                                    )
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable()
                                    // ->storeFiles(false)
                                    // ->visibility('public')
                                    ->required(),
                                // ViewField::make('mmc_certificate_preview')
                                //     ->visible(fn (): bool => (count($this->mmcCertificate) > 0))
                                //     ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                //         // 'filePath' => asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->mmcCertificate),
                                //         'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->mmcCertificate),

                                //         'fileName' => 'MMC Registration Certificate for Person In-charge',
                                //         'userId' => $this->user->clinicData->id,
                                //         'type' => 'mmc'
                                //     ]),
                                FileUpload::make('apc_certificate')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->validationAttribute('Current Annual Practicing Certificate (APC)')
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->visible(fn ($get) => in_array($get('clinic_account_type_id'), [1, 2, 3, 7, 6, 8]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->label(new HtmlString(
                                        'Current Annual Practicing Certificate (APC) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`The Current Annual Practicing Certificate (APC) is a mandatory document certifying that a medical professional is licensed to practice for the ongoing year.`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('apc_certificate_'),
                                    )
                                    ->required()
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable(),
                                    // ->storeFiles(false)
                                    // ->visibility('public'),
                                // ViewField::make('apc_certificate_preview')
                                //     ->visible(fn (): bool => (count($this->apcCertificate) > 0))
                                //     ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                //         // 'filePath' => $this->apcCertificate ? asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->apcCertificate) : null,
                                //         'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->apcCertificate),
                                //         'fileName' => 'Current Annual Practicing Certificate (APC)',
                                //         'userId' => $this->user->clinicData->id,
                                //         'type' => 'apc'
                                //     ]),
                                FileUpload::make('arc_certificate')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->validationAttribute('Annual Retention Certificate (ARC)')
                                    ->visible(fn ($get) => in_array($get('clinic_account_type_id'), [4, 5]))
                                    ->rules(['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->label(new HtmlString(
                                        'Annual Retention Certificate (ARC) <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Annual Retention Certificate (ARC)`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('arc_certificate_'),
                                    )
                                    ->required()
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable(),
                                    // ->storeFiles(false)
                                    // ->visibility('public'),
                                // ViewField::make('arc_certificate_preview')
                                //     ->visible(fn (): bool => (count($this->arcCertificate) > 0))
                                //     ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                //         // 'filePath' => asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->arcCertificate),
                                //         'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->arcCertificate),
                                //         'fileName' => 'ARC Certificate',
                                //         'userId' => $this->user->clinicData->id,
                                //         'type' => 'arc'
                                //     ]),
                                FileUpload::make('poison_license')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->validationAttribute('Poison License Certificate')
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->visible(fn ($get) => in_array($get('clinic_account_type_id'), [4, 5]))
                                    ->label(new HtmlString(
                                        'Poison License <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Poison License`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->rules(function () {
                                        // Explicitly restrict SVG by not including it in allowed types
                                        return ['required', File::types(['jpg', 'jpeg', 'png', 'pdf'])];
                                    })
                                    ->required()
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('poison_license_'),
                                    )
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable(),
                                    // ->visibility('public'),
                                    // ViewField::make('poison_license_preview')
                                    // ->visible(fn (): bool => (count($this->poisonLicense) > 0))
                                    // ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                    //     // 'filePath' => asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->poisonLicense),
                                    //     'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->poisonLicense),

                                    //     'fileName' => 'Poison License Certificate',
                                    //     'userId' => $this->user->clinicData->id,
                                    //     'type' => 'license'
                                    // ]),
                                FileUpload::make('other_relevant_documents')
                                    ->multiple()
                                    ->maxFiles(3)
                                    ->validationAttribute('Other Relevant Documents')
                                    ->rules(['nullable', File::types(['jpg', 'jpeg', 'png', 'pdf'])->max(2 * 1024)])
                                    ->helperText('Supported formats: JPEG, PNG, PDF (SVG files are not allowed. Max 3 files, 2 MB each)')
                                    ->label(new HtmlString(
                                        'Other Relevant Documents <svg xmlns="http://www.w3.org/2000/svg" x-tooltip="`Other Relevant Documents`" class="inline w-4 h-4 ml-1 text-gray-400 cursor-help" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M12 20.5A8.5 8.5 0 103.5 12 8.5 8.5 0 0012 20.5z" />
                                        </svg>'
                                    ))
                                    ->directory(function (Get $get) {
                                        return config('constants.api.media.clinic_medias') . $this->user->clinicData->id . '/';
                                    }) // This specifies the storage directory
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->lower()
                                            ->replaceMatches('/[^A-Za-z0-9\-\_\.]/', '_')
                                            ->replaceMatches('/_+/', '_')
                                            ->prepend('other_relevant_documents_'),
                                    )
                                    ->downloadable()
                                    ->previewable()
                                    ->openable()
                                    ->deletable()
                                    ->reorderable(),
                                    // ->visibility('public'),
                                // ViewField::make('other_relevant_documents_preview')
                                //     ->visible(fn (): bool => (count($this->otherRelevantDocuments) > 0))
                                //     ->view('filament.admin.resources.clinic-resource.pages.certificate-preview', [
                                //         // 'filePath' => asset('storage/images/clinic/'.$this->user->clinicData->id.'/'.$this->otherRelevantDocuments),
                                //         'filePaths' => array_map(fn ($path) => asset('storage/images/clinic/' . $this->user->clinicData->id . '/' . $path), $this->otherRelevantDocuments),
                                //         'fileName' => 'Other Relevant Documents',
                                //         'userId' => $this->user->clinicData->id,
                                //         'type' => 'other_relevant_documents'
                                //     ]),
                                // Select::make('apc_certificate_expired_date')
                                //     ->label('Expiry Year')->placeholder('Select year')
                                //     ->options(self::getExpiryYearOptions())
                                //     ->searchable()
                                //     ->rules(['required'])->required(),


                            ])->columns(2),
                        ])
                ])
                ->submitAction(
                    Action::make('submit')
                        ->label('Submit')
                        ->action('store')
                        ->color('primary')
                )
                ->columnSpanFull()
            // ->startOnStep($this->user->pcDetails->step),
        ])->statePath('data');
    }

    public function store(ClinicProfileByAdminService $service)
    {
        $service->storeLegalDocuments($this->form->getState(), $this->record);

        // Activity Log Start - log only on first onboarding
        $clinicData = $this->user->clinicData ?? null;
        if ($clinicData && empty($clinicData->is_submitted)) {
            activity()
                ->causedBy(auth()->user())
                ->useLog('facility_profile_create')
                ->performedOn($clinicData)
                ->withProperties([
                    'attributes' => [
                        'status' => 'pending',
                    ],
                ])
                ->log("Facility onboarding has been completed by admin for {$this->user->name}");
        }
        // Activity Log End

        Notification::make()
            ->success()
            ->duration(1000)
            ->body('Facility has been updated successfully.')
            ->send();
        $this->redirect(ClinicResource::getUrl('view', ['record' => $this->record]));
    }

    public function deleteSupplier($supplierId)
    {
        $supplier = ClinicPharmaSupplier::find($supplierId);

        if ($supplier && $supplier->clinic_id == $this->record) {
            $supplier->delete();
            Notification::make()
                ->title('Supplier deleted successfully')
                ->success()
                ->send();
        }
    }
}
