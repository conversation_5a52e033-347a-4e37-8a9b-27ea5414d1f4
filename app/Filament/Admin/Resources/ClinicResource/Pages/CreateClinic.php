<?php

namespace App\Filament\Admin\Resources\ClinicResource\Pages;

use App\Filament\Admin\Resources\ClinicResource;
use App\Models\ClinicPharmaSupplier;
use App\Service\ClinicProfileByAdminService;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Filament\Actions\Action;

class CreateClinic extends CreateRecord
{
    protected static string $resource = ClinicResource::class;

    protected function getFormActions(): array
    {
        return [];
    }

    public function storeData(bool $another = false): void
    {
        $this->form->getState();
        try {


            // Process certificates
            $certificateFields = [
                'borang_certificate',
                'mmc_certificate',
                'apc_certificate',
                'arc_certificate',
                'poison_license',
            ];

            $service = new ClinicProfileByAdminService();
            $service->storeLegalDocuments($this->data, $this->data['user_id']);

            // Redirect to the list page after successful submission
            Notification::make()
                ->success()
                // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                ->duration(1000)
                ->body('Facility has been created successfully.')
                ->send();
            $this->redirect(ClinicResource::getUrl('index')); // Update with your actual route name
        } catch (\Exception $e) {
            // Log the exception and show an error message
            info('Error in storeData: ' . $e->getMessage());
            session()->flash('error', 'An error occurred while processing your request.');
        }
    }

    public function deleteSupplier($supplierId)
    {
        $supplier = ClinicPharmaSupplier::find($supplierId);
        if ($supplier && $supplier->clinic_id) {
            $supplier->delete();
            Notification::make()
                ->title('Supplier deleted successfully')
                ->success()
                ->send();
        }
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ClinicResource::getUrl('index')),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => "Facilities",
            3 => "Create",
        ];
    }
}
