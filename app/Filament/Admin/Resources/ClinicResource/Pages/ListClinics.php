<?php

namespace App\Filament\Admin\Resources\ClinicResource\Pages;

use App\Filament\Admin\Resources\ClinicResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListClinics extends ListRecords
{
    protected static string $resource = ClinicResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label("+ Add Facility")
            ->extraAttributes(['class'=> 'mb-4']),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Users",
            // $this->getResource()::getUrl('index') => "Facilities",
            // 3 => "List",
        ];
    }
}
