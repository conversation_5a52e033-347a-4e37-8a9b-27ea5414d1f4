<?php

namespace App\Filament\Admin\Resources\ClinicResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class DpharmapointsRelationManager extends RelationManager
{
    protected static string $relationship = 'dpharmapoints';
    protected static ?string $title = 'Audit Trail of Reward Points';



    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('points')
                    ->required()
                    ->maxLength(255),
            ]);
    }


    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('points')
            ->modifyQueryUsing(fn(Builder $query) => $query->orderBy('created_at', 'desc'))
            ->columns([
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Date')
                    ->formatStateUsing(fn(string $state): string => (new \DateTime($state))->format('F j, Y')),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')->html(),
                Tables\Columns\TextColumn::make('points')
                    ->label('Points Earned'),
                Tables\Columns\TextColumn::make('redeem')
                    ->label('Points Redeem'),
                Tables\Columns\TextColumn::make('balance')
                    ->label('Balance')
                    ->formatStateUsing(fn(string $state): string => $state . ' points'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    // public static function canViewForRecord($ownerRecord, $pageClass): bool
    // {
    //     $resource = $pageClass::getResource();

    //     // Get the active tab from the Resource's Livewire property
    //     $activeTab = $resource->activeTab ?? null;

    //     // Only allow viewing if the active tab is 'Reward Points'
    //     return $activeTab === 'Reward Points';
    // }
}
