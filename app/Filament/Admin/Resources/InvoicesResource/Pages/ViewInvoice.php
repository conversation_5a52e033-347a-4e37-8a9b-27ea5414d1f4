<?php

namespace App\Filament\Admin\Resources\InvoicesResource\Pages;

use App\Filament\Admin\Resources\InvoicesResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use App\Models\SubOrder;
class ViewInvoice extends ViewRecord
{
    protected static string $resource = InvoicesResource::class;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Order Details')
                    ->schema([
                        TextEntry::make('order_number')
                            ->label('Order ID')
                            ->prefix('#'),
                        TextEntry::make('ps_invoice_id')
                            ->label('PS Invoice ID'),
                        TextEntry::make('amount')
                            ->label('Order Amount')
                            ->prefix('RM '),
                        TextEntry::make('commission_amount')
                            ->label('Commission Amount')
                            ->prefix('RM '),
                        TextEntry::make('created_at')
                            ->label('Order Date')
                            ->dateTime(),
                    ])
                    ->columns(2),
            ]);
    }

    public function subOrdersTable(Table $table): Table
    {
        return $table
            ->query(SubOrder::query()->where('order_id', $this->record->id))
            ->columns([
                TextColumn::make('po_number')
                    ->label('PO Number')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('invoice_ps_to_customer')
                    ->label('Invoice (PS to Customer)')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('invoice_admin_to_ps')
                    ->label('Invoice (Admin to PS)')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('amount')
                    ->label('Order Amount')
                    ->prefix('RM ')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('commission_amount')
                    ->label('Admin Fee')
                    ->prefix('RM ')
                    ->searchable()
                    ->sortable(),
                IconColumn::make('status')
                    ->label('Status')
                    ->icon(fn (string $state): string => match ($state) {
                        'delivered' => 'heroicon-o-check-circle',
                        'pending' => 'heroicon-o-clock',
                        'cancelled' => 'heroicon-o-x-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->color(fn (string $state): string => match ($state) {
                        'delivered' => 'success',
                        'pending' => 'warning',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
            ])
            ->paginated(false)
            ->actions([])
            ->bulkActions([]);
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('back')
                ->label('Back to List')
                ->url(fn () => route('filament.admin.resources.invoices.index'))
                ->icon('heroicon-o-arrow-left'),
        ];
    }
}
