<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\InquiryResource\Pages;
use App\Models\Inquiry;
use Carbon\Carbon;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class InquiryResource extends Resource
{
    protected static ?string $model = Inquiry::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('inquiries_view');
    }

    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->columns([
                TextColumn::make('name')->searchable()->sortable()->toggleable(),
                TextColumn::make('landline_number')
                    ->label('Mobile number')
                    ->toggleable()->default('-')
                    ->formatStateUsing(function ($record) {
                        if (blank($record->landline_number)) {
                            return '-';
                        }
                
                        $code = $record->code ? '+'.$record->code : '';
                        return trim("{$code} {$record->landline_number}");
                    })
                    ->sortable()
                    ->searchable(query: function ($query, $search) {
                        $query->where(function ($q) use ($search) {
                            $q->where('landline_number', 'like', "%{$search}%")
                                ->orWhere('code', 'like', "%{$search}%");
                        });
                    }),       
                TextColumn::make('email')->searchable()->sortable()->toggleable(),
                TextColumn::make('type')->searchable()->sortable()->formatStateUsing(fn($state) => strtoupper($state))->toggleable(),
                TextColumn::make('description')->toggleable()
                    ->searchable()
                    ->sortable()
                    // ->formatStateUsing(function (string $state) {
                    //     $words = explode(' ', $state);
                    //     $first4Words = array_slice($words, 0, 4);
                    //     return implode(' ', $first4Words) . '...';
                    // }),
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $fullText = $column->getRecord()->description;
                        return strlen($fullText) > 30 ? $fullText : null;
                    }),
                TextColumn::make('subject')->searchable()->toggleable()
                    ->sortable()
                    // ->formatStateUsing(function (string $state) {
                    //     $words = explode(' ', $state);
                    //     $first4Words = array_slice($words, 0, 4);
                    //     return implode(' ', $first4Words) . '...';
                    // }),
                    ->limit(30)
                    ->tooltip(function (TextColumn $column): ?string {
                        $fullText = $column->getRecord()->subject;
                        return strlen($fullText) > 30 ? $fullText : null;
                    }),
                TextColumn::make('created_at')->toggleable()
                    ->label('Date & Time')
                    ->searchable()
                    ->sortable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('type')
                    ->label('Type')
                    ->options([
                        'b2b' => 'B2B',
                        'b2c' => 'B2C',
                    ]),
                SelectFilter::make('user_id')
                    ->label('User')
                    ->options(function () {
                        return \App\Models\User::whereHas('inquiries')
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->searchable()
                    ->multiple(),
                Filter::make('custom_date_range')
                    ->form([
                        DateTimePicker::make('start_date')
                            ->label('Start Date & Time')
                            ->seconds(false)
                            ->timezone($timeZone)
                            ->displayFormat('M d, Y | h:i A')
                            ->maxDate(now($timeZone))
                            ->closeOnDateSelection(false)
                            ->reactive()
                            ->afterStateUpdated(function ($set, $state, $get) use ($timeZone) {
                                $endDate = $get('end_date');
                                if ($state && $endDate) {
                                    try {
                                        $startDateTime = Carbon::parse($state, $timeZone);
                                        $endDateTime = Carbon::parse($endDate, $timeZone);
                                        if ($endDateTime->lt($startDateTime) && !$endDateTime->isSameDay($startDateTime)) {
                                            $set('end_date', null);
                                        }
                                    } catch (\Exception $e) {
                                        // Handle any parsing errors silently
                                    }
                                }
                            }),

                        DateTimePicker::make('end_date')
                            ->label('End Date & Time')
                            // ->native(false)
                            ->seconds(false)
                            ->timezone($timeZone)
                            ->closeOnDateSelection(false)
                            ->displayFormat('M d, Y | h:i A')
                            ->minDate(function ($get) use ($timeZone) {
                                $startDate = $get('start_date');
                                if ($startDate) {
                                    try {
                                        return Carbon::parse($startDate, $timeZone)->startOfDay();
                                    } catch (\Exception $e) {
                                        return null;
                                    }
                                }
                                return null;
                            })
                            ->maxDate(now($timeZone))
                            ->rules([
                                function ($get) use ($timeZone) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get, $timeZone) {
                                        $startDate = $get('start_date');
                                        if ($value && $startDate) {
                                            try {
                                                $startDateTime = Carbon::parse($startDate, $timeZone);
                                                $endDateTime = Carbon::parse($value, $timeZone);
                                                
                                                if ($endDateTime->lte($startDateTime)) {
                                                    $fail('End date must be after start date.');
                                                }
                                            } catch (\Exception $e) {
                                                $fail('Invalid date format.');
                                            }
                                        }
                                    };
                                }
                            ]),
                    ])
                    ->query(function ($query, array $data) use ($timeZone) {
                        if (!empty($data['start_date'])) {
                            $start = Carbon::parse($data['start_date'], $timeZone)->startOfMinute()->setTimezone('UTC');
                            $query->where('created_at', '>=', $start);
                        }
                        
                        if (!empty($data['end_date'])) {
                            $end = Carbon::parse($data['end_date'], $timeZone)->endOfMinute()->setTimezone('UTC');
                            $query->where('created_at', '<=', $end);
                        }
                    })
                    ->indicateUsing(function (array $data) use ($timeZone): ?string {
                        $indicators = [];
                        
                        if (!empty($data['start_date'])) {
                            $indicators[] = 'From: ' . Carbon::parse($data['start_date'], $timeZone)->format('M d, Y | h:i A');
                        }
                        
                        if (!empty($data['end_date'])) {
                            $indicators[] = 'To: ' . Carbon::parse($data['end_date'], $timeZone)->format('M d, Y | h:i A');
                        }
                        
                        return !empty($indicators) ? implode(' - ', $indicators) : null;
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\Action::make('view')
                    ->label('')
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->color('gray')
                    ->size('md')
                    ->color(function (Inquiry $record): string {
                        return $record->read_at ? 'primary' : 'success';
                    })
                    ->tooltip(function (Inquiry $record): string {
                        return $record->read_at ? 'View (Read)' : 'View (Unread)';
                    })
                    ->url(fn(Inquiry $record): string => static::getUrl('view', ['record' => $record]))
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListInquiries::route('/'),
            'view' => Pages\ViewInquiry::route('/{record}'),
        ];
    }
}
