<?php

namespace App\Filament\Admin\Resources\UserManageResource\Pages;

use App\Filament\Admin\Resources\UserManageResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Notifications\Notification;
use Filament\Pages\Actions\Action;
use Indianic\FilamentShield\Resources\RoleResource\Pages\ViewRole;

class ViewUserManage extends ViewRecord
{
    protected static string $resource = UserManageResource::class;

    public function getTitle(): string
    {
        return $this->record->name;
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserManageResource::getUrl()),
        ];
    }

    public static function canView(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_view');
    }

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Details')
                    ->schema([
                        Grid::make()
                            ->schema([
                                // Left column with large avatar
                                ImageEntry::make('photo')
                                    ->circular()
                                    ->columnSpan(1)
                                    ->default(asset('/images/user-avatar.png')),
                                // Right column with user details in a grid
                                Grid::make()
                                    ->schema([
                                        TextEntry::make('id')
                                            ->label('User ID')
                                            ->formatStateUsing(fn(string $state): string => "#{$state}"),
                                        TextEntry::make('name')
                                            ->label('Full Name'),
                                        TextEntry::make('email')
                                            ->label('Email'),
                                        TextEntry::make('phone')
                                            ->label('Phone Number')
                                            ->formatStateUsing(fn(string $state): string => preg_replace('/(\+\d{1,3})(\d+)/', '$1 $2', $state)),
                                        TextEntry::make('roles.name')
                                            ->label('Role')
                                            ->default('-')
                                            ->url(fn($record) => $record->roles->first() ? ViewRole::getUrl(['record' => $record->roles->first()->id]) : null)
                                            ->formatStateUsing(function ($state) {
                                                if (!$state) {
                                                    return '<span style="color: blue;">-</span>';
                                                }

                                                // Remove leading numbers and dash (e.g., "2-Admin" becomes "Admin")
                                                $cleaned = preg_replace('/^\d+-\s*/', '', $state);

                                                return '<span style="color: blue;">' . e($cleaned) . '</span>';
                                            })
                                            ->html(),
                                        ViewEntry::make('is_active')
                                            ->label('Status')
                                            ->view('components.user-toggle-switch')

                                    ])
                                    ->columns(3)
                                    ->columnSpan(2),
                            ])
                            ->columns(3),
                    ]),
            ]);
    }

    public function getBreadcrumbs(): array
    {
        return [
            0 => 'Users',
            $this->getResource()::getUrl('index') => 'Admin Users',
            1 => 'User Details',
        ];
    }

    public function toggleStatus($userId, $status)
    {
        $user = \App\Models\User::find($userId);

        if ($user) {
            $user->is_active = $status;
            $user->save();
            Notification::make()
                ->success()
                ->title('Status Updated')
                ->body("User status has been updated to " . ($status ? 'Active' : 'Inactive') . ".")
                ->send();
        }
    }
}
