<?php

namespace App\Filament\Admin\Resources\UserManageResource\Pages;

use Filament\Actions;
use App\Mail\UserCreatedMail;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Mail;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Admin\Resources\UserManageResource;
use App\Mail\SendAdminUserPasswordMail;
use App\Models\User;
use Filament\Actions\Action;
use Illuminate\Support\Facades\Hash;

class CreateUserManage extends CreateRecord
{

    protected static string $resource = UserManageResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserManageResource::getUrl()),
        ];
    }

    public function getTitle(): string
    {
        return 'Add Admin User';
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_create');
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => 'Admin Users',
            3 => 'Add Admin User',
        ];
    }

    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Add'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['parent_id'] = auth()->id();
        unset($data['roles']);
        return $data;
    }

    protected function afterCreate(): void
    {
        $user = $this->record;
        $role = $this->data['role'];

        if ($role) {
            $user->assignRole($role);
        }

        // Mail::to($this->record->email)->send(new UserCreatedMail($this->record, 'pc'));
        $this->sendCredential($this->record);

        try {
            Notification::make()
                ->success()
                // ->title('User has been Created Successfully')
                ->title("User has been created successfully.")
                ->send();
            //Mail::to($user->email)->send(new UserCreatedMail($user->username, $this->plainPassword));
        } catch (\Exception $e) {
            Notification::make()
                ->warning()
                // ->title('User has been Created Successfully')
                ->title("User created successfully with username '{$user->username}' but email notification failed to send.")
                ->persistent()
                ->send();
        }
    }

    protected function getCreatedNotification(): ?Notification
    {
        return null;
    }

    public function sendCredential($data)
    {
        $userEmail = $data['email'];
        $plainPassword = generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['id'])->update(['password' => $hashedPassword, 'is_temp_password' => true]);
        $loginUrl = config('app.admin_url');    
        Mail::to($userEmail)->send(new SendAdminUserPasswordMail($plainPassword, $userEmail, $loginUrl, $data));
    }
}
