<?php

namespace App\Filament\Admin\Resources\UserManageResource\Pages;

use App\Filament\Admin\Resources\UserManageResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditUserManage extends EditRecord
{
    protected static string $resource = UserManageResource::class;

    public static function canEdit(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('admin-users_update');
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UserManageResource::getUrl()),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Users",
            $this->getResource()::getUrl('index') => 'Admin Users',
            3 => 'Edit Admin User',
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        unset($data['role']);

        return $data;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['role'] = $this->record->roles->first()?->name;

        return $data;
    }

    // protected function afterSave(): void
    // {
    //     $user = $this->record;
    //     $roleName = $this->data['role'];

    //     if ($roleName) {
    //         $user->syncRoles([$roleName]);
    //     }
    // }

    protected function afterSave(): void
    {
        $user = $this->record;
        $newRoleName = $this->data['role'];
        $oldRoleName = $user->roles->first()?->name;

        if ($newRoleName && $newRoleName !== $oldRoleName) {
            $user->syncRoles([$newRoleName]);

            activity()
                ->causedBy(auth()->user())
                ->useLog('users')
                ->performedOn($user)
                ->withProperties([
                    'old' => [
                        'role' => $oldRoleName,
                    ],
                    'attributes' => [
                        'role' => $newRoleName,
                    ],
                ])
                ->log("Admin User has been updated");
        }
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title('User has been updated')
            ->title('The user has been updated successfully.');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
