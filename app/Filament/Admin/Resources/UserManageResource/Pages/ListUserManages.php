<?php

namespace App\Filament\Admin\Resources\UserManageResource\Pages;

use App\Filament\Admin\Resources\UserManageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Imports\UserManageImporter;

class ListUserManages extends ListRecords
{
    protected static string $resource = UserManageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add User'),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Users",
            // $this->getResource()::getUrl('index') => "Admin Users",
            // 3 => "List",
        ];
    }
}
