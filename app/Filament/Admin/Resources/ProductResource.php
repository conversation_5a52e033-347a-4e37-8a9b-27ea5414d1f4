<?php

namespace App\Filament\Admin\Resources;

use Carbon\Carbon;
use App\Models\User;
use NumberFormatter;
use App\Models\Product;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Enums\LabelEnum;
use App\Models\Category;
use App\Models\PcDetail;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use App\Models\ProductBatch;
use Illuminate\Http\Request;
use PhpParser\Node\Stmt\Nop;
use App\Livewire\SellersList;
use Illuminate\Support\Number;
use App\Models\ProductRelation;
use App\Actions\EditPriceAction;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Livewire\Attributes\Session;
use App\Actions\AdminPriceAction;
use App\Mail\ProductRejectedMail;
use App\Models\ProductCommission;
use Awcodes\TableRepeater\Header;
use Livewire\Attributes\Computed;
use App\Observers\ProductObserver;
use Filament\Support\Colors\Color;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use App\Component\PackagingToolTip;
use Filament\Forms\Components\Grid;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Radio;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\View;
use App\Livewire\ProductTableForForm;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\Tabs;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Enums\FiltersLayout;
use App\Services\ProductTabsCacheService;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use App\Filters\CategorySubCategoryFilter;
use Filament\Forms\Components\Placeholder;
use Filament\Infolists\Components\Livewire;
use Filament\Infolists\Components\Tabs\Tab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\ImageEntry;
use DefStudio\SearchableInput\DTO\SearchResult;
use Illuminate\Support\Number as SupportNumber;
use App\Infolists\Components\DragDropImageEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Awcodes\TableRepeater\Components\TableRepeater;
use Filament\Infolists\Components\Grid as InfoGrid;
use App\Filament\Pc\Resources\ProductResource\Pages;
use Filament\Infolists\Components\Group as InfoGroup;
use Google\Service\DatabaseMigrationService\TableEntity;
use Filament\Infolists\Components\Section as InfoSection;
use App\Filament\Admin\Resources\UserResource\Pages\ViewUser;
use Filament\Forms\Components\Livewire as ComponentsLivewire;
use DefStudio\SearchableInput\Forms\Components\SearchableInput;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Illuminate\Contracts\Database\Query\Builder as QueryBuilder;
use App\Filament\Admin\Resources\ProductResource\Pages\EditProduct;
use App\Filament\Admin\Resources\ProductResource\Pages\ViewProduct;
use App\Filament\Admin\Resources\ProductResource\Pages\ListProducts;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateProduct;
use App\Filament\Admin\Resources\ProductResource\Pages\PcProductView;
use App\Filament\Pc\Resources\ProductResource\Widgets\NormalProducts;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProduct;
use App\Filament\Pc\Resources\ProductResource\Widgets\BatchWiseProducts;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateProductNewForPc;
use App\Filament\Admin\Resources\ProductResource\Pages\ProductBulkStockUpdate;
use App\Filament\Admin\Resources\ProductResource\Pages\CreateNewProductFromExistingProduct;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'bi-box-seam';

    protected static bool $canCreateAnother = false;

    protected static ?string $navigationLabel = 'Product Catalog';

    public $isAllProducts = false;

    public $isRejectedProducts = false;

    public $isPending = false;

    public $categories = [];

    #[Computed]
    public static function getCategories()
    {
        static $categories = [];

        if (empty($categories)) {
            $categories = Category::whereNull('parent_id')
                ->where('status', true)
                ->orderBy('serial_number', 'asc')
                ->orderBy('name', 'asc')
                ->get();
        }

        return $categories;
    }


    public static function form(Form $form): Form
    {
        // Cache Auth::id() to avoid repeated database queries
        $authId = Auth::id();

        return $form
            ->schema([
                // 🟢 Section with form fields only
                Section::make()->schema([
                    Select::make('product_id')
                        ->label(new HtmlString(
                            LabelEnum::PRODUCT_NAME->value . " <span class='text-red-500' style='color:red;'>*</span>"
                        ))
                        ->allowHtml(true)
                        ->live(onBlur: true)
                        ->native(false)
                        ->validationAttribute('product')
                        ->options(function ($livewire) {
                            $products = \App\Services\ProductRelationCacheService::getProductsNotAddedByUser($livewire->pcId, 9);
                            $createNew = collect(['create_new' => '<span class="text-blue-950">+ Create New</span>']);

                            return collect($products)->union($createNew)->all();
                        })
                        ->afterStateUpdated(function (Get $get, $livewire) {
                            if ($get('product_id') === 'create_new') {
                                return redirect()->to(ProductResource::getUrl('create-new-product-for-pc', ['user_id' => $livewire->pcId]));
                            }
                        })
                        ->rules(['required'])
                        ->getSearchResultsUsing(function (string $search, $livewire) use ($authId): array {
                            session()->put('search_term_' . $authId, $search);

                            // Use cache for search results
                            $cacheKey = "product_search_{$search}_{$livewire->pcId}";
                            $products = \Illuminate\Support\Facades\Cache::remember($cacheKey, 300, function () use ($search, $livewire) {
                                // Get products not added by user using caching service
                                $notAddedProductIds = \App\Services\ProductRelationCacheService::getProductsNotAddedByUser($livewire->pcId, 1000, true);

                                return Product::whereRaw("similarity(name, ?) > 0.2", [$search])
                                    ->orderByRaw("similarity(name, ?) DESC", [$search])
                                    ->whereIn('id', $notAddedProductIds)
                                    ->where('status', 'approved')
                                    ->take(50)
                                    ->pluck('name', 'id')
                                    ->toArray();
                            });

                            if (count($products) === 0) {
                                Notification::make()
                                    ->title('No products found, please create new.')
                                    ->warning()
                                    ->send();
                            }

                            return $products + [
                                'create_new' => '<span class="text-blue-950">+ Create New</span>',
                            ];
                        })
                        ->searchable()
                        ->preload(),
                ]),

                Actions::make([
                    \Filament\Forms\Components\Actions\Action::make('add')
                        ->action('create'),
                    \Filament\Forms\Components\Actions\Action::make('cancel')
                        ->label('Cancel')
                        ->color('gray')
                        ->outlined()
                        ->action(function (Get $get, Set $set) {
                            return redirect()->to(ProductResource::getUrl('index'));
                        })
                ]),

                ComponentsLivewire::make(ProductTableForForm::class)
                    ->key(fn($livewire) => "product-table-for-form-{$livewire->pcId}")
                    ->columnSpanFull(), // Optional styling control
            ]);
    }

    // #[Computed]
    public static function userName($state)
    {
        static $userCache = [];
        
        if (!$state) return '';
        
        if (!isset($userCache[$state])) {
            $userCache[$state] = User::find($state)?->name ?? '';
        }
        
        return $userCache[$state];
    }

    // #[Computed]
    public static function productRelation()
    {
        static $relations = null;
        
        if ($relations === null) {
            $relations = User::whereHas('productRelations')
                ->whereNotNull('name')
                ->where('name', '!=', '')
                ->select('id', 'name')
                ->pluck('name', 'id')
                ->toArray();
        }
        
        return $relations;
    }


    public static function table(Table $table): Table
    {
        // Cache Auth::id() to avoid repeated database queries
        // $authId = Auth::id();

        return $table
            ->actionsColumnLabel(LabelEnum::TABLE_ACTION_LABEL->value)
            ->defaultSort('id', 'desc')
            ->recordUrl(fn($record)  => route('filament.admin.resources.products.view', ['record' => $record->id]))
            ->filtersFormWidth('4xl')
            ->filtersFormColumns(2)
            ->modifyQueryUsing(fn(Builder $query) => $query->with(['productData', 'generic', 'category', 'subcategory', 'brand']))

            ->columns([
                TextColumn::make('id')
                    ->toggleable()
                    ->label(LabelEnum::PRODUCT_ID->value)
                    ->prefix('#')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->toggleable()
                    ->label(LabelEnum::PRODUCT_NAME->value)
                    ->searchable()
                    ->sortable(),
                TextColumn::make('generic.name')
                    ->toggleable()
                    ->label(LabelEnum::GENERIC_NAME->value)
                    ->searchable()
                    ->sortable(),
                TextColumn::make('category.name')
                    ->toggleable()
                    ->label(LabelEnum::CATEGORY_NAME->value)
                    ->searchable()
                    ->sortable(),
                TextColumn::make('subcategory.name')
                    ->toggleable()
                    ->label(LabelEnum::SUB_CATEGORY_NAME->value)
                    ->searchable()
                    ->sortable(),
                TextColumn::make('add_request_by')
                    ->label(LabelEnum::ADDED_FROM->value)
                    ->toggleable()
                    // ->visible(fn($livewire) => !$livewire->isAllProducts)
                    ->formatStateUsing(function ($state) {
                        // return $state ? User::find($state)->name : '';
                        return self::userName($state);
                    })
                    ->sortable(),
                TextColumn::make('status')
                    ->formatStateUsing(function ($state) {
                        // Define background and text colors for each status
                        $statuses = [
                            'approved' => ['bg' => '#e4fbe4', 'text' => '#006400'],   // Green
                            'pending' => ['bg' => '#f3e3c6', 'text' => '#FF8C00'],    // Yellow
                            'cancelled' => ['bg' => '#f8d9d4', 'text' => '#B22222'],  // Red
                            'rejected' => ['bg' => '#f8d9d4', 'text' => '#B22222'],   // Red
                        ];

                        $badgeColor = $statuses[$state]['bg'] ?? '#e5e7eb'; // Default gray if not found
                        $textColor = $statuses[$state]['text'] ?? '#374151';

                        // Use inline style for background and text color
                        return new HtmlString(
                            "<span class='inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium' style='background-color: {$badgeColor}; color: {$textColor};'>{$state}</span>"
                        );
                    })
                    ->label(LabelEnum::STATUS->value)
                    ->sortable(),
                TextColumn::make('created_at')
                    ->toggleable()
                    ->label('Created by')
                    ->visible(fn($livewire) => $livewire->isAllProducts)
                    ->formatStateUsing(function ($record) {
                        // Enhanced user cache with bulk loading to avoid N+1 queries
                        static $userCache = [];
                        static $isInitialized = false;

                        // Initialize cache with bulk loading on first call
                        if (!$isInitialized) {
                            // Get all unique user IDs that might be accessed
                            $userIds = collect(request()->get('tableRecords', []))
                                ->pluck('owner_id')
                                ->filter()
                                ->unique()
                                ->values();

                            if ($userIds->isNotEmpty()) {
                                $users = User::whereIn('id', $userIds)->get(['id', 'name']);
                                foreach ($users as $user) {
                                    $userCache["user_{$user->id}"] = $user;
                                }
                            }
                            $isInitialized = true;
                        }

                        $cacheKey = "user_{$record->owner_id}";

                        // Fallback to individual query if not in bulk cache
                        if (!isset($userCache[$cacheKey])) {
                            $userCache[$cacheKey] = User::find($record->owner_id);
                        }

                        $user = $userCache[$cacheKey];
                        return $user ? $user->name : '';
                    })

                    ->action(function ($record) {
                        return redirect()->to(UserResource::getUrl('view', ['record' => $record->owner_id]));
                    }),
                TextColumn::make('productData.rejected_reason')
                    ->label('Rejected Reason')
                    ->formatStateUsing(function ($state) {
                        return Str::limit($state, 20);
                    })
                    ->visible(function ($record, $livewire) {
                        return $livewire->isRejectedProducts;
                    })
                    ->alignCenter()
                    ->tooltip(function ($state, $record) {
                        if (!$record->add_request_by) {
                            return '-';
                        }
                        $reson = $record->productDataForPC($record->add_request_by)?->rejected_reason ?? '-';
                        return $reson;
                    }),
                TextColumn::make('created_at')
                    ->label('Created At')
                    ->formatStateUsing(function ($state) {
                        $userTimezone = Auth::user()->timezone ?? 'UTC';
                        return Carbon::parse($state)->timezone($userTimezone ?? 'UTC')->format('d-m-Y h:i A');
                    })
                    ->sortable(),
                TextColumn::make('productData.submitted_on')
                    ->label('Submitted On')
                    ->default('-')
                    ->toggleable()
                    ->formatStateUsing(function ($record) {
                        if (!$record->owner_id) {
                            return '-';
                        }
                        $submittedOn = $record->productDataForPC($record->owner_id)?->submitted_on;
                        $userTimezone = Auth::user()->timezone ?? 'UTC';
                        return !empty($submittedOn) ? Carbon::parse($submittedOn)->timezone($userTimezone ?? 'UTC')->format('d-m-Y h:i A') : '-';
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->leftJoin('products_relation as pr_sort', function ($join) {
                                $join->on('products.id', '=', 'pr_sort.product_id')
                                     ->where('pr_sort.user_id', '=', function ($subQuery) {
                                         $subQuery->select('owner_id')
                                                  ->from('products as p2')
                                                  ->whereColumn('p2.id', 'products.id')
                                                  ->limit(1);
                                     });
                            })
                            ->select('products.*') // Ensure we select only products columns to avoid conflicts
                            ->orderBy('pr_sort.submitted_on', $direction);
                    }),

            ])
            ->filters([
                SelectFilter::make('status')
                    ->label(LabelEnum::STATUS->value)
                    ->options([
                        'pending' => 'Pending',
                        'approved' => 'Approved',
                        'rejected' => 'Rejected',
                    ]),

                SelectFilter::make('generic_name_id')
                    ->label(LabelEnum::GENERIC_NAME->value)
                    ->relationship('generic', 'name')
                    ->searchable()
                    ->preload(),

                CategorySubCategoryFilter::make('category-subcategory'),

                SelectFilter::make('add_request_by')
                    ->label(LabelEnum::ADDED_FROM->value)
                    ->options(function () {
                        // return User::whereHas('productRelations')
                        // ->whereNotNull('name')
                        // ->where('name', '!=', '')
                        // ->pluck('name', 'id')
                        // ->toArray();
                        return self::productRelation();
                    })
                    ->searchable(),
                Filter::make('created_at')
                    ->label('Created Date Range')
                    ->form([
                        \Filament\Forms\Components\Section::make('Created Date Range')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        DatePicker::make('created_from')
                                            ->label('From')
                                            ->placeholder('Start date')
                                            ->maxDate(today()),
                                        DatePicker::make('created_until')
                                            ->label('Until')
                                            ->placeholder('End date')
                                            ->minDate(fn($get) => $get('created_from'))
                                            ->maxDate(today()),
                                    ]),
                            ])
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['created_from'] ?? null) {
                            $indicators['created_from'] = 'Created from ' . \Carbon\Carbon::parse($data['created_from'])->format('M j, Y');
                        }
                        if ($data['created_until'] ?? null) {
                            $indicators['created_until'] = 'Created until ' . \Carbon\Carbon::parse($data['created_until'])->format('M j, Y');
                        }
                        return $indicators;
                    }),

                Filter::make('submitted_on')
                    ->label('Submitted Date Range')
                    ->form([
                        \Filament\Forms\Components\Section::make('Submitted Date Range')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        DatePicker::make('submitted_from')
                                            ->label('From')
                                            ->placeholder('Start date')
                                            ->maxDate(today()),
                                        DatePicker::make('submitted_until')
                                            ->label('Until')
                                            ->placeholder('End date')
                                            ->minDate(fn($get) => $get('submitted_from'))
                                            ->maxDate(today()),
                                    ]),
                            ])
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['submitted_from'],
                                fn(Builder $query, $date): Builder => $query->whereHas('productData', function ($subQuery) use ($date) {
                                    $subQuery->whereDate('submitted_on', '>=', $date);
                                }),
                            )
                            ->when(
                                $data['submitted_until'],
                                fn(Builder $query, $date): Builder => $query->whereHas('productData', function ($subQuery) use ($date) {
                                    $subQuery->whereDate('submitted_on', '<=', $date);
                                }),
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['submitted_from'] ?? null) {
                            $indicators['submitted_from'] = 'Submitted from ' . \Carbon\Carbon::parse($data['submitted_from'])->format('M j, Y');
                        }
                        if ($data['submitted_until'] ?? null) {
                            $indicators['submitted_until'] = 'Submitted until ' . \Carbon\Carbon::parse($data['submitted_until'])->format('M j, Y');
                        }
                        return $indicators;
                    }),
            ])

            ->actions([
                EditAction::make()
                    ->label(LabelEnum::EDIT->value)
                    ->tooltip(LabelEnum::EDIT->value)
                    ->visible(function ($livewire, $record) {
                        $user = Auth::user();
                        // Use cached productData to avoid duplicate queries
                        $cachedProductData = self::getCachedProductData($record);
                        $hasUsers = $cachedProductData['hasUsers'];
                        if (!$hasUsers && $livewire->isAllProducts) {
                            return $user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_update');
                        }
                        return false;
                    })
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),

                // Action::make('approve')
                //     ->label(LabelEnum::APPROVE->value)
                //     ->icon('heroicon-o-check')
                //     ->tooltip(LabelEnum::APPROVE->value)
                //     ->color('success')
                //     ->visible(function ($record, $livewire) {
                //         $user = Auth::user();
                //         return $record->status == 'pending' && 
                //                ($user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_approve'));
                //     })
                //     ->requiresConfirmation()
                //     ->action(function ($record) use ($authId) {
                //         $record->update(['status' => 'approved', 'approved_by' => $authId, 'approved_on' => now()]);
                //         // Cache productData to avoid duplicate queries
                //         $productData = $record->productDataForPC($record->owner_id);
                //         $productData?->update(['admin_approval' => true, 'is_rejected' => false]);
                //         Notification::make()
                //             ->sendToDatabase(User::find($productData?->user_id ?? 1))
                //             ->title('Your product has been approved by admin.');
                //         Notification::make()
                //             ->title('Product approved successfully.')
                //             ->send();
                //     })
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                //     ->size('sm')->iconButton(),

                ViewAction::make('view')
                    ->label(LabelEnum::VIEW->value)
                    ->tooltip(LabelEnum::VIEW->value)
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->visible(function ($record, $livewire) {
                        $user = Auth::user();
                        return $user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_view');
                    })
                    ->action(fn($record) => redirect()->to(ProductResource::getUrl('view', ['record' => $record->id]))),

                // Action::make('reject')
                //     ->label(LabelEnum::REJECT->value)
                //     ->icon('heroicon-o-x-mark')
                //     ->tooltip(LabelEnum::REJECT->value)
                //     ->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                //     ->visible(function ($record, $livewire) {
                //         $user = Auth::user();
                //         // Use cached productData to avoid duplicate queries
                //         $cachedProductData = self::getCachedProductData($record);
                //         $firstProductData = $cachedProductData['firstProductData'];
                //         return $record->status == 'pending' && 
                //                ($user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_reject'));
                //     })
                //     ->requiresConfirmation()
                //     ->modalHeading('Reject Product?')
                //     ->modalDescription('Are you sure you want to reject this product? This action cannot be undone.')
                //     ->modalSubmitActionLabel('Yes, Reject')
                //     ->modalIcon('heroicon-o-x-circle') // Optional icon
                //     ->color('danger') // Makes button red like delete modal
                //     ->form([
                //         Textarea::make('reason')
                //             ->label(new HtmlString(LabelEnum::REASON->value . " <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                //             ->validationAttribute('reason')
                //             ->rules(['required', 'string'])
                //             ->maxLength(255)
                //     ])
                //     ->action(function ($record, $data) use ($authId) {
                //         $userId = $record->owner_id;
                //         // Cache productData to avoid duplicate queries
                //         $productData = $record->productDataForPC($userId);
                //         $createdBy = User::find($productData?->user_id);
                //         $productData?->update([
                //             'admin_approval' => false,
                //             'rejected_reason' => $data['reason'],
                //             'is_rejected' => true,

                //         ]);
                //         $res = $record->update([
                //             'approved_by' => $authId,
                //             'approved_on' => now(),
                //             'admin_verified_on' => now(),
                //             'status' => 'rejected',
                //         ]);

                //         Mail::to($createdBy->email)->send(new ProductRejectedMail($data['reason'], $record, $createdBy));

                //         Notification::make()
                //             ->body("Rejected")
                //             ->info()
                //             ->title("Your product ($record->name) has been rejected by admin.")
                //             ->actions([
                //                 \Filament\Notifications\Actions\Action::make('view')
                //                     ->label('View Product')
                //                     ->url(\App\Filament\Pc\Resources\ProductResource::getUrl('view', ['record' => $record->id], panel: 'pc')),

                //             ])
                //             ->sendToDatabase($createdBy);

                //         Notification::make()
                //             ->title('Product rejected successfully.')
                //             ->send();
                //     }),
                DeleteAction::make()
                    ->label(LabelEnum::DELETE->value)
                    ->tooltip(LabelEnum::DELETE->value)
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($livewire, $record) {
                        $user = Auth::user();
                        // Use cached productData to avoid duplicate queries
                        $cachedProductData = self::getCachedProductData($record);
                        $hasUsers = $cachedProductData['hasUsers'];
                        if (!$hasUsers && $livewire->isAllProducts) {
                            return $user->hasRole('Super Admin') || $user->hasRole('Admin') || $user->can('products_delete');
                        }
                        return false;
                    }),
            ])

            ->bulkActions([
                // ...
            ]);
    }

    /**
     * Cache productData per record to avoid duplicate queries
     */
    private static function getCachedProductData($record): array
    {
        static $cache = [];
        $cacheKey = "product_data_{$record->id}";
        if (!isset($cache[$cacheKey])) {
            $productDataCollection = $record->productData;
            $cache[$cacheKey] = [
                'hasUsers' => $productDataCollection?->count() > 0,
                'firstProductData' => $productDataCollection?->first(),
                'collection' => $productDataCollection
            ];
        }

        return $cache[$cacheKey];
    }

    /**
     * Cache productData for forms to avoid repeated queries
     */
    private static function getCachedProductDataForForms($record): mixed
    {
        static $formCache = [];
        $cacheKey = "form_product_data_{$record->id}";

        if (!isset($formCache[$cacheKey])) {
            $formCache[$cacheKey] = $record->productData;
        }

        return $formCache[$cacheKey];
    }

    /**
     * Clear static caches when needed (useful for testing or long-running processes)
     */
    public static function clearCaches(): void
    {
        // Force reset of static caches by triggering a dummy call
        // This is mainly useful for testing or long-running processes

        // Note: Request-scoped static caches will be reset automatically
        // on the next request, but this method documents cache clearing capability
        // In practice, static caches reset automatically per request
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $authId = Auth::id();
        $userId = getUser(Auth::user())->id;
        $record = $infolist->getRecord();
        $productCommission = ProductCommission::where('product_id', $record->id)->where('user_id', $record->owner_id)->first();
        
        // Cache global settings to avoid repeated database queries
        static $globalCommissionCache = null;
        if ($globalCommissionCache === null) {
            $globalCommissionCache = DB::table('global_settings')
                ->whereIn('name', ['commission', 'commission_type'])
                ->get();
        }
        $globalCommission = $globalCommissionCache;

        // Cache PcDetail to avoid repeated database queries
        static $pcCommissionCache = [];
        if (!isset($pcCommissionCache[$authId])) {
            $pcCommissionCache[$authId] = PcDetail::where('id', $authId)->first();
        }
        $pcCommission = $pcCommissionCache[$authId];

        $finalCommissionType = '';
        $finalCommission = 0;
        if (! empty($pcCommission)) {
            $finalCommissionType = $pcCommission?->commission_type;
            $finalCommission = $pcCommission?->commission_percentage;
        } else {
            $finalCommissionType = $globalCommission?->where('name', 'commission_type')->first()?->value;
            $finalCommission = $globalCommission?->where('name', 'commission')->first()?->value;
        }

        if($productCommission){
            $finalCommissionType = $productCommission?->east_fixed_cummission_type;
            $finalCommission = $productCommission?->east_fixed_cummission_value;
        }

        return $infolist
            ->extraAttributes(['x-data' => '{ showDetails: false }'])
            ->schema([
                InfoGrid::make(3)->schema([
                    InfoSection::make('Product Details')
                        ->schema([
                            InfoGrid::make(2)->schema([
                                InfoGroup::make()->schema([

                                    TextEntry::make('name')->label('Product Name'),
                                    TextEntry::make('category.name')
                                        ->label('Category'),
                                    TextEntry::make('subcategory.name')
                                        ->label('Sub Category'),

                                    TextEntry::make('sku')
                                        ->label('Stock Keeping Unit (SKU)'),
                                    TextEntry::make('generic_name_id')
                                        ->formatStateUsing(function ($state, $record) {
                                            $data = $record->generic?->name;
                                            return $data;
                                        })
                                        ->label('Generic Name'),
                                    TextEntry::make('brand_id')
                                        ->formatStateUsing(function ($state, $record) {
                                            return $record->brand?->name;
                                        })
                                        ->label('Brand'),

                                    TextEntry::make('distributors')
                                        ->label('Distributors')
                                        ->formatStateUsing(function ($record) {
                                            $names = [];

                                            foreach ($record->distributors as $dist) {
                                                $names[] = $dist['name'] ?? '-';
                                            }

                                            return implode(' | ', $names);
                                        }),
                                    TextEntry::make('status')
                                    ->label(new HtmlString('<span class="text-white font-bold bg-red-500 p-2 rounded-md shadow-md">Reason for Rejection</span>'))
                                        ->formatStateUsing(function ($record) {
                                            // Cache productData to avoid duplicate queries
                                            static $productDataCache = [];
                                            $userId = $record->owner_id;
                                            $cacheKey = "product_data_{$record->id}_{$userId}";

                                            if (!isset($productDataCache[$cacheKey])) {
                                                $productDataCache[$cacheKey] = $record->productDataForPC($userId);
                                            }

                                            $reason = $productDataCache[$cacheKey]?->rejected_reason ?? '';
                                            return new HtmlString('<span class="font-bold text-red-700">' . $reason . '</span>');
                                        })->columnSpanFull()
                                        ->visible(function ($record) {
                                            return $record->status == 'rejected';
                                        }),

                                    SpatieMediaLibraryImageEntry::make('mda_document')
                                        ->collection('mda-documents')
                                        ->disk('s3')
                                        ->width(100)
                                        ->visibility('public')
                                        ->visible(function ($record) {
                                            return (bool) $record->subcategory?->is_mda;
                                        })
                                        ->label('MDA Document'),
                                    TextEntry::make('is_prescription_required')
                                        ->formatStateUsing(function ($state) {
                                            return $state ? 'Yes' : 'No';
                                        })
                                        ->label('Prescription Required'),
                                    TextEntry::make('created_at')
                                        ->label('Created At')
                                        ->formatStateUsing(function ($state) {
                                            // dd($state);
                                            $userTimezone = Auth::user()->timezone ?? 'UTC';
                                            return Carbon::parse($state)->timezone($userTimezone ?? 'UTC')->format('d-m-Y h:i A');
                                        }),
                                    TextEntry::make('productData.submitted_on')
                                        ->label('Submitted On')
                                        ->visible(function ($record) {
                                            return $record->status == 'pending' || $record->status == 'rejected';
                                        })
                                        ->formatStateUsing(function ($record) {
                                        if (!$record->owner_id) {
                                            return '-';
                                        }
                                        $submittedOn = $record->productDataForPC($record->owner_id)?->submitted_on;
                                        $userTimezone = Auth::user()->timezone ?? 'UTC';
                                        return Carbon::parse($submittedOn)->timezone($userTimezone ?? 'UTC')->format('d-m-Y h:i A');
                                    }),
                                    TextEntry::make('productData.rejected_on')
                                        ->visible(fn($record) => $record->status === 'rejected')
                                        ->label('Rejected On')
                                                                ->formatStateUsing(function ($state, $record) {
                            if (empty($state)) {
                                // Fallback: try to get from relation if not already loaded
                                if ($record->owner_id) {
                                    $state = $record->productDataForPC($record->owner_id)?->rejected_on;
                                }
                            }
                            if (empty($state)) {
                                return '-';
                            }
                            $userTimezone = Auth::user()->timezone ?? 'UTC';
                            return Carbon::parse($state)->timezone($userTimezone ?? 'UTC')->format('d-m-Y h:i A');
                        }),
                                    InfoGroup::make()
                                        ->schema([
                                            ViewEntry::make('view_more')
                                                ->label('')
                                                ->view('custom-view.show-hide-button'),
                                        ])->columnSpanFull(),

                                ])->columns(3)->columnSpanFull(),
                            ]),
                        ])->columnSpan(2),
                    InfoSection::make('Product Images')
                        ->extraAttributes(['class' => 'min-h-full'])
                        ->schema([
                            DragDropImageEntry::make('images')
                        ])->columnSpan(1),
                ])->columnSpanFull(),
                InfoSection::make()

                    ->heading('Product Description')
                    ->extraAttributes(['x-show' => 'showDetails', 'x-cloak' => true])
                    ->schema([
                        InfoGroup::make()
                            ->label('Product Details')
                            ->schema([
                                InfoGroup::make()
                                    ->label('Product Details')
                                    ->schema([
                                        Tabs::make(label: 'Product Details')
                                            ->schema([
                                                Tab::make('Product Description')
                                                    ->schema([
                                                        TextEntry::make('product_description')
                                                            ->html()
                                                            ->formatStateUsing(fn($state) => $state)
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Key Ingredients')
                                                    ->schema([
                                                        TextEntry::make('description_ingredients')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Storage Instructions')
                                                    ->schema([
                                                        TextEntry::make('description_storage_instructions')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Usage/Indication')
                                                    ->schema([
                                                        TextEntry::make('description_indications')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Contradiction')
                                                    ->schema([
                                                        TextEntry::make('description_contradictions')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('How to Use')

                                                    ->schema([
                                                        TextEntry::make('description_how_to_use')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Safety Information/Pregnancy')
                                                    ->schema([
                                                        TextEntry::make('description_safety_information')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Dosage Information')
                                                    ->schema([
                                                        TextEntry::make('description_dosage')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                                Tab::make('Side Effects')
                                                    ->schema([
                                                        TextEntry::make('description_side_effects')
                                                            ->html()
                                                            ->label(''),
                                                    ]),
                                            ]),
                                    ]),
                            ])->columnSpanFull(),
                    ])->columnSpanFull(),

                InfoSection::make('Stock Details')
                    ->schema([
                        TextEntry::make('container.name')
                        ->label(PackagingToolTip::tooltip()),
                        TextEntry::make('foam.name')
                        ->default('-')
                        ->label('Product Form'),
                        TextEntry::make('unit.name')->label('Volume Unit'),
                        TextEntry::make('quantity_per_unit')
                            ->label(function ($record) {
                                if (!empty($record->container->name)) {
                                    return "Volume by {$record->container->name}";
                                }
                                return 'Volume';
                            }),
                       
                        TextEntry::make('weight')
                            ->label('Weight (gms)'),


                    ])->columns(5),
                InfoSection::make('sellers')
                    ->schema(function ($record) {
                        return [
                            Livewire::make(SellersList::class, ['record' => $record])
                                ->key(fn($record) => "sellers-list-{$record->id}")
                        ];
                    }),
                TextEntry::make('disclaimer')
                    ->default('Disclaimer will be here !')
                    ->label('Disclaimer')
                    ->columnSpanFull()
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getWidgets(): array
    {
        return [
            NormalProducts::class,
            BatchWiseProducts::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListProducts::route('/'),
            'create' => CreateProduct::route('/create'),
            'edit' => EditProduct::route('/{record}/edit'),
            'create-new' => CreateNewProduct::route('/create-new'),
            'create-existing' => CreateNewProductFromExistingProduct::route('/create-existing/{record}'),
            'product-bulk-stock-update' => ProductBulkStockUpdate::route('/product-bulk-stock-update'),
            'view' => ViewProduct::route('/{record}/view-admin'),
            'create-new-product-for-pc' => CreateProductNewForPc::route('/create-new-product-for-pc/{user_id}'),
            'pc-product-view' => PcProductView::route('/{record}/pc-product-view/{user}'),
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            // $this->addProductAction(),
            $this->bulkStockUpdateAction(),
        ];
    }


    public function bulkStockUpdateAction(): Action
    {
        return Action::make('bulk_stock_update')
            ->label('Bulk Stock Update');
    }

    public static function getEditPriceOptions($record)
    {
        return [
            'fixed' => new HtmlString('Fixed &nbsp;
            <div x-data="{ tooltip: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip: true}" x-on:mouseleave="{tooltip: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip : true}">Sets a fixed price that does not vary with quantity or duration.</title>
            </svg>
            </div>
            '),

                'bonus' => new HtmlString('Bonus &nbsp;
            <div x-data="{ tooltip2: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip2: true}" x-on:mouseleave="{tooltip2: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip2 : true}">Provides extra value or quantity at the same price, like Buy 1 Get 1 Free.</title>
            </svg>
            </div>
            '),

                'tier' => new HtmlString('Tier &nbsp;
            <div x-data="{ tooltip3: false }" class="relative inline-block">
                <svg x-on:mouseenter="{tooltip3: true}" x-on:mouseleave="{tooltip3: false}" style="cursor: pointer;" xmlns="http://www.w3.org/2000/svg" class="inline w-3 h-3 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
                <title x-show="{tooltip3 : true}">Applies different prices based on quantity purchased — more units may cost less per unit.</title>
            </svg>
            </div>
            '),
        ];
    }

    public static function getFixedPriceSchema($record, $finalCommission, $finalCommissionType)
    {
        return [
            Group::make()
                ->extraAttributes(['style' => 'background-color: #DEEFF5;height:40px;border-radius: 5px; padding: 10px; padding-right: 20px;margin-right: 20px;'])
                ->schema([
                    Placeholder::make('Region'),
                    Placeholder::make('Price')->label('Price(RM)'),
                    Placeholder::make('Admin Fees'),
                    Placeholder::make('Net Earnings'),
                ])->columns(4),
            Group::make()->schema([
                Placeholder::make('East Malaysia')
                    ->label("")
                    ->content("East Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('east_zone_price')
                    ->validationAttribute('east zone price')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric', 'price'];
                        if ($get('price_type') === 'fixed') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->formatStateUsing(function ($record) {
                        $cachedProductData = self::getCachedProductDataForForms($record);
                        return $cachedProductData?->east_zone_price;
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            $commission = $finalCommission * $get('east_zone_price') / 100;
                            $earning = $get('east_zone_price') - $commission;

                            return 'RM ' . number_format($earning, 2);
                        } else {
                            $commission = $finalCommission * $get('west_zone_price');
                            $earning = $get('east_zone_price') - $commission;

                            return 'RM ' . number_format($earning, 2);
                        }

                        return 'RM15';
                    }),

                Placeholder::make('West Malaysia')
                    ->label("")
                    ->content("West Malaysia")
                    ->extraAttributes(['class' => 'mt-3']),
                TextInput::make('west_zone_price')
                    ->live()
                    ->numeric()
                    ->rules(function (Get $get) {
                        $rules = ['numeric', 'price'];
                        if ($get('price_type') === 'bonus') {
                            $rules[] = 'required';
                        }
                        return $rules;
                    })
                    ->validationAttribute('west zone price')
                    ->formatStateUsing(function ($record) {
                        return $record->productData?->west_zone_price;
                    })
                    ->label(''),
                Placeholder::make('Admin Fees')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function () use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            return $finalCommission . '%';
                        }

                        return 'RM ' . $finalCommission;
                    }),
                Placeholder::make('Net Earnings')
                    ->label("")
                    ->extraAttributes(['class' => 'mt-3'])
                    ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                        if ($finalCommissionType == 'percentage') {
                            $commission = (float)$finalCommission * (float)$get('west_zone_price') / 100;
                            $earning = (float)$get('west_zone_price') - (float)$commission;

                            return 'RM ' . number_format((float)$earning, 2);
                        } else {
                            $commission = $finalCommission * $get('west_zone_price');
                            $earning = (float)$get('west_zone_price') - $commission;

                            return 'RM ' . number_format((float)$earning, 2);
                        }

                        return 'RM15';
                    }),
            ])->columns(4),
        ];
    }

    public static function getEastBonusPriceSchema($record, $finalCommission, $finalCommissionType)
    {
        return [
            Group::make()
                ->schema([
                    TextInput::make('east_zone_price')
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_1_base_price;
                        })
                        ->label(new HtmlString('Base Price <span style="color:red;">*</span>'))
                        ->validationAttribute('east zone price')
                        ->live(onBlur: true)
                        ->numeric()
                        ->rules(function (Get $get) {
                            $rules = ['numeric', 'price'];
                            if ($get('price_type') === 'bonus') {
                                $rules[] = 'required';
                            }
                            return $rules;
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_zone_price;
                        })
                        ->afterStateUpdated(function ($state, Set $set, Get $get) use ($finalCommission, $finalCommissionType) {
                            // Calculate Admin Fees
                            if ($finalCommissionType == 'percentage') {
                                $adminFees = $finalCommission;
                            } else {
                                $adminFees = $finalCommission;
                            }

                            $set('east_bonus_admin_fees', $adminFees);

                            // Calculate Net Earnings
                            $commission = ($finalCommissionType == 'percentage')
                                ? ($finalCommission * $state / 100)
                                : ($finalCommission * $state);

                            $netEarnings = $state - $commission;
                            $set('east_bonus_net_earnings', number_format($netEarnings, 2, '.', ''));
                        })
                        ->prefix('RM'),
                    TextInput::make('east_bonus_admin_fees')
                        ->label('Admin Fees')
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return $finalCommission;
                            }

                            return $finalCommission;
                        })
                        ->disabled()
                        ->prefix(function () use ($finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return '%';
                            }

                            return 'RM';
                        }),
                    TextInput::make('east_bonus_net_earnings')
                        ->disabled()
                        ->prefix('RM')
                        ->reactive()
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                $commission = $finalCommission * $record->productData?->east_zone_price / 100;
                                $earning = $record->productData?->east_zone_price - $commission;
                            } else {
                                $commission = $finalCommission * $record->productData?->east_zone_price;
                                $earning = $record->productData?->east_zone_price - $commission;
                            }

                            return $earning;
                        })

                        ->label('Net Earnings'),
                ])
                ->columns(3),
            Group::make()
                ->columns(2)
                ->schema([
                    TextInput::make('east_bonus_1_quantity')
                        ->numeric()
                        ->validationAttribute('east bonus 1 quantity')
                        ->rules(function (Get $get) {
                            if ($get('price_type') == 'bonus') {
                                return ['required', 'max:999999', 'numeric'];
                            }
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_1_quantity;
                        })
                        ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                    TextInput::make('east_bonus_1_quantity_value')
                        ->numeric()
                        ->validationAttribute('east bonus 1 quantity value')
                        ->rules(function (Get $get) {
                            $max = $get('east_bonus_1_quantity');
                            if ($get('price_type') == 'bonus') {
                                return ['required', "max:$max", 'numeric'];
                            }
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_1_quantity_value;
                        })
                        ->label(new HtmlString('Bonus Qty. <span style="color:red;">*</span>')),
                    TextInput::make('east_bonus_2_quantity')
                        ->numeric()
                        ->validationAttribute('east bonus 2 quantity')
                        ->rules(['max:999999', 'numeric'])
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_2_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('east_bonus_2_quantity_value')
                        ->numeric()
                        ->validationAttribute('east bonus 2 quantity value')
                        ->rules(function (Get $get) {
                            $max = $get('east_bonus_2_quantity');
                            return ['numeric', "max:$max"];
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_2_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                    TextInput::make('east_bonus_3_quantity')
                        ->validationAttribute('east bonus 3 quantity')
                        ->rules(['numeric', 'max:999999'])
                        ->numeric()
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_3_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('east_bonus_3_quantity_value')
                        ->validationAttribute('east bonus 3 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('east_bonus_3_quantity');
                            return ['numeric', "max:$max"];
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->east_bonus_3_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                ]),
        ];
    }

    public static function getWestBonusPriceSchema($record, $finalCommission, $finalCommissionType)
    {
        return [
            Group::make()
                ->schema([
                    TextInput::make('west_zone_price')
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_zone_price;
                        })
                        ->label(new HtmlString('Base Price <span style="color:red;">*</span>'))
                        ->validationAttribute('west zone price')
                        ->live(onBlur: true)
                        ->numeric()
                        ->rules(function (Get $get) {
                            $rules = ['numeric', 'price'];
                            if ($get('price_type') === 'bonus') {
                                $rules[] = 'required';
                            }
                            return $rules;
                        })
                        ->afterStateUpdated(function ($state, Set $set, Get $get) use ($finalCommission, $finalCommissionType) {
                            // Calculate Admin Fees
                            if ($finalCommissionType == 'percentage') {
                                $adminFees = $finalCommission;
                            } else {
                                $adminFees = $finalCommission;
                            }

                            $set('west_bonus_admin_fees', $adminFees);

                            // Calculate Net Earnings
                            $commission = ($finalCommissionType == 'percentage')
                                ? ($finalCommission * $state / 100)
                                : ($finalCommission * $state);

                            $netEarnings = $state - $commission;
                            $set('west_bonus_net_earnings', number_format($netEarnings, 2));
                        })
                        ->prefix('RM'),
                    TextInput::make('west_bonus_admin_fees')
                        ->label('Admin Fees')
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return $finalCommission;
                            }

                            return $finalCommission;
                        })
                        ->disabled()
                        ->prefix(function () use ($finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                return '%';
                            }

                            return 'RM';
                        }),
                    TextInput::make('west_bonus_net_earnings')
                        ->disabled()
                        ->formatStateUsing(function ($record) use ($finalCommission, $finalCommissionType) {
                            if ($finalCommissionType == 'percentage') {
                                $commission = $finalCommission * $record->productData?->west_zone_price / 100;
                                $earning = $record->productData?->west_zone_price - $commission;
                            } else {
                                $commission = $finalCommission * $record->productData?->west_zone_price;
                                $earning = $record->productData?->west_zone_price - $commission;
                            }

                            return $earning;
                        })
                        ->prefix('RM')
                        ->reactive()
                        ->label('Net Earnings'),
                ])
                ->columns(3),
            Group::make()
                ->columns(2)
                ->schema([
                    TextInput::make('west_bonus_1_quantity')
                        ->validationAttribute('west bonus 1 quantity')
                        ->numeric()
                        ->rules(['required', 'max:999999', 'numeric'])
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_1_quantity;
                        })
                        ->label(new HtmlString('Quantity <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_1_quantity_value')
                        ->validationAttribute('west bonus 1 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_1_quantity');
                            return ['required', 'numeric', "max:$max"];
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_1_quantity_value;
                        })
                        ->label(new HtmlString('Quantity Qty. <span style="color:red;">*</span>')),
                    TextInput::make('west_bonus_2_quantity')
                        ->validationAttribute('west bonus 2 quantity')
                        ->rules(['numeric', 'max:999999'])
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_2_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_2_quantity_value')
                        ->validationAttribute('west bonus 2 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_2_quantity');
                            return ['numeric', "max:$max"];
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_2_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                    TextInput::make('west_bonus_3_quantity')
                        ->validationAttribute('west bonus 3 quantity')
                        ->numeric()
                        ->rules(['numeric', 'max:999999'])
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_3_quantity;
                        })
                        ->label('Quantity'),
                    TextInput::make('west_bonus_3_quantity_value')
                        ->validationAttribute('west bonus 3 quantity value')
                        ->numeric()
                        ->rules(function (Get $get) {
                            $max = $get('west_bonus_3_quantity');
                            return ['numeric', "max:$max"];
                        })
                        ->formatStateUsing(function ($record) {
                            return $record->productData?->west_bonus_3_quantity_value;
                        })
                        ->label('Bonus Qty.'),
                ]),
        ];
    }

    public static function getEastMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType): array
    {
        return [
            Section::make()
                ->heading('East Malaysia')
                ->schema([
                    Group::make()
                        ->schema([
                            Placeholder::make('type'),
                            Placeholder::make('min_qty'),
                            Placeholder::make('max_qty'),
                            Placeholder::make('price')->label('Price per Unit'),
                            Placeholder::make('admin_fees'),
                            Placeholder::make('net_earnings'),
                        ])->columns(6),
                    Group::make()
                        ->schema([
                            Placeholder::make('tier_1')
                                ->label('')
                                ->content(fn() => new HtmlString('<div style="display: flex; align-items: center; margin-top: 10px;">Tier 1</div>')),

                            TextInput::make('east_tier_1_min_quantity')
                                ->numeric()
                                ->validationAttribute('east tier 1 min quantity')
                                ->readOnly()
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'numeric', 'max:999999'];
                                    }
                                    return [];
                                })
                                ->formatStateUsing(function ($record) {
                                    return !empty($record->productData?->east_tier_1_min_quantity) ? $record->productData?->east_tier_1_min_quantity : 1;
                                })
                                ->label(''),
                            TextInput::make('east_tier_1_max_quantity')
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $nextqty = (int) $get('east_tier_1_max_quantity') + 1;
                                    $set('east_tier_2_min_quantity', $nextqty);
                                })
                                ->numeric()
                                ->validationAttribute('east tier 1 max quntity')

                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_1_min_quantity');
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'numeric', "gt:$max"];
                                    }
                                    return ['numeric', "get:$max"];
                                })
                                ->formatStateUsing(function ($record) {
                                    return $record->productData?->east_tier_1_max_quantity;
                                })
                                //     $nextqty = (int) $get('east_tier_1_max_quantity') + 1;
                                //     $set('east_tier_2_min_quantity', $nextqty);
                                // })
                                ->label(''),
                            TextInput::make('east_tier_1_base_price')->label('')
                                ->numeric()
                                ->validationAttribute('east tier 1 base price')
                                ->rules(function (Get $get) {
                                    if ($get('price_type') == 'tier') {
                                        return ['required', 'price', 'numeric'];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_1_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_1_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_1_net_earnings', number_format($netEarnings, 2));
                                    $set('east_tier_1_base_price', $get('east_tier_1_base_price'));
                                })
                                ->live(debounce: 500),
                            Placeholder::make('east_tier_1_admin_fees')
                                ->reactive()
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        // return new HtmlString("<div style='display: flex; align-items: center; margin-top: 0px;'>$finalCommission  %</div>");
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_1_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($record, $finalCommission, $finalCommissionType) {
                                    $basePrice = $get('east_tier_1_base_price') ?? $record->productData?->east_tier_1_base_price;

                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $commission = $finalCommission * $basePrice;
                                        $earning = $basePrice - $commission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),

                            Placeholder::make('tier_2')
                                ->content(fn() => "Tier 2")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->label(""),
                            TextInput::make('east_tier_2_min_quantity')
                                ->numeric()
                                ->validationAttribute('east tier 2 min quantity')
                                ->rules(['numeric', 'max:999999'])
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_min_quantity ?? null;
                                })
                                ->label(''),
                            TextInput::make('east_tier_2_max_quantity')
                                ->numeric()
                                ->validationAttribute('east tier 2 max quantity')
                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_2_min_quantity');
                                    return ['numeric', "gt:$max"];
                                })
                                ->live(onBlur: true)
                                ->afterStateUpdated(function (Get $get, Set $set) {
                                    $nextVal = $get('east_tier_2_max_quantity') + 1;
                                    $set('east_tier_3_min_quantity', $nextVal);
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_max_quantity ?? null;
                                })
                                ->label(''),
                            TextInput::make('east_tier_2_base_price')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $min = $get('east_tier_2_min_quantity');
                                    $max = $get('east_tier_2_max_quantity');
                                    if ($get('price_type') == 'tier' && (isset($min) || isset($max))) {
                                        return ['numeric', "required", "price"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_2_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_2_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_2_net_earnings', number_format($netEarnings, 2));
                                })
                                ->label('')
                                ->live(onBlur: true),
                            Placeholder::make('east_tier_2_admin_fees')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_2_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                    $basePrice = $record->productData?->east_tier_2_base_price ?? $get('east_tier_2_base_price');
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $commission = $finalCommission * $basePrice;
                                        $earning = $basePrice - $commission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),

                            Placeholder::make('tier_3')
                                ->content(fn() => "Tier 3")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->label(""),
                            TextInput::make('east_tier_3_min_quantity')
                                ->numeric()
                                ->rules(function (Get $get) {
                                    $max = $get('east_tier_2_max_quantity');
                                    if ($get('price_type') == 'tier') {
                                        return ['numeric', "required", "gt:$max"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_3_min_quantity ?? null;
                                })
                                ->label(''),
                            Placeholder::make('east_tier_3_max_quantity')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content('and above'),
                            TextInput::make('east_tier_3_base_price')
                                ->numeric()
                                ->validationAttribute('east tier 3 base price')
                                ->rules(function (Get $get) {
                                    $min = $get('east_tier_3_min_quantity');
                                    $max = $get('east_tier_3_max_quantity');
                                    if ($get('price_type') == 'tier' && (isset($min) || isset($max))) {
                                        return ['numeric', "required", "price"];
                                    }
                                    return ['numeric'];
                                })
                                ->formatStateUsing(function () use ($record) {
                                    return $record->productData?->east_tier_3_base_price ?? null;
                                })
                                ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                    if ($finalCommissionType == 'percentage') {
                                        $adminFees = $finalCommission;
                                    } else {
                                        $adminFees = $finalCommission;
                                    }

                                    $set('east_tier_3_admin_fees', $adminFees);

                                    $commission = ($finalCommissionType == 'percentage')
                                        ? ($finalCommission * $state / 100)
                                        : ($finalCommission * $state);

                                    $netEarnings = $state - $commission;
                                    $set('east_tier_3_net_earnings', $netEarnings);
                                })
                                ->label('')
                                ->live()
                                ->debounce('500ms'),
                            Placeholder::make('east_tier_3_admin_fees')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function () use ($finalCommissionType, $finalCommission) {
                                    if ($finalCommissionType == 'percentage') {
                                        return $finalCommission . '%';
                                    }

                                    return 'RM ' . $finalCommission;
                                }),
                            Placeholder::make('east_tier_3_net_earnings')
                                ->label("")
                                ->extraAttributes(['class' => 'mt-3'])
                                ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                    $basePrice = $record->productData?->east_tier_3_base_price ?? $get('east_tier_3_base_price');
                                    if ($finalCommissionType == 'percentage') {
                                        $commission = $finalCommission * $basePrice / 100;
                                        $earning = $basePrice - $commission;
                                    } else {
                                        $commission = $finalCommission * $basePrice;
                                        $earning = $basePrice - $commission;
                                    }

                                    return 'RM ' . number_format($earning, 2);
                                }),
                        ])->columns(6),
                ]),

            self::getWestMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType),
        ];
    }

    public static function getWestMalasiyaTierPriceSchema($record, $finalCommission, $finalCommissionType): Component
    {
        return
            Section::make()
            ->heading('West Malaysia')
            ->schema([
                Group::make()
                    ->schema([
                        Placeholder::make('type'),
                        Placeholder::make('min_qty'),
                        Placeholder::make('max_qty'),
                        Placeholder::make('price')->label('Price per Unit'),
                        Placeholder::make('admin_fees'),
                        Placeholder::make('net_earnings'),
                    ])->columns(6),
                Group::make()
                    ->schema([
                        Placeholder::make('tier_1')
                            ->content(fn() => "Tier 1")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_1_min_quantity')
                            ->numeric()
                            ->validationAttribute('west tier 1 min quantity')
                            ->readOnly()
                            ->rules(['numeric', 'max:999999'])
                            ->formatStateUsing(function () use ($record) {
                                return !empty($record->productData?->west_tier_1_min_quantity) ? $record->productData?->west_tier_1_min_quantity : 1;
                            })
                            ->label(''),
                        TextInput::make('west_tier_1_max_quantity')
                            ->numeric()
                            ->validationAttribute('west tier 1 max quantity')
                            ->rules(function (Get $get) {
                                if ($get('price_type') == 'tier' && $get('west_tier_1_min_quantity') > 0) {
                                    return ['numeric', 'max:999999', 'min:' . $get('west_tier_1_min_quantity')];
                                } elseif ($get('price_type') == 'tier') {
                                    return ['numeric', 'max:999999', 'required'];
                                }
                                return ['numeric', 'max:999999'];
                            })
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set) {
                                $nextVal = $get('west_tier_1_max_quantity') + 1;
                                $set('west_tier_2_min_quantity', $nextVal);
                            })
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_1_max_quantity ?? null;
                            })
                            ->label(''),
                        TextInput::make('west_tier_1_base_price')
                            ->numeric()
                            ->validationAttribute('west tier 1 base price')
                            ->rules(function (Get $get) {
                                if ($get('price_type') == 'tier') {
                                    return ['numeric', 'max:999999', 'price', 'required'];
                                }
                                return ['numeric', 'max:999999'];
                            })
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_1_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }

                                $set('west_tier_1_admin_fees', $adminFees);

                                $commission = ($finalCommissionType == 'percentage')
                                    ? ($finalCommission * $state / 100)
                                    : ($finalCommission * $state);

                                $netEarnings = $state - $commission;
                                $set('west_tier_1_net_earnings', number_format($netEarnings, 2));
                            })
                            ->label('')
                            ->live(onBlur: true),
                        Placeholder::make('west_tier_1_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('east_tier_1_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_1_base_price ?? $get('west_tier_1_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $commission = $finalCommission * $basePrice;
                                    $earning = $basePrice - $commission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),

                        Placeholder::make('tier_2')
                            ->content(fn() => "Tier 2")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_2_min_quantity')
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_min_quantity ?? null;
                            })
                            ->label(''),
                        TextInput::make('west_tier_2_max_quantity')
                            ->live(onBlur: true)
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_max_quantity ?? null;
                            })
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (Get $get, Set $set) {
                                $nextVal = !is_null($get('west_tier_2_max_quantity')) ? $get('west_tier_2_max_quantity') + 1 : null;
                                $set('west_tier_3_min_quantity', $nextVal);
                            })
                            ->label(''),
                        TextInput::make('west_tier_2_base_price')->label('')
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_2_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }
                            })
                            ->live(onBlur: true),
                        Placeholder::make('west_tier_2_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('west_tier_2_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_2_base_price ?? $get('west_tier_2_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $commission = $finalCommission * $basePrice;
                                    $earning = $basePrice - $commission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),

                        Placeholder::make('tier_3')
                            ->content(fn() => "Tier 3")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->label(""),
                        TextInput::make('west_tier_3_min_quantity')
                            ->formatStateUsing(function (Get $get) use ($record) {
                                return !is_null($get('west_tier_2_max_quantity')) ? $get('west_tier_2_max_quantity') + 1 : null ?? $record->productData?->west_tier_3_min_quantity  ?? null;
                            })

                            ->label(''),
                        Placeholder::make('west_tier_3_max_quantity')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content('and above'),
                        TextInput::make('west_tier_3_base_price')->label('')
                            ->reactive()
                            ->formatStateUsing(function () use ($record) {
                                return $record->productData?->west_tier_3_base_price ?? null;
                            })
                            ->afterStateUpdated(function (Set $set, Get $get, $state) use ($finalCommission, $finalCommissionType) {
                                if ($finalCommissionType == 'percentage') {
                                    $adminFees = $finalCommission;
                                } else {
                                    $adminFees = $finalCommission;
                                }
                            })
                            ->live(),
                        Placeholder::make('west_tier_3_admin_fees')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function () use ($finalCommissionType, $finalCommission) {
                                if ($finalCommissionType == 'percentage') {
                                    return $finalCommission . '%';
                                }

                                return 'RM ' . $finalCommission;
                            }),
                        Placeholder::make('west_tier_3_net_earnings')
                            ->label("")
                            ->extraAttributes(['class' => 'mt-3'])
                            ->content(function (Get $get) use ($finalCommissionType, $finalCommission) {
                                $basePrice = $record->productData?->west_tier_3_base_price ?? $get('west_tier_3_base_price');
                                if ($finalCommissionType == 'percentage') {
                                    $commission = $finalCommission * $basePrice / 100;
                                    $earning = $basePrice - $commission;
                                } else {
                                    $commission = $finalCommission * $basePrice;
                                    $earning = $basePrice - $commission;
                                }

                                return 'RM ' . number_format($earning, 2);
                            }),
                    ])->columns(6),
            ]);
    }

    public static function numericValueValidationRule()
    {
        return [
            'x-data' => "{
                        sanitizeInput(event) {
                            let value = event.target.value.replace(/[^\\d.]/g, '');

                            const decimalCount = (value.match(/\\./g) || []).length;
                            if (decimalCount > 1) {
                                const parts = value.split('.');
                                value = parts[0] + '.' + parts.slice(1).join('');
                            }

                            event.target.value = value;
                        }
                    }",
            'x-on:input' => 'sanitizeInput($event)',
        ];
    }

    public static function handleOtherPriceTypes($priceType, $record)
    {
        // Cache Auth::id() to avoid repeated database queries
        $authId = Auth::id();

        $relation = \App\Services\ProductRelationCacheService::getProductRelation($record->id, $authId);

        $nullBonusArr = [
            // 'east_zone_price' => null,
            // 'west_zone_price' => null,
            'east_bonus_1_quantity' => null,
            'east_bonus_1_quantity_value' => null,
            'east_bonus_2_quantity' => null,
            'east_bonus_2_quantity_value' => null,
            'east_bonus_3_quantity' => null,
            'east_bonus_3_quantity_value' => null,

            'west_bonus_1_quantity' => null,
            'west_bonus_1_quantity_value' => null,
            'west_bonus_2_quantity' => null,
            'west_bonus_2_quantity_value' => null,
            'west_bonus_3_quantity' => null,
            'west_bonus_3_quantity_value' => null,
        ];

        $nullTierArr = [
            'east_tier_1_min_quantity' => null,
            'east_tier_1_max_quantity' => null,
            'east_tier_1_base_price' => null,
            'east_tier_2_min_quantity' => null,
            'east_tier_2_max_quantity' => null,
            'east_tier_2_base_price' => null,
            'east_tier_3_min_quantity' => null,
            'east_tier_3_max_quantity' => null,
            'east_tier_3_base_price' => null,
            'west_tier_1_min_quantity' => null,
            'west_tier_1_max_quantity' => null,
            'west_tier_1_base_price' => null,
            'west_tier_2_min_quantity' => null,
            'west_tier_2_max_quantity' => null,
            'west_tier_2_base_price' => null,
            'west_tier_3_min_quantity' => null,
            'west_tier_3_max_quantity' => null,
            'west_tier_3_base_price' => null,
        ];

        $nullFixedArray = [
            'east_zone_price' => null,
            'west_zone_price' => null,
        ];

        if ($priceType == 'fixed') {
            $relation->update(array_merge($nullBonusArr, $nullTierArr));
        }

        if ($priceType == 'bonus') {
            $relation->update($nullTierArr);
        }

        if ($priceType == 'tier') {
            $relation->update(array_merge($nullBonusArr, $nullFixedArray));
        }
    }

    public function boot(): void
    {
        // ... existing code ...

        // Register ProductObserver
        Product::observe(ProductObserver::class);
    }

    // ✅ OPTIMIZED (Efficient)
    public function scopePendingApprovalsForAdmin($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeWithoutRejectedForAdmin($query)
    {
        return $query->where('status', 'approved');
    }

    public function scopeRejectedForAdmin($query)
    {
        return $query->where('status', 'rejected');
    }

    private function getHeaderHeading()
    {
        // Use cached counts instead of live queries
        $tabCounts = ProductTabsCacheService::getTabCounts();

        return view('custom-view.header-action', [
            'title' => 'Product Catalog',
            'headerActions' => $this->getHeaderActions(),
            'isAllProducts' => $this->isAllProducts,
            'isRejectedProducts' => $this->isRejectedProducts,
            'isPending' => $this->isPending,
            'pending' => $tabCounts['pending'], // ✅ Cached count
            'rejected' => $tabCounts['rejected'], // ✅ Add rejected count
            'all' => $tabCounts['all'], // ✅ Add all count
        ]);
    }

    protected function getTableQuery(): ?Builder
    {
        // Base query with optimized eager loading
        $baseQuery = Product::query()->with([
            'category:id,name',
            'subcategory:id,name',
            'brand:id,name',
            'generic:id,name',
            'productData' => function ($query) {
                $query->select('id', 'product_id', 'user_id', 'rejected_reason', 'admin_approval');
            }
        ]);

        if ($this->isRejectedProducts) {
            return $baseQuery->rejectedForAdmin();
        }

        if ($this->isPending) {
            return $baseQuery->pendingApprovalsForAdmin();
        }

        // Default: approved products
        return $baseQuery->withoutRejectedForAdmin();
    }

    public function pendingApprovals()
    {
        $this->isPending = true;
        $this->isAllProducts = false;
        $this->isRejectedProducts = false;
        $this->resetTable(); // More efficient than dispatch
    }

    public function allProducts()
    {
        $this->isPending = false;
        $this->isRejectedProducts = false;
        $this->isAllProducts = true;
        $this->resetTable(); // More efficient than dispatch
    }

    public function rejectedProducts()
    {
        $this->isPending = false;
        $this->isAllProducts = false;
        $this->isRejectedProducts = true;
        $this->resetTable(); // More efficient than dispatch
    }
}
