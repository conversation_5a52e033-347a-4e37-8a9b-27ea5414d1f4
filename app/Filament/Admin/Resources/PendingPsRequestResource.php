<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PendingPsRequestResource\Pages;
use App\Filament\Admin\Resources\PendingPsRequestResource\RelationManagers;
use App\Models\Approval;
use App\Models\PendingPsRequest;
use App\Models\User;
use App\Models\UserAddress;
use Carbon\Carbon;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Actions\ViewAction;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\HtmlString;
use Indianic\Settings\Models\GlobalSettings;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;

class PendingPsRequestResource extends Resource
{
    protected static ?string $model = Approval::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Pending Requests';
    protected static ?string $navigationLabel = 'Pharma Supplier Update Requests';

    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_view ps edit request');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Actions')
            ->query(function (Builder $query) {
                return User::query()
                    ->role('Pharmaceutical Company')
                    ->whereHas('approvals', function ($q) {
                        $q->where('user_type', 'pc')->where('approved_at', null)->where('rejected_at', null)->where('approved_by', null)->where('rejected_by', null);
                    })
                    ->with('pcDetails', 'address.city', 'address.state', 'approvals', 'userAddresses.city', 'userAddresses.state');
            })
            ->columns([
                TextColumn::make('id')->sortable()->searchable()->toggleable(),
                TextColumn::make('name')
                    ->label('Name')
                    ->sortable()
                    ->searchable(
                        query: function (Builder $query, string $search): Builder {
                            return $query->whereHas('pcDetails', function ($q) use ($search) {
                                $q->where('company_name', 'like', "%{$search}%")
                                    ->orWhere('business_name', 'like', "%{$search}%");
                            });
                        }
                    )
                    ->toggleable()
                    ->getStateUsing(function ($record) {
                        // If company_type_id is 1, show business_name, else show company_name
                        if (isset($record->pcDetails->company_type_id) && $record->pcDetails->company_type_id == 1) {
                            return $record->pcDetails->company_name ?? $record->pcDetails->business_name;
                        }
                        return $record->pcDetails->company_name ?? '-';
                    }),
                TextColumn::make('pcDetails.is_restricted')
                    ->searchable(query: function (Builder $query, string $search): Builder {

                        $searchStr = "";
                        if (Str::lower($search) == 'global') {
                            $searchStr = 0;
                        } elseif (Str::lower($search) == 'restricted') {
                            $searchStr = 1;
                        } else {
                            $searchStr = null;
                        }
                        return $query
                            ->whereHas('pcDetails', function (Builder $query) use ($searchStr) {
                                $query->where('is_restricted', $searchStr);
                            });
                    })
                    ->label('Access')
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        return $state ? 'Restricted' : 'Global';
                    })->toggleable(),
                TextColumn::make('userAddresses.city.name')->sortable()->searchable()->toggleable(),
                TextColumn::make('userAddresses.state.name')->sortable()->searchable()->toggleable(),
                TextColumn::make('email')->sortable()->searchable()->toggleable(),

                // TextColumn::make('created_at')
                //     ->label('Registered On')
                //     ->dateTime('M d, Y')->sortable()->searchable()->toggleable(),
                 TextColumn::make('approvals.created_at')
                    ->toggleable()
                    ->label('Requested Date')
                    ->formatStateUsing(function ($record): string {
                        $latestApproval = $record->approvals->sortByDesc('id')->first();
                        if ($latestApproval) {
                            $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                            $convertedDate = \Carbon\Carbon::parse($latestApproval->created_at)->timezone($userTimezone);
                            return $convertedDate->format('M d, Y | h:i A');
                        }
                        return '-';
                    })
                    // ->formatStateUsing(function ($state): string {
                    //     if (empty($state)) {
                    //         return '-';
                    //     }

                    //     $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                    //     $convertedDate = Carbon::parse($state)->timezone($userTimezone);

                    //     return $convertedDate->format('M d, Y | H:i');
                    // })

                    ->searchable(),
            ])
            ->filters([
                SelectFilter::make('user_id')
                    ->label('Name')
                    ->options(function () {
                        // Get all users with role 'Pharmaceutical Company'
                        $users = \App\Models\User::role('Pharmaceutical Company')
                            ->with(['pcDetails' => function ($query) {
                                $query->whereNotNull('company_type_id');
                            }])
                            ->get();

                        $options = [];
                        foreach ($users as $user) {
                            // Check if pcDetails exists to avoid null property access
                            if ($user->pcDetails) {
                                $companyTypeId = $user->pcDetails->company_type_id ?? null;
                                $companyName = $user->pcDetails->company_name ?? null;
                                $businessName = $user->pcDetails->business_name ?? null;

                                // If company_type_id is 1 and company name is available, show company name first, then business name
                                // Always prefer company name, if empty then use business name, else fallback to user name
                                if (isset($user->pcDetails->company_type_id) && $companyTypeId == 1) {
                                    $displayName = $companyName ?: $businessName;
                                } else {
                                    $displayName = $companyName ?: $businessName;
                                }

                                if (!empty($displayName)) {
                                    $options[$user->id] = $displayName;
                                }
                            }
                        }

                        asort($options);

                        return $options;
                    })
                    ->searchable()
                    ->native(false)
                    ->query(function ($query, $data) {
                        if (!empty($data['value'])) {
                            $query->where('id', $data['value']);
                        }
                    }),
                SelectFilter::make('is_restricted')
                    ->options([
                        '0' => 'Global',
                        '1' => 'Restricted',
                    ])
                    ->modifyQueryUsing(function (Builder $query, array $data): Builder {
                        // info($data['value']);
                        if (isset($data['value'])) {
                            $d = $query->whereHas('pcDetails', function (Builder $query) use ($data) {
                                $query->where('is_restricted', $data['value']);
                            });
                            return $d;
                        }
                        return $query;
                    })
                    ->label('Access')
                    ->placeholder('Select Access'),
                    // SelectFilter::make('state')
                    //     ->label('State')
                    //     ->options(function () {
                    //         return State::orderBy('name')
                    //             ->pluck('name', 'id')
                    //             ->map(fn ($name) => $name ?? 'Unknown State')
                    //             ->toArray();
                    //     })
                    //     ->searchable()
                    //     ->preload()
                    //     ->query(function (Builder $query, array $data) {
                    //         if (empty($data['value'])) {
                    //             return;
                    //         }
                    //         $query->whereHas('userAddresses', function ($q) use ($data) {
                    //             $q->where('state_id', $data['value']);
                    //         });
                    //     }),

                    // // City Filter
                    // SelectFilter::make('city')
                    //     ->label('City')
                    //     ->options(function () {
                    //         // Get the selected state from the filter data, not from filament() helper
                    //         $filters = request()->input('tableFilters', []);
                    //         $selectedState = $filters['state']['value'] ?? null;

                    //         if ($selectedState) {
                    //             // If a state is selected, show only cities from that state
                    //             return City::where('state_id', $selectedState)
                    //                 ->orderBy('name')
                    //                 ->pluck('name', 'id')
                    //                 ->map(fn ($name) => $name ?? 'Unknown City')
                    //                 ->toArray();
                    //         }
                    //         // If no state is selected, show all cities
                    //         return City::orderBy('name')
                    //             ->pluck('name', 'id')
                    //             ->map(fn ($name) => $name ?? 'Unknown City')
                    //             ->toArray();
                    //     })
                    //     ->searchable()
                    //     ->preload()
                    //     ->query(function (Builder $query, array $data) {
                    //         if (empty($data['value'])) {
                    //             return;
                    //         }
                    //         $query->whereHas('userAddresses', function ($q) use ($data) {
                    //             $q->where('city_id', $data['value']);
                    //         });
                    //     })

                    // State and City dependent filter
                    Filter::make('location')
                        ->label('Location')
                        ->form([
                            Select::make('state_id')
                                ->label('State')
                                ->searchable()
                                ->getSearchResultsUsing(function (string $search) {
                                    $results = State::where('name', 'like', "%{$search}%")
                                        ->orderBy('name')
                                        ->pluck('name', 'id')
                                        ->toArray();
                                    // If no results found, show "No result found"
                                    if (empty($results)) {
                                        return ['' => 'No result found'];
                                    }
                                    return $results;
                                })
                                ->preload()
                                ->options(fn () => State::orderBy('name')->pluck('name', 'id'))
                                ->reactive()
                                ->afterStateUpdated(fn (callable $set) => $set('city_id', null)),
                            Select::make('city_id')
                                ->label('City')
                                ->searchable()
                                ->getSearchResultsUsing(function (string $search, callable $get) {
                                    $stateId = $get('state_id');
                                    $query = City::query();
                                    if ($stateId) {
                                        $query->where('state_id', $stateId);
                                    }
                                    $results = $query->where('name', 'like', "%{$search}%")
                                        ->orderBy('name')
                                        ->pluck('name', 'id')
                                        ->toArray();
                                    if (empty($results)) {
                                        return ['' => 'No result found'];
                                    }
                                    return $results;
                                })
                                ->preload()
                                ->options(function (callable $get) {
                                    $stateId = $get('state_id');
                                    if ($stateId) {
                                        return City::where('state_id', $stateId)->orderBy('name')->pluck('name', 'id');
                                    }
                                    return City::orderBy('name')->pluck('name', 'id');
                                })
                                ->reactive(),
                        ])
                        ->query(function (Builder $query, array $data): Builder {
                            if (!empty($data['state_id'])) {
                                $query->whereHas('userAddresses', function ($q) use ($data) {
                                    $q->where('state_id', $data['state_id']);
                                });
                            }
                            if (!empty($data['city_id'])) {
                                $query->whereHas('userAddresses', function ($q) use ($data) {
                                    $q->where('city_id', $data['city_id']);
                                });
                            }
                            return $query;
                        })
                        ->indicateUsing(function (array $data): array {
                            $indicators = [];
                            if (!empty($data['state_id'])) {
                                $state = State::find($data['state_id']);
                                $indicators[] = 'State: ' . ($state ? $state->name : 'Unknown');
                            }
                            if (!empty($data['city_id'])) {
                                $city = City::find($data['city_id']);
                                $indicators[] = 'City: ' . ($city ? $city->name : 'Unknown');
                            }
                            return $indicators;
                        })
                    ,
                    Filter::make('requested_date')
                    ->label('Requested Date Range')
                    ->form([
                        DatePicker::make('requested_from')
                            ->label('Requested From Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->reactive(),
                        DatePicker::make('requested_until')
                            ->label('Requested To Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->minDate(fn ($get) => $get('requested_from')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // For PostgreSQL compatibility, avoid MySQL-style backticks and use double quotes.
                        // Also, ensure the correct relationship key (approvalable_id) is used.
                        // We'll filter users by the created_at of their latest approval record.

                        if (!empty($data['requested_from']) || !empty($data['requested_until'])) {
                            // Subquery to get the latest approval id for each user
                            $latestApprovalSub = \DB::table('approvals')
                                ->selectRaw('MAX(id)')
                                ->whereColumn('approvalable_id', 'users.id')
                                ->where('user_type', 'pc');

                            $query->whereHas('approvals', function ($q) use ($data, $latestApprovalSub) {
                                $q->where('user_type', 'pc')
                                  ->whereColumn('approvalable_id', 'users.id')
                                  ->whereIn('id', $latestApprovalSub);

                                if (!empty($data['requested_from'])) {
                                    $q->whereDate('created_at', '>=', $data['requested_from']);
                                }
                                if (!empty($data['requested_until'])) {
                                    $q->whereDate('created_at', '<=', $data['requested_until']);
                                }
                            });
                        }
                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (!$data['requested_from'] && !$data['requested_until']) {
                            return null;
                        }
                        $from = $data['requested_from'] ? Carbon::parse($data['requested_from'])->toFormattedDateString() : '...';
                        $until = $data['requested_until'] ? Carbon::parse($data['requested_until'])->toFormattedDateString() : '...';
                        return "Requested between {$from} and {$until}";
                    }),

            ])->deferFilters()
            ->actions([
                // ActionGroup::make([
                ViewAction::make('view')->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->label('')
                    ->tooltip('View')
                    ->visible(function ($record) {
                        return true;
                    })->url(fn ($record): string => "/users/{$record->id}"),

            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPendingPsRequests::route('/'),
            'create' => Pages\CreatePendingPsRequest::route('/create'),
            'edit' => Pages\EditPendingPsRequest::route('/{record}/edit'),
        ];
    }
}
