<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ImportResource\Pages;
use Filament\Notifications\Notification;
use App\Models\ProductImports;
use Filament\Forms\Components\Actions;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\View;
use Illuminate\Validation\ValidationException;
use League\Csv\Reader;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Filament\Forms\Components\Html;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class ImportResource extends Resource
{
    protected static ?string $model = ProductImports::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-arrow-up';
    protected static ?string $navigationLabel = 'Import Products';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        FileUpload::make('file')
                            ->label('Excel File')
                            ->required()
                            ->disk('s3')
                            ->preserveFilenames()
                            ->directory('imports/temp')
                            ->helperText('Upload the Excel file for the import.')
                            ->acceptedFileTypes([
                                'application/vnd.ms-excel',                // .xls
                                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                                'text/csv',                                // .csv
                            ])
                            ->afterStateUpdated(function (callable $set, $state, callable $get) {
                                if (!$state) return;

                                \Log::info('afterStateUpdated called', [
                                    'state' => $state,
                                    'state_type' => gettype($state),
                                    'state_class' => is_object($state) ? get_class($state) : 'not_object'
                                ]);

                                $tempFile = null;
                                $ext = null;
                                $s3Path = null; // Track S3 path for cleanup
                                $shouldClearFile = false;

                                try {
                                    // Handle different types of $state
                                    if ($state instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                                        // Case 1: We have a TemporaryUploadedFile object
                                        \Log::info('Handling TemporaryUploadedFile object');

                                        $tempFile = $state->getRealPath();
                                        $ext = strtolower(pathinfo($state->getClientOriginalName(), PATHINFO_EXTENSION));

                                        \Log::info('TemporaryUploadedFile details', [
                                            'tempFile' => $tempFile,
                                            'originalName' => $state->getClientOriginalName(),
                                            'extension' => $ext,
                                            'fileExists' => file_exists($tempFile),
                                            'fileSize' => file_exists($tempFile) ? filesize($tempFile) : 'file not found'
                                        ]);
                                    } elseif (is_string($state)) {
                                        // Case 2: We have an S3 path string
                                        \Log::info('Handling S3 path string');

                                        $disk = \Storage::disk('s3');
                                        $s3Path = $state; // Store for cleanup
                                        $ext = strtolower(pathinfo($state, PATHINFO_EXTENSION));

                                        // Download from S3 to temp file for validation
                                        if (!$disk->exists($state)) {
                                            $shouldClearFile = true;
                                            throw new \Exception('File not found in S3: ' . $state);
                                        }

                                        $tempFile = tempnam(sys_get_temp_dir(), 's3_validation_') . '.' . $ext;
                                        $contents = $disk->get($state);
                                        file_put_contents($tempFile, $contents);

                                        \Log::info('S3 file downloaded for validation', [
                                            's3Path' => $state,
                                            'tempFile' => $tempFile,
                                            'extension' => $ext,
                                            'fileSize' => filesize($tempFile)
                                        ]);
                                    } else {
                                        $shouldClearFile = true;
                                        throw new \Exception('Unexpected state type: ' . gettype($state));
                                    }

                                    // Validate extension
                                    if (!in_array($ext, ['xlsx', 'xls', 'csv'])) {
                                        $shouldClearFile = true;
                                        throw new \Exception('Invalid file extension. Only xlsx, xls, and csv files are allowed.');
                                    }

                                    // Validate file exists and is not empty
                                    if (!file_exists($tempFile) || filesize($tempFile) === 0) {
                                        $shouldClearFile = true;
                                        throw new \Exception('File is empty or not accessible');
                                    }

                                    $expectedHeaders = [
                                        'Brand',
                                        'Product Name',
                                        'Generic Name',
                                        'B2C Category',
                                        'B2C Subcategory',
                                        'Distributor',
                                        'Prescription Required',
                                        'Dosage Form',
                                        'Container',
                                        'Volume',
                                        'Volume Unit',
                                        'Weight in gms',
                                        'Product Images',
                                        'Product Description',
                                        'Key Ingredients',
                                        'Storage Instructions',
                                        'Usage/Indication',
                                        'Contraindication',
                                        'How to Use',
                                        'Safety Information/Pregnancy',
                                        'Dosage Information',
                                        'Side Effects'
                                    ];

                                    $actualHeaders = [];

                                    if (in_array($ext, ['xlsx', 'xls'])) {
                                        try {
                                            // Try to identify the file type first
                                            $inputFileType = IOFactory::identify($tempFile);
                                            \Log::info('Excel file type identified', ['fileType' => $inputFileType]);

                                            $reader = IOFactory::createReader($inputFileType);
                                            $spreadsheet = $reader->load($tempFile);
                                            $sheet = $spreadsheet->getActiveSheet();

                                            $headerRow = $sheet->rangeToArray('A1:' . $sheet->getHighestColumn() . '1')[0];
                                            \Log::info('Raw headers from Excel', ['headers' => $headerRow]);

                                            // Clean up headers
                                            $actualHeaders = array_filter(array_map(function ($header) {
                                                return $header !== null ? trim((string)$header) : null;
                                            }, $headerRow), function ($header) {
                                                return $header !== null && $header !== '';
                                            });

                                            $actualHeaders = array_values($actualHeaders);
                                        } catch (\Exception $e) {
                                            $shouldClearFile = true;
                                            throw new \Exception('Unable to read Excel file: ' . $e->getMessage());
                                        }
                                    } elseif ($ext === 'csv') {
                                        try {
                                            $csv = Reader::createFromPath($tempFile, 'r');
                                            $csv->setHeaderOffset(0);
                                            $headerRow = $csv->getHeader();
                                            \Log::info('Raw headers from CSV', ['headers' => $headerRow]);

                                            // Clean up headers
                                            $actualHeaders = array_filter(array_map(function ($header) {
                                                return trim((string)$header);
                                            }, $headerRow), function ($header) {
                                                return $header !== '';
                                            });

                                            $actualHeaders = array_values($actualHeaders);
                                        } catch (\Exception $e) {
                                            $shouldClearFile = true;
                                            throw new \Exception('Unable to read CSV file: ' . $e->getMessage());
                                        }
                                    }

                                    \Log::info('Cleaned headers', [
                                        'actualHeaders' => $actualHeaders,
                                        'count' => count($actualHeaders)
                                    ]);

                                    // Check for missing headers (case-insensitive comparison)
                                    $expectedHeadersNormalized = array_map('trim', $expectedHeaders);
                                    $actualHeadersNormalized = array_map('trim', $actualHeaders);

                                    $missing = [];
                                    foreach ($expectedHeadersNormalized as $expected) {
                                        $found = false;
                                        foreach ($actualHeadersNormalized as $actual) {
                                            if (strcasecmp($expected, $actual) === 0) {
                                                $found = true;
                                                break;
                                            }
                                        }
                                        if (!$found) {
                                            $missing[] = $expected;
                                        }
                                    }

                                    \Log::info('Header validation results', [
                                        'expected_headers' => $expectedHeadersNormalized,
                                        'actual_headers' => $actualHeadersNormalized,
                                        'missing_headers' => $missing
                                    ]);

                                    if (!empty($missing)) {
                                        $shouldClearFile = true;
                                        throw ValidationException::withMessages([
                                            'file' => 'The uploaded file is missing required headers: ' . implode(', ', $missing),
                                        ]);
                                    }

                                    \Log::info('File headers validated successfully');
                                } catch (\Exception $e) {
                                    \Log::error('Header validation error', [
                                        'error' => $e->getMessage(),
                                        'state' => $state,
                                        'shouldClearFile' => $shouldClearFile,
                                        's3Path' => $s3Path,
                                        'line' => $e->getLine()
                                    ]);

                                    // Clear the file input immediately
                                    if ($shouldClearFile) {
                                        \Log::info('Clearing file input due to validation failure');

                                        // Delete from S3 if file was uploaded there
                                        if ($s3Path && \Storage::disk('s3')->exists($s3Path)) {
                                            \Log::info('Deleting invalid file from S3', ['s3Path' => $s3Path]);
                                            \Storage::disk('s3')->delete($s3Path);
                                        }

                                        // Clear the file input with multiple methods to ensure it works
                                        $set('file', null);
                                        $set('file', []);

                                        // Force clear by dispatching an event (if needed)
                                        // $this->dispatch('clearFileInput', 'file');
                                    }

                                    // Show error notification
                                    Notification::make()
                                        ->title('Header Validation Failed')
                                        ->body($e instanceof ValidationException ? collect($e->errors())->flatten()->join(', ') : 'Error: ' . $e->getMessage())
                                        ->danger()
                                        //->persistent()
                                        ->duration(5000)
                                        ->send();

                                    // Prevent further processing
                                    return;
                                } finally {
                                    // Clean up temp file if we created one for S3 validation
                                    if (isset($tempFile) && is_string($state) && file_exists($tempFile)) {
                                        @unlink($tempFile);
                                    }
                                }
                            }),
                        FileUpload::make('zipfile')
                            ->label('ZIP File')
                            ->required()
                            ->disk('s3')
                            ->preserveFilenames()
                            ->directory('imports/temp')
                            // ->storeFileNamesIn('zip_path')
                            ->helperText('Upload the ZIP file containing images.')
                            ->rules(['required', 'file', 'mimes:zip', 'max:20480'])
                            ->validationMessages([
                                'required' => 'The ZIP file is required.',
                                'file' => 'The uploaded item must be a valid file.',
                                'mimes' => 'Only ZIP files are allowed. Please upload a file with .zip extension.',
                                'max' => 'The ZIP file size cannot exceed 20MB. Please upload a smaller file.',
                            ])
                    ])->columns(2),

                Section::make('Last Imported Record')
                    ->schema([
                        View::make('filament.admin.resources.imports.partials.imported-table')
                            ->visible(fn() => ProductImports::latest()->exists())
                            ->columnSpanFull(),
                    ])
                    ->visible(fn() => ProductImports::latest()->exists())
                    ->columnSpanFull(),
                Section::make('Note')
                    ->schema([
                        Placeholder::make('')
                            ->content('The Last Imported section displays details of the most recent import attempt, along with its result (whether it was successful or failed).')
                            ->extraAttributes(['class' => 'text-gray-600 text-sm']),
                    ])
                    ->columns(1)

            ]);
    }



    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')->label('Imported By')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),

                Tables\Columns\TextColumn::make('status')->sortable()
                    ->color(fn($state) => match (strtolower($state)) {
                        'success' => 'success',
                        'failed' => 'danger',
                        'inprogress' => 'warning',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('description')
                    ->label('Description')->formatStateUsing(function ($state) {
                        // Extract success, error, and total from the description string
                        preg_match('/Success Rows: (\d+), errorRows: (\d+), Total: (\d+)/', $state, $matches);

                        $success = $matches[1] ?? 0;
                        $error = $matches[2] ?? 0;
                        $total = $matches[3] ?? 0;

                        // Return HTML with badges
                        return <<<HTML
                    <span class="inline-flex items-center gap-2">
                        <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            Valid: {$success}
                        </span>
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            Invalid: {$error}
                        </span>
                        <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">
                            Total: {$total}
                        </span>
                    </span>
                HTML;
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('audit_file')
                    ->label('Log file')
                    ->sortable()
                    ->icon('heroicon-o-document-arrow-down')
                    ->formatStateUsing(function ($record) {
                        return in_array($record->status, ['CompletedWithError', 'Failed', 'Success'])
                            ? 'Download Log'
                            : 'No Log Available';
                    })
                    ->url(function ($record) {
                        return in_array($record->status, ['CompletedWithError', 'Failed', 'Success'])
                            ? route('audit.download', ['file' => $record->audit_file])
                            : null;
                    })->weight(function ($record) {
                        // Apply bold if file exists, otherwise normal font weight
                        return $record->audit_file ? 'bold' : 'normal';
                    })->color(function ($record) {
                        return $record->audit_file ? 'primary' : '';
                    })->html(),
            ])->defaultSort('created_at', 'desc')
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListImports::route('/list-imports'),
            //'create' => Pages\CreateImport::route('/import-products'),
            'import-products' => Pages\CreateImport::route('/import-products'),
            'download' => Pages\CreateImport::route('/download/{file}'),
            //'edit' => Pages\EditImport::route('/{record}/edit'),
        ];
    }

    public static function getDownloadUrl(string $fileName): string
    {
        $fileName = 'samplefile.xlsx';
        $filePath = storage_path("app/samples/{$fileName}");
        if (!file_exists($filePath)) {
            abort(404, 'File not found.');
        }

        return route('import.sample.download', ['file' => $fileName]);
    }
}
