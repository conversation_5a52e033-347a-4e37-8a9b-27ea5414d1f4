<?php

namespace App\Filament\Admin\Resources\ActivityLogResource\Pages;

use Illuminate\Support\Str;
use Filament\Actions\Action;
use App\Traits\HasBackButton;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Group;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use App\Filament\Admin\Resources\ActivityLogResource;
use App\Models\PcDetail;

class ViewActivityLog extends ViewRecord
{
    protected static string $resource = ActivityLogResource::class;

    protected function getHeaderActions(): array
    {
        $activeTab = session('activity_log_active_tab', 'Admin');
        $indexUrl = ActivityLogResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url($indexUrl),
        ];
    }

    // Map foreign key names to their model classes
    protected $foreignKeyMap = [
        'parent_id' => 'App\Models\Category',
        'Role_id' => 'App\Models\Role',
        'role_id' => 'App\Models\Role',
        'user_id' => 'App\Models\User',
        'category_id' => 'App\Models\Category',
        'clinic_account_type_id' => 'App\Models\ClinicAccountType',
        'product_id' => 'App\Models\Product',
        'company_type_id' => 'App\Models\PcCompanyType',
        'brand_id' => 'App\Models\Brand',
        'redirect_to_id' => null,
        // Add more foreign keys as needed
    ];

    // Map models to their display field
    protected $displayFieldMap = [
        'App\Models\User' => 'name',
        'App\Models\Category' => 'name',
        'App\Models\Role' => 'name',
        'App\Models\Product' => 'name',
        'App\Models\ClinicAccountType' => 'name',
        'company_type_id' => 'App\Models\PcCompanyType',
        'App\Models\Brand' => 'name',
        'App\Models\PcDetail' => 'business_name',
        // Add more model mappings as needed
    ];

    public function getTitle(): string
    {
        return 'Activity Log Details';
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('')
                    ->schema([
                        TextEntry::make('updated_at')
                            ->label('Date')
                            ->dateTime('M d, Y | h:i A'),
                        TextEntry::make('description')
                            ->label('Task')
                            ->formatStateUsing(fn(string $state) => Str::ucfirst(Str::lower($state))),
                        TextEntry::make('causer.name')
                            ->label('Perform Made by')
                            ->getStateUsing(function ($record) {
                                if ($record->causer && $record->causer->id === 1) {
                                    return 'DPharma';
                                }
                                if (!$record->causer) {
                                    return 'Unknown';
                                }
                                $causer = $record->causer;
                                if ($causer->hasRole('Pharmaceutical Company')) {
                                    $pcDetails = PcDetail::where('user_id', $causer->id)->first();
                                    
                                    if ($pcDetails) {
                                        return !empty($pcDetails->company_name) 
                                            ? $pcDetails->company_name 
                                            : ($pcDetails->business_name ?? 'Unknown Company');
                                    }
                                }
                                return $causer->name ?? 'Unknown';
                            }),
                        TextEntry::make('ip_address')
                            ->label('IP Address')
                            ->default('N/A'),
                        // Group::make()
                        // ->columnSpanFull()
                        // ->schema([
                        //     Section::make('Old Status')
                        //     ->schema([
                        //         TextEntry::make('changes_summary')
                        //             ->label('')
                        //             ->getStateUsing(function ($record) {
                        //                 return $this->formatPropertiesForTooltip($record, 'old');;
                        //             })
                        //             ->html(),
                        //     ])
                        //     ->collapsible()
                        //     ->collapsed(true),
                            
                        // // Detailed comparison section
                        // Section::make('New Status')
                        //     ->schema([
                        //         TextEntry::make('detailed_changes')
                        //             ->label('')
                        //             ->getStateUsing(function ($record) {
                        //                 return $this->formatPropertiesForTooltip($record, 'attributes');
                        //             })
                        //             ->html(),
                        //     ])
                        //     ->collapsible()
                        //     ->collapsed(true),  
                        // ])
                        TextEntry::make('properties.old')
                            ->label('Previous Status')
                            ->badge()
                            ->color('warning')
                            ->tooltip(function ($record) {
                                return $this->formatPropertiesForTooltip($record, 'old');
                            })
                            ->getStateUsing(function ($record) {
                                return $this->formatPropertiesForDisplay($record, 'old');
                            }),
                        TextEntry::make('properties.attributes')
                            ->label('Current Status')
                            ->badge()
                            ->color(function ($record) {
                                // Check if any value in the attributes is 'rejected'
                                $attributes = (array)($record->properties['attributes'] ?? []);
                                foreach ($attributes as $value) {
                                    if (is_string($value) && strtolower($value) === 'rejected') {
                                        return 'danger';
                                    }
                                }
                                return 'success';
                            })
                            ->tooltip(function ($record) {
                                return $this->formatPropertiesForTooltip($record, 'attributes');
                            })
                            ->getStateUsing(function ($record) {
                                return $this->formatPropertiesForDisplay($record, 'attributes');
                            }),
                    ])
                    ->columns(3),
            ]);
    }

    /**
     * Get city and state names by their IDs
     */
    protected static function getCityState($stateId, $cityId)
    {
        $stateName = null;
        $cityName = null;

        if ($stateId) {
            $stateName = State::where('id', $stateId)->value('name');
        }

        if ($cityId) {
            $cityName = City::where('id', $cityId)->value('name');
        }

        return [
            'state_name' => $stateName,
            'city_name' => $cityName
        ];
    }

    /**
     * Format properties for display (inline)
     */
    protected function formatPropertiesForDisplay($record, $propertyKey)
    {
        if ($record->event === 'deleted') {
            return '-';
        }

        $formatted = $this->formatProperties($record, $propertyKey);
        return $formatted === '-' ? '-' : $formatted;
    }

    /**
     * Format properties for tooltip (with line breaks)
     */
    protected function formatPropertiesForTooltip($record, $propertyKey)
    {
        if (!isset($record->properties[$propertyKey])) {
            return '-';
        }

        if (!isset($record->properties[$propertyKey])) {
            return '-';
        }

        $data = (array)$record->properties[$propertyKey];
      
        if (empty($data)) {
            return '-';
        }

        // Handle special cases first
        if ($record->log_name === 'threads' && $propertyKey === 'attributes') {
            $senderName = $record->subject->sender->name ?? 'Unknown Sender';
            $receiverName = $record->subject->receiver->name ?? 'Unknown Receiver';
            return "Sender: {$senderName}\nReceiver: {$receiverName}";
        }

        if ($record->log_name === 'order_export' && $propertyKey === 'attributes') {
            return 'Export Date: ' . $record->properties[$propertyKey];
        }

        // Skip unwanted fields for tooltip
        $skipFields = [
            'created_at',
            'updated_at',
            'deleted_at',
            'id',
            'remember_token',
            'remember_me',
            'email_verified_at',
            'token',
            'is_stay_login',
            'last_stay_login_at',
            'is_temp_password',
            'is_admin_verified',
            'guard_name',
            'photo',
            'image',
            'password',
            'admin_verified_by',
            'signature_type',
            'dc_signature'
        ];

        foreach ($skipFields as $field) {
            unset($data[$field]);
        }

        if (empty($data)) {
            return 'N/A';
        }

        // Handle deleted records
        if ($record->event === 'deleted') {
            if (isset($data['deleted_at'])) {
                $deletedDate = \Carbon\Carbon::parse($data['deleted_at'])->format('Y-m-d');
                return 'Deleted on: ' . $deletedDate;
            } else {
                $deletedDate = \Carbon\Carbon::parse($record->updated_at)->format('Y-m-d');
                return 'Deleted on: ' . $deletedDate;
            }
        }

        $result = [];
        foreach ($data as $key => $value) {
            if ($value === null) {
                if ($record->subject_type === 'App\Models\Category' && $key === 'serial_number') {
                    $fieldLabel = $this->getReadableFieldName($key);
                    $result[] = "{$fieldLabel}: 0";
                }
                continue;
            }

            $formattedValue = $this->formatFieldValue($key, $value, $record, $propertyKey);
            $fieldLabel = $this->getReadableFieldName($key);
            $result[] = "{$fieldLabel}: {$formattedValue}";
        }

        return implode("\n", $result);
    }

    protected function formatProperties($record, $propertyKey)
    {
        if (!isset($record->properties[$propertyKey])) {
            return '-';
        }

        if ($record->event === 'deleted') {
            return '-';
        }

        $data = (array)$record->properties[$propertyKey];

        if (empty($data)) {
            return '-';
        }

        // Handle special log types
        if ($record->log_name === 'threads' && $propertyKey === 'attributes') {
            $senderName = $record->subject->sender->name ?? 'Unknown Sender';
            $receiverName = $record->subject->receiver->name ?? 'Unknown Receiver';
            return "Sender: {$senderName} | Receiver: {$receiverName}";
        }

        $attributeValue = $record->properties[$propertyKey];
        if ($record->log_name === 'order_export' && $propertyKey === 'attributes') {
            return 'Export Date: ' . $attributeValue;
        }

        // Skip unwanted fields
        $skipFields = [
            'created_at',
            'updated_at',
            'deleted_at',
            'id',
            'remember_token',
            'remember_me',
            'email_verified_at',
            'token',
            'is_stay_login',
            'last_stay_login_at',
            'is_temp_password',
            'is_admin_verified',
            'guard_name',
            'photo',
            'image',
            'password',

        ];

        foreach ($skipFields as $field) {
            unset($data[$field]);
        }

        if (empty($data)) {
            return 'N/A';
        }

        // Handle deleted records
        if ($record->event === 'deleted') {
            if (isset($data['deleted_at'])) {
                $deletedDate = \Carbon\Carbon::parse($data['deleted_at'])->format('Y-m-d');
                return 'Deleted on: ' . $deletedDate;
            } else {
                $deletedDate = \Carbon\Carbon::parse($record->updated_at)->format('Y-m-d');
                return 'Deleted on: ' . $deletedDate;
            }
        }

        // Handle specific status cases
        if ($record->subject_type === 'App\Models\SupportTicket' && isset($data['status'])) {
            $statusLabel = match ($data['status']) {
                'open', 1, '1' => 'Open',
                'closed', 0, '0' => 'Closed',
                default => Str::title($data['status']),
            };
            return 'Status: ' . $statusLabel;
        }

        if ($record->subject_type === 'App\Models\SubOrder' && isset($data['status'])) {
            $statusLabel = match ($data['status']) {
                'accepted' => 'Accepted',
                'cancelled' => 'Cancelled',
                'delivered' => 'Delivered',
                'in_transit' => 'In Transit',
                'pending' => 'Pending',
                'ready_for_pickup' => 'Ready For Pickup',
                'rejected' => 'Rejected',
                default => Str::title(str_replace('_', ' ', $data['status'])),
            };
            return 'Status: ' . $statusLabel;
        }

        // Handle generic status fields
        if (isset($data['is_active'])) {
            return 'Status: ' . ($data['is_active'] ? 'Active' : 'Inactive');
        }

        if (isset($data['status']) && is_bool($data['status'])) {
            return 'Status: ' . ($data['status'] ? 'Active' : 'Inactive');
        }

        if (isset($data['roles'])) {
            return 'Roles: ' . (is_array($data['roles']) ? implode(', ', $data['roles']) : $data['roles']);
        }

        $result = [];
        foreach ($data as $key => $value) {
            if ($value === null) {
                if ($record->subject_type === 'App\Models\Category' && $key === 'serial_number') {
                    $fieldLabel = $this->getReadableFieldName($key);
                    $result[] = "{$fieldLabel}: 0";
                }
                continue;
            }

            $formattedValue = $this->formatFieldValue($key, $value, $record, $propertyKey);
            $fieldLabel = $this->getReadableFieldName($key);
            $result[] = "{$fieldLabel}: {$formattedValue}";
        }

        return implode(' | ', $result);
    }

    /**
     * Format individual field values
     */
    protected function formatFieldValue($key, $value, $record, $propertyKey)
    {
        // Handle state_id and city_id for contact_details log
        if ($record->log_name && in_array($key, ['state_id', 'city_id'])) {
            $cityState = static::getCityState(
                $key === 'state_id' ? $value : ($record->properties[$propertyKey]['state_id'] ?? null),
                $key === 'city_id' ? $value : ($record->properties[$propertyKey]['city_id'] ?? null)
            );
            return $key === 'state_id' ? ($cityState['state_name'] ?? "#{$value}") : ($cityState['city_name'] ?? "#{$value}");
        }

        // Handle numeric fields that should display without decimals
        $numericFields = ['min_order_value', 'delivery_days', 'delivery_days_west'];
        if (in_array($key, $numericFields)) {
            return (string)(int)$value;
        }
        // Handle redirect_to_id dynamically based on redirect_to
        if ($key === 'redirect_to_id') {
            $redirectTo = $record->properties[$propertyKey]['redirect_to'] ?? null;

            if (!$redirectTo) {
                $otherPropertyKey = $propertyKey === 'attributes' ? 'old' : 'attributes';
                $redirectTo = $record->properties[$otherPropertyKey]['redirect_to'] ?? null;
            }

            if (!$redirectTo && $record->subject) {
                $redirectTo = $record->subject->redirect_to ?? null;
            }

            if ($redirectTo) {
                $modelClass = match ($redirectTo) {
                    'seller' => 'App\Models\PcDetail',
                    'brand' => 'App\Models\Brand',
                    'product' => 'App\Models\Product',
                    'category' => 'App\Models\Category',
                    default => null,
                };

                if ($modelClass && class_exists($modelClass)) {
                    return $this->resolveModelName($modelClass, $value);
                }
            }
            return "#{$value}";
        }

        // Handle foreign keys
        if (isset($this->foreignKeyMap[$key])) {
            $modelClass = $this->foreignKeyMap[$key];

            if (is_string($value) && strpos($value, ',') !== false) {
                $ids = array_map('trim', explode(',', $value));
                $names = [];

                foreach ($ids as $id) {
                    $names[] = $this->resolveModelName($modelClass, $id);
                }

                return implode(', ', $names);
            } else {
                return $this->resolveModelName($modelClass, $value);
            }
        }

        // Handle boolean values
        if (is_bool($value)) {
            return $value ? 'Yes' : 'No';
        }

        // Handle arrays
        if (is_array($value)) {
            // Handle associative arrays (key => value) and indexed arrays
            $isAssoc = function (array $arr) {
                if ([] === $arr) return false;
                return array_keys($arr) !== range(0, count($arr) - 1);
            };

            if ($isAssoc($value)) {
                $parts = [];
                foreach ($value as $k => $v) {
                    if (is_array($v)) {
                        $v = json_encode($v, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    }
                    $parts[] = "{$k}: {$v}";
                }
                return implode(', ', $parts);
            } else {
                // Indexed array
                $parts = [];
                foreach ($value as $v) {
                    if (is_array($v)) {
                        $parts[] = json_encode($v, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                    } else {
                        $parts[] = (string)$v;
                    }
                }
                return implode(', ', $parts);
            }
        }

        if (is_string($value) && preg_match('/^\d{4}-\d{2}-\d{2}( \d{2}:\d{2}(:\d{2})?)?$/', $value)) {
            try {
                return \Carbon\Carbon::parse($value)->format('M d, Y H:i A');
            } catch (\Exception $e) {
                return $value;
            }
        }
        return (string)$value;
    }

    /**
     * Resolve a model name from its ID
     */
    protected function resolveModelName($modelClass, $id)
    {
        try {
            if (!class_exists($modelClass)) {
                return "#{$id}";
            }

            $model = $modelClass::find($id);

            if (!$model) {
                return "#{$id}";
            }

            $displayField = $this->displayFieldMap[$modelClass] ?? 'name';
            $possibleFields = [$displayField, 'full_name', 'username', 'label', 'name', 'title'];

            foreach ($possibleFields as $field) {
                if (isset($model->$field) && !empty($model->$field)) {
                    return $model->$field;
                }
            }

            return "#{$id}";
        } catch (\Exception $e) {
            return "#{$id}";
        }
    }

    /**
     * Convert field name to a more readable format
     */
    protected function getReadableFieldName($key)
    {
        // Handle special cases that need to be checked before removing _id suffix
        $preProcessSpecialCases = [
            'redirect_to_id' => 'Redirect On',
        ];

        if (isset($preProcessSpecialCases[$key])) {
            return $preProcessSpecialCases[$key];
        }

        $key = preg_replace('/_id$/', '', $key);

        // Handle special cases
        $specialCases = [
            'is_active' => 'Status',
            'parent' => 'Parent Category',
            'role' => 'Role',
            'rejected_by' => 'Rejected By',
            'rejected_reason' => 'Rejection Reason',
            'tin_number' => 'TIN Number',
            'sst_number' => 'SST Number',
            'company_type_id' => 'Company Name',
            'person_in_charge_nric' => 'NRIC Number',
            'borang_certificate' => 'Certificate of Registration',
            'mmc_certificate' => 'MMC Registration Certificate for Person In-charge',
            'apc_certificate' => 'Current Annual Practicing Certificate',
            'arc_certificate' => 'Annual Retention Certificate',
            'poison_license' => 'Poison A License',
            'redirect_to' => 'Redirect To',
            'delivery_days' => 'Delivery Days East',
            'person_in_charge_name' => 'Full Name',
            'person_in_charge_nric' => 'NRIC Number',
            'person_in_charge_phone' => 'Mobile Number',
            'person_in_charge_email' => 'Email',
        ];

        if (isset($specialCases[$key])) {
            return $specialCases[$key];
        }

        return Str::title(str_replace('_', ' ', $key));
    }

    public function getBreadcrumbs(): array
    {
        $activeTab = session('activity_log_active_tab', 'Admin');
        $indexUrl = ActivityLogResource::getUrl('index', ['activeTab' => $activeTab]);

        return [
            $indexUrl => 'Activity Logs',
            '#' => 'Activity Log Details',
        ];
    }
}
