<?php

namespace App\Filament\Admin\Resources\ActivityLogResource\Pages;

use App\Filament\Admin\Resources\ActivityLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Models\Activity;

class ListActivityLogs extends ListRecords
{
    protected static string $resource = ActivityLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string
    {
        return 'Activity Logs';
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Activity Logs",
            // 3 => "List",
        ];
    }


    public function getTabs(): array
    {
        return [
            'Admin' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->whereHas('causer', function ($q) {
                    $q->where(function ($query) {
                        $query->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'Super Admin');
                        })->orWhereHas('parent.roles', function ($parentRoleQuery) {
                            $parentRoleQuery->where('name', 'Super Admin');
                        });
                    });
                });
            }),
            'Pharmaceutical Supplier' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->whereHas('causer', function ($q) {
                    $q->where(function ($query) {
                        $query->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', 'Pharmaceutical Company');
                        })->orWhereHas('parent.roles', function ($parentRoleQuery) {
                            $parentRoleQuery->where('name', 'Pharmaceutical Company');
                        });
                    });
                });
            }),
            'Facility' => Tab::make()->modifyQueryUsing(function (Builder $query) {
                $query->whereHas('causer', function ($q) {
                    $q->whereHas('roles', function ($roleQuery) {
                        $roleQuery->where('name', 'Clinic');
                    });
                });
            }),
        ];
    }
}
