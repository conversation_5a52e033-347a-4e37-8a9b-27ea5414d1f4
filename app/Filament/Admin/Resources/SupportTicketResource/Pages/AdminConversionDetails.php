<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use App\Models\Message;
use Livewire\Component;
use App\Models\Messages;
use App\Models\SubOrder;
use App\Models\SupportTicket;
use Filament\Resources\Pages\Page;
use Illuminate\Contracts\View\View;
use App\Models\SupportTicketMessage;
use App\Models\User;
use App\Notifications\SupportNotification;
use Filament\Notifications\Notification;
use Filament\Actions\Action;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Livewire\Features\SupportFileUploads\WithFileUploads;
use Spatie\Permission\Models\Role;

class AdminConversionDetails extends Page implements HasForms
{
    use InteractsWithForms;
    use WithFileUploads;

    protected static string $resource = SupportTicketResource::class;
    protected static string $view = 'filament.admin.pages.conversion-details';
    protected static ?string $title = null;

    public $ticket;
    public $messageText = '';
    public $subOrder;
    public $messages = [];
    public $attachedFiles = [];
    public $perPage = 15;
    public $page = 1;
    public $hasMoreMessages = true;

    public function mount($record)
    {
        $this->ticket = SupportTicket::findOrFail($record);
        $this->subOrder = SubOrder::where('order_id', $this->ticket->order_id)->where('user_id')->first();
        $this->loadMessages();

        auth()->user()->unreadNotifications
            ->filter(fn($n) => isset($n->data['actions'][0]['url']) && preg_match('/\/' . preg_quote($record, '/') . '(\/|$)/', $n->data['actions'][0]['url']))
            ->each->markAsRead();
        //auth()->user()->unreadNotifications()->where('data->body', 'like', '%#' . $record . '%')->update(['read_at' => now()]);

        $this->markMessagesAsRead();
    }

    public function getTitle(): string
    {
        $orderNumber = $this->ticket->order?->order_number ?? '-';

        return 'TKT-' . $this->ticket->id . ' | Order ID: ' . '#' . $orderNumber;
    }

    public function getBreadcrumbs(): array
    {
        $activeTab = session('support_ticket_active_tab', 'all');
        $indexUrl = SupportTicketResource::getUrl('index', ['activeTab' => $activeTab]);
        return [
            '1' => (__('message.support_ticket_received.breadcrumb_support')),
            $indexUrl => (__('message.support_ticket_received.breadcrumb_received_tickets')),
            '2' => (__('message.support_ticket_received.breadcrumb_ticket_details')),
        ];
    }

    public function markMessagesAsRead()
    {
        SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    public function ticket($ticketId)
    {
        $this->ticket = SupportTicket::find($ticketId);
        $this->page = 1;
        $this->messages = [];
        $this->loadMessages();

        $this->markMessagesAsRead();
    }

    public function loadMessages()
    {
        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);
    }

    public function loadMoreMessages()
    {
        if (!$this->hasMoreMessages) {
            return;
        }

        $this->page++;

        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = SupportTicketMessage::where('support_ticket_id', $this->ticket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        // Merge with existing messages in the right order
        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);

        $this->dispatch('messages-loaded');
    }

    public function sendMessage()
    {
        if ($this->ticket->status === 'closed') {
            Notification::make()
                // ->title(__('message.support_ticket_received.closed_send_error_title'))
                ->title(__('message.support_ticket_received.closed_send_error_body'))
                ->danger()
                ->send();
            return;
        }

        if (empty(trim($this->messageText)) && empty($this->attachedFiles)) {
            $this->addError('messageText', __('message.support_ticket_received.empty_message_error'));
            return;
        }

        $validationPassed = $this->validateUploads();

        if (empty($this->messageText) && !$validationPassed) {
            $this->addError('attachedFiles', __('message.support_ticket_received.invalid_file_error'));
            return;
        }

        $message = SupportTicketMessage::create([
            'support_ticket_id' => $this->ticket->id,
            'from_id' => auth()->user()->id,
            'message' => $this->messageText ?? '',
        ]);

        if (!empty($this->attachedFiles)) {
            $folderPath = config('constants.api.media.support_ticket') . $this->ticket->id;
            $mediaData = [];

            foreach ($this->attachedFiles as $key => $file) {
                $extension = $file->getClientOriginalExtension();

                $fileName = hash('sha256', $file->getFilename() . '_' . now()) . '.' . $extension;

                getStorageDisk()->putFileAs($folderPath, $file, $fileName);

                $mediaData[] = [
                    "model_type"            => 'App\Models\SupportTicketMessage',
                    "model_id"              => $message->id,
                    "uuid"                  => Str::uuid()->toString(),
                    "collection_name"       => 'support-ticket-images',
                    "name"                  => $fileName,
                    "file_name"             => $fileName,
                    "mime_type"             => $file->getMimeType(),
                    "disk"                  => config('filesystems.default'),
                    "conversions_disk"      => config('filesystems.default'),
                    "size"                  => $file->getSize(),
                    "manipulations"         => json_encode([]),
                    "custom_properties"     => json_encode([]),
                    "generated_conversions" => json_encode([]),
                    "responsive_images"     => json_encode([]),
                    "order_column"          => $key + 1,
                    "created_at"            => now(),
                    "updated_at"            => now(),
                ];
            }

            if (!empty($mediaData)) {
                \App\Models\Media::insert($mediaData);
            }
        }

        $senderId = $this->ticket->sender_id;
        $receiverId = $this->ticket->receiver_id;
        $sender = User::find($senderId);
        $receiver = User::find($receiverId);

        $senderClinicRole = Role::where('name', 'Clinic')
            ->whereHas('users', function ($query) use ($senderId) {
                $query->where('id', $senderId);
            })->first();

        $receiverClinicRole = Role::where('name', 'Clinic')
            ->whereHas('users', function ($query) use ($receiverId) {
                $query->where('id', $receiverId);
            })->first();

        $hasClinicRole = $senderClinicRole || $receiverClinicRole;

        if ($hasClinicRole) {
            if ($sender && $sender->id !== Auth::user()->id) {
                $sender->notify(new SupportNotification($message, 'new_message', 'pc'));
            }

            if ($receiver && $receiver->id !== Auth::user()->id) {
                $receiver->notify(new SupportNotification($message, 'new_message', 'pc'));
            }

            if ($sender) {
                $senderRole = Role::whereIn('name', ['Pharmaceutical Company', 'Clinic'])
                    ->whereHas('users', function ($query) use ($senderId) {
                        $query->where('id', $senderId);
                    })->first();

                if ($senderRole) {
                    $senderSubUsers = \App\Models\User::where('parent_id', $senderId)
                        ->get()
                        ->filter(function ($user) {
                           return (
                                $user->hasAllPermissions(['dpharma-support_chat', 'dpharma-support_view'])
                                || $user->hasAllPermissions(['facilities-support_chat', 'facilities-support_view'])
                            );
                        });

                    foreach ($senderSubUsers as $subUser) {
                        if ($subUser && $subUser->id !== Auth::user()->id) {
                            $subUser->notify(new SupportNotification($message, 'new_message', 'pc'));
                        }
                    }
                }
            }

            if ($receiver) {
                $receiverRole = Role::whereIn('name', ['Pharmaceutical Company', 'Clinic'])
                    ->whereHas('users', function ($query) use ($receiverId) {
                        $query->where('id', $receiverId);
                    })->first();

                if ($receiverRole) {
                    $receiverSubUsers = \App\Models\User::where('parent_id', $receiverId)
                        ->get()
                        ->filter(function ($user) {
                            return (
                                $user->hasAllPermissions(['dpharma-support_chat', 'dpharma-support_view'])
                                || $user->hasAllPermissions(['facilities-support_chat', 'facilities-support_view'])
                            );
                        });

                    foreach ($receiverSubUsers as $subUser) {
                        if ($subUser && $subUser->id !== Auth::user()->id) {
                            $subUser->notify(new SupportNotification($message, 'new_message', 'pc'));
                        }
                    }
                }
            }
        } else {
            $recipientId = ($senderId == Auth::id()) ? $receiverId : $senderId;
            $recipient = User::find($recipientId);

            if ($recipient && $recipient->id !== Auth::user()->id) {
                $recipient->notify(new SupportNotification($message, 'new_message', 'pc'));
            }

            if ($recipient) {
                $recipientRole = Role::where('name', 'Pharmaceutical Company')
                    ->whereHas('users', function ($query) use ($recipientId) {
                        $query->where('id', $recipientId);
                    })->first();

                if ($recipientRole) {
                    $subUsersWithPermission = \App\Models\User::where('parent_id', $recipientId)
                        ->get()
                        ->filter(function ($user) {
                          return (
                                $user->hasAllPermissions(['dpharma-support_chat', 'dpharma-support_view'])
                                || $user->hasAllPermissions(['facilities-support_chat', 'facilities-support_view'])
                            );
                        });

                    foreach ($subUsersWithPermission as $subUser) {
                        if ($subUser && $subUser->id !== Auth::user()->id) {
                            $subUser->notify(new SupportNotification($message, 'new_message', 'pc'));
                        }
                    }
                }
            }
        }
        $this->messageText = '';
        $this->attachedFiles = [];
        $this->page = 1;
        $this->loadMessages();

        $this->dispatch('message-sent');
    }

    public function validateUploads()
    {
        if (empty($this->attachedFiles)) {
            return true;
        }

        if (count($this->attachedFiles) > 5) {
            $this->addError('attachedFiles', __('message.support_ticket_received.file_upload_limit_error'));
            $this->attachedFiles = array_slice($this->attachedFiles, 0, 5);
            return false;
        }

        $hasInvalidFiles = false;
        $invalidFiles = [];

        foreach ($this->attachedFiles as $key => $file) {
            $mimeType = $file->getMimeType();
            $extension = strtolower($file->getClientOriginalExtension());
            $fileSizeKB = $file->getSize() / 1024;

            $isPdf = in_array($mimeType, ['application/pdf', 'application/x-pdf']) || $extension === 'pdf';
            $isImage = strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png']);
            $sizeLimitKB = 2048; // 2MB

            if (!$isPdf && !$isImage) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            } elseif ($fileSizeKB > $sizeLimitKB) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            }
        }

        if ($hasInvalidFiles) {
            $errorMessage =  __('message.support_ticket_received.file_type_limit_error');
            if (!empty($invalidFiles)) {
                $errorMessage .= ' Invalid files: ' . implode(', ', $invalidFiles);
            }
            $this->addError('attachedFiles', $errorMessage);
            $this->attachedFiles = array_values($this->attachedFiles); // Reindex array
        } else {
            // Clear any previous errors if validation passes
            $this->resetErrorBag('attachedFiles');
        }

        return !$hasInvalidFiles;
    }

    public function updatedAttachedFiles()
    {
        if ($this->ticket->status === 'closed') {
            $this->attachedFiles = [];
            Notification::make()
                // ->title(__('message.support_ticket_received.closed_file_attach_title'))
                ->title(__('message.support_ticket_received.closed_file_attach_body'))
                ->danger()
                ->send();
            return;
        }

        $validationPassed = $this->validateUploads();

        if ($validationPassed && !empty($this->attachedFiles)) {
            $this->resetErrorBag('messageText');
        }
    }

    public function updatedMessageText($value)
    {
        if ($this->ticket->status === 'closed') {
            $this->messageText = '';
            Notification::make()
                // ->title(__('message.support_ticket_received.closed_typing_title'))
                ->title(__('message.support_ticket_received.closed_typing_body'))
                ->danger()
                ->send();
            return;
        }

        // Clear messageText error if the input is non-empty
        if (!empty(trim($value))) {
            $this->resetErrorBag('messageText');
        }
    }

    public function removeFile($index)
    {
        if (isset($this->attachedFiles[$index])) {
            unset($this->attachedFiles[$index]);
            $this->attachedFiles = array_values($this->attachedFiles);
            $this->validateUploads();
        }
    }

    public function markAsClosed()
    {
        $this->ticket->update([
            'status' => 'closed',
            'closed_at' => now(),
        ]);

        try {
            $ticket = \App\Models\SupportTicket::with(['sender', 'receiver'])->find($this->ticket->id);

            if (!$ticket) {
                throw new \Exception("Support ticket not found.");
            }

            $currentUserId = auth()->id();
            $allRecipientsToNotify = collect();

            $pcUsers = \App\Models\User::whereHas('roles', function ($query) {
                $query->where('name', 'Pharmaceutical Company');
            })->whereIn('id', [$ticket->sender_id, $ticket->receiver_id])->get();

            $allRecipientsToNotify = $allRecipientsToNotify->merge($pcUsers);

            if ($ticket->sender) $allRecipientsToNotify->push($ticket->sender);
            if ($ticket->receiver) $allRecipientsToNotify->push($ticket->receiver);

            $pcUserIds = $pcUsers->pluck('id');

            $senderReceiver = collect([$ticket->sender_id, $ticket->receiver_id])->filter();
            $parentUserIds = $pcUserIds->merge($senderReceiver)->unique();

            $subUsersWithPermission = \App\Models\User::whereIn('parent_id', $parentUserIds)
                ->get()
                ->filter(function ($user) {
                    return $user->hasAnyPermission(['dpharma-support_view', 'facilities-support_view']);
                });

            $allRecipientsToNotify = $allRecipientsToNotify
                ->merge($subUsersWithPermission)
                ->unique('id')
                ->filter(fn($user) => $user->id !== $currentUserId && $user->email);

            foreach ($allRecipientsToNotify as $user) {
                $userType = 'pc';

                if ($pcUsers->contains('id', $user->id)) {
                    $userType = 'pc';
                } elseif ($user->id === $ticket->sender_id) {
                    $userType = 'sender';
                } elseif ($user->id === $ticket->receiver_id) {
                    $userType = 'receiver';
                } elseif ($subUsersWithPermission->contains('id', $user->id)) {
                    $userType = 'pc';
                }

                Mail::to($user->email)->send(new \App\Mail\SupportTicketMail($ticket, $userType, $user));

                $user->notify(new \App\Notifications\SupportNotification($ticket, 'ticket_closed', $userType));
            }
        } catch (\Exception $e) {
            \Log::error("Failed to send support ticket closure email: {$e->getMessage()}");
        }

        Notification::make()
            ->title(__('message.support_ticket_received.ticket_closed_title'))
            ->success()
            ->send();
    }

    public function notifyAlreadyClosed()
    {
        Notification::make()
            // ->title(__('message.support_ticket_received.closed_ticket_title'))
            ->title(__('message.support_ticket_received.closed_ticket_body'))
            ->danger()
            ->send();
    }

    public function markAsClosedAction()
    {
        $isAdminCompany = isAdmin();
        if ($isAdminCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-tickets_support ticket mark as closed')) {
        return Action::make('markAsClosed')
            ->action(function () {
                // $this->ticket->refresh();
                if ($this->ticket->status === 'closed') {
                    $this->notifyAlreadyClosed();
                } else {
                    $this->markAsClosed();
                    $this->ticket->refresh();
                }
            })
            ->requiresConfirmation(fn() => $this->ticket->status === 'open')
            ->label('Mark as Closed')
            ->color('primary')
            ->visible(fn() => $this->ticket->status !== 'closed');
        }
    }
}
