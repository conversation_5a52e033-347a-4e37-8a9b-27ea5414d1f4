<?php

namespace App\Filament\Admin\Resources\SupportTicketResource\Pages;

use App\Filament\Admin\Resources\SupportTicketResource;
use Filament\Actions;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListSupportTickets extends ListRecords
{
    protected static string $resource = SupportTicketResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
    public function getTitle(): string
    {
        return __('message.support_ticket_received.breadcrumb_received_tickets');
    }

    public function getBreadcrumbs(): array
    {
        return [
            // '1' => (__('message.support_ticket_received.breadcrumb_support')),
            // $this->getResource()::getUrl("index") => (__('message.support_ticket_received.breadcrumb_received_tickets')),
            // '2' => (__('message.support_ticket_received.breadcrumb_list')),
        ];
    }

   public function getTabs(): array
    {
        $getFormattedRole = function ($roleName) {
            return match (strtolower($roleName)) {
                'pharmaceutical company' => 'Pharmaceutical Supplier',
                'clinic' => 'Facility',
                default => ucfirst($roleName),
            };
        };

        return [
            'all' => ListRecords\Tab::make(__('All')),
            'pharmaceutical_supplier' => ListRecords\Tab::make(__('Pharmaceutical Supplier'))
                ->badge(fn () => $this->getTabBadgeCount('pharmaceutical_supplier'))
                ->query(function (Builder $query) {
                    return $query->whereHas('sender', function ($senderQuery) {
                        $senderQuery->where(function ($q) {
                            $q->whereNull('parent_id')
                              ->whereHas('roles', function ($roleQuery) {
                                  $roleQuery->whereIn('name', ['Pharmaceutical Company']);
                              });
                        })->orWhere(function ($q) {
                            $q->whereNotNull('parent_id')
                              ->whereHas('parent.roles', function ($roleQuery) {
                                  $roleQuery->whereIn('name', ['Pharmaceutical Company']);
                              });
                        });
                    });
                }),
            'facility' => ListRecords\Tab::make(__('Facility'))
                ->badge(fn () => $this->getTabBadgeCount('facility'))
                ->query(function (Builder $query) {
                    return $query->whereHas('sender', function ($senderQuery) {
                        $senderQuery->where(function ($q) {
                            $q->whereNull('parent_id')
                              ->whereHas('roles', function ($roleQuery) {
                                  $roleQuery->whereIn('name', ['Clinic']);
                              });
                        })->orWhere(function ($q) {
                            $q->whereNotNull('parent_id')
                              ->whereHas('parent.roles', function ($roleQuery) {
                                  $roleQuery->whereIn('name', ['Clinic']);
                              });
                        });
                    });
                }),
        ];
    }

     protected function getTabBadgeCount(string $tab): int
    {
        $query = $this->getResource()::getEloquentQuery();
        
        return match ($tab) {
            'pharmaceutical_supplier' => $query->whereHas('sender', function ($senderQuery) {
                $senderQuery->where(function ($q) {
                    $q->whereNull('parent_id')
                      ->whereHas('roles', function ($roleQuery) {
                          $roleQuery->whereIn('name', ['Pharmaceutical Company']);
                      });
                })->orWhere(function ($q) {
                    $q->whereNotNull('parent_id')
                      ->whereHas('parent.roles', function ($roleQuery) {
                          $roleQuery->whereIn('name', ['Pharmaceutical Company']);
                      });
                });
            })->count(),
            'facility' => $query->whereHas('sender', function ($senderQuery) {
                $senderQuery->where(function ($q) {
                    $q->whereNull('parent_id')
                      ->whereHas('roles', function ($roleQuery) {
                          $roleQuery->whereIn('name', ['Clinic']);
                      });
                })->orWhere(function ($q) {
                    $q->whereNotNull('parent_id')
                      ->whereHas('parent.roles', function ($roleQuery) {
                          $roleQuery->whereIn('name', ['Clinic']);
                      });
                });
            })->count(),
            default => 0,
        };
    }
}
