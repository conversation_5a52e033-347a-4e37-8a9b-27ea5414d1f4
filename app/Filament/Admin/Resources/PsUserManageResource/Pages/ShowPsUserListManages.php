<?php

namespace App\Filament\Admin\Resources\PsUserManageResource\Pages;

use App\Models\User;
use App\Filament\Admin\Resources\PsUserManageResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use App\Models\Role;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ShowPsUserListManages extends ViewRecord implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;
    public const STATUS_ACTIVE = 1;

    public const STATUS_INACTIVE = 0;
    protected static string $resource = PsUserManageResource::class;

    protected static string $view = 'filament.admin.resources.ps-user-manage-resource.pages.show-ps-user-list-manages';
    public function getTitle(): string
    {
        return $this->record->name;
    }
    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url('/ps-user-manages') // ← Adjust route if needed
                ->button()
                ->color('gray'),
        ];
    }
    public function table(Table $table): Table
    {
        return $table->recordUrl(fn($record) => PsUserManageResource\Pages\ViewPsUserManage::getUrl(['record' => $record->id]))
            ->query(function () {
                $user = auth()->user();
                $createdBy = $user->id;
                if ($user->hasRole('Super Admin')) {
                    return User::query()->where('parent_id', $this->record->id);
                } elseif ($user->hasRole('Pharmaceutical Company')) {

                    return User::query()->where('parent_id', $this->record->id)->where('created_by', $createdBy);
                } elseif ($user->hasRole('Clinic')) {
                    return User::query()->where('parent_id', $this->record->id)->where('created_by', $createdBy);
                } else {

                    return User::query()->where('parent_id', $this->record->id)->where('created_by', $createdBy);
                }
            })
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable()->toggleable()->sortable(),
                Tables\Columns\TextColumn::make('email')->searchable()->toggleable()->sortable(),
                Tables\Columns\TextColumn::make('createdBy.name')
                    ->label('Created By')
                    ->formatStateUsing(function ($record) {
                        return $record->createdBy ? "{$record->createdBy->name}" : 'N/A';
                    })->searchable()->toggleable()->sortable(),
                Tables\Columns\TextColumn::make('created_at')->searchable()->toggleable()->sortable()->dateTime('M d, Y | h:i A'),
                ToggleColumn::make('is_active')->label('Status')
                    ->toggleable()->sortable()
                    ->getStateUsing(fn($record) => $record->is_active)
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company']))
                    ->afterStateUpdated(function ($state, $record) {
                        $record->update(['is_active' => $state]);
                        Notification::make()
                            ->success()
                            ->title('Status has been updated successfully.')
                            ->send();
                    }),
            ])->defaultSort('id', 'desc')->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->url(fn($record) => PsUserManageResource\Pages\EditPsUserManage::getUrl(['record' => $record->id]))
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company']))
                    ->tooltip(function (Model $record) {
                        return  "Edit";
                    }),
                Tables\Actions\ViewAction::make()
                    ->size('sm')
                    ->url(fn($record) => PsUserManageResource\Pages\ViewPsUserManage::getUrl(['record' => $record->id]))
                    ->iconButton()
                    ->icon('heroicon-o-eye')
                    ->extraAttributes([
                        'class' => 'border-2 rounded-lg text-gray-400',
                        'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',
                    ])->tooltip(function (Model $record) {
                        return  "View";
                    }),
                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title('User Deleted')
                            ->title('The users have been deleted successfully.')
                    )->tooltip(function (Model $record) {
                        return  "Delete";
                    }),
            ])->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ])

            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $records->each->delete();
                        Notification::make()
                            ->success()
                            // ->title('Users Deleted')
                            ->title('The users have been deleted successfully.')
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('active')
                    ->label('Active')
                    ->color('success')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_ACTIVE; // Use is_active instead of status
                                $record->save();
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been activated successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to activate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactive')
                    ->label('Inactive')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_INACTIVE; // Use is_active instead of status
                                $record->save();

                                // Logout the deactivated user from all sessions
                                \App\Filament\Admin\Resources\UserManageResource::logoutUserById($record->id);
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been deactivated and logged out successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to deactivate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
            ]);
    }
}
