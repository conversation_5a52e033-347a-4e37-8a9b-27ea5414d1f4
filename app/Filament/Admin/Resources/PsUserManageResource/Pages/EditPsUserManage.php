<?php

namespace App\Filament\Admin\Resources\PsUserManageResource\Pages;

use App\Filament\Admin\Resources\PsUserManageResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditPsUserManage extends EditRecord
{
    protected static string $resource = PsUserManageResource::class;

    protected function getHeaderActions(): array
    {

        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(fn() => url("ps-user-manages/{$this->record->parent_id}/ps-users"))
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "PS Users",
            $this->getResource()::getUrl('index') => 'PS Users',
            3 => 'Edit PS User',
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        unset($data['role']);
        return $data;
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data['role'] = $this->record->roles->first()?->name;

        return $data;
    }

    // protected function afterSave(): void
    // {
    //     $user = $this->record;
    //     $roleName = $this->data['role'];

    //     if ($roleName) {
    //         $user->syncRoles([$roleName]);
    //     }
    // }

    protected function afterSave(): void
    {
        $user = $this->record;
        $newRoleName = $this->data['role'];
        $oldRoleName = $user->roles->first()?->name;

        if ($newRoleName && $newRoleName !== $oldRoleName) {
            $user->syncRoles([$newRoleName]);

            $currentUser = auth()->user();

            activity()
                ->causedBy(auth()->user())
                ->useLog('user')
                ->performedOn($user)
                ->withProperties([
                    'old' => [
                        'role' => $oldRoleName,
                    ],
                    'attributes' => [
                        'role' => $newRoleName,
                    ],
                ])
                ->log("PS User has been updated");
        }
    }

    protected function getSavedNotification(): ?Notification
    {
        // activity()->log('User has been updated');

        return Notification::make()
            ->success()
            // ->title('User has been updated')
            ->title('The user has been updated successfully.');
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
}
