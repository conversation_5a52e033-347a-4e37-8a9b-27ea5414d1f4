<?php

namespace App\Filament\Admin\Resources\PsUserManageResource\Pages;

use App\Filament\Admin\Resources\PsUserManageResource;
use App\Mail\SendPCUserPasswordMail;
use App\Mail\UserCreatedMail;
use App\Models\User;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Str;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;

class CreatePsUserManage extends CreateRecord
{
    protected static string $resource = PsUserManageResource::class;

    protected string $plainPassword;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Add PS User';
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url('/ps-user-manages'),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "PS Users",
            $this->getResource()::getUrl('index') => 'PS Users',
            3 => 'Add PS User',
        ];
    }

    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Add'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }


    /**
     * Generate a username from the user's name
     */
    protected function generateUsername(string $name): string
    {
        $nameParts = explode(' ', strtolower($name));

        $username = $nameParts[0];
        if (count($nameParts) > 1) {
            $username .= substr($nameParts[count($nameParts) - 1], 0, 1);
        }

        $username .= '_dpharma';

        $baseUsername = $username;
        $counter = 1;

        while (User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        //$data['username'] = $this->generateUsername($data['name']);

        $this->data['role'] = $data['role'];
        // $this->plainPassword = Str::random(10);
        // $data['password'] = bcrypt($this->plainPassword);
        $data['created_by'] = auth()->id();
        unset($data['role']);

        return $data;
    }

    protected function afterCreate(): void
    {
        $user = $this->record;
        $role = $this->data['role'];

        if ($role) {
            $user->assignRole($role);
        }

        // Mail::to($this->record->email)->send(new UserCreatedMail($this->record, 'pc'));
        $this->sendCredential($this->record);

        try {
            Notification::make()
                ->success()
                // ->title('User has been Created Successfully')
                ->title("User has been created successfully.")
                // ->persistent()
                ->send();
            //Mail::to($user->email)->send(new UserCreatedMail($user->username, $this->plainPassword));
        } catch (\Exception $e) {
            Notification::make()
                ->warning()
                // ->title('User has been Created Successfully')
                ->title("User created successfully with username '{$user->username}' but email notification failed to send.")
                ->persistent()
                ->send();
        }
    }

    protected function getCreatedNotification(): ?Notification
    {
        return null;
    }

    public function sendCredential($data)
    {
        $userEmail = $data['email'];
        $plainPassword = generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['id'])->update(['password' => $hashedPassword, 'is_temp_password' => true]);
        $loginUrl = config('app.pc_url') . '/home';
        Mail::to($userEmail)->send(new SendPCUserPasswordMail($plainPassword, $userEmail, $loginUrl, $data));
    }
}
