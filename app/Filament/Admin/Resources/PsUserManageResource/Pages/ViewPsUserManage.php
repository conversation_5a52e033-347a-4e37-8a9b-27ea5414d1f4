<?php

namespace App\Filament\Admin\Resources\PsUserManageResource\Pages;

use App\Filament\Admin\Resources\PsUserManageResource;
use App\Filament\Admin\Resources\RolePermissionResource\Pages\ViewRolePermission;

use Filament\Actions;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Str;
use Filament\Pages\Actions\Action;

class ViewPsUserManage extends ViewRecord
{
    protected static string $resource = PsUserManageResource::class;

    public function getTitle(): string
    {
        return $this->record->name;
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(fn() => url("ps-user-manages/{$this->record->parent_id}/ps-users"))

        ];
    }

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make('Basic Details')
                    ->schema([
                        Grid::make()
                            ->schema([
                                // Left column with large avatar
                                ImageEntry::make('photo')
                                    ->circular()
                                    ->columnSpan(1)
                                    ->default(asset('/images/user-avatar.png')),
                                // Right column with user details in a grid
                                Grid::make()
                                    ->schema([
                                        TextEntry::make('id')
                                            ->label('User ID')
                                            ->formatStateUsing(fn(string $state): string => "#{$state}"),
                                        TextEntry::make('name')
                                            ->label('Full Name')->extraAttributes(['class' => 'break-words'])
                                            ->formatStateUsing(fn($state) => Str::limit($state, 35, '...'))
                                            ->tooltip(fn($record) => $record->name),
                                        TextEntry::make('email')
                                            ->label('Email'),
                                        TextEntry::make('phone')
                                            ->label('Phone Number'),
                                        TextEntry::make('roles.name')
                                            ->label('Role')
                                            ->default('-')
                                            ->url(fn($record) => $record->roles->first() ? ViewRolePermission::getUrl(['record' => $record->roles->first()->id]) : null)
                                            ->formatStateUsing(function ($state) {
                                                if (!$state) {
                                                    return '<span style="color: blue;">-</span>';
                                                }

                                                // Remove leading numbers and dash (e.g., "2-Admin" becomes "Admin")
                                                $cleaned = preg_replace('/^\d+-\s*/', '', $state);

                                                return '<span style="color: blue;">' . e($cleaned) . '</span>';
                                            })
                                            ->html(),
                                        TextEntry::make('parent.name')
                                            ->label('Pharmaceutical Company')
                                            ->default('-'),
                                        ViewEntry::make('is_active')
                                            ->label('Status')

                                            ->view('components.user-toggle-switch') // custom Blade view

                                        // TextEntry::make('is_active')
                                        //     ->label('Status')
                                        //     ->badge()
                                        //     ->default('-')
                                        //     ->formatStateUsing(fn($state) => $state == PsUserManageResource::STATUS_ACTIVE ? 'Active' : 'Inactive')
                                        //     ->color(fn($state) => $state == PsUserManageResource::STATUS_ACTIVE ? 'success' : 'danger'),

                                    ])
                                    ->columns(3)
                                    ->columnSpan(2),
                            ])
                            ->columns(3),
                    ]),
            ]);
    }

    public function getBreadcrumbs(): array
    {
        return [
            0 => 'PS Users',
            $this->getResource()::getUrl('index') => 'PS Users',
            1 => 'PS Users Details',
        ];
    }
    public function toggleStatus($userId, $status)
    {
        $user = \App\Models\User::find($userId);

        if ($user) {
            $user->is_active = $status;
            $user->save();

            // If user is being deactivated, logout from all sessions
            if (!$status) {
                \App\Filament\Admin\Resources\UserManageResource::logoutUserById($userId);
                Notification::make()
                    ->success()
                    ->title('Status Updated')
                    ->body("User has been deactivated and logged out from all sessions.")
                    ->send();
            } else {
                Notification::make()
                    ->success()
                    ->title('Status Updated')
                    ->body("User has been activated successfully.")
                    ->send();
            }
        }
    }
}
