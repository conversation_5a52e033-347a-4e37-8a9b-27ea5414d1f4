<?php

namespace App\Filament\Admin\Resources\PsUserManageResource\Pages;

use App\Filament\Admin\Resources\PsUserManageResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPsUserManages extends ListRecords
{
    protected static string $resource = PsUserManageResource::class;
    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('ps-users_view');
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add PS User'),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "PS Users",
            // $this->getResource()::getUrl('index') => "PS Users",
            // 3 => "List",
        ];
    }

    public function getDeletedNotification()
    {
        //activity()->log('Ps User has been deleted');
    }
}
