<?php

namespace App\Filament\Admin\Resources\DosageFormResource\Pages;

use App\Filament\Admin\Resources\DosageFormResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateDosageForm extends CreateRecord
{
    protected static string $resource = DosageFormResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(DosageFormResource::getUrl()),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Dosage Form",
            3 => "Add Dosage Form",
        ];
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.dosage_foam.title.created'))
            ->title(__('message.dosage_foam.create_success'));
    }

    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getTitle(): string
    {
        return 'Add Dosage Form';
    }
}
