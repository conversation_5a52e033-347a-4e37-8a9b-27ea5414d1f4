<?php

namespace App\Filament\Admin\Resources\DosageFormResource\Pages;

use App\Filament\Admin\Resources\DosageFormResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListDosageForms extends ListRecords
{
    protected static string $resource = DosageFormResource::class;

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // 2 => "Dosage Forms",
            // 3 => "List", //$this->record->name
        ];
    }
    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Dosage Form'),
        ];
    }

    public function getTitle(): string
    {
        return 'Dosage Form'; 
    }
}
