<?php

namespace App\Filament\Admin\Resources\DosageFormResource\Pages;

use App\Filament\Admin\Resources\DosageFormResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;
use Filament\Actions\Action;

class EditDosageForm extends EditRecord
{
    protected static string $resource = DosageFormResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(DosageFormResource::getUrl()),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Dosage Form",
            3 => "Edit Dosage Form", //$this->record->name
        ];
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title(__('message.dosage_foam.title.updated'))
            ->title(__('message.dosage_foam.update_success'));
    }
}
