<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\OrderChatResource\Pages;
use App\Filament\Admin\Resources\OrderChatResource\RelationManagers;
use App\Models\Thread;
use App\Models\ThreadMessage;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\User;

class OrderChatResource extends Resource
{
    protected static ?string $model = Thread::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Support';

    public static function canAccess(): bool
    {
        $user = auth()->user();

        $isPharmaceuticalCompany = isPharmaceuticalCompany();

        return $isPharmaceuticalCompany || auth()->user()->hasRole('Super Admin') ||  $user->can('support-tickets_all orders chat');
    }

    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->query(
                Thread::query()
                    ->select('threads.*')
                    ->leftJoin('thread_messages', 'threads.id', '=', 'thread_messages.thread_id')
                    ->groupBy('threads.id')
                    ->orderByRaw('MAX(thread_messages.created_at) DESC NULLS LAST')
            )
            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\Layout\Stack::make([
                        Tables\Columns\TextColumn::make('receiver.name')
                            ->label('')
                            ->formatStateUsing(function ($record) {

                                $name = $record->sender_id === Auth::id()
                                    ? ($record->receiver ? $record->receiver->name : null)
                                    : ($record->sender ? $record->sender->name : null);

                                if (empty($name) && $record->order?->order_number) {
                                    return 'Order ' . '<span style="color: blue;">#' . $record->order->order_number . '</span>';
                                }

                                if ($record->order?->order_number) {
                                    $orderLink = ($record->order_id &&
                                        (auth()->user()->hasRole('Super Admin') ||
                                            auth()->user()->can('all-orders_view details')))
                                        ? '<a href="' . route('filament.admin.resources.orders.view', ['record' => $record->order_id]) . '" style="color: blue;">#' . $record->order->order_number . '</a>'
                                        : '<span    style="color: blue;">#' . $record->order->order_number . '</span>';
                                    return Str::title($name) . '  |  Order ' . $orderLink;
                                }

                                return Str::title($name);
                            })
                            ->tooltip(function (Thread $record) {
                                if (
                                    !$record->order_id ||
                                    (
                                        !auth()->user()->hasRole('Super Admin') &&
                                        !auth()->user()->can('all-orders_view details')
                                    )
                                ) {
                                    return 'You do not have permission to view this order.';
                                }
                                return null;
                            })
                            ->html()
                            ->searchable(query: function ($query, $search) {
                                $query = $query->where(function ($subQuery) use ($search) {
                                    $subQuery->whereHas('receiver', function ($q) use ($search) {
                                        $q->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%']);
                                    })
                                        ->orWhereHas('sender', function ($q) use ($search) {
                                            $q->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%']);
                                        })
                                        ->orWhereHas('order', function ($q) use ($search) {
                                            $q->where('order_number', 'like', '%' . $search . '%');
                                        });
                                });
                                return $query;
                            })->extraAttributes(['class' => 'font-bold']),
                    ]),
                    Tables\Columns\TextColumn::make('getLatestMessage.created_at')
                        ->label('')
                        ->size(5)
                        ->formatStateUsing(function (Thread $record) {
                            $lastMessage = $record->getMessages()->latest()->first();
                            if (!$lastMessage) {
                                return null;
                            }
                            $userTimezone = auth()->user()->timezone;
                            $messageDate = Carbon::parse($lastMessage->created_at)->timezone($userTimezone);
                            if ($messageDate->isToday()) {
                                return 'Today ' . $messageDate->format('h:iA');;
                            } elseif ($messageDate->isYesterday()) {
                                return 'Yesterday ' . $messageDate->format('h:iA');;
                            } else {
                                return $messageDate->format('d M, Y h:iA');
                            }
                        })
                        ->alignEnd()
                        ->extraAttributes(['class' => 'text-xs text-gray-500']),

                ]),
                Tables\Columns\TextColumn::make('getLatestMessage.message')
                    ->label('')
                    ->limit(20)
                    ->formatStateUsing(function (Thread $record) {
                        $lastMessage = $record->messages()->latest()->first();
                        if ($lastMessage) {
                            return $lastMessage->message;
                        }
                        return 'No messages yet';
                    })
                    ->color('gray'),
                Tables\Columns\TextColumn::make('getLatestMessage.media')
                    ->label('')
                    ->icon(function (?Thread $record) {
                        if (!$record) {
                            return null;
                        }
                        $lastMessage = $record->getMessages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'heroicon-o-photo'; // Icon for images
                        } elseif ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'heroicon-o-document-text'; // Icon for PDFs/files
                        }
                        return null;
                    })
                    ->getStateUsing(function (?Thread $record) {
                        if (!$record) {
                            return null;
                        }

                        $lastMessage = $record->getMessages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'Photo';
                        } elseif ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            $media = $lastMessage->getMedia('support-ticket-images')->first();
                            return $media ? $media->name : null;
                        }
                        return null;
                    })
                    ->hidden(fn(?Thread $record) => !$record || (!$record->messages()->latest()->first()?->hasMedia('support-ticket-images') && !$record->messages()->latest()->first()?->hasMedia('support-ticket-images'))),
                Tables\Columns\TextColumn::make('unreadMessagesCount')
                    ->label('')
                    ->badge()
                    ->color('success')
                    ->state(function (Thread $record) {
                        // if ($record->status !== 'open') {
                        //     return null;
                        // }
                        $unreadCount = ThreadMessage::where('thread_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->alignEnd(),
            ])
            ->actions([
                Tables\Actions\Action::make('select')
                    ->label('')
                    ->action(function (Thread $record, $livewire) {
                        $livewire->selectTicket($record->id);
                    })
            ])

            ->filters([
                Tables\Filters\SelectFilter::make('read_status')
                    ->label('Message Status')
                    ->options([
                        'unread' => 'Has Unread',
                        'read' => 'All Read',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        if ($data['value'] === 'unread') {
                            return $query->whereHas('messages', function ($messageQuery) {
                                $messageQuery->where('is_read', false)
                                    ->where('from_id', '!=', Auth::id());
                            });
                        } elseif ($data['value'] === 'read') {
                            return $query->whereDoesntHave('messages', function ($messageQuery) {
                                $messageQuery->where('is_read', false)
                                    ->where('from_id', '!=', Auth::id());
                            });
                        }

                        return $query;
                    }),
                Tables\Filters\SelectFilter::make('user')
                    ->label('User')
                    ->options(function () {
                        $userIds = Thread::query()
                            ->selectRaw('DISTINCT COALESCE(sender_id, receiver_id) as user_id')
                            ->whereNotNull('sender_id')
                            ->whereNotNull('receiver_id')
                            ->where('sender_id', '!=', Auth::id())
                            ->where('receiver_id', '!=', Auth::id())
                            ->orWhere(function ($query) {
                                $query->where('sender_id', '!=', Auth::id())
                                    ->where('receiver_id', Auth::id())
                                    ->orWhere('sender_id', Auth::id())
                                    ->where('receiver_id', '!=', Auth::id());
                            })->pluck('user_id')->filter()->values();

                        return User::whereIn('id', $userIds)
                            ->orderBy('name', 'asc')
                            ->pluck('name', 'id')
                            ->mapWithKeys(function ($name, $id) {
                                return [$id => $name ?? 'Unnamed User #' . $id];
                            })->toArray();
                    })
                    ->preload()
                    ->searchable()
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'] ?? null,
                            fn($query, $userId) => $query->where(function ($query) use ($userId) {
                                $query->where('sender_id', $userId)->orWhere('receiver_id', $userId)
                                    ->where(function ($query) {
                                        $query->where('sender_id', Auth::id())->orWhere('receiver_id', Auth::id());
                                    });
                            })
                        );
                    }),

                Tables\Filters\SelectFilter::make('order_number')
                    ->label('Order Number')
                    ->options(function () {
                        $usedOrderIds = \App\Models\Thread::query()
                            ->whereNotNull('order_id')
                            ->pluck('order_id')
                            ->unique()
                            ->toArray();

                        return \App\Models\Order::query()
                            ->whereNotNull('order_number')
                            ->whereIn('id', $usedOrderIds)
                            ->pluck('order_number', 'order_number')
                            ->mapWithKeys(fn($num) => [$num => "#{$num}"])
                            ->toArray();
                    })
                    ->preload()
                    ->searchable()
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        $usedOrderIds = \App\Models\Thread::query()
                            ->whereNotNull('order_id')
                            ->pluck('order_id')
                            ->unique()
                            ->toArray();

                        $order = \App\Models\Order::query()
                            ->where('order_number', $data['value'])
                            ->whereIn('id', $usedOrderIds)
                            ->first();

                        if (!$order) {
                            return $query->whereRaw('1=0');
                        }

                        return $query->whereHas('order', function ($orderQuery) use ($data) {
                            $orderQuery->where('order_number', $data['value']);
                        });
                    }),
            ])
            ->recordAction('select');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrderChats::route('/'),
        ];
    }
}
