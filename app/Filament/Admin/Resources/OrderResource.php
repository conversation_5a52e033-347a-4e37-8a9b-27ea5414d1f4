<?php

namespace App\Filament\Admin\Resources;

use Filament\Forms;
use App\Models\User;
use Filament\Tables;
use App\Models\Order;
use App\Models\SubOrder;
use App\Models\ClinicReferredCode;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Arr;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Actions\ActionGroup;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\Filter;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\View;
use App\Filament\Exports\OrderExporter;
use Filament\Forms\Components\Textarea;
use Filament\Infolists\Components\Card;
use Filament\Infolists\Components\Grid;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Infolists\Components\Group;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\ExportAction;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Filament\Infolists\Components\Actions;
use Filament\Infolists\Components\Actions\Action as NewAction;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ViewEntry;
use Filament\Infolists\Components\RepeatableEntry;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Admin\Resources\OrderResource\Pages;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use App\Filament\Admin\Resources\OrderResource\RelationManagers;
use App\Mail\PCActionMail\RejectOrderMail;
use Carbon\Carbon;
use App\Models\ClinicDetail;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Filament\Infolists\Components\Actions\Action as InfolistAction;
use Filament\Navigation\NavigationItem;
use Illuminate\Support\Facades\DB;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\Indicator;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Indianic\EmailTemplate\Models\EmailTemplate;
use App\Service\EcommerceService;
use Illuminate\Support\Facades\Log;
use App\Models\Transaction;
use App\Models\ClinicCreditHistory;
use App\Models\PcDetail;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Orders Management';
    protected static ?string $navigationLabel = 'All Orders';
    public static function getNavigationItems(): array
    {
        // Check if the query string contains 'type=credit_line'
        $isCreditLineActive = request()->query('type') === 'credit_line';
        // Define default navigation items
        $navigationItems = [
            NavigationItem::make('All Orders')
                ->visible(fn() => auth()->user()->hasRole('Super Admin') || auth()->user()->can('orders_view'))
                ->url(route('filament.admin.resources.orders.index'))
                ->isActiveWhen(
                    fn(): bool =>
                    request()->routeIs('filament.admin.resources.orders.*') && !$isCreditLineActive
                )
                // ->isActiveWhen(fn(): bool => !$isCreditLineActive)
                ->group('Orders Management'),
        ];

        // Add 'Credit Line Orders' navigation item
        $navigationItems[] = NavigationItem::make('Credit Line Orders')
            ->visible(fn() => auth()->user()->hasRole('Super Admin') || auth()->user()->can('credit_line_orders_view'))
            //->hidden(true)
            ->url(route('filament.admin.resources.credit-line-orders.index', ['type' => 'credit_line']))
            ->isActiveWhen(fn(): bool => $isCreditLineActive)
            ->group('Orders Management');
        //dd($navigationItems);
        return $navigationItems;
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $relationship = match (session('queryType')) {
            'credit_line' => 'subOrderTypeCreditLine',
            default => 'subOrder',
        };
        return $infolist
            ->schema(function ($record) use ($relationship) {
                // dd($record);
                return [

                    Grid::make()

                        ->schema([

                            Section::make('Facility Details')
                                ->heading(function ($record) use ($relationship) {

                                    if ($relationship === 'subOrderTypeCreditLine') {
                                        $earnPoints =  $record->subOrderTypeCreditLine()->sum('earn_points') ?? 0;
                                    } else {
                                        $earnPoints = $record->subOrder()->sum('earn_points') ?? 0;
                                    }

                                    return new HtmlString('<div class="flex items-center justify-between w-full">Facility Details <span class="px-2 py-1 text-sm font-medium border rounded bg-primary-50 border-primary-100 text-primary-600">' . $earnPoints . ' Points earned</span></div>');
                                })

                                ->schema([
                                    Group::make()->schema([
                                        TextEntry::make('order_number')->label('Order ID'),
                                        TextEntry::make('user.clinicData.clinic_name')
                                            ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">' . ucfirst($state) . '</span>' : '<span style="color: blue;">-</span>')->html()

                                            ->url(route('filament.admin.resources.clinics.view', ['record' => $record->user->id]))
                                            ->label('Facility Name'),
                                        TextEntry::make('user.email')->label('Email Address'),
                                        TextEntry::make('created_at')->label('Order Date')->date('M d, Y'),
                                        TextEntry::make('amount')->label('Order Total')->prefix('RM ')->formatStateUsing(fn($state) => number_format($state, 2)),
                                        TextEntry::make('points')
                                            ->label('Earnpoint Total')
                                            ->formatStateUsing(function ($state, $record) use ($relationship) {
                                                if ($relationship === 'subOrderTypeCreditLine') {
                                                    return $record->subOrderTypeCreditLine()->sum('earn_points') ?? 0;
                                                } else {
                                                    return $record->subOrder()->sum('earn_points') ?? 0;
                                                }
                                            }),
                                        TextEntry::make('orderProducts')
                                            ->formatStateUsing(function ($state, $record) use ($relationship) {
                                                if ($relationship == 'subOrderTypeCreditLine') {
                                                    return $record->subOrderTypeCreditLine()->withCount('orderProducts')->get()->sum('order_products_count');
                                                } else {
                                                    return $record->subOrder()->withCount('orderProducts')->get()->sum('order_products_count');
                                                }
                                            })
                                            ->label('Total Items'),
                                        TextEntry::make('orderProducts')
                                            ->label('Admin Fee')
                                            ->formatStateUsing(function ($state, $record) {
                                                $totalCommission = $record->orderProducts->sum('total_commission');
                                                return $totalCommission > 0
                                                    ? 'RM ' . number_format($totalCommission, 2)
                                                    : '-';
                                            }),

                                        TextEntry::make('subOrder')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->subOrder()->count();
                                            })
                                            ->label('Number Of Pharmaceutical Suppliers'),
                                        // TextEntry::make('trancationDetail.transaction_id')->default('-')->label('Transaction ID')
                                        //     ->formatStateUsing(function ($state, $record) {
                                        //         return $record->trancationDetail->transaction_id ?? '-';
                                        //     }),
                                        // TextEntry::make('trancationDetail.status')
                                        //     ->label('Transaction Status')
                                        //     ->default('-')
                                        //     ->formatStateUsing(function ($state, $record) {
                                        //         return ucfirst($record->trancationDetail?->status ?? '-');
                                        //     })

                                    ])->columns(3),
                                    Group::make()->schema([
                                        // TextEntry::make('status')->label('Order Item Status')
                                        //     ->formatStateUsing(function ($state, $record) use ($relationship) {

                                        //         if ($relationship == 'subOrderTypeCreditLine') {

                                        //             $orderRecords = $record->subOrderTypeCreditLine()->with('orderProducts')->get();

                                        //             $orderProducts = $orderRecords->pluck('orderProducts')->flatten();
                                        //         } else {
                                        //             $orderRecords = $record->subOrder()->with('orderProducts')->get();
                                        //             $orderProducts = $orderRecords->pluck('orderProducts')->flatten();
                                        //         }

                                        //         $statusCounts = $orderProducts
                                        //             ->groupBy(fn($product) => \App\Models\OrderProduct::STATUS_DETAIL[$product->status] ?? 'Unknown')
                                        //             ->map(fn($products) => $products->count());
                                        //         $statusCounts = $statusCounts->toArray(); // Convert to array

                                        //         // Define the status colors for different statuses
                                        //         $statuses = [
                                        //             'Confirmed' => ['bg' => '#e4fbe4', 'text' => '#006400'],  // Green
                                        //             'Accepted' => ['bg' => '#e4fbe4', 'text' => '#006400'],   // Green
                                        //             'Pending' => ['bg' => '#f3e3c6', 'text' => '#FF8C00'],    // Yellow
                                        //             'Cancelled' => ['bg' => '#f8d9d4', 'text' => '#B22222'],  // Red
                                        //             'Rejected' => ['bg' => '#f8d9d4', 'text' => '#B22222'],   // Red
                                        //         ];

                                        //         // Initialize the HTML output with a div to wrap the status labels
                                        //         $output = '<div class="flex flex-wrap gap-3">';

                                        //         // Loop through the statuses and generate the badge for each one
                                        //         foreach ($statuses as $status => $colors) {
                                        //             // If this status is present in the statusCounts
                                        //             if (array_key_exists($status, $statusCounts)) {
                                        //                 $count = $statusCounts[$status];

                                        //                 // Add the badge to the output, using dynamic background and text colors with inline styles
                                        //                 $output .= sprintf(
                                        //                     '<span class="text-sm font-medium border border-%s-300 leading-5 py-1 px-2 rounded-md" style="background-color: %s; color: %s;">%s (%d)</span>',
                                        //                     ltrim($colors['bg'], '#'),  // Border color (remove leading '#' for Tailwind compatibility)
                                        //                     $colors['bg'],             // Background color (inline style)
                                        //                     $colors['text'],           // Text color (inline style)
                                        //                     $status,                   // Status label
                                        //                     $count                     // Product count
                                        //                 );
                                        //             }
                                        //         }
                                        //         return $output;
                                        //     }),
                                        TextEntry::make('orderProducts')
                                            ->formatStateUsing(function ($state, $record) use ($relationship) {
                                                if ($relationship == 'subOrderTypeCreditLine') {
                                                    return $record->subOrderTypeCreditLine()->withCount('orderProducts')->get()->sum('order_products_count');
                                                } else {
                                                    return $record->subOrder()->withCount('orderProducts')->get()->sum('order_products_count');
                                                }
                                            })
                                            ->label('Total Items'),
                                        // TextEntry::make('orderProducts')
                                        //     ->label('Admin Fee')
                                        //     ->formatStateUsing(function ($state, $record) {
                                        //         $totalCommission = $record->orderProducts->sum('total_commission');
                                        //         return $totalCommission > 0
                                        //             ? 'RM ' . number_format($totalCommission, 2)
                                        //             : '-';
                                        //     }),

                                        // TextEntry::make('subOrder')
                                        //     ->formatStateUsing(function ($state, $record) {
                                        //         return $record->subOrder()->count();
                                        //     })
                                        //     ->label('Pharmaceutical Suppliers'),
                                        TextEntry::make('trancationDetail.transaction_id')->default('-')->label('Transaction ID')
                                            ->formatStateUsing(function ($state, $record) {
                                                return $record->trancationDetail->transaction_id ?? '-';
                                            }),
                                        TextEntry::make('trancationDetail.status')
                                            ->label('Transaction Status')
                                            ->default('-')
                                            ->formatStateUsing(function ($state, $record) {
                                                if ($record->trancationDetail?->status == '') {
                                                    return '-';
                                                }
                                                $status = strtolower($record->trancationDetail?->status ?? '-');

                                                $label = ucfirst($status);

                                                // Define color classes for each status
                                                $colors = [
                                                    'success'    => ['bg' => '#D1FAE5', 'text' => '#065F46'], // green
                                                    'failed'     => ['bg' => '#f8d9d4', 'text' => '#B22222'],
                                                    'pending' => ['bg' => '#f3e3c6', 'text' => '#FF8C00'],
                                                    'authorised' => ['bg' => '#DBEAFE', 'text' => '#1D4ED8'], // blue
                                                    '-'          => ['bg' => '#F3F4F6', 'text' => '#6B7280'], // gray
                                                ];

                                                $color = $colors[$status] ?? $colors['-'];

                                                return sprintf(
                                                    '<span class="text-sm font-medium border border-%s-300 leading-5 py-1 px-2 rounded-md" style="background-color: %s; color: %s;">%s</span>',
                                                    ltrim($color['bg'], '#'),  // Border color (remove leading '#' for Tailwind compatibility)
                                                    $color['bg'],             // Background color (inline style)
                                                    $color['text'],           // Text color (inline style)
                                                    $label,                   // Status label

                                                );
                                            })
                                            ->html()

                                    ])->columns(3),
                                    Group::make()->schema([
                                        TextEntry::make('status')->label('Order Status')
                                            ->formatStateUsing(function ($state, $record) use ($relationship) {

                                                if ($relationship == 'subOrderTypeCreditLine') {

                                                    $orderRecords = $record->subOrderTypeCreditLine()->with('orderProducts')->get();

                                                    $orderProducts = $orderRecords->pluck('orderProducts')->flatten();
                                                } else {
                                                    $orderRecords = $record->subOrder()->with('orderProducts')->get();
                                                    $orderProducts = $orderRecords->pluck('orderProducts')->flatten();
                                                }

                                                $statusCounts = $orderProducts
                                                    ->groupBy(fn($product) => \App\Models\OrderProduct::STATUS_DETAIL[$product->status] ?? 'Unknown')
                                                    ->map(fn($products) => $products->count());
                                                $statusCounts = $statusCounts->toArray(); // Convert to array
                                                // dd($statusCounts);
                                                // Define the status colors for different statuses
                                                $statuses = [
                                                    'Confirmed' => [
                                                        'bg' => config('constants.order_status.bg_color.delivered', '#CCE5CC'),
                                                        'text' => config('constants.order_status.color.delivered', '#006600')
                                                    ],
                                                    'Delivered' => [
                                                        'bg' => config('constants.order_status.bg_color.delivered', '#CCE5CC'),
                                                        'text' => config('constants.order_status.color.delivered', '#006600')
                                                    ],
                                                    'In-Transit' => [
                                                        'bg' => '#CCE5FF',
                                                        'text' => '#0066CC'
                                                    ],

                                                    'Accepted' => [
                                                        'bg' => config('constants.order_status.bg_color.accepted', '#E6CCFF'),
                                                        'text' => config('constants.order_status.color.accepted', '#6600CC')
                                                    ],
                                                    'Ready For Pickup' => [
                                                        'bg' => config('constants.order_status.bg_color.ready_for_pickup', '#D3D3D3'),
                                                        'text' => config('constants.order_status.color.ready_for_pickup', '#A9A9A9')
                                                    ],
                                                    'Pending' => [
                                                        'bg' => config('constants.order_status.bg_color.pending', '#FFE4CC'),
                                                        'text' => config('constants.order_status.color.pending', '#CC5500')
                                                    ],
                                                    'Cancelled' => [
                                                        'bg' => config('constants.order_status.bg_color.cancelled', '#FFCCCC'),
                                                        'text' => config('constants.order_status.color.cancelled', '#CC0000')
                                                    ],
                                                    'Rejected' => [
                                                        'bg' => config('constants.order_status.bg_color.rejected', '#FFCCCC'),
                                                        'text' => config('constants.order_status.color.rejected', '#CC0000')
                                                    ],
                                                ];

                                                // Initialize the HTML output with a div to wrap the status labels
                                                $output = '<div class="flex flex-wrap gap-3">';

                                                // Loop through the statuses and generate the badge for each one
                                                foreach ($statuses as $status => $colors) {
                                                    // If this status is present in the statusCounts
                                                    if (array_key_exists($status, $statusCounts)) {
                                                        $count = $statusCounts[$status];

                                                        // Add the badge to the output, using dynamic background and text colors with inline styles
                                                        $output .= sprintf(
                                                            '<span class="text-sm font-medium border border-%s-300 leading-5 py-1 px-2 rounded-md" style="background-color: %s; color: %s;">%s (%d)</span>',
                                                            ltrim($colors['bg'], '#'),  // Border color (remove leading '#' for Tailwind compatibility)
                                                            $colors['bg'],             // Background color (inline style)
                                                            $colors['text'],           // Text color (inline style)
                                                            $status,                   // Status label
                                                            $count                     // Product count
                                                        );
                                                    }
                                                }

                                                // Close the div tag
                                                $output .= '</div>';

                                                // Return the HTML string as the formatted output
                                                return $output;
                                            })
                                            ->extraAttributes(['class' => 'custom-status-display'])
                                            ->columnSpan('full')  // Makes the field span the full width
                                            ->html()


                                    ])->columns(2),
                                ])->columnSpan(2),

                            Group::make()->schema(
                                [

                                    Section::make('Billing Address')->schema([
                                        TextEntry::make('billing_address_1')->label(false)->formatStateUsing(function ($state, $record) {
                                            return implode(
                                                ', ',
                                                array_filter([
                                                    $record->billing_first_name ?? '',
                                                    $record->billing_last_name ?? '',
                                                    $record->billing_address_1 ?? '',
                                                    $record->billing_address_2 ?? '',
                                                    optional($record->billingCity)->name ?? '',
                                                    optional($record->billingState)->name ?? '',
                                                    $record->billing_postal_code ?? '',
                                                    optional($record->billingCountry)->name ?? '',
                                                    !empty($record->billing_phone_code)
                                                        ? '(' . $record->billing_phone_code . ') ' . $record->billing_phone_number
                                                        : $record->billing_phone_number,
                                                ])
                                            );
                                        })
                                    ])->columnSpan(1),
                                    Section::make('Shipping Address')->schema([
                                        TextEntry::make('shipping_address_1')->label(false)->formatStateUsing(function ($state, $record) {

                                            return implode(
                                                ', ',
                                                array_filter([
                                                    $record->shipping_first_name ?? '',
                                                    $record->shipping_last_name ?? '',
                                                    $record->shipping_address_1 ?? '',
                                                    $record->shipping_address_2 ?? '',
                                                    optional($record->shippingCity)->name ?? '',
                                                    optional($record->shippingState)->name ?? '',
                                                    $record->shipping_postal_code ?? '',
                                                    optional($record->shippingCountry)->name ?? '',
                                                    !empty($record->shipping_phone_code)
                                                        ? '(' . $record->shipping_phone_code . ') ' . $record->shipping_phone_number
                                                        : $record->shipping_phone_number,
                                                ])

                                            );
                                        })

                                    ])->columnSpan(1)
                                ]
                            )->columns(1)
                        ])->columns(3),

                    RepeatableEntry::make($relationship)
                        ->label('')
                        ->schema([
                            Section::make()
                                // ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">' . ucfirst($state) . '</span>' : '<span style="color: blue;">-</span>')->html()
                                // ->url(route('filament.admin.resources.users.view', ['record' => $record->user->id]))
                                ->heading(function ($record) {
                                    $record->load('user');
                                    $statusLabel = match ($record->status) {
                                        'accepted' => 'Accepted',
                                        'cancelled' => 'Cancelled',
                                        'delivered' => 'Delivered',
                                        'in_transit' => 'In Transit',
                                        'pending' => 'Pending',
                                        'ready_for_pickup' => 'Ready For Pickup',
                                        'rejected' => 'Rejected',
                                        default => Str::title(str_replace('_', ' ', $record->status)),
                                    };
                                    $labels = explode(',', $statusLabel); // Assuming status is a comma-separated string
                                    // if($labels['0'] != 'Pending'){
                                    //     dd($labels);
                                    // }


                                    // Generate the badges dynamically
                                    $statusHtml = collect($labels)->map(function ($label) {
                                        $backgroundColor = match (strtolower($label)) {
                                            'pending' => '#FFE4CC',
                                            'accepted' => '#E6CCFF',
                                            'rejected' => '#FFCCCC',
                                            'delivered' => '#e4fbe4',
                                            'cancelled' => '#FFCCCC',
                                            'Cancelled' => '#FFCCCC',
                                            'approved' => '#e4fbe4',
                                            'in transit' => '#CCE5FF',
                                            default => '#D3D3D3',
                                        };

                                        $textColor = match (strtolower($label)) {
                                            'pending' => '#CC5500',
                                            'accepted' => '#6600CC',
                                            'rejected' => '#CC0000',
                                            'delivered' => '#006400',
                                            'cancelled' => '#CC0000',
                                            'approved' => '#006400',
                                            'in transit' => '#0066CC',
                                            default => '#A9A9A9',
                                        };

                                        return '<span class="inline-flex items-center justify-center px-3 py-1 text-sm font-medium leading-5 text-white rounded-md" style="background-color: ' . $backgroundColor . '; color: ' . $textColor . ';">' . ucfirst($label) . '</span>';
                                    })->implode(' ');




                                    // Return the HTML for the badges and the user's name
                                    return new HtmlString('<a href="' . route('filament.admin.resources.users.view', ['record' => $record->user->id]) . '">' . ucfirst(pcCompanyName($record->user->pcDetails)) . ' - ' . ($record->status != "" ? $statusHtml : 'Pending') . '</a>');
                                })
                                // ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">' . ucfirst($state) . '</span>' : '<span style="color: blue;">-</span>')->html()

                                //         ->url(route('filament.admin.resources.users.view', ['record' => $record->user->id]))

                                ->icon(function ($record) {
                                    return Storage::disk('s3')->url('users/' . $record->user->photo);
                                })
                                ->extraAttributes(['class' => 'rounded-full'])
                                ->headerActions([
                                    // InfolistAction::make('approve')
                                    //     ->label('Approve')
                                    //     ->visible(function ($record) {
                                    //         return $record->status == 'pending';
                                    //     })
                                    //     ->requiresConfirmation()
                                    //     ->action(function ($record,  array $data) {
                                    //         DB::beginTransaction();
                                    //         try {
                                    //             OrderResource::getApprovedOrderTotal($record);
                                    //             // Handle approve action
                                    //             $record->update([
                                    //                 'approved_at' => now(),
                                    //                 'status' => 'accepted',
                                    //             ]);

                                    //             $record->orderProducts()->update([
                                    //                 'status' => 'accepted',
                                    //             ]);
                                    //             OrderResource::updateOrderStatus($record->order_id);
                                    //             OrderResource::payoutOrder($record);
                                    //             $earnPoint =  $record->earn_points ?? 0;
                                    //             DB::table('dpharma_points')->insert([
                                    //                 'user_id'          => $record->order->user_id,
                                    //                 'description'      => 'Order Approved - Points Successfully Rewarded',
                                    //                 'points'           => $earnPoint, // Adjust points logic as needed
                                    //                 'redeem'           => null,
                                    //                 'balance'          => DB::raw("(
                                    //                     COALESCE(
                                    //                         (SELECT balance FROM dpharma_points
                                    //                          WHERE user_id = {$record->order->user_id}
                                    //                          ORDER BY created_at DESC LIMIT 1),
                                    //                         0
                                    //                     ) + {$earnPoint}
                                    //                 )"),
                                    //                 'created_at'       => now(),
                                    //                 'updated_at'       => now(),
                                    //                 'reference_id'     => $record->id,
                                    //                 'reference_value'  => 'suborder',
                                    //             ]);

                                    //             DB::commit();
                                    //             Notification::make()
                                    //                 // ->title('Pharmaceutical Supplier Order Approved')
                                    //                 ->title('The Order has been approved successfully.')
                                    //                 ->success()
                                    //                 ->send();
                                    //         } catch (\Exception $e) {
                                    //             DB::rollBack();
                                    //             Notification::make()
                                    //                 // ->title('Approval Failed')
                                    //                 ->title('An error occurred while approving the order. Please try again.')
                                    //                 ->danger()
                                    //                 ->send();
                                    //         }
                                    //     }),
                                    // InfolistAction::make('download_invoice')
                                    //     ->label('Download Invoice')
                                    //     ->icon('heroicon-o-arrow-down-tray')
                                    //     // ->tooltip('Download Invoice')
                                    //     ->action(function ($record) {
                                    //         $user = $record->user;

                                    //         $fileName = $record->invoice_path;
                                    //         $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                    //         $disk = Storage::disk('s3');
                                    //         $filePath = $invoicePath . $fileName;

                                    //         if ($fileName && $disk->exists($filePath)) {

                                    //             //Activity Log Start
                                    //             // activity()
                                    //             //     ->causedBy(auth()->user())
                                    //             //     ->performedOn($record)
                                    //             //     ->useLog('invoice_download')
                                    //             //     ->log(
                                    //             //         $record->order->order_number
                                    //             //             ? "Invoice for order #{$record->order->order_number} downloaded successfully"
                                    //             //             : "Invoice downloaded successfully"
                                    //             //     );
                                    //             //Activity Log End

                                    //             return new StreamedResponse(function () use ($disk, $filePath) {
                                    //                 echo $disk->get($filePath);
                                    //             }, 200, [
                                    //                 'Content-Type' => 'application/pdf',
                                    //                 'Content-Disposition' => 'attachment; filename="'.$fileName.'"',
                                    //             ]);
                                    //         }

                                    //         //Activity Log Start
                                    //         // activity()
                                    //         //     ->causedBy(auth()->user())
                                    //         //     ->performedOn($record)
                                    //         //     ->useLog('invoice_download')
                                    //         //     ->withProperties([
                                    //         //         'attributes' => array_filter([
                                    //         //             'status' => 'failed',
                                    //         //             'reason' => 'File not found or unavailable',
                                    //         //         ], fn($value) => !is_null($value)),
                                    //         //     ])
                                    //         //     ->log(
                                    //         //         $record->order->order_number
                                    //         //             ? "Failed to download invoice for order #{$record->order->order_number} due to file not found"
                                    //         //             : "Failed to download invoice due to file not found"
                                    //         //     );
                                    //         //Activity Log End

                                    //         Notification::make()
                                    //             ->title('File not found')
                                    //             ->body('The requested invoice file is unavailable or could not be located.')
                                    //             ->danger()
                                    //             ->send();

                                    //         return null;
                                    //     }),
                InfolistAction::make('downloadInvoice')
                ->tooltip('Download Invoice')
                ->icon('heroicon-o-arrow-down-tray')
                ->size('sm')
                ->iconButton()
                ->extraAttributes([
                    'class' => 'border-2 border-success rounded-lg text-blue-900',
                    'style' => 'margin-left: inherit; border-color: rgb(0, 70, 104);'
                ])
                ->action(function ($record) {
                    $media = $record->getMedia('invoices')->last();

                    if ($media) {
                        try {
                            $url = $media->getTemporaryUrl(now()->addMinutes(5));
                            $fileName = $media->file_name;

                            //Activitylog start
                            activity()
                                ->causedBy(auth()->user())
                                ->useLog('invoice_download')
                                ->performedOn($record)
                                ->log("Invoice has been downloaded for Order #{$record->order->order_number}");
                            //Activitylog end

                            return response()->streamDownload(function () use ($url) {
                                echo file_get_contents($url);
                            }, $fileName);
                        } catch (\Exception $e) {
                            Log::error('Error creating invoice download URL', [
                                'media_id' => $media->id,
                                'error' => $e->getMessage(),
                            ]);
                            Notification::make()
                                ->title('Error')
                                ->body('Unable to download invoice. Please try again later.')
                                ->danger()
                                ->send();
                            return null;
                        }
                    }

                    Notification::make()
                        ->title('Error')
                        ->body('No invoice found.')
                        ->danger()
                        ->send();
                    return null;
                })
                ->visible(function ($record) {
                    $user = auth()->user();
                    $conditionStatus = $record->status === 'accepted' && $record->getMedia('invoices')->isNotEmpty();
                    return $conditionStatus && ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download invoice'));
                })
                ->label(false)
                ->color('primary'),

                                        InfolistAction::make('downloadPo')
                                        ->visible(function () {
                                            $user = auth()->user();
                                            return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download po');
                                        })
                                        ->tooltip(function ($record) {
                                            $fileName = $record->invoice_path;
                                            $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                            $disk = Storage::disk('s3');
                                            $filePath = $invoicePath . $fileName;

                                            if ($fileName && $disk->exists($filePath)) {
                                                return 'Download PO';
                                            }
                                            return 'Purchase Order is being generated. Please check back shortly.';
                                        })
                                        ->label('')
                                        ->icon('heroicon-o-document-arrow-down')->size('sm')->iconButton()
                                        ->extraAttributes(function ($record) {
                                            $fileName = $record->invoice_path;
                                            $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                            $disk = Storage::disk('s3');
                                            $filePath = $invoicePath . $fileName;

                                            $iconColor =  'rgb(0, 70, 104)';
                                            $borderColor = 'rgb(0, 70, 104)';
                                            $textColor =  'rgb(0, 70, 104)';
                                            if ($fileName && $disk->exists($filePath)) {
                                                $disabled = false;
                                            } else {
                                                $disabled = true;
                                            }

                                            // dd($disk->exists($filePath));
                                            return [
                                                'class' => "border-2 rounded-lg {$textColor}" . ($disabled ? ' cursor-not-allowed opacity-50' : ''),
                                                'style' => "margin-left: inherit; border-color: {$borderColor}; color: {$iconColor};",
                                                // 'disabled' => $disabled ? 'disabled' : null,
                                                'aria-disabled' => $disabled ? 'true' : 'false',
                                                'tabindex' => $disabled ? '-1' : '0',
                                            ];
                                        })
                                        ->action(function ($record) {
                                            $user = auth()->user();
                                            $fileName = $record->invoice_path;
                                            $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                            $disk = Storage::disk('s3');
                                            $filePath = $invoicePath . $fileName;

                                            // If file does not exist, do nothing (button is disabled)
                                            if (!($fileName && $disk->exists($filePath))) {
                                                return null;
                                            }

                                            if (!($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('all-orders_download po'))) {
                                                abort(403);
                                            }

                                            //Activitylog start
                                            activity()
                                                ->causedBy(auth()->user())
                                                ->useLog('order_po_download')
                                                ->performedOn($record)
                                                ->log("PO downloaded for Order #{$record->order->order_number}");
                                            //Activitylog end

                                            return new StreamedResponse(function () use ($disk, $filePath) {
                                                echo $disk->get($filePath);
                                            }, 200, [
                                                'Content-Type' => 'application/pdf',
                                                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                                            ]);
                                        })
                                        ->color('primary'),
                                    // InfolistAction::make('download_po')
                                    // ->label('Download PO')
                                    // ->icon('heroicon-o-arrow-down-tray')
                                    // ->action(function ($record) {
                                    // $user = $record->user;

                                    // $fileName = $record->invoice_path;
                                    // $invoicePath = config('constants.api.order_invoices.supplier_invoice');
                                    // $disk = Storage::disk('s3');
                                    // $filePath = $invoicePath . $fileName;

                                    // if ($fileName && $disk->exists($filePath)) {
                                    //     return new StreamedResponse(function () use ($disk, $filePath) {
                                    //         echo $disk->get($filePath);
                                    //     }, 200, [
                                    //         'Content-Type' => 'application/pdf',
                                    //         'Content-Disposition' => 'attachment; filename="orderInvoice.pdf"',
                                    //     ]);
                                    // }
                                    //     Notification::make()
                                    //         ->title('File not found')
                                    //         ->body('The requested PO invoice file is unavailable or could not be located.')
                                    //         ->danger()
                                    //         ->send();

                                    //     return null;
                                    // }),
                                    InfolistAction::make('reject')
                                        ->label('Reject')
                                        ->modalHeading('Reason for Order Rejection')

                                        ->modalSubmitActionLabel('Save')
                                        ->visible(function ($record) {
                                            $pending =  $record->status == 'pending';
                                            $visible = false;
                                            if ($record->payment_type == 'pay_now' || $record->payment_type == 'pay_later') {
                                                if ($record->order->payment_status == 'paid') {
                                                    $visible = true;
                                                }
                                            } else {
                                                $visible = true;
                                            }
                                            $isPharmaceucticalCompany = isPharmaceuticalCompany();
                                            return $visible && $pending && ($isPharmaceucticalCompany || auth()->user()->hasRole('Super Admin') || auth()->user()->can('all-orders_reject order'));
                                        })
                                        ->form([
                                            Textarea::make('reason')
                                                ->label(false)
                                                ->validationMessages([
                                                    'required' => "The reason field is required.",
                                                ])
                                                ->label(new HtmlString("Reason<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                                ->rules(['required'])
                                                ->columnSpanFull(),

                                        ])
                                        ->color('danger')
                                        ->modalSubmitAction(
                                            fn(\Filament\Actions\StaticAction $action) =>
                                            $action->color('primary')
                                        )

                                        ->action(function ($record, array $data) {

                                            DB::beginTransaction();
                                            try {

                                                // $order = Order::with('user')->find($record->order_id);
                                                $order = Order::with(['orderProductsGrouped' => function ($query) {
                                                    $query->select('order_id', 'status', DB::raw('COUNT(*) as count'))
                                                        ->groupBy('order_id', 'status');
                                                }, 'subOrder', 'subOrder.user', 'orderProducts', 'orderProducts.product', 'user'])->find($record->order_id);


                                                // Use relationships if available to avoid multiple queries
                                                $pcUser = User::find($record->user_id);
                                                $pcEmail = $pcUser?->email;
                                                $facilityUser = $order?->user;
                                                $facilityEmail = $facilityUser?->email;
                                                $oldStatus = $record->status;
                                                $record->update([
                                                    'rejected_note' => $data['reason'],
                                                    'rejected_at' => now(),
                                                    'status' => 'rejected',
                                                ]);

                                                $returnPoints = 0;
                                                $totalRefundAmt = 0;
                                                if ($record->payment_type != 'credit_line') {
                                                    $returnPoints += $record->total_dpharma_points_used;
                                                    $totalRefundAmt += $record->total_amount;


                                                    if ((count($record->order->subOrder) == 1) && $totalRefundAmt > 0 && $record->order->ecommerce_tran_id && $record->order->payment_status == 'paid') {
                                                        $refundRes = (new EcommerceService())->refundPayment($record->order->ecommerce_tran_id);
                                                        Log::error(
                                                            'refund process response from controller',
                                                            [
                                                                'refundRes' => $refundRes,
                                                                'order_id' => $record->id,
                                                            ]
                                                        );
                                                        if (isset($refundRes['result'])) {
                                                            $refundResult =  $refundRes['result'];
                                                            $refundStatus = $refundResult['refundStatus'];


                                                            if ($refundStatus == 11) {

                                                                Transaction::create([
                                                                    "transaction_id" => $refundResult['transactionNumber'],
                                                                    "order_id" => $record->order->id,
                                                                    "sender_id" => 1, //for admin
                                                                    "sub_order_id" => $record->id,
                                                                    "payment_method" => 'CREDIT',
                                                                    "amount" => $refundResult['amount'],
                                                                    "status" => 'success',
                                                                    "order_status" => 'cancelled',
                                                                    "ecommerce_status" => (int) $refundStatus,
                                                                    "meta_data" => json_encode($refundResult),
                                                                ]);
                                                            }
                                                        }
                                                        // else {
                                                        //     $isRefund = false;
                                                        //     throw new \Exception('Failed to get refund process.');
                                                        // }
                                                    }
                                                }
                                                if ($record->payment_type == 'credit_line') {
                                                    $lastClinicCredit = ClinicCreditHistory::where('supplier_id', $record->user_id)->where('facility_id', (int)$record->order->user_id)->orderBy('id', 'desc')->first();

                                                    if (!empty($lastClinicCredit)) {

                                                        $remainingAmount = 0;
                                                        if ($lastClinicCredit->remaining_amount == 0) {
                                                            $remainingAmount = $record->total_amount + $lastClinicCredit->total_credit_amount;
                                                            $remainingAmount = $remainingAmount - $lastClinicCredit->order_credit_used;
                                                        } else {
                                                            $remainingAmount = $lastClinicCredit->remaining_amount + $record->total_amount;
                                                        }

                                                        $clinicCredit = new ClinicCreditHistory();
                                                        $clinicCredit->facility_id = $record->order->user_id;
                                                        $clinicCredit->supplier_id = $record->user_id;
                                                        $clinicCredit->credit_amount = $record->total_amount;
                                                        $clinicCredit->debit_amount = 0;
                                                        $clinicCredit->edit_credit = 0;
                                                        $clinicCredit->order_credit_used = $lastClinicCredit->order_credit_used - $record->total_amount;
                                                        $clinicCredit->total_credit_amount = $lastClinicCredit->total_credit_amount;
                                                        $clinicCredit->remaining_amount = $remainingAmount;
                                                        $clinicCredit->reference_id = $record->id;
                                                        $clinicCredit->reference_value = 'suborder';
                                                        $clinicCredit->action = 'Order Rejected';
                                                        $clinicCredit->save();
                                                    }
                                                }

                                                if($order->subOrder[0]?->user->timezone){
                                                    $timezone = $order->subOrder[0]?->user->timezone;
                                                    $orderDate = Carbon::parse($record->created_at)->timezone($timezone)->format('d M, Y h:i A');
                                                    $rejectedDate = Carbon::parse($order->subOrder[0]?->rejected_at)->timezone($timezone)->format('d M, Y h:i A');
                                                }else{
                                                    $orderDate = Carbon::parse($order->created_at)->format('d-m-Y H:i:s');
                                                    $rejectedDate = Carbon::parse($order->rejected_at)->format('d-m-Y H:i:s');
                                                }


                                                $paymentType = match ($record->payment_type) {
                                                    'pay_now'    => 'Pay Now',
                                                    'pay_later'  => 'Pay Later',
                                                    'credit_line' => 'Credit Line',
                                                    default      => 'Unknown',
                                                };
                                                $orderDetails = [
                                                    'Order Number'      => "#" . $record->order->order_number,
                                                    'Order Date'    => $orderDate ?? Carbon::parse($order->created_at)->format('d-m-Y H:i:s'),
                                                    'Total Items'   => $record->orderProducts->count(),
                                                    'Supplier'   => pcCompanyName($pcUser->pcDetails) ?? 'N/A',
                                                    'Order Total'   => "RM " . number_format($record->total_amount ?? 0, 2),
                                                    'Payment Type'  => $paymentType ?? 'N/A',
                                                    'Rejected Date' => $rejectedDate ? Carbon::parse($order->rejected_at)->format('d-m-Y H:i:s') : 'N/A',
                                                ];
                                                if ($pcEmail) {
                                                    Mail::to($pcEmail)->send(new RejectOrderMail($record, pcCompanyName($pcUser->pcDetails), $orderDetails));
                                                }
                                                // Send mail to Facility
                                                if ($facilityEmail) {
                                                    Mail::to($facilityEmail)->send(new RejectOrderMail($record, $facilityUser?->name, $orderDetails));
                                                }

                                                $record->orderProducts()->update([
                                                    'status' => 'rejected',
                                                ]);
                                                OrderResource::updateOrderStatus($record->order_id);
                                                if ($record->total_dpharma_points_used > 0) {
                                                    DB::table('dpharma_points')->insert([
                                                        'user_id'          => $record->order->user_id,
                                                        'description'      => 'Order Rejected - Points Successfully Returned',
                                                        'points'           => $record->total_dpharma_points_used, // Adjust points logic as needed
                                                        'redeem'           => null,
                                                        'balance' => DB::raw("(
                                                            COALESCE(
                                                                (SELECT balance FROM dpharma_points
                                                                 WHERE user_id = {$record->order->user_id}
                                                                 ORDER BY created_at DESC LIMIT 1),
                                                                0
                                                            ) + {$record->total_dpharma_points_used}
                                                        )"),
                                                        'created_at'       => now(),
                                                        'updated_at'       => now(),
                                                        'reference_id'     => $record->id,
                                                        'reference_value'  => 'suborder',
                                                    ]);
                                                }

                                                // Activity Log Start
                                                activity()
                                                    ->causedBy(auth()->user())
                                                    ->useLog('order_status_update')
                                                    ->performedOn(auth()->user())
                                                    ->withProperties([
                                                        'old' => [
                                                            'status' => 'pending',
                                                        ],
                                                        'attributes' => [
                                                            'status' => 'rejected',
                                                        ],
                                                    ])
                                                    ->log("Order #{$record->order->order_number} status has been Rejected");
                                                //Activitylog end

                                                DB::commit();
                                                // Optionally, provide feedback to the user
                                                Notification::make()
                                                    // ->title('Pharmacuetical Supplier Order Rejected')
                                                    ->title('The rejected reason has been saved successfully.')
                                                    ->success()
                                                    ->send();

                                                //send mail from here for pc and facility both with reason

                                            } catch (\Exception $e) {
                                                DB::rollBack();
                                                Notification::make()
                                                    // ->title('Approval Failed')
                                                    ->title('An error occurred while approving the order. Please try again.')
                                                    ->danger()
                                                    ->send();
                                            }
                                        }),

                                ])
                                ->schema([
                                    Group::make()->schema(
                                        function ($record) {
                                            return [
                                                Group::make()->schema([
                                                    TextEntry::make('po_number')->label('P.O. Number'),
                                                    TextEntry::make('warehouse_type')->label('ETA')
                                                        ->formatStateUsing(function ($state, $record) {
                                                            if ($record->warehouse_type == 'owned') {
                                                                if(empty($record->delivery_days)){
                                                                    return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->pcDetail->delivery_days)->format('M d, Y') : '-';
                                                                }
                                                                return $record->order->created_at ? Carbon::parse($record->order->created_at)->addDays((int) $record->delivery_days)->format('M d, Y') : '-';
                                                            }
                                                            return '-';
                                                        }),
                                                    TextEntry::make('invoice_po_number')->label('Shipping Method')
                                                        ->default('-')
                                                        ->formatStateUsing(function ($state, $record) {
                                                            return ($record->warehouse_type == 'owned') ? 'Own Logistic' : 'Dpharma Logistic';
                                                        }),
                                                    TextEntry::make('earn_points')->label('Total Points Earned')->default('-'),
                                                    TextEntry::make('payment_type')
                                                        ->label('Order Type')
                                                        ->formatStateUsing(function ($state) {
                                                            return match ($state) {
                                                                'pay_now'     => 'Pay Now',
                                                                'credit_line' => 'Credit Line',
                                                                'pay_later'   => 'Pay Later',
                                                                default       => ucfirst(str_replace('_', ' ', $state)),
                                                            };
                                                        }),
                                                    TextEntry::make('approved_at')
                                                        ->label('Accepeted At')
                                                        ->visible(function ($state) {
                                                            return !empty($state) ? true : false;
                                                        })
                                                        ->formatStateUsing(function ($record) {
                                                            if(!empty($record->pcDetail->user->timezone)){
                                                                $timezone = $record->pcDetail->user->timezone;
                                                                $time = Carbon::parse($record->approved_at)->timezone($timezone);
                                                            }else{
                                                                $time = Carbon::parse($record->approved_at);
                                                            }
                                                            return  $time->format('d M, Y h:i A');
                                                        }),
                                                    TextEntry::make('ready_for_pickup_at')
                                                        ->label('Ready For Pickup At')
                                                        ->visible(function ($state) {
                                                            return !empty($state) ? true : false;
                                                        })
                                                        ->formatStateUsing(function ($record) {
                                                            if(!empty($record->pcDetail->user->timezone)){
                                                                $timezone = $record->pcDetail->user->timezone;
                                                                $time = Carbon::parse($record->ready_for_pickup_at)->timezone($timezone);
                                                            }else{
                                                                $time = Carbon::parse($record->ready_for_pickup_at);
                                                            }

                                                            return  $time->format('d M, Y h:i A');
                                                        }),
                                                    TextEntry::make('rejected_at')
                                                        ->label('Rejected At')
                                                        ->visible(function ($state) {
                                                            return !empty($state) ? true : false;
                                                        })
                                                        ->formatStateUsing(function ($record) {
                                                            if(!empty($record->pcDetail->user->timezone)){
                                                                $timezone = $record->pcDetail->user->timezone;
                                                                $time = Carbon::parse($record->rejected_at)->timezone($timezone);
                                                            }else{
                                                                $time = Carbon::parse($record->rejected_at);
                                                            }

                                                             return  $time->format('d M, Y h:i A');
                                                        }),
                                                    TextEntry::make('in_transit_at')
                                                        ->label('In-Transit At')
                                                        ->visible(function ($state) {
                                                            return !empty($state) ? true : false;
                                                        })
                                                        ->formatStateUsing(function ($record) {
                                                            if(!empty($record->pcDetail->user->timezone)){
                                                                $timezone = $record->pcDetail->user->timezone;
                                                                $time = Carbon::parse($record->in_transit_at)->timezone($timezone);
                                                            }else{
                                                                $time = Carbon::parse($record->in_transit_at);
                                                            }
                                                            return  $time->format('d M, Y h:i A');
                                                        }),
                                                    TextEntry::make('deliver_at')
                                                        ->label('Delivered At')
                                                        ->visible(function ($state) {
                                                            return !empty($state) ? true : false;
                                                        })
                                                        ->formatStateUsing(function ($record) {
                                                            if(!empty($record->pcDetail->user->timezone)){
                                                                $timezone = $record->pcDetail->user->timezone;
                                                                $time = Carbon::parse($record->deliver_at)->timezone($timezone);
                                                            }else{
                                                                $time = Carbon::parse($record->deliver_at);
                                                            }

                                                            return  $time->format('d M, Y h:i A');
                                                        }),
                                                    TextEntry::make('refunded_transaction_id')->label('Refunded Transaction ID')->default('-')->formatStateUsing(function ($state, $record) {
                                                        return '-';
                                                    })->visible(fn($record) => $record->status === 'rejected'),
                                                    TextEntry::make('refunded_transaction_status')->label('Transaction Status')->default('-')->formatStateUsing(function ($state, $record) {
                                                        return '-';
                                                    })->visible(fn($record) => $record->status === 'rejected'),
                                                    TextEntry::make('total_dpharma_points_used')->label('Refunded DPharma Points')->default('-')->formatStateUsing(function ($state, $record) {
                                                        return $record->total_dpharma_points_used ? $record->total_dpharma_points_used : '-';
                                                    })->visible(fn($record) => $record->status === 'rejected'),
                                                    TextEntry::make('rejected_note')->label('Rejected Note')->default('-')->formatStateUsing(function ($state, $record) {
                                                        return $record->rejected_note ? $record->rejected_note : '-';
                                                    })->visible(fn($record) => $record->status === 'rejected'),

                                                ])->columns(3),
                                                ViewEntry::make('my')->view('filament.infolist.components.sub-order-detail')
                                                    ->viewData(['suborder' => $record]),
                                            ];
                                        }

                                    )
                                ])
                        ])
                        ->columnSpanFull(),
                ];
            });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                Order::with('subOrder.user', 'orderProducts', 'user')
                    ->withCount('orderProducts')
                    ->withSum('orderProducts', 'total_commission')
            )
            ->defaultSort('id', 'desc')
            ->paginated([5, 10, 25, 50, 100])
            ->description(new HtmlString(
                '<div class="flex items-center gap-4 flex-wrap">' .
                    '<span class="font-bold whitespace-nowrap">Item Status:</span> ' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.pending') . '; border-color:' . config('constants.order_status.border_color.pending') . ';"></span>' .
                    '<span class="whitespace-nowrap">Pending</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.accepted') . '; border-color:' . config('constants.order_status.border_color.accepted') . ';"></span>' .
                    '<span class="whitespace-nowrap">Accepted</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.in_transit') . '; border-color:' . config('constants.order_status.border_color.in_transit') . ';"></span>' .
                    '<span class="whitespace-nowrap">In Transit</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.delivered') . '; border-color:' . config('constants.order_status.border_color.delivered') . ';"></span>' .
                    '<span class="whitespace-nowrap">Completed</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.cancelled') . '; border-color:' . config('constants.order_status.border_color.cancelled') . ';"></span>' .
                    '<span class="whitespace-nowrap">Cancelled</span>' .
                    '</div>' .
                    '</div>'
            ))
            ->recordUrl(null)
            ->columns([
                TextColumn::make('created_at')->label('Order Date')->dateTime('M d, Y | H:i A')->sortable()->searchable()->toggleable(),
                TextColumn::make('order_number')->label('Order ID')->sortable()->searchable()->toggleable(),
                TextColumn::make('user.name')->label('Facility Name')->sortable()->searchable()->toggleable()->formatStateUsing(fn($state) => ucfirst($state)),

                TextColumn::make('pharmaceutical_company')->toggleable()
                    ->label('Pharmaceutical Supplier')
                    ->getStateUsing(function (Order $record) {
                        $supplierNames = $record->subOrder
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->values();

                        $totalUsers = $supplierNames->count();

                        if ($totalUsers > 1) {
                            return $supplierNames->take(1)->implode(', ') . "... +" . ($totalUsers - 1);
                        }

                        return $supplierNames->implode(', ');
                    })->formatStateUsing(function (Order $record) {
                        $supplierNames = $record->subOrder
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->values();

                        $totalUsers = $supplierNames->count();

                        return View::make('filament.admin.resources.order-resource.pages.tooltip', [
                            'userNames' => $supplierNames,
                            'totalUsers' => $totalUsers
                        ])->render();
                    })->tooltip(function (Order $record) {
                        $supplierNames = $record->subOrder
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->implode(', ');

                        return $supplierNames;
                    })
                    ->html(),

                TextColumn::make('amount')->label('Order Total')->sortable()->searchable()->toggleable()->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),
                TextColumn::make('order_products_sum_total_commission')
                    ->label('Admin Fee')
                    ->formatStateUsing(function ($state) {
                        return $state > 0 ? 'RM ' . number_format($state, 2) : '-';
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('order_products_count') // Note: Using snake_case here
                    ->label('Items')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('payment_types')
                    ->label('Payment Types')
                    ->getStateUsing(function (Order $record) {
                        $paymentTypes = $record->subOrder
                            ->pluck('payment_type')
                            ->unique()
                            ->map(function ($type) {
                                return match ($type) {
                                    'pay_now' => 'Pay Now',
                                    'pay_later' => 'Pay Later',
                                    'credit_line' => 'Credit Line',
                                    default => ucfirst(str_replace('_', ' ', $type)),
                                };
                            })
                            ->values();

                        $totalTypes = $paymentTypes->count();

                        // If more than 2 types, show the first two followed by "+X"
                        if ($totalTypes > 2) {
                            return $paymentTypes->take(2)->implode(', ') . "... +" . ($totalTypes - 2);
                        }

                        // Otherwise, show all payment types
                        return $paymentTypes->implode(', ');
                    })
                    ->formatStateUsing(function (Order $record) {
                        $paymentTypes = $record->subOrder
                            ->pluck('payment_type')
                            ->unique()
                            ->map(function ($type) {
                                $backgroundColor = match ($type) {
                                    'pay_now' => '#fef3c7',
                                    'pay_later' => '#dbeafe',
                                    'credit_line' => '#fecaca',
                                    default => '#e5e7eb',
                                };

                                $textColor = match ($type) {
                                    'pay_now' => '#d97706',
                                    'pay_later' => '#2563eb',
                                    'credit_line' => '#dc2626',
                                    default => '#6b7280',
                                };

                                $displayName = match ($type) {
                                    'pay_now' => 'Pay Now',
                                    'pay_later' => 'Pay Later',
                                    'credit_line' => 'Credit Line',
                                    default => ucfirst(str_replace('_', ' ', $type)),
                                };

                                return [
                                    'name' => $displayName,
                                    'bg_color' => $backgroundColor,
                                    'text_color' => $textColor,
                                ];
                            })
                            ->values();

                        $totalTypes = $paymentTypes->count();

                        // Generate HTML for badges
                        $badgesHtml = $paymentTypes->map(function ($type) {
                            return sprintf(
                                '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: %s; color: %s; margin-right: 4px;">%s</span>',
                                $type['bg_color'],
                                $type['text_color'],
                                $type['name']
                            );
                        })->implode('');

                        // If more than 2 types, show first 2 + count
                        if ($totalTypes > 2) {
                            $firstTwo = $paymentTypes->take(2)->map(function ($type) {
                                return sprintf(
                                    '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: %s; color: %s; margin-right: 4px;">%s</span>',
                                    $type['bg_color'],
                                    $type['text_color'],
                                    $type['name']
                                );
                            })->implode('');

                            $badgesHtml = $firstTwo . sprintf(
                                '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">+%d</span>',
                                $totalTypes - 2
                            );
                        }

                        return $badgesHtml;
                    })
                    ->tooltip(function (Order $record) {
                        $paymentTypes = $record->subOrder
                            ->pluck('payment_type')
                            ->unique()
                            ->map(function ($type) {
                                return match ($type) {
                                    'pay_now' => 'Pay Now',
                                    'pay_later' => 'Pay Later',
                                    'credit_line' => 'Credit Line',
                                    default => ucfirst(str_replace('_', ' ', $type)),
                                };
                            })
                            ->implode(', ');

                        return $paymentTypes;
                    })
                    ->html()
                    ->toggleable(),
                TextColumn::make('status')
                    ->searchable()
                    ->label('Order Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', ($record->status == 'delivered' ? 'Completed' : $record->status))) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'accepted' => 'bi-patch-check-fill',
                            'cancelled' => 'bi-patch-check-fill',
                            'delivered' => 'bi-patch-check-fill',
                            'in_transit' => 'heroicon-o-truck',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color = config("constants.order_status.color.{$status}", '#424242');

                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),

                TextColumn::make('product_statuses')
                    ->label('Item Status')
                    ->getStateUsing(function (Order $record) {
                        $statusCounts = $record->orderProducts
                            ->groupBy('status')
                            ->map(fn($products) => $products->count())
                            ->mapWithKeys(function ($count, $status) {
                                $statusLabel = $status;
                                return [$statusLabel => $count];
                            });

                        $tooltipText = collect($statusCounts)
                            ->filter(fn($count, $label) => $label !== 'Not Showing')
                            ->map(fn($count, $label) => ucfirst(Str::title(str_replace('_', ' ', $label))) . " - {$count}")
                            ->implode('<br>');


                        // dd($tooltipText);

                        return $statusCounts
                            ->map(function ($count, $label) use ($tooltipText) {
                                $backgroundColor = match ($label) {
                                    'pending' => '#f3e3c6',
                                    'accepted' => config('constants.order_status.bg_color.accepted'),
                                    'rejected' => '#f8d9d4',
                                    'delivered' => '#e4fbe4',
                                    'cancelled' => '#f8d9d4',
                                    default => '#D3D3D3',
                                };

                                $textColor = match ($label) {
                                    'pending' => '#FF8C00',
                                    'accepted' => '#006400',
                                    'rejected' => '#B22222',
                                    'delivered' => '#006400',
                                    'cancelled' => '#B22222',
                                    default => '#A9A9A9',
                                };

                                // Use a unique ID for the tooltip so it can be toggled individually
                                $tooltipId = "tooltip-{$label}";

                                return View::make('filament.admin.resources.order-resource.pages.tooltip-status', [
                                    'textColor' => $textColor,
                                    'backgroundColor' => $backgroundColor,
                                    'tooltipText' => $tooltipText,
                                    'tooltipId' => $tooltipId,
                                    'count' => $count,
                                    'label' => $label,
                                ])->render();
                            })
                            ->implode(' ');
                    })
                    ->html(),

            ])
            ->filters([
                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('user.clinicData', 'clinic_name')->options(fn() => ClinicDetail::whereNotNull('clinic_name')->pluck('clinic_name', 'id')->toArray()),
                SelectFilter::make('user_id')
                    ->label('Pharmaceutical Suppliers')
                    ->multiple()
                    ->options(function () {
                        return PcDetail::with('companyType')
                            ->get()
                            ->mapWithKeys(function ($pc) {
                                $companyTypeName = optional($pc->companyType)->name;
                                $label = !empty($pc->company_name)
                                    ? $pc->company_name
                                    : (($companyTypeName === 'Sole Proprietary' && !empty($pc->business_name))
                                        ? $pc->business_name
                                        : null);
                                return $label ? [$pc->user_id => ucfirst($label)] : [];
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        $supplierIds = $data['values'] ?? [];
                        if (!empty($supplierIds)) {
                            $query->whereHas('subOrder', function ($q) use ($supplierIds) {
                                $q->whereIn('user_id', $supplierIds);
                            });
                        }
                        return $query;
                    }),

                SelectFilter::make('status')
                    ->label('Order Status')
                    ->multiple()
                    ->options([
                        'pending' => 'Pending',
                        // 'rejected' => 'Rejected',
                        // 'accepted' => 'Accepted',
                        'cancelled' => 'Cancelled',
                        'in_transit' => 'In Transit',
                        'delivered' => 'Completed',

                    ]),
                SelectFilter::make('payment_type')
                    ->label('Payment Type')
                    ->multiple()
                    ->options([
                        'pay_now' => 'Pay Now',
                        'pay_later' => 'Pay Later',
                        'credit_line' => 'Credit Line',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        $paymentTypes = $data['values'] ?? [];

                        if (!empty($paymentTypes)) {
                            $query->whereHas('subOrder', function ($q) use ($paymentTypes) {
                                $q->whereIn('payment_type', $paymentTypes);
                            });
                        }

                        return $query;
                    }),
                // ->query(function (\Illuminate\Database\Eloquent\Builder $query, array $data) {
                //     $statuses = $data['values'] ?? [];

                //     // Separate the statuses based on which table they belong to
                //     $subOrderStatuses = collect($statuses)->intersect(['rejected', 'accepted']);
                //     $orderStatuses = collect($statuses)->diff(['rejected', 'accepted']);

                //     $query->where(function ($query) use ($subOrderStatuses, $orderStatuses) {
                //         if ($subOrderStatuses->isNotEmpty()) {
                //             $query->orWhereHas('subOrders', function ($q) use ($subOrderStatuses) {
                //                 $q->whereIn('status', $subOrderStatuses);
                //             });
                //         }

                //         if ($orderStatuses->isNotEmpty()) {
                //             $query->orWhereIn('status', $orderStatuses);
                //         }
                //     });

                //     return $query;
                // }),

                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('From')
                            ->maxDate(fn($get) => $get('to_date') ?? now())
                            ->closeOnDateSelection()
                            ->placeholder('Select from date'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('To')
                            ->minDate(fn($get) => $get('from_date'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->placeholder('Select to date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date)
                            )
                            ->when(
                                $data['to_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date)
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from_date'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from_date'])->toFormattedDateString();
                        }
                        if ($data['to_date'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['to_date'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ])
            // ->filters([
            //     Filter::make('custom_search')
            //         ->form([
            //             Forms\Components\Repeater::make('conditions')
            //                 ->label('')
            //                 ->schema([
            //                     Forms\Components\Select::make('field')
            //                         ->label('Field')
            //                         ->rules(['required'])
            //                         ->options([
            //                             'user.name' => 'Facility Name',
            //                             // 'order_number' => 'Order Number',
            //                             'subOrder.user.name' => 'Pharmaceutical Company',
            //                         ])
            //                         ->required(),
            //                     Forms\Components\Select::make('operator')
            //                         ->label('Operator')
            //                         ->rules(['required'])
            //                         ->options([
            //                             'contains' => 'Contains',
            //                             'not_contains' => 'Not Contains',
            //                             'equals' => 'Equals To',
            //                             'not_equals' => 'Not Equals To',
            //                         ])
            //                         ->required(),
            //                     Forms\Components\TextInput::make('keyword')
            //                         ->label('Keyword')
            //                         ->rules(['required']),
            //                     Forms\Components\Radio::make('logic')
            //                         ->label('Logic')
            //                         ->options([
            //                             'and' => 'AND',
            //                             'or' => 'OR',
            //                         ])
            //                         ->default('and') // Default to 'AND'
            //                         ->inline(true)
            //                         ->required(),

            //                 ])
            //                 ->columns(3)
            //                 ->addActionLabel('Add More')
            //                 ->minItems(1)
            //                 ->collapsible(false)
            //                 ->orderable(false)
            //                 ->deletable(true)
            //                 ->deletable(fn(mixed $state): bool => is_array($state) && count($state) > 1)

            //         ])
            //         ->query(function (Builder $query, array $data) {
            //             if (!empty($data['conditions']) && is_array($data['conditions'])) {
            //                 $previousLogic = null;

            //                 $query->where(function ($query) use ($data, &$previousLogic) {
            //                     foreach ($data['conditions'] as $key => $condition) {
            //                         if (isset($condition['field'], $condition['operator'], $condition['keyword'])) {
            //                             $operatorMap = [
            //                                 'contains' => 'like',
            //                                 'not_contains' => 'not like',
            //                                 'equals' => '=',
            //                                 'not_equals' => '<>',
            //                             ];
            //                             $operator = $operatorMap[$condition['operator']] ?? null;
            //                             $keyword = $condition['keyword'];
            //                             if (str_contains($condition['field'], '.')) {
            //                                 // Handle relations
            //                                 $relations = explode('.', $condition['field']);
            //                                 $field = array_pop($relations); // Extract the final field (e.g., `text`)
            //                                 $relationChain = implode('.', $relations); // Build the relation chain (e.g., `post.comment`)

            //                                 if ($operator === 'like' || $operator === 'not like') {
            //                                     $keyword = strtolower($condition['keyword']);
            //                                     if ($previousLogic === 'and' || $previousLogic === null) {
            //                                         $query->whereHas($relationChain, function ($query) use ($field, $operator, $keyword) {
            //                                             $query->where(DB::raw("LOWER({$field})"), $operator, "%$keyword%");
            //                                         });
            //                                     } elseif ($previousLogic === 'or') {
            //                                         $query->orWhereHas($relationChain, function ($query) use ($field, $operator, $keyword) {
            //                                             $query->where(DB::raw("LOWER({$field})"), $operator, "%$keyword%");
            //                                         });
            //                                     }
            //                                 } else {
            //                                     if ($previousLogic === 'and' || $previousLogic === null) {
            //                                         $query->whereHas($relationChain, function ($query) use ($field, $operator, $keyword) {
            //                                             $query->where($field, $operator, $keyword);
            //                                         });
            //                                     } elseif ($previousLogic === 'or') {
            //                                         $query->orWhereHas($relationChain, function ($query) use ($field, $operator, $keyword) {
            //                                             $query->where($field, $operator, $keyword);
            //                                         });
            //                                     }
            //                                 }
            //                             } else {


            //                                 // Handle main model fields
            //                                 if ($operator === 'like' || $operator === 'not like') {
            //                                     $keyword = strtolower($condition['keyword']);
            //                                     // For LIKE and NOT LIKE, apply LOWER
            //                                     if ($previousLogic === 'and' || $previousLogic === null) {
            //                                         $query->where(DB::raw("LOWER({$condition['field']})"), $operator, "%$keyword%");
            //                                     } elseif ($previousLogic === 'or') {
            //                                         $query->orWhere(DB::raw("LOWER({$condition['field']})"), $operator, "%$keyword%");
            //                                     }
            //                                 } else {
            //                                     // For other operators, no need for LOWER
            //                                     if ($previousLogic === 'and' || $previousLogic === null) {
            //                                         $query->where($condition['field'], $operator, $keyword);
            //                                     } elseif ($previousLogic === 'or') {
            //                                         $query->orWhere($condition['field'], $operator, $keyword);
            //                                     }
            //                                 }
            //                             }

            //                             // Update the logic for the next iteration
            //                             $previousLogic = $condition['logic'] ?? null;
            //                         }
            //                     }
            //                 });
            //             }
            //             //dd($data, $query->toSql(), $query->getBindings());
            //         })->indicateUsing(function (array $data) {
            //             $indicators = [];

            //             if (!empty($data['conditions']) && is_array($data['conditions'])) {
            //                 foreach ($data['conditions'] as $key => $condition) {
            //                     if (isset($condition['field'], $condition['operator'], $condition['keyword'])) {
            //                         $indicators[] = Indicator::make($condition["keyword"])
            //                             ->removeField('from');
            //                     }
            //                 }
            //             }
            //             return $indicators;
            //         })

            // ])->deferFilters()->filtersFormWidth(MaxWidth::FourExtraLarge)
            ->actionsColumnLabel('Actions')
            ->actions([
                ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->url(function ($record) {
                        Cookie::queue('source', 'all-order');
                        return route('filament.admin.resources.orders.view', ['record' => $record->id]);
                    }) // Change order_id to record
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('Order Details')->label(false),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
            'view' => Pages\ViewOrder::route('/{record}'),
        ];
    }
    public static function getApprovedOrderTotal($order)
    {
        try {

            $referredValue = DB::table('clinic_referred_codes')
                ->join('users', 'clinic_referred_codes.to_user_id', '=', 'users.id')
                ->where('clinic_referred_codes.to_user_id', $order->order->user_id)->where('clinic_referred_codes.status', 'pending')->select('clinic_referred_codes.order_value', 'clinic_referred_codes.points', 'clinic_referred_codes.id', 'clinic_referred_codes.to_user_id', 'users.name', 'clinic_referred_codes.from_user_id')->first();

            if ($referredValue) {
                $userId =  $order->order->user_id;
                $userTotalAmount = Order::where('user_id', $userId)
                    ->with(['subOrder' => function ($query) {
                        $query->where('status', 'accepted');
                    }])
                    ->get()
                    ->sum(function ($order) {
                        return $order->subOrder->sum('total_amount');
                    });

                $totalAmountReferred = $userTotalAmount + $order->total_amount;

                if ($totalAmountReferred >= $referredValue->order_value) {

                    DB::table('dpharma_points')->insert([
                        'user_id'          => $referredValue->from_user_id,
                        'description'      => 'Cheer up! Earn rewards by referring to the ' . $referredValue->name,
                        'points'           => $referredValue->points,
                        'redeem'           => null,
                        'balance' => DB::raw("(
                            COALESCE(
                                (SELECT balance FROM dpharma_points
                                 WHERE user_id = {$referredValue->from_user_id}
                                 ORDER BY created_at DESC LIMIT 1),
                                0
                            ) + {$referredValue->points}
                        )"),
                        'created_at'       => now(),
                        'updated_at'       => now(),
                        'reference_id'     => $referredValue->id,
                        'reference_value'  => 'clinic_referred_codes',
                    ]);
                    DB::table('clinic_referred_codes')
                        ->where('id', $referredValue->id)
                        ->update(['status' => 'approved']);
                    return 1;
                }
            }
            return 0;
        } catch (\exception $th) {
            return 0;
        }
    }
    public static function updateOrderStatus($orderId)
    {
        try {
            $statuses = DB::table('sub_orders')
                ->where('order_id', $orderId)
                ->pluck('status')
                ->toArray();
            if (empty($statuses)) {
                return;
            }

            $allStatusesUpdated = !in_array('pending', $statuses);

            if ($allStatusesUpdated) {
                $orderStatus = 'delivered'; // All statuses updated
            } elseif (count($statuses) === 1 && (in_array('rejected', $statuses) || in_array('accepted', $statuses))) {
                $orderStatus = 'in_transit'; // Only one status is accepted or rejected
            } elseif (in_array('rejected', $statuses) || in_array('accepted', $statuses)) {
                $orderStatus = 'in_transit';
            } else {
                $orderStatus = 'pending'; // Fallback status
            }
            DB::table('orders')
                ->where('id', $orderId)
                ->update(['status' => $orderStatus]);
        } catch (\exception $th) {
            return 0;
        }
    }
    public static function payoutOrder($order)
    {
        try {
            $pcId = $order->user_id;
            $today = Carbon::now()->startOfDay();
            $payoutType = $order->payout_type ?? '';
            $cycleType = $order->cycle_type ?? '';
            if ($cycleType === 'monthly') {
                $startDate = Carbon::now()->startOfMonth()->startOfDay();
                $endDate = Carbon::now()->endOfMonth()->endOfDay();
            } else {
                if ($today->day < 15) {
                    // 1st to 14th cycle
                    $startDate = $today->copy()->startOfMonth()->startOfDay();
                    $endDate = $today->copy()->startOfMonth()->addDays(13)->endOfDay(); // 14th day
                } else {
                    // 15th to end of month cycle
                    $startDate = $today->copy()->startOfMonth()->addDays(14)->startOfDay(); // 15th day
                    $endDate = $today->copy()->endOfMonth()->endOfDay(); // last day of month
                }
                // $startDate = Carbon::now()->startOfMonth()->startOfDay();
                // $endDate = Carbon::now()->startOfMonth()->addDays(13)->endOfDay();
            }

            $payout = DB::table('payouts')
                ->where('user_id', $pcId)
                ->whereDate('start_date', '<=', $today)
                ->whereDate('end_date', '>=', $today)
                ->first();
            if (!$payout) {
                $payoutId = DB::table('payouts')->insertGetId([
                    'user_id' => $pcId,
                    'payout_type' => $payoutType,
                    'cycle_type' => $cycleType,
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ]);
            } else {
                $payoutId = $payout->id;
            }
            if ($payoutId) {
                DB::table('payout_sub_orders')->insert([
                    'payout_id' => $payoutId,
                    'sub_order_id' => $order->id,
                    'order_id' => $order->order_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        } catch (\exception $th) {
            return 0;
        }
    }
}
