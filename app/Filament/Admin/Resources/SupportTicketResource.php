<?php

namespace App\Filament\Admin\Resources;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Models\SupportTicket;
use Filament\Resources\Resource;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Filament\Admin\Resources\SupportTicketResource\Pages;
use App\Filament\Admin\Resources\SupportTicketResource\RelationManagers;
use App\Filament\Admin\Resources\SupportTicketResource\Pages\ListSupportTickets;
use App\Filament\Admin\Resources\SupportTicketResource\Pages\CreateSupportTicket;
use App\Filament\Admin\Resources\SupportTicketResource\Pages\AdminConversionDetails;
use App\Models\ClinicDetail;
use App\Models\SubOrder;
use Filament\Tables\Actions\Action;
use App\Models\SupportCategory;
use App\Models\SupportTicketMessage;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\DatePicker;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\Indicator;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;

class SupportTicketResource extends Resource
{
    protected static ?string $model = SupportTicket::class;

    protected static ?string $navigationLabel = 'Support Tickets';
    protected static ?string $navigationGroup = 'Support';

    public static function canAccess(): bool
    {
        // auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-tickets_support ticket view');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {        
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->recordUrl(function (SupportTicket $record): string {
                return self::getUrl('details', ['record' => $record]);
            })
            ->emptyStateHeading('No support ticket records found')
            ->description(new \Illuminate\Support\HtmlString('<strong>Note:</strong> 
                    <span> Tab counts are based on all data and do not update with search or filter changes.</span>'))
            ->columns([
                TextColumn::make('id')
                    ->label('Ticket ID')
                    ->sortable()
                    ->prefix('TKT-')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.id', 'like', "%{$search}%");
                    })
                    ->toggleable(),
                TextColumn::make('order.order_number')
                    ->url(function (SupportTicket $record) {
                        if (
                            $record->order_id &&
                            (
                                auth()->user()->hasRole('Super Admin') ||
                                auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            return route('filament.admin.resources.orders.view', ['record' => $record->order_id]);
                        }
                        return null;
                    })
                    ->tooltip(function (SupportTicket $record) {
                        if (
                            !$record->order_id ||
                            (
                                !auth()->user()->hasRole('Super Admin') &&
                                !auth()->user()->can('all-orders_view details')
                            )
                        ) {
                            return 'You do not have permission to view this order.';
                        }
                        return null;
                    })
                    ->formatStateUsing(fn(?string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html()
                    ->label('Order ID')->sortable()->toggleable()->searchable(),
                TextColumn::make('name')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.name', 'like', "%{$search}%");
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('sender.name')->label('Raised From')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    }),
                TextColumn::make('receiver.name')->label('Raised To')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('receiver', function (Builder $query) use ($search) {
                            $query->where(function (Builder $query) use ($search) {
                                $query->where('users.name', 'like', "%{$search}%")
                                    ->orWhere(function (Builder $query) use ($search) {
                                        if (stripos('DPharma', $search) !== false) {
                                            $query->where('users.id', 1);
                                        }
                                    });
                            });
                        });
                    })
                    ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query->leftJoin('users as receiver_users', 'support_tickets.receiver_id', '=', 'receiver_users.id')
                            ->orderByRaw("
                                CASE 
                                    WHEN receiver_users.id = 1 THEN 'A' 
                                    ELSE receiver_users.name 
                                END {$direction}
                            ");
                    })
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->receiver && $record->receiver->hasRole('Super Admin')) {
                            return 'DPharma';
                        }
                        return ucfirst($state);
                    }),
                TextColumn::make('sender.roles.name')
                    ->label('User Type')
                    ->searchable()
                    ->sortable()
                    ->toggleable()
                    ->formatStateUsing(function ($state, SupportTicket $record) {
                        $getFormattedRole = function ($roleName) {
                            return match (strtolower($roleName)) {
                                'pharmaceutical company' => 'Pharmaceutical Supplier',
                                'clinic' => 'Facility',
                                default => ucfirst($roleName),
                            };
                        };

                        if ($record->sender->parent_id === null) {
                            $role = $record->sender->roles->first();
                            return $role ? $getFormattedRole($role->name) : 'No Role';
                        } else {
                            $parent = $record->sender->parent;
                            $parentRole = $parent ? $parent->roles->first() : null;
                            return $parentRole ? $getFormattedRole($parentRole->name) : 'No Parent Role';
                        }
                    }),
                TextColumn::make('created_at')
                    ->label('Created Date')
                    ->sortable()
                    ->toggleable()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.created_at', 'like', "%{$search}%");
                    })
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),
                TextColumn::make('email')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.email', 'like', "%{$search}%");
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('category.name')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('subject')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.subject', 'like', "%{$search}%");
                    })
                    ->sortable()
                    ->toggleable()
                    ->limit(10)
                    ->tooltip(fn($record) => $record->subject)
                    ->suffix('...'),
                TextColumn::make('closed_at')
                    ->label('Closed Date')
                    ->sortable()
                    ->toggleable()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.closed_at', 'like', "%{$search}%");
                    })
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        if (is_null($state) || $state === '' || $state === '-') {
                            return '-';
                        }
                        try {
                            return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                                ->setTimezone($timeZone)
                                ->format('M d, Y | h:i A');
                        } catch (\Exception $e) {
                            return '-';
                        }
                    })->default('-'),
                TextColumn::make('status')
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('support_tickets.status', 'like', "%{$search}%");
                    })
                    ->toggleable()
                    ->sortable()
                    ->badge()
                    ->color(function ($state) {
                        if ($state === 'open') {
                            return 'success';
                        } else if ($state === 'closed') {
                            return 'danger';
                        } else {
                            return 'primary';
                        }
                    })->formatStateUsing(function ($state) {
                        return ucfirst($state);
                    })
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('account_type')
                    ->label('User Type')
                    ->searchable()
                    ->multiple()
                    ->options([
                        'Clinic' => 'Facility',
                        'Pharmaceutical Company' => 'Pharmaceutical Supplier',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->where(function ($query) use ($data) {
                            $query->whereHas('sender', function ($q) use ($data) {
                                $q->whereNull('parent_id')
                                    ->whereHas('roles', function ($roleQuery) use ($data) {
                                        $roleQuery->whereIn('name', $data['values']);
                                    });
                            });

                            $query->orWhereHas('sender', function ($q) use ($data) {
                                $q->whereNotNull('parent_id')
                                    ->whereHas('parent.roles', function ($parentRoleQuery) use ($data) {
                                        $parentRoleQuery->whereIn('name', $data['values']);
                                    });
                            });
                        });
                    }),
                SelectFilter::make('sender.name')
                    ->label('Raised From')
                    ->multiple()
                    ->options(function () {
                        return SupportTicket::with('sender')
                            ->get()
                            ->unique('sender_id')
                            ->mapWithKeys(function ($ticket) {
                                if ($ticket->sender) {
                                    $displayName = $ticket->sender->id === 1 ? 'DPharma' : $ticket->sender->name;
                                    return [$ticket->sender->id => $displayName];
                                }
                                return [];
                            })
                            ->filter()
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }
                        return $query->whereIn('sender_id', $data['values']);
                    }),

                // Updated filter for "Raised To" with DPharma support
                SelectFilter::make('receiver.name')
                    ->label('Raised To')
                    ->multiple()
                    ->options(function () {
                        return SupportTicket::with('receiver')
                            ->get()
                            ->unique('receiver_id')
                            ->mapWithKeys(function ($ticket) {
                                if ($ticket->receiver) {
                                    $displayName = $ticket->receiver->id === 1 ? 'DPharma' : $ticket->receiver->name;
                                    return [$ticket->receiver->id => $displayName];
                                }
                                return [];
                            })
                            ->filter()
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }
                        return $query->whereIn('receiver_id', $data['values']);
                    }),
                SelectFilter::make('category_id')
                    ->label('Category')
                    ->multiple()
                    ->relationship('category', 'name')
                    ->preload(),
                SelectFilter::make('status')
                    ->label('Status')
                    ->multiple()
                    ->options([
                        'open' => 'Open',
                        'closed' => 'Closed',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereIn('status', $data['values']);
                    }),
                Filter::make('created_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Created Date')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['date'], fn($q) => $q->whereDate('created_at', $data['date']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' .  Carbon::parse($data['date'])->toFormattedDateString();
                    }),
                Filter::make('closed_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Closed Date')->maxDate(now()),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when($data['date'], fn($q) => $q->whereDate('closed_at', $data['date']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' .  Carbon::parse($data['date'])->toFormattedDateString();
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->label('')
                    ->url(function ($record, $livewire) {
                        $activeTab = $livewire->activeTab; //request()->query('activeTab');
                        $validTabs = ['all', 'pharmaceutical_supplier', 'facility'];
                        $activeTab = in_array($activeTab, $validTabs) ? $activeTab : 'all';

                        // Store in session for persistence
                        session(['support_ticket_active_tab' => $activeTab]);
                        return self::getUrl('details', ['record' => $record, 'activeTab' => $activeTab]);
                    }),
                // ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record])),
                Action::make('Chat')
                    ->icon('heroicon-o-chat-bubble-oval-left-ellipsis')->tooltip('Chat')
                    ->size('sm')
                    ->iconButton()
                    ->color(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    })
                     ->visible(function () {
                        $user = auth()->user();
                        return $user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('support-tickets_support ticket chat');
                    })
                    ->extraAttributes(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return [
                            'class' => 'border-2 rounded-lg ' . ($unreadCount > 0 ? 'text-green-500' : 'text-blue-500'),
                            'style' => 'margin-left: inherit; border-color: ' . ($unreadCount > 0 ? 'rgb(34, 197, 94)' : 'rgb(0, 70, 104)'),
                        ];
                    })
                    ->label('')
                    ->url(fn(SupportTicket $record) => self::getUrl('details', ['record' => $record]) . '#conversation-header')
                    ->badge(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->badgeColor(function (SupportTicket $record) {
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? 'success' : 'primary';
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->emptyStateHeading('No support tickets Found');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSupportTickets::route('/'),
            'create' => CreateSupportTicket::route('/create'),
            'details' => AdminConversionDetails::route('/{record}/details/{activeTab?}'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }
}
