<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\PendingFacilityRequestResource\Pages;
use App\Filament\Admin\Resources\PendingFacilityRequestResource\RelationManagers;
use App\Models\Approval;
use App\Models\PendingFacilityRequest;
use App\Models\User;
use App\Models\UserAddress;
use Carbon\Carbon;
use Filament\Tables\Actions\ViewAction;
use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\State;

class PendingFacilityRequestResource extends Resource
{
    protected static ?string $model = Approval::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Pending Requests';
    protected static ?string $navigationLabel = 'Facility Update Requests';
    protected static ?int $navigationSort = 4;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('pending-requests_view facility edit request');
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return User::query()
                    ->role('Clinic')
                    ->whereHas('approvals', function ($q) {
                        $q->where('user_type', 'facility')->where('approved_at', null)->where('rejected_at', null)->where('approved_by', null)->where('rejected_by', null);
                    })
                    ->with('clinicDetails', 'address.city', 'address.state', 'billingAddress.city', 'billingAddress.state');
            })
            ->columns([
                TextColumn::make('id')->label('Facility Id')->sortable()->searchable()->toggleable(),
                TextColumn::make('clinicDetails.clinic_name')->label('Facility Name')->searchable()->sortable()->toggleable(),
                TextColumn::make('clinicDetails.clinicAccountType.name')->label('Facility Type')->sortable()->searchable()->toggleable(),
                TextColumn::make('billingAddress.city.name')->toggleable(),
                TextColumn::make('billingAddress.state.name')->toggleable(),
                TextColumn::make('email')->sortable()->searchable()->toggleable(),
                TextColumn::make('clinicDetails.tier')
                    ->label('Tier')
                    ->searchable()->toggleable()
                    ->badge()
                    ->sortable()
                    ->formatStateUsing(fn ($state): string => ucfirst($state))
                    ->color(function ($state) {
                        $st = match ($state) {
                            'gold' => 'warning',
                            'silver' => 'success',
                            'bronze' => 'danger',
                            default => 'warning',
                        };
                        return $st;
                    }),

                // TextColumn::make('created_at')->label('Registered On')->dateTime('M d, Y')->searchable()->toggleable(),
                TextColumn::make('approvals.created_at')
                    ->toggleable()
                    ->label('Requested Date')
                    ->formatStateUsing(function ($record): string {
                        $latestApproval = $record->approvals->sortByDesc('id')->first();
                        if ($latestApproval) {
                            $userTimezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
                            $convertedDate = \Carbon\Carbon::parse($latestApproval->created_at)->timezone($userTimezone);
                            return $convertedDate->format('M d, Y | h:i A');
                        }
                        return '-';
                    })
                    
                    ->searchable(),
                // TextColumn::make('company_name')->sortable()->searchable()->toggleable(),

            ])
            ->filters([
                SelectFilter::make('clinicDetails.clinic_name')
                    ->label('Facility Name')
                    ->relationship(
                        'clinicDetails',
                        'clinic_name',
                        fn (Builder $query) => $query->whereNotNull('clinic_name')->where('is_submitted', 1)
                    )
                    ->searchable()
                    ->preload(),
                SelectFilter::make('clinicDetails.clinic_account_type_id') // or whatever the foreign key is named
                    ->label('Facility Type')
                    ->relationship('clinicDetails.clinicAccountType', 'name')
                    ->searchable()
                    ->preload(),
                    Filter::make('location')
                    ->label('Location')
                    ->form([
                        Select::make('state_id')
                            ->label('State')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search) {
                                $results = State::where('name', 'like', "%{$search}%")
                                    ->orderBy('name')
                                    ->pluck('name', 'id')
                                    ->toArray();
                                // If no results found, show "No result found"
                                if (empty($results)) {
                                    return ['' => 'No result found'];
                                }
                                return $results;
                            })
                            ->preload()
                            ->options(fn () => State::orderBy('name')->pluck('name', 'id'))
                            ->reactive()
                            ->afterStateUpdated(fn (callable $set) => $set('city_id', null)),
                        Select::make('city_id')
                            ->label('City')
                            ->searchable()
                            ->getSearchResultsUsing(function (string $search, callable $get) {
                                $stateId = $get('state_id');
                                $query = City::query();
                                if ($stateId) {
                                    $query->where('state_id', $stateId);
                                }
                                $results = $query->where('name', 'like', "%{$search}%")
                                    ->orderBy('name')
                                    ->pluck('name', 'id')
                                    ->toArray();
                                if (empty($results)) {
                                    return ['' => 'No result found'];
                                }
                                return $results;
                            })
                            ->preload()
                            ->options(function (callable $get) {
                                $stateId = $get('state_id');
                                if ($stateId) {
                                    return City::where('state_id', $stateId)->orderBy('name')->pluck('name', 'id');
                                }
                                return City::orderBy('name')->pluck('name', 'id');
                            })
                            ->reactive(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!empty($data['state_id'])) {
                            $query->whereHas('userAddresses', function ($q) use ($data) {
                                $q->where('state_id', $data['state_id']);
                            });
                        }
                        if (!empty($data['city_id'])) {
                            $query->whereHas('userAddresses', function ($q) use ($data) {
                                $q->where('city_id', $data['city_id']);
                            });
                        }
                        return $query;
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if (!empty($data['state_id'])) {
                            $state = State::find($data['state_id']);
                            $indicators[] = 'State: ' . ($state ? $state->name : 'Unknown');
                        }
                        if (!empty($data['city_id'])) {
                            $city = City::find($data['city_id']);
                            $indicators[] = 'City: ' . ($city ? $city->name : 'Unknown');
                        }
                        return $indicators;
                    }),
                    Filter::make('requested_date')
                    ->label('Requested Date Range')
                    ->form([
                        DatePicker::make('requested_from')
                            ->label('Requested From Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->reactive(),
                        DatePicker::make('requested_until')
                            ->label('Requested To Date')
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->minDate(fn ($get) => $get('requested_from')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // For PostgreSQL compatibility, avoid MySQL-style backticks and use double quotes.
                        // Also, ensure the correct relationship key (approvalable_id) is used.
                        // We'll filter users by the created_at of their latest approval record.

                        if (!empty($data['requested_from']) || !empty($data['requested_until'])) {
                            // Subquery to get the latest approval id for each user
                            $latestApprovalSub = \DB::table('approvals')
                                ->selectRaw('MAX(id)')
                                ->whereColumn('approvalable_id', 'users.id')
                                ->where('user_type', 'facility');

                            $query->whereHas('approvals', function ($q) use ($data, $latestApprovalSub) {
                                $q->where('user_type', 'facility')
                                  ->whereColumn('approvalable_id', 'users.id')
                                  ->whereIn('id', $latestApprovalSub);

                                if (!empty($data['requested_from'])) {
                                    $q->whereDate('created_at', '>=', $data['requested_from']);
                                }
                                if (!empty($data['requested_until'])) {
                                    $q->whereDate('created_at', '<=', $data['requested_until']);
                                }
                            });
                        }
                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (!$data['requested_from'] && !$data['requested_until']) {
                            return null;
                        }
                        $from = $data['requested_from'] ? Carbon::parse($data['requested_from'])->toFormattedDateString() : '...';
                        $until = $data['requested_until'] ? Carbon::parse($data['requested_until'])->toFormattedDateString() : '...';
                        return "Requested between {$from} and {$until}";
                    }),

                // Filter::make('created_at')
                //     ->label('Registered On')
                //     ->form([
                //         DatePicker::make('created_from')->label('Registered From')->closeOnDateSelection()->reactive(),
                //         DatePicker::make('created_until')->label('Registered Until')->closeOnDateSelection()->minDate(function ($get) {
                //             return $get('created_from') ? Carbon::parse($get('created_from')) : null;
                //         }),
                //     ])
                //     ->query(function (Builder $query, array $data): Builder {
                //         return $query
                //             ->when(
                //                 $data['created_from'],
                //                 fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                //             )
                //             ->when(
                //                 $data['created_until'],
                //                 fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                //             );
                //     })->indicateUsing(function (array $data): ?string {
                //         if (! $data['created_from']) {
                //             return null;
                //         }
                //         return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                //     }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                ViewAction::make('view')->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])->label('')
                    ->visible(function ($record) {
                        return true;
                    })->url(fn ($record): string => "/clinics/{$record->id}"),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPendingFacilityRequests::route('/'),
            'create' => Pages\CreatePendingFacilityRequest::route('/create'),
            'edit' => Pages\EditPendingFacilityRequest::route('/{record}/edit'),
        ];
    }
}
