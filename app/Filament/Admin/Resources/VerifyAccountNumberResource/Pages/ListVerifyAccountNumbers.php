<?php

namespace App\Filament\Admin\Resources\VerifyAccountNumberResource\Pages;

use Filament\Actions;
use Illuminate\View\View;
use Livewire\Attributes\Session;
use App\Models\ClinicPharmaSupplier;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Admin\Resources\VerifyAccountNumberResource;

class ListVerifyAccountNumbers extends ListRecords
{
    protected static string $resource = VerifyAccountNumberResource::class;

    #[Session]
    public bool $isVerified = true;

    #[Session]
    public bool $isPending = false;

    #[Session]
    public bool $isRejected = false;


    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Pending Request',
            // VerifyAccountNumberResource::getUrl() => 'Verify Account Number',
            // 2 => 'List',
        ];
    }

    public function getTitle(): string
    {
        return 'Verify Credit Line';
    }

    public function getHeader(): ?View
    {
        return $this->getHeaderHeading();
    }


    private function getHeaderHeading()
    {
        return view('custom-view.header-action-request', [
            'title' => 'Verify Credit Line',
            'headerActions' => $this->getHeaderActions(),
            'isVerified' => $this->isVerified,
            'isRejected' => $this->isRejected,
            'isPending' => $this->isPending,
            // 'pending' => ClinicPharmaSupplier::query()->pendingApprovals()->count(),
        ]);
    }

    public function pendingApprovals()
    {
        $this->isPending = true;
        $this->isVerified = false;
        $this->isRejected = false;
        $this->resetTable();
    }

    public function allVerified()
    {
        $this->isPending = false;
        $this->isVerified = true;
        $this->isRejected = false;
        $this->resetTable();
    }

    public function rejectedApprovals()
    {
        $this->isPending = false;
        $this->isVerified = false;
        $this->isRejected = true;
        $this->resetTable();
    }

    protected function getTableQuery(): ?Builder
    {
        if ($this->isVerified) {
            return ClinicPharmaSupplier::query()->where('status', 'approved')->where('is_open_account', false);
        } elseif ($this->isPending) {
            return ClinicPharmaSupplier::query()->where('status', 'pending')->where('is_open_account', false);
        } else {
            return ClinicPharmaSupplier::query()->where('status', 'rejected')->where('is_open_account', false);
        }
    }


    //     public function getTabs(): array
    // {
    //     return [
    //         'verified' => Tab::make()
    //             ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'approved')),
    //         'pending' => Tab::make()
    //             ->modifyQueryUsing(fn (Builder $query) => $query->where('status', 'pending')),
    //     ];
    // }
}
