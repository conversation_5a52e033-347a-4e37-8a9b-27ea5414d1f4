<?php

namespace App\Filament\Admin\Resources\PendingFacilityRequestResource\Pages;

use App\Filament\Admin\Resources\PendingFacilityRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPendingFacilityRequests extends ListRecords
{
    protected static string $resource = PendingFacilityRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Pending Request',
            // PendingFacilityRequestResource::getUrl() => 'Facility Edit Request',
            // 2 => 'List',
        ];
    }

    public function getTitle(): string
    {
        return 'Facility Update Requests';
    }
}
