<?php

namespace App\Filament\Admin\Resources\ScheduledPayoutResource\Widgets;

use App\Models\Payout;
use App\Models\PayoutSubOrder;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Filament\Contracts\SupportsConfiguration;
use Filament\Widgets\TableWidget;
use Illuminate\Support\Facades\Cookie;

class ScheduledPayoutItemsTable extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';
    public $record = null;
    protected static string $view = 'filament.admin.resources.scheduled-payout-resource.widgets.scheduled-payout-items-table';
    protected static ?string $heading  = 'Order Details';




    public function table(Table $table): Table
    {

        return $table
        ->query(function (Builder $query) {
            return PayoutSubOrder::query()
                ->where('payout_id', $this->record->id)
                ->leftJoin('sub_orders', 'payout_sub_orders.sub_order_id', '=', 'sub_orders.id')
                ->leftJoin('order_products', 'sub_orders.id', '=', 'order_products.sub_order_id')
                ->selectRaw('payout_sub_orders.*, COALESCE(SUM(order_products.total_commission), 0) as admin_fee_sum')
                ->groupBy('payout_sub_orders.id');
        })
            ->columns([
                TextColumn::make('order.order_number')->sortable()->label('Order ID'),
                TextColumn::make('order.created_at')->sortable()->dateTime('M d, Y')->label('Order Date'),
                TextColumn::make('order.amount')->sortable()->label('Order Total')
                ->formatStateUsing(function ($record) {
                    $amount = $record->order->amount;
                    return 'RM ' . number_format($amount, 2);
                }),
                TextColumn::make('admin_fee_sum')
                ->label('Admin Fee')
                ->sortable()
                ->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),


            ])
            ->filters([

                            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Action::make('view')
                // ->url(fn(PayoutSubOrder $record): string => route('filament.admin.resources.orders.view', ['record' => $record->order->id, 'type' => 'scheduled'])) // Change order_id to record

                ->url(function(PayoutSubOrder $record){
                    Cookie::queue('source', 'scheduled');
                    Cookie::queue('id', $this->record->id);
                    return route('filament.admin.resources.orders.view', ['record' => $record->order->id, 'type' => 'scheduled']);
                }) // Change order_id to record
                ->icon('heroicon-o-eye')
                ->size('w-5 h-5')
                ->extraAttributes(['class' => 'fi-color-gray fi-icon-btn h-8 w-8 rounded-lg justify-center items-center -m-0'])

                ->tooltip('Order Details')
                ->label(false),
            ]);

    }
}

