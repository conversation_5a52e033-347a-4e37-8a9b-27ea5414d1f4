<?php

namespace App\Filament\Admin\Resources\ScheduledPayoutResource\Pages;

use App\Filament\Admin\Resources\ScheduledPayoutResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListScheduledPayouts extends ListRecords
{
    protected static string $resource = ScheduledPayoutResource::class;

    protected function getHeaderActions(): array
    {
        return [
           // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Payouts History",
            // $this->getResource()::getUrl('index') => "Scheduled Payouts",
            // 3 => "List",
        ];
    }
}
