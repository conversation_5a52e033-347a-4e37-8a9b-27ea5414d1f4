<?php

namespace App\Filament\Admin\Resources\ScheduledPayoutResource\Pages;

use App\Models\ClinicDetail;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use App\Filament\Admin\Resources\ClinicResource\Pages;
use App\Filament\Admin\Resources\ScheduledPayoutResource;
use App\Filament\Admin\Resources\FullPayoutResource\Widgets\FullPayoutItemsTable;
use App\Filament\Admin\Resources\ScheduledPayoutResource\Widgets\ScheduledPayoutItemsTable;
use App\Models\Payout;
use App\Models\PayoutSubOrder;
use Livewire\Livewire;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Builder;
use Filament\Widgets\WidgetConfiguration;

class ViewScheduledPayout extends ViewRecord
{
    protected static string $resource = ScheduledPayoutResource::class;

    // protected ?string $heading = 'Scheduled Payouts';

    // protected static string $view = 'filament.admin.resources.clinic-resource.pages.view-clinic-onboarding-page';

    public ?array $data = [];

    public User $user;

    public Payout $payout;
    public $userAddress;
    public $clinicDetail;
    public $clinicAccountType;
    public $clinicPcDetail;

    // public function mount(int|string $record): void
    // {


    //     $this->payout = Payout::find($record);


    // }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Payouts History",
            $this->getResource()::getUrl('index') => "Scheduled Payouts",
            3 => "Payout Details",
        ];
    }

    public function getTitle(): string
    {
        return '#' . $this->getRecord()->id ?? '-';
    }

    protected function getFooterWidgets(): array
    {
        return [
            ScheduledPayoutItemsTable::make([
                'record' => $this->getRecord(), // Pass the current payout record
            ]),
        ];

    }



    public function getHeaderActions(): array
    {

        return [
            Action::make('back')
            ->label('Back')
            ->color('gray')
            ->url(ScheduledPayoutResource::getUrl('index'))
        ];

    }



}
