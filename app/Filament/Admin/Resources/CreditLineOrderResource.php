<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Exports\OrderExporter;
use App\Filament\Admin\Resources\CreditLineOrderResource\Pages;
use App\Filament\Pc\Resources\CreditLineOrderResource\RelationManagers;
use App\Models\ClinicDetail;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use App\Models\User;
use App\Models\Order;
use App\Models\SubOrder;
use Filament\Facades\Filament;
use Illuminate\Support\Arr;
use Filament\Infolists\Infolist;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Filament\Tables\Actions\ViewAction;
use Filament\Infolists\Components\ViewEntry;
use Filament\Tables\Filters\Indicator;
use Filament\Tables\Filters\Filter;
use Illuminate\Support\HtmlString;
use Filament\Support\Enums\MaxWidth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cookie;
use App\Models\PcDetail;

class CreditLineOrderResource extends Resource
{
    protected static ?string $model = Order::class;

    //protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationGroup = 'Orders Management';
    protected static ?string $navigationLabel = 'Credit Line Orders';

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('credit-line-orders_view')
            || auth()->user()->can('credit-line-orders credit') || auth()->user()->can('credit-line-orders assign credit');
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table

            ->query(
                Order::with('subOrder.user', 'orderProducts', 'user')
                    ->withCount([
                        'orderProducts as filtered_order_products_count' => function ($query) {
                            $query->whereHas('subOrder', function ($subQuery) {
                                $subQuery->where('payment_type', 'credit_line');
                            });
                        }
                    ])
                    ->whereHas('subOrder', function ($query) {
                        $query->where('payment_type', 'credit_line');
                    })->withSum('orderProducts', 'total_commission')
            )->defaultSort('id', 'desc')
            ->paginated([5, 10, 25, 50, 100])
            ->description(new HtmlString(
                '<div class="flex items-center gap-4 flex-wrap">' .
                    '<span class="font-bold whitespace-nowrap">Order Status:</span> ' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.pending') . '; border-color:' . config('constants.order_status.border_color.pending') . ';"></span>' .
                    '<span class="whitespace-nowrap">Pending</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.accepted') . '; border-color:' . config('constants.order_status.border_color.accepted') . ';"></span>' .
                    '<span class="whitespace-nowrap">Accepted</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.in_transit') . '; border-color:' . config('constants.order_status.border_color.in_transit') . ';"></span>' .
                    '<span class="whitespace-nowrap">In Transit</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.delivered') . '; border-color:' . config('constants.order_status.border_color.delivered') . ';"></span>' .
                    '<span class="whitespace-nowrap">Completed</span>' .
                    '</div>' .
                    '<div class="flex items-center gap-2">' .
                    '<span class="w-5 h-5 border rounded-full flex-shrink-0" style="background-color:' . config('constants.order_status.bg_color.cancelled') . '; border-color:' . config('constants.order_status.border_color.cancelled') . ';"></span>' .
                    '<span class="whitespace-nowrap">Cancelled</span>' .
                    '</div>' .
                    '</div>'
            ))
            ->recordUrl(null)
            ->columns([
                TextColumn::make('created_at')->label('Order Date')->dateTime('M d, Y | H:i A')->sortable()->searchable()->toggleable(),
                TextColumn::make('order_number')->label('Order ID')->sortable()->searchable()->toggleable(),
                TextColumn::make('user.name')->label('Facility Name')->sortable()->searchable()->toggleable()->formatStateUsing(fn($state) => ucfirst($state)),

                // TextColumn::make('pharmaceutical_company')->toggleable()
                //     ->label('Pharmaceutical Supplier')
                //     ->getStateUsing(function (Order $record) {
                //         $userNames = $record->subOrder
                //             ->where('payment_type', 'credit_line')
                //             ->pluck('user.name') // Get user names through products
                //             ->unique() // Remove duplicates
                //             ->values();

                //         $totalUsers = $userNames->count();

                //         // If more than 2 users, show the first two followed by "+X"
                //         if ($totalUsers > 1) {
                //             return $userNames->take(1)->implode(', ') . "... +" . ($totalUsers - 1);
                //         }

                //         // Otherwise, show all user names
                //         return $userNames->implode(', ');
                //     })->formatStateUsing(function (Order $record) {
                //         $userNames = $record->subOrder
                //             ->where('payment_type', 'credit_line')
                //             ->pluck('user.name')
                //             ->map(fn($name) => ucfirst($name))
                //             ->unique()
                //             ->values();

                //         $totalUsers = $userNames->count();

                //         return View::make('filament.admin.resources.order-resource.pages.tooltip', [
                //             'userNames' => $userNames,
                //             'totalUsers' => $totalUsers
                //         ])->render();
                //     })
                //     ->html(),

                TextColumn::make('pharmaceutical_company')->toggleable()
                    ->label('Pharmaceutical Supplier')
                    ->getStateUsing(function (Order $record) {
                        $supplierNames = $record->subOrder
                        ->where('payment_type', 'credit_line')
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->values();

                        $totalUsers = $supplierNames->count();

                        if ($totalUsers > 1) {
                            return $supplierNames->take(1)->implode(', ') . "... +" . ($totalUsers - 1);
                        }

                        return $supplierNames->implode(', ');
                    })->formatStateUsing(function (Order $record) {
                        $supplierNames = $record->subOrder
                        ->where('payment_type', 'credit_line')
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->values();

                        $totalUsers = $supplierNames->count();

                        return View::make('filament.admin.resources.order-resource.pages.tooltip', [
                            'userNames' => $supplierNames,
                            'totalUsers' => $totalUsers
                        ])->render();
                    })->tooltip(function (Order $record) {
                        $supplierNames = $record->subOrder
                        ->where('payment_type', 'credit_line')
                            ->map(function ($subOrder) {
                                $pc = $subOrder->user?->pcDetails;
                                return $pc ? ucfirst(pcCompanyName($pc)) : null;
                            })
                            ->filter()
                            ->unique()
                            ->implode(', ');

                        return $supplierNames;
                    })
                    ->html(),

                TextColumn::make('amount')->label('Order Total')->sortable()->searchable()->toggleable()->formatStateUsing(fn($state) => 'RM ' . number_format($state, 2)),
                TextColumn::make('order_products_sum_total_commission')
                    ->label('Admin Fee')
                    ->formatStateUsing(function ($state) {
                        return $state > 0 ? 'RM ' . number_format($state, 2) : '-';
                    })
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('filtered_order_products_count') // Note: Using snake_case here
                    ->label('Items')
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('status')
                    ->searchable()
                    ->label('Order Status')
                    ->toggleable()
                    ->formatStateUsing(function ($state, $record) {
                        return $record ? ucwords(str_replace('_', ' ', ($record->status == 'delivered' ? 'Completed' : $record->status))) : 'Unknown';
                    })
                    ->icon(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        return match ($status) {
                            'pending' => 'bi-clock-fill',
                            'rejected' => 'bi-x-circle-fill',
                            'accepted' => 'bi-patch-check-fill',
                            'cancelled' => 'bi-patch-check-fill',
                            'delivered' => 'bi-patch-check-fill',
                            'in_transit' => 'heroicon-o-truck',
                            default => 'heroicon-o-question-mark-circle',
                        };
                    })
                    ->color(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $color  = config("constants.order_status.color.{$status}", '#424242');

                        return $color;
                    })
                    ->extraAttributes(function ($state, $record) {
                        $status = $record ? strtolower($record->status) : 'unknown';

                        $bgColor = config("constants.order_status.bg_color.{$status}", '#E0E0E0');
                        $color = config("constants.order_status.color.{$status}", '#424242');
                        $borderColor = config("constants.order_status.border_color.{$status}", '#BDBDBD');

                        return [
                            'style' => "background-color:{$bgColor}; border: 1px solid {$borderColor}; border-radius: 6px; color:{$color}; padding: 4px 8px; width: fit-content; font-weight: 500;"
                        ];
                    }),
                TextColumn::make('product_statuses')
                    ->label('Status')
                    ->getStateUsing(function (Order $record) {
                        $statusCounts = $record->orderProducts()
                            ->whereHas('subOrder', function ($query) {
                                $query->where('payment_type', 'credit_line');
                            })
                            ->get()
                            ->groupBy('status')
                            ->map(fn($products) => $products->count())
                            ->mapWithKeys(function ($count, $status) {
                                $statusLabel = $status;
                                return [$statusLabel => $count];
                            });

                        $tooltipText = collect($statusCounts)
                            ->filter(fn($count, $label) => $label !== 'Not Showing')
                            ->map(fn($count, $label) => ucfirst($label) . " - {$count}")
                            ->implode('<br>');


                        return $statusCounts
                            ->map(function ($count, $label) use ($tooltipText) {
                                $backgroundColor = config("constants.order_status.bg_color." . $label, '#D3D3D3');
                                $textColor = config("constants.order_status.color." . $label, '#A9A9A9');

                                // Use a unique ID for the tooltip so it can be toggled individually
                                $tooltipId = "tooltip-{$label}";

                                return View::make('filament.admin.resources.order-resource.pages.tooltip-status', [
                                    'textColor' => $textColor,
                                    'backgroundColor' => $backgroundColor,
                                    'tooltipText' => $tooltipText,
                                    'tooltipId' => $tooltipId,
                                    'count' => $count,
                                    'label' => $label,
                                ])->render();
                            })
                            ->implode(' ');
                    })
                    ->html(),

            ])
            ->filters([
                SelectFilter::make('clinic_id')->label('Facilities')->multiple()->relationship('user.clinicData', 'clinic_name')->options(fn() => ClinicDetail::whereNotNull('clinic_name')->pluck('clinic_name', 'id')->toArray()),
                // SelectFilter::make('user_id')->label('Pharmaceutical Suppliers')->multiple()->relationship('subOrder.user', 'name')->options(fn() => User::whereNotNull('name')->pluck('name', 'id')->toArray()),

                SelectFilter::make('user_id')
                ->label('Pharmaceutical Suppliers')
                ->multiple()
                ->options(function () {
                    return PcDetail::with('companyType')
                        ->get()
                        ->mapWithKeys(function ($pc) {
                            $companyTypeName = optional($pc->companyType)->name;
                            $label = !empty($pc->company_name)
                                ? $pc->company_name
                                : (($companyTypeName === 'Sole Proprietary' && !empty($pc->business_name))
                                    ? $pc->business_name
                                    : null);
                            return $label ? [$pc->user_id => ucfirst($label)] : [];
                        })
                        ->toArray();
                })
                ->query(function (Builder $query, array $data): Builder {
                    $supplierIds = $data['values'] ?? [];
                    if (!empty($supplierIds)) {
                        $query->whereHas('subOrder', function ($q) use ($supplierIds) {
                            $q->whereIn('user_id', $supplierIds);
                        });
                    }
                    return $query;
                }),
                SelectFilter::make('status')
                    ->label('Order Status')
                    ->multiple()
                    ->options([
                        'pending' => 'Pending',
                        // 'rejected' => 'Rejected',
                        // 'accepted' => 'Accepted',
                        'cancelled' => 'Cancelled',
                        'in_transit' => 'In Transit',
                        'delivered' => 'Completed',

                    ]),
                // ->query(function (\Illuminate\Database\Eloquent\Builder $query, array $data) {
                //     $statuses = $data['values'] ?? [];

                //     // Separate the statuses based on which table they belong to
                //     $subOrderStatuses = collect($statuses)->intersect(['rejected', 'accepted']);
                //     $orderStatuses = collect($statuses)->diff(['rejected', 'accepted']);

                //     $query->where(function ($query) use ($subOrderStatuses, $orderStatuses) {
                //         if ($subOrderStatuses->isNotEmpty()) {
                //             $query->orWhereHas('subOrders', function ($q) use ($subOrderStatuses) {
                //                 $q->whereIn('status', $subOrderStatuses);
                //             });
                //         }

                //         if ($orderStatuses->isNotEmpty()) {
                //             $query->orWhereIn('status', $orderStatuses);
                //         }
                //     });

                //     return $query;
                // }),

                Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('from_date')
                            ->label('From')
                            ->maxDate(fn($get) => $get('to_date') ?? now())
                            ->closeOnDateSelection()
                            ->placeholder('Select from date'),
                        Forms\Components\DatePicker::make('to_date')
                            ->label('To')
                            ->minDate(fn($get) => $get('from_date'))
                            ->maxDate(now())
                            ->closeOnDateSelection()
                            ->placeholder('Select to date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date)
                            )
                            ->when(
                                $data['to_date'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date)
                            );
                    })
                    ->indicateUsing(function (array $data): array {
                        $indicators = [];
                        if ($data['from_date'] ?? null) {
                            $indicators[] = 'From: ' . Carbon::parse($data['from_date'])->toFormattedDateString();
                        }
                        if ($data['to_date'] ?? null) {
                            $indicators[] = 'To: ' . Carbon::parse($data['to_date'])->toFormattedDateString();
                        }
                        return $indicators;
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Action::make('view')
                    ->url(function (Order $record) {
                        Cookie::queue('source', 'credit_line');
                        return route('filament.admin.resources.orders.view', ['record' => $record->id, 'type' => 'credit_line']);
                    }) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('Order Details')
                    ->color('gray')
                    ->label(false),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCreditLineOrders::route('/'),
            'create' => Pages\CreateCreditLineOrder::route('/create'),
            'edit' => Pages\EditCreditLineOrder::route('/{record}/edit'),
        ];
    }
}
