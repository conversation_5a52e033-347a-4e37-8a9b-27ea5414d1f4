<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ScheduledPayoutResource\Pages;
use App\Filament\Admin\Resources\ScheduledPayoutResource\RelationManagers;
use App\Filament\Admin\Resources\ScheduledPayoutResource\Widgets\FullPayoutItemsTable;
use App\Models\Payout;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Tables\Columns\TextColumn;
use App\Models\User;
use Carbon\Carbon;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Notifications\Notification;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Repeater;
use App\Models\PcDetail;

class ScheduledPayoutResource extends Resource
{
    protected static ?string $model = Payout::class;

    protected static ?string $navigationGroup = 'Finance';
    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationIcon = '';
    protected static ?string $navigationLabel = 'Scheduled Payouts';
    protected static ?string $label = 'Scheduled Payouts';
    protected static ?int $navigationSort = 3;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('finance_schedule payout');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            // Removed extraAttributes for AlpineJS tab handling
            ->schema(function ($record) {
                $record = $record->load('user', 'payoutSubOrders');
                $start = Carbon::parse($record->start_date);
                $end = Carbon::parse($record->end_date);

                $monthYear = $start->format('M');
                $startFormatted = $start->format('jS M');
                $endFormatted = $end->format('jS M');

                $headerTitle =  "{$monthYear} ({$startFormatted} - {$endFormatted})";
                return [
                    InfoSection::make($headerTitle)

                        ->schema([
                            \Filament\Infolists\Components\Grid::make([
                                'default' => 5,
                                'sm' => 5,
                            ])
                                ->columnSpan('full')
                                ->extraAttributes(['class' => 'gap-0'])
                                ->schema([


                                    \Filament\Infolists\Components\Grid::make([
                                        'default' => 4,
                                        'sm' => 4,
                                    ])
                                        ->columnSpan(4)
                                        ->schema([
                                            TextEntry::make('id')
                                                ->label('Payout ID'),
                                            TextEntry::make('payout_on')
                                                ->label('Payout On')->dateTime('M d, Y'),
                                            TextEntry::make('user.pcdetails.business_name')
                                            ->formatStateUsing(function ($record) {
                                                return ucfirst(pcCompanyName($record->user->pcdetails));
                                            })
                                                ->label('Pharma Supplier'),

                                            TextEntry::make('payoutSubOrders')
                                                ->label('Total Orders')
                                                ->formatStateUsing(function ($record) {
                                                    $count = $record->payoutSubOrders->count();
                                                    return $count;
                                                }),
                                            TextEntry::make('payoutSubOrders')
                                                ->label('Payout Amount')
                                                ->formatStateUsing(function ($record) {
                                                    $amount = $record->payoutSubOrders->sum(fn($subOrder) => $subOrder->order->amount ?? 0);
                                                    return 'RM ' . number_format($amount, 2);
                                                }),
                                            TextEntry::make('transaction_id')
                                                ->label('Transaction ID')->default('-'),
                                            TextEntry::make('is_payout')->label('Payout Status')
                                                ->formatStateUsing(function ($state) {
                                                    if ($state) {
                                                        return 'Yes';
                                                    } else {
                                                        return 'No';
                                                    }
                                                }),
                                            // TextEntry::make('outstanding_commission_status')
                                            //     ->label('Payout Status')->formatStateUsing(fn($state) => blank($state) ? '-' : ucfirst($state)),

                                        ])
                                ])
                        ]),


                ];
            });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return Payout::query()->where('payout_type', 'schedule')->with(['user', 'payoutSubOrders.order'])->withSum('orders', 'amount');
            })
            ->columns([
                TextColumn::make('id')->label('ID')->sortable()->searchable()->toggleable(),


                TextColumn::make('user.pcDetails.business_name')
                ->label('Pharma Supplier')
                ->formatStateUsing(function ($record) {
                    return ucfirst(pcCompanyName($record->user->pcdetails));
                })
                ->searchable()->sortable()->toggleable(),
                TextColumn::make('start_date')
                    ->label('Payout Cycle')->toggleable()
                    ->formatStateUsing(function ($record) {
                        if (!$record->start_date || !$record->end_date) {
                            return '—'; // Or return null to leave it blank
                        }
                        $start = Carbon::parse($record->start_date);
                        $end = Carbon::parse($record->end_date);

                        $monthYear = $start->format('M Y');
                        $startFormatted = $start->format('M d, Y');
                        $endFormatted = $end->format('M d, Y');

                        return "{$startFormatted} - {$endFormatted}";
                    }),
                TextColumn::make('payout_on')->label('Payout On')->dateTime('M d, Y')->searchable()->toggleable(),

                TextColumn::make('orders_sum_amount') // This column is created by withSum
                    ->label('Payout Amount')
                    ->sortable()
                    ->toggleable()
                    ->default('-')
                    ->formatStateUsing(fn($state) => (is_numeric($state) || is_float($state)) ? 'RM ' .  number_format($state ?? 0, 2) : ''),

                TextColumn::make('admin_fee')
                    ->label('Admin Fee')
                    ->sortable(query: function (\Illuminate\Database\Eloquent\Builder $query, string $direction) {
                        // Sort by the sum of total_commission for all suborders in the payout
                        return $query->orderByRaw(
                            '(SELECT COALESCE(SUM(order_products.total_commission),0)
                                FROM payout_sub_orders
                                JOIN sub_orders ON payout_sub_orders.sub_order_id = sub_orders.id
                                JOIN order_products ON order_products.sub_order_id = sub_orders.id
                                WHERE payout_sub_orders.payout_id = payouts.id
                            ) ' . $direction
                        );
                    })
                    ->getStateUsing(function ($record) {
                        // Eager load payoutSubOrders.subOrder.orderProducts if not loaded
                        if (!$record->relationLoaded('payoutSubOrders')) {
                            $record->load('payoutSubOrders.subOrder.orderProducts');
                        }
                        $total = 0;
                        foreach ($record->payoutSubOrders as $payoutSubOrder) {
                            $subOrder = $payoutSubOrder->subOrder;
                            if ($subOrder && $subOrder->relationLoaded('orderProducts')) {
                                $total += $subOrder->orderProducts->sum('total_commission');
                            } elseif ($subOrder) {
                                // fallback if not loaded
                                $total += $subOrder->orderProducts()->sum('total_commission');
                            }
                        }
                        return $total;
                    })
                    ->formatStateUsing(function ($state) {
                        return 'RM ' . number_format($state, 2);
                    }),

                TextColumn::make('is_payout')->label('Payout Status')->toggleable()
                    ->formatStateUsing(function ($state) {
                        if ($state) {
                            return 'Yes';
                        } else {
                            return 'No';
                        }
                    }),
            ])
            ->filters([
                // SelectFilter::make('user.pcdetails.business_name') // or whatever the foreign key is named
                //     ->label('Pharma Supplier')
                //     ->relationship('user.pcdetails', 'business_name')
                //     ->searchable()
                //     ->preload(),
                SelectFilter::make('user.pcdetails.business_name')
                    ->label('Pharmaceutical Suppliers')
                    ->multiple()
                    ->options(function () {
                        return PcDetail::with('companyType')
                            ->get()
                            ->mapWithKeys(function ($pc) {
                                $companyTypeName = optional($pc->companyType)->name;
                                $label = !empty($pc->company_name)
                                    ? $pc->company_name
                                    : (($companyTypeName === 'Sole Proprietary' && !empty($pc->business_name))
                                        ? $pc->business_name
                                        : null);
                                return $label ? [$pc->user_id => ucfirst($label)] : [];
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        $supplierIds = $data['values'] ?? [];
                        if (!empty($supplierIds)) {
                            // $query->whereHas('subOrder', function ($q) use ($supplierIds) {
                                $query->whereIn('user_id', $supplierIds);
                            // });
                        }
                        return $query;
                    }),
                Filter::make('payout_on')
                    ->label('Payout On')
                    ->form([
                        DatePicker::make('payout_on_from')->label('Payout From')->closeOnDateSelection()->reactive(),
                        DatePicker::make('payout_on_until')->label('Payout Until')->closeOnDateSelection()->minDate(function ($get) {
                            return $get('payout_on_from') ? Carbon::parse($get('payout_on_from')) : null;
                        }),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['payout_on_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payout_on', '>=', $date),
                            )
                            ->when(
                                $data['payout_on_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('payout_on', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['payout_on_from']) {
                            return null;
                        }
                        return 'From ' . Carbon::parse($data['payout_on_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['payout_on_until'])->toFormattedDateString();
                    }),
                SelectFilter::make('is_payout')
                    ->label('Payout Status')
                    ->options([
                        true => 'Yes',
                        false => 'No'
                    ])
                    ->searchable()
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('View'),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }



    public static function getPages(): array
    {
        return [
            'index' => Pages\ListScheduledPayouts::route('/'),
            'create' => Pages\CreateScheduledPayout::route('/create'),
            'edit' => Pages\EditScheduledPayout::route('/{record}/edit'),
            'view' => Pages\ViewScheduledPayout::route('/{record}'),
        ];
    }
}
