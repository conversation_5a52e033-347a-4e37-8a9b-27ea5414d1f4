<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\RolePermissionResource\Pages;
use App\Filament\Admin\Resources\RolePermissionResource\Pages\ShowRolePermissionUsers;
use App\Filament\Admin\Resources\RolePermissionResource\Pages\ViewUserManage;
use App\Filament\Admin\Resources\RolePermissionResource\RelationManagers;
use App\Models\AccountType;
use App\Models\RolePermission;
use App\Models\User;
use App\Services\PermissionService;
use App\Services\PsPermissionService;
use Filament\Facades\Filament;
use Filament\Forms;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Illuminate\Support\Str;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Google\Service\AIPlatformNotebooks\Status;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;
use Indianic\FilamentShield\Forms\ShieldSelectAllToggle;
use Indianic\FilamentShield\Support\Utils;
use Spatie\Permission\Models\Role;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Commands\Show;

class RolePermissionResource extends Resource
{
    protected static ?string $model = Role::class;

    protected static ?string $navigationGroup = "PS Users";
    protected static ?int $navigationSort = 2;
    protected static ?string $label = 'Roles & Permissions';


    // public static function canView(Model $record): bool
    // {

    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('roles_view');
    // }

    // public static function canCreate(): bool
    // {

    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('roles_create');
    // }

    // public static function canEdit(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('roles_edit');
    // }

    // public static function canDelete(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('roles_delete');
    // }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }


    public static $parent_id;

    public static function form(Form $form): Form
    {
        self::$parent_id = request()->get('parent_id');

        $permissionsData = self::getPermissionList();

        $permissionGroups = $permissionsData['permissions'];
        $assignedPermissions = $permissionsData['assigned'];
        $permissionComponents = [];

        foreach ($permissionGroups as $groupName => $options) {
            $groupAssignedPermissions = array_intersect(array_keys($options), $assignedPermissions);

            if (!empty($options)) {
                $permissionComponents[] = Grid::make()
                    ->schema([
                        Section::make("{$groupName} Permissions")
                            ->heading(fn() => $groupName === 'credit-line-orders'
                                ? 'Credit Line Permissions'
                                : Str::headline($groupName) . ' Permissions')
                            ->schema([
                                CheckboxList::make("permissions_{$groupName}")
                                    ->label('')
                                    ->options($options)
                                    ->live()
                                    ->extraAttributes([
                                        'class' => 'fix-checkbox-label-click',
                                    ])
                                    ->columns(2)
                                    ->bulkToggleable()
                                    ->default($groupAssignedPermissions)
                                    ->afterStateUpdated(function (Get $get, Set $set, $state, $old, CheckboxList $component) use ($groupName) {
                                        $dependentPermissions = [
                                            'credit-line-orders_assign credit',
                                            'credit-line-orders_edit assign credit',
                                        ];

                                        $facilityKey = 'permissions_facility';
                                        $facilityPermissions = collect($get($facilityKey) ?? []);

                                        $sessionKey = "permissions_{$groupName}_state_info";
                                        $stateInfo = session()->get($sessionKey, [
                                            'facility_view_previously_checked' => false,
                                            'facility_auto_added' => false,
                                            'all_orders_chat_auto_added' => false,
                                            'previous_state' => []
                                        ]);

                                        $facilityViewPreviouslyChecked = $stateInfo['facility_view_previously_checked'] ?? false;
                                        $facilityWasAutoInserted = $stateInfo['facility_auto_added'] ?? false;
                                        $chatWasAutoInserted = $stateInfo['all_orders_chat_auto_added'] ?? false;

                                        $newState = collect($state ?? []);
                                        $oldState = collect($old ?? []);
                                        $dependentSelected = $newState->intersect($dependentPermissions)->isNotEmpty();

                                        $changed = false;

                                        // Facility view auto-insert logic
                                        if ($dependentSelected) {
                                            if (!$facilityPermissions->contains('facility_view')) {
                                                $facilityPermissions->push('facility_view');
                                                $changed = true;
                                                $stateInfo['facility_auto_added'] = true;
                                            }
                                        } else {
                                            if (
                                                $facilityWasAutoInserted &&
                                                !$facilityViewPreviouslyChecked &&
                                                $facilityPermissions->contains('facility_view')
                                            ) {
                                                $facilityPermissions = $facilityPermissions->reject(fn($p) => $p === 'facility_view');
                                                $changed = true;
                                                $stateInfo['facility_auto_added'] = false;
                                            }
                                        }

                                        if ($changed) {
                                            $set($facilityKey, $facilityPermissions->values()->toArray());
                                        }

                                        // ✅ Sync credit-line-orders_chat ↔ all-orders_chat ONLY IF credit-line-orders_chat was toggled
                                        $chatPermission = 'credit-line-orders_chat';
                                        $chatWasChecked = !$oldState->contains($chatPermission) && $newState->contains($chatPermission);
                                        $chatWasUnchecked = $oldState->contains($chatPermission) && !$newState->contains($chatPermission);

                                        if ($chatWasChecked || $chatWasUnchecked) {
                                            $allOrdersKey = 'permissions_all-orders';
                                            $allOrdersPermissions = collect($get($allOrdersKey) ?: []);
                                            $allOrdersChanged = false;

                                            if ($chatWasChecked) {
                                                if (!$allOrdersPermissions->contains('all-orders_chat')) {
                                                    $allOrdersPermissions->push('all-orders_chat');
                                                    $stateInfo['all_orders_chat_auto_added'] = true;
                                                    $allOrdersChanged = true;
                                                }
                                            } elseif ($chatWasUnchecked) {
                                                if (
                                                    $chatWasAutoInserted &&
                                                    $allOrdersPermissions->contains('all-orders_chat')
                                                ) {
                                                    $allOrdersPermissions = $allOrdersPermissions->reject(fn($p) => $p === 'all-orders_chat');
                                                    $stateInfo['all_orders_chat_auto_added'] = false;
                                                    $allOrdersChanged = true;
                                                }
                                            }

                                            if ($allOrdersChanged) {
                                                $set($allOrdersKey, $allOrdersPermissions->values()->toArray());
                                            }
                                        }

                                        // Save the new state
                                        $stateInfo['previous_state'] = $state;
                                        session()->put($sessionKey, $stateInfo);
                                    })
                                    ->afterStateHydrated(function (CheckboxList $component, $state, Get $get) use ($groupAssignedPermissions, $groupName) {
                                        if (empty($state) || $state === null) {
                                            $component->state(array_values($groupAssignedPermissions));
                                        }

                                        // Initialize state tracking on hydration
                                        $sessionKey = "permissions_{$groupName}_state_info";
                                        $facilityKey = 'permissions_facility';
                                        $facilityPermissions = collect($get($facilityKey) ?? []);

                                        session()->put($sessionKey, [
                                            'facility_view_previously_checked' => $facilityPermissions->contains('facility_view'),
                                            'auto_added' => false,
                                            'previous_state' => $state ?? $groupAssignedPermissions
                                        ]);
                                    })
                                    ->dehydrated(fn() => true)
                            ])
                            ->columnSpan(2)

                            ->collapsible(),
                    ]);
            }
        }

        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([
                                Forms\Components\TextInput::make('display_name')
                                    ->formatStateUsing(fn($state, $record) => $state ?? $record->name ?? null)
                                    ->label(new HtmlString('Role <span class="text-red-500">*</span>'))
                                    ->validationAttribute('Role')
                                    ->placeholder('Role')
                                    ->rules(function (Get $get, ?Model $record) {
                                        $userId = $get('created_by');
                                        $generatedName = "{$userId}-{$get('display_name')}";
                                        return [
                                            'required',
                                            'regex:/^[a-zA-Z0-9 ]+$/',
                                            'max:20',
                                            function ($attribute, $value, $fail) use ($record, $userId, $generatedName) {
                                                $value = trim($value);
                                                $generatedName = "{$userId}-{$value}";
                                                $generatedName = trim(strtolower(trim($generatedName)));
                                                $query = Role::whereRaw('LOWER(TRIM(name)) = ?', [$generatedName]);
                                                if ($record) {
                                                    $query->where('id', '!=', $record->id);
                                                }
                                                if ($query->exists()) {
                                                    $fail("The role '{$value}' is already taken.");
                                                }
                                            }
                                        ];
                                    })
                                    ->validationMessages([
                                        'required' => 'The Role field is required.',
                                        'regex' => 'Only letters, numbers, and spaces are allowed.',
                                    ])
                                    ->afterStateUpdated(function (Get $get, Set $set) {
                                        $userId = auth()->id();
                                        $set('name', "{$userId}-{$get('display_name')}");
                                    })
                                    ->live(onBlur: true)
                                    // ->columns(3)
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('name')
                                    ->label('Name')
                                    ->hidden()
                                    ->unique(ignoreRecord: true)
                                    ->dehydrated(),

                                Forms\Components\Select::make('created_by')
                                    ->label(new HtmlString('Pharmaceutical Company<span class="text-red-500" style="color:red;">*</span>'))
                                    ->placeholder('Pharmaceutical Company')
                                    ->formatStateUsing(function ($state) {
                                        if (!empty(self::$parent_id)) {

                                            return self::$parent_id;
                                        }
                                        return $state;
                                    })
                                    ->disabledOn('edit')
                                    ->options(function () {
                                        $pharmaceuticalCompanyRole = Role::where('name', 'Pharmaceutical Company')->first();

                                        if (!$pharmaceuticalCompanyRole) {
                                            return [];
                                        }
                                        return User::query()
                                            ->whereNull('parent_id')
                                            ->whereHas('roles', function ($query) use ($pharmaceuticalCompanyRole) {
                                                $query->where('role_id', $pharmaceuticalCompanyRole->id);
                                            })
                                            ->whereNotNull('name')
                                            ->get()
                                            ->mapWithKeys(fn($user) => [$user->id => $user->name ?? '[No Name]'])
                                            ->toArray();
                                    })
                                    ->rules('required')
                                    ->validationMessages([
                                        'required' => (__('message.role_permission.company_required')),
                                    ])
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->afterStateUpdated(fn($set) => $set('role', null)),

                                // Forms\Components\TextInput::make('guard_name')
                                //     ->label(__('filament-shield::filament-shield.field.guard_name'))
                                //     ->readOnly()
                                //     ->dehydrated(true)
                                //     ->default(Utils::getFilamentAuthGuard())
                                //     ->nullable()
                                //     ->maxLength(255),
                                ShieldSelectAllToggle::make('select_all')
                                    ->onIcon('heroicon-s-shield-check')
                                    ->offIcon('heroicon-s-shield-exclamation')
                                    ->label(__('filament-shield::filament-shield.field.select_all.name'))
                                    ->helperText(fn(): HtmlString => new HtmlString(__('filament-shield::filament-shield.field.select_all.message')))
                                    ->dehydrated(fn(bool $state): bool => $state),

                            ])
                            ->columns([
                                'sm' => 3,
                                'lg' => 3,
                            ]),
                    ]),
                Group::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make('User Permissions')
                            ->description(__('message.role_permission.description'))
                            ->schema([
                                Grid::make(2)
                                    ->columnSpan(2)
                                    ->schema($permissionComponents)
                            ])
                            ->columnSpan(2)
                            ->visible(fn() => count($permissionComponents) > 0),
                    ])
            ]);
    }

    public static function getPermissionList(): array
    {
        $routeId = request()->route('record');
        return app(PsPermissionService::class)->getPermissionsForRole($routeId);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Actions')
            ->query(function () {
                $searchTerm = request()->input('tableSearch');
                $data =  User::query()
                    ->select([
                        'users.parent_id as id',
                        'parent_users.name as parent_name',
                        DB::raw('(SELECT COUNT(*) FROM roles WHERE roles.created_by = users.parent_id) as created_roles_count'),
                    ])
                    ->leftJoin('users as parent_users', 'users.parent_id', '=', 'parent_users.id')
                    ->whereIn('users.parent_id', function ($query) {
                        $query->select('id')
                            ->from('users')
                            ->whereExists(function ($roleQuery) {
                                $roleQuery->select(DB::raw(1))
                                    ->from('model_has_roles')
                                    ->whereColumn('model_has_roles.model_id', 'users.id')
                                    ->whereIn('model_has_roles.role_id', function ($subQuery) {
                                        $subQuery->select('id')
                                            ->from('roles')
                                            ->where('name', 'Pharmaceutical Company');
                                    });
                            });
                    })

                    ->whereNull('users.deleted_at')
                    ->groupBy('users.parent_id', 'parent_users.name');
                return $data;
            })
            ->columns([

                Tables\Columns\TextColumn::make('parent_name')
                    ->label('Pharmaceutical Company')
                    ->url(fn($record) => ShowRolePermissionUsers::getUrl(['record' => $record->id]))
                    ->sortable()
                    ->toggleable()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->where('parent_users.name', 'ILIKE', "%{$search}%");
                    }),
                ViewColumn::make('created_roles_count')
                    ->label('Number of Roles Created')
                    ->sortable()
                    ->url(fn($record) => ShowRolePermissionUsers::getUrl(['record' => $record->id]))
                    ->view('tables.columns.created-roles-list-modal') // Blade view we’ll create next
                    ->toggleable()
                // ->disableClick(),

            ])->defaultSort('parent_name', 'asc')
            ->filters([
                SelectFilter::make('users.parent_id')
                    ->label('Pharmaceutical Company')
                    ->column('users.parent_id')
                    ->options(function () {
                        return User::query()
                            ->select('users.parent_id as id', 'parent_users.name as parent_name')
                            ->leftJoin('users as parent_users', 'users.parent_id', '=', 'parent_users.id')
                            ->whereIn('users.parent_id', function ($query) {
                                $query->select('id')
                                    ->from('users')
                                    ->whereExists(function ($roleQuery) {
                                        $roleQuery->select(DB::raw(1))
                                            ->from('model_has_roles')
                                            ->whereColumn('model_has_roles.model_id', 'users.id')
                                            ->whereIn('model_has_roles.role_id', function ($subQuery) {
                                                $subQuery->select('id')
                                                    ->from('roles')
                                                    ->where('name', 'Pharmaceutical Company');
                                            });
                                    });
                            })
                            ->whereNull('users.deleted_at')
                            ->groupBy('users.parent_id', 'parent_users.name')
                            ->pluck('parent_name', 'id')
                            ->sort() // 'id' is the alias for users.parent_id
                            ->toArray();
                    })->searchable()
                    ->preload()

            ])
            ->actions([
                Tables\Actions\Action::make('viewRoles')
                    ->iconButton()
                    ->color('gray')
                    ->url(fn($record) => ShowRolePermissionUsers::getUrl(['record' => $record->id]))
                    ->icon('heroicon-o-eye')
                    ->visible(function (Model $record) {
                        $user = auth()->user();
                        return ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('roles_view'));
                    })
                    ->tooltip(function (Model $record) {
                        return "View";
                    }),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRolePermissions::route('/'),
            'create' => Pages\CreateRolePermission::route('/create'),
            'edit' => Pages\EditRolePermission::route('/{record}/edit'),
            'view' => Pages\ViewRolePermission::route('/{record}'),
            'user-list' => Pages\ShowRolePermissionUsers::route('/{record}/roles-users'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        if (Auth::user()->hasRole('Pharmaceutical Company')) {
            return Role::count();
        }

        return null;
    }
}
