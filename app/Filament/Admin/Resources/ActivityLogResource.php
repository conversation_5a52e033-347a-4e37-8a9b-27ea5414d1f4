<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\ActivityLogResource\Pages;
use App\Models\PcDetail;
use App\Models\User;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Activitylog\Models\Activity;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;

class ActivityLogResource extends Resource
{
    protected static ?string $model = Activity::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $label = 'Activity Logs';

    public static function getNavigation(): array
    {
        return [
            'label' => 'Activity Logs',
            'icon' => 'heroicon-o-clipboard-list',
        ];
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }
    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');


        return $table
            ->columns([
                TextColumn::make('updated_at')
                    ->label('Date')
                    ->sortable()
                    ->formatStateUsing(function ($state) use ($timeZone) {
                        return Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                            ->setTimezone($timeZone)
                            ->format('M d, Y | h:i A');
                    }),

                TextColumn::make('description')
                    ->label('Task')
                    ->wrap()
                    ->sortable()
                    ->searchable()
                    ->formatStateUsing(fn(string $state) => Str::ucfirst(Str::lower($state))),

                TextColumn::make('causer.name')
                    ->label('Perform Made by')
                    ->getStateUsing(function ($record) {
                        if ($record->causer && $record->causer->id === 1) {
                            return 'DPharma';
                        }
                        
                        if (!$record->causer) {
                            return 'Unknown';
                        }
                        
                        $causer = $record->causer;
                        if ($causer->hasRole('Pharmaceutical Company')) {
                            $pcDetails = PcDetail::where('user_id', $causer->id)->first();
                            
                            if ($pcDetails) {
                                return !empty($pcDetails->company_name) 
                                    ? $pcDetails->company_name 
                                    : ($pcDetails->business_name ?? 'Unknown Company');
                            }
                        }
                        return $causer->name ?? 'Unknown';
                    })
                   ->sortable(query: function (Builder $query, string $direction): Builder {
                        return $query
                            ->join('users', function ($join) {
                                $join->on('activity_log.causer_id', '=', 'users.id')
                                    ->where('activity_log.causer_type', '=', \App\Models\User::class);
                            })
                            ->orderByRaw("CASE WHEN users.id = 1 THEN 'Dpharma' ELSE users.name END {$direction}");
                    })
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        $search = trim($search); 
                        if (empty($search)) {
                            return $query;
                        }

                        return $query->where(function (Builder $query) use ($search) {
                            $query->where('activity_log.causer_type', \App\Models\User::class)
                                ->where(function (Builder $query) use ($search) {
                                    if (stripos('DPharma', $search) !== false) {
                                        $query->orWhere('activity_log.causer_id', 1);
                                    }
                                    $query->orWhereHas('causer', function (Builder $query) use ($search) {
                                        $query->where('name', 'like', "%{$search}%");
                                    });
                                    $query->orWhereHas('causer', function (Builder $query) use ($search) {
                                        $query->whereHas('roles', function (Builder $roleQuery) {
                                            $roleQuery->where('name', 'Pharmaceutical Company');
                                        })->whereHas('pcDetails', function (Builder $pcQuery) use ($search) {
                                            $pcQuery->where(function ($q) use ($search) {
                                                $q->where('company_name', 'like', "%{$search}%")
                                                ->orWhere('business_name', 'like', "%{$search}%");
                                            });
                                        });
                                    });
                                });
                        });
                    }),
                TextColumn::make('ip_address')
                    ->label('IP Address')
                    ->searchable()
                    ->sortable()
                    ->default('N/A'),

            ])
            ->defaultSort('updated_at', 'desc')
            ->actionsColumnLabel('Actions')
            ->filters([
                Filter::make('custom_date_range')
                    ->form([
                        DateTimePicker::make('start_date')
                            ->label('Start Date & Time')
                            // ->native(false)
                            ->seconds(false)
                            ->timezone($timeZone)
                            ->displayFormat('M d, Y | h:i A')
                            ->maxDate(now($timeZone))
                            ->closeOnDateSelection(false)
                            ->reactive()
                            ->afterStateUpdated(function ($set, $state, $get) use ($timeZone) {
                                $endDate = $get('end_date');
                                if ($state && $endDate) {
                                    try {
                                        $startDateTime = Carbon::parse($state, $timeZone);
                                        $endDateTime = Carbon::parse($endDate, $timeZone);
                                        
                                        // Only clear end date if it's clearly before start date (different days)
                                        if ($endDateTime->lt($startDateTime) && !$endDateTime->isSameDay($startDateTime)) {
                                            $set('end_date', null);
                                        }
                                    } catch (\Exception $e) {
                                        // Handle any parsing errors silently
                                    }
                                }
                            }),

                        DateTimePicker::make('end_date')
                            ->label('End Date & Time')
                            // ->native(false)
                            ->seconds(false)
                            ->timezone($timeZone)
                            ->closeOnDateSelection(false)
                            ->displayFormat('M d, Y | h:i A')
                            ->minDate(function ($get) use ($timeZone) {
                                $startDate = $get('start_date');
                                if ($startDate) {
                                    try {
                                        return Carbon::parse($startDate, $timeZone)->startOfDay();
                                    } catch (\Exception $e) {
                                        return null;
                                    }
                                }
                                return null;
                            })
                            ->maxDate(now($timeZone))
                            ->rules([
                                function ($get) use ($timeZone) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get, $timeZone) {
                                        $startDate = $get('start_date');
                                        if ($value && $startDate) {
                                            try {
                                                $startDateTime = Carbon::parse($startDate, $timeZone);
                                                $endDateTime = Carbon::parse($value, $timeZone);
                                                
                                                if ($endDateTime->lte($startDateTime)) {
                                                    $fail('End date must be after start date.');
                                                }
                                            } catch (\Exception $e) {
                                                $fail('Invalid date format.');
                                            }
                                        }
                                    };
                                }
                            ]),
                    ])
                    ->query(function ($query, array $data) use ($timeZone) {
                        if (!empty($data['start_date'])) {
                            $start = Carbon::parse($data['start_date'], $timeZone)->startOfMinute()->setTimezone('UTC');
                            $query->where('updated_at', '>=', $start);
                        }
                        
                        if (!empty($data['end_date'])) {
                            $end = Carbon::parse($data['end_date'], $timeZone)->endOfMinute()->setTimezone('UTC');
                            $query->where('updated_at', '<=', $end);
                        }
                    })
                    ->indicateUsing(function (array $data) use ($timeZone): ?string {
                        $indicators = [];
                        
                        if (!empty($data['start_date'])) {
                            $indicators[] = 'From: ' . Carbon::parse($data['start_date'], $timeZone)->format('M d, Y | h:i A');
                        }
                        
                        if (!empty($data['end_date'])) {
                            $indicators[] = 'To: ' . Carbon::parse($data['end_date'], $timeZone)->format('M d, Y | h:i A');
                        }
                        
                        return !empty($indicators) ? implode(' - ', $indicators) : null;
                    }),
                    Filter::make('causer.name')
                    ->form([
                        Select::make('causer_id')
                            ->label('User')
                            ->options(function ($livewire) {
                                $activeTab = $livewire->activeTab ?? 'Admin';
                                $query = User::query()->whereNotNull('name');

                                if ($activeTab === 'Admin') {
                                    $query->where(function ($q) {
                                        $q->whereHas('roles', function ($roleQuery) {
                                            $roleQuery->where('name', 'Super Admin');
                                        })->orWhereHas('parent.roles', function ($parentRoleQuery) {
                                            $parentRoleQuery->where('name', 'Super Admin');
                                        });
                                    });
                                } elseif ($activeTab === 'Pharmaceutical Supplier') {
                                    $query->where(function ($q) {
                                        $q->whereHas('roles', function ($roleQuery) {
                                            $roleQuery->where('name', 'Pharmaceutical Company');
                                        })->orWhereHas('parent.roles', function ($parentRoleQuery) {
                                            $parentRoleQuery->where('name', 'Pharmaceutical Company');
                                        });
                                    });
                                } elseif ($activeTab === 'Facility') {
                                    $query->whereHas('roles', function ($roleQuery) {
                                        $roleQuery->where('name', 'Clinic');
                                    });
                                }

                                return $query
                                    ->orderBy('name', 'asc')
                                    ->get()
                                    ->mapWithKeys(function ($user) use ($activeTab) {
                                        if ($user->id === 1) {
                                            return [$user->id => 'DPharma'];
                                        }
                                        if ($activeTab === 'Pharmaceutical Supplier' && $user->hasRole('Pharmaceutical Company')) {
                                            $pcDetails = PcDetail::where('user_id', $user->id)->first();
                                            
                                            if ($pcDetails) {
                                                $displayName = !empty($pcDetails->company_name) 
                                                    ? $pcDetails->company_name 
                                                    : ($pcDetails->business_name ?? $user->name);
                                                return [$user->id => $displayName];
                                            }
                                        }
                                        
                                        return [$user->id => $user->name];
                                    })
                                    ->toArray();
                            })
                            ->searchable(),
                    ])
                    ->query(function ($query, array $data) {
                        if ($data['causer_id']) {
                            $query->where('causer_id', $data['causer_id']);
                        }
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['causer_id']) {
                            $user = User::find($data['causer_id']);
                            if (!$user) {
                                return 'User: Unknown';
                            }
                            if ($user->id === 1) {
                                return 'User: DPharma';
                            }
                            if ($user->hasRole('Pharmaceutical Company')) {
                                $pcDetails = PcDetail::where('user_id', $user->id)->first();
                                
                                if ($pcDetails) {
                                    $displayName = !empty($pcDetails->company_name) 
                                        ? $pcDetails->company_name 
                                        : ($pcDetails->business_name ?? $user->name);
                                    return 'User: ' . $displayName;
                                }
                            }
                            
                            return 'User: ' . $user->name;
                        }
                        return null;
                    }),
            ])
            ->filtersFormWidth(MaxWidth::Medium)
            ->actions([
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->url(function ($record, $livewire) {
                        $activeTab = $livewire->activeTab; //request()->query('activeTab');
                        $validTabs = ['Admin', 'Pharmaceutical Supplier', 'Facility'];
                        $activeTab = in_array($activeTab, $validTabs) ? $activeTab : 'Admin';

                        // Store in session for persistence
                        session(['activity_log_active_tab' => $activeTab]);

                        return static::getUrl('view', [
                            'record' => $record->id,
                            'activeTab' => $activeTab,
                        ]);
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])

            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListActivityLogs::route('/'),
            'view' => Pages\ViewActivityLog::route('/{record}/{activeTab?}'),
        ];
    }
}
