<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\BannerResource\Pages;
use App\Models\Banner;
use App\Models\Brand;
use App\Models\Category;
use App\Models\PcDetail;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\MarkdownEditor;
use Filament\Forms\Components\View;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists\Components\ViewEntry;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Validation\Rules\File;
use Illuminate\Support\HtmlString;
use Livewire\Component as Livewire;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

class BannerResource extends Resource
{
    protected static ?string $model = Banner::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $label = 'Banner Management';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 7;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('banners_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('banners_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('banners_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('banners_delete');
    }
    public static function form(Form $form): Form
    {
        $activeTab = session('banner_active_tab', 'Pop-up Banner');
        $defaultBannerType = match ($activeTab) {
            'Pop-up Banner' => 'popup_banner',
            'Main Banner' => 'main_banner',
            'Sub Banner' => 'sub_banner',
            default => 'popup_banner',
        };

        return $form
            ->schema([
                Section::make()->schema([
                    Group::make()
                        ->schema([
                            Section::make()
                                ->schema([
                                    Select::make('banner_type')
                                        ->label(new HtmlString("Banner Type <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                        ->live()
                                        ->placeholder('Select banner type')
                                        ->default($defaultBannerType)
                                        ->options([
                                            'popup_banner' => 'Pop-up Banner',
                                            'main_banner' => 'Main Banner',
                                            'sub_banner' => 'Sub Banner',
                                        ])->rules(['required'])
                                        ->validationMessages([
                                            'required' => __('message.banner.banner_type_required'),

                                        ]),

                                    Select::make('sequence')
                                        ->label(new HtmlString("Select Sequence<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                        ->visible(fn($get) => $get('banner_type') != 'popup_banner')
                                        ->options([
                                            '1' => '1',
                                            '2' => '2',
                                            '3' => '3',
                                            '4' => '4',
                                            '5' => '5',
                                        ])->rules(['required'])->validationMessages([
                                            'required' => __('message.banner.sequence_required'),

                                        ]),
                                    Select::make('redirect_to')
                                        ->placeholder('No Redirection')
                                        ->label(new HtmlString("Redirected To"))
                                        ->options([
                                            'seller' => 'Seller',
                                            'product' => 'Product',
                                            'brand' => 'Brand',
                                            'category' => 'Category',
                                        ])
                                        ->default(NULL)
                                        ->afterStateUpdated(function (Livewire $livewire, Set $set) {
                                            $set('redirect_to_id', null);
                                        })->live()
                                        ->rules(['required_unless:redirect_to,NULL'])
                                        ->validationMessages([
                                            'required_unless' => __('message.banner.redirect_to_required'),
                                        ]),

                                    Select::make('redirect_to_id')
                                        ->label(new HtmlString("Redirected On<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                        ->placeholder('Select Redirected On')
                                        ->default('Select Redirected On')

                                        ->options(function (Get $get, Set $set) {

                                            if (!empty($get('redirect_to')) && $get('redirect_to') == 'seller') {
                                                return PcDetail::pluck('business_name', 'id')->toArray();
                                            } elseif (!empty($get('redirect_to')) && $get('redirect_to') == 'brand') {
                                                return Brand::pluck('name', 'id')->toArray();
                                            } elseif (!empty($get('redirect_to')) && $get('redirect_to') == 'product') {
                                                return Product::pluck('name', 'id')->toArray();
                                            } elseif (!empty($get('redirect_to')) && $get('redirect_to') == 'category') {
                                                return Category::pluck('name', 'id')->toArray();
                                            }
                                            return [];
                                        })->visible(function (Get $get) {
                                            $redirectTo = $get('redirect_to');
                                            return $redirectTo && $redirectTo !== null;
                                        })
                                        ->rules(function (Get $get) {
                                            $redirectTo = $get('redirect_to');

                                            if ($redirectTo && $redirectTo !== null) {
                                                return ['required'];
                                            }

                                            return [];
                                        })
                                        ->validationMessages([
                                            'required' => __('message.banner.redirect_to_id_required'),
                                        ]),
                                    DateTimePicker::make('start_date')
                                        ->label(new HtmlString("Start Date<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                        ->placeholder('Select date & time')
                                        ->native(false)
                                        ->minDate(Carbon::today())
                                        ->rules([
                                            'required',
                                            'after_or_equal:today'
                                        ])
                                        ->before('end_date')
                                        ->validationAttribute('start date')
                                        ->live()
                                        ->closeOnDateSelection()
                                        ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                            $endDate = $get('end_date');
                                            if ($endDate && $state && Carbon::parse($state)->gt(Carbon::parse($endDate))) {
                                                $set('end_date', null);
                                            }
                                        })
                                        ->validationMessages([
                                            'required' => __('message.banner.start_date_required'),
                                            'before' => __('message.banner.start_date_before'),
                                            'after_or_equal' => __('message.banner.start_date_expired'),
                                        ]),

                                    DateTimePicker::make('end_date')
                                        ->label(new HtmlString("End Date<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                        ->placeholder('Select date & time')
                                        ->native(false)
                                        ->minDate(function (Get $get) {
                                            $startDate = $get('start_date');
                                            return $startDate ? Carbon::parse($startDate) : Carbon::today();
                                        })
                                        ->rules([
                                            'required',
                                            'after_or_equal:today'
                                        ])
                                        ->after('start_date')
                                        ->live()
                                        ->closeOnDateSelection()
                                        ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                            $startDate = $get('start_date');
                                            if ($startDate && $state && Carbon::parse($startDate)->gt(Carbon::parse($state))) {
                                                $set('start_date', null);
                                            }
                                        })
                                        ->validationMessages([
                                            'required' => __('message.banner.end_date_required'),
                                            'after' => __('message.banner.end_date_after'),
                                            'after_or_equal' => __('message.banner.end_date_expired'),
                                        ]),
                                ])
                                ->columns(2),

                        ])
                        ->columnSpan(['lg' => 2]),

                    Group::make()
                        ->schema([
                            Section::make(new HtmlString("Banner Image <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))

                                ->schema([
                                    FileUpload::make('image')->label('')
                                        ->rules([
                                            'required',
                                            'image',
                                            'mimes:jpeg,png,jpg',
                                            'dimensions:width=3840,height=1000'
                                        ])
                                        ->validationMessages([
                                            'required' => 'Image is required',
                                            'image' => 'The file must be an image.',
                                            'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                            'dimensions' => 'Image dimension must be 3840px x 1000px',
                                        ])
                                        ->maxSize(2048)
                                        ->validationAttribute('Banner Image')
                                        ->helperText('Supported formats: JPEG, PNG, JPG (2 MB each), Dimension: 3840px x 1000px')
                                        ->imageEditor()->required()
                                        ->directory('banners')
                                    // ->disk('public')

                                    // ->rules(['required', 'image', 'mimes:jpeg,png,jpg', 'max:2048']),

                                ]),

                        ])
                        ->columnSpan(['lg' => 1]),
                    Group::make()
                        ->schema(function ($record, $get) {
                            return [
                                Section::make('Banner Placement')
                                    ->schema([
                                        View::make('banner_image')
                                            ->view('filament.admin.resources.banner-resource.pages.banner-image')
                                            ->viewData([
                                                'type' => $record->banner_type ?? $get('banner_type'),
                                            ]),
                                    ]),
                            ];
                        })
                        ->columnSpan(['lg' => 2]),
                ])->columns(3)
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')->toggleable()
                    ->getStateUsing(function ($record) {
                        return $record->image ? Storage::disk('s3')->url($record->image) : null;
                    }),
                TextColumn::make('banner_type')->getStateUsing(function ($record) {
                    $status = $record->banner_type;

                    return $status ? ucwords(str_replace('_', ' ', $status)) : '-';
                })->toggleable()->searchable()->sortable(),
                TextColumn::make('redirect_to')->label('Redirected To')
                    ->searchable()->sortable()->toggleable()->default('No Redirection')->formatStateUsing(function ($record) {
                        return ucfirst($record->redirect_to);
                    }),
                TextColumn::make('end_date')->searchable()->sortable()->toggleable()->label('Expiry Date')
                    ->formatStateUsing(fn($state) => \Carbon\Carbon::parse($state)->format('M j, Y')),

                ToggleColumn::make('status')
                    ->toggleable()
                    ->sortable()
                    ->afterStateUpdated(function (Banner $record) {
                        if ($record->status && $record->banner_type == 'popup_banner') {
                            $overlappingBanners = Banner::where('banner_type', 'popup_banner')
                                ->where('status', true)
                                ->where('id', '!=', $record->id)
                                ->where(function ($query) use ($record) {
                                    $query->where(function ($q) use ($record) {
                                        $q->where('start_date', '<=', $record->start_date)
                                            ->where('end_date', '>=', $record->start_date);
                                    })->orWhere(function ($q) use ($record) {
                                        $q->where('start_date', '<=', $record->end_date)
                                            ->where('end_date', '>=', $record->end_date);
                                    })->orWhere(function ($q) use ($record) {
                                        $q->where('start_date', '>=', $record->start_date)
                                            ->where('end_date', '<=', $record->end_date);
                                    });
                                })
                                ->get();

                            if (count($overlappingBanners) > 0) {
                                Notification::make()
                                    ->danger()
                                    // ->title('Cannot Activate')
                                    ->title(__('message.banner.pop_up_status_validation_date'))
                                    ->send();

                                $record->status = false;
                                $record->save();
                            } else {
                                Notification::make()
                                    ->success()
                                    // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                                    ->duration(1000)
                                    ->title(__('message.banner.status_updated'))
                                    ->send();
                            }
                        } else {
                            Notification::make()
                                ->success()
                                // ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                                ->duration(1000)
                                ->title(__('message.banner.status_updated'))
                                ->send();
                        }
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('banner_type')
                    ->label('Banner Type')
                    ->options([
                        'popup_banner' => 'Pop-up Banner',
                        'main_banner' => 'Main Banner',
                        'sub_banner' => 'Sub Banner',
                    ]),
                SelectFilter::make('redirect_to')
                    ->label('Redirected To')
                    ->options([
                        'seller' => 'Seller',
                        'product' => 'Product',
                        'brand' => 'Brand',
                        'category' => 'Category',
                    ]),
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),

                Filter::make('end_date')
                    ->label('Expiry Date')
                    ->form([
                        DatePicker::make('created_from')->label('Expiry From')->reactive(),
                        DatePicker::make('created_until')->label('Expiry Until')->minDate(fn(callable $get) => $get('created_from')),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('end_date', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('end_date', '<=', $date),
                            );
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['created_from']) {
                            return null;
                        }
                        // return 'From ' . Carbon::parse($data['created_from'])->toFormattedDateString() . ' To ' . Carbon::parse($data['created_until'])->toFormattedDateString();
                        $from = Carbon::parse($data['created_from'])->toFormattedDateString();
                        $until = $data['created_until']
                            ? Carbon::parse($data['created_until'])->toFormattedDateString()
                            : 'Present';

                        return "From {$from} To {$until}";
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->url(function ($record, $livewire) {
                        $activeTab = $livewire->activeTab; //request()->query('activeTab');
                        $validTabs = ['Pop-up Banner', 'Main Banner', 'Sub Banner'];
                        $activeTab = in_array($activeTab, $validTabs) ? $activeTab : 'Pop-up Banner';

                        // Store in session for persistence
                        session(['banner_active_tab' => $activeTab]);

                        return static::getUrl('edit', [
                            'record' => $record->id,
                            'activeTab' => $activeTab,
                        ]);
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\Action::make('edit_date')
                    ->label('Edit Dates')
                    ->icon('heroicon-o-calendar')
                    ->size('sm')
                    ->iconButton()
                    ->tooltip('Edit Date')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->form([
                        DateTimePicker::make('start_date')
                            ->label(new HtmlString("Start Date<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder('Select date & time')
                            ->native(false)
                            ->minDate(Carbon::today())
                            ->closeOnDateSelection()
                            ->rules([
                                'required',
                                'after_or_equal:today' // Laravel's built-in validation rul
                            ])
                            ->before('end_date')
                            ->validationAttribute('start date')
                            ->live()
                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                $endDate = $get('end_date');
                                if ($endDate && $state && Carbon::parse($state)->gt(Carbon::parse($endDate))) {
                                    $set('end_date', null);
                                }
                            })
                            ->default(fn(Banner $record) => $record->start_date)
                            ->validationMessages([
                                'required' => __('message.banner.start_date_required'),
                                'before' => __('message.banner.start_date_before'),
                                'after_or_equal' => __('message.banner.start_date_expired'),
                            ]),
                        DateTimePicker::make('end_date')
                            ->label(new HtmlString("End Date<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->placeholder('Select date & time')
                            ->native(false)
                            ->minDate(function (Get $get) {
                                $startDate = $get('start_date');
                                return $startDate ? Carbon::parse($startDate) : Carbon::today();
                            })
                            ->rules([
                                'required',
                                'after_or_equal:today' 
                            ])
                            ->after('start_date')
                            ->live()
                            ->closeOnDateSelection()
                            ->afterStateUpdated(function (Set $set, $state, Get $get) {
                                $startDate = $get('start_date');
                                if ($startDate && $state && Carbon::parse($startDate)->gt(Carbon::parse($state))) {
                                    $set('start_date', null);
                                }
                            })
                            ->default(fn(Banner $record) => $record->end_date) 
                            ->validationMessages([
                                'required' => __('message.banner.end_date_required'),
                                'after' => __('message.banner.end_date_after'),
                                'after_or_equal' => __('message.banner.end_date_expired'),
                            ]),
                    ])
                    ->action(function (Banner $record, array $data) {
                        // Update the record with new dates
                        $record->update([
                            'start_date' => $data['start_date'],
                            'end_date' => $data['end_date'],
                        ]);

                        if ($record->banner_type === 'popup_banner' && $record->status) {
                            $overlappingBanners = Banner::where('banner_type', 'popup_banner')
                                ->where('status', true)
                                ->where('id', '!=', $record->id)
                                ->where(function ($query) use ($record) {
                                    $query->where(function ($q) use ($record) {
                                        $q->where('start_date', '<=', $record->start_date)
                                            ->where('end_date', '>=', $record->start_date);
                                    })->orWhere(function ($q) use ($record) {
                                        $q->where('start_date', '<=', $record->end_date)
                                            ->where('end_date', '>=', $record->end_date);
                                    })->orWhere(function ($q) use ($record) {
                                        $q->where('start_date', '>=', $record->start_date)
                                            ->where('end_date', '<=', $record->end_date);
                                    });
                                })
                                ->count();

                            if ($overlappingBanners > 0) {
                                $record->update(['status' => false]);
                                Notification::make()
                                    ->danger()
                                    ->title(__('message.banner.pop_up_status_validation_date'))
                                    ->send();
                                return;
                            }
                        }

                        Notification::make()
                            ->success()
                            ->title('Dates updated successfully')
                            ->send();
                    })
                    ->requiresConfirmation(),
                Tables\Actions\DeleteAction::make()
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title(__('message.banner.title.deleted'))
                            ->title(__('message.banner.delete_success'))
                    ),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $records->each->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.banner.title.deletion_completed'))
                            ->title(__('message.banner.bulk_delete_success'))
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function (Collection $records) {
                        $popupBanners = $records->filter(fn($record) => $record->banner_type === 'popup_banner');

                        if ($popupBanners->isNotEmpty()) {
                            $dateRanges = [];

                            foreach ($popupBanners as $banner) {
                                $dateRanges[] = [
                                    'id' => $banner->id,
                                    'start_date' => $banner->start_date,
                                    'end_date' => $banner->end_date
                                ];
                            }

                            $hasOverlap = false;
                            $count = count($dateRanges);

                            for ($i = 0; $i < $count; $i++) {
                                for ($j = $i + 1; $j < $count; $j++) {
                                    if (
                                        ($dateRanges[$i]['start_date'] <= $dateRanges[$j]['end_date']) &&
                                        ($dateRanges[$i]['end_date'] >= $dateRanges[$j]['start_date'])
                                    ) {
                                        $hasOverlap = true;
                                        break 2;
                                    }
                                }
                            }

                            if ($hasOverlap) {
                                Notification::make()
                                    ->danger()
                                    // ->title(__('message.banner.title.saved'))
                                    ->title(__('message.banner.pop_up_overlap_selected'))
                                    ->send();
                                return;
                            }
                            foreach ($popupBanners as $banner) {
                                $overlappingBanners = Banner::where(['banner_type' => 'popup_banner', 'status' => true])
                                    ->where('id', '!=', $banner->id)
                                    ->where(function ($query) use ($banner) {
                                        $query->where(function ($q) use ($banner) {
                                            $q->where('start_date', '>=', $banner->start_date)
                                                ->where('start_date', '<=', $banner->end_date);
                                        })->orWhere(function ($q) use ($banner) {
                                            $q->where('end_date', '>=', $banner->start_date)
                                                ->where('end_date', '<=', $banner->end_date);
                                        })->orWhere(function ($q) use ($banner) {
                                            $q->where('start_date', '<=', $banner->start_date)
                                                ->where('end_date', '>=', $banner->end_date);
                                        });
                                    })
                                    ->count();

                                if ($overlappingBanners > 0) {
                                    Notification::make()
                                        ->danger()
                                        // ->title(__('message.banner.title.saved'))
                                        ->title(__('message.banner.pop_up_status_validation_date'))
                                        ->send();
                                    return;
                                }
                            }
                        }
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });

                        Notification::make()
                            // ->title(__('message.banner.title.activated'))
                            ->title(__('message.banner.bulk_activate_success'))
                            ->success()
                            ->send();
                    })
                    ->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $records->each(function ($record) {
                            $record->update(['status' => false]);
                        });
                        Notification::make()
                            // ->title(__('message.banner.title.deactivated'))
                            ->title(__('message.banner.bulk_inactivate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBanners::route('/'),
            'create' => Pages\CreateBanner::route('/create'),
            'edit' => Pages\EditBanner::route('/{record}/edit/{activeTab?}'),
        ];
    }
}
