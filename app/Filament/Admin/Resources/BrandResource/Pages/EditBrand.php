<?php

namespace App\Filament\Admin\Resources\BrandResource\Pages;

use App\Filament\Admin\Resources\BrandResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditBrand extends EditRecord
{
    protected static string $resource = BrandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(BrandResource::getUrl()),

        ];
    }
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.brand.title.updated'))
            ->title(__('message.brand.update_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getSaveFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Brands",
            3 => "Edit Brand",
        ];
    }
}
