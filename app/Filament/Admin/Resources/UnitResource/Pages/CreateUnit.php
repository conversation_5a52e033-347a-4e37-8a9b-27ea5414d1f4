<?php

namespace App\Filament\Admin\Resources\UnitResource\Pages;

use App\Filament\Admin\Resources\UnitResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;

class CreateUnit extends CreateRecord
{
    protected static string $resource = UnitResource::class;
    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
    protected function getHeaderActions(): array
    {
        return [
         Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(UnitResource::getUrl()),
        ];
    }
    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            // ->title(__('message.unit.title.created'))
            ->body(__('message.unit.create_success'));
    }
    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Save'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            1 => "Master",
            $this->getResource()::getUrl('index') => "Volume Unit",
            3 => "Add Volume Unit",
        ];
    }

    public function getTitle(): string
    {
        return 'Add Volume Unit';
    }
}
