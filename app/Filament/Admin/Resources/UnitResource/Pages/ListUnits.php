<?php

namespace App\Filament\Admin\Resources\UnitResource\Pages;

use App\Filament\Admin\Resources\UnitResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUnits extends ListRecords
{
    protected static string $resource = UnitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('+ Add Volume Unit'),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Master",
            // $this->getResource()::getUrl('index') => "Units",
            // 3 => "List",
        ];
    }
    public function getTitle(): string
    {
        return 'Volume Unit';
    }
}
