<?php

namespace App\Filament\Admin\Resources\PendingPsRequestResource\Pages;

use App\Filament\Admin\Resources\PendingPsRequestResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPendingPsRequests extends ListRecords
{
    protected static string $resource = PendingPsRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Pending Request',
            // PendingPsRequestResource::getUrl() => 'PS Edit Request',
            // 2 => 'List',
        ];
    }

    public function getTitle(): string
    {
        return 'Pharma Supplier Update Requests';
    }
    
}
