<?php

namespace App\Filament\Admin\Resources\InquiryResource\Pages;

use App\Filament\Admin\Resources\InquiryResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;
use Filament\Pages\Actions\Action;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ViewInquiry extends ViewRecord
{
    protected static string $resource = InquiryResource::class;

    public function getTitle(): string
    {
        return $this->record->name;
    }

    public function mount(int | string $record): void
    {
        parent::mount($record);
        if (is_null($this->record->read_at)) {
            DB::table('inquiries')
                ->where('id', $this->record->id)
                ->whereNull('read_at')
                ->update(['read_at' => now()]);
            
            $this->record->refresh();
        }
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        try {
            return $infolist
                ->schema([
                    Section::make('')
                        ->schema([
                            Grid::make(5)
                                ->schema([
                                    // TextEntry::make('id')
                                    //     ->label('Inquiry ID')
                                    //     ->formatStateUsing(fn(string $state): string => "#{$state}"),
                                    TextEntry::make('name')
                                        ->label('Name'),
                                        TextEntry::make('landline_number')
                                        ->label('Mobile number')
                                        ->state(function ($record) {
                                            if (blank($record->landline_number)) {
                                                return '-';
                                            }
                                    
                                            $code = $record->code ? '+'.$record->code : '';
                                            return trim("{$code} {$record->landline_number}");
                                        })
                                        ->icon(fn($record) => blank($record->landline_number) ? null : 'heroicon-m-phone'),                                                                    
                                    TextEntry::make('email')
                                        ->label('Email'),
                                    TextEntry::make('type')
                                        ->label('Type')->formatStateUsing(fn($state) => strtoupper($state)),
                                    TextEntry::make('created_at')
                                        ->label('Date & Time')
                                        ->formatStateUsing(fn($state) => $state->setTimezone($timeZone) ->format('M d, Y | h:i A')),
                                ]),
                            TextEntry::make('subject')
                                ->columnSpanFull(),                            
                            TextEntry::make('description')
                                ->label('Description')
                                ->columnSpanFull()
                                 ->extraAttributes(['class' => 'break-all']),
                        ])
                        ->columns(1),
                ]);
        } catch (\Throwable $th) {
            // dd($th);
        }
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Inquiries',
            1 => 'Inquiry Details',
        ];
    }


    protected function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(InquiryResource::getUrl('index')),
        ];
    }
}
