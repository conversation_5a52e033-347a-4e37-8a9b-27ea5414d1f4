<?php

namespace App\Filament\Admin\Resources\InquiryResource\Pages;

use App\Filament\Admin\Resources\InquiryResource;
use App\Models\Inquiry;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListInquiries extends ListRecords
{
    protected static string $resource = InquiryResource::class;

    public function getTitle(): string
    {
        return 'Inquiries' . ' (' . Inquiry::whereNull('read_at')->count() . ' Unread)';
    }
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Inquiries",
            // 2 => "List",
        ];
    }
}
