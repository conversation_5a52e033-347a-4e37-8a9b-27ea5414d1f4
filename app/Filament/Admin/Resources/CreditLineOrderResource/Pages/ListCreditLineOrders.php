<?php

namespace App\Filament\Admin\Resources\CreditLineOrderResource\Pages;

use App\Filament\Admin\Resources\CreditLineOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCreditLineOrders extends ListRecords
{
    protected static string $resource = CreditLineOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //  Actions\CreateAction::make(),
        ];
    }
    public function getTitle(): string
    {
        return 'Credit Line Orders';
    }
    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Orders Management",
            // $this->getResource()::getUrl('index') => "Credit Line Orders",
            // 2 => "List",

        ];
    }
}
