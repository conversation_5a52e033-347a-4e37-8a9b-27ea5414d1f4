<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\OutstandingPaymentResource\Pages;
use App\Filament\Admin\Resources\OutstandingPaymentResource\RelationManagers;
use App\Jobs\ReceivedConfirmationMailJob;
use App\Mail\SendReminderMail;
use App\Models\OutstandingPayment;
use Filament\Tables\Columns\TextColumn;
use App\Models\Payout;
use Filament\Forms\Form;
use Carbon\Carbon;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Resources\Resource;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;
use Filament\Notifications\Notification;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\Section as InfoSection;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\TextInput;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Textarea;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Get;
use Illuminate\Support\Facades\Mail;
use App\Models\PcDetail;

class OutstandingPaymentResource extends Resource
{
    protected static ?string $model = Payout::class;
    protected static ?string $navigationGroup = 'Finance';
    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationIcon = '';
    protected static ?string $navigationLabel = 'Outstanding Payments';
    protected static ?string $label = 'Outstanding Payments';
    protected static ?int $navigationSort = 4;
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }
    public static function infolist(Infolist $infolist): Infolist
    {

        return $infolist
            // Removed extraAttributes for AlpineJS tab handling
            ->schema(function ($record) {
                $record = $record->load('user', 'payoutSubOrders');
                $start = Carbon::parse($record->start_date);
                $end = Carbon::parse($record->end_date);

                $monthYear = $start->format('M');
                $startFormatted = $start->format('jS M');
                $endFormatted = $end->format('jS M');

                $headerTitle =  "{$monthYear} ({$startFormatted} - {$endFormatted})";
                return [
                    InfoSection::make($headerTitle)
                        ->schema([
                            \Filament\Infolists\Components\Grid::make([
                                'default' => 5,
                                'sm' => 5,
                            ])
                                ->columnSpan('full')
                                ->extraAttributes(['class' => 'gap-0'])
                                ->schema([


                                    \Filament\Infolists\Components\Grid::make([
                                        'default' => 4,
                                        'sm' => 4,
                                    ])
                                        ->columnSpan(4)
                                        ->schema([
                                            TextEntry::make('id')
                                                ->label('Payout ID'),
                                            TextEntry::make('payout_on')
                                                ->label('Payout On')->dateTime('M d, Y'),
                                            TextEntry::make('user.pcdetails.business_name')
                                            ->formatStateUsing(function ($record) {
                                                return ucfirst(pcCompanyName($record->user->pcdetails));
                                            })
                                                ->label('Pharma Supplier'),
                                            TextEntry::make('payment_type')
                                                ->label('Payment Type')->formatStateUsing(fn($state) => blank($state) ? '-' : ucfirst($state)),
                                            TextEntry::make('payoutSubOrders')
                                                ->label('Total Orders')
                                                ->formatStateUsing(function ($record) {
                                                    $count = $record->payoutSubOrders->count();
                                                    return $count;
                                                }),
                                            TextEntry::make('payoutSubOrders')
                                                ->label('Payout Amount')
                                                ->formatStateUsing(function ($record) {
                                                    $amount = $record->payoutSubOrders->sum(fn($subOrder) => $subOrder->order->amount ?? 0);
                                                    return 'RM ' . number_format($amount, 2);
                                                }),
                                            TextEntry::make('transaction_id')
                                                ->label('Transaction ID'),
                                            TextEntry::make('outstanding_commission_status')
                                                ->label('Payout Status')->formatStateUsing(fn($state) => blank($state) ? '-' : ucfirst($state)),

                                        ])
                                ])
                        ]),


                ];
            });
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return Payout::query()
                    ->where(['payout_type' => 'full', 'is_payout' => true, 'is_received' => false])
                    ->with(['user', 'payoutSubOrders.order', 'payoutSubOrders.subOrder'])
                    ->withSum('orders', 'amount')
                    ->withCount('payoutSubOrders');
            })
            ->defaultSort('id','desc')
            ->columns([
                TextColumn::make('id')
                    ->label('ID')->sortable()->searchable()->toggleable()
                    ->formatStateUsing(fn(string $state): string => !empty($state) ? '<span style="color: blue;">#' . $state . '</span>' : '<span style="color: blue;">-</span>')->html(),
                TextColumn::make('user.pcDetails.business_name')
                ->formatStateUsing(function ($record) {
                    return ucfirst(pcCompanyName($record->user->pcdetails));
                })
                    ->label('Pharma Supplier')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('start_date')
                    ->label('Payout Cycle')
                    ->toggleable()
                    // ->sortable()
                    ->searchable()
                    ->formatStateUsing(function ($record) {
                        if (!$record->start_date || !$record->end_date) {
                            return '—';
                        }
                        $start = Carbon::parse($record->start_date);
                        $end = Carbon::parse($record->end_date);
                        $monthYear = $start->format('M Y');
                        $startFormatted = $start->format('jS M');
                        $endFormatted = $end->format('jS M');
                        return "{$monthYear} ({$startFormatted} - {$endFormatted})";
                    }),
                TextColumn::make('payout_sub_orders_count')
                    ->label('Items')
                    ->toggleable()
                    ->sortable()
                    ->formatStateUsing(fn($state) => $state),
                TextColumn::make('orders_sum_amount')
                    ->label('Order Total')
                    ->sortable()
                    ->toggleable()
                    ->default('-')
                    ->formatStateUsing(fn($state) => 'RM ' . number_format($state ?? 0, 2)),
                TextColumn::make('is_received')->label('Admin Fee')->toggleable()
                    ->sortable()
                    ->formatStateUsing(function ($record) {
                        $amount = $record->payoutSubOrders
                            ->map(function ($payoutSubOrder) {
                                return $payoutSubOrder->subOrder?->orderProducts->sum('total_commission') ?? 0;
                            })
                            ->sum();
                        return 'RM ' . number_format($amount, 2);
                    }),
                TextColumn::make('is_payout')->label('Payout Status')->toggleable()
                    ->sortable()
                    ->formatStateUsing(function ($state) {
                        if ($state) {
                            return 'Yes';
                        } else {
                            return 'No';
                        }
                    }),
            ])
            ->filters([

                // SelectFilter::make('user_id')
                //     ->label('Pharma Supplier')
                //     ->relationship('user.pcDetails', 'business_name')
                //     ->searchable()
                //     ->preload(),

                SelectFilter::make('user.pcdetails.business_name')
                    ->label('Pharmaceutical Suppliers')
                    ->multiple()
                    ->options(function () {
                        return PcDetail::with('companyType')
                            ->get()
                            ->mapWithKeys(function ($pc) {
                                $companyTypeName = optional($pc->companyType)->name;
                                $label = !empty($pc->company_name)
                                    ? $pc->company_name
                                    : (($companyTypeName === 'Sole Proprietary' && !empty($pc->business_name))
                                        ? $pc->business_name
                                        : null);
                                return $label ? [$pc->user_id => ucfirst($label)] : [];
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        $supplierIds = $data['values'] ?? [];
                        if (!empty($supplierIds)) {
                            // $query->whereHas('subOrder', function ($q) use ($supplierIds) {
                                $query->whereIn('user_id', $supplierIds);
                            // });
                        }
                        return $query;
                    }),

                SelectFilter::make('is_payout')
                    ->label('Payout Status')
                    ->options([
                        1 => 'Yes',
                        0 => 'No',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\Action::make('configure')
                    ->label('')
                    ->extraAttributes(['class' => '-m-0', 'style' => 'margin:0 !important'])
                    ->icon('heroicon-s-check')->size('sm')->iconButton()
                    ->color('success')
                    ->visible(fn($record) => $record->is_received != true)
                    ->modalHeading('Mark as Recieved')
                    ->modalButton('Save') // <-- Add this line
                    ->tooltip('Mark as Recieved')
                    ->action(function ($record, $data) {
                        $data['is_received'] = true;
                        $data['outstanding_commission_status'] = 'received';
                        $record->update($data);
                         //Activitylog start
                        activity()
                        ->causedBy(auth()->user())
                        ->useLog('outstanding_status_update')
                        ->performedOn($record)
                        ->withProperties([
                            'old' => [
                                'status' => 'pending',
                            ],
                            'attributes' => [
                                'status' => 'recieved',
                            ],
                        ])
                        ->log("{$record->user->pcDetails->business_name} outstanding payment has been recieved ");
                    //Activitylog end
                        ReceivedConfirmationMailJob::dispatch($record);

                        //Activity Log Start
                        // activity()
                        //     ->causedBy(auth()->user())
                        //     ->performedOn($record)
                        //     ->useLog('payout_status_update')
                        //     ->withProperties([
                        //         'attributes' => array_filter([
                        //             'outstanding_commission_status' => 'received',
                        //             'received_on' => $data['received_on'],
                        //             'received_marked' => $data['received_marked'],
                        //         ], fn($value) => !is_null($value)),
                        //     ])
                        //     ->log(
                        //         $record->id
                        //             ? "Payout #{$record->id} marked as received"
                        //             : "Payout marked as received"
                        //     );
                        //Activity Log Start

                        Notification::make()
                            ->title('Payment Recieved')
                            ->body('The payout mark as recieved successfully.')
                            ->success()
                            ->send();
                    })
                    ->form([
                        Section::make()
                            ->schema([
                                DatePicker::make('received_on')
                                    ->label(new HtmlString('Recieved On <span style="color:red">*</span>'))
                                    ->validationAttribute('Recieved On')
                                    ->maxDate(now())
                                    ->placeholder('dd/mm/yy')
                                    ->rules(['required']),
                                Textarea::make('received_marked')
                                    ->label('Remarks')
                                    ->placeholder('Enter remarks'),
                            ])
                            ->columns(1),
                    ]),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->tooltip('View')
                    ->extraAttributes(['class' => '-m-0','style'=>'margin:0 !important']),



                Tables\Actions\Action::make('notification')
                    ->label('')
                    ->visible(fn($record) => $record->is_received != true)
                    ->icon('heroicon-o-bell') // solid bell
                    ->size('w-5 h-5')
                    ->extraAttributes(['class' => 'fi-icon-btn h-8 w-8 rounded-lg justify-center items-center text-white -m-0','style'=>'margin:0 !important'])
                    ->tooltip('Send Reminder')
                    ->action(function ($record) {
                        Mail::to($record->user->email)->send(new SendReminderMail($record->user, 'admin'));

                        //Activity Log Start
                        // activity()
                        //     ->causedBy(auth()->user())
                        //     ->performedOn($record)
                        //     ->useLog('payout_notification')
                        //     ->log(
                        //         $record->id
                        //             ? "Reminder sent for payout #{$record->id}"
                        //             : "Reminder sent for payout"
                        //     );
                        //Activity Log End
                    }),
                // Tables\Actions\Action::make('reject')
                //     ->label('')->tooltip('Reject')
                //     ->visible(fn($record) => $record->is_received != true)
                //     ->icon('heroicon-m-x-mark')
                //     ->size('w-5 h-5')
                //     ->extraAttributes(['class' => 'fi-color-danger fi-icon-btn h-8 w-8 rounded-lg justify-center items-center -m-0', 'style' => 'margin:0 !important'])
                //     ->color('white')
                //     ->action(function ($record) {
                //         $data['is_received'] = false;
                //         $data['outstanding_commission_status'] = 'rejected';
                //         $record->update($data);

                //         //Activity Log Start
                //         // activity()
                //         //     ->causedBy(auth()->user())
                //         //     ->performedOn($record)
                //         //     ->useLog('payout_status_update')
                //         //     ->withProperties([
                //         //         'attributes' => array_filter([
                //         //             'payout_id' => $record->id,
                //         //             'outstanding_commission_status' => 'rejected',
                //         //         ], fn($value) => !is_null($value)),
                //         //     ])
                //         //     ->log(
                //         //         $record->id
                //         //             ? "Payout #{$record->id} rejected"
                //         //             : "Payout rejected"
                //         //     );
                //         //Activity Log End
                //     })

            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOutstandingPayments::route('/'),
            'create' => Pages\CreateOutstandingPayment::route('/create'),
            'edit' => Pages\EditOutstandingPayment::route('/{record}/edit'),
            'view' => Pages\ViewOutstandingPayment::route('/{record}'),
        ];
    }
}
