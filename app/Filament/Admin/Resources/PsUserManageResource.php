<?php

namespace App\Filament\Admin\Resources;

use Filament\Forms;
use App\Models\Role;
use App\Models\User;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\AccountType;
use Filament\Actions\Action;
use Illuminate\Validation\Rule;
use Filament\Resources\Resource;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Indianic\FilamentShield\Resources\RoleResource;
use App\Filament\Admin\Resources\PsUserManageResource\Pages;
use App\Filament\Admin\Resources\PsUserManageResource\RelationManagers;
use App\Filament\Admin\Resources\PsUserManageResource\Pages\EditPsUserManage;
use App\Filament\Admin\Resources\PsUserManageResource\Pages\CreatePsUserManage;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Validation\Rules\Unique;

class PsUserManageResource extends Resource
{
    protected static ?string $model = User::class;

    public const STATUS_ACTIVE = 1;

    public const STATUS_INACTIVE = 0;

    protected static ?string $navigationGroup = "Users";
    // protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?int $navigationSort = 3;

    protected static ?string $label = 'PS User';

    protected static ?string $navigationGroupIcon = 'heroicon-o-users';

    // public static function canView(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('ps-users_view');
    // }
    // public static function canCreate(): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('ps-users_create');
    // }
    // public static function canEdit(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('ps-users_update');
    // }
    // public static function canDelete(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Super Admin') || auth()->user()->can('ps-users_delete');
    // }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('')
                    ->schema([
                        Grid::make()
                            ->schema([
                                FileUpload::make('photo')
                                    ->label('')
                                    ->image()
                                    ->imageEditor()
                                    ->circleCropper()
                                    ->directory('users')
                                    ->avatar()
                                    ->columnSpanFull()
                                    // ->required()
                                    ->alignCenter()
                                    ->rules(['image', 'mimes:jpg,jpeg,png', 'max:1280'])
                                    ->validationMessages([
                                        'required' => 'The profile image field is required.',
                                        'image' => 'The file must be an image.',
                                        'mimes' => 'Only JPG, JPEG, and PNG formats are allowed.',
                                        'max' => 'The image must not exceed 2MB.'
                                    ])

                            ])->extraAttributes([
                                'style' => 'text-align: center; '
                            ]),
                        Grid::make(5)
                            ->schema([
                                TextInput::make('name')
                                    ->placeholder('Full Name')
                                    ->label(new HtmlString("Full Name<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->rules(['required', 'regex:/^[A-Za-z0-9\s]+$/'])
                                    ->maxLength(50)
                                    ->validationMessages([
                                        'required' => 'The name field is required.',
                                        'regex' => 'The name must contain only letters, numbers, and spaces.',
                                        'max' => 'The name must not exceed 255 characters.'
                                    ]),
                                // TextInput::make('email')
                                //     ->placeholder('Enter Email')
                                //     ->label(new HtmlString("Email<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                //     ->rules(function ($record) {
                                //         if (!empty($record->id)) {
                                //             return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', Rule::unique('users', 'email')->ignore($record->id, 'id')];
                                //         }
                                //         return ['required', 'email', 'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/', 'max:100', 'unique:users,email'];
                                //     })
                                //     ->validationMessages([
                                //         'required' => 'Please enter an email address.',
                                //         'email' => 'Please enter a valid email address.',
                                //         'unique' => 'This email is already registered.',
                                //         'regex' => 'Please enter a valid email address.'
                                //     ]),
                                TextInput::make('email')
                                    ->label(new HtmlString("Email <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->placeholder("Enter Email")
                                    ->rules(['required', 'email'])
                                    ->unique(
                                        table: User::class,
                                        column: 'email',
                                        ignoreRecord: true,
                                        modifyRuleUsing: function (Unique $rule) {
                                            return $rule->whereNull('deleted_at');
                                        }
                                    )
                                    ->validationMessages([
                                        'required' => 'Please enter an email address.',
                                        'email' => 'Please enter a valid email address.',
                                        'unique' => 'This email is already registered.'
                                    ])->afterStateUpdated(
                                        function (Get $get, Set $set) {
                                            $set('email', \Str::lower($get('email')));
                                        }
                                    )->disabled(fn() => $form->getOperation() === 'edit')
                                    ->live(),


                                TextInput::make('phone')
                                    ->numeric()
                                    ->prefix('+60')
                                    ->live()
                                    ->mask('999999999999')
                                    ->stripCharacters(['-'])
                                    ->extraAttributes([
                                        'inputmode' => 'numeric',
                                        'maxlength' => '12'
                                    ])
                                    ->validationMessages([
                                        'digits_between' => 'The phone number must be between :min and :max characters.',
                                        'required' => 'The phone number field is required.',
                                    ])
                                    ->label(new HtmlString('<span style="font-size: 14px !important;">Enter phone number</span> <span class="font-medium text-danger-600 dark:text-danger-400">*</span>'))
                                    ->placeholder('Phone Number')
                                    ->suffixIcon(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'heroicon-s-check-circle' : null)
                                    ->suffixIconColor(fn($state) => strlen($state) >= 8 && strlen($state) <= 12 ? 'success' : null)
                                    ->rules(['required', 'digits_between:8,12']),

                                Select::make('parent_id')
                                    ->label(new HtmlString("Pharmaceutical Company<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                                    ->options(function () {
                                        $pharmaceuticalCompanyRole = Role::where('name', 'Pharmaceutical Company')->first();

                                        if (!$pharmaceuticalCompanyRole) {
                                            return [];
                                        }
                                        return User::query()
                                            ->whereNull('parent_id')
                                            ->whereHas('roles', function ($query) use ($pharmaceuticalCompanyRole) {
                                                $query->where('role_id', $pharmaceuticalCompanyRole->id);
                                            })
                                            ->whereNotNull('name')
                                            ->whereHas('pcDetails') // ensure user_id exists in pc_details
                                            ->with('pcDetails') // eager load for company_type_id and business_name
                                            ->get()
                                            ->mapWithKeys(function ($user) {
                                                $pcDetail = $user->pcDetails;

                                                $displayName = ($pcDetail && $pcDetail->company_type_id == 1)
                                                    ? ($pcDetail->business_name ?? '[No Business Name]')
                                                    : ($user->name ?? '[No Name]');

                                                return [$user->id => $displayName];
                                            })
                                            ->toArray();
                                        // return User::query()
                                        //     ->whereNull('parent_id')
                                        //     ->whereHas('roles', function ($query) use ($pharmaceuticalCompanyRole) {
                                        //         $query->where('role_id', $pharmaceuticalCompanyRole->id);
                                        //     })
                                        //     ->whereNotNull('name')
                                        //     ->get()
                                        //     ->mapWithKeys(fn($user) => [$user->id => $user->name ?? '[No Name]'])
                                        //     ->toArray();
                                    })
                                    ->rules('required')
                                    ->validationMessages([
                                        'required' => 'Please select a pharmaceutical company.',
                                    ])
                                    ->searchable()
                                    ->preload()
                                    ->live()
                                    ->afterStateUpdated(fn($set) => $set('role', null)),
                                Select::make('role')
                                    ->label(new HtmlString("Role<span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))

                                    ->options(function (Get $get) {
                                        if ($get('parent_id')) {
                                            $roles = Role::where('panel', 'pc')
                                                ->where('is_active', true)
                                                ->where('created_by', $get('parent_id'))
                                                ->get();
                                            $rolesToReturn = [];
                                            foreach ($roles as $role) {
                                                $rolesToReturn[$role->name] = $role->display_name ?? $role->name;
                                            }

                                            if (count($roles) < 1) {
                                                Notification::make()
                                                    ->body('No roles found for the selected pharmaceutical company.')
                                                    ->actions([
                                                        \Filament\Notifications\Actions\Action::make('create_role')
                                                            ->label('Create Role')
                                                            ->url(fn() => RolePermissionResource::getUrl('create', ['parent_id' => $get('parent_id')]))

                                                    ])
                                                    ->warning()
                                                    ->send();
                                            }
                                            return $rolesToReturn;
                                        }
                                    })


                                    ->rules('required')
                                    ->validationMessages([
                                        'required' => 'Please select a role.',
                                    ])
                                    ->default(function ($record) {
                                        return $record?->roles->first()?->name;
                                    }),
                            ])
                            ->columns(2),
                    ])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->recordUrl(fn() => null)
            ->query(function () {
                $search = request()->input('tableSearch');
                $selectedParentId = request()->input('tableFilters.users.parent_id');
                return User::query()
                    ->select([
                        'users.parent_id as id',
                        'parent_users.name as parent_name',
                        DB::raw('COUNT(users.id) as users_count'),
                    ])
                    ->leftJoin('users as parent_users', 'users.parent_id', '=', 'parent_users.id')
                    ->whereIn('users.parent_id', function ($query) {
                        $query->select('id')
                            ->from('users')
                            ->whereExists(function ($roleQuery) {
                                $roleQuery->select(DB::raw(1))
                                    ->from('model_has_roles')
                                    ->whereColumn('model_has_roles.model_id', 'users.id')
                                    ->whereIn('model_has_roles.role_id', function ($subQuery) {
                                        $subQuery->select('id')
                                            ->from('roles')
                                            ->where('name', 'Pharmaceutical Company');
                                    });
                            });
                    })
                    ->when($search, function ($query, $search) {
                        $query->where(function ($sub) use ($search) {
                            $sub->whereRaw('CAST(users.parent_id AS TEXT) ILIKE ?', ["%{$search}%"])
                                ->orWhereRaw('parent_users.name ILIKE ?', ["%{$search}%"]);
                        });
                    })
                    ->when($selectedParentId, function ($query, $selectedParentId) {
                        $query->where('users.parent_id', $selectedParentId);
                    })
                    ->whereNull('users.deleted_at')
                    ->groupBy('users.parent_id', 'parent_users.name');
            })
            ->columns([
                // TextColumn::make('id')
                //     ->label('User ID')
                //     ->formatStateUsing(fn(string $state): string => "#{$state}")
                //     ->toggleable()
                //     ->searchable(query: function ($query, $search) {
                //         return  $query->whereRaw('CAST(users.parent_id AS TEXT) ILIKE ?', ["%{$search}%"]);
                //     })
                //     ->sortable(),
                TextColumn::make('parent_name')
                    ->label('Pharmaceutical Company')
                    ->toggleable()
                    ->url(fn($record) => PsUserManageResource::getUrl('user-list', ['record' => $record]))
                    ->searchable(query: function ($query, $search) {
                        return  $query->whereRaw('parent_users.name ILIKE ?', ["%{$search}%"]);
                    })
                    ->sortable(),
                ViewColumn::make('users_count')
                    ->label('No. of Users')
                    ->url(fn($record) => PsUserManageResource::getUrl('user-list', ['record' => $record]))
                    ->view('tables.columns.pcusers-list-modal'),
            ])
            ->defaultSort('parent_name', 'asc')

            ->filters([
                // Tables\Filters\SelectFilter::make('role')
                //     ->label('Role')
                //     ->options(
                //         \Spatie\Permission\Models\Role::where('panel', 'pc')->pluck('name', 'name')
                //     )
                //     ->query(function (Builder $query, array $data) {
                //         if (!empty($data['value'])) {
                //             $query->whereHas('roles', function ($query) use ($data) {
                //                 $query->where('name', $data['value']);
                //             });
                //         }
                //     }),

                Tables\Filters\SelectFilter::make('users.parent_id')
                    ->label('Pharmaceutical Company')
                    ->options(function () {
                        $pharmaRoleId = Role::where('name', 'Pharmaceutical Company')->value('id');

                        // Get the unique parent_ids for pharma company users
                        $parentIds = User::query()
                            ->whereNull('users.deleted_at')
                            ->whereIn('users.parent_id', function ($query) use ($pharmaRoleId) {
                                $query->select('model_id')
                                    ->from('model_has_roles')
                                    ->where('role_id', $pharmaRoleId);
                            })
                            ->pluck('parent_id')
                            ->unique();

                        // Return ['parent_id' => 'parent_name'] for the filter dropdown
                        return User::query()
                            ->whereIn('id', $parentIds)
                            ->whereNull('deleted_at')
                            ->orderBy('name')
                            ->pluck('name', 'id')
                            ->toArray();
                    })
                    ->searchable()
                    ->preload(),

                // Tables\Filters\SelectFilter::make('is_active')
                //     ->label('Status')
                //     ->options([
                //         true => 'Active',
                //         false => 'Inactive',
                //     ]),
            ])
            ->actionsColumnLabel('Action')
            ->actions([
                // Tables\Actions\EditAction::make()
                //     ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                //     ->disabled(fn($record) => in_array(optional($record->roles->first())->name, ['Super Admin', 'Pharmaceutical Company'])),
                // Tables\Actions\DeleteAction::make()
                //     ->icon('heroicon-o-trash')->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                //     ->successNotification(
                //         Notification::make()
                //             ->success()
                //             // ->title('User Deleted')
                //             ->title('The users have been deleted successfully.'),
                //     ),
                Tables\Actions\Action::make('viewPsUsers')
                    ->iconButton()
                    ->url(fn($record) => PsUserManageResource::getUrl('user-list', ['record' => $record]))
                    ->icon('heroicon-o-eye')
                    ->color('gray')
                    ->tooltip("View"),




            ])
            ->bulkActions([
                // Tables\Actions\DeleteBulkAction::make()
                //     ->action(function (Collection $records) {
                //         $records->each->delete();
                //         Notification::make()
                //             ->success()
                //             // ->title('Users Deleted')
                //             ->title('The users have been deleted successfully.')
                //             ->send();
                //     }),
                // Tables\Actions\BulkAction::make('active')
                //     ->label('Active')
                //     ->color('success')
                //     ->action(function (Collection $records) {
                //         try {
                //             DB::beginTransaction();
                //             foreach ($records as $record) {
                //                 $record->is_active = self::STATUS_ACTIVE; // Use is_active instead of status
                //                 $record->save();
                //             }
                //             DB::commit();
                //             Notification::make()
                //                 ->title('The selected users have been activated successfully.')
                //                 ->success()
                //                 ->send();
                //         } catch (\Exception $e) {
                //             DB::rollBack();
                //             Notification::make()
                //                 ->title('Failed to activate users: ' . $e->getMessage())
                //                 ->danger()
                //                 ->send();
                //         }
                //     })
                //     ->requiresConfirmation(),

                // Tables\Actions\BulkAction::make('inactive')
                //     ->label('Inactive')
                //     ->color('warning')
                //     ->action(function (Collection $records) {
                //         try {
                //             DB::beginTransaction();
                //             foreach ($records as $record) {
                //                 $record->is_active = self::STATUS_INACTIVE; // Use is_active instead of status
                //                 $record->save();
                //             }
                //             DB::commit();
                //             Notification::make()
                //                 ->title('The selected users have been deactivated successfully.')
                //                 ->success()
                //                 ->send();
                //         } catch (\Exception $e) {
                //             DB::rollBack();
                //             Notification::make()
                //                 ->title('Failed to deactivate users: ' . $e->getMessage())
                //                 ->danger()
                //                 ->send();
                //         }
                //     })
                //     ->requiresConfirmation(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPsUserManages::route('/'),
            'create' => Pages\CreatePsUserManage::route('/create'),
            'edit' => Pages\EditPsUserManage::route('/{record}/edit'),
            'view' => Pages\ViewPsUserManage::route('/{record}'),
            'user-list' => Pages\ShowPsUserListManages::route('/{record}/ps-users')
        ];
    }
}
