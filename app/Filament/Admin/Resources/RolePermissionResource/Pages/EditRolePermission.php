<?php

namespace App\Filament\Admin\Resources\RolePermissionResource\Pages;

use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Indianic\FilamentShield\Support\Utils;

class EditRolePermission extends EditRecord
{
    protected static string $resource = RolePermissionResource::class;

    public Collection $permissions;


    protected function getActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return static::getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        Log::info('Edit Role Form Data: ' . json_encode($data));
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', Utils::getTenantModelForeignKey()]);
            })
            ->values()
            ->flatten()
            ->unique();
        if (Arr::has($data, Utils::getTenantModelForeignKey())) {
            return Arr::only($data, ['name', 'guard_name', Utils::getTenantModelForeignKey()]);
        }

        return Arr::only($data, ['name', 'guard_name', 'display_name']);
    }

    protected function afterSave(): void
    {
        $permissionModels = collect();

        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Roles and Permissions',
            2 => 'Edit Role',
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url(fn() => url("role-permissions/{$this->record->created_by}/roles-users"))
                ->color('gray')
                ->button(),
        ];
    }
    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title(__('Role Updated'))
            ->body(__('Role has been successfully updated'));
    }
}
