<?php

namespace App\Filament\Admin\Resources\RolePermissionResource\Pages;

use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Actions\Action;

class ViewRolePermission extends ViewRecord
{
    protected static string $resource = RolePermissionResource::class;

    protected function getActions(): array
    {
        return [
            Actions\EditAction::make(),
        ];
    }
    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url(fn() => url("role-permissions/{$this->record->created_by}/roles-users"))
                ->color('gray')
                ->button(),
        ];
    }
}
