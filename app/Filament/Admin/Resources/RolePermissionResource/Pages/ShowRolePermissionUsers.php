<?php

namespace App\Filament\Admin\Resources\RolePermissionResource\Pages;

use App\Models\User;
use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use App\Models\Role;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use App\Filament\Admin\Resources\RolePermissionResource\Pages\ViewUserManage;
use App\Filament\Admin\Resources\UserResource\Pages\ViewUser;
use Illuminate\Database\Eloquent\Model;

class ShowRolePermissionUsers extends Page implements Tables\Contracts\HasTable
{
    use Tables\Concerns\InteractsWithTable;

    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 0;

    protected static string $resource = RolePermissionResource::class;
    protected static string $view = 'filament.admin.resources.ps-user-manage-resource.pages.show-ps-user-list-manages';

    public $userId;

    public function mount($record): void
    {
        $this->userId = $record;
    }

    public function getTitle(): string
    {
        $recordName = User::where('id', $this->userId)->value('name');
        return $recordName ? $recordName : 'Role Permission Users';
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url('/role-permissions')
                ->color('gray')
                ->button(),
        ];
    }

    public function table(Table $table): Table
    {
        return $table->recordUrl(fn($record) => ViewRolePermission::getUrl(['record' => $record->id]))
            ->query(function () {
                return Role::where('created_by', $this->userId)->withCount('users');
            })
            ->columns([
                Tables\Columns\TextColumn::make('name')->width('30%')->wrap()->toggleable()->sortable()->searchable()
                    ->formatStateUsing(fn($state) => preg_replace('/^\d+\s*-\s*/', '', $state)),
                ToggleColumn::make('is_active')->label('Status')
                    ->toggleable()
                    ->sortable()
                    ->alignment('center')
                    ->disabled(fn($record) => $record->users_count > 0 && $record->is_active)
                    ->getStateUsing(fn($record) => $record->is_active)
                    ->beforeStateUpdated(function ($record, $state) {
                        if ($record->users_count > 0 && !$state) {
                            Notification::make()
                                ->danger()
                                ->title('Cannot change status')
                                ->body('Cannot inactivate role that has users assigned. Please reassign users first.')
                                ->send();
                            return false;
                        }
                        return true;
                    })
                    ->afterStateUpdated(function ($state, $record) {
                        $record->update(['is_active' => $state]);
                        Notification::make()
                            ->success()
                            ->title('Status has been updated successfully.')
                            ->send();
                    })
                    ->tooltip(fn($record) => $record->users_count > 0 && $record->is_active
                        ? 'Cannot inactivate role that has users assigned'
                        : null)
                    ->extraAttributes(function ($record) {
                        return $record->users_count > 0 && $record->is_active ? [
                            'class' => 'cursor-not-allowed opacity-50',
                        ] : [];
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn($record) => EditRolePermission::getUrl(['record' => $record->id]))
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->visible(function ($record) {
                        $user = auth()->user();
                        return ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('roles_update'));
                    })
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])->iconButton()
                    ->tooltip(function (Model $record) {
                        return  "Edit";
                    }),
                Tables\Actions\ViewAction::make()
                    ->url(fn($record) => ViewRolePermission::getUrl(['record' => $record->id]))
                    ->icon('heroicon-o-eye')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; );'])
                    ->iconButton()->tooltip(function (Model $record) {
                        return  "View";
                    }),
                Tables\Actions\DeleteAction::make()
                    //->visible(fn($record) => $record->users_count === 0)
                    ->visible(function ($record) {
                        $user = auth()->user();
                        return $record->users_count === 0 || ($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('roles_delete'));
                    })
                    ->icon('heroicon-o-trash')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])->iconButton()->tooltip(function (Model $record) {
                        return  "Delete";
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title('User Deleted')
                            ->title('The Role has been deleted successfully.')
                    ),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ])
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $rolesWithUsers = $records->filter(fn($record) => $record->users_count > 0);
                        $rolesWithoutUsers = $records->filter(fn($record) => $record->users_count === 0);

                        if ($rolesWithUsers->isNotEmpty()) {
                            $roleNames = $rolesWithUsers->pluck('name')
                                ->map(fn($name) => preg_replace('/^\d+\s*-\s*/', '', $name))
                                ->implode(', ');
                            Notification::make()
                                ->warning()
                                ->title('Some roles could not be deleted')
                                ->body("The following roles have users assigned and cannot be deleted: {$roleNames}")
                                ->send();
                        }

                        if ($rolesWithoutUsers->isNotEmpty()) {
                            $deletedRoleNames = $rolesWithoutUsers->pluck('name')
                                ->map(fn($name) => preg_replace('/^\d+\s*-\s*/', '', $name))
                                ->implode(', ');
                            $rolesWithoutUsers->each->delete();
                            Notification::make()
                                ->success()
                                ->title('Roles deleted successfully')
                                ->body("The following roles have been deleted: {$deletedRoleNames}")
                                ->send();
                        }

                        // If no roles were deleted, show info notification
                        if ($rolesWithoutUsers->isEmpty() && $rolesWithUsers->isNotEmpty()) {
                            Notification::make()
                                ->info()
                                ->title('No roles were deleted')
                                ->body('All selected roles have users assigned and cannot be deleted.')
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('active')
                    ->label('Active')
                    ->color('success')
                    ->action(function (Collection $records) {
                        try {
                            DB::beginTransaction();
                            foreach ($records as $record) {
                                $record->is_active = self::STATUS_ACTIVE;
                                $record->save();
                            }
                            DB::commit();
                            Notification::make()
                                ->title('The selected users have been activated successfully.')
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('Failed to activate users: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
                Tables\Actions\BulkAction::make('inactive')
                    ->label('Inactive')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $rolesWithUsers = $records->filter(fn($record) => $record->users_count > 0);
                        $rolesWithoutUsers = $records->filter(fn($record) => $record->users_count === 0);

                        if ($rolesWithUsers->isNotEmpty()) {
                            $roleNames = $rolesWithUsers->pluck('name')
                                ->map(fn($name) => preg_replace('/^\d+\s*-\s*/', '', $name))
                                ->implode(', ');
                            Notification::make()
                                ->warning()
                                ->title('Some roles could not be deactivated')
                                ->body("The following roles have users assigned and cannot be deactivated: {$roleNames}")
                                ->send();
                        }

                        if ($rolesWithoutUsers->isNotEmpty()) {
                            $deactivatedRoleNames = $rolesWithoutUsers->pluck('name')
                                ->map(fn($name) => preg_replace('/^\d+\s*-\s*/', '', $name))
                                ->implode(', ');
                            try {
                                DB::beginTransaction();
                                foreach ($rolesWithoutUsers as $record) {
                                    $record->is_active = self::STATUS_INACTIVE;
                                    $record->save();
                                }
                                DB::commit();
                                Notification::make()
                                    ->success()
                                    ->title('Roles deactivated successfully')
                                    ->body("The following roles have been deactivated: {$deactivatedRoleNames}")
                                    ->send();
                            } catch (\Exception $e) {
                                DB::rollBack();
                                Notification::make()
                                    ->title('Failed to deactivate roles: ' . $e->getMessage())
                                    ->danger()
                                    ->send();
                            }
                        }

                        if ($rolesWithoutUsers->isEmpty() && $rolesWithUsers->isNotEmpty()) {
                            Notification::make()
                                ->info()
                                ->title('No roles were deactivated')
                                ->body('All selected roles have users assigned and cannot be deactivated.')
                                ->send();
                        }
                    })
                    ->requiresConfirmation(),
            ]);
    }
}
