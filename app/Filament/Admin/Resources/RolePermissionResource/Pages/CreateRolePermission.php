<?php

namespace App\Filament\Admin\Resources\RolePermissionResource\Pages;

use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Indianic\FilamentShield\Support\Utils;
use Illuminate\Validation\ValidationException;

class CreateRolePermission extends CreateRecord
{
    protected static string $resource = RolePermissionResource::class;

    public Collection $permissions;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Add Role';
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => 'Roles and Permissions',
            2 => 'Add Role',
        ];
    }
    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url('/role-permissions')
                ->color('gray'),
        ];
    }

    protected function getFormActions(): array
    {
        return [
            parent::getCreateFormAction()
                ->label('Add'),
            parent::getCancelFormAction()
                ->label('Cancel'),
        ];
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $selectedPermissions = collect($data)
            ->filter(fn($value, $key) => str_starts_with($key, 'permissions_') && !empty($value))
            ->flatten();
        if (count($selectedPermissions) < 1) {
            Notification::make()
                ->title('At least one permission must be selected.')
                ->danger()
                ->send();
            throw ValidationException::withMessages([
                'permissions' => 'At least one permission must be selected.',
            ]);
        }
        $data['name'] = trim($data['created_by'] . "-{$data['display_name']}");
        $data['guard_name'] = 'web';
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', Utils::getTenantModelForeignKey()]);
            })
            ->values()
            ->flatten()
            ->unique();

        if (Arr::has($data, Utils::getTenantModelForeignKey())) {
            return Arr::only($data, ['name', 'guard_name', Utils::getTenantModelForeignKey()]);
        }

        $data['panel'] = 'pc';

        return Arr::only($data, ['name', 'guard_name', 'created_by', 'panel', 'display_name']);
    }

    protected function afterCreate(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                /** @phpstan-ignore-next-line */
                'name' => $permission,
                'guard_name' => 'web',
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Role Created')
            ->body('The role has been created successfully.');
    }
}
