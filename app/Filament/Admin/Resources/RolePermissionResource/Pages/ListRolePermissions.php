<?php

namespace App\Filament\Admin\Resources\RolePermissionResource\Pages;

use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRolePermissions extends ListRecords
{
    protected static string $resource = RolePermissionResource::class;

    protected function getHeaderActions(): array
    {

        $user = auth()->user();
        if (($user->hasRole('Super Admin') || $user->hasRole('Pharmaceutical Company') || $user->can('roles_create'))) {
            return  [Actions\CreateAction::make()->label('+ Add Role')];
        }

        return [];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => "Roles & Permissions",
            // 3 => "List",
        ];
    }
}
