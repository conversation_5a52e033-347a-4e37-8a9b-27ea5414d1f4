<?php

namespace App\Filament\Admin\Resources\FullPayoutResource\Widgets;

use App\Models\Payout;
use App\Models\PayoutSubOrder;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Order;
use App\Models\PcDetail;
use App\Models\User;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Pagination\LengthAwarePaginator;

class PcCommissionTable extends BaseWidget
{
    protected int|string|array $columnSpan = 'full';

    protected static string $view = 'filament.admin.resources.full-payout-resource.widgets.pc-commission-table';
    protected static ?string $heading  = 'Pharmaceutical Supplier Commission';

    public $currentPage = 1;
    public $perPage = 10;



    public function table(Table $table): Table
    {
        return $table
            ->query(function (Builder $query) {
                return User::query()->where(['verification_status' => 'approved'])->role('Pharmaceutical Company')->with('pcDetails', 'subOrders');
            })
            ->columns([
                TextColumn::make('pcDetails.company_name')->label('Pharma. Supplier')->searchable()->sortable(),
                TextColumn::make('commission_value_display')
                    ->label('Commission Value')
                    ->getStateUsing(function ($record) {
                        if ($record->pcDetails && !is_null($record->pcDetails->commission_percentage)) {
                            $numericValue = is_numeric($record->pcDetails->commission_percentage)
                                ? (float) $record->pcDetails->commission_percentage
                                : 0;

                            if ($record->pcDetails->commission_type === 'percentage') {
                                return number_format($numericValue, 2) . '%';
                            } elseif ($record->pcDetails->commission_type === 'flat') {
                                return 'RM ' . number_format($numericValue, 2);
                            }
                        }
                        return '-';
                    })
                    ->sortable(query: function (Builder $query, string $direction) {
                        return $query->leftJoin('pc_details', 'users.id', '=', 'pc_details.user_id')
                            ->orderByRaw("
                                CASE 
                                    WHEN pc_details.commission_percentage IS NOT NULL 
                                    THEN CAST(pc_details.commission_percentage AS DECIMAL(10,2))
                                    ELSE 0 
                                END {$direction}
                            ")
                            ->select('users.*');
                    })
                    ->searchable(query: function (Builder $query, string $search) {
                        $query->whereHas('pcDetails', function ($q) use ($search) {
                            $q->where(function ($subQuery) use ($search) {
                                // Search for percentage values (with or without % symbol)
                                $subQuery->where('commission_type', 'percentage')
                                    ->whereRaw('CAST(commission_percentage AS TEXT) ILIKE ?', ["%$search%"]);
                            })
                                ->orWhere(function ($subQuery) use ($search) {
                                    // Search for flat values (with or without RM prefix)
                                    $subQuery->where('commission_type', 'flat')
                                        ->whereRaw('CAST(commission_percentage AS TEXT) ILIKE ?', ["%$search%"]);
                                });
                        });
                    }),
                TextColumn::make('pcDetails.commission_type')->label('Commission Type')->default('-')
                    ->formatStateUsing(fn($state) => empty($state) ? '-' : ucfirst($state))->searchable()->sortable(),
                TextColumn::make('subOrders')->label('Total Admin Fee')
                    ->default('RM 0.00')
                    ->formatStateUsing(function ($record) {
                        $subOrders = $record->subOrders ?? collect();

                        $amount = $subOrders->sum(function ($subOrder) {
                            return $subOrder->orderProducts->sum('total_commission');
                        });

                        return !empty($amount) ? 'RM ' . number_format($amount, 2) : 'RM 0.00';
                    })->sortable(query: function (Builder $query, string $direction) {
                        return $query->leftJoinSub(
                            function ($subQuery) {
                                $subQuery->select('sub_orders.user_id')
                                    ->selectRaw('COALESCE(SUM(order_products.total_commission)) as total_commission_sum')
                                    ->from('sub_orders')
                                    ->leftJoin('order_products', 'sub_orders.id', '=', 'order_products.sub_order_id')
                                    ->groupBy('sub_orders.user_id');
                            },
                            'commission_sums',
                            'users.id',
                            '=',
                            'commission_sums.user_id'
                        )
                            ->orderBy('commission_sums.total_commission_sum', $direction);
                    })
                    ->searchable(query: function (Builder $query, string $search) {
                        $query->whereExists(function ($subQuery) use ($search) {
                            $subQuery->selectRaw(1)
                                ->from('sub_orders')
                                ->join('order_products', 'sub_orders.id', '=', 'order_products.sub_order_id')
                                ->whereColumn('sub_orders.user_id', 'users.id')
                                ->groupBy('sub_orders.user_id')
                                ->havingRaw('SUM(order_products.total_commission)::text ILIKE ?', ["%$search%"]);
                        });
                    }),

            ])
            ->filters([
                SelectFilter::make('user')
                    ->label('Pharma. Supplier')
                    ->options(function () {
                        return User::query()->where(['verification_status' => 'approved'])->role('Pharmaceutical Company')->with('pcDetails')->get()
                            ->filter(function ($user) {
                                return $user->pcDetails && !empty($user->pcDetails->company_name);
                            })->pluck('pcDetails.company_name', 'id')->sort()->toArray();
                    })->attribute('id')->searchable()->multiple()->preload()->placeholder('All Suppliers'),
                SelectFilter::make('commission_type')
                    ->label('Commission Type')
                    ->options(function () {
                        $types = PcDetail::whereNotNull('commission_type')
                            ->where('commission_type', '!=', '')
                            ->distinct()
                            ->pluck('commission_type')
                            ->filter(fn($type) => !is_null($type) && $type !== '')
                            ->mapWithKeys(fn($type) => [$type => ucfirst($type)])
                            ->toArray();
                        return !empty($types) ? $types : ['flat' => 'Flat', 'percentage' => 'Percentage'];
                    })
                    ->default(null)
                    ->placeholder('All Types')
                    ->preload()
                    ->query(function (Builder $query, array $data) {
                        if (!empty($data['value'])) {
                            $query->whereHas('pcDetails', function ($q) use ($data) {
                                $q->whereIn('commission_type', (array) $data['value']);
                            });
                        }
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Action::make('view')
                    ->url(fn(User $record): string => route('filament.admin.resources.users.view', ['record' => $record->id])) // Change order_id to record
                    ->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->tooltip('User Details')
                    ->color('gray')
                    ->label(false),

            ]);
    }

    protected function paginateTableQuery(Builder $query): LengthAwarePaginator
    {
        $total = $query->count();
        $currentPage = $this->getTablePage() ?? 1;
        $perPage = $this->getTableRecordsPerPage();

        $items = $query
            ->offset(($currentPage - 1) * $perPage)
            ->limit($perPage)
            ->get();

        return new LengthAwarePaginator(
            $items,
            $total,
            $perPage,
            $currentPage,
            [
                'path' => request()->url(),
                'pageName' => 'page',
            ]
        );
    }
}
