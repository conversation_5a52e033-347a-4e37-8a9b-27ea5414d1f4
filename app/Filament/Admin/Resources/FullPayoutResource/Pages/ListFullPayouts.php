<?php

namespace App\Filament\Admin\Resources\FullPayoutResource\Pages;

use App\Filament\Admin\Resources\FullPayoutResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListFullPayouts extends ListRecords
{
    protected static string $resource = FullPayoutResource::class;

    protected function getHeaderActions(): array
    {
        return [
           // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => "Payouts History",
            // $this->getResource()::getUrl('index') => "Full Payouts",
            // 3 => "List",
        ];
    }
}
