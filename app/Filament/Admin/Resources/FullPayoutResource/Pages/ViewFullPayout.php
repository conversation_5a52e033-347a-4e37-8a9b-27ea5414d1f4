<?php

namespace App\Filament\Admin\Resources\FullPayoutResource\Pages;

use App\Models\ClinicDetail;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use App\Models\User;
use Filament\Actions\Action;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use App\Filament\Admin\Resources\ClinicResource\Pages;
use App\Filament\Admin\Resources\FullPayoutResource;
use App\Filament\Admin\Resources\FullPayoutResource\Widgets\FullPayoutItemsTable;
use App\Models\Payout;

class ViewFullPayout extends ViewRecord
{
    protected static string $resource = FullPayoutResource::class;

    // protected ?string $heading = 'Full Payouts';

    // protected static string $view = 'filament.admin.resources.clinic-resource.pages.view-clinic-onboarding-page';

    public ?array $data = [];

    public User $user;

    public Payout $payout;
    public $userAddress;
    public $clinicDetail;
    public $clinicAccountType;
    public $clinicPcDetail;

    // public function mount(int|string $record): void
    // {


    //     $this->payout = Payout::find($record);


    // }
    public function getTitle(): string
    {
        return '#' . $this->getRecord()->id ?? '-';
    }

    public function getBreadcrumbs(): array
    {
        return [
            1 => "Payouts History",
            $this->getResource()::getUrl('index') => "Full Payouts",
            3 => "Payout Details",
        ];
    }
    protected function getFooterWidgets(): array
    {
        return [
            FullPayoutItemsTable::make([
                'record' => $this->getRecord(), // Pass the current payout record
            ]),
        ];
    }

    public function getHeaderActions(): array
    {

        return [
            Action::make('back')
            ->label('Back')
            ->color('gray')
            ->url(FullPayoutResource::getUrl('index'))
        ];

    }



}
