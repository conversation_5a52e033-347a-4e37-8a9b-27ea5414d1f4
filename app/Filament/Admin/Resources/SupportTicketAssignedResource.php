<?php

namespace App\Filament\Admin\Resources;

use Carbon\Carbon;
use App\Models\User;
use Filament\Tables;
use App\Models\Order;
use Filament\Forms\Form;
use Filament\Tables\Table;
use App\Models\SupportTicket;
use Filament\Resources\Resource;
use Illuminate\Support\HtmlString;
use App\Models\SupportTicketMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\MarkdownEditor;
use App\Filament\Admin\Resources\SupportTicketAssignedResource\Pages;
use Illuminate\Database\Eloquent\Builder;

class SupportTicketAssignedResource extends Resource
{
    protected static ?string $navigationLabel = 'All Conversation';

    protected static ?string $model = SupportTicket::class;

    // protected static ?string $breadcrumb = 'Support';

    protected static ?string $navigationGroup = 'Support';

    public static function canAccess(): bool
    {
        //auth()->user()->unreadNotifications()->update(['read_at' => now()]);
        return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-tickets_all conversation');
    }

    // public static function canCreate(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-assigneds_create');
    // }

    // public static function canUpdate(): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-assigneds_update');
    // }

    // public static function canDelete(Model $record): bool
    // {
    //     return auth()->user()->hasRole('Pharmaceutical Company') || auth()->user()->hasRole('Super Admin') || auth()->user()->can('support-ticket-assigneds_delete');
    // }
    public static function table(Table $table): Table
    {
        $timeZone = Auth::user()->timezone ?? config('app.timezone');
        return $table
            ->query(
                SupportTicket::query()
                    ->select('support_tickets.*')
                    ->leftJoin('support_ticket_messages', 'support_tickets.id', '=', 'support_ticket_messages.support_ticket_id')
                    ->groupBy('support_tickets.id')
                    ->orderByRaw('MAX(support_ticket_messages.created_at) DESC NULLS LAST')
            )
            ->columns([
                Tables\Columns\Layout\Split::make([
                    Tables\Columns\Layout\Stack::make([
                        Tables\Columns\TextColumn::make('receiver.name')
                            ->label('')
                            ->formatStateUsing(function ($record) {

                                $name = $record->sender_id === Auth::id()
                                    ? ($record->receiver ? $record->receiver->name : null)
                                    : ($record->sender ? $record->sender->name : null);

                                if (empty($name) && $record->order?->order_number) {
                                    return 'Order ' . '<span style="color: blue;">#' . $record->order->order_number . '</span>';
                                }

                                if ($record->order?->order_number) {
                                    $orderLink = ($record->order_id && 
                                                (auth()->user()->hasRole('Super Admin') || 
                                                auth()->user()->can('all-orders_view details')))
                                                ? '<a href="' . route('filament.admin.resources.orders.view', ['record' => $record->order_id]) . '" style="color: blue;">#' . $record->order->order_number . '</a>'
                                                : '<span    style="color: blue;">#' . $record->order->order_number . '</span>';
                                    return Str::title($name) . '  |  Order ' . $orderLink;
                                }

                                return Str::title($name);
                            })
                            ->tooltip(function (SupportTicket $record) {
                                if (
                                    !$record->order_id ||
                                    (
                                        !auth()->user()->hasRole('Super Admin') &&
                                        !auth()->user()->can('all-orders_view details')
                                    )
                                ) {
                                    return 'You do not have permission to view this order.';
                                }
                                return null;
                            })
                            ->html()
                            ->searchable(query: function ($query, $search) {
                                $query = $query->where(function ($subQuery) use ($search) {
                                    $subQuery->whereHas('receiver', function ($q) use ($search) {
                                        $q->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%']);
                                    })
                                        ->orWhereHas('sender', function ($q) use ($search) {
                                            $q->whereRaw('LOWER(name) LIKE ?', ['%' . strtolower($search) . '%']);
                                        })
                                        ->orWhereHas('order', function ($q) use ($search) {
                                            $q->where('order_number', 'like', '%' . $search . '%');
                                        });
                                });
                                return $query;
                            })
                            ->extraAttributes(['class' => 'font-bold']),
                        Tables\Columns\TextColumn::make('closed_at')
                            ->label('')
                            ->searchable()
                            ->formatStateUsing(function ($state) use ($timeZone) {
                                if (!$state) return null;
                                return '<span style="color: red;">Closed Date: ' . Carbon::createFromFormat('Y-m-d H:i:s', $state, 'UTC')
                                    ->setTimezone($timeZone)
                                    ->format('M d, Y | h:i A');
                            })
                            ->html(),


                    ]),
                    Tables\Columns\TextColumn::make('getLatestMessage.created_at')
                        ->label('')
                        ->size(5)
                        ->formatStateUsing(function (SupportTicket $record) {
                            $lastMessage = $record->getMessages()->latest()->first();
                            if (!$lastMessage) {
                                return null;
                            }
                            $userTimezone = auth()->user()->timezone;
                            $messageDate = Carbon::parse($lastMessage->created_at)->timezone($userTimezone);
                            if ($messageDate->isToday()) {
                                return 'Today ' . $messageDate->format('h:iA');;
                            } elseif ($messageDate->isYesterday()) {
                                return 'Yesterday ' . $messageDate->format('h:iA');;
                            } else {
                                return $messageDate->format('d M, Y h:iA');
                            }
                        })
                        ->alignEnd()
                        ->extraAttributes(['class' => 'text-xs text-gray-500']),
                        
                ]),
                Tables\Columns\TextColumn::make('getLatestMessage.message')
                    ->label('')
                    ->limit(20)
                    ->formatStateUsing(function (SupportTicket $record) {
                        $lastMessage = $record->messages()->latest()->first();
                        if ($lastMessage) {
                            return $lastMessage->message;
                        }
                        return 'No messages yet';
                    })
                    ->color('gray'),
                Tables\Columns\TextColumn::make('getLatestMessage.media')
                    ->label('')
                    ->icon(function (?SupportTicket $record) {
                        if (!$record) {
                            return null;
                        }
                        $lastMessage = $record->getMessages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'heroicon-o-photo'; // Icon for images
                        } elseif ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'heroicon-o-document-text'; // Icon for PDFs/files
                        }
                        return null;
                    })
                    ->getStateUsing(function (?SupportTicket $record) {
                        if (!$record) {
                            return null;
                        }

                        $lastMessage = $record->getMessages()->latest()->first();
                        if ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            return 'Photo';
                        } elseif ($lastMessage && $lastMessage->hasMedia('support-ticket-images')) {
                            $media = $lastMessage->getMedia('support-ticket-images')->first();
                            return $media ? $media->name : null;
                        }
                        return null;
                    })
                    ->hidden(fn(?SupportTicket $record) => !$record || (!$record->messages()->latest()->first()?->hasMedia('support-ticket-images') && !$record->messages()->latest()->first()?->hasMedia('support-ticket-images'))),
                Tables\Columns\TextColumn::make('unreadMessagesCount')
                    ->label('')
                    ->badge()
                    ->color('success')
                    ->state(function (SupportTicket $record) {
                        if ($record->status !== 'open') {
                            return null;
                        }
                        $unreadCount = SupportTicketMessage::where('support_ticket_id', $record->id)
                            ->where('is_read', false)
                            ->where('from_id', '!=', Auth::id())
                            ->count();
                        return $unreadCount > 0 ? $unreadCount : null;
                    })
                    ->alignEnd(),
            ])
            ->actions([
                Tables\Actions\Action::make('select')
                    ->label('')
                    ->action(function (SupportTicket $record, $livewire) {
                        $livewire->selectTicket($record->id);
                    })
            ])

            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->multiple()
                    ->options([
                        'open' => 'Open',
                        'closed' => 'Closed',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['values'])) {
                            return $query;
                        }

                        return $query->whereIn('status', $data['values']);
                    }),
                Tables\Filters\SelectFilter::make('read_status')
                    ->label('Message Status')
                    ->options([
                        'unread' => 'Has Unread',
                        'read' => 'All Read',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }

                        if ($data['value'] === 'unread') {
                            return $query->whereHas('messages', function ($messageQuery) {
                                $messageQuery->where('is_read', false)
                                    ->where('from_id', '!=', Auth::id());
                            });
                        } elseif ($data['value'] === 'read') {
                            return $query->whereDoesntHave('messages', function ($messageQuery) {
                                $messageQuery->where('is_read', false)
                                    ->where('from_id', '!=', Auth::id());
                            });
                        }

                        return $query;
                    }),
                    Tables\Filters\SelectFilter::make('user')
                    ->label('User')
                    ->options(function () {
                        $userIds = SupportTicket::query()
                            ->selectRaw('DISTINCT COALESCE(sender_id, receiver_id) as user_id')
                            ->whereNotNull('sender_id')
                            ->whereNotNull('receiver_id')
                            ->where('sender_id', '!=', Auth::id())
                            ->where('receiver_id', '!=', Auth::id())
                            ->orWhere(function ($query) {
                                $query->where('sender_id', '!=', Auth::id())
                                    ->where('receiver_id', Auth::id())
                                    ->orWhere('sender_id', Auth::id())
                                    ->where('receiver_id', '!=', Auth::id());
                            })->pluck('user_id')->filter()->values();

                        return User::whereIn('id', $userIds)
                            ->orderBy('name', 'asc')
                            ->pluck('name', 'id')
                            ->mapWithKeys(function ($name, $id) {
                                return [$id => $name ?? 'Unnamed User #' . $id];
                            })->toArray();
                    })
                    ->preload()
                    ->searchable()
                    ->query(function ($query, array $data) {
                        return $query->when(
                            $data['value'] ?? null,
                            fn($query, $userId) => $query->where(function ($query) use ($userId) {
                                $query->where('sender_id', $userId)->orWhere('receiver_id', $userId)
                                    ->where(function ($query) {
                                        $query->where('sender_id', Auth::id())->orWhere('receiver_id', Auth::id());
                                    });
                            })
                        );  
                    }),

                    Tables\Filters\SelectFilter::make('order_number')
                    ->label('Order Number')
                    ->options(function () {
                        $usedOrderIds = \App\Models\SupportTicket::query()
                            ->whereNotNull('order_id')
                            ->pluck('order_id')
                            ->unique()
                            ->toArray();
                
                        return \App\Models\Order::query()
                            ->whereNotNull('order_number')
                            ->whereIn('id', $usedOrderIds)
                            ->pluck('order_number', 'order_number')
                            ->mapWithKeys(fn($num) => [$num => "#{$num}"])
                            ->toArray();
                    })
                    ->preload()
                    ->searchable()
                    ->query(function (Builder $query, array $data): Builder {
                        if (empty($data['value'])) {
                            return $query;
                        }
                        
                        $usedOrderIds = \App\Models\SupportTicket::query()
                            ->whereNotNull('order_id')
                            ->pluck('order_id')
                            ->unique()
                            ->toArray();
                            
                        $order = \App\Models\Order::query()
                            ->where('order_number', $data['value'])
                            ->whereIn('id', $usedOrderIds)
                            ->first();
                            
                        if (!$order) {
                            return $query->whereRaw('1=0');
                        }
                        
                        return $query->whereHas('order', function ($orderQuery) use ($data) {
                            $orderQuery->where('order_number', $data['value']);
                        });
                    }),
            ])
            ->recordAction('select');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSupportTicketAssigneds::route('/'),
            'create' => Pages\CreateSupportTicketAssigned::route('/create')
        ];
    }
}