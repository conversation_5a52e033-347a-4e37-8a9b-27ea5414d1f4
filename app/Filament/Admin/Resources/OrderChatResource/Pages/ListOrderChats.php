<?php

namespace App\Filament\Admin\Resources\OrderChatResource\Pages;

use App\Filament\Admin\Resources\OrderChatResource;
use App\Models\Notification;
use App\Models\Thread;
use App\Models\ThreadMessage;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\User;
use App\Models\Role;
use App\Notifications\ThreadNotification;

class ListOrderChats extends ListRecords
{
    protected static string $resource = OrderChatResource::class;
    public $selectTicket = null;
    public $messageText = '';
    public $messages = [];
    public $perPage = 15;
    public $page = 1;
    public $hasMoreMessages = true;
    protected static string $view = 'filament.admin.pages.order-chat';
    public $attachedFiles = [];


    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getTitle(): string
    {
        $totalUnreadCount = Thread::get()
            ->sum(function (Thread $ticket) {
                return ThreadMessage::where('thread_id', $ticket->id)
                    ->where('is_read', false)
                    ->where('from_id', '!=', Auth::id())
                    ->count();
            });

        $title = __('message.order_chat.breadcrumb_title');
        return $totalUnreadCount > 0 ? "{$title} ({$totalUnreadCount} Unread)" : $title;
    }

   
    public function getBreadcrumbs(): array
    {
        return [
            // $this->getResource()::getUrl('index') => __('message.order_chat.breadcrumb_support'),
            // 2 => __('message.order_chat.breadcrumb_title'),

        ];
    }

    public function selectTicket($ticketId)
    {
        $this->selectTicket = Thread::find($ticketId);
        $this->page = 1;
        $this->messages = [];
        $this->loadMessages();

        ThreadMessage::where('thread_id', $ticketId)
            ->where('from_id', '!=', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);
    }

    public function loadMessages()
    {
        $offset = ($this->page - 1) * $this->perPage;

        $newMessages = ThreadMessage::where('thread_id', $this->selectTicket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = ThreadMessage::where('thread_id', $this->selectTicket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);
    }

    public function loadMoreMessages()
    {
        if (!$this->hasMoreMessages) {
            return;
        }

        $this->page++;

        $offset = ($this->page - 1) * $this->perPage;

        // Fixed: Changed $this->ticket to $this->selectTicket
        $newMessages = ThreadMessage::where('thread_id', $this->selectTicket->id)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->offset($offset)
            ->take($this->perPage)
            ->get()
            ->reverse();

        $totalMessages = ThreadMessage::where('thread_id', $this->selectTicket->id)->count();
        $this->hasMoreMessages = ($offset + $this->perPage) < $totalMessages;

        // Merge with existing messages in the right order
        $this->messages = $this->page === 1
            ? $newMessages
            : $newMessages->merge($this->messages);

        $this->dispatch('messages-loaded');
    }

    public function easyLoadAllMessages()
    {
        $this->loadMessages(true);
    }

    public function sendMessage()
    {
        if ($this->selectTicket->status === 'closed') {
            Notification::make()
                // ->title(__('message.support_ticket_received.closed_send_error_title'))
                ->title(__('message.support_ticket_received.closed_send_error_body'))
                ->danger()
                ->send();
            return;
        }

        if (empty(trim($this->messageText)) && empty($this->attachedFiles)) {
            $this->addError('messageText', __('message.support_ticket_received.empty_message_error'));
            return;
        }

        $validationPassed = $this->validateUploads();

        if (empty($this->messageText) && !$validationPassed) {
            $this->addError('attachedFiles', __('message.support_ticket_received.invalid_file_error'));
            return;
        }

        $message = ThreadMessage::create([
            'thread_id' => $this->selectTicket->id,
            'from_id' => auth()->user()->id,
            'message' => $this->messageText ?? '',
        ]);

        if (!empty($this->attachedFiles)) {
            $folderPath = config('constants.api.media.thread') . $this->selectTicket->id;
            $mediaData = [];

            foreach ($this->attachedFiles as $key => $file) {
                $extension = $file->getClientOriginalExtension();

                $fileName = hash('sha256', $file->getFilename() . '_' . now()) . '.' . $extension;

                getStorageDisk()->putFileAs($folderPath, $file, $fileName);

                $mediaData[] = [
                    "model_type"            => 'App\Models\ThreadMessage',
                    "model_id"              => $message->id,
                    "uuid"                  => Str::uuid()->toString(),
                    "collection_name"       => 'thread-chat-images',
                    "name"                  => $fileName,
                    "file_name"             => $fileName,
                    "mime_type"             => $file->getMimeType(),
                    "disk"                  => config('filesystems.default'),
                    "conversions_disk"      => config('filesystems.default'),
                    "size"                  => $file->getSize(),
                    "manipulations"         => json_encode([]),
                    "custom_properties"     => json_encode([]),
                    "generated_conversions" => json_encode([]),
                    "responsive_images"     => json_encode([]),
                    "order_column"          => $key + 1,
                    "created_at"            => now(),
                    "updated_at"            => now(),
                ];
            }

            if (!empty($mediaData)) {
                \App\Models\Media::insert($mediaData);
            }
        }

        $recipient = ($this->selectTicket->sender_id == Auth::id())
        ? User::find($this->selectTicket->receiver_id)
        : User::find($this->selectTicket->sender_id);

        $senderId = $this->selectTicket->sender_id;
        $receiverId = $this->selectTicket->receiver_id;

        // Query for senderId
        $senderPharmaRole = Role::where('name', 'Pharmaceutical Company')
            ->whereHas('users', function ($query) use ($senderId) {
                $query->where('id', $senderId);
            })->first();

        // Query for receiverId
        $receiverPharmaRole = Role::where('name', 'Pharmaceutical Company')
            ->whereHas('users', function ($query) use ($receiverId) {
                $query->where('id', $receiverId);
            })->first();
        $matchedId = null;
        if ($senderPharmaRole) {
            $matchedId = $senderId;
        } elseif ($receiverPharmaRole) {
            $matchedId = $receiverId;
        }



        if ($matchedId && $matchedId !== $recipient->id) {
            $user = User::where('id', $matchedId)->first();
            $user->notify(new ThreadNotification($message, 'new_message', 'pc'));
        }

        if ($recipient && $recipient->id !== auth()->user()->id) {
            $recipient->notify(new \App\Notifications\ThreadNotification($message, 'new_message', 'pc'));
        }

        $this->reset(['messageText', 'attachedFiles']);
        // $this->attachedFiles = [];
        $this->page = 1;
        $this->loadMessages();
        $this->dispatch('message-sent');
        $this->dispatch('$refresh');
    }

    public function validateUploads()
    {
        if (empty($this->attachedFiles)) {
            return true;
        }

        if (count($this->attachedFiles) > 5) {
            Notification::make()
                ->title(__('message.order_chat.file_upload_limit_error'))
                ->danger()
                ->send();

            $this->attachedFiles = array_slice($this->attachedFiles, 0, 5);
            return false;
        }

        $hasInvalidFiles = false;
        $invalidFiles = [];

        foreach ($this->attachedFiles as $key => $file) {
            // Check if file object is valid before accessing properties
            if (!$file || !method_exists($file, 'getMimeType')) {
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
                continue;
            }

            $mimeType = $file->getMimeType();
            $extension = strtolower($file->getClientOriginalExtension());
            $fileSizeKB = $file->getSize() / 1024;

            $isPdf = in_array($mimeType, ['application/pdf', 'application/x-pdf']) || $extension === 'pdf';
            $isImage = strpos($mimeType, 'image/') === 0 || in_array($extension, ['jpg', 'jpeg', 'png', 'gif']);

            $sizeLimitKB = 1024; // 1MB

            if ((!$isPdf && !$isImage) || $fileSizeKB > $sizeLimitKB) {
                $invalidFiles[] = $file->getClientOriginalName();
                unset($this->attachedFiles[$key]);
                $hasInvalidFiles = true;
            }
        }

        if ($hasInvalidFiles) {
            Notification::make()
                ->title(__('message.order_chat.file_type_limit_error'))
                ->danger()
                ->send();

            $this->attachedFiles = array_values($this->attachedFiles);
        }

        return !$hasInvalidFiles;
    }

    public function updatedAttachedFiles()
    {
        if ($this->selectTicket->status === 'closed') {
            $this->attachedFiles = [];
            Notification::make()
                ->title(__('message.support_ticket_received.closed_file_attach_body'))
                ->danger()
                ->send();
            return;
        }

        $validationPassed = $this->validateUploads();

        if ($validationPassed && !empty($this->attachedFiles)) {
            $this->resetErrorBag('messageText');
        }
    }

    public function updatedMessageText($value)
    {
        if ($this->selectTicket->status === 'closed') {
            $this->messageText = '';
            Notification::make()
                ->title(__('message.support_ticket_received.closed_typing_body'))
                ->danger()
                ->send();
            return;
        }

        // Clear messageText error if the input is non-empty
        if (!empty(trim($value))) {
            $this->resetErrorBag('messageText');
        }
    }

    public function removeFile($index)
    {
        if (isset($this->attachedFiles[$index])) {
            unset($this->attachedFiles[$index]);
            $this->attachedFiles = array_values($this->attachedFiles);
            $this->validateUploads();
        }
    }
}
