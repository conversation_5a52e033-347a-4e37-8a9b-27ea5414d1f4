<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\DistributorResource\Pages;
use App\Models\Distributor;
use App\Models\Product;
use App\Rules\CaseSensitiveUnique;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\Model;

class DistributorResource extends Resource
{
    protected static ?string $model = Distributor::class;

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 10;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('distributors_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('distributors_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('distributors_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('distributors_delete');
    }
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    TextInput::make('name')
                        ->label(new HtmlString("Distributor Name <span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->maxLength(100)
                        ->placeholder('Enter Distributor Name')
                        ->rules([
                            'required',
                            // 'regex:/^[\w\s\p{P}]+$/u',
                            'max:100',
                            fn(Get $get) => new CaseSensitiveUnique(Distributor::class, 'name', $get('id'))
                        ])
                        ->validationMessages([
                            'required' => __('message.distributor.required'),
                            // 'regex' => __('message.distributor.regex'),
                            'max' => __('message.distributor.max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.distributor.case_sensitive_unique'),
                        ]),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Distributor Name')->sortable()->searchable()->toggleable(),
                ToggleColumn::make('status')->label('Status')
                    ->sortable()
                    ->toggleable()
                    ->disabled(function ($record) {
                        return DB::table('distributor_product')
                            ->where('distributor_id', $record->id)
                            ->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        $hasProducts = DB::table('distributor_product')
                            ->where('distributor_id', $record->id)
                            ->exists();

                        if ($hasProducts) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                ->title(__('message.distributor.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            ->duration(1000)
                            ->title(__('message.distributor.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !DB::table('distributor_product')
                            ->where('distributor_id', $record->id)
                            ->exists();
                    })
                    ->action(function ($record) {
                        // Safe to delete
                        $record->delete();

                        Notification::make()
                            ->success()
                            // ->title(__('message.distributor.title.deleted'))
                            ->title(__('message.distributor.delete_success'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $hasProduct = DB::table('distributor_product')
                                ->where('distributor_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.distributor.title.deletion_completed'))
                                ->title(__('message.distributor.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.distributor.title.partial_deleted'))
                                ->title(__('message.distributor.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.distributor.title.activated'))
                            ->title(__('message.distributor.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = DB::table('distributor_product')
                                ->where('distributor_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.distributor.title.deactivated'))
                                ->title(__('message.distributor.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.distributor.title.partial_inactivated'))
                                ->title(__('message.distributor.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDistributors::route('/'),
            'create' => Pages\CreateDistributor::route('/create'),
            'edit' => Pages\EditDistributor::route('/{record}/edit'),
        ];
    }
}
