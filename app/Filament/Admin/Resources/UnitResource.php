<?php

namespace App\Filament\Admin\Resources;

use App\Filament\Admin\Resources\UnitResource\Pages;
use App\Models\Product;
use App\Models\Unit;
use App\Rules\CaseSensitiveUnique;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\nCollection;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Illuminate\Database\Eloquent\Model;

class UnitResource extends Resource
{
    protected static ?string $model = Unit::class;

    // protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Master';

    protected static ?int $navigationSort = 5;

    public static function canAccess(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('volume-units_view');
    }
    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('volume-units_create');
    }
    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('volume-units_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('volume-units_delete');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()->schema([
                    TextInput::make('name')->label(new HtmlString("Volume Unit Name<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->rules([
                            'required',
                            'max:100',
                            //  'regex:/^[\w\s\p{P}]+$/u',
                            fn(Get $get) => new CaseSensitiveUnique(Unit::class, 'name', $get('id'))
                        ])
                        ->maxLength(100)

                        ->validationMessages([
                            'required' => __('message.unit.name_required'),
                            // 'regex' => __('message.unit.name_regex'),
                            'max' => __('message.unit.name_max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.unit.name_case_sensitive_unique'),
                        ]),
                    TextInput::make('short_form')->label(new HtmlString("Volume Unit Short Form<span class='text-danger-600 dark:text-danger-400 font-medium'>*</span>"))
                        ->rules(['required', 'max:15', fn(Get $get) => new CaseSensitiveUnique(Unit::class, 'short_form', $get('id'))])
                        ->maxLength(15)

                        ->validationMessages([
                            'required' => __('message.unit.short_form_required'),
                            'max' => __('message.unit.short_form_max'),
                            'App\\Rules\\CaseSensitiveUnique' => __('message.unit.short_form_case_sensitive_unique'),
                        ]),

                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')->label('Volume Unit Name')->sortable()->searchable()->toggleable(),
                TextColumn::make('short_form')->label('Volume Unit Short Form')->sortable()->searchable()->toggleable(),
                ToggleColumn::make('status')->label('Status')
                    ->sortable()
                    ->toggleable()
                    ->disabled(function ($record) {
                        return Product::query()->where('unit_id', $record->id)->exists();
                    })
                    ->afterStateUpdated(function ($record, $livewire) {
                        if (Product::query()->where('unit_id', $record->id)->exists()) {
                            $record->status = true;
                            $record->save();

                            Notification::make()
                                ->warning()
                                // ->title(__('message.unit.title.warning'))
                                ->title(__('message.unit.status_warning', ['names' => $record->name]))
                                ->send();

                            $livewire->dispatch('refresh');
                            return;
                        }

                        Notification::make()
                            ->success()
                            // ->title(__('message.unit.title.saved'))
                            ->duration(1000)
                            ->title(__('message.unit.status_updated'))
                            ->send();
                    })
                    ->extraAttributes([
                        'wire:loading.class' => 'opacity-50 cursor-wait',
                    ]),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()->tooltip('Delete')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                    ->visible(function ($record) {
                        return !Product::query()->where('unit_id', $record->id)->exists();
                    })
                    ->action(function ($record) {
                        $record->delete();
                        Notification::make()
                            ->success()
                            // ->title(__('message.unit.title.deleted'))
                            ->title(__('message.unit.delete_success'))
                            ->send();
                    }),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make()
                    ->action(function (Collection $records) {
                        $failed = [];
                        $deleted = 0;

                        $records->each(function ($record) use (&$failed, &$deleted) {
                            $product = DB::select("SELECT * FROM products WHERE unit_id = :id", ['id' => $record->id]);
                            $product = Product::query()->where('unit_id', $record->id)->exists();
                            if ($product) {
                                $failed[] = $record->name; // Assuming "name" is the attribute for display
                            } else {
                                $record->delete();
                                $deleted++;
                            }
                        });

                        if ($deleted > 0) {
                            Notification::make()
                                ->success()
                                // ->title(__('message.unit.title.deletion_completed'))
                                ->title(__('message.unit.bulk_delete_success', ['count' => $deleted]))
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                ->warning()
                                // ->title(__('message.unit.title.partial_deleted'))
                                ->title(__('message.unit.bulk_delete_failed', ['names' => implode(', ', $failed)]))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('activate')
                    ->label('Active')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $records->each(function ($record) {
                            $record->update(['status' => true]);
                        });
                        Notification::make()
                            // ->title(__('message.unit.title.activated'))
                            ->title(__('message.unit.bulk_activate_success'))
                            ->success()
                            ->send();
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('inactivate')
                    ->label('Inactive')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function (Collection $records) {
                        $failed = [];
                        $inactivated = 0;

                        $records->each(function ($record) use (&$failed, &$inactivated) {
                            $hasProduct = Product::query()
                                ->where('unit_id', $record->id)
                                ->exists();

                            if ($hasProduct) {
                                $failed[] = $record->name;
                            } else {
                                $record->update(['status' => false]);
                                $inactivated++;
                            }
                        });

                        if ($inactivated > 0) {
                            Notification::make()
                                // ->title(__('message.unit.title.deactivated'))
                                ->title(__('message.unit.bulk_inactivate_success', ['count' => $inactivated]))
                                ->success()
                                ->send();
                        }

                        if (!empty($failed)) {
                            Notification::make()
                                // ->title(__('message.unit.title.partial_inactivated'))
                                ->title(__('message.unit.bulk_inactivate_failed', ['names' => implode(', ', $failed)]))
                                ->warning()
                                ->send();
                        }
                    })->after(function () {
                        redirect(static::getUrl('index'));
                    })
                    ->requiresConfirmation(),
                // ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUnits::route('/'),
            'create' => Pages\CreateUnit::route('/create'),
            'edit' => Pages\EditUnit::route('/{record}/edit'),
        ];
    }
}
