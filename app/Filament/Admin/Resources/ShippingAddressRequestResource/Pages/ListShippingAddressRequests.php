<?php

namespace App\Filament\Admin\Resources\ShippingAddressRequestResource\Pages;

use App\Filament\Admin\Resources\ShippingAddressRequestResource;
use App\Mail\AddressApprovedMail;
use App\Mail\AddressRejectedMail;
use App\Mail\MultipleAddressApprovedMail;
use App\Mail\MultipleAddressRejectedMail;
use App\Models\UserAddress;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\Mail;

class ListShippingAddressRequests extends ListRecords
{
    protected static string $resource = ShippingAddressRequestResource::class;


    public function getTitle(): string
    {
        return 'Shipping Address';
    }
    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            // 1 => 'Pending Request',
            // ShippingAddressRequestResource::getUrl() => 'Shipping Address',
            // 2 => 'List',
        ];
    }

    public function approveRequest($id)
    {
       
        $request = UserAddress::find($id);
        $record = $request?->user;
        if ($request) {
            $request->update([
                'is_approved' => true,
                'status' => 'approved',
                'is_requested' => false
            ]);

            // Activity Log Start
            $clinicName = $request->clinicDetail->clinic_name ?? ($request->clinicDetail->name ?? 'N/A');
            $status = 'approved';
            activity()
                ->causedBy(auth()->user())
                ->performedOn($request)
                ->useLog('shipping_address_request')
                ->withProperties([
                    'old' => [
                        'status' => 'pending',
                    ],
                    'attributes' => array_filter([
                        'status' => $status,
                    ], fn($value) => !is_null($value)),
                ])
                ->log($clinicName . "'s shipping address request has been approved");
            // Activity Log End

            Notification::make()
                ->title('Request Approved')
                ->success()
                ->send();
            // Mail::to($record->email)->send(new AddressApprovedMail($record));
            Mail::to($record->email)->send(new MultipleAddressApprovedMail([
                'user' => $record->name,
                'nick_name' => $request->nick_name
            ]));
        
            // return $this->redirect(ShippingAddressRequestResource::getUrl('index'));

        } else {
            Notification::make()
                ->title('Request Not Approved')
                ->danger()
                ->send();
        }
        
    }

    public function rejectRequest($id)
    {
        $request = UserAddress::find($id);
        $record = $request?->user;
        if ($request) {
            $request->update([
                'is_approved' => true,
                'status' => 'rejected',
                'is_requested' => false
            ]);

             // Activity Log Start
             $clinicName = $request->clinicDetail->clinic_name ?? ($request->clinicDetail->name ?? 'N/A');
             $status = 'rejected';
 
             activity()
                 ->causedBy(auth()->user())
                 ->performedOn($request)
                 ->useLog('shipping_address_request')
                 ->withProperties([
                     'old' => [
                         'status' => 'pending',
                     ],
                     'attributes' => array_filter([
                         'status' => $status,
                     ], fn($value) => !is_null($value)),
                 ])
                 ->log($clinicName . "'s shipping address request has been rejected");
             // Activity Log End
            Notification::make()
                ->title('Request Rejected')
                ->success()
                ->send();
            // Mail::to($record->email)->send(new AddressRejectedMail($record));
            Mail::to($record->email)->send(new MultipleAddressRejectedMail([
                'user' => $record->name,
                'nick_name' => $request->nick_name
            ]));
        } else {
            Notification::make()
                ->title('Request Not Rejected')
                ->danger()
                ->send();
        }
    }
}
