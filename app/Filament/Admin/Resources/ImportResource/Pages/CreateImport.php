<?php

namespace App\Filament\Admin\Resources\ImportResource\Pages;

use App\Filament\Admin\Resources\ImportResource;
use App\Filament\Admin\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use PhpOffice\PhpSpreadsheet\IOFactory;


class CreateImport extends CreateRecord
{
    protected static string $resource = ImportResource::class;
    public ?string $heading = 'Import Products';
    protected static bool $canCreateAnother = false;
    public function getCreatedNotification(): ?\Filament\Notifications\Notification
    {
        return null;
    }

    public  function getTitle(): string
    {
        return 'Import Products';
    }
    protected function getRedirectUrl(): string
    {
        return static::getUrl();
    }

    public function ViewImportProductsAction(): Action
    {
        return Action::make('index')
            ->outlined()
            ->url(ImportResource::getUrl('index'))
            ->label('View Import Logs')
            ->icon('heroicon-o-eye');
    }

    protected function getHeaderActions(): array
    {
        return [
            $this->ViewImportProductsAction(),

            Actions\Action::make('create')
                ->label('Download Sample')
                ->url(route('sample.download', ['filename' => 'sample.xlsx']))
                ->openUrlInNewTab()
                ->icon('heroicon-o-document-arrow-down'),
            Actions\Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ProductResource::getUrl('index')),
        ];
    }

    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Product catalog',
            url(route('filament.admin.resources.imports.index')) => 'Imports',
            "#" => 'Import Bulk Products',
        ];
    }

    protected array $expectedHeaders = [
        'Brand',
        'Product Name',
        'Generic Name',
        'B2C Category',
        'B2C Subcategory',
        'Distributor',
        'Prescription Required',
        'Dosage Form',
        'Container',
        'Volume',
        'Volume Unit',
        'Weight in gms',
        'Product Images',
        'Product Description',
        'Key Ingredients',
        'Storage Instructions',
        'Usage/Indication',
        'Contraindication',
        'How to Use',
        'Safety Information/Pregnancy',
        'Dosage Information',
        'Side Effects'
    ];

    protected function mutateFormDataBeforeCreate(array $data): array
    {

        // $missingHeaders = [];

        // if (!$filePath || !Storage::disk('s3')->exists($filePath)) {
        //     throw ValidationException::withMessages([
        //         'file' => 'Uploaded file not found.',
        //     ]);
        // }

        // // Download file from S3 and save to temp path
        // $fileContents = Storage::disk('s3')->get($filePath);
        // $ext = pathinfo($filePath, PATHINFO_EXTENSION);
        // $tempFile = storage_path('app/temp_upload.' . $ext);
        // file_put_contents($tempFile, $fileContents);

        // if (in_array($ext, ['xlsx', 'xls'])) {
        //     $missingHeaders = $this->validateXlsxHeaders($tempFile);
        // } else {
        //     throw ValidationException::withMessages([
        //         'file' => 'Invalid file type. Only XLSX or XLS allowed.',
        //     ]);
        // }

        // // Remove temp file
        // @unlink($tempFile);

        // if (!empty($missingHeaders)) {
        //     $msg = implode(', ', $missingHeaders);

        //     // Optionally delete the bad file
        //     Storage::disk('s3')->delete($filePath);

        //     throw ValidationException::withMessages([
        //         'file' => 'Invalid headers. Missing: ' . $msg,
        //     ]);
        // }

        // Clean up unneeded fields
        unset($data['file'], $data['zipfile']);
        $data['user_id'] = auth()->id();


        return $data;
    }

    /**
     * Validate headers for CSV files
     */
    // protected function validateCsvHeaders(string $filePath): array
    // {
    //     if (($handle = fopen($filePath, 'r')) !== false) {
    //         // Read the first row which contains headers
    //         $headers = fgetcsv($handle);
    //         fclose($handle);

    //         // Compare with expected headers
    //         return $this->compareHeaders($headers);
    //     }

    //     return $this->expectedHeaders;
    // }

    /**
     * Validate headers for XLSX files
     */
    // protected function validateXlsxHeaders(string $filePath): array
    // {
    //     // Using the PhpSpreadsheet library to read XLSX files
    //     $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($filePath);
    //     $worksheet = $spreadsheet->getActiveSheet();
    //     $headers = [];

    //     // Get the first row headers
    //     foreach ($worksheet->getRowIterator(1, 1) as $row) {
    //         $cellIterator = $row->getCellIterator();
    //         $cellIterator->setIterateOnlyExistingCells(false);

    //         foreach ($cellIterator as $cell) {
    //             $headers[] = $cell->getValue();
    //         }
    //         break; // Only need the first row
    //     }

    //     // Compare with expected headers
    //     return $this->compareHeaders($headers);
    // }

    /**
     * Compare file headers with expected headers
     */
    // protected function compareHeaders(?array $fileHeaders): array
    // {
    //     if (!$fileHeaders) {
    //         return $this->expectedHeaders;
    //     }
    //     // Remove any empty headers and trim whitespace
    //     $fileHeaders = array_map('trim', array_filter($fileHeaders, fn($header) => $header !== null && $header !== ''));
    //     // Check if all expected headers exist in file headers
    //     return array_diff($this->expectedHeaders, $fileHeaders);
    // }


    // protected function afterCreate(): void
    // {
    //     $id = $this->record?->id;
    //     if (!$id) return;

    //     $finalDir = "imports/{$id}";

    //     // Get uploaded file names from form data
    //     $filePath = $this->data['file'] ?? null;
    //     $zipPath  = $this->data['zipfile'] ?? null;

    //     // Support both string and array (depending on preserveFilenames / multiple)
    //     $filePath = is_array($filePath) ? reset($filePath) : $filePath;
    //     $zipPath  = is_array($zipPath) ? reset($zipPath) : $zipPath;

    //     // Extract base file names
    //     $fileName = $filePath ? basename($filePath) : null;
    //     $zipName  = $zipPath ? basename($zipPath) : null;

    //     // Locate real paths from S3 (since they are saved under imports/temp/)
    //     $file = $fileName
    //         ? collect(Storage::disk('s3')->allFiles('imports/temp'))
    //         ->first(fn($path) => basename($path) === $fileName)
    //         : null;

    //     $zip = $zipName
    //         ? collect(Storage::disk('s3')->allFiles('imports/temp'))
    //         ->first(fn($path) => basename($path) === $zipName)
    //         : null;

    //     \Log::info('Resolved actual uploaded paths', [
    //         'file' => $file,
    //         'zipfile' => $zip,
    //     ]);

    //     // Move XLSX file
    //     if ($file && Storage::disk('s3')->exists($file)) {
    //         $newFilePath = "{$finalDir}/{$fileName}";
    //         $fileContents = Storage::disk('s3')->get($file);

    //         Storage::disk('s3')->put($newFilePath, $fileContents);
    //         Storage::disk('s3')->setVisibility($newFilePath, 'public');
    //         Storage::disk('s3')->delete($file);

    //         \Log::info("✅ Moved Excel to: {$newFilePath}");
    //     } else {
    //         \Log::warning("❌ Excel file not found at: {$file}");
    //     }

    //     // Move ZIP file
    //     if ($zip && Storage::disk('s3')->exists($zip)) {
    //         $newZipPath = "{$finalDir}/{$zipName}";
    //         $zipContents = Storage::disk('s3')->get($zip);

    //         Storage::disk('s3')->put($newZipPath, $zipContents);
    //         Storage::disk('s3')->setVisibility($newZipPath, 'public');
    //         Storage::disk('s3')->delete($zip);

    //         \Log::info("✅ Moved ZIP to: {$newZipPath}");
    //     } else {
    //         \Log::warning("❌ ZIP file not found at: {$zip}");
    //     }
    // }



    protected function afterCreate(): void
    {
        $id = $this->record?->id;
        if (!$id) return;

        $finalDir = "imports/{$id}";

        // Get uploaded file paths from form
        $filePath = $this->data['file'] ?? null;
        $zipPath  = $this->data['zipfile'] ?? null;

        // Support array or string
        $filePath = is_array($filePath) ? reset($filePath) : $filePath;
        $zipPath  = is_array($zipPath) ? reset($zipPath) : $zipPath;

        $fileName = $filePath ? basename($filePath) : null;
        $zipName  = $zipPath ? basename($zipPath) : null;

        // Locate uploaded files
        $file = $fileName
            ? collect(Storage::disk('s3')->allFiles('imports/temp'))->first(fn($path) => basename($path) === $fileName)
            : null;

        $zip = $zipName
            ? collect(Storage::disk('s3')->allFiles('imports/temp'))->first(fn($path) => basename($path) === $zipName)
            : null;

        Log::info('Resolved actual uploaded paths', ['file' => $file, 'zipfile' => $zip]);

        // Move Excel
        if ($file && Storage::disk('s3')->exists($file)) {
            $newFilePath = "{$finalDir}/{$fileName}";
            $fileContents = Storage::disk('s3')->get($file);
            Storage::disk('s3')->put($newFilePath, $fileContents);
            Storage::disk('s3')->setVisibility($newFilePath, 'public');
            Storage::disk('s3')->delete($file);
            Log::info("Moved Excel to: {$newFilePath}");
        } else {
            Log::warning("Excel file not found at: {$file}");
        }

        // Move ZIP
        if ($zip && Storage::disk('s3')->exists($zip)) {
            $newZipPath = "{$finalDir}/{$zipName}";
            $zipContents = Storage::disk('s3')->get($zip);
            Storage::disk('s3')->put($newZipPath, $zipContents);
            Storage::disk('s3')->setVisibility($newZipPath, 'public');
            Storage::disk('s3')->delete($zip);
            Log::info("Moved ZIP to: {$newZipPath}");
        } else {
            Log::warning("ZIP file not found at: {$zip}");
        }

        //Create audit file path
        $auditFileName = 'Audit_log_' . Carbon::now()->format('YmdHis') . '.csv';
        $auditFolder = "audit_logs";
        $auditS3Path = "{$auditFolder}/{$auditFileName}";

        // Create empty audit file on S3
        Storage::disk('s3')->put($auditS3Path, '');
        Storage::disk('s3')->setVisibility($auditS3Path, 'public');

        //Prepare artisan command
        $commandData = json_encode([
            "file_path"     => $finalDir,
            "importType"    => 'product',
            'folder_id'     => $id,
            "auditFilename" => $auditFileName,
            "jobId"         => $id,
        ]);

        $command = "php artisan import:comman-data '{$commandData}'";

        //Update product_imports record
        try {
            $this->record->update([
                'name'            => 'product',
                'folder_path'     => $finalDir,
                'audit_file'      => $auditFileName,
                'command'         => $command,
                'retry_frequency' => 0, // default value
            ]);
            Notification::make()
                ->title('Imported successfully')
                ->body("Products are being processed in the background.You can track the progress in the Product Imports.")
                ->success()
                ->send();

            Log::info("Updated import record for ID {$id}", [
                'audit_file' => $auditFileName,
                'command'    => $command,
            ]);
        } catch (\Exception $e) {
            Log::error("Error updating import record:", ['error' => $e->getMessage()]);
        }
    }











    // protected function afterCreate(): void
    // {


    //     $file = $this->data['file'];
    //     $zipfile = $this->data['zipfile'];

    //     $filePath = reset($file);
    //     $zipFilePath = reset($zipfile);

    //     $disk = 's3';
    //     $import = $this->record;

    //     $folderPath = 'imports/' . $import->id;

    //     try {
    //         Storage::disk('public')->makeDirectory($folderPath);
    //     } catch (\Exception $e) {
    //         Log::error("Directory creation failed: " . $e->getMessage());
    //     }

    //     if (!Storage::disk('public')->exists($folderPath)) {
    //         // Create the directory
    //         Storage::disk('public')->makeDirectory($folderPath);

    //         // Optionally, set the permissions (if needed, for local filesystems only)
    //         $fullPath = storage_path("app/public/{$folderPath}");
    //         @chmod($fullPath, 0755);
    //     }


    //     $storedFilePath = "{$folderPath}/uploaded_file.xlsx";
    //     $storedZipFilePath = "{$folderPath}/uploaded_file.zip";

    //     try {
    //         Storage::disk('public')->move($filePath, $storedFilePath);
    //         Storage::disk('public')->move($zipFilePath, $storedZipFilePath);
    //     } catch (\Exception $e) {
    //         \Log::error("Error moving files:", ['error' => $e->getMessage()]);
    //     }

    //     try {
    //         $import->update([
    //             'name' => 'product',
    //             'folder_path' => $folderPath,
    //         ]);
    //     } catch (\Exception $e) {
    //         \Log::error("Error updating import record:", ['error' => $e->getMessage()]);
    //     }

    //     $auditFile = 'Audit_log_' . Carbon::now()->format('YmdHis') . '.csv';

    //     // 2. Define the folder path in S3 (e.g. 'imports/123')
    //     $folderPath = 'imports/' . $import->id;

    //     // 3. Full S3 file path
    //     $s3Path = "{$folderPath}/{$auditFile}";

    //     // 4. Generate CSV content (as string)
    //     $csvContent = [
    //         ['Timestamp', 'Event', 'User'],
    //         [now()->toDateTimeString(), 'Import started', auth()->user()->name ?? 'system'],
    //     ];

    //     // 5. Convert array to CSV string
    //     $stream = fopen('php://temp', 'r+');
    //     foreach ($csvContent as $row) {
    //         fputcsv($stream, $row);
    //     }
    //     rewind($stream);
    //     $csvString = stream_get_contents($stream);
    //     fclose($stream);

    //     // 6. Upload to S3
    //     Storage::disk('s3')->put($s3Path, $csvString, 'public');

    //     $commandData = json_encode([
    //         "file_path" => $folderPath,
    //         "importType" => 'product',
    //         'folder_id' => $import->id,
    //         "auditFilename" => $auditFile,
    //         "jobId" => $import->id,
    //     ]);


    //     $command = "php artisan import:comman-data '" . $commandData . "'";


    //     try {
    //         $import->update(['command' => $command, 'audit_file' => $auditFile]);
    //     } catch (\Exception $e) {
    //         Log::error("Error updating command or audit file:", ['error' => $e->getMessage()]);
    //     }
    // }



    public function download($file)
    {
        $path = "audit_logs/{$file}";
        // Check if the file exists in S3
        if (Storage::disk('s3')->exists($path)) {
            // Generate the public URL (assuming visibility is public)
            $url = Storage::disk('s3')->url($path);
            Log::info("File found in S3: " . $url);
            // Redirect to S3 for download
            return redirect()->away($url);
        } else {
            Log::error("File not found in S3: " . $path);
            return response()->json(['message' => 'File not found'], 404);
        }/*

        // $filePath = storage_path("app/public/audit_logs/{$file}");
        $file = "dpharma/audit_logs/{$file}";

        $s3Url = Storage::disk('s3')->url($file);


        if (Storage::disk('s3')->exists($file)) {
            $s3Url = Storage::disk('s3')->url($file);
            return redirect()->away($s3Url); // or return response()->json(['url' => $s3Url]);
        } else {
            return response()->json(['message' => 'File not found'], 404);
        } */
    }
}
