<?php

namespace App\Filament\Admin\Resources\ImportResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use App\Filament\Admin\Resources\ImportResource;
use App\Filament\Admin\Resources\ProductResource;

class ListImports extends ListRecords
{
    protected static string $resource = ImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('create')
                ->label('Create New Import')
                ->url($this->getResource()::getUrl('import-products'))
                ->icon('heroicon-o-plus'),
            Actions\Action::make('back')
                ->label('Back')
                ->color('gray')
                ->url(ImportResource::getUrl('import-products')),
        ];
    }
    public function getBreadcrumbs(): array
    {
        return [
            url(route('filament.admin.resources.products.index')) => 'Product Catalog',
            "#" => 'Product Imports',
        ];
    }
}
