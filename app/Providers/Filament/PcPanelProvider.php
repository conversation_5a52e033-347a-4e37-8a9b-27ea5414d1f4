<?php

namespace App\Providers\Filament;

use Filament\Pages;
use Filament\Panel;
use Filament\Widgets;
use App\Login\PcLogin;
use App\Login\AdminLogin;
use Filament\PanelProvider;
use App\Register\PcRegister;
use App\Models\SupportTicket;
use Filament\FilamentManager;
use Filament\Facades\Filament;
use Filament\Navigation\MenuItem;
use Filament\Support\Colors\Color;
use Illuminate\Support\HtmlString;
use App\Filament\Pc\Pages\Settings;
use Filament\View\PanelsRenderHook;
use App\Filament\Pc\Pages\Dashboard;
use App\Filament\Pc\Pages\MyProfile;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Config;
use Spatie\Activitylog\ActivityLogger;
use Filament\Navigation\NavigationItem;
use Filament\Navigation\NavigationGroup;
use Filament\Http\Middleware\Authenticate;
use Filament\Navigation\NavigationBuilder;
use App\Http\Middleware\ForcePasswordReset;
use Rmsramos\Activitylog\ActivitylogPlugin;
use App\Filament\Pc\Resources\OrderResource;
use App\Http\Middleware\IsPcProfileCompleted;
use App\ResetPassword\PcRequestPasswordReset;
use App\Filament\Pc\Resources\InvoiceResource;
use App\Filament\Pc\Resources\ProductResource;
use App\Filament\Pc\Resources\ShipmentResource;
use App\Http\Middleware\IsApprovedPcMiddleware;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Cookie\Middleware\EncryptCookies;
use App\Filament\Pc\Resources\UserManageResource;
use Filament\Http\Middleware\AuthenticateSession;
use Indianic\FilamentShield\FilamentShieldPlugin;
use TomatoPHP\FilamentSocial\FilamentSocialPlugin;
use App\Filament\Pc\Pages\Dashboard\OrderDashboard;
use App\Filament\Pc\Resources\ClinicDetailResource;
use Indianic\FilamentShield\Resources\RoleResource;
use App\Filament\Admin\Pages\PendingApprovalProducts;
use App\Filament\Admin\Resources\ActivityLogResource;
use App\Filament\Pc\Pages\Dashboard\FinanceDashboard;
use App\Filament\Pc\Pages\Dashboard\ProductDashborad;
use FilipFonal\FilamentLogManager\FilamentLogManager;
use Illuminate\Routing\Middleware\SubstituteBindings;
use App\Filament\Pc\Resources\CreditLineOrderResource;
use App\Filament\Pc\Resources\FacilityRequestResource;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Http\Middleware\RedirectToLandingPageMiddleware;
use Filament\Http\Middleware\DisableBladeIconComponents;
use App\Filament\Pc\Resources\OutStandingPaymentResource;
use App\Filament\Pc\Resources\FacilityOpenRequestResource;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;

use Njxqlus\FilamentProgressbar\FilamentProgressbarPlugin;
use App\Filament\Pc\Resources\PaymentEarningRecordResource;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use Solutionforest\FilamentEmail2fa\FilamentEmail2faPlugin;
use App\Filament\Pc\Resources\SupportTicketAssignedResource;
use App\Filament\Pc\Resources\SupportTicketReceivedResource;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PcPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {

        return $panel
            ->id('pc')
            ->domain(config('app.pc_url'))
            ->sidebarWidth('0rem')
            ->maxContentWidth('full')
            ->brandLogo(asset('images/logo.svg'))
            ->brandLogoHeight('38px')
            ->passwordReset()
            ->sidebarCollapsibleOnDesktop()
            ->colors([
                'primary' => "#004668",
                'create_button' => "#004668",
                'green' => Color::Green,
            ])
            ->viteTheme('resources/css/app.css')
            ->profile()
            ->darkMode(false)
            ->routes(function () {
                Route::fallback(function () {
                    $route = Filament::getCurrentPanel()->getUrl();
                    if (empty($route) || $route === "/") {
                        throw new NotFoundHttpException();
                    }
                    throw new \Symfony\Component\HttpKernel\Exception\HttpException(500);
                    //return redirect()->to($route);
                });
            })
            ->userMenuItems([
                'profile' => MenuItem::make()->url(fn() => MyProfile::getUrl())->icon(fn() => Auth::user()?->image_url ?? null),
                'logout' => MenuItem::make()
                    ->label('Sign out')
                    // ->postAction(function () {
                    //     Filament::auth()->logout();
                    //     session()->invalidate();
                    //     session()->regenerateToken();
                    // }),
                    ->url(fn() => route('filament.pc.auth.logout.test')),
            ])
            // ->authGuard('pc')
            ->login(PcLogin::class)
            ->passwordReset(PcRequestPasswordReset::class)
            ->registration(PcRegister::class)
            // ->viteTheme('resources/css/filament/theme.css')
            ->discoverResources(in: app_path('Filament/Pc/Resources'), for: 'App\\Filament\\Pc\\Resources')
            ->discoverPages(in: app_path('Filament/Pc/Pages'), for: 'App\\Filament\\Pc\\Pages')
            ->pages([
                Dashboard::class,
                OrderDashboard::class,
                ProductDashborad::class,
                FinanceDashboard::class,
                PendingApprovalProducts::class,
            ])
            ->homeUrl(fn() => Dashboard::getUrl())
            ->discoverWidgets(in: app_path('Filament/Pc/Widgets'), for: 'App\\Filament\\Pc\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->plugins([

                FilamentShieldPlugin::make(),
                FilamentEmail2faPlugin::make(),
                FilamentSocialPlugin::make()
                    ->socialLogin()
                    ->socialRegister(),
                FilamentProgressbarPlugin::make()->color('#29b'),
                FilamentApexChartsPlugin::make(),
                FilamentLogManager::make(),

            ])
            ->middleware([
                'web',
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
                IsApprovedPcMiddleware::class,
                RedirectToLandingPageMiddleware::class,
                // IsPcProfileCompleted::class,
                // ForcePasswordReset::class,
            ])
            ->databaseNotifications()
            // ->databaseNotificationsPolling('10s')
            ->authMiddleware([
                IsApprovedPcMiddleware::class,
                Authenticate::class,
                \App\Http\Middleware\ExtendSessionForRememberMe::class,
            ])
            ->renderHook(
                PanelsRenderHook::SIDEBAR_NAV_END,
                fn() => view('components.filament.sidebar-user')
            )->renderHook(
                'panels::body.end',
                fn(): string =>
                Blade::render('@livewire(\App\Livewire\NotificationPoller::class)') .
                    '<script>
                        document.addEventListener("livewire:initialized", () => {
                            Livewire.on("notification-received", (event) => {
                                const audio = new Audio("https://notificationsounds.com/storage/sounds/file-0b_oringz-pack-nine-07.ogg");
                                audio.play().catch(error => {
                                    console.error("Error playing notification sound:", error);
                                });
                            });
                            Livewire.hook("request", ({ fail }) => {
                                fail(async ({ status, preventDefault, retry }) => {
                                    if (status === 419) {
                                        preventDefault();
                                        console.log("419 error");
                                        // const response = await fetch("/refresh-csrf", {
                                        //     method: "GET",
                                        //     headers: {
                                        //         "Accept": "application/json",
                                        //     },
                                        //     credentials: "same-origin",
                                        // });

                                        // const data = await response.json();
                                        // const token = data.token;

                                        // document.querySelector("meta[name=\\"csrf-token\\"]")
                                        //     .setAttribute("content", token);

                                        // Livewire.csrfToken = token;
                                        retry();
                                    }
                                });
                            });
                            
                        });
                    </script>'
            )->navigation(fn(NavigationBuilder $navigation) => $this->buildNavigation($navigation));
    }

    protected function buildNavigation(NavigationBuilder $navigation): NavigationBuilder
    {
        // Cache the authenticated user to avoid duplicate queries
        $user = Auth::user();
        $isPharmaceuticalCompany = $this->isPharmaceuticalCompany($user);

        // Cache support ticket counts to avoid duplicate queries
        $supportTicketCounts = $this->getSupportTicketCounts($user);

        return $navigation->groups([

            NavigationGroup::make()
                ->items([
                    NavigationItem::make('Main')
                        ->label('Main')
                        ->url('')
                        ->isActiveWhen(fn() => false),
                ]),


            NavigationGroup::make('Dashboard')
                ->label('Dashboard')
                ->icon('heroicon-o-squares-2x2')
                ->items([
                    NavigationItem::make('Orders')
                        ->label('Orders')
                        ->url(fn() => OrderDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.pc.pages.order-dashboard') || request()->routeIs('filament.pc.pages.dashboard'))
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('dashboard_order view');
                        }),
                    NavigationItem::make('Products')
                        ->label('Products')
                        ->url(fn() => ProductDashborad::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.pc.pages.product-dashborad'))
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('dashboard_product view');
                        }),
                    NavigationItem::make('Finance')
                        ->label('Finance')
                        ->url(fn() => FinanceDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.pc.pages.finance-dashboard'))
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('dashboard_finance view');
                        }),
                ]),
            NavigationGroup::make()
                ->items([
                    NavigationItem::make('Portal User Manual')
                        ->label('Portal User Manual')
                        ->url('#')
                        ->isActiveWhen(fn() => false)
                        ->icon('bi-book'),
                ]),
            NavigationGroup::make('Orders Management')
                ->label('Orders Management')
                ->icon('heroicon-o-shopping-cart')
                ->items([
                    NavigationItem::make('All Orders')
                        ->label('All Orders')
                        ->url(fn() => OrderResource::getUrl('index'))
                        ->isActiveWhen(function () {
                            return request()->routeIs(OrderResource::getRouteBaseName() . '.*') && request('type') != 'accepted' && request('type') != 'credit_line' && request('type') != 'credit_line_detail';
                        })
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('all-orders_view') ||
                                $user->can('	all-orders_view details') || $user->can('all-orders_review order') || $user->can('all-orders_reject order') ||
                                $user->can('all-orders_chat') || $user->can('all-orders_approve order') || $user->can('all-orders_download po') ||
                                $user->can('all-orders_upload invoice') || $user->can('all-orders_export') || $user->can('all-orders_confirm order');
                        }),
                    NavigationItem::make("Accepted Orders")
                        ->label("Accepted Orders")
                        ->url(fn() => ShipmentResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ShipmentResource::getRouteBaseName() . '.*') || request('type') === 'accepted')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('accept-orders_view');
                        }),

                    NavigationItem::make('Credit Line Orders')
                        ->label('Credit Line Orders')
                        ->url(fn() => CreditLineOrderResource::getUrl('index'))
                        ->isActiveWhen(function () {
                            return request()->routeIs(CreditLineOrderResource::getRouteBaseName() . '.*') || request('type') === 'credit_line_detail' || request('type') === 'credit_line';
                        })
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('credit-line-orders_view');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    NavigationItem::make('my products')
                        ->label('My Products')
                        ->url(fn() => ProductResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ProductResource::getRouteBaseName() . '.*'))
                        ->icon('bi-box-seam')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('products_view')
                                || $user->can('products_update') || $user->can('products_delete') || $user->can('products_create') || $user->can('products_bulk stock update')
                                || $user->can('products_update price') || $user->can('products_update stock') || $user->can('products_approve');
                        }),
                    // above code will follow the model policy during rendering side bar, please follow this for other items.
                ]),

            NavigationGroup::make()
                ->items([
                    NavigationItem::make('Facilities')
                        ->label('Facilities')
                        ->url(fn() => ClinicDetailResource::getUrl('index'))
                        ->isActiveWhen(function () {
                            return request()->routeIs(ClinicDetailResource::getRouteBaseName() . '.*') ||
                                request()->routeIs(FacilityOpenRequestResource::getRouteBaseName() . '.*') ||
                                request()->routeIs(FacilityRequestResource::getRouteBaseName() . '.*');
                        })
                        ->icon('heroicon-o-building-office-2')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facility_view')
                                ||  $user->can('facility_open account request') || $user->can('facility_assign credit')
                                || $user->can('facility_edit assign credit') || $user->can('facility_approve facility')
                                || $user->can('facility_reject facility') || $user->can('facility_add facility');
                        }),
                ]),

            NavigationGroup::make('Payment Records')
                ->label('Payment Records')
                ->icon('heroicon-o-currency-dollar')
                ->items([
                    NavigationItem::make('Income Stream')
                        ->label('Income Stream')
                        ->url(fn() => PaymentEarningRecordResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(PaymentEarningRecordResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('payment-records_earnings view');
                        }),
                    NavigationItem::make('Payments')
                        ->label('Payments')
                        ->url(fn() => OutStandingPaymentResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(OutStandingPaymentResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('payment-records_payment view');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...InvoiceResource::getNavigationItems(),
                    NavigationItem::make('Invoices')
                        ->label('Invoices')
                        ->url(fn() => InvoiceResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(InvoiceResource::getRouteBaseName() . '.*'))
                        ->icon('heroicon-o-clipboard-document-list')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $isPharmaceuticalCompany || $user->hasRole('Super Admin') || $user->can('invoice-management_view invoice');
                            // return auth()->user()->can('invoice-management_view invoice', InvoiceResource::getModel());
                        }),
                ]),


            // NavigationGroup::make()
            //     ->items([
            //         NavigationItem::make('General')
            //             ->label('General')
            //             ->url('')
            //             ->isActiveWhen(fn() => false),
            //     ]),

            NavigationGroup::make('Support')
                ->label('Support')
                ->icon('heroicon-o-lifebuoy')
                ->items([
                    // ...SupportTicketAssignedResource::getNavigationItems(),
                    // ...SupportTicketReceivedResource::getNavigationItems(),

                    NavigationItem::make('DPharma Support')
                        ->label('DPharma Support')
                        ->url(fn() => SupportTicketAssignedResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(SupportTicketAssignedResource::getRouteBaseName() . '.*'))
                        ->badge(function () use ($supportTicketCounts) {
                            return (string) $supportTicketCounts['dpharma_support'];
                        }, color: 'primary')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('dpharma-support_view');
                        }),
                    NavigationItem::make('Facilities Support')
                        ->label('Facilities Support')
                        ->url(fn() => SupportTicketReceivedResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(SupportTicketReceivedResource::getRouteBaseName() . '.*'))
                        ->badge(function () use ($supportTicketCounts) {
                            return (string) $supportTicketCounts['facilities_support'];
                        }, color: 'primary')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('facilities-support_view');
                        }),

                ]),

            NavigationGroup::make()
                ->items([
                    // ...UserManageResource::getNavigationItems(),
                    NavigationItem::make('Users')
                        ->label('Users')
                        ->url(fn() => UserManageResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(UserManageResource::getRouteBaseName() . '.*'))

                        ->icon('heroicon-o-user-group')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('users_view')
                                || $user->can('users_create') || $user->can('users_update') || $user->can('users_delete')
                                || $user->can('users_change status');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...RoleResource::getNavigationItems(),
                    NavigationItem::make('Roles')
                        ->label('Roles')
                        ->url(fn() => RoleResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(RoleResource::getRouteBaseName() . '.*'))
                        ->icon('heroicon-s-shield-exclamation')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_view')
                                || $user->can('roles_create') || $user->can('roles_update') || $user->can('roles_delete')
                                || $user->can('roles_change status');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...Settings::getNavigationItems(),
                    NavigationItem::make('Settings')
                        ->label('Settings')
                        ->url(fn() => Settings::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.pc.pages.settings')) // Adjust if your panel or slug differs
                        ->icon('heroicon-o-cog-6-tooth')
                        ->visible(function () use ($user, $isPharmaceuticalCompany) {
                            return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('settings_view')
                                || $user->can('settings_order settings update') || $user->can('settings_datetime settings update');
                        })
                ]),


        ]);
    }

    /**
     * Get cached support ticket counts to avoid duplicate queries
     */
    private function getSupportTicketCounts($user): array
    {
        $baseQuery = SupportTicket::where('status', 'open');

        // DPharma Support count (sender_id)
        $dpharmaQuery = clone $baseQuery;
        if ($user->parent_id === null) {
            $dpharmaCount = $dpharmaQuery->where('sender_id', $user->id)->count();
        } else {
            $dpharmaCount = $dpharmaQuery->where(function ($query) use ($user) {
                $query->where('sender_id', $user->id)
                    ->orWhere('sender_id', $user->parent_id);
            })->count();
        }

        // Facilities Support count (receiver_id)
        $facilitiesQuery = clone $baseQuery;
        if ($user->parent_id === null) {
            $facilitiesCount = $facilitiesQuery->where('receiver_id', $user->id)->count();
        } else {
            $facilitiesCount = $facilitiesQuery->where(function ($query) use ($user) {
                $query->where('receiver_id', $user->id)
                    ->orWhere('receiver_id', $user->parent_id);
            })->count();
        }

        return [
            'dpharma_support' => $dpharmaCount,
            'facilities_support' => $facilitiesCount,
        ];
    }

    private function isPharmaceuticalCompany($user = null)
    {
        if ($user === null) {
            $user = Auth::user();
        }
        $parentUser = getUser($user);
        $isPharmaceuticalCompany = false;
        if ($user->id == $parentUser->id) {
            $isPharmaceuticalCompany = true;
        }
        return $isPharmaceuticalCompany;
    }
}
