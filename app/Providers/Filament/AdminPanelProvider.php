<?php

namespace App\Providers\Filament;

use Filament\Pages;
use Filament\Panel;
use App\Models\User;
use Filament\Widgets;
use App\Models\Product;
use App\Login\AdminLogin;
use App\Models\UserAddress;
use Filament\PanelProvider;
use App\Models\SupportTicket;
use Filament\Facades\Filament;
use Filament\Support\Assets\Js;
use Filament\Navigation\MenuItem;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use App\Models\ClinicPharmaSupplier;
use App\Models\SupportTicketMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Route;
use Indianic\CmsPages\CmsPagesPlugin;
use Indianic\Settings\SettingsPlugin;
use App\Filament\Admin\Pages\Settings;
use App\Filament\Admin\Pages\Dashboard;
use App\Filament\Admin\Pages\MyProfile;
use Filament\Navigation\NavigationItem;
use App\Filament\Admin\Pages\Commission;
use Filament\Navigation\NavigationGroup;
use Filament\Http\Middleware\Authenticate;
use Filament\Navigation\NavigationBuilder;
use Rmsramos\Activitylog\ActivitylogPlugin;
use App\Filament\Admin\Widgets\StatsOverview;
use App\Filament\Admin\Resources\UnitResource;
use App\Filament\Admin\Resources\UserResource;
use App\Filament\Admin\Resources\BrandResource;
use App\Filament\Admin\Resources\OrderResource;
use Illuminate\Session\Middleware\StartSession;
use Indianic\EmailTemplate\EmailTemplatePlugin;
use App\Filament\Admin\Resources\BannerResource;
use App\Filament\Admin\Resources\ClinicResource;
use App\ResetPassword\AdminRequestPasswordReset;
use Illuminate\Cookie\Middleware\EncryptCookies;
use App\Filament\Admin\Pages\ProfitShareSettings;
use App\Filament\Admin\Resources\InquiryResource;
use App\Filament\Admin\Resources\ProductResource;
use App\Filament\Admin\Widgets\UserStatsOverview;
use Indianic\FilamentShield\FilamentShieldPlugin;
use App\Filament\Admin\Resources\CategoryResource;
use App\Filament\Admin\Resources\InvoicesResource;
use App\Filament\Admin\Widgets\OrderStatsOverview;
use App\Filament\Admin\Resources\ContainerResource;
use App\Filament\Admin\Resources\OrderChatResource;
use App\Http\Middleware\ExtendSessionForRememberMe;
use Indianic\FilamentShield\Resources\RoleResource;
use App\Filament\Admin\Resources\DosageFormResource;
use App\Filament\Admin\Resources\FullPayoutResource;
use App\Filament\Admin\Resources\UserManageResource;
use App\Filament\Admin\Widgets\ProductStatsOverview;
use App\Filament\Admin\Pages\Dashboard\UserDashboard;
use App\Filament\Admin\Pages\PendingApprovalProducts;
use App\Filament\Admin\Resources\ActivityLogResource;
use App\Filament\Admin\Resources\DistributorResource;
use App\Filament\Admin\Resources\GenericNameResource;
use App\Filament\Admin\Resources\SubCategoryResource;
use App\Filament\Admin\Resources\TransactionResource;
use FilipFonal\FilamentLogManager\FilamentLogManager;
use Illuminate\Routing\Middleware\SubstituteBindings;
use App\Filament\Admin\Pages\Dashboard\OrderDashboard;
use App\Filament\Admin\Resources\PsUserManageResource;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use App\Filament\Admin\Resources\SupportTicketResource;
use App\Filament\Admin\Pages\Dashboard\FinanceDashboard;
use App\Filament\Admin\Pages\Dashboard\ProductDashborad;
use App\Filament\Admin\Resources\RolePermissionResource;
use Filament\Http\Middleware\DisableBladeIconComponents;
use App\Filament\Admin\Resources\CreditLineOrderResource;

use App\Filament\Admin\Resources\ScheduledPayoutResource;
use Indianic\CmsPages\Filament\Resources\CmsPageResource;
use App\Filament\Admin\Resources\PendingPsRequestResource;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Njxqlus\FilamentProgressbar\FilamentProgressbarPlugin;
use Leandrocfe\FilamentApexCharts\FilamentApexChartsPlugin;
use Solutionforest\FilamentEmail2fa\FilamentEmail2faPlugin;
use App\Filament\Admin\Resources\OutstandingPaymentResource;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use App\Filament\Admin\Resources\VerifyAccountNumberResource;
use App\Filament\Admin\Pages\Dashboard\PayoutHistoryDashboard;
use App\Filament\Admin\Resources\SupportTicketAssignedResource;
use App\Filament\Admin\Resources\PendingFacilityRequestResource;
use App\Filament\Admin\Resources\ShippingAddressRequestResource;
use Exception;
use Indianic\EmailTemplate\Filament\Resources\EmailTemplateResource;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {

        return $panel

            ->default()
            ->id('admin')
            // ->path('admin')
            ->sidebarWidth("344px")
            ->domain(config('app.url'))
            ->brandLogo(asset('images/logo.svg'))
            ->brandLogoHeight('38px')
            ->maxContentWidth('full')
            ->login(AdminLogin::class)
            ->font('Inter')
            ->passwordReset(AdminRequestPasswordReset::class)
            ->globalSearch(false)
            ->viteTheme('resources/css/app.css')
            ->profile()
            ->darkMode(false)
            ->colors([
                'primary' => "#004668",
                'create_button' => "#004668",
                'green' => Color::Green,
            ])

            ->databaseNotifications()
            ->userMenuItems([
                'profile' => MenuItem::make()->url(fn() => MyProfile::getUrl())->icon(fn() => Auth::user()?->image_url ?? null),
            ])
            ->discoverResources(in: app_path('Filament/Admin/Resources'), for: 'App\\Filament\\Admin\\Resources')
            ->discoverPages(in: app_path('Filament/Admin/Pages'), for: 'App\\Filament\\Admin\\Pages')
            ->pages([
                UserDashboard::class,
                OrderDashboard::class,
                ProductDashborad::class,
                Dashboard::class,
            ])
            ->homeUrl(fn() => Dashboard::getUrl())
            ->discoverWidgets(in: app_path('Filament/Admin/Widgets'), for: 'App\\Filament\\Admin\\Widgets')
            ->widgets([
                UserStatsOverview::class,
                OrderStatsOverview::class,
                ProductStatsOverview::class,
                StatsOverview::class,
            ])
            ->middleware([
                // ExtendSessionForRememberMe::class,
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,

            ])
            ->plugins([
                FilamentShieldPlugin::make(),
                FilamentEmail2faPlugin::make(),
                CmsPagesPlugin::make(),
                EmailTemplatePlugin::make(),
                SettingsPlugin::make(),
                FilamentProgressbarPlugin::make()->color('#29b'),
                FilamentLogManager::make(),
                FilamentApexChartsPlugin::make(),
                ActivitylogPlugin::make()
                    ->resource(ActivityLogResource::class)
            ])
            ->routes(function () {
                Route::fallback(function () {
                    $route = Filament::getCurrentPanel()->getUrl();
                    return redirect()->to($route);
                });
            })
            ->routes(function () {
                Route::fallback(function () {
                    $route = Filament::getCurrentPanel()->getUrl();
                    if (empty($route) || $route === "/") {
                        throw new NotFoundHttpException();
                    }
                    throw new \Symfony\Component\HttpKernel\Exception\HttpException(500);
                    //return redirect()->to($route);
                });
            })
            ->authMiddleware([
                Authenticate::class,
                \App\Http\Middleware\ExtendSessionForRememberMe::class,
            ])
            ->renderHook(
                PanelsRenderHook::SIDEBAR_NAV_END,
                fn() => view('components.filament.sidebar-user')
            )
            ->renderHook(
                'panels::body.end',
                fn(): string =>
                Blade::render('@livewire(\App\Livewire\NotificationPoller::class)') .
                    '<script>
                        document.addEventListener("livewire:initialized", () => {
                            Livewire.on("notification-received", (event) => {
                                const audio = new Audio("https://notificationsounds.com/storage/sounds/file-0b_oringz-pack-nine-07.ogg");
                                audio.play().catch(error => {
                                    console.error("Error playing notification sound:", error);
                                });
                            });

                            Livewire.hook("request", ({ fail }) => {
                                fail(async ({ status, preventDefault, retry }) => {
                                    if (status === 419) {
                                        preventDefault();
                                        
                                        const response = await fetch("/refresh-csrf", {
                                            method: "GET",
                                            headers: {
                                                "Accept": "application/json",
                                            },
                                            credentials: "same-origin",
                                        });

                                        const data = await response.json();
                                        const token = data.token;

                                        document.querySelector("meta[name=\\"csrf-token\\"]")
                                            .setAttribute("content", token);

                                        Livewire.csrfToken = token;
                                        retry();
                                    }
                                });
                            });
                        });
                    </script>'
            )
            ->navigation(fn(NavigationBuilder $navigation) => $this->buildNavigation($navigation));
    }

    protected function buildNavigation(NavigationBuilder $navigation): NavigationBuilder
    {
        // Cache the authenticated user to avoid duplicate queries
        $user = Auth::user();
        $isAdmin = isAdmin();

        // Cache support ticket counts to avoid duplicate queries
        $supportTicketCounts = $this->getSupportTicketCounts($user);

        return $navigation->groups([
            NavigationGroup::make()
                ->items([
                    NavigationItem::make('Main')
                        ->label('Main')
                        ->url('')
                        ->isActiveWhen(fn() => false),
                ]),

            NavigationGroup::make('Dashboard')
                ->label('Dashboard')
                ->icon('heroicon-o-squares-2x2')
                ->items([
                    // ...UserDashboard::getNavigationItems(),
                    NavigationItem::make('Users')
                        ->label('Users')
                        ->url(fn() => UserDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.user-dashboard') || request()->routeIs('filament.admin.pages.dashboard'))
                        ->visible(
                            function () use ($user, $isAdmin) {
                                return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('dashboard_users view');
                            }
                        ),
                    // ...FinanceDashboard::getNavigationItems(),
                    NavigationItem::make('Finance')
                        ->label('Finance')
                        ->url(fn() => FinanceDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.finance-dashboard'))
                        ->visible(
                            function () use ($user, $isAdmin) {
                                return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('dashboard_finance view');
                            }
                        ),
                    // ...OrderDashboard::getNavigationItems(),
                    NavigationItem::make('Orders')
                        ->label('Orders')
                        ->url(fn() => OrderDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.order-dashboard'))
                        ->visible(
                            function () use ($user, $isAdmin) {
                                return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('dashboard_order view');
                            }
                        ),
                    // ...ProductDashborad::getNavigationItems(),
                    NavigationItem::make('Products')
                        ->label('Products')
                        ->url(fn() => ProductDashborad::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.product-dashborad'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('dashboard_product view');
                        }),

                ]),

            NavigationGroup::make('Users')
                ->label('Users')
                ->icon('heroicon-o-user-group')
                ->items([
                    // ...ClinicResource::getNavigationItems(),
                    NavigationItem::make('Facilities')
                        ->label('Facilities')
                        ->url(fn() => ClinicResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ClinicResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('facility_view')
                                ||  $user->can('facility_create') || $user->can('facility_update');
                        }),
                    // ...UserResource::getNavigationItems(),
                    NavigationItem::make('Pharmaceutical Suppliers')
                        ->label('Pharmaceutical Suppliers')
                        ->url(fn() => UserResource::getUrl('index'))
                        ->badge(fn() => ($count = User::query()
                            ->role('Pharmaceutical Company')
                            ->where(function ($q) {
                                $q->where('verification_status', 'pending')
                                    ->orWhereNull('verification_status');
                            })
                            ->count()) > 0 ? $count : null)
                        ->isActiveWhen(fn() => request()->routeIs(UserResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('pharmaceutical-suppliers_view') || $user->can('pharmaceutical-suppliers_create') || $user->can('pharmaceutical-suppliers_update') || $user->can('pharmaceutical-suppliers_configure');
                        }),
                    // ...UserManageResource::getNavigationItems(),
                    NavigationItem::make('Admin Users')
                        ->label('Admin Users')
                        ->url(fn() => UserManageResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(UserManageResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('admin-users_view') || $user->can('admin-users_create')  || $user->can('admin-users_update') || $user->can('admin-users_delete');
                        }),
                    // ...PsUserManageResource::getNavigationItems(),
                    NavigationItem::make('PS Users')
                        ->label('PS Users')
                        ->url(fn() => PsUserManageResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(PsUserManageResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {


                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('ps-users_view')
                                || $user->can('ps-users_create') || $user->can('ps-users_update') || $user->can('ps-users_delete');
                        }),

                ]),

            NavigationGroup::make('Pending Request')
                ->label('Pending Request')
                ->icon('heroicon-o-clock')
                ->items([
                    // ...VerifyAccountNumberResource::getNavigationItems(),
                    NavigationItem::make('Verify Credit Line')
                        ->label('Verify Credit Line')
                        ->url(fn() => VerifyAccountNumberResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(VerifyAccountNumberResource::getRouteBaseName() . '.*'))
                        ->badge(fn() => ($count = ClinicPharmaSupplier::where('status', 'pending')->where('is_open_account', false)->count()) > 0 ? $count : null)
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('pending-requests_view verify credit line');
                        }),
                    // ...ShippingAddressRequestResource::getNavigationItems(),
                    NavigationItem::make('Shipping Address')
                        ->label('Shipping Address')
                        ->url(fn() => ShippingAddressRequestResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ShippingAddressRequestResource::getRouteBaseName() . '.*'))
                        ->badge(fn() => ($count = UserAddress::where('is_requested', true)->where('is_approved', false)->where('status', 'pending')->count()) > 0 ? $count : null)
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('pending-requests_view shipping address');
                        }),
                    // ...PendingPsRequestResource::getNavigationItems(),
                    NavigationItem::make('Pharma Supplier Update Requests')
                        ->label('Pharma Supplier Update Requests')
                        ->url(fn() => PendingPsRequestResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(PendingPsRequestResource::getRouteBaseName() . '.*'))
                        ->badge(function () {
                            $count = User::query()
                                ->role('Pharmaceutical Company')
                                ->whereHas('approvals', function ($q) {
                                    $q->where('user_type', 'pc')
                                        ->whereNull('approved_at')
                                        ->whereNull('rejected_at')
                                        ->whereNull('approved_by')
                                        ->whereNull('rejected_by');
                                })
                                ->count();
                            return $count > 0 ? $count : null;
                        })
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('pending-requests_view ps edit request');
                        }),
                    // ...PendingFacilityRequestResource::getNavigationItems(),
                    NavigationItem::make('Facility Update Requests')
                        ->label('Facility Update Requests')
                        ->url(fn() => PendingFacilityRequestResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(PendingFacilityRequestResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('pending-requests_view facility edit request');
                        })
                        ->badge(function () {
                            $count = User::query()
                                ->role('Clinic')
                                ->whereHas('approvals', function ($q) {
                                    $q->where('user_type', 'facility')
                                        ->whereNull('approved_at')
                                        ->whereNull('rejected_at')
                                        ->whereNull('approved_by')
                                        ->whereNull('rejected_by');
                                })
                                ->count();
                            return $count > 0 ? $count : null;
                        }),
                    NavigationItem::make('Pending Approval Products')
                        ->label('Pending Approval Products')
                        ->url(fn() => route('filament.admin.pages.pending-approval-products'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('pending-requests_approve product');
                        })
                        ->badge(fn() => ($count = Product::pendingApprovalsForAdmin()->count()) > 0 ? $count : null)
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.pending-approval-products'))

                ]),

            NavigationGroup::make('Orders Management')
                ->label('Orders Management')
                ->icon('heroicon-o-shopping-cart')
                ->items([
                    // ...CreditLineOrderResource::getNavigationItems(),
                    // ...OrderResource::getNavigationItems(),
                    NavigationItem::make('All Orders')
                        ->label('All Orders')
                        ->url(fn() => OrderResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(OrderResource::getRouteBaseName() . '.*') && request('type') != 'credit_line')
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('all-orders_view');
                        }),
                    NavigationItem::make('Credit Line Orders')
                        ->label('Credit Line Orders')
                        ->url(fn() => CreditLineOrderResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(CreditLineOrderResource::getRouteBaseName() . '.*') || request('type') === 'credit_line')
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('viewAny');
                            // return $user->can('viewAny', CreditLineOrderResource::getModel());
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...ProductResource::getNavigationItems(),
                    NavigationItem::make('Product Catalog')
                        ->label('Product Catalog')
                        ->icon('bi-box-seam')
                        ->url(fn() => ProductResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ProductResource::getRouteBaseName() . '.*') || request()->is('imports/import-products')
                            || request()->is('imports/list-imports'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('products_view');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...CmsPageResource::getNavigationItems(),
                    NavigationItem::make('Static Pages')
                        ->label('Static Pages')
                        ->icon('heroicon-o-code-bracket-square')
                        ->url(fn() => CmsPageResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(CmsPageResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('static-pages_view');
                        }),
                ]),

            NavigationGroup::make('Finance')
                ->label('Finance')
                ->icon('heroicon-o-currency-dollar')
                ->items([
                    // ...PayoutHistoryDashboard::getNavigationItems(),
                    NavigationItem::make('Dashboard')
                        ->label('Dashboard')
                        ->url(fn() => PayoutHistoryDashboard::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.payout-history-dashboard'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('finance_dashboard');
                        }),
                    // ...FullPayoutResource::getNavigationItems(),
                    NavigationItem::make('Full Payouts')
                        ->label('Full Payouts')
                        ->url(fn() => FullPayoutResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(FullPayoutResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('finance_full payout');
                        }),
                    // ...ScheduledPayoutResource::getNavigationItems(),
                    NavigationItem::make('Scheduled Payouts')
                        ->label('Scheduled Payouts')
                        ->url(fn() => ScheduledPayoutResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ScheduledPayoutResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('finance_schedule payout');
                        }),
                    // ...OutstandingPaymentResource::getNavigationItems(),
                    NavigationItem::make('Outstanding Payments')
                        ->label('Outstanding Payments')
                        ->url(fn() => OutstandingPaymentResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(OutstandingPaymentResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('finance_outstanding payment');
                        }),
                    NavigationItem::make('Invoices')
                        ->label('Invoices')
                        ->url(fn() => InvoicesResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(InvoicesResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin');
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    // ...EmailTemplateResource::getNavigationItems(),
                    NavigationItem::make('Email Templates')
                        ->label('Email Templates')
                        ->icon('heroicon-o-envelope')
                        ->url(fn() => EmailTemplateResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(EmailTemplateResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('email-templates_view') ||  $user->can('email-templates_update') ||  $user->can('email-templates_create') ||  $user->can('email-templates_delete');
                        }),
                ]),
            NavigationGroup::make()
                ->items([
                    // ...TransactionResource::getNavigationItems(),
                    NavigationItem::make('Transactions')
                        ->label('Transactions')
                        ->icon('heroicon-o-rectangle-stack')
                        ->url(fn() => TransactionResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(TransactionResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('transactions_view') ||  $user->can('transactions_update') ||  $user->can('transactions_create') ||  $user->can('transactions_delete');
                        }),
                ]),
            // NavigationGroup::make()
            //     ->items([
            //         ...InvoicesResource::getNavigationItems(),
            //     ]),
            NavigationGroup::make()
                ->items([
                    // ...BannerResource::getNavigationItems(),
                    NavigationItem::make('Banner Management')
                        ->label('Banner Management')
                        ->icon('heroicon-o-rectangle-stack')
                        ->url(fn() => BannerResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(BannerResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') ||  $user->can('banners_view') ||  $user->can('banners_create') ||  $user->can('banners_update') ||  $user->can('banners_delete');
                            //return $user->can('banners_view', BannerResource::getModel());
                        }),
                ]),
            NavigationGroup::make()
                ->items([
                    // ...RoleResource::getNavigationItems(),
                    NavigationItem::make('Roles')
                        ->label('Roles')
                        ->icon('heroicon-s-shield-exclamation')
                        ->url(fn() => RoleResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(RoleResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {

                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('roles_view')
                                || $user->can('roles_create') || $user->can('roles_update') || $user->can('roles_delete')
                                || $user->can('roles_change status');
                        }),
                ]),

            NavigationGroup::make('PS Users')
                ->label('PS User Roles')
                ->icon('heroicon-o-users')
                ->items([
                    // ...RolePermissionResource::getNavigationItems(),af
                    NavigationItem::make('Roles & Permissions')
                        ->label('Roles & Permissions')
                        ->url(fn() => RolePermissionResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(RolePermissionResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('roles_view')
                                || $user->can('roles_create') || $user->can('roles_update') || $user->can('roles_delete');
                        }),
                ]),
            NavigationGroup::make()
                ->items([
                    // ...ActivityLogResource::getNavigationItems(),
                    NavigationItem::make('Activity Logs')
                        ->label('Activity Logs')
                        ->icon('heroicon-o-clock')
                        ->url(fn() => ActivityLogResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ActivityLogResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('activity-logs_view');
                            // return $user->can('activity-logs_view', ActivityLogResource::getModel());
                        }),
                ]),

            NavigationGroup::make()
                ->items([
                    NavigationItem::make('General')
                        ->label('General')
                        ->url('')
                        ->isActiveWhen(fn() => false),
                ]),

            NavigationGroup::make('Master')
                ->label('Master')
                ->icon('heroicon-o-pencil-square')
                ->items([
                    // ...CategoryResource::getNavigationItems(),
                    NavigationItem::make('Categories')
                        ->label('Categories')
                        ->url(fn() => CategoryResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(CategoryResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('categories_view')
                                || $user->can('categories_create') || $user->can('categories_update') || $user->can('categories_delete');
                            // return $user->can('categories_view', CategoryResource::getModel());
                        }),
                    // ...SubCategoryResource::getNavigationItems(),
                    NavigationItem::make('Sub Categories')
                        ->label('Sub Categories')
                        ->url(fn() => SubCategoryResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(SubCategoryResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('sub-categories_view')
                                || $user->can('sub-categories_create') || $user->can('sub-categories_update') || $user->can('sub-categories_delete');
                            // return $user->can('sub-categories_view', SubCategoryResource::getModel());
                        }),
                    // ...DosageFormResource::getNavigationItems(),
                    NavigationItem::make('Dosage Form')
                        ->label('Dosage Form')
                        ->url(fn() => DosageFormResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(DosageFormResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('dosage-forms_view')
                                || $user->can('dosage-forms_create') || $user->can('dosage-forms_update') || $user->can('dosage-forms_delete');
                            // return $user->can('dosage-forms_view', DosageFormResource::getModel());
                        }),
                    // ...BrandResource::getNavigationItems(),
                    NavigationItem::make('Brands')
                        ->label('Brands')
                        ->url(fn() => BrandResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(BrandResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('brands_view')
                                || $user->can('brands_create') || $user->can('brands_update') || $user->can('brands_delete');
                            // return $user->can('brands_view', BrandResource::getModel());
                        }),
                    // ...UnitResource::getNavigationItems(),
                    NavigationItem::make('Volume Unit')
                        ->label('Volume Unit')
                        ->url(fn() => UnitResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(UnitResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('volume-units_view')
                                || $user->can('volume-units_create') || $user->can('volume-units_update') || $user->can('volume-units_delete');
                            // return $user->can('units_view', UnitResource::getModel());
                        }),
                    // ...Commission::getNavigationItems(),
                    NavigationItem::make('Global Commission')
                        ->label('Global Commission')
                        ->url(fn() => Commission::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.commission'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('global-commission_view')
                                || $user->can('global-commission_update');

                            // return $user->can('global-commission_view', Commission::getModel());
                        }),
                    // ...GenericNameResource::getNavigationItems(),
                    NavigationItem::make('Generic Names')
                        ->label('Generic Names')
                        ->url(fn() => GenericNameResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(GenericNameResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('generic-names_view')
                                || $user->can('generic-names_create') || $user->can('generic-names_update') || $user->can('generic-names_delete');
                            // return $user->can('generic-names_view', GenericNameResource::getModel());
                        }),
                    // ...ContainerResource::getNavigationItems(),
                    NavigationItem::make('Packages')
                        ->label('Packages')
                        ->url(fn() => ContainerResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(ContainerResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('packages_view')
                                || $user->can('packages_create') || $user->can('packages_update') || $user->can('packages_delete');

                            // return $user->can('packages_view', ContainerResource::getModel());
                        }),
                    // ...DistributorResource::getNavigationItems(),
                    NavigationItem::make('Distributors')
                        ->label('Distributors')
                        ->url(fn() => DistributorResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(DistributorResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('distributors_view')
                                || $user->can('distributors_create') || $user->can('distributors_update') || $user->can('distributors_delete');
                            // return $user->can('distributors_view', DistributorResource::getModel());
                        }),
                ]),

            NavigationGroup::make('Support')
                ->label('Support')
                ->icon('heroicon-o-lifebuoy')
                ->items([
                    // ...SupportTicketAssignedResource::getNavigationItems(),
                    NavigationItem::make('All Conversation')
                        ->label('All Conversation')
                        ->url(fn() => SupportTicketAssignedResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(SupportTicketAssignedResource::getRouteBaseName() . '.*'))
                        ->badge(function () {
                            $unreadCount = \App\Models\SupportTicketMessage::where('is_read', false)
                                ->where('from_id', '!=', auth()->id())
                                ->whereHas('ticket', function ($query) {
                                    $query->where('status', 'open');
                                })
                                ->count();
                            return $unreadCount > 0 ? (string) $unreadCount : null;
                        }, color: 'primary')
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('support-tickets_all conversation');

                            // return $user->can('support-tickets_all conversation', SupportTicketAssignedResource::getModel());
                        }),
                    // ...SupportTicketResource::getNavigationItems(),
                    NavigationItem::make('Support Tickets')
                        ->label('Support Tickets')
                        ->url(fn() => SupportTicketResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(SupportTicketResource::getRouteBaseName() . '.*'))
                        ->badge(function () use ($supportTicketCounts) {
                            return (string) $supportTicketCounts['openSupportTickets'];
                        }, color: 'primary')
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('support-tickets_support ticket view');
                            // return $user->can('support-tickets_received ticket', SupportTicketResource::getModel());
                        }),
                    NavigationItem::make('All Order Chats')
                        ->label('All Order Chats')
                        ->url(fn() => OrderChatResource::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs(OrderChatResource::getRouteBaseName() . '.*'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('support-tickets_all orders chat');
                            // return $user->can('support-tickets_received ticket', SupportTicketResource::getModel());
                        })
                        ->badge(function () {
                            $unreadCount = \App\Models\ThreadMessage::where('is_read', false)
                                ->where('from_id', '!=', auth()->id())
                                ->count();
                            return $unreadCount > 0 ? (string) $unreadCount : null;
                        }, color: 'primary'),
                    // ...InquiryResource::getNavigationItems(),
                    NavigationItem::make('Inquiries')
                        ->label('Inquiries')
                        ->url(fn() => InquiryResource::getUrl('index'))
                        ->isActiveWhen(fn() => request()->routeIs(InquiryResource::getRouteBaseName() . '.*'))
                        ->badge(function () {
                            $count = \App\Models\Inquiry::whereNull('read_at')->count();
                            return $count > 0 ? $count : null;
                        }, color: 'primary')
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('inquiries_view');
                        }),
                ]),

            NavigationGroup::make('Settings')
                ->label('Settings')
                ->icon('heroicon-o-cog-8-tooth')
                ->items([
                    // ...ProfitShareSettings::getNavigationItems(),
                    NavigationItem::make('Profit Share')
                        ->label('Profit Share')
                        ->url(fn() => ProfitShareSettings::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.profit-share-settings'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('settings_profit share');

                            // return $user->can('settings_profit share', ProfitShareSettings::getModel());
                        }),
                    // ...Settings::getNavigationItems(),
                    NavigationItem::make('Settings')
                        ->label('Settings')
                        ->url(fn() => Settings::getUrl())
                        ->isActiveWhen(fn() => request()->routeIs('filament.admin.pages.settings'))
                        ->visible(function () use ($user, $isAdmin) {
                            return $isAdmin || $user->hasRole('Super Admin') || $user->can('settings_view');
                            // return $user->can('settings_view', Settings::getModel());
                        }),

                ]),

        ]);
    }

    /**
     * Cache support ticket counts to avoid duplicate queries
     */
    private function getSupportTicketCounts($user): array
    {
        $openSupportTickets = SupportTicket::where('status', 'open')->count();
        $unreadMessageCount = SupportTicketMessage::whereHas('ticket', function ($query) {
            $query->where('status', 'open');
        })->where('is_read', false)->where('from_id', '!=', $user->id)->count();

        return [
            'openSupportTickets' => $openSupportTickets,
            'unreadMessageCount' => $unreadMessageCount
        ];
    }
}
