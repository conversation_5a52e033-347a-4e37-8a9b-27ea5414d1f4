<?php

namespace App\Providers;

use App\Models\Role;
use App\Models\Brand;
use App\Models\Banner;
use Livewire\Livewire;
use App\Models\Category;
use Illuminate\View\View;
use App\Models\DosageForm;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use App\Models\ClinicDetail;
use Illuminate\Http\Request;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use App\Livewire\InfoListPrice;
use App\Policies\Pc\RolePolicy;
use Filament\Support\Assets\Js;
use Illuminate\Support\HtmlString;
use App\Policies\Admin\BrandPolicy;
use Filament\View\PanelsRenderHook;
use Illuminate\Support\Facades\URL;
use App\Policies\Admin\BannerPolicy;
use Illuminate\Routing\UrlGenerator;
use App\Policies\Admin\CmsPagePolicy;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Illuminate\Support\Facades\Route;
use Indianic\CmsPages\Models\CmsPage;
use App\Policies\Admin\CategoryPolicy;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Schema;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\ServiceProvider;
use Spatie\Activitylog\Models\Activity;
use App\Policies\Admin\DosageFormPolicy;
use Filament\Forms\Components\TextInput;
use Filament\Tables\Columns\ImageColumn;
use Laravel\Socialite\Facades\Socialite;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\ToggleColumn;
use Illuminate\Support\Facades\Validator;

//for apple login
use Illuminate\Validation\Rules\Password;
use App\Policies\Admin\ClinicDetailPolicy;
use Filament\Support\Facades\FilamentView;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse;
use App\Response\AdminLogoutResponse;
use App\Response\PcLogoutResponse;
use Filament\Support\Facades\FilamentAsset;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\ImageEntry;
use SocialiteProviders\Apple\Provider as AppleProvider;

class AppServiceProvider extends ServiceProvider
{
    protected $policies = [
        // Activity::class => ActivityPolicy::class,
        Banner::class => BannerPolicy::class,
        Brand::class => BrandPolicy::class,
        Category::class => CategoryPolicy::class,
        ClinicDetail::class => ClinicDetailPolicy::class,
        CmsPage::class => CmsPagePolicy::class,
        DosageForm::class => DosageFormPolicy::class,
        Role::class => RolePolicy::class,
    ];
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Your existing register code remains the same
        UrlGenerator::macro('alternateHasCorrectSignature', function (Request $request, $absolute = true, array $ignoreQuery = []) {
            $ignoreQuery[] = 'signature';

            $absoluteUrl = url($request->path());
            $url = $absolute ? $absoluteUrl : '/' . $request->path();

            $queryString = collect(explode('&', (string) $request->server->get('QUERY_STRING')))
                ->reject(fn($parameter) => in_array(Str::before($parameter, '='), $ignoreQuery))
                ->join('&');

            $original = rtrim($url . '?' . $queryString, '?');

            $signature = hash_hmac('sha256', $original, config('app.key'));

            return hash_equals($signature, (string) $request->query('signature', ''));
        });

        UrlGenerator::macro('alternateHasValidSignature', function (Request $request, $absolute = true, array $ignoreQuery = []) {
            return URL::alternateHasCorrectSignature($request, $absolute, $ignoreQuery)
                && URL::signatureHasNotExpired($request);
        });

        Request::macro('hasValidSignature', function ($absolute = true, array $ignoreQuery = []) {
            return URL::alternateHasValidSignature($this, $absolute, $ignoreQuery);
        });

        // Bind logout responses for different panels
        $this->app->bind(LogoutResponse::class, function ($app) {
            $currentPanel = Filament::getCurrentPanel();

            if ($currentPanel && $currentPanel->getId() === 'admin') {
                return new AdminLogoutResponse();
            } elseif ($currentPanel && $currentPanel->getId() === 'pc') {
                return new PcLogoutResponse();
            }

            // Default logout response
            return new \Filament\Http\Responses\Auth\LogoutResponse();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // \Debugbar::disable();
        // set_error_handler(function ($severity, $message, $file, $line) {
        //     if (error_reporting() & $severity) {
        //         Log::warning("PHP Warning: $message in $file:$line");
        //         throw new \ErrorException($message, 0, $severity, $file, $line);
        //     }
        // });
        Livewire::component('info-list-price', InfoListPrice::class);
        $this->calculateNetEarnings();
        $this->calculateNetEarningsForRepeater();
        DatePicker::configureUsing(function (DatePicker $date) {
            $date->native(condition: false)
                // ->format(getFormatedDateForPc())
                // ->displayFormat(getFormatedDateForPc())
                ->closeOnDateSelection()
                ->suffixIcon('heroicon-c-calendar'); // Disable future dates
        });

        \Filament\Notifications\Actions\Action::configureUsing(function (\Filament\Notifications\Actions\Action $action) {
            $action->color('info');
        });


        Validator::extend('price', function ($attribute, $value, $parameters, $validator) {
            if (!is_numeric($value)) {
                return false;
            }

            // Ensure value is within decimal(10, 2) range
            return $value <= 99999999.99 && $value > 0;
        }, 'The :attribute must be a valid price with up to 10 digits,2 decimal places and gt than 0.');

        TextInput::macro('price', function ($isRequired = false, $maxValue = null) {
            $required = $isRequired ? 'required' : null;
            $max = !empty($maxValue) ? "max:$maxValue" : null;
            return $this->numeric()->rules(['numeric', 'price', $required, $max]);
        });
        Model::unguard();


        $currentHost = request()->getHost();
        $currentScheme = request()->getScheme();

        // Ensure base URL always uses HTTPS
        if (app()->environment() != 'local') {
            if (str_contains($currentHost, 'admin')) {
                $baseUrl = str_replace('http://', 'https://', config('app.admin_url', "https://admin-dpharma-dev.devpress.net/"));
            } elseif (str_contains($currentHost, 'pc')) {
                $baseUrl = str_replace('http://', 'https://', config('app.pc_url', "https://pc-dpharma-dev.devpress.net/"));
            } elseif (str_contains($currentHost, 'clinic')) {
                $baseUrl = str_replace('http://', 'https://', config('app.clinic_url', "https://clinic-dpharma-dev.devpress.net/"));
            } else {
                $baseUrl = str_replace('http://', 'https://', config('app.url', "https://admin-dpharma-dev.devpress.net/"));
            }
        } else {
            if (str_contains($currentHost, 'admin')) {
                $baseUrl = config('app.admin_url', "https://admin-dpharma-dev.devpress.net/");
            } elseif (str_contains($currentHost, 'pc')) {

                $baseUrl = config('app.pc_url', "https://pc-dpharma-dev.devpress.net/");
            } elseif (str_contains($currentHost, 'clinic')) {
                $baseUrl = config('app.clinic_url', "https://clinic-dpharma-dev.devpress.net/");
            } else {
                $baseUrl = config('app.url', "https://admin-dpharma-dev.devpress.net/");
            }
        }

        if (! app()->environment('local')) {
            URL::forceScheme('https');
            URL::forceRootUrl($baseUrl);

            // Force HTTPS for all asset URLs
            if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') {
                // Force assets to load over HTTPS
                $this->app['request']->server->set('HTTPS', 'on');
            }
        }

        if ($this->app->environment('local')) {
            URL::forceScheme('http');
        }

        Password::defaults(function () {
            return Password::min(8)->mixedCase()->symbols()->numbers();
        });

        // Ensure config URLs are always HTTPS
        Config::set('app.url', $baseUrl);
        if (app()->environment() != 'local') {
            Config::set('filesystems.disks.public.url', str_replace('http://', 'https://', "{$baseUrl}/storage"));
        } else {
            Config::set('filesystems.disks.public.url', "{$baseUrl}/storage");
        }

        if (in_array(app()->environment(), ['production', 'staging', 'develop'])) {
            $this->app['url']->forceScheme('https');
            $this->app['request']->server->set('HTTPS', 'on');
        }
        FileUpload::configureUsing(function (FileUpload $component) {
            $component->disk(config('filesystems.default'))->visibility(config('filesystems.disks.s3.visibility', 'private')); //->visibility('public');
        });
        // Default disk for ImageColumn
        ImageColumn::configureUsing(function (ImageColumn $column) {
            $column->disk(config('filesystems.default'));
        });
        ImageEntry::configureUsing(function (ImageEntry $column) {
            $column->disk(config('filesystems.default'));
        });

        Activity::saving(function (Activity $activity) {
            $activity->ip_address = request()->ip();
        });

        ToggleColumn::configureUsing(function (ToggleColumn $column) {
            $column->onColor('success')->offColor('gray');
        });

        Toggle::configureUsing(function (Toggle $column) {
            $column->onColor('success')->offColor('gray');
        });

        //register apple provider for apple login
        Socialite::extend('apple', function ($app) {
            $config = $app['config']['services.apple'];

            return Socialite::buildProvider(AppleProvider::class, $config);
        });

        Table::configureUsing(function (Table $table): void {
            $table
                ->paginated([10, 25, 50, 100])
                ->defaultPaginationPageOption(10);
        });

        // Set default timezone for TextColumn for datetime attributes
        TextColumn::configureUsing(function (TextColumn $component): void {
            if (in_array($component->getName(), ['created_at', 'updated_at', 'published_at'])) {
                $component->timezone(auth()->user()?->timezone ?? config('app.timezone'));
            }
        });

        TextEntry::configureUsing(function (TextEntry $component): void {
            if (in_array($component->getName(), ['created_at', 'updated_at', 'published_at'])) {
                $component->timezone(auth()->user()?->timezone ?? config('app.timezone'));
            }
        });
    }


    public function calculateNetEarnings()
    {
        return TextInput::macro('calculateNetEarnings', function ($commission, $commissionType, $fieldId, $currentField) {
            return $this->extraAlpineAttributes(function ($get, $set) use ($commission, $commissionType, $fieldId, $currentField) {
                return [
                    'x-data' => "{
                        calculate(event) {
                            try {
                                console.log('event:', event);
                                // Get value from event target
                                let bonus_price = parseFloat(event?.target?.value) || 0;
                                let admin_fees = parseFloat('" . $commission . "') || 0;
                                let commission_type = '" . $commissionType . "';
                                console.log('bonus_price:', bonus_price);
                                if (bonus_price < 0 || admin_fees < 0) {
                                    console.error('Invalid negative values');
                                    return;
                                }

                                let commission = 0;
                                let earnings = 0;

                                if (commission_type === 'percentage') {
                                    commission = bonus_price * (admin_fees / 100);
                                } else {
                                    commission = admin_fees;
                                }

                                earnings = bonus_price - commission;

                                if (isNaN(earnings)) {
                                    console.error('Invalid calculation result');
                                    return;
                                }

                                // Update the net earnings field
                                let netEarningsField = document.getElementById('" . $fieldId . "');
                                if (netEarningsField) {
                                    netEarningsField.value = earnings.toFixed(2);
                                }
                            } catch (error) {
                                console.error('Calculation error:', error);
                            }
                        }
                    }",
                    'x-init' => "setTimeout(() => {
                        let inputField = document.getElementById('$currentField');
                        console.log('inputField:', inputField);
                        if (inputField) {
                            calculate({ target: inputField });
                        }
                    }, 500)",
                    '@change' => "calculate(event)"
                ];
            });
        });
    }

    public function calculateNetEarningsForRepeater()
    {
        return TextInput::macro('calculateNetEarningsForRepeater', function ($commission, $commissionType) {
            return $this->extraAlpineAttributes(function ($get, $set) use ($commission, $commissionType) {
                return [
                    'x-data' => "{
                    calculate(event) {
                        const input = event.target;
                        const price = parseFloat(input.value) || 0;
                        const admin_fees = parseFloat('" . $commission . "') || 0;
                        const commission_type = '" . $commissionType . "';
                        let commission = 0;
                        if (commission_type === 'percentage') {
                            commission = price * (admin_fees / 100);
                        } else {
                            commission = admin_fees;
                        }
                        const earnings = Math.max(price - commission, 0); // Prevent negative

                        const netEarningsInput = input.closest('tr').querySelectorAll('[type=text]')[1];
                        if (netEarningsInput) {
                            netEarningsInput.value = new Intl.NumberFormat('en-US', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            }).format(earnings);
                        }
                    }
                }",
                    '@input' => 'calculate($event)',
                    '@change' => 'calculate($event)',
                    'x-init' => 'setTimeout(() => calculate({target: $el}), 100)',
                ];
            });
        });

        // Register model observers for cache invalidation
        \App\Models\ProductRelation::observe(\App\Observers\ProductRelationObserver::class);
        \App\Models\ProductRelationStock::observe(\App\Observers\ProductRelationStockObserver::class);
        \App\Models\ProductRelationPrice::observe(\App\Observers\ProductRelationPriceObserver::class);
    }
}
