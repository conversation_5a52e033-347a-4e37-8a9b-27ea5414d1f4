<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;

class PcResetPasswordMail extends Mailable implements \Illuminate\Contracts\Queue\ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public $user;
    public $code;

    public string $htmlBody;

    public string $emailSubject;

    public string $name;


    public function __construct($user, $code)
    {
        $this->user = $user;
        $this->code = $code;
        $this->name = $user->name;

        $template = DB::table('email_templates')->where('key', 'PS_RESET_PASSWORD')->first();

        $this->emailSubject = $template->subject ?? '2 FA Code';

        $this->htmlBody = $this->parseTemplate($template->email_content, [
            'name' => $this->name,
            'code' => $this->code,
        ]);
    }

    private function parseTemplate(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{{ ' . $key . ' }}', $value, $template);
        }
        return $template;
    }

    /**
     * Get the message envelope.
     */
    public function build()
    {
        return $this->subject($this->emailSubject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->htmlBody,
            'logo' => asset('images/logo.png')
        ]);
    }
}
