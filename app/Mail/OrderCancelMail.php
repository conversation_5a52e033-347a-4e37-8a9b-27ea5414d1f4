<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class OrderCancelMail extends Mailable
{
    use Queueable, SerializesModels;

    public $order;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($order)
    {
        $this->order = $order;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_ORDER_CANCEL_CONFIRMATION']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->order->user->name,
                'ORDERID' => $this->order->order_number,
                'CREATEDDATE' => convertToUserMailTimezone($this->order->user->timezone,$this->order->created_at, 'd M Y  H:i'),
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ]);
    }
}
