<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\DB;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProductApprovedMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $htmlBody;
    public $subject;
    public $product;
    public $user;
    /**
     * Create a new message instance.
     */
    public function __construct($product, $user)
    {
        $this->product = $product;
        $this->user = $user;

        $template = DB::table('email_templates')->where('key', 'PRODUCT_APPROVED')->first();

        $this->subject = $template->subject ?? 'Product Approved';

        $this->htmlBody = $this->parseTemplate($template->email_content, [
            'user_name' => $this->user?->name,
            'product_name' => $this->product?->name,
            'category_name' => $this->product?->category?->name,
            'subcategory_name' => $this->product?->subcategory?->name,
        ]);
        
        
    }

    private function parseTemplate(string $template, array $data): string

    {
        foreach ($data as $key => $value) {
            $template = str_replace('{{ ' . $key . ' }}', $value, $template);
        }
        return $template;
    }

    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
                    ->with([
                        'html' => $this->htmlBody,
                        'logo' => asset('images/logo.png')
                    ]);
    }
}
