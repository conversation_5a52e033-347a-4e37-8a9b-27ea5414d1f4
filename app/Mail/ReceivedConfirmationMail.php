<?php

namespace App\Mail;

use App\Models\Payout;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Spatie\Browsershot\Browsershot;

class ReceivedConfirmationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $record;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($record)
    {
        $this->record = $record;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_COMMISSION_RECEIVED_MAIL']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->record->user->name,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }


    public function build()
    { 
        try {
            $record = Payout::with('payoutSubOrders.subOrder.orderProducts.product')->find($this->record->id);

            $fileName = 'invoice_' . $this->record->id . '.pdf';
            $s3Path = config('constants.api.order_invoices.admin_received_invoice') . $fileName;

            $html = view('pdf.admin-received-pay-pdf', [
                'record' => $record,
                'adminSettingDetail' => adminSettingDetail()
            ])->render();

                $pdfBinary = Browsershot::html($html)
                        ->setChromePath('/usr/bin/google-chrome')
                        ->format('A4')
                        ->noSandbox()
                        ->pdf();

            Storage::disk(config(FILESYSTEM_DEFAULT))->put($s3Path, $pdfBinary);
            $record->update(['invoice_path' => $fileName]);
            $payoutDate = Carbon::parse($record->payout_on)->format('Y_m_d');

            return $this->subject($this->subject)
            ->view('emails.common_mail')
            ->with([
                'html' => $this->emailContent,
                'logo' => asset('images/logo.png')
            ])->attachData($pdfBinary, 'outstanding_payout_'.$payoutDate.'_invoice.pdf', [
                'mime' => 'application/pdf',
            ]);
        }catch(Exception $e)
        {
            Log::error("from admin paymentError: " . $e->getMessage());
        }
    }
}
