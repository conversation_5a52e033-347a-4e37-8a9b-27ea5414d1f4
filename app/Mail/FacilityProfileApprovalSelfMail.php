<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class FacilityProfileApprovalSelfMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $newData;
    public $step;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($user,$newData,$step)
    {
        $this->user = $user;
        $this->newData = $newData;
        $this->step = $step;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_PROFILE_APPROVAL_SELF']);
        // $formattedNewData = collect($this->newData)
        //     ->map(function ($value, $key) {
        //         if (is_array($value)) {
        //             $filtered = collect($value)->except('id');

        //             return strtoupper($key + 1) . ': ' . $filtered
        //                 ->map(fn($v, $k) => "$k: $v")
        //                 ->implode(', ');
        //         }

        //         return strtoupper($key) . ': ' . $value;
        //     })
        //     ->implode("\n");
        $tab = '';
        if($this->step == "1") {
            $tab = 'Basic Info';
        }
        elseif($this->step == "2") {
            $tab = 'Address Detail';
        }
        if($this->step == "3") {
            $tab = 'Person In-charge';
        }
        if($this->step == "4") {
            $tab = 'Documents';
        }

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->user->name,
                //'NEW_DATA' => $formattedNewData,
                'TAB' => $tab
            ];
          
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
       
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}
