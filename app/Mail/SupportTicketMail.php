<?php

namespace App\Mail;

use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class SupportTicketMail extends Mailable implements ShouldQueue
{
    protected string $mailKey;
    public $emailContent;
    protected array $mailTemplate;
    public function __construct(public SupportTicket $supportTicket, public string $panel = 'default', public User $user)
    {
        $this->user = $user;
        $this->mailKey = $supportTicket->status === 'closed' ? 'SUPPORTTICKET_CLOSE' : 'SUPPORTTICKET_CREATE';
        $this->mailTemplate = EmailTemplate::where('key', $this->mailKey)->first()->toArray();
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => $this->mailKey ?? 'SUPPORTTICKET_CREATE']);
        $statusText = $this->supportTicket->status === 'closed'
        ? '<span style="color: #DC3545; font-weight: 500;">Closed</span>'
        : '<span style="color: #28A745; font-weight: 500;">Open</span>';

        if($emailTemplate) {
            $replacements = [
                'USER_NAME' => $this->user->name ?? ' ',
                'TICKET_ID' => $this->supportTicket->id ?? ' ',
                'ORDER_ID' => $this->supportTicket->order->order_number ?? 'N/A',
                'NAME' => $this->supportTicket->name ?? ' ',
                'EMAIL' => $this->supportTicket->email ?? ' ',
                'CATEGORY' => $this->supportTicket->category->name ?? 'N/A',
                'SUBJECT' => $this->supportTicket->subject ?? ' ',
                'DESCRIPTION' => $this->supportTicket->description ?? ' ',
                'STATUS' => $statusText,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}
