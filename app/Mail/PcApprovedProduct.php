<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\DB;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Contracts\Queue\ShouldQueue;

class PcApprovedProduct extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $htmlBody;
    public $subject;
    public $product;
    public $user;
    public $pcName;
    /**
     * Create a new message instance.
     */
    public function __construct($product, $user, $pcName)
    {
        $this->product = $product;
        $this->user = $user;
        $this->pcName = $pcName;

        $template = DB::table('email_templates')->where('key', 'PC_APPROVED_PRODUCT')->first();

        $this->subject = $template->subject ?? 'Pc Approved Product';

        $this->htmlBody = $this->parseTemplate($template->email_content, [
            'user_name' => $this->user?->name,
            'product_name' => $this->product?->name,
            'pc_name' => $this->pcName ?? null,
            'category_name' => $this->product?->category?->name,
            'subcategory_name' => $this->product?->subcategory?->name,
        ]);
        
    }

    private function parseTemplate(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{{ ' . $key . ' }}', $value, $template);
        }
        return $template;
    }

    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->htmlBody,
            'logo' => asset('images/logo.png')
        ]);
    }
}
