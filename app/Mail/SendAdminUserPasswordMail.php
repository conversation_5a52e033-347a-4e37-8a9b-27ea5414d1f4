<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class SendAdminUserPasswordMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $password;
    public $userEmail;
    public $loginUrl;
    public $emailContent;
    public $user;
    /**
     * Create a new message instance.
     */
    public function __construct($password, $userEmail, $loginUrl, $user)
    {
        $this->password = $password;
        $this->userEmail = $userEmail;
        $this->loginUrl = $loginUrl;
        $this->user = $user;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate())->getTemplateToSend(['key' => 'ADMIN_USER_PASSWORD']);

        if($emailTemplate) {
            $replacements = [
                'LOGINURL' => $this->loginUrl,
                'PASSWORD' => $this->password,
                'EMAIL' => $this->userEmail,
                'USER_NAME' => $this->user->name,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}