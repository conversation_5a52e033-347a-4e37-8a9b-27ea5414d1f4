<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Illuminate\Validation\ValidationException;

class PcPayoutCommissionConfirmMail extends Mailable
{
    use Queueable, SerializesModels;

    public $payout;
    public $transactionResult;
    public $emailContent;
    public $payoutCycleDate;
    public $subject;

    public function __construct($payout, $transactionResult,$payoutCycleDate)
    {
        $this->payout = $payout;
        $this->transactionResult = $transactionResult;
        $this->payoutCycleDate = $payoutCycleDate;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_PAYOUT_COMMISSION_CONFIRMATION']);

        if($emailTemplate) {
            $replacements = [
                'CYCLEDATE' => $this->payoutCycleDate,
                'TYPE' => $this->payout->payout_type,
                'ADMINFEE' => $this->transactionResult && $this->transactionResult['amount'] ? $this->transactionResult['amount'] / 100 : 0,
                'NAME' => ucfirst(pcCompanyName($this->payout->pcDetail)),
               
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }


    public function build()
    {
        $finalSubject = str_replace('CYCLEDATE', $this->payoutCycleDate, $this->subject);

        return $this->subject($finalSubject)
        ->view('emails.common_mail')
        ->with([ 
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ]);
    }
}
