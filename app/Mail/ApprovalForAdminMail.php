<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class ApprovalForAdminMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($user)
    {
        $this->user = $user;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'VERSION_APPROVAL']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->user->pcDetails->business_name,
                'EMAIL' => $this->user->email,
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }
    public function build()
    {
        return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
    }
}
