<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\DB;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Contracts\Queue\ShouldQueue;

class PcRejectedProduct extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $htmlBody;
    public $subject;
    public $product;
    public $user;
    public $pcName;
    public $reason;

    /**
     * Create a new message instance.
     */
    public function __construct($product, $user, $pcName, $reason)
    {
        $this->product = $product;
        $this->user = $user;
        $this->pcName = $pcName;
        $this->reason = $reason;

        $template = DB::table('email_templates')->where('key', 'PC_REJECTED_PRODUCT')->first();

        $this->subject = $template->subject ?? 'Pc Rejected Product';

        $this->htmlBody = $this->parseTemplate($template->email_content, [
            'user_name' => $this->user?->name,
            'product_name' => $this->product?->name,
            'category_name' => $this->product?->category?->name,
            'subcategory_name' => $this->product?->subcategory?->name,
            'pc_name' => $this->pcName ?? null,
            'reason' => $this->reason ?? null,
        ]);
    }

    private function parseTemplate(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{{ ' . $key . ' }}', $value, $template);
        }
        return $template;
    }
    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->htmlBody,
            'logo' => asset('images/logo.png')
        ]);
    }
}
