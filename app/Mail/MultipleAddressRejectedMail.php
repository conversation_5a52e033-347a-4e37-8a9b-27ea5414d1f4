<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Illuminate\Validation\ValidationException;


class MultipleAddressRejectedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $nickName;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct(array $data)
    {
        $this->user = $data['user'];
        $this->nickName = $data['nick_name'] ?? null;
        $this->loadTemplate();
    }
    
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'MULTIPLE_ADDRESS_REJECT']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->user,
                'NICK' => $this->nickName ?? 'Not Provided',
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }
    public function build()
    {
        return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
    }
}
