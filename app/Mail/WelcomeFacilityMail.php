<?php

namespace App\Mail;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class WelcomeFacilityMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($user)
    {
        $this->user = $user;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_WELCOME']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->user->name,
                'URL' => config('app.clinic_url').'/login',
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            Log::error('FACILITY_WELCOME template not found');
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }
    public function build()
    {
        try{
            return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
        
                    Log::error("FACILITY_WELCOME mail sent");
        }catch(Exception $e)
        {
            Log::error("FACILITY_WELCOME issues. Error: " . $e->getMessage());
        }
    }
}
