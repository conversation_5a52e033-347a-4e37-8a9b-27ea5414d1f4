<?php

namespace App\Mail\PCActionMail;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class PaidCreditLineOrderMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subOrder;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($subOrder)
    {
        $this->subOrder = $subOrder;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        try {
            Log::error("From Pc order credit amount paid mail call :". $this->subOrder);
            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_CREDITLINE_ORDER_PAID']);
            if(!empty($emailTemplate)) {

                $replacements = [
                    'NAME' => $this->subOrder->order->user->name,
                    'ORDERNUMBER' => $this->subOrder->order->order_number,
                    'SUPPLIER' => pcCompanyName($this->subOrder->user->pcDetails),
                    'ORDERDATE' => $this->subOrder->created_at->format('d-m-Y H:i:s'),

                ];


                foreach ($replacements as $key => $value) {
                    $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
                }
                $this->emailContent = $emailTemplate->email_content;
                $this->subject  = $emailTemplate->subject;

            }else{
                return throw ValidationException::withMessages([
                    __('api.auth.mail_issue')
                ]);
            }
        }catch(Exception $e)
        {

            Log::error("  From Pc order rejected call issue in template: " . $e->getMessage());
        }
    }
    public function build()
    {
        try {
            Log::error("From Pc order rejected build :");
            return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
        }catch(Exception $e)
        {

            Log::error("  From Pc order rejected call issue. Error: " . $e->getMessage());
        }
    }
}
