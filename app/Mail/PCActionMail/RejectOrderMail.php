<?php

namespace App\Mail\PCActionMail;

use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class RejectOrderMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subOrder;
    public $name;
    public $emailContent;
    public $orderDetails;

    /**
     * Create a new message instance.
     */
    public function __construct($subOrder, $name, $orderDetails)
    {
        $this->name = $name;
        $this->subOrder = $subOrder;
        $this->orderDetails = $orderDetails;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        try {
            Log::error("From Pc order rejected mail call :" . $this->subOrder);
            if ($this->subOrder->user) {
                $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_REJECTED_ORDER']);
            } else {
                $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'ADMIN_REJECTED_ORDER']);
            }

            if ($emailTemplate) {
                $orderDetailsHtml = '<table cellpadding="8" cellspacing="0" border="1" style="border-collapse: collapse; width: 100%; font-family: Arial, sans-serif; font-size: 14px;">';
                foreach ($this->orderDetails as $key => $value) {
                    $orderDetailsHtml .= "
        <tr>
            <th align='left' style='background-color: #f4f4f4; padding: 8px; border: 1px solid #ddd; width: 30%;'>{$key}</th>
            <td style='padding: 8px; border: 1px solid #ddd;'>{$value}</td>
        </tr>";
                }
                $orderDetailsHtml .= '</table>';
                if ($this->subOrder->user && !empty($this->subOrder?->user->timezone)) {
                    $timezone = $this->subOrder->user->timezone;
                    $time = Carbon::parse($this->subOrder->created_at)->timezone($timezone);
                    $orderDate =  $time->format('d M, Y h:i A');
                } else {
                    $orderDate = $this->subOrder->created_at->format('d-m-Y H:i:s');
                }
                $replacements = [
                    'USERNAME' => $this->name,
                    'ORDERNUMBER' => "#" . $this->subOrder->order->order_number,
                    'REASON' => $this->subOrder->rejected_note,
                    'ORDERDETAIL' => $orderDetailsHtml,
                    'SUPPLIERNAME' => pcCompanyName($this->subOrder->user->pcDetails),
                    'ORDERDATE' => $orderDate ?? $this->subOrder->created_at->format('d-m-Y H:i:s'),
                    'CREATEDDATE' => $this->subOrder->created_at->format('d-m-Y H:i:s')
                ];
                foreach ($replacements as $key => $value) {
                    $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
                }
                $this->emailContent = $emailTemplate->email_content;
                $this->subject  = $emailTemplate->subject;
            } else {
                return throw ValidationException::withMessages([
                    __('api.auth.mail_issue')
                ]);
            }
        } catch (Exception $e) {

            Log::error("  From Pc order rejected call issue in template: " . $e->getMessage());
        }
    }
    public function build()
    {
        try {
            Log::error("From Pc order rejected build :");
            return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
        } catch (Exception $e) {

            Log::error("  From Pc order rejected call issue. Error: " . $e->getMessage());
        }
    }
}
