<?php

namespace App\Mail;

use App\Models\OrderProduct;
use App\Models\SubOrder;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Spatie\Browsershot\Browsershot;


class PaymentSuccessfulMail extends Mailable
{
    use Queueable, SerializesModels;

    public $order;
    public $transactionStatus;
    public $paymentType;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($order, $transactionStatus,$paymentType)
    {
        $this->order = $order;
        $this->transactionStatus = $transactionStatus;
        $this->paymentType = $paymentType;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        if($this->paymentType == 'credit_line'){
            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_CREDIT_LINE_ORDER_CONFIRMATION']);
        }else{
            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_PAY_NOW_ORDER_CONFIRMATION']);
        }
        Log::error("paymentType: " . $this->paymentType);
        if($emailTemplate) {
            $transactionInfo = '';
            if($this->paymentType == 'pay_now'){
                if($this->transactionStatus == 'success'){

                    $transactionInfo = 'Your order is now being processed, and you will receive a notification once it has been dispatched';
                }else{
                    $transactionInfo = 'Your order payment fail , Please contact the administrator';

                }
            }
            $replacements = [ 
                'NAME' => $this->order->user->name,
                'ORDERNUMBER' => $this->order->order_number,
                'AMOUNT' => number_format($this->order->amount, 2),
                'CREATEDDATE' => convertToUserMailTimezone($this->order->user->timezone,$this->order->created_at, 'd M Y  H:i'),
                'PAYMENTSTATUS' =>  $this->paymentType == 'credit_line' ? 'Pending' : 'Paid',
                'TRANSACTIONSTATUS' =>  $this->paymentType == 'credit_line' ? 'Your order is now being processed, and you will receive a notification once it has been dispatched' : $transactionInfo,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        try {

            $fileName = 'invoice_' . $this->order->id . '.pdf';
            $s3Path = config('constants.api.order_invoices.main') . $fileName;
            Log::error("main Order s3Path: " . $s3Path);
            

            $html = view('pdf.invoice', [
                'order' => $this->order, 
                'shippingAmt' => $this->order->subOrder->sum('total_shipping_amount'),
                'supTotalAmt' => $this->order->subOrder->sum('total_sub_order_value'),
                'supPointApplied' => $this->order->subOrder->sum('total_dpharma_points_used'),
                'adminSettingDetail' => adminSettingDetail()
            ])->render();

            $pdfBinary = Browsershot::html($html)
                        ->setChromePath('/usr/bin/google-chrome')
                        ->format('A4')
                        ->noSandbox()
                        ->pdf();

        $putStatus = Storage::disk(config(FILESYSTEM_DEFAULT))->put($s3Path, $pdfBinary);

        Log::error("main Order putStatus: " . $putStatus);

        $this->order->update(['pdf_path' => $fileName]);
        
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ])->attachData($pdfBinary, 'orderInvoice.pdf', [
            'mime' => 'application/pdf',
        ]);
        }catch(Exception $e)
        {
            Log::error("payment main order issue. Error: " . $e->getMessage());
        }
    }
}
