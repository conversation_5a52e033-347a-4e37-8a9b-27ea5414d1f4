<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class OrderCancelPcMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subOrder;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($subOrder)
    {
        $this->subOrder = $subOrder;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'ORDER_CANCEL_PC_MAIL']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => pcCompanyName($this->subOrder->pcDetail),
                'ORDERID' => $this->subOrder->order->order_number,
                'CREATEDDATE' => convertToUserMailTimezone($this->subOrder->user->timezone,$this->subOrder->created_at,'d M Y  H:i'),
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ]);

        //Order Cancellation  #'.$this->subOrder->order_id.'  – Digital Pharma
    }
}
