<?php

namespace App\Mail;

use App\Models\OrderProduct;
use App\Models\SubOrder;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Spatie\Browsershot\Browsershot;


class RetryPaymentMail extends Mailable
{
    use Queueable, SerializesModels;

    public $order;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($order)
    {
        $this->order = $order;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_ORDER_RETRY_PAYMENT']);
        
      
        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->order->user->name,
                'ORDERNUMBER' => $this->order->order_number,
                'AMOUNT' => $this->order->subOrder->where('payment_type','!=','credit_line')->sum('total_amount'),
                'CREATEDDATE' => convertToUserMailTimezone($this->order->user->timezone,$this->order->created_at,'d M Y H:i'),
                'PAYMENTSTATUS' =>  'Pending',
                 ];

            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        try {
            return $this->subject($this->subject)
            ->view('emails.common_mail')
            ->with([
                'html' => $this->emailContent,
                'logo' => asset('images/logo.png')
            ]);
        } catch(Exception $e)  {
            Log::error("facility order retry payment mail issue . Error: " . $e->getMessage());
        }
    }
}
