<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class SendPCPasswordMail extends Mailable
{
    use Queueable, SerializesModels;


    public $password;
    public $userEmail;
    public $loginUrl;

    /**
     * Create a new message instance.
     */
    public function __construct($password, $userEmail, $loginUrl)
    {
        $this->password = $password;
        $this->userEmail = $userEmail;
        $this->loginUrl = $loginUrl;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Login Credentials',
        );
    }

    /**
     * Get the message content definition.
     */
    public function build()
    {
        return $this->subject('Your Account Credentials')
                    ->view('emails.send_password')
                    ->with([
                        'password' => $this->password,
                        'email' => $this->userEmail,
                        'loginUrl' => $this->loginUrl
                    ]);
    }
   
}
