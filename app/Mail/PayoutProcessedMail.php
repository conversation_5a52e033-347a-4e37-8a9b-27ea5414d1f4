<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Illuminate\Validation\ValidationException;

class PayoutProcessedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $payout;
    public $pdfPath;
    public $payoutCycleDate;
    public $totalPayableAmt;
    public $emailContent;
    public $subject;

    public function __construct($payout, $pdfPath,$payoutCycleDate,$totalPayableAmt)
    {
        $this->payout = $payout;
        $this->pdfPath = $pdfPath;
        $this->payoutCycleDate = $payoutCycleDate;
        $this->totalPayableAmt = $totalPayableAmt;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PS_PAYOUT_SEND']);

        $type = $this->payout->payout_type; 

        if($emailTemplate) { 
            $replacements = [
                'NAME' => ucfirst(pcCompanyName($this->payout->pcDetail)), 
                'PAYOUTDATE' => Carbon::parse($this->payout->payout_on)->format('F j, Y'),
                'CYCLEDATE' => $this->payoutCycleDate,
                'PAYABLEAMOUNT' => $this->totalPayableAmt,
                'PAYOUTTYPETEXT' => $type == 'full' ? 
                        'This payout includes the full amount for the specified cycle period.':
                            'The payout for the specified cycle period has been processed and deducted the admin commission. The remaining amount has been successfully transferred to you.' ,
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }


    public function build()
    {
        Log::info("PayoutProcessedMail call"); 

        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ])
        ->attachFromStorageDisk('s3', $this->pdfPath, 'payout.pdf', [
            'mime' => 'application/pdf',
        ]);
    }
}
