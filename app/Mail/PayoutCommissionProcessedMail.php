<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Indianic\EmailTemplate\Models\EmailTemplate;
use Illuminate\Validation\ValidationException;



class PayoutCommissionProcessedMail extends Mailable
{
    use Queueable, SerializesModels;

    public $payout;
    public $pdfPath;
    public $emailContent;
    public $payoutCycleDate;
    public $totalAdminFee;
    public $subject;
    public $merchantPaymentLink;

    public function __construct($payout, $pdfPath,$payoutCycleDate,$totalAdminFee,$merchantPaymentLink)
    {
        $this->payout = $payout;
        $this->pdfPath = $pdfPath;
        $this->payoutCycleDate = $payoutCycleDate;
        $this->totalAdminFee = $totalAdminFee;
        $this->merchantPaymentLink = $merchantPaymentLink;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PS_PAYOUT_COMMISSION']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => ucfirst(pcCompanyName($this->payout->pcDetail)),
                'CYCLEDATE' => $this->payoutCycleDate,
                'TYPE' => $this->payout->payout_type,
                'ADMINFEE' => $this->totalAdminFee,
                'MERCHANTPAYMENTLINK' => $this->merchantPaymentLink ? 
                                        "<p>This is your admin fee payment link. You can click the link to pay the admin fee: 
                                        <a href=\"{$this->merchantPaymentLink}\">Pay Admin Fee</a></p>
                                        <p>This link is valid for 24 hours.</p>" 
                                        : null,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }


    public function build()
    {
        Log::info("PayoutCommissionProcessedMail call"); 

        $finalSubject = str_replace('CYCLEDATE', $this->payoutCycleDate, $this->subject);

        return $this->subject($finalSubject)
        ->view('emails.common_mail')
        ->with([ 
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ])->attachFromStorageDisk('s3', $this->pdfPath, 'commission_payout.pdf', [
            'mime' => 'application/pdf',
        ]);
    }
}
