<?php

namespace App\Mail;

use App\Models\PcDetail;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class SendPCUserPasswordMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $password;
    public $userEmail;
    public $loginUrl;
    public $emailContent;
    public $subject;
    public $user;
    public $pcDetails; 
    
    /**
     * Create a new message instance.
     */
    public function __construct($password, $userEmail, $loginUrl, $user)
    {
        $this->password = $password;
        $this->userEmail = $userEmail;
        $this->loginUrl = $loginUrl;
        $this->user = $user;
        $this->pcDetails = PcDetail::where('user_id', $user->parent_id)->first();
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_USER_PASSWORD']);

        if($emailTemplate) {
            $replacements = [
                'LOGINURL' => $this->loginUrl,
                'PASSWORD' => $this->password,
                'EMAIL' => $this->userEmail,
                'USER_NAME' => $this->user->name,
                'PARENT_NAME' => $this->pcDetails
                    ? (!empty($this->pcDetails->company_name)
                        ? $this->pcDetails->company_name
                        : $this->pcDetails->business_name)
                    : 'Pharmaceutical Company',
                'PARENT_ID' => $this->user->parent_id,
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
                $emailTemplate->subject = str_replace($key, $value, $emailTemplate->subject); 
            }
            
            $this->emailContent = $emailTemplate->email_content;
            $this->subject = $emailTemplate->subject;
    
        } else {
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}