<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class SendOtpMail extends Mailable
{
    use Queueable, SerializesModels;

    public $otp;
    public $time;
    public $type;
    public $emailContent;
    public $subject;

    public function __construct($otp,$time,$type)
    {
        $this->otp = $otp;
        $this->time = $time;
        $this->type = $type;
        $this->loadTemplate();
    }

    public function loadTemplate()
    {
        $emailTemplate = null;
        if($this->type == 'register') {

            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_REGISTER_OTP_SEND']);
            
        }
        elseif($this->type == 'login') {

            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_LOGIN_OTP_SEND']);
        }
        elseif($this->type == 're_sent') {

            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_OTP_RE_SEND']);
        }
        elseif($this->type == 'forgot_password') {

            $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_FORGOT_PASSWORD_OTP_SEND']);
        }

        if($emailTemplate){
            $replacements = [
                'OTP' => $this->otp,
                'TIME' => $this->time,
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
    }
}
