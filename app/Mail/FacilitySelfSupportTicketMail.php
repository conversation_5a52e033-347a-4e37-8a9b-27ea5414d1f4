<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class FacilitySelfSupportTicketMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
        $this->loadTemplate();
    }
    
    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_SUPPORT_TICKET_GENERATE']);

        if($emailTemplate) {
            $replacements = [
                'NAME' => $this->data->name,
                'SUBJECT' => $this->data->subject,
                'ORDERNUMBER' => $this->data->order->order_number,
                'RAISEDTO' => $this->data->receiver_id == 1 ? 'DPharma' :  pcCompanyName($this->data->pcDetail)
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}
