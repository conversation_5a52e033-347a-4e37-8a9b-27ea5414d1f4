<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class AdminInquiryMail extends Mailable
{
    use Queueable, SerializesModels;

    public $data;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($data)
    {
        $this->data = $data;
        $this->loadTemplate();
    }
    
    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'ADMIN_INQUIRY']);

        if($emailTemplate) {
            $replacements = [
                'FACILITYNAME' => $this->data->name,
                'NAME' => $this->data->name,
                'SUBJECT' => $this->data->subject,
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ]);
    }
}
