<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class SendClinicPasswordMail extends Mailable
{
    use Queueable, SerializesModels;

    public $password;
    public $userEmail;
    public $loginUrl;
    public $emailContent;
    

    /**
     * Create a new message instance.
     */
    public function __construct($password, $userEmail, $loginUrl)
    {
        $this->password = $password;
        $this->userEmail = $userEmail;
        $this->loginUrl = $loginUrl;
        $this->loadTemplate();
    }

    /**
     * Load email template and replace placeholders.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'CLINIC_PASSWORD']);

        if ($emailTemplate) {
            $replacements = [
                'PASSWORD' => $this->password,
                'EMAIL' => $this->userEmail,
                'LOGINURL' => $this->loginUrl
            ];

            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject = $emailTemplate->subject;
        } else {
            throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

   

    /**
     * Get the message content definition.
     */
    public function build()
    {
        return $this->subject($this->subject)
                    ->view('emails.common_mail')
                    ->with([
                        'html' => $this->emailContent,
                        'logo' => asset('images/logo.png')
                    ]);
    }
}
