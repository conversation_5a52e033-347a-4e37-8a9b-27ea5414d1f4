<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class ReferFriendMail extends Mailable
{
    use Queueable, SerializesModels;

    public $clinicData;
    public $emailContent;

    public function __construct($clinicData)
    {
        $this->clinicData = $clinicData;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'FACILITY_REFERRAL_CODE']);

        if($emailTemplate) {
            $replacements = [
                'REFERRALCODE' => $this->clinicData->referral_code,
                'FACILITYNAME' => $this->clinicData->user->name
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
    
        }else{
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        return $this->subject($this->subject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->emailContent,
            'logo' => asset('images/logo.png')
        ]);
    }
}
