<?php

namespace App\Mail;

use App\Models\OrderProduct;
use App\Models\SubOrder;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\File;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class PcOrderMail extends Mailable
{
    use Queueable, SerializesModels;

    public $subOrder;
    public $pdfPath;
    public $emailContent;

    /**
     * Create a new message instance.
     */
    public function __construct($subOrder,$pdfPath)
    {
        $this->subOrder = $subOrder;
        $this->pdfPath = $pdfPath;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {
        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_NEW_ORDER_MAIL']);

        if($emailTemplate) { 
            $replacements = [
                'NAME' =>  pcCompanyName($this->subOrder->pcDetail),
                'ORDERNUMBER' => $this->subOrder->order->order_number,
                'CREATEDDATE' => convertToUserMailTimezone($this->subOrder->user->timezone,$this->subOrder->created_at, 'd M Y  H:i:s'),
                'URL' => route('filament.pc.resources.orders.view', ['record' => $this->subOrder->id, 'type' => 'allorder']),
                
            ];
    
            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;

            Log::info("pc order email data added dynamic:");
    
        }else{
            Log::error("pc order email not found:");
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }

    public function build()
    {
        
        try {
            return $this->subject($this->subject)
                ->view('emails.common_mail')
                ->with([
                    'html' => $this->emailContent,
                    'logo' => asset('images/logo.png')
                ])->attachData($this->pdfPath, "purchase-order-{$this->subOrder->id}.pdf", [
                'mime' => 'application/pdf',
            ]);
    
        }catch(Exception $e)
        {
            Log::error("pc order issue. Error: " . $e->getMessage());
        }
    }
}
