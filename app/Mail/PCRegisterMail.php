<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Validation\ValidationException;
use Indianic\EmailTemplate\Models\EmailTemplate;

class PCRegisterMail extends Mailable
{
    use Queueable, SerializesModels;

    public $user;
    public $emailContent;
    public $link;

    /**
     * Create a new message instance.
     */
    public function __construct($user, $link)
    {
        $this->user = $user;
        $this->link = $link;
        $this->loadTemplate();
    }

    /**
     * Build the message.
     */
    public function loadTemplate()
    {

        $emailTemplate = (new EmailTemplate)->getTemplateToSend(['key' => 'PC_REGISTRATION_MAIL']);

        if ($emailTemplate) {
            $replacements = [
                'NAME' => $this->user['name'] ?? null,
                'EMAIL' => $this->user['email'] ?? null,
                'LINK' => $this->link ?? null
            ];

            foreach ($replacements as $key => $value) {
                $emailTemplate->email_content = str_replace($key, $value, $emailTemplate->email_content);
            }
            $this->emailContent = $emailTemplate->email_content;
            $this->subject  = $emailTemplate->subject;
        } else {
            return throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
    }
    public function build()
    {
        return $this->subject($this->subject)
            ->view('emails.common_mail')
            ->with([
                'html' => $this->emailContent,
                'logo' => asset('images/logo.png')
            ]);
    }
}
