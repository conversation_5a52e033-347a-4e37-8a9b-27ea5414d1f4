<?php

namespace App\Observers;

use App\Models\Product;
use App\Services\ProductTabsCacheService;

class ProductObserver
{
    /**
     * Handle the Product "created" event.
     */
    public function created(Product $product): void
    {
        ProductTabsCacheService::clearTabCounts();
    }

    /**
     * Handle the Product "updated" event.
     */
    public function updated(Product $product): void
    {
        // Only clear cache if status changed
        if ($product->isDirty('status')) {
            ProductTabsCacheService::clearTabCounts();
        }
    }

    /**
     * Handle the Product "deleted" event.
     */
    public function deleted(Product $product): void
    {
        ProductTabsCacheService::clearTabCounts();
    }

    /**
     * Handle the Product "restored" event.
     */
    public function restored(Product $product): void
    {
        ProductTabsCacheService::clearTabCounts();
    }
}
