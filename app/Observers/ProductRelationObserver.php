<?php

namespace App\Observers;

use App\Models\ProductRelation;
use App\Services\ProductRelationCacheService;

class ProductRelationObserver
{
    /**
     * Handle the ProductRelation "created" event.
     */
    public function created(ProductRelation $productRelation): void
    {
        $this->clearCaches($productRelation);
    }

    /**
     * Handle the ProductRelation "updated" event.
     */
    public function updated(ProductRelation $productRelation): void
    {
        $this->clearCaches($productRelation);
    }

    /**
     * Handle the ProductRelation "deleted" event.
     */
    public function deleted(ProductRelation $productRelation): void
    {
        $this->clearCaches($productRelation);
    }

    /**
     * Handle the ProductRelation "restored" event.
     */
    public function restored(ProductRelation $productRelation): void
    {
        $this->clearCaches($productRelation);
    }

    /**
     * Handle the ProductRelation "force deleted" event.
     */
    public function forceDeleted(ProductRelation $productRelation): void
    {
        $this->clearCaches($productRelation);
    }

    /**
     * Clear related caches
     */
    private function clearCaches(ProductRelation $productRelation): void
    {
        // Clear specific product relation cache
        ProductRelationCacheService::clearProductRelationCache($productRelation->product_id, $productRelation->user_id);
        
        // Clear batch-wise products cache
        \Illuminate\Support\Facades\Cache::forget("batch_wise_products_user_{$productRelation->user_id}");
        
        // Clear search caches (pattern-based)
        $searchPattern = "product_search_*_{$productRelation->user_id}";
        $keys = \Illuminate\Support\Facades\Cache::getRedis()->keys($searchPattern);
        if (!empty($keys)) {
            \Illuminate\Support\Facades\Cache::getRedis()->del($keys);
        }
    }
} 