<?php

namespace App\Observers;

use App\Models\ProductRelationStock;
use App\Services\ProductRelationCacheService;

class ProductRelationStockObserver
{
    /**
     * Handle the ProductRelationStock "created" event.
     */
    public function created(ProductRelationStock $productRelationStock): void
    {
        $this->clearCaches($productRelationStock);
    }

    /**
     * Handle the ProductRelationStock "updated" event.
     */
    public function updated(ProductRelationStock $productRelationStock): void
    {
        $this->clearCaches($productRelationStock);
    }

    /**
     * Handle the ProductRelationStock "deleted" event.
     */
    public function deleted(ProductRelationStock $productRelationStock): void
    {
        $this->clearCaches($productRelationStock);
    }

    /**
     * Handle the ProductRelationStock "restored" event.
     */
    public function restored(ProductRelationStock $productRelationStock): void
    {
        $this->clearCaches($productRelationStock);
    }

    /**
     * Handle the ProductRelationStock "force deleted" event.
     */
    public function forceDeleted(ProductRelationStock $productRelationStock): void
    {
        $this->clearCaches($productRelationStock);
    }

    /**
     * Clear related caches
     */
    private function clearCaches(ProductRelationStock $productRelationStock): void
    {
        // Clear stock-specific cache
        ProductRelationCacheService::clearProductRelationStockCache($productRelationStock->product_relation_id);
    }
} 