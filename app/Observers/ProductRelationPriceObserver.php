<?php

namespace App\Observers;

use App\Models\ProductRelationPrice;
use App\Services\ProductRelationCacheService;

class ProductRelationPriceObserver
{
    /**
     * Handle the ProductRelationPrice "created" event.
     */
    public function created(ProductRelationPrice $productRelationPrice): void
    {
        $this->clearCaches($productRelationPrice);
    }

    /**
     * Handle the ProductRelationPrice "updated" event.
     */
    public function updated(ProductRelationPrice $productRelationPrice): void
    {
        $this->clearCaches($productRelationPrice);
    }

    /**
     * Handle the ProductRelationPrice "deleted" event.
     */
    public function deleted(ProductRelationPrice $productRelationPrice): void
    {
        $this->clearCaches($productRelationPrice);
    }

    /**
     * Handle the ProductRelationPrice "restored" event.
     */
    public function restored(ProductRelationPrice $productRelationPrice): void
    {
        $this->clearCaches($productRelationPrice);
    }

    /**
     * Handle the ProductRelationPrice "force deleted" event.
     */
    public function forceDeleted(ProductRelationPrice $productRelationPrice): void
    {
        $this->clearCaches($productRelationPrice);
    }

    /**
     * Clear related caches
     */
    private function clearCaches(ProductRelationPrice $productRelationPrice): void
    {
        // Clear price-specific cache
        ProductRelationCacheService::clearProductRelationPriceCache($productRelationPrice->product_relation_id);
    }
} 