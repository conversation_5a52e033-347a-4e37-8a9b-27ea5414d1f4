<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Exception;

class CreateConcurrentIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:create-concurrent-indexes {--dry-run : Show what would be executed without running}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create concurrent indexes for optimized query performance (zero downtime)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No indexes will be created');
        } else {
            $this->info('🚀 Creating concurrent indexes for improved performance...');
        }
        
        $this->newLine();

        // Check if we're using PostgreSQL
        if (DB::getDriverName() !== 'pgsql') {
            $this->error('❌ This command is only supported for PostgreSQL databases.');
            return 1;
        }

        $indexes = $this->getIndexDefinitions();
        $created = 0;
        $failed = 0;

        foreach ($indexes as $name => $sql) {
            $this->info("📊 Creating index: {$name}");
            
            if ($isDryRun) {
                $this->line("   SQL: {$sql}");
                $created++;
                continue;
            }

            try {
                // Check if index already exists
                $exists = $this->indexExists($name);
                
                if ($exists) {
                    $this->warn("   ⚠️  Index already exists, skipping...");
                    continue;
                }

                // Create the index
                DB::statement($sql);
                $this->info("   ✅ Created successfully");
                $created++;
                
            } catch (Exception $e) {
                $this->error("   ❌ Failed: " . $e->getMessage());
                $failed++;
            }
        }

        $this->newLine();
        
        if ($isDryRun) {
            $this->info("🔍 DRY RUN COMPLETE:");
            $this->line("   • {$created} indexes would be created");
        } else {
            $this->info("✅ CONCURRENT INDEX CREATION COMPLETE:");
            $this->line("   • {$created} indexes created successfully");
            if ($failed > 0) {
                $this->line("   • {$failed} indexes failed");
            }
            
            $this->newLine();
            $this->info("📈 Run 'php artisan analyze:cached-queries' to verify performance improvements.");
        }

        return $failed > 0 ? 1 : 0;
    }

    /**
     * Get all index definitions
     */
    private function getIndexDefinitions(): array
    {
        return [
            'idx_dosage_foams_active_records_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dosage_foams_active_records_concurrent 
                 ON dosage_foams (id, name) 
                 WHERE status = true AND deleted_at IS NULL",

            'idx_units_active_records_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_units_active_records_concurrent 
                 ON units (id, name) 
                 WHERE status = true AND deleted_at IS NULL",

            'idx_dosage_foams_status_deleted_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_dosage_foams_status_deleted_concurrent 
                 ON dosage_foams (status, deleted_at)",

            'idx_units_status_deleted_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_units_status_deleted_concurrent 
                 ON units (status, deleted_at)",

            'idx_products_dosage_foams_id_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_dosage_foams_id_concurrent 
                 ON products (dosage_foams_id) 
                 WHERE dosage_foams_id IS NOT NULL",

            'idx_products_unit_id_concurrent' => 
                "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_unit_id_concurrent 
                 ON products (unit_id) 
                 WHERE unit_id IS NOT NULL",
        ];
    }

    /**
     * Check if an index exists
     */
    private function indexExists(string $indexName): bool
    {
        $result = DB::select("
            SELECT indexname 
            FROM pg_indexes 
            WHERE indexname = ?
        ", [$indexName]);

        return count($result) > 0;
    }
} 