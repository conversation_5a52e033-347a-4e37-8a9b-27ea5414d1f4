<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductVariant;
use App\Models\Product;
use App\Models\ProductRelationPrice;
use App\Models\ProductRelationStock;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupOrphanedVariants extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'products:cleanup-orphaned-variants 
                           {--dry-run : Show what would be deleted without actually deleting}
                           {--force : Force deletion without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up orphaned product variants without parent products and their related data';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        $this->info('Starting orphaned variants cleanup...');

        try {
            // Find orphaned variants
            $orphanedVariants = ProductVariant::whereDoesntHave('product')->get();
            
            if ($orphanedVariants->isEmpty()) {
                $this->info('✅ No orphaned variants found. Database is clean!');
                return Command::SUCCESS;
            }

            $this->warn("Found {$orphanedVariants->count()} orphaned variants:");
            
            // Show details of orphaned variants
            foreach ($orphanedVariants as $variant) {
                $this->line("  - Variant ID: {$variant->id}, SKU: {$variant->sku}, Product ID: {$variant->product_id}");
            }

            if ($isDryRun) {
                $this->info("\n🔍 DRY RUN - No data will be deleted");
                $this->showCleanupPlan($orphanedVariants);
                return Command::SUCCESS;
            }

            // Confirm deletion unless forced
            if (!$isForced && !$this->confirm('Do you want to proceed with cleanup?')) {
                $this->info('Cleanup cancelled.');
                return Command::SUCCESS;
            }

            // Perform cleanup
            $results = $this->performCleanup($orphanedVariants);
            
            $this->info("\n✅ Cleanup completed successfully!");
            $this->displayResults($results);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("❌ Cleanup failed: {$e->getMessage()}");
            Log::error('Orphaned variants cleanup failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }

    /**
     * Show what would be cleaned up in dry run mode
     */
    private function showCleanupPlan($orphanedVariants): void
    {
        $this->info("\nCleanup plan:");
        
        foreach ($orphanedVariants as $variant) {
            $this->line("\nVariant ID: {$variant->id}");
            
            // Count related data
            $attributeCount = DB::table('product_variant_attributes')
                ->where('product_variant_id', $variant->id)
                ->count();
            
            $priceCount = ProductRelationPrice::where('product_variant_id', $variant->id)->count();
            $stockCount = ProductRelationStock::where('product_variant_id', $variant->id)->count();
            $mediaCount = $variant->getMedia('variant-images')->count();
            
            $this->line("  - Variant attributes: {$attributeCount}");
            $this->line("  - Price records: {$priceCount}");
            $this->line("  - Stock records: {$stockCount}");
            $this->line("  - Media files: {$mediaCount}");
        }
    }

    /**
     * Perform the actual cleanup
     */
    private function performCleanup($orphanedVariants): array
    {
        $results = [
            'variants_deleted' => 0,
            'attributes_deleted' => 0,
            'prices_deleted' => 0,
            'stocks_deleted' => 0,
            'media_deleted' => 0,
        ];

        return DB::transaction(function () use ($orphanedVariants, $results) {
            foreach ($orphanedVariants as $variant) {
                $this->line("Cleaning variant ID: {$variant->id}");
                
                // Delete variant attributes
                $attributesDeleted = DB::table('product_variant_attributes')
                    ->where('product_variant_id', $variant->id)
                    ->delete();
                $results['attributes_deleted'] += $attributesDeleted;
                
                // Delete pricing records
                $pricesDeleted = ProductRelationPrice::where('product_variant_id', $variant->id)->delete();
                $results['prices_deleted'] += $pricesDeleted;
                
                // Delete stock records
                $stocksDeleted = ProductRelationStock::where('product_variant_id', $variant->id)->delete();
                $results['stocks_deleted'] += $stocksDeleted;
                
                // Delete media files
                try {
                    $mediaItems = $variant->getMedia('variant-images');
                    $mediaCount = $mediaItems->count();
                    $variant->clearMediaCollection('variant-images');
                    $results['media_deleted'] += $mediaCount;
                } catch (\Exception $e) {
                    $this->warn("  ⚠️  Could not delete media for variant {$variant->id}: {$e->getMessage()}");
                }
                
                // Delete the variant itself
                $variant->delete();
                $results['variants_deleted']++;
                
                $this->line("  ✅ Cleaned variant {$variant->id}");
            }

            return $results;
        });
    }

    /**
     * Display cleanup results
     */
    private function displayResults(array $results): void
    {
        $this->table(
            ['Item', 'Count Deleted'],
            [
                ['Variants', $results['variants_deleted']],
                ['Variant Attributes', $results['attributes_deleted']],
                ['Price Records', $results['prices_deleted']],
                ['Stock Records', $results['stocks_deleted']],
                ['Media Files', $results['media_deleted']],
            ]
        );

        // Log the cleanup activity
        Log::info('Orphaned variants cleanup completed', $results);
    }

    /**
     * Additional cleanup methods for other orphaned data
     */
    public function cleanupOrphanedAttributes(): int
    {
        $this->info('Checking for orphaned variant attributes...');
        
        $orphanedAttributes = DB::table('product_variant_attributes as pva')
            ->leftJoin('product_variants as pv', 'pva.product_variant_id', '=', 'pv.id')
            ->whereNull('pv.id')
            ->count();

        if ($orphanedAttributes > 0) {
            $this->warn("Found {$orphanedAttributes} orphaned variant attributes");
            
            if ($this->confirm('Delete orphaned variant attributes?')) {
                $deleted = DB::table('product_variant_attributes as pva')
                    ->leftJoin('product_variants as pv', 'pva.product_variant_id', '=', 'pv.id')
                    ->whereNull('pv.id')
                    ->delete();
                
                $this->info("Deleted {$deleted} orphaned variant attributes");
            }
        } else {
            $this->info('No orphaned variant attributes found');
        }

        return Command::SUCCESS;
    }

    /**
     * Cleanup orphaned pricing records
     */
    public function cleanupOrphanedPrices(): int
    {
        $this->info('Checking for orphaned price records...');
        
        $orphanedPrices = ProductRelationPrice::whereDoesntHave('productVariant')->count();

        if ($orphanedPrices > 0) {
            $this->warn("Found {$orphanedPrices} orphaned price records");
            
            if ($this->confirm('Delete orphaned price records?')) {
                $deleted = ProductRelationPrice::whereDoesntHave('productVariant')->delete();
                $this->info("Deleted {$deleted} orphaned price records");
            }
        } else {
            $this->info('No orphaned price records found');
        }

        return Command::SUCCESS;
    }

    /**
     * Cleanup orphaned stock records  
     */
    public function cleanupOrphanedStocks(): int
    {
        $this->info('Checking for orphaned stock records...');
        
        $orphanedStocks = ProductRelationStock::whereDoesntHave('productVariant')->count();

        if ($orphanedStocks > 0) {
            $this->warn("Found {$orphanedStocks} orphaned stock records");
            
            if ($this->confirm('Delete orphaned stock records?')) {
                $deleted = ProductRelationStock::whereDoesntHave('productVariant')->delete();
                $this->info("Deleted {$deleted} orphaned stock records");
            }
        } else {
            $this->info('No orphaned stock records found');
        }

        return Command::SUCCESS;
    }
} 