<?php

namespace App\Console\Commands;

use App\Models\Unit;
use App\Models\DosageForm;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class AnalyzeCachedQueries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'analyze:cached-queries {--explain : Show query execution plans}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Analyze cached queries performance and suggest optimizations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('📊 Analyzing Cached Queries Performance');
        $this->newLine();

        // Analyze table statistics
        $this->analyzeTableStatistics();
        
        // Analyze index usage
        $this->analyzeIndexUsage();
        
        // Test query performance
        $this->testQueryPerformance();
        
        // Cache analysis
        $this->analyzeCacheEffectiveness();
        
        // Show execution plans if requested
        if ($this->option('explain')) {
            $this->showQueryPlans();
        }
        
        $this->newLine();
        $this->info('✅ Analysis complete!');
    }

    /**
     * Analyze table statistics
     */
    private function analyzeTableStatistics()
    {
        $this->info('📈 Table Statistics:');
        $this->newLine();

        $tables = ['dosage_foams', 'units', 'products', 'products_relation'];
        
        foreach ($tables as $table) {
            $stats = DB::select("
                SELECT 
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes,
                    n_live_tup as live_rows,
                    n_dead_tup as dead_rows,
                    last_vacuum,
                    last_autovacuum,
                    last_analyze,
                    last_autoanalyze
                FROM pg_stat_user_tables 
                WHERE tablename = ?
            ", [$table]);

            if (!empty($stats)) {
                $stat = $stats[0];
                $this->table(
                    ['Metric', 'Value'],
                    [
                        ['Table', $stat->tablename],
                        ['Live Rows', number_format($stat->live_rows)],
                        ['Dead Rows', number_format($stat->dead_rows)],
                        ['Inserts', number_format($stat->inserts)],
                        ['Updates', number_format($stat->updates)],
                        ['Deletes', number_format($stat->deletes)],
                        ['Last Analyze', $stat->last_autoanalyze ?: 'Never'],
                    ]
                );
                $this->newLine();
            }
        }
    }

    /**
     * Analyze index usage
     */
    private function analyzeIndexUsage()
    {
        $this->info('📊 Index Usage Analysis:');
        $this->newLine();

        $indexes = DB::select("
            SELECT 
                schemaname,
                tablename,
                indexname,
                idx_scan as scans,
                idx_tup_read as tuples_read,
                idx_tup_fetch as tuples_fetched
            FROM pg_stat_user_indexes 
            WHERE tablename IN ('dosage_foams', 'units', 'products', 'products_relation')
            ORDER BY tablename, idx_scan DESC
        ");

        if (!empty($indexes)) {
            $this->table(
                ['Table', 'Index', 'Scans', 'Tuples Read', 'Tuples Fetched'],
                collect($indexes)->map(function ($index) {
                    return [
                        $index->tablename,
                        $index->indexname,
                        number_format($index->scans),
                        number_format($index->tuples_read),
                        number_format($index->tuples_fetched),
                    ];
                })->toArray()
            );
        }
        $this->newLine();
    }

    /**
     * Test query performance
     */
    private function testQueryPerformance()
    {
        $this->info('⚡ Query Performance Testing:');
        $this->newLine();

        $queries = [
            'DosageForm::all()' => fn() => DosageForm::all(),
            'Unit::all()' => fn() => Unit::all(),
            'DosageForm::where(status, true)' => fn() => DosageForm::where('status', true)->get(),
            'Unit::where(status, true)' => fn() => Unit::where('status', true)->get(),
            'DosageForm::find(1)' => fn() => DosageForm::find(1),
            'Unit::find(1)' => fn() => Unit::find(1),
        ];

        $results = [];
        
        foreach ($queries as $name => $query) {
            $start = microtime(true);
            $result = $query();
            $end = microtime(true);
            
            $results[] = [
                'Query' => $name,
                'Time (ms)' => number_format(($end - $start) * 1000, 2),
                'Records' => is_countable($result) ? count($result) : ($result ? 1 : 0),
            ];
        }

        $this->table(
            ['Query', 'Time (ms)', 'Records'],
            $results
        );
        $this->newLine();
    }

    /**
     * Analyze cache effectiveness
     */
    private function analyzeCacheEffectiveness()
    {
        $this->info('🚀 Cache Effectiveness Analysis:');
        $this->newLine();

        // Test cache hit ratios
        $cacheKeys = [
            'dosage_form_options',
            'unit_options',
            'dosage_form_1',
            'dosage_form_2',
            'dosage_form_3',
        ];

        $cacheData = [];
        foreach ($cacheKeys as $key) {
            $value = Cache::get($key);
            $cacheData[] = [
                'Key' => $key,
                'Status' => $value ? '✅ Hit' : '❌ Miss',
                'Size' => $value ? $this->formatBytes(strlen(serialize($value))) : 'N/A',
            ];
        }

        $this->table(
            ['Cache Key', 'Status', 'Size'],
            $cacheData
        );
        $this->newLine();

        // Cache recommendations
        $this->info('💡 Cache Recommendations:');
        $this->line('• Monitor cache hit ratios in production');
        $this->line('• Consider increasing cache TTL for stable data');
        $this->line('• Use cache tags for more granular invalidation');
        $this->line('• Implement cache warming strategies for cold starts');
        $this->newLine();
    }

    /**
     * Show query execution plans
     */
    private function showQueryPlans()
    {
        $this->info('🔍 Query Execution Plans:');
        $this->newLine();

        $queries = [
            'DosageForm lookup by ID' => "SELECT * FROM dosage_foams WHERE id = 1 AND deleted_at IS NULL",
            'Unit options query' => "SELECT id, name FROM units WHERE deleted_at IS NULL",
            'DosageForm options query' => "SELECT id, name FROM dosage_foams WHERE deleted_at IS NULL",
            'Active DosageForm query' => "SELECT * FROM dosage_foams WHERE status = true AND deleted_at IS NULL",
        ];

        foreach ($queries as $name => $sql) {
            $this->line("📋 {$name}:");
            $plan = DB::select("EXPLAIN (ANALYZE, BUFFERS) {$sql}");
            
            foreach ($plan as $row) {
                $this->line("  " . $row->{'query plan'});
            }
            $this->newLine();
        }
    }

    /**
     * Format bytes into human readable format
     */
    private function formatBytes($bytes)
    {
        if ($bytes >= 1024 * 1024) {
            return round($bytes / (1024 * 1024), 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
} 