<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;
use App\Models\DosageForm;
use App\Models\Unit;
use App\Services\LocationCacheService;
use App\Services\UserAddressCacheService;

class ClearAllCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-all {--type=all : Type of cache to clear (all, roles, locations, addresses, forms)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all application-specific caches (roles, locations, addresses, forms)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');

        switch ($type) {
            case 'roles':
                $this->clearRoleCaches();
                break;
            case 'locations':
                $this->clearLocationCaches();
                break;
            case 'addresses':
                $this->clearAddressCaches();
                break;
            case 'forms':
                $this->clearFormCaches();
                break;
            case 'all':
            default:
                $this->clearAllCaches();
                break;
        }

        return 0;
    }

    /**
     * Clear all caches
     */
    private function clearAllCaches()
    {
        $this->info('Clearing all application caches...');
        
        $this->clearRoleCaches();
        $this->clearLocationCaches();
        $this->clearAddressCaches();
        $this->clearFormCaches();

        $this->info('All caches cleared successfully!');
    }

    /**
     * Clear role caches
     */
    private function clearRoleCaches()
    {
        $this->info('Clearing role caches...');
        Role::clearAllCache();
        $this->line('✓ Role caches cleared');
    }

    /**
     * Clear location caches
     */
    private function clearLocationCaches()
    {
        $this->info('Clearing location caches...');
        LocationCacheService::clearAllCache();
        $this->line('✓ Location caches cleared');
    }

    /**
     * Clear user address caches
     */
    private function clearAddressCaches()
    {
        $this->info('Clearing user address caches...');
        UserAddressCacheService::clearAllCache();
        $this->line('✓ User address caches cleared');
    }

    /**
     * Clear form-related caches (DosageForm, Unit)
     */
    private function clearFormCaches()
    {
        $this->info('Clearing form caches...');
        DosageForm::clearAllCache();
        Unit::clearAllCache();
        $this->line('✓ Form caches cleared');
    }
} 