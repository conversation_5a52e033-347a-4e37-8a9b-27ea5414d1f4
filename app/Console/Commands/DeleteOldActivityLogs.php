<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Spatie\Activitylog\Models\Activity;
use Carbon\Carbon;

class DeleteOldActivityLogs extends Command
{
    protected $signature = 'activitylogs:clean';
    protected $description = 'Delete activity logs older than 7 days';

    public function handle()
    {
        $sevenDaysAgo = Carbon::now()->subDays(7);
        $deletedCount = Activity::where('created_at', '<', $sevenDaysAgo)->delete();
        $this->info("Deleted {$deletedCount} activity log(s) older than 7 days.");
    }
}