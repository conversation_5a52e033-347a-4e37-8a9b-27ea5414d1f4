<?php

namespace App\Console\Commands;

use Illuminate\Support\Facades\Mail;
use Illuminate\Console\Command;
use App\Mail\RetryPaymentMail;
use App\Models\Order;
use Carbon\Carbon;

class PaymentRetryMailCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment-retry-mail';

    /**
     * The console command description.
     *
     * @var string
     */
   protected $description = 'Send retry payment email for pending orders with status init and created more than 15 minutes ago';

    /**
     * Execute the console command.
     */
    public function handle()
    {
         // Calculate the time 15 minutes ago
        $fifteenMinutesAgo = Carbon::now()->subMinutes(15);


        // Fetch orders that match the criteria
        $orders = Order::where('payment_status', 'init')
                       ->where('created_at', '<=', $fifteenMinutesAgo)
                       ->where('mail_count' , '<' , 1)
                       ->get();

            

        // Process each order and send retry payment email
        foreach ($orders as $order) {
            // Send email
            Mail::to($order->user->email)->send(new RetryPaymentMail($order));

            $order->update(['mail_count' => ($order->mail_count + 1)]);
            
            $this->info("Sent retry payment email for Order ID: {$order->id}");
        }

        $this->info('Retry payment emails sent successfully.');
    }
}
