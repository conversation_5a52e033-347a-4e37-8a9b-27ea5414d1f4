<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class CreateCachingOptimizationIndexes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:create-caching-indexes {--dry-run : Show what would be executed without running}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create caching optimization indexes concurrently for zero-downtime deployment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if (DB::getDriverName() !== 'pgsql') {
            $this->error('This command is designed for PostgreSQL only. Concurrent index creation is not available for other databases.');
            return 1;
        }

        $this->info('Creating caching optimization indexes concurrently...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No indexes will be created');
        }

        $sqlFile = database_path('scripts/create_caching_optimization_indexes_concurrent.sql');
        
        if (!File::exists($sqlFile)) {
            $this->error("SQL file not found: {$sqlFile}");
            return 1;
        }

        $sql = File::get($sqlFile);
        
        // Split the SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            fn($statement) => !empty($statement) && !str_starts_with(trim($statement), '--')
        );

        $this->info("Found " . count($statements) . " SQL statements to execute");
        
        if ($isDryRun) {
            $this->line("\nSQL statements that would be executed:");
            foreach ($statements as $index => $statement) {
                $this->line(($index + 1) . ". " . $statement . ";");
            }
            return 0;
        }

        $this->warn('This operation may take several minutes on large tables.');
        
        if (!$this->confirm('Do you want to continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $progressBar = $this->output->createProgressBar(count($statements));
        $progressBar->start();
        
        $successful = 0;
        $failed = 0;
        $errors = [];

        foreach ($statements as $statement) {
            try {
                // Execute each statement separately
                DB::statement($statement);
                $successful++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = [
                    'statement' => $statement,
                    'error' => $e->getMessage()
                ];
                
                // For concurrent index creation, some errors are expected (like if index already exists)
                if (!str_contains($e->getMessage(), 'already exists')) {
                    $this->error("\nError executing statement: " . substr($statement, 0, 100) . "...");
                    $this->error("Error: " . $e->getMessage());
                }
            }
            
            $progressBar->advance();
        }
        
        // Create pharmaceutical company specific index dynamically
        $this->createPharmaceuticalCompanyIndex();
        $successful++;

        $progressBar->finish();
        $this->newLine(2);

        // Summary
        $this->info("Index creation completed!");
        $this->info("Successful: {$successful}");
        
        if ($failed > 0) {
            $this->warn("Failed: {$failed}");
            
            if ($this->option('verbose')) {
                $this->line("\nErrors encountered:");
                foreach ($errors as $error) {
                    $this->line("Statement: " . substr($error['statement'], 0, 100) . "...");
                    $this->line("Error: " . $error['error']);
                    $this->line("---");
                }
            }
        }

        // Verify some key indexes were created
        $this->verifyIndexes();

        return 0;
    }

    /**
     * Verify that key indexes were created successfully
     */
    private function verifyIndexes()
    {
        $this->info("\nVerifying key indexes...");
        
        $keyIndexes = [
            'idx_roles_name_guard',
            'idx_states_country_id', 
            'idx_cities_state_id',
            'idx_user_addresses_user_id',
            'idx_model_roles_lookup',
            'idx_users_active_deleted',
            'idx_model_has_roles_pharma_company_dynamic'
        ];

        foreach ($keyIndexes as $indexName) {
            $exists = $this->indexExists($indexName);
            
            if ($exists) {
                $this->info("✓ {$indexName}");
            } else {
                $this->warn("✗ {$indexName} - Not found");
            }
        }
    }

    /**
     * Create pharmaceutical company specific index dynamically
     */
    private function createPharmaceuticalCompanyIndex()
    {
        try {
            // Get the pharmaceutical company role ID
            $roleId = DB::table('roles')
                ->where('name', 'Pharmaceutical Company')
                ->where('guard_name', 'web')
                ->value('id');
            
            if ($roleId) {
                $this->info("\nCreating pharmaceutical company specific index (role_id: {$roleId})...");
                
                $sql = "CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_model_has_roles_pharma_company_dynamic 
                        ON model_has_roles (model_id) 
                        WHERE model_type = 'App\\Models\\User' 
                        AND role_id = {$roleId}";
                
                DB::statement($sql);
                $this->info("✓ Pharmaceutical company index created successfully");
            } else {
                $this->warn("Pharmaceutical Company role not found, skipping specific index");
            }
        } catch (\Exception $e) {
            $this->error("Failed to create pharmaceutical company index: " . $e->getMessage());
        }
    }

    /**
     * Check if an index exists
     */
    private function indexExists(string $indexName): bool
    {
        $result = DB::select("
            SELECT COUNT(*) as count
            FROM pg_indexes 
            WHERE indexname LIKE ?
        ", ["%{$indexName}%"]);
        
        return $result[0]->count > 0;
    }
} 