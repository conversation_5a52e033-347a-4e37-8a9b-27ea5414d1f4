<?php

namespace App\Console\Commands;

use App\Services\ImportService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ImportCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import-manager';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch the job to import data by job manager';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Dispatch the job
        Log::info('Dispatched    the import job to run pending status job');
        ImportService::executeJob();
        Log::info('time       ' . now());
        $this->info('Dispatched the job to run the pending status job');
        return Command::SUCCESS;
    }
}
