<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ProtectProductionDatabaseCommand extends Command
{
    protected $signature = 'db:protect 
                           {--force : Force protection even in production environment}
                           {--backup : Create backup before applying protection}';

    protected $description = 'Protect production database from accidental destructive operations';

    private array $productionDatabases = ['dpharma_new', 'dpharma_qa', 'dpharma_prod', 'dpharma_production', 'dpharma'];

    public function handle(): int
    {
        $this->info('🛡️  Protecting Production Database...');
        
        // Safety check
        if (!$this->validateSafetyFirst()) {
            return 1;
        }
        
        $currentDatabase = $this->getCurrentDatabase();
        
        if (!$this->isProductionDatabase($currentDatabase)) {
            $this->info("✅ Database '{$currentDatabase}' is not a production database. No protection needed.");
            return 0;
        }
        
        $this->warn("⚠️  Production database '{$currentDatabase}' detected!");
        
        if ($this->option('backup')) {
            $this->createBackup($currentDatabase);
        }
        
        $this->applyProtection($currentDatabase);
        
        $this->info('✅ Production database protection applied successfully!');
        return 0;
    }
    
    private function validateSafetyFirst(): bool
    {
        // Run our safety validation first
        $exitCode = $this->call('db:validate-safety');
        
        if ($exitCode !== 0 && !$this->option('force')) {
            $this->error('🚨 Safety validation failed. Use --force to override.');
            return false;
        }
        
        return true;
    }
    
    private function getCurrentDatabase(): string
    {
        $connection = config('database.default');
        return config("database.connections.{$connection}.database") ?? 'unknown';
    }
    
    private function isProductionDatabase(string $database): bool
    {
        return in_array($database, $this->productionDatabases);
    }
    
    private function createBackup(string $database): void
    {
        $this->info("📦 Creating backup of database '{$database}'...");
        
        $backupName = $database . '_backup_' . date('Y_m_d_H_i_s');
        $this->line("Backup name: {$backupName}");
        
        // Note: This would require actual backup implementation
        // For now, just log the backup intention
        $this->warn("📝 Backup functionality would be implemented here");
        $this->warn("📝 Consider running: pg_dump {$database} > {$backupName}.sql");
    }
    
    private function applyProtection(string $database): void
    {
        $this->info("🔒 Applying protection to database '{$database}'...");
        
        // Create a protection table if it doesn't exist
        if (!Schema::hasTable('database_protection')) {
            Schema::create('database_protection', function ($table) {
                $table->id();
                $table->string('database_name');
                $table->boolean('is_protected')->default(true);
                $table->text('protection_note')->nullable();
                $table->timestamp('protected_at');
                $table->timestamps();
            });
            
            $this->line("✅ Created 'database_protection' table");
        }
        
        // Insert protection record
        DB::table('database_protection')->updateOrInsert(
            ['database_name' => $database],
            [
                'is_protected' => true,
                'protection_note' => 'Protected by db:protect command on ' . now()->format('Y-m-d H:i:s'),
                'protected_at' => now(),
                'updated_at' => now(),
                'created_at' => now(),
            ]
        );
        
        $this->line("✅ Protection record created");
        $this->line("✅ Database '{$database}' is now marked as protected");
        
        // Display warning message
        $this->warn("⚠️  WARNING: This database contains production data!");
        $this->warn("⚠️  Always use development database for local development:");
        $this->warn("⚠️  DB_DATABASE=dpharma_dev");
    }
} 