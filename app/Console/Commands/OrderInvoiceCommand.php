<?php

namespace App\Console\Commands;

use App\Mail\PayoutProcessedMail;
use App\Models\Payout;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Storage;
use App\Mail\PayoutCommissionProcessedMail;
use App\Jobs\PaymentSuccessfulMailJob;
use App\Jobs\PaymentFailMailJob;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;

class OrderInvoiceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'orderInvoice:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate mising order invoice';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $orders = Order::with('subOrder')
                ->whereNull('pdf_path')
                ->whereNotNull('ecommerce_tran_id')
                ->get();

        foreach ($orders as $order) {
            Log::info("order invoice generate for {$order->id}");
            $paymentTypeCount = $order->subOrder->whereIn('payment_type',['pay_now','pay_later'])->count();
            $paymentType = $paymentTypeCount > 0 ? 'pay_now' : 'credit_line';
            $paymentStatus = $order->payment_status;
            $transactionStatus = null;
            if ($paymentStatus == 'paid') {
                $transactionStatus = 'success';
                PaymentSuccessfulMailJob::dispatch($order, $transactionStatus, $paymentType);

            }else{

                PaymentFailMailJob::dispatch($order, $transactionStatus);
            }
           
        }

    }


}
