<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class Clear2FASessionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:clear-2fa-sessions {--remember-me : Also clear expired remember me cache}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear stuck 2FA sessions that prevent login completion and optionally clear expired remember me cache';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Clearing stuck 2FA sessions...');

        // Clear database sessions that contain 2FA data
        $cleared = DB::table('sessions')
            ->where('payload', 'like', '%2fa%')
            ->orWhere('payload', 'like', '%login.id%')
            ->orWhere('payload', 'like', '%otp%')
            ->delete();

        $this->info("Cleared {$cleared} stuck 2FA sessions from database.");
        
        // Clear general 2FA related cache entries
        cache()->forget('remember_me');
        
        if ($this->option('remember-me')) {
            $this->info('Clearing expired remember me cache entries...');
            
            // Get all users with remember_me disabled in database
            $usersWithoutRememberMe = User::where('remember_me', false)->pluck('id');
            
            $cleanedCount = 0;
            foreach ($usersWithoutRememberMe as $userId) {
                $cacheKey = 'remember_me_' . $userId;
                if (cache()->has($cacheKey)) {
                    cache()->forget($cacheKey);
                    $cleanedCount++;
                }
            }
            
            $this->info("Cleared {$cleanedCount} expired remember me cache entries.");
        }
        
        $this->info('Cleared 2FA related cache entries.');
        $this->info('✅ 2FA session cleanup completed!');
        
        return Command::SUCCESS;
    }
} 