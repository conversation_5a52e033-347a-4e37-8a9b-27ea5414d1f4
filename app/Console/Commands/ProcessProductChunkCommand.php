<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Services\CSVUtility;
use App\Services\ProductService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessProductChunkCommand extends Command
{
    protected $signature = 'process-product-chunk {chunkData} {jobId} {extractPath}';

    protected $description = 'Process product import chunk';

    public function handle()
    {
        try {
            $chunkData = base64_decode($this->argument('chunkData'));
            $jobId = $this->argument('jobId');
            $extractPath = $this->argument('extractPath');

            $chunk = json_decode($chunkData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                Log::error('Invalid chunk data', ['chunkData' => $chunkData]);
                $this->error('Invalid chunk data');
                return 1;
            }

            // Reset CSVUtility counters for this chunk
            \App\Services\CSVUtility::$records = [];
            \App\Services\CSVUtility::$successRows = 0;
            \App\Services\CSVUtility::$errorRows = 0;

            $insertable = [];
            $checkBoolean = [];
            $mediaMap = []; // product name => mediaItems
            $distributorMap = []; // product name => distributor list

            foreach ($chunk as $row) {
                $result =  ProductService::processProductRow($row, $jobId, $extractPath);
                if (is_array($result)) {
                    $insertable[] = $result['data'];
                    $mediaMap[$result['data']['name']] = $result['mediaItems'];
                    $distributorMap[$result['data']['name']] = $result['validDistributors'];
                } else {
                    $checkBoolean[] = $result;
                }
            }
            if (count($checkBoolean) === 0 && !empty($insertable)) {
                // First, validate which products have valid images
                $validProductsToInsert = [];
                Log::info("Validating products with images...");
                Log::info("Total products to insert: " . count($insertable));
                Log::info("Total media items: " . count($mediaMap));
                foreach ($insertable as $productData) {
                    $productName = $productData['name'];
                    $hasValidImages = false;

                    // Check if this product has valid images
                    if (isset($mediaMap[$productName]) && !empty($mediaMap[$productName])) {
                        foreach ($mediaMap[$productName] as $mediaItem) {
                            if ($this->isValidImage($mediaItem, $productName)) {
                                $hasValidImages = true;
                                break; // At least one valid image found
                            }
                        }
                    }

                    // Only include products that have valid images
                    if ($hasValidImages) {
                        $validProductsToInsert[] = $productData;
                    } else {
                        // Log products that were skipped due to invalid/missing images
                        \Log::warning("Product '{$productName}' skipped - no valid matching images found");
                    }
                }

                // Only proceed if we have valid products to insert
                if (!empty($validProductsToInsert)) {
                    // Insert only validated products in chunks
                    foreach (collect($validProductsToInsert)->chunk(500) as $chunkedInsert) {
                        Product::insert($chunkedInsert->toArray());
                    }

                    // Get the inserted products
                    $lowerNameMap = array_map('strtolower', array_column($validProductsToInsert, 'name'));

                    $insertedProducts = Product::whereIn(\DB::raw('LOWER(name)'), $lowerNameMap)
                        ->get()
                        ->keyBy(fn($item) => strtolower($item->name));

                    // Loop over media map directly (assuming mediaMap keys are original names)
                    foreach ($mediaMap as $originalProductName => $mediaItems) {
                        $productKey = strtolower($originalProductName);

                        // Get the matching product
                        $product = $insertedProducts[$productKey] ?? null;

                        if (!$product) {
                            \Log::warning("Product not found for media upload: $originalProductName");
                            continue;
                        }

                        $defaultImageId = null;
                        foreach ($mediaItems as $mediaItem) {
                            if ($this->isValidImage($mediaItem, $originalProductName)) {
                                try {
                                    $media = $product->addMedia($mediaItem['path'])
                                        ->preservingOriginal()
                                        ->usingName($mediaItem['name'])
                                        ->usingFileName($mediaItem['original_filename'])
                                        ->toMediaCollection('product-images', 's3'); // explicitly use s3

                                    if ($mediaItem['is_default']) {
                                        $defaultImageId = $media->id;
                                    }
                                } catch (\Exception $e) {
                                    \Log::error("Failed to upload image for product {$originalProductName}: " . $e->getMessage());
                                }
                            }
                        }

                        if ($defaultImageId) {
                            $product->update(['default_image_id' => $defaultImageId]);
                        }

                        // Attach distributors if available
                        if (!empty($distributorMap[$originalProductName])) {
                            $product->distributors()->attach($distributorMap[$originalProductName]->pluck('id'));
                        }
                    }
                } else {
                    \Log::info("No products inserted - no valid products with matching images found");
                }
            }

            $successRecords = [];
            $failedRecords = [];

            foreach (\App\Services\CSVUtility::$records as $record) {
                if ($record['status'] === 'Success') {
                    $successRecords[] = $record;
                } else {
                    $failedRecords[] = $record;
                }
            }

            $output = [
                'successRows' => \App\Services\CSVUtility::$successRows,
                'errorRows' => \App\Services\CSVUtility::$errorRows,
                'total' => count($chunk),
                'successRecords' => $successRecords,
                'failedRecords' => $failedRecords
            ];



            echo json_encode($output);
            return 0;
        } catch (\Exception $e) {
            Log::error('Error processing chunk: ' . $e->getMessage());
            $this->error('Error processing chunk: ' . $e->getMessage());
            return 1;
        }
    }

    private function isValidImage($mediaItem, $productName)
    {


        if (!isset($mediaItem['path']) || !isset($mediaItem['name']) || !isset($mediaItem['original_filename'])) {
            Log::warning("Missing fields in media item: " . json_encode($mediaItem));
            return false;
        }

        if (!file_exists($mediaItem['path'])) {
            Log::warning("File does not exist: {$mediaItem['path']} for product '{$productName}'");
            return false;
        }

        $imageInfo = @getimagesize($mediaItem['path']);
        if ($imageInfo === false) {
            Log::warning("Not a valid image: {$mediaItem['path']} for product '{$productName}'");
            return false;
        }

        $allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($imageInfo['mime'], $allowedMimeTypes)) {
            Log::warning("Unsupported MIME type ({$imageInfo['mime']}) for image: {$mediaItem['path']}");
            return false;
        }

        if (filesize($mediaItem['path']) > 10 * 1024 * 1024) {
            Log::warning("Image too large (>10MB): {$mediaItem['path']}");
            return false;
        }

        return true;
    }
}
