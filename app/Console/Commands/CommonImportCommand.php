<?php

namespace App\Console\Commands;

use App\Services\ProductService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class CommonImportCommand extends Command
{
    protected $signature = 'import:comman-data {data}';
    protected $description = 'This command will pick the imported file and execute it';

    public function handle()
    {
        $data = $this->argument('data');

        // Decode the JSON input
        $jsonData = json_decode($data, true);

        if (!isset($jsonData['importType']) || !isset($jsonData['file_path'])) {
            $this->error('Invalid JSON data passed to the command.');
            return;
        }

        if ($jsonData['importType'] === 'product') {
            Log::info('Starting product import job', $jsonData);

            // Setup S3 and local paths
            $s3Disk = Storage::disk('s3');
            $s3FolderPath = $jsonData['file_path']; // e.g., 'imports/127'
            $localTempDir = storage_path("app/temp_imports/{$jsonData['folder_id']}");

            if (!file_exists($localTempDir)) {
                mkdir($localTempDir, 0755, true);
            }

            // Download Excel file from S3
            $excelFiles = $s3Disk->files($s3FolderPath);
            $excelFile = collect($excelFiles)->first(fn($f) => str_ends_with($f, '.xlsx') || str_ends_with($f, '.xls'));

            if (!$excelFile) {
                $this->error("No Excel file found in {$s3FolderPath}");
                return;
            }

            $localExcelPath = "{$localTempDir}/" . basename($excelFile);
            $s3Content = $s3Disk->get($excelFile);
            file_put_contents($localExcelPath, $s3Content);

            // Inject local path into jsonData
            $jsonData['local_excel_path'] = $localExcelPath;

            // Run import service
            $outputs = ProductService::importData($jsonData);

            foreach ($outputs as $result) {
                if ($result['success']) {
                    $this->info("✅ Success Rows: {$result['successRows']}, errorRows: {$result['errorRows']}, Total: {$result['total']}");
                } else {
                    $this->error("Chunk {$result['chunk']}: " . ($result['output'] ?? 'No error message'));
                }
            }

            // ZIP extraction cleanup
            $extractedImages = "{$localTempDir}/extracted_images";
            if (file_exists($extractedImages)) {
                exec("rm -rf " . escapeshellarg($extractedImages));
            }

            // Optionally delete local temp files
            unlink($localExcelPath);
            @rmdir($localTempDir);
        }
    }
}
