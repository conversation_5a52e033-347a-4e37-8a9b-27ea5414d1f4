<?php

namespace App\Console\Commands;

use App\Mail\PayoutProcessedMail;
use App\Models\Payout;
use App\Models\Transaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Storage;
use App\Mail\PayoutCommissionProcessedMail;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Service\EcommerceService;
use App\Service\PayoutService;

class PayoutCommand extends Command
{

    protected $payoutService;
    protected $ecommerceService;

    public function __construct(PayoutService $payoutService, EcommerceService $ecommerceService)
    {
        parent::__construct();
        $this->payoutService = $payoutService;
        $this->ecommerceService = $ecommerceService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payout:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate payouts on 1st and 15th of each month';

    /**
     * Execute the console command.
     */
    public function handle()
    {

        $today = now()->startOfDay();
        $day = $today->day;

        if ($day === 1) { // get date previous month
            $startDate = $today->copy()->subMonth()->startOfMonth();
            $endDate = $today->copy()->subMonth()->endOfMonth();
            $cycle = false;
        } elseif ($day === 15) { 
            $startDate = $today->copy()->startOfMonth();
            $endDate = $today->copy()->subDay();
            $cycle = true;
        } else {
            $this->info("Today is not the 1st or 15th. Exiting.");
            return;
        }

        
        // Fetch records to process
        $query = Payout::where('is_payout', false)
                ->whereDate('start_date', '>=', $startDate)
                ->whereDate('end_date', '<=', $endDate)
                ->where(function($q) {
                    $q->where(function($q2) {
                            // Case 1: ecommerce_tran_id is NULL and ecommerce_status is NULL
                            $q2->whereNull('ecommerce_tran_id')
                            ->whereNull('ecommerce_status');
                        })
                    ->orWhere(function($q2) {
                            // Case 4: ecommerce_tran_id has value AND ecommerce_status NOT IN [0,1] AND NOT NULL
                            $q2->whereNotNull('ecommerce_tran_id')
                            ->whereNotNull('ecommerce_status')
                            ->whereNotIn('ecommerce_status', [0, 1]);
                        });
                });
        if($cycle){
            $query->where('cycle_type', 'bi_weekly');
        }

        $this->info("payout query run");
        // Use chunk to process data in smaller segments
        $query->chunk(5, function ($payouts) {
            $this->info("find payout");
          
            foreach ($payouts as $payout) {
                $payoutInvoiceId = $payout->id . $payout->user_id . date('mY');
                $payout->update([
                    
                    'payout_invoice_id' => $payoutInvoiceId,
                ]);
                $fileName = 'payout_' . $payout->id . '.pdf';
                $s3Path = config('constants.api.payout_pdf.main_path')  . $fileName;

                $payout_cycle_date = $this->payoutService->invoiceDateFormat($payout);

                $grid =  $this->payoutService->payoutOrderGrid($payout->payoutSubOrders, $payout,$payout_cycle_date); 
                $payAmout = $payout->payout_type == 'full' ?  $grid['totalPayout'] : ($grid['totalPayout'] - $grid['returnAmount']);
                $paymentStatus  =  $this->SupplierPayAmout($grid,$payAmout,$payout);

                if(!$paymentStatus){
                    Log::error("payout process broken for this payout {$payout_cycle_date}");
                   // continue;
                }
               
               
               

                Log::info("Processing payout ID: {$payout->id}");
            }
        });

        $this->info("Processed payouts Done.");
    }


    public function SupplierPayAmout($reqData, $amount, $payout)
    {
        // Use test amount in non-production environments
        if (!in_array(config('app.env'), ['production', 'prod'])) {
            $amount = 10;
        }
        $data  = [
            "returnUrl" => url("/supplier-payment-handler"),
           // "callbackUrl" =>"https://c1da507911ca.ngrok-free.app/api/webhook",
            "callbackUrl" => config('app.url')."/api/v1/webhook/payment-handler",
            "channelId" => 15,
            "providerChannelId" => 'UOVBMYKL',
            "description" => "Payout amount done.",
            "customer" => [
                "email" => $reqData['email'],
                "mobileNo" => "**********",
                "name" => "AC NAME1 **********",
                "username" => "**********"
            ],
        ];
        try{
            $res =  $this->ecommerceService->store(null,$amount, 'payout',$data);
            Log::info("payput paid response " . json_encode($res));
            //sleep(20);
            if($res && isset($res['transactionNumber'])) {
                Log::info("payout payment handler for this PC ". $reqData['email']);
    
                $payout->update([
                    'ecommerce_tran_id' => $res['transactionNumber'],
                ]);
                Log::info("payout sucessfully done for this transaction number " . $res['transactionNumber']);
                return true;
            }else{
               
                Log::info("Getting Error on payout payment handler for this PC : " . $reqData['email']);
                return false ; 
            } 
        } catch(\Exception $e) {
            Log::info("Getting Error on payout payment handler for this PC : " . $e->getMessage());
            return false ; 
        }
    
    }


}
