<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Services\ExtendedSessionService;

class CheckRememberMeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:check-remember-me {email? : Email of user to check} {--set : Set remember me for user} {--clear : Clear remember me for user} {--all : Show all users with remember me}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check, set, or clear remember me status for users';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('all')) {
            $this->showAllRememberMeUsers();
            return Command::SUCCESS;
        }

        $email = $this->argument('email');
        
        if (!$email) {
            $email = $this->ask('Enter user email');
        }

        $user = User::where('email', $email)->first();
        
        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return Command::FAILURE;
        }

        if ($this->option('set')) {
            $this->setRememberMe($user);
        } elseif ($this->option('clear')) {
            $this->clearRememberMe($user);
        } else {
            $this->checkRememberMe($user);
        }

        return Command::SUCCESS;
    }

    private function showAllRememberMeUsers()
    {
        $users = User::where('remember_me', true)->get(['id', 'name', 'email', 'remember_me']);
        
        if ($users->isEmpty()) {
            $this->info('No users have remember me enabled.');
            return;
        }

        $this->info('Users with remember me enabled:');
        $this->table(['ID', 'Name', 'Email', 'Cache Status'], $users->map(function ($user) {
            $cacheStatus = cache()->has('remember_me_' . $user->id) ? 'Valid' : 'Expired/Missing';
            return [$user->id, $user->name, $user->email, $cacheStatus];
        }));
    }

    private function checkRememberMe(User $user)
    {
        $this->info("Remember Me Status for: {$user->name} ({$user->email})");
        $this->line("User ID: {$user->id}");
        $this->line("Database remember_me: " . ($user->remember_me ? 'Yes' : 'No'));
        
        $cacheKey = 'remember_me_' . $user->id;
        $cacheValue = cache()->get($cacheKey);
        $this->line("Cache status: " . ($cacheValue ? 'Valid' : 'Missing/Expired'));
        
        if ($cacheValue) {
            $this->info("✅ User will bypass 2FA on next login");
        } else {
            $this->warn("⚠️  User will need to complete 2FA on next login");
        }
    }

    private function setRememberMe(User $user)
    {
        $user->update(['remember_me' => true]);
        $cacheKey = 'remember_me_' . $user->id;
        cache()->put($cacheKey, true, ExtendedSessionService::daysToMinutes(15));
        
        $this->info("✅ Remember me enabled for {$user->name}");
        $this->line("Cache will expire in 15 days");
    }

    private function clearRememberMe(User $user)
    {
        $user->update(['remember_me' => false]);
        $cacheKey = 'remember_me_' . $user->id;
        cache()->forget($cacheKey);
        
        $this->info("✅ Remember me disabled for {$user->name}");
        $this->line("User will need 2FA on next login");
    }
} 