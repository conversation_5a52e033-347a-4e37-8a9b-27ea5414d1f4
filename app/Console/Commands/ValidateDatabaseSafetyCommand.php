<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ValidateDatabaseSafetyCommand extends Command
{
    protected $signature = 'db:validate-safety 
                           {--check-environment : Check if environment is safe for operations}
                           {--check-production : Check if production database is protected}';

    protected $description = 'Validate database safety to prevent accidental data deletion';

    public function handle(): int
    {
        $this->info('🛡️  Validating Database Safety Configuration...');
        
        $errors = [];
        
        if ($this->option('check-environment') || !$this->hasAnyOption()) {
            $errors = array_merge($errors, $this->checkEnvironmentSafety());
        }
        
        if ($this->option('check-production') || !$this->hasAnyOption()) {
            $errors = array_merge($errors, $this->checkProductionDatabaseProtection());
        }
        
        if (empty($errors)) {
            $this->info('✅ All database safety checks passed!');
            return 0;
        }
        
        $this->error('🚨 Database Safety Violations Found:');
        foreach ($errors as $error) {
            $this->error("   • {$error}");
        }
        
        return 1;
    }
    
    private function hasAnyOption(): bool
    {
        return $this->option('check-environment') || $this->option('check-production');
    }
    
    private function checkEnvironmentSafety(): array
    {
        $errors = [];
        
        // Check current environment
        $environment = app()->environment();
        $this->line("Current Environment: {$environment}");
        
        // Check current database
        $currentConnection = config('database.default');
        $currentDatabase = config("database.connections.{$currentConnection}.database");
        
        $this->line("Current Database Connection: {$currentConnection}");
        $this->line("Current Database Name: {$currentDatabase}");
        
        // Critical production database names
        $productionDatabases = ['dpharma_new', 'dpharma_qa', 'dpharma_prod', 'dpharma_production', 'dpharma'];
        
        if (in_array($currentDatabase, $productionDatabases) && $environment === 'local') {
            $errors[] = "CRITICAL: Production database '{$currentDatabase}' is configured in local environment";
        }
        
        return $errors;
    }
    
    private function checkProductionDatabaseProtection(): array
    {
        $errors = [];
        
        // Get all database connections
        $connections = config('database.connections', []);
        $productionDatabases = ['dpharma_new', 'dpharma_qa', 'dpharma_prod', 'dpharma_production', 'dpharma'];
        
        $this->line('Checking all database connections...');
        
        foreach ($connections as $connectionName => $connection) {
            $database = $connection['database'] ?? null;
            
            if (!$database) {
                continue;
            }
            
            $this->line("  Connection '{$connectionName}': {$database}");
            
            // Check if production database is exposed
            if (in_array($database, $productionDatabases)) {
                
                // Check if this is the testing connection (which should use :memory:)
                if ($connectionName === 'testing' && $database !== ':memory:') {
                    $errors[] = "Testing connection should use ':memory:' database, not '{$database}'";
                }
                
                // Check if production database is the default in non-production environment
                if ($connectionName === config('database.default') && 
                    app()->environment() !== 'production' && 
                    !app()->environment('testing')) {
                    $errors[] = "Production database '{$database}' should not be default in {app()->environment()} environment";
                }
            }
        }
        
        return $errors;
    }
} 