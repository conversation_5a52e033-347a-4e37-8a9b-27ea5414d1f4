<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class FixPostgresSequences extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:fix-sequences {--dry-run : Show what would be executed without actually running}';

    /**
     * The console command description.
     */
    protected $description = 'Fix PostgreSQL primary key sequences for all tables with auto-increment IDs';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        $this->info('Fixing PostgreSQL sequences for all tables...');

        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        try {
            // Get all tables in the database
            $tables = $this->getAllTables();
            
            $fixedCount = 0;
            $skippedCount = 0;

            foreach ($tables as $table) {
                $result = $this->fixSequenceForTable($table, $isDryRun);
                
                if ($result) {
                    $fixedCount++;
                } else {
                    $skippedCount++;
                }
            }

            $this->info("\nSummary:");
            $this->line("Tables with sequences fixed: {$fixedCount}");
            $this->line("Tables skipped (no auto-increment ID): {$skippedCount}");

            if (!$isDryRun) {
                $this->info('✅ All PostgreSQL sequences have been updated successfully!');
            } else {
                $this->info('✅ Dry run completed. Use without --dry-run to apply changes.');
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Error fixing sequences: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Get all tables in the database
     */
    private function getAllTables(): array
    {
        $connection = config('database.default');
        
        if ($connection === 'pgsql') {
            $tables = DB::select("
                SELECT tablename 
                FROM pg_tables 
                WHERE schemaname = 'public' 
                AND tablename NOT LIKE '%_seq'
                ORDER BY tablename
            ");
            
            return array_map(fn($table) => $table->tablename, $tables);
        }

        // Fallback for other databases or using Laravel's Schema
        return DB::connection()->getDoctrineSchemaManager()->listTableNames();
    }

    /**
     * Fix sequence for a specific table
     */
    private function fixSequenceForTable(string $tableName, bool $isDryRun): bool
    {
        try {
            // Check if table has an auto-increment 'id' column
            if (!Schema::hasColumn($tableName, 'id')) {
                $this->line("⏭️  Skipping {$tableName} - no 'id' column");
                return false;
            }

            // Get the column info to check if it's auto-increment
            $columnInfo = DB::select("
                SELECT column_default 
                FROM information_schema.columns 
                WHERE table_name = ? AND column_name = 'id' AND table_schema = 'public'
            ", [$tableName]);

            if (empty($columnInfo) || !str_contains($columnInfo[0]->column_default ?? '', 'nextval')) {
                $this->line("⏭️  Skipping {$tableName} - 'id' column is not auto-increment");
                return false;
            }

            // Get the maximum ID from the table
            $maxId = DB::table($tableName)->max('id') ?? 0;

            // Get the sequence name (usually tablename_id_seq)
            $sequenceName = "{$tableName}_id_seq";

            // Check if sequence exists
            $sequenceExists = DB::select("
                SELECT sequence_name 
                FROM information_schema.sequences 
                WHERE sequence_name = ? AND sequence_schema = 'public'
            ", [$sequenceName]);

            if (empty($sequenceExists)) {
                $this->line("⏭️  Skipping {$tableName} - sequence {$sequenceName} not found");
                return false;
            }

            $newSequenceValue = $maxId + 1;

            $sql = "SELECT setval('{$sequenceName}', {$newSequenceValue}, true);";

            if ($isDryRun) {
                $this->line("🔍 Would execute for {$tableName}: {$sql}");
            } else {
                DB::select($sql);
                $this->line("✅ Fixed {$tableName}: sequence set to {$newSequenceValue}");
            }

            return true;

        } catch (\Exception $e) {
            $this->error("❌ Error fixing {$tableName}: " . $e->getMessage());
            return false;
        }
    }
}
