<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ResetSequencesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:reset-sequences {--force : Force the operation without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Reset all PostgreSQL primary key sequences to fix duplicate key violations';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        if (config('database.default') !== 'pgsql') {
            $this->error('This command only works with PostgreSQL databases.');
            return 1;
        }

        if (!$this->option('force')) {
            if (!$this->confirm('This will reset all primary key sequences. Do you want to continue?')) {
                $this->info('Operation cancelled.');
                return 0;
            }
        }

        $this->info('Resetting PostgreSQL sequences...');

        try {
            // Use modern PostgreSQL method to get all tables with auto-incrementing primary keys
            $tables = DB::select("
                SELECT 
                    t.table_schema as schemaname,
                    t.table_name as tablename,
                    c.column_name as attname,
                    pg_get_serial_sequence(quote_ident(t.table_schema)||'.'||quote_ident(t.table_name), c.column_name) as seq_name
                FROM 
                    information_schema.tables t
                    JOIN information_schema.columns c ON (t.table_name = c.table_name AND t.table_schema = c.table_schema)
                WHERE 
                    t.table_schema = 'public'
                    AND t.table_type = 'BASE TABLE'
                    AND c.column_default LIKE 'nextval%'
                    AND pg_get_serial_sequence(quote_ident(t.table_schema)||'.'||quote_ident(t.table_name), c.column_name) IS NOT NULL
                ORDER BY t.table_name
            ");

            $resetCount = 0;
            $bar = $this->output->createProgressBar(count($tables));

            foreach ($tables as $table) {
                if (empty($table->seq_name)) {
                    continue;
                }

                try {
                    // Get the maximum ID from the table
                    $maxId = DB::selectOne("SELECT COALESCE(MAX(\"{$table->attname}\"), 0) as max_id FROM \"{$table->tablename}\"");
                    
                    if ($maxId && isset($maxId->max_id)) {
                        // Reset the sequence to the maximum ID + 1
                        $nextVal = $maxId->max_id + 1;
                        DB::statement("SELECT setval('{$table->seq_name}', {$nextVal}, false)");
                        
                        $this->line("\n✓ Reset sequence for {$table->tablename}.{$table->attname} to {$nextVal}");
                        $resetCount++;
                    }
                } catch (\Exception $e) {
                    $this->line("\n✗ Failed to reset sequence for {$table->tablename}: " . $e->getMessage());
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);
            $this->info("Successfully reset {$resetCount} sequences.");

            // Additional manual sequence reset for common problematic sequences
            $this->info('Checking for additional sequences...');
            
            $additionalSequences = [
                'activity_log_id_seq' => 'activity_log',
                'failed_jobs_id_seq' => 'failed_jobs',
                'jobs_id_seq' => 'jobs',
                'cache_locks_id_seq' => 'cache_locks',
            ];

            foreach ($additionalSequences as $seqName => $tableName) {
                try {
                    // Check if sequence exists
                    $seqExists = DB::selectOne("SELECT 1 FROM pg_class WHERE relname = ? AND relkind = 'S'", [$seqName]);
                    
                    if ($seqExists) {
                        // Check if table exists
                        $tableExists = DB::selectOne("SELECT 1 FROM information_schema.tables WHERE table_name = ? AND table_schema = 'public'", [$tableName]);
                        
                        if ($tableExists) {
                            $maxId = DB::selectOne("SELECT COALESCE(MAX(id), 0) as max_id FROM \"{$tableName}\"");
                            
                            if ($maxId && isset($maxId->max_id)) {
                                $nextVal = $maxId->max_id + 1;
                                DB::statement("SELECT setval('{$seqName}', {$nextVal}, false)");
                                $this->info("✓ Reset additional sequence {$seqName} to {$nextVal}");
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Silently continue if sequence doesn't exist
                }
            }

        } catch (\Exception $e) {
            $this->error('Failed to reset sequences: ' . $e->getMessage());
            return 1;
        }

        $this->info('All sequences have been reset successfully!');
        return 0;
    }
} 