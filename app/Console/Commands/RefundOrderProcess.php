<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Models\SubOrder;
use App\Models\Transaction;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Service\EcommerceService;


class RefundOrderProcess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refund-order-process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process supplier sub orders refund payment';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $ecommerceService = new EcommerceService();
        $chunkSize = 10;
            Order::whereNull('is_refund')
                ->where('status', 'cancelled')
                ->whereHas('subOrder', function ($query) {
                $query->whereNull('is_refund')
                    ->where('status', 'cancelled')
                    ->whereNot('payment_type', 'credit_line')
                    ->where('payment_status', 'paid')
                    ->orderBy('id');
            })
            ->chunk($chunkSize, function ($orders) use($ecommerceService) {
                foreach ($orders as $order) {
                    $this->info("Processing Order ID: {$order->id}");

                    $totalRefundAmt = $order->subOrder->sum('total_amount');
                    try {
                        if($totalRefundAmt > 0 && $order->ecommerce_tran_id && $order->payment_status == 'paid') {
                            $refundRes = $ecommerceService->refundPayment($order->ecommerce_tran_id);
                            Log::error('refund process response from cronjob',
                                [
                                'refundRes' => $refundRes,
                                'order_id' => $order->id,
                                ]
                            );
                            if (isset($refundRes['result'])) {
                                $refundResult =  $refundRes['result'];
                                $refundStatus = $refundResult['refundStatus'];
                                $refundAmount = $refundResult['amount'];
                                $refundMetaData = json_encode($refundResult);
                                $refundAt = now();
                            
                                $order->update([
                                        'is_refund' => true,
                                        'refund_amount' => $refundAmount,
                                        'refund_meta_data' => $refundMetaData,
                                    ]);
                                SubOrder::where('order_id', $order->id)->update([
                                    'refund_at' =>  $refundAt,
                                    'is_refund' => true,
                                ]);
                                if($refundStatus == 11) {

                                        Transaction::create([
                                            "transaction_id" => $refundResult['transactionNumber'],
                                            "order_id" => $order->id,
                                            "sender_id" =>  1, //default super admin id 
                                            "payment_method" => 'CREDIT',
                                            "amount" => $refundAmount,
                                            "status" => 'success',
                                            "order_status" => 'cancelled',
                                            "ecommerce_status" => (int) $refundStatus,
                                            "meta_data" => json_encode($refundResult),
                                        ]);
                                }
                            }elseif($refundRes == 'Payment already refund'){
                                    $order->update([
                                            'is_refund' => true
                                    ]);
                            }else{
                                $order->update([
                                    'is_refund' => false,
                                    'refund_amount' => 0,
                                    'refund_meta_data' => $refundRes,
                                ]);
                            }
                        }
                    } catch (Exception $e) {
                         $order->update([
                                'is_refund' => false,
                                'refund_amount' => 0,
                                'refund_meta_data' => json_encode($e->getMessage()),
                            ]);
                        Log::error("Error for  Order ID: {$order->id} ".$e->getMessage());
                    }
                    
                    
                    
                }
                $this->info("Chunk processed. Fetching next batch...");
            });
    
        $this->info('All Orders processing completed.');
    }
}
