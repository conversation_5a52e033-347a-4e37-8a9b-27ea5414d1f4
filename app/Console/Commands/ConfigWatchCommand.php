<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Symfony\Component\Finder\Finder;
use Symfony\Component\Finder\SplFileInfo;

class ConfigWatchCommand extends Command
{
    public $signature = 'config:watch
        {--interval=1 : Watch interval in seconds}
        {--once : Run once and exit}';

    public $description = 'Watch config files and auto-clear cache on changes';

    private array $fileHashes = [];

    public function handle(): int
    {
        $this->initializeFileHashes();

        while (true) {
            if ($this->configFilesChanged()) {
                $this->clearConfigCache();
            }

            if ($this->option('once')) {
                break;
            }

            sleep((int)$this->option('interval'));
        }

        return self::SUCCESS;
    }

    private function initializeFileHashes(): void
    {
        $files = $this->getWatchedFiles();

        foreach ($files as $file) {
            $this->fileHashes[$file->getRealPath()] = $this->getFileHash($file);
        }
    }

    private function configFilesChanged(): bool
    {
        $changed = false;
        $files = $this->getWatchedFiles();

        foreach ($files as $file) {
            $currentHash = $this->getFileHash($file);
            $path = $file->getRealPath();

            if (!isset($this->fileHashes[$path]) || $this->fileHashes[$path] !== $currentHash) {
                $this->fileHashes[$path] = $currentHash;
                $changed = true;
                $this->info("Detected change in: {$path}");
            }
        }

        return $changed;
    }

    private function getWatchedFiles(): iterable
    {
        $finder = (new Finder())
            ->files()
            ->in(config_path())
            ->name('*.php');

        $envPath = base_path('.env');
        if (file_exists($envPath)) {
            $finder->append([$envPath]);
        }

        return $finder;
    }

    public function getFileHash($file): string
    {
        if ($file instanceof SplFileInfo) {
            return md5_file($file->getRealPath());
        }
        return md5_file($file);
    }

    private function clearConfigCache(): void
    {
        $this->info('Config changes detected. Clearing cache...');
        Artisan::call('config:clear');
        Artisan::call('config:cache');
        $this->info('Config cache refreshed successfully.');
    }
}
