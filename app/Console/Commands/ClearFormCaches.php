<?php

namespace App\Console\Commands;

use App\Models\Unit;
use App\Models\DosageForm;
use Illuminate\Console\Command;

class ClearFormCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-forms {--type= : Specific cache type to clear (unit, dosage, all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear all form-related caches (Unit and DosageForm)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type') ?? 'all';

        switch ($type) {
            case 'unit':
                Unit::clearAllCache();
                $this->info('Unit caches cleared successfully.');
                break;

            case 'dosage':
                DosageForm::clearAllCache();
                $this->info('DosageForm caches cleared successfully.');
                break;

            case 'all':
            default:
                Unit::clearAllCache();
                DosageForm::clearAllCache();
                $this->info('All form caches cleared successfully.');
                break;
        }

        return 0;
    }
} 