<?php

namespace App\Filters;

use Carbon\Carbon;
use Filament\Forms\Get;
use Filament\Forms\Set;
use App\Models\Category;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;
use Illuminate\Database\Eloquent\Builder;

class CategorySubCategoryFilter extends Filter
{
    
    protected function setUp(): void
    {
        parent::setUp();

        $this->form([
            Select::make('category')
                ->label('Category')
                ->options(function () {
                    return Category::whereNull('parent_id')->select('id', 'name')->pluck('name', 'id');
                })
                ->multiple()
                ->searchable()
                ->preload()
                ->live()
                ->afterStateUpdated(function (Set $set) {
                    $set('subcategory', null); // Clear subcategory when category changes
                }),
            
            Select::make('subcategory')
                ->label('Subcategory')
                ->options(function (Get $get) {
                    $selectedCategories = $get('category');
                    
                    if (empty($selectedCategories)) {
                        //  return  Category::whereNotNull('parent_id')->pluck('name', 'id');
                        return [];
                    }

                    $selectedCategories = is_array($selectedCategories) ? $selectedCategories : [$selectedCategories];
                    
                    return Category::whereIn('parent_id', $selectedCategories)
                        ->pluck('name', 'id');
                })
                ->multiple()
                ->searchable()
                // ->visible(fn (Get $get) => !empty($get('category')))
                ->live(),
        ]);

        $this->indicateUsing(function (array $data): ?string {
            $indicators = [];
            
            if (!empty($data['category'])) {
                $categoryNames = Category::whereIn('id', $data['category'])->pluck('name')->toArray();
                if (!empty($categoryNames)) {
                    $indicators[] = 'Categories: ' . implode(', ', $categoryNames);
                }
            }
            
            if (!empty($data['subcategory'])) {
                $subcategoryNames = Category::whereIn('id', $data['subcategory'])->pluck('name')->toArray();
                if (!empty($subcategoryNames)) {
                    $indicators[] = 'Subcategories: ' . implode(', ', $subcategoryNames);
                }
            }
            
            return !empty($indicators) ? implode(' | ', $indicators) : null;
        });

        $this->query(function (Builder $query, array $data): Builder {
            return $query
                ->when(
                    $data['category'] ?? [],
                    fn (Builder $query, array $categories): Builder => 
                        $query->whereHas('categoryForFilter', fn (Builder $query) => 
                            $query->whereIn('id', $categories)
                        )
                )
                ->when(
                    $data['subcategory'] ??  [],
                    fn (Builder $query, array $subcategories): Builder => 
                        $query->whereHas('subcategoryForFilter', fn (Builder $query) => 
                            $query->whereIn('id', $subcategories)
                        )
                );
        });
    }
}

// Usage in your table:
// ->filters([
//     CategorySubcategoryFilter::make(),
// ])