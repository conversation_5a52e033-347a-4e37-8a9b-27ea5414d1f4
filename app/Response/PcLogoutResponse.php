<?php

namespace App\Response;

use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse as Responsable;

class PcLogoutResponse implements Responsable
{
    public function toResponse($request): RedirectResponse
    {
        $user = Auth::user();
        $userId = Auth::id();

        // Log the logout activity before logging out
        if ($user) {
            activity()
                ->performedOn($user)
                ->causedBy($user)
                ->withProperties([
                    'User ID' => $user->id,
                    'User Name' => $user->name,
                    'User Email' => $user->email,
                    'Logout Time' => now()->format('Y-m-d H:i:s'),
                    'IP Address' => $request->ip(),
                    'User Agent' => $request->userAgent(),
                    'Session ID' => $request->session()->getId(),
                ])
                ->log("PC user '{$user->name}' logged out successfully");
        }

        Auth::logout();
        Session::flush();

        if ($userId) {
            cache()->forget('remember_me_' . $userId);
        }

        return redirect()->to(Filament::getPanel('pc')->getLoginUrl());
    }
}
