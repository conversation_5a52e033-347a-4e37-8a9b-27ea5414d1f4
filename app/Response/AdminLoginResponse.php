<?php

namespace App\Response;

use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\RedirectResponse;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Redirect;
use Livewire\Features\SupportRedirects\Redirector;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as Responsable;
use App\Services\ExtendedSessionService;

class AdminLoginResponse implements Responsable
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        try {
            $user = Filament::auth()->user();
            if (!$user->is_active) {
                Filament::auth()->logout();
                Notification::make()
                    ->title('Your account is not active. Please contact the administrator.')
                    ->danger()
                    ->send();

                $loginUrl = Filament::getCurrentPanel()->getLoginUrl();

                return Redirect::to($loginUrl);
            }
            $hasSuperAdminAccess = $user->hasRole('Super Admin');

            // If current user doesn't have Super Admin, check parent
            if (!$hasSuperAdminAccess && $user->parent_id) {
                $parent = User::find($user->parent_id);
                $hasSuperAdminAccess = $parent && $parent->hasRole('Super Admin');
            }

            if ($hasSuperAdminAccess && $user->is_active) {
                // Check if user has remember me enabled and valid cache
                $hasValidRememberMe = $user->remember_me && cache()->get('remember_me_' . $user->id);
                
                if ($hasValidRememberMe) {
                    // User has valid remember me - bypass 2FA and go directly to dashboard
                    // Create 2FA verification record to mark as verified for this session
                    $user->twoFaVerifis()->create([
                        'session_id' => request()->session()->getId(),
                    ]);
                    
                    return redirect()->intended(Filament::getUrl());
                } else {
                    // Check if this is a first-time login with remember me
                    $isFirstTimeRememberMe = cache()->get('remember_me_first_time_login_' . $user->id);
                    
                    if (!$isFirstTimeRememberMe) {
                        // Only send 2FA email if NOT a first-time remember me login
                        // (first-time remember me will be handled by ExtendSessionForRememberMe middleware)
                        $user->send2FAEmail();
                    }
                    return redirect()->intended(Filament::getUrl());
                }
            }

            return $this->handleUnauthorizedAccess('Unauthorized access attempt');
        } catch (\Exception $e) {
            Log::error('Error in AdminLoginResponse:', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->handleUnauthorizedAccess('An error occurred during authentication');
        }
    }

    private function handleUnauthorizedAccess(string $reason)
    {
        Filament::auth()->logout();
        Notification::make()
            ->title($reason)
            ->danger()
            ->persistent()
            ->send();

        $loginUrl = Filament::getCurrentPanel()->getLoginUrl();

        return Redirect::to($loginUrl);
    }
}
