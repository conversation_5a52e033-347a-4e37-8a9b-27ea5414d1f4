<?php

namespace App\Response;

use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Filament\Http\Responses\Auth\Contracts\LogoutResponse as Responsable;

class AdminLogoutResponse implements Responsable
{
    public function toResponse($request): RedirectResponse
    {
        $userId = Auth::id();

        Filament::auth()->logout();

        if ($userId) {
            cache()->forget('remember_me_' . $userId);
        }
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->to(Filament::getPanel('admin')->getLoginUrl());
    }
} 