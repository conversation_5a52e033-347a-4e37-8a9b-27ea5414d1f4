<?php

namespace App\Response;

use Filament\Facades\Filament;
use Illuminate\Auth\Events\Login;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Event;
use App\Filament\Pc\Pages\EditProfile;
use App\Services\ExtendedSessionService;
use Filament\Notifications\Notification;
use Livewire\Features\SupportRedirects\Redirector;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as Responsable;

class PcLoginResponse implements Responsable
{
    public function toResponse($request): RedirectResponse|Redirector
    {
        $user = Filament::auth()->user();
        if (!$user->is_active) {
            Filament::auth()->logout();
            Notification::make()
                ->title('Your account is not active. Please contact the administrator.')
                ->danger()
                ->send();
            $loginUrl = Filament::getCurrentPanel()->getLoginUrl();
            return redirect()->to($loginUrl);
        }
        
        // Check if user has remember me enabled and valid cache
        $hasValidRememberMe = $user->remember_me && cache()->get('remember_me_' . $user->id);
        if ($hasValidRememberMe) {
            // User has valid remember me - bypass 2FA and proceed directly
            // Create 2FA verification record to mark as verified for this session
            $user->twoFaVerifis()->create([
                'session_id' => request()->session()->getId(),
            ]);
        } else {
            // Check if this is a first-time login with remember me
            $isFirstTimeRememberMe = cache()->get('remember_me_first_time_login_' . $user->id);
            
            if (!$isFirstTimeRememberMe) {
                // Only send 2FA email if NOT a first-time remember me login
                // (first-time remember me will be handled by ExtendSessionForRememberMe middleware)
                $user->send2FAEmail();
            }
        }

        if ($user->is_approved_by_admin == true) {
            Filament::getCurrentPanel()->sidebarWidth('25rem');
            $intendedUrl = cache()->get('url.intended_user_id_' . Auth::id());
            if($intendedUrl) {
                cache()->forget('url.intended_user_id_' . Auth::id());
                return redirect()->to($intendedUrl);
            }
            return redirect()->intended(Filament::getUrl());
            
        } elseif ($user->is_approved_by_admin == false) {
            return to_route(EditProfile::getRouteName());
        } else {
            return to_route(EditProfile::getRouteName());
        }
    }
}
