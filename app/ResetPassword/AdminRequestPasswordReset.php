<?php

namespace App\ResetPassword;

use Exception;
use App\Models\User;
use Filament\Forms\Form;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Pages\SimplePage;
use App\Mail\PcResetPasswordMail;
use App\Traits\SendTwoFaCode;
use Filament\Actions\ActionGroup;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Mail;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Password;
use Filament\Support\Facades\FilamentIcon;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Contracts\Auth\CanResetPassword;
use DanH<PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use Filament\Pages\Concerns\InteractsWithFormActions;
use Solutionforest\FilamentEmail2fa\Models\TwoFaCode;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Filament\Notifications\Auth\ResetPassword as ResetPasswordNotification;

class AdminRequestPasswordReset extends SimplePage
{
    use InteractsWithFormActions;
    use WithRateLimiting;
    use SendTwoFaCode;

    /**
     * @var view-string
     */
    protected static string $view = 'custom-view.auth.admin-reset-password';

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    public function mount(): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->form->fill();
    }

    public function request()
    {
        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return;
        }

        $data = $this->form->getState();

        $user = User::where('email', $data['email'])->first();

        $res = $this->sendTwoFaCode($user, 'password_reset');
        return to_route('pc.passwor-reset.otp', ['id' => encryptParam($user->id)]);

        // $status = Password::broker(Filament::getAuthPasswordBroker())->sendResetLink(
        //     $data,
        //     function (CanResetPassword $user, string $token): void {
        //         if (! method_exists($user, 'notify')) {
        //             $userClass = $user::class;

        //             throw new Exception("Model [{$userClass}] does not have a [notify()] method.");
        //         }

        //         $notification = app(ResetPasswordNotification::class, ['token' => $token]);
        //         $notification->url = Filament::getResetPasswordUrl($token, $user);

        //         $user->notify($notification);
        //     },
        // );

        // if ($status !== Password::RESET_LINK_SENT) {
        //     Notification::make()
        //         ->title(__($status))
        //         ->danger()
        //         ->send();

        //     return;
        // }

        // Notification::make()
        //     ->title(__($status))
        //     ->success()
        //     ->send();

        // $this->form->fill();
    }

    protected function getRateLimitedNotification(TooManyRequestsException $exception): ?Notification
    {
        return Notification::make()
            ->title(__('filament-panels::pages/auth/password-reset/request-password-reset.notifications.throttled.title', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]))
            ->body(array_key_exists('body', __('filament-panels::pages/auth/password-reset/request-password-reset.notifications.throttled') ?: []) ? __('filament-panels::pages/auth/password-reset/request-password-reset.notifications.throttled.body', [
                'seconds' => $exception->secondsUntilAvailable,
                'minutes' => $exception->minutesUntilAvailable,
            ]) : null)
            ->danger();
    }

    public function form(Form $form): Form
    {
        return $form;
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        $this->getEmailFormComponent(),
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
        ->label(new HtmlString('<span class="">Email Address <span style="color: red;">*</span></span>'))
            ->rules(['required', 'email','exists:users,email' ,'regex:/^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}$/'])
            ->validationAttribute('email')
            ->autocomplete(false)
            ->dehydrateStateUsing(fn ($state) => strtolower($state))
            ->afterStateUpdated(fn ($state, callable $set) => $set('email', strtolower($state)))
            ->autofocus();
    }

    public function loginAction(): Action
    {
        return Action::make('login')
            ->link()
            ->label('Login')
            // ->icon(match (__('filament-panels::layout.direction')) {
            //     'rtl' => FilamentIcon::resolve('panels::pages.password-reset.request-password-reset.actions.login.rtl') ?? 'heroicon-m-arrow-right',
            //     default => FilamentIcon::resolve('panels::pages.password-reset.request-password-reset.actions.login') ?? 'heroicon-m-arrow-left',
            // })
            ->url(Filament::getCurrentPanel()->getLoginUrl());
    }

    public function getTitle(): string | Htmlable
    {
        return __('filament-panels::pages/auth/password-reset/request-password-reset.title');
    }

    public function getHeading(): string | Htmlable
    {
        return "Forgot Password";
    }

    /**
     * @return array<Action | ActionGroup>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getRequestFormAction(),
        ];
    }

    protected function getRequestFormAction(): Action
    {
        return Action::make('request')
            ->label('Reset Password')
            ->submit('request');
    }

    protected function hasFullWidthFormActions(): bool
    {
        return true;
    }
}
