<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;

class PersonInChargeRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $requiredIfDocInChargeFalse = 'required_if:is_admin_in_charge,false';
        return [
            "dc_name" =>  ['required','string','max:50'],
            "dc_nric" =>  ['nullable', 'string', 'max:50'],
            "dc_mmc_number" =>  ['required','string','max:10','min:1'],
            "dc_apc_number" =>  ['required','string','max:10','min:1'],
            "dc_phone_code" =>  ['required','string'],
            "dc_phone_number" =>  ['required','integer','digits_between:8,12'],
            "dc_landline_code" =>  ['nullable','string'],
            "dc_landline_number" =>  ['nullable','integer','digits_between:7,10'],
            "dc_signature" =>  ['nullable','file','mimes:jpeg,jpg,png', 'max:10000'],
            "is_admin_in_charge" => ['boolean'],


            "ac_name" => [$requiredIfDocInChargeFalse,'string','max:50'],
            "ac_nric" => ['nullable', 'string', 'max:50'],
            "ac_phone_code" => [$requiredIfDocInChargeFalse,'string'],
            "ac_phone_number" => [$requiredIfDocInChargeFalse,'integer','digits_between:8,12'],
            "ac_landline_code" => ['nullable','string'],
            "ac_landline_number" => ['nullable','integer','digits_between:7,10'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'dc_name.required' => __('validation.person_in_charge.dc_name_required'),
            'dc_nric.required' => __('validation.person_in_charge.dc_nric_required'),
            'dc_mmc_number.required' => __('validation.person_in_charge.dc_mmc_number_required'),
            'dc_mmc_number.integer' => __('validation.person_in_charge.dc_mmc_number_integer'),
            'dc_apc_number.required' => __('validation.person_in_charge.dc_apc_number_required'),
            'dc_phone_code.required' => __('validation.person_in_charge.dc_phone_code_required'),
            'dc_phone_number.required' => __('validation.person_in_charge.dc_phone_number_required'),
            'dc_phone_number.integer' => __('validation.person_in_charge.dc_phone_number_integer'),
            'dc_landline_number.integer' => __('validation.person_in_charge.dc_landline_number_integer'),
            'dc_signature.file' => __('validation.person_in_charge.dc_signature_file'),
            'dc_signature.mimes' => __('validation.person_in_charge.dc_signature_mimes'),
            'dc_signature.max' => __('validation.person_in_charge.dc_signature_max'),

            'ac_name.required_if' => __('validation.person_in_charge.ac_name_required_if'),
            'ac_nric.required_if' => __('validation.person_in_charge.ac_nric_required_if'),
            'ac_phone_code.required_if' => __('validation.person_in_charge.ac_phone_code_required_if'),
            'ac_phone_number.required_if' => __('validation.person_in_charge.ac_phone_number_required_if'),
            'ac_phone_number.integer' => __('validation.person_in_charge.ac_phone_number_integer'),
            'ac_landline_number.integer' => __('validation.person_in_charge.ac_landline_number_integer'),
        ];
    }
}