<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class LoginOtpVerifyRequest extends ValidationRequests
{

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => 'required|email|exists:users,email',
            'otp' => 'required|exists:otp_verifications,otp',
        ];
    }

    /**
     * Get custom validation messages for the request.
     */
    public function messages(): array
    {
        return [
            'email.required' => __('validation.otp.email_required'),
            'email.email' => __('validation.otp.email_invalid'),
            'email.exists' => __('validation.otp.email_not_found'),
            'otp.required' => __('validation.otp.otp_required'),
            'otp.exists' => __('validation.otp.otp_invalid'),
        ];
    }
}
