<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\ActiveUserEmail;

class ForgotPasswordRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
     public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => ['required', 'email', new ActiveUserEmail],
        ];
    }

    /**
     * Get custom validation messages for the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'email.required' => __('validation.forgot_password.email_required'),
            'email.email' => __('validation.forgot_password.email_invalid'),
            'email.exists' => __('validation.forgot_password.email_not_found'),
        ];
    }
}
