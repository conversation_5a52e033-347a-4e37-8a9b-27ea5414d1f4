<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;
use App\Rules\CustomPostalCodeValidation;
use App\Rules\RequiredIfBusinessTypeNotOne;


class OnboardingRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $step = $this->input('completed_step');
        $requiredIfDocInChargeFalse = 'required_if:is_admin_in_charge,false';

        $rules = [
            'completed_step' => ['required', Rule::in(["1", "2", "3", "4", "5"])],
            'from_review_page' => ['nullable','boolean']
        ];

        if ($step == "1") {
            $rules['business_type_id'] =  ['required',new EncryptedExists('business_types')];
            /* $rules['clinic_name'] = ['required','string', 'max:150'];  */
            $rules['clinic_number'] = ['required','string','max:20','min:2'];
            $rules['mobile_code'] = ['nullable','string'];
            $rules['mobile_number'] = ['nullable','integer','digits_between:8,12'];
            $rules['landline_code'] = ['nullable','string'];
            $rules['landline_number'] = ['nullable','integer','digits_between:7,10'];

            $rules['company_name'] = [new RequiredIfBusinessTypeNotOne($this->business_type_id) ? 'nullable' : 'required', 'string','max:64'];
            $rules['company_number'] = [new RequiredIfBusinessTypeNotOne($this->business_type_id)? 'nullable' : 'required' ,'string','max:20','min:2'];
            $rules['clinic_owner'] = ['nullable','string','max:50'];
            $rules['clinic_year'] = ['required'];
            $rules['tin_number'] =['required', 'string', 'max:20','min:1'];
            $rules['sst_number'] =['nullable', 'string', 'max:20','min:1'];
        } 

        if ($step == "2") {
            
            $rules['addresses'] = ['required','array'];
            $rules['addresses.*.id'] = ['nullable',new EncryptedExists('user_addresses')];
            $rules['addresses.*.address_1'] = ['required','string','max:100'];
            $rules['addresses.*.address_2'] = ['nullable','string','max:100'];
            $rules['addresses.*.nick_name'] = ['required','string','max:50'];
            $rules['addresses.*.postal_code'] = ['required','string'];
            $rules['addresses.*.country_id'] =  ['required',new EncryptedExists('countries')];
            $rules['addresses.*.state_id'] =  ['required',new EncryptedExists('states')];
            $rules['addresses.*.city_id'] =  ['required',new EncryptedExists('cities')];
            $rules['addresses.*.is_default'] =  ['required','boolean'];
            $rules['addresses.*.is_same_as_billing'] =  ['required','boolean'];
            
            $rules['b_address_1'] = ['required','string','max:100'];
            $rules['b_address_2'] = ['nullable','string','max:100'];
            $rules['b_postal_code'] = ['required','string',new CustomPostalCodeValidation($this->b_city_id)];
            $rules['b_country_id'] =  ['required',new EncryptedExists('countries')];
            $rules['b_state_id'] =  ['required',new EncryptedExists('states')];
            $rules['b_city_id'] =  ['required',new EncryptedExists('cities')];
           // $rules['b_nick_name'] = ['required','string','max:50'];

            $rules['remove_addresses'] =['nullable','array'];
        }

        if($step == '3') {
            $rules["dc_name"] = ['required','string','max:50'];
            $rules['dc_nric'] = ['nullable', 'string','max:50'];
            $rules["dc_mmc_number"] = ['required','string','max:10','min:1'];
            $rules["dc_apc_number"] = ['required','string','max:10','min:1'];
            $rules["dc_phone_code"] = ['required','string'];
            $rules["dc_phone_number"] = ['required','integer','digits_between:8,12'];
            $rules["dc_landline_code"] = ['nullable','string'];
            $rules["dc_landline_number"] = ['nullable','integer','digits_between:7,10'];
            $rules["dc_signature"] = ['nullable','file','mimes:jpeg,jpg,png', 'max:10000'];
            $rules["is_admin_in_charge"] =['boolean'];


            $rules["ac_name"] =[$requiredIfDocInChargeFalse,'string','max:50'];
            $rules["ac_nric"] = ['nullable', 'string', 'max:50'];
            $rules["ac_phone_code"] =[$requiredIfDocInChargeFalse,'string'];
            $rules["ac_phone_number"] =[$requiredIfDocInChargeFalse,'integer','digits_between:8,12'];
            $rules["ac_landline_code"] =['nullable','string'];
            $rules["ac_landline_number"] =['nullable','integer','digits_between:7,10'];
            $rules["apc_certificate_expired_date"] = ['nullable', 'integer', 'min:' . (now()->year)];

        }

        if($step == "4") {
            $rules['clinic_account_type_id'] = ['required',new EncryptedExists('clinic_account_types')];
            $rules["borang_certificate"] = ['nullable', 'array'];
            $rules["mmc_certificate"] = ['nullable', 'array'];
            $rules["apc_certificate"] = ['nullable', 'array'];
            $rules["arc_certificate"] = ['nullable', 'array'];
            $rules["poison_license"] = ['nullable', 'array'];
            $rules["other_relevant_documents"] = ['nullable', 'array'];
            $rules["removed_borang_certificate"] = ['nullable', 'array'];
            $rules["removed_mmc_certificate"] = ['nullable', 'array'];
            $rules["removed_apc_certificate"] = ['nullable', 'array'];
            $rules["removed_arc_certificate"] = ['nullable', 'array'];
            $rules["removed_poison_license"] = ['nullable', 'array'];
            $rules["removed_other_relevant_documents"] = ['nullable', 'array'];
        }
        if($step == "5") {
            $rules["is_declare_info"] =['required','boolean'];
            $rules["is_term"] =['required','boolean'];
        }
        return $rules;
    }

    public function messages()
    {
        return [
            'completed_step.required' => __('validation.onboarding.completed_step_required'),
            'completed_step.in' => __('validation.onboarding.completed_step_in'),
    
            // Step 1
            'business_type_id.required' => __('validation.onboarding.business_type_required'),
            'clinic_name.required' => __('validation.onboarding.clinic_name_required'),
            'clinic_number.required' => __('validation.onboarding.clinic_number_required'),
            'mobile_code.required' => __('validation.onboarding.mobile_code_required'),
            'mobile_number.required' => __('validation.onboarding.mobile_number_required'),
            'company_name.required' => __('validation.onboarding.company_name_required'),
            'clinic_year.required' => __('validation.onboarding.clinic_year_required'),
            'tin_number.required' => __('validation.onboarding.tin_number_required'),
    
            // Step 2
            'addresses.required' => __('validation.onboarding.addresses_required'),
            'addresses.*.address_1.required' => __('validation.onboarding.address_1_required'),
            'addresses.*.address_2.required' => __('validation.onboarding.address_2_required'),
            'addresses.*.nick_name.required' => __('validation.onboarding.nick_name_required'),
            'addresses.*.postal_code.required' => __('validation.onboarding.postal_code_required'),
            
            // Step 3
            'dc_name.required' => __('validation.onboarding.dc_name_required'),
            'dc_nric.required' => __('validation.onboarding.dc_nric_required'),
            'dc_mmc_number.required' => __('validation.onboarding.dc_mmc_number_required'),
            'dc_signature.mimes' => __('validation.onboarding.dc_signature_mimes'),
    
            // Step 4
            'clinic_account_type_id.required' => __('validation.onboarding.clinic_account_type_required'),
            'mmc_certificate.mimes' => __('validation.onboarding.mmc_certificate_mimes'),
    
            // Step 5
            'is_declare_info.required' => __('validation.onboarding.is_declare_info_required'),
            'is_term.required' => __('validation.onboarding.is_term_required'),
        ];
    }
}
