<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;

class AddToCartRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "supplier_id" => ["required",new EncryptedExists('users')],
            "product_id" => ["required", new EncryptedExists('products')],
            "quantity" => ["required", "integer", "min:1"],
            "tier_number" => ["nullable"]
        ];
    }
    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'supplier_id.required' => __('validation.add_to_cart.supplier_id_required'),
            'product_id.required' => __('validation.add_to_cart.product_id_required'),
            'quantity.required' => __('validation.add_to_cart.quantity_required'),
            'quantity.integer' => __('validation.add_to_cart.quantity_integer'),
            'quantity.min' => __('validation.add_to_cart.quantity_min'),
        ];
        
    }
}