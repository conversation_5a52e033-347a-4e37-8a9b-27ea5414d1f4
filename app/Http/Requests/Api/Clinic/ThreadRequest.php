<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Support\Facades\DB;


class ThreadRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "receiver_id" =>  ["nullable", new EncryptedExists('users')],
            "order_id" => ["required",  new EncryptedExists('orders')],
        ];
    }
    /**
     * Get custom validation messages.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            "order_id.required" => __('validation.thread.order_id_required'),
            "receiver_id.exists" => __('validation.thread.receiver_id_invalid'),
        ];
    }
}