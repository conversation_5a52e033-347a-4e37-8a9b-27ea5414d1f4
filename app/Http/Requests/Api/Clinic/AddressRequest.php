<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CustomPostalCodeValidation;

class AddressRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'addresses' => ['required','array'],
            'addresses.*.id' => ['nullable',new EncryptedExists('user_addresses')],
            'addresses.*.address_1' => ['required','string','max:100'],
            'addresses.*.address_2' => ['nullable','string','max:100'],
            'addresses.*.nick_name' => ['required','string','max:100'],
            'addresses.*.postal_code' => ['required','string'],
            'addresses.*.country_id' =>  ['required',new EncryptedExists('countries')],
            'addresses.*.state_id' =>  ['required',new EncryptedExists('states')],
            'addresses.*.city_id' =>  ['required',new EncryptedExists('cities')],
            'addresses.*.is_default' =>  ['boolean','required'],
            
            'is_billing_address_same' => ['boolean'],
            'index' => ['nullable'],
            
            'b_address_1' => ['required','string','max:100'],
            'b_address_2' => ['nullable','string','max:100'],
            //'b_nick_name' => ['required','string','max:100'],
            'b_postal_code' => ['required','string',new CustomPostalCodeValidation($this->b_city_id)],
            'b_country_id' =>  ['required',new EncryptedExists('countries')],
            'b_state_id' =>  ['required',new EncryptedExists('states')],
            'b_city_id' =>  ['required',new EncryptedExists('cities')],

            'remove_addresses' =>['nullable','array'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'addresses.required' => __('validation.address.addresses_required'),
            'addresses.array' => __('validation.address.addresses_array'),
            'addresses.*.id.exists' => __('validation.address.addresses_id_exists'),
            'addresses.*.address_1.required' => __('validation.address.addresses_address_1_required'),
            'addresses.*.address_1.string' => __('validation.address.addresses_address_1_string'),
            'addresses.*.address_2.string' => __('validation.address.addresses_address_2_string'),
            'addresses.*.postal_code.required' => __('validation.address.addresses_postal_code_required'),
            'addresses.*.country_id.required' => __('validation.address.addresses_country_id_required'),
            'addresses.*.state_id.required' => __('validation.address.addresses_state_id_required'),
            'addresses.*.nick_name.required' => __('validation.onboarding.nick_name_required'),
            'addresses.*.city_id.required' => __('validation.address.addresses_city_id_required'),
            'addresses.*.is_default.required' => __('validation.address.addresses_is_default_required'),
            'addresses.*.is_default.boolean' => __('validation.address.addresses_is_default_boolean'),

            'is_billing_address_same.boolean' => __('validation.address.is_billing_address_same_boolean'),

            'b_address_1.required' => __('validation.address.b_address_1_required'),
            'b_address_1.string' => __('validation.address.b_address_1_string'),
            'b_address_2.required' => __('validation.address.b_address_2_required'),
            'b_address_2.string' => __('validation.address.b_address_2_string'),
            'b_postal_code.required' => __('validation.address.b_postal_code_required'),
            'b_country_id.required' => __('validation.address.b_country_id_required'),
            'b_state_id.required' => __('validation.address.b_state_id_required'),
            'b_city_id.required' => __('validation.address.b_city_id_required'),

            'remove_addresses.array' => __('validation.address.remove_addresses_array'),
        ];
    }
}