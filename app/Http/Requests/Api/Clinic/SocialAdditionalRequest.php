<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;


class SocialAdditionalRequest extends ValidationRequests {

    protected const REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';
    protected const MAX = 'max:100';
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            'account_type_id' => ['required', 'string'],
            'name' => ['required', 'string', self::MAX, self::REGEX,
            Rule::unique('clinic_details', 'clinic_name')->ignore(auth()->id(), 'user_id')],
            "referral_code" =>['nullable','exists:clinic_details,referral_code'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array {
        return [
            'account_type_id.required' => __('validation.social_additional.account_type_id_required'),
            'account_type_id.string' => __('validation.social_additional.account_type_id_string'),
            'referral_code.string' => __('validation.social_additional.referral_code_string'),
            'referral_code.exists' => __('validation.social_additional.code_not_found'),
            'name.unique' => __('validation.otp.facility_name_unique'),
        ];
    }

}
