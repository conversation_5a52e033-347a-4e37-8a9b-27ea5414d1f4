<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;

class SupplierRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'pc_id' =>  ['required',new EncryptedExists('users')],
            'account_number' => ['nullable', 'string','min:1','max:20'],
        ];
    }

    public function messages()
    {
        return [
            'pc_id.required' => __('validation.supplier.pc_id_required'),
            'pc_id.exists' => __('validation.supplier.pc_id_invalid'),
            'account_number.string' => __('validation.supplier.account_string'),
            'account_number.min' => __('validation.supplier.account_min'),
            'account_number.max' => __('validation.supplier.account_max'),
        ];
    }
}
