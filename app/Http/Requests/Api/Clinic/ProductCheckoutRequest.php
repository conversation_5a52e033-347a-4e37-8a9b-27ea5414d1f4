<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;

class ProductCheckoutRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'applied_points' => 'nullable|integer',
            'shipping_address_id' => ['required',new EncryptedExists('user_addresses')],
            'suppliers' => 'required|array',
            'suppliers.*.id' => 'required|string', // Example validation rules
            'suppliers.*.payment_type' => 'required|in:pay_now,pay_later,credit_line',
        ];
    }

    public function messages()
    {
        return [
            'applied_points.integer' => __('validation.product_checkout.applied_points_integer'),
            'shipping_address_id.required' => __('validation.product_checkout.shipping_address_required'),
            'suppliers.required' => __('validation.product_checkout.suppliers_required'),
            'suppliers.array' => __('validation.product_checkout.suppliers_array'),
            'suppliers.*.id.required' => __('validation.product_checkout.supplier_id_required'),
            'suppliers.*.id.string' => __('validation.product_checkout.supplier_id_string'),
            'suppliers.*.payment_type.required' => __('validation.product_checkout.payment_type_required'),
            'suppliers.*.payment_type.in' => __('validation.product_checkout.payment_type_invalid'),
        ];
    }
}
