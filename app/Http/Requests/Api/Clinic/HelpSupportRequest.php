<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Support\Facades\DB;


class HelpSupportRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            "category_id" =>  ["required", new EncryptedExists('support_categories')],
            "receiver_id" =>  ["nullable", new EncryptedExists('users')],
            "order_id" => ["required",  new EncryptedExists('orders')],
            "subject" => ["required"],
            "description" => ["required"]
        ];
    }

    /**
     * Get custom validation messages for the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'category_id.required' => __('validation.help_support.category_required'),
            'order_id.required' => __('validation.help_support.order_required'),
            'subject.required' => __('validation.help_support.subject_required'),
            'description.required' => __('validation.help_support.description_required'),
        ];
    }
}