<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;

class FavouriteRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'type' => ['required','in:supplier,product'],
            'supplier_id' => ['required_if:type,supplier',new EncryptedExists('users')],
            'product_id' => ['required_if:type,product',new EncryptedExists('products')],
        ];
    }

    /**
     * Get custom validation messages for the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'type.required' => __('validation.favourite.type_required'),
            'type.in' => __('validation.favourite.type_invalid'),

            'supplier_id.required_if' => __('validation.favourite.supplier_id_required_if'),
            'product_id.required_if' => __('validation.favourite.product_id_required_if'),
        ];
    }
}
