<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class ResetPasswordRequest extends ValidationRequests
{

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'token' => 'required|string',
            'email' => 'required|string|email:rfc,dns|exists:users,email',
            'password' => [
                'required',
                'string',
                'confirmed',
                'min:6',
                'max:16',
                'regex:/[a-z]/', // must contain at least one lowercase letter
                'regex:/[A-Z]/', // must contain at least one uppercase letter
                'regex:/[0-9]/', // must contain at least one digit
                'regex:/[@$!%*#?&]/',
            ],
            'password_confirmation' => 'required|same:password',
        ];
    }

    public function messages(): array
    {
        return [
            'token.required' => __('validation.common.token_required'),
            'token.string' => __('validation.common.token_string'),
            'email.required' => __('validation.otp.email_required'),
            'email.string' => __('validation.otp.email_string'),
            'email.email' => __('validation.otp.email_invalid'),
            'email.exists' => __('validation.forgot_password.email_not_found'),
            'password.required' => __('validation.change_pass.password_required'),
            'password.min' => __('validation.change_pass.password_min'),
            'password.max' => __('validation.change_pass.password_max'),
            'password.regex' => __('validation.change_pass.password_regex'),
            'password_confirmation.required' => __('validation.change_pass.password_confirmation_required'),
            'password_confirmation.same' => __('validation.change_pass.password_confirmation_same'),
        ];
    }
}
