<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class ResentOtpRequest extends ValidationRequests
{

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => 'required|string|email:rfc,dns'
        ];
    }

    public function messages(): array
    {
        return [
            'email.required' => __('validation.otp.email_required'),
            'email.string' => __('validation.otp.email_string'),
            'email.email' => __('validation.otp.email_invalid'),
        ];
    }
}
