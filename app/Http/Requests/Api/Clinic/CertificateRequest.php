<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;

class CertificateRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'apc_certificate' => ['required','file','mimes:jpeg,jpg,png', 'max:10000'],
            'apc_certificate_expired_date' => ['required', 'digits:4', 'integer', 'min:' . (now()->year)],
        ];
    }

    public function messages()
    {
        return [
            'apc_certificate.required' => __('validation.certificate.apc_certificate_required'),
            'apc_certificate.file' => __('validation.certificate.apc_certificate_file'),
            'apc_certificate.mimes' => __('validation.certificate.apc_certificate_mimes'),
            'apc_certificate.max' => __('validation.certificate.apc_certificate_max'),
    
            'apc_certificate_expired_date.required' => __('validation.certificate.expired_date_required'),
            'apc_certificate_expired_date.digits' => __('validation.certificate.expired_date_digits'),
            'apc_certificate_expired_date.integer' => __('validation.certificate.expired_date_integer'),
            'apc_certificate_expired_date.min' => __('validation.certificate.expired_date_min'),
        ];
        
    }
}
