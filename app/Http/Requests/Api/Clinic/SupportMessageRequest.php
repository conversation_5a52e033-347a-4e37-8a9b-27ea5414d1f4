<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;

class SupportMessageRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'support_ticket_id' =>  ['required',new EncryptedExists('support_tickets')],
            'text' => ['nullable', 'string','max:16000'],
            'files' => ['nullable', 'array','max:5'],
            'files.*' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:2048']
        ];
    }

    public function messages()
    {
        return [
            'support_ticket_id.required' => __('validation.support_message.ticket_required'),
            'support_ticket_id.exists' => __('validation.support_message.ticket_invalid'),
            'text.string' => __('validation.support_message.text_string'),
            'text.max' => __('validation.support_message.text_max'),
            'files.array' => __('validation.support_message.files_array'),
            'files.max' => __('validation.support_message.files_max'),
            'files.*.file' => __('validation.support_message.file_invalid'),
            'files.*.mimes' => __('validation.support_message.file_mimes'),
            'files.*.max' => __('validation.support_message.file_max_size'),
        ];
    }
}
