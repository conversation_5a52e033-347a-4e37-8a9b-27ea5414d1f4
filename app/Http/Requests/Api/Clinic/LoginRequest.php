<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\ActiveUserEmail;


class LoginRequest extends ValidationRequests
{

    public function prepareForValidation(): void
{
    if ($this->has('email')) {
        $this->merge([
            'email' => strtolower($this->input('email')),
        ]);
    }
}
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'email' => ['required', 'email', new ActiveUserEmail],
            'password' => 'required|string',
            'stay_login' => 'required|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'email.required' => __('validation.login.email_required'),
            'email.email' => __('validation.login.email_invalid'),
            'email.exists' => __('validation.login.email_not_found'),
            'password.required' => __('validation.login.password_required'),
            'password.string' => __('validation.login.password_string'),
            'stay_login.required' => __('validation.login.stay_login_required'),
            'stay_login.boolean' => __('validation.login.stay_login_boolean'),
        ];

    }
}
