<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;

class ThreadMessageRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'thread_id' =>  ['required',new EncryptedExists('threads')],
            'text' => ['nullable', 'string','max:16000'],
            'files' => ['nullable', 'array','max:5'],
            'files.*' => ['file', 'mimes:jpg,jpeg,png,pdf', 'max:2048']
        ];
    }

    public function messages()
    {
        return [
            'thread_id.required' => __('validation.thread_message.thread_id_required'),
            'thread_id.exists' => __('validation.thread_message.thread_id_invalid'),
            'text.string' => __('validation.thread_message.text_string'),
            'text.max' => __('validation.thread_message.text_max'),
            'files.array' => __('validation.thread_message.files_array'),
            'files.max' => __('validation.thread_message.files_max'),
            'files.*.file' => __('validation.thread_message.files_file'),
            'files.*.mimes' => __('validation.thread_message.files_mimes'),
            'files.*.max' => __('validation.thread_message.files_max_size'),
        ];
    }
}
