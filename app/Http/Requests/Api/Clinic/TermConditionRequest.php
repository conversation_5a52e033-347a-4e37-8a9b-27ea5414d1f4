<?php

namespace App\Http\Requests\Api\Clinic;

use App\Rules\EncryptedExists;
use App\Http\Requests\Api\ValidationRequests;

class TermConditionRequest extends ValidationRequests
{
    protected const REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';

    protected const MAX50 = 'max:50';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'required|email|exists:users,email',
            'terms_id' => ['required', new EncryptedExists('term_conditions')],
            'password' => 'required|string',
            'stay_login' => 'required|boolean',

        ];
    }

    public function messages()
    {
        return [
            'email.required' => __('validation.term_condition.email_required'),
            'email.email' => __('validation.term_condition.email_invalid'),
            'email.exists' => __('validation.term_condition.email_not_found'),
            'terms_id.required' => __('validation.term_condition.terms_id_required'),
            'terms_id.exists' => __('validation.term_condition.terms_id_invalid'),
            'password.required' => __('validation.term_condition.password_required'),
            'password.string' => __('validation.term_condition.password_string'),
            'stay_login.required' => __('validation.term_condition.stay_login_required'),
            'stay_login.boolean' => __('validation.term_condition.stay_login_boolean'),
        ];
    }
}
