<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class OtpRequest extends ValidationRequests
{
    protected const REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';

    protected const MAX = 'max:100';
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', self::MAX, self::REGEX,'unique:clinic_details,clinic_name'],
            'password' => ['required', 'min:8', 'max:16', 'regex:/^\S*$/u', 'regex:/^(?=.*?[0-9]).{6,}$/'],
            'password_confirmation' => ['required', 'max:16','same:password'],
            'email' => ['required', 'string', 'email:rfc,dns','unique:users,email'] 
        ];
    }

    public function messages()
    {
        return [
            'email.required' => __('validation.otp.email_required'),
            'email.string' => __('validation.otp.email_string'),
            'email.email' => __('validation.otp.email_invalid'),
            'name.unique' => __('validation.otp.facility_name_unique'),
            'email.unique' => __('validation.otp.email_unique'),
            'name.required' => __('validation.common.name_required'),
            'name.string' => __('validation.common.name_string'),
            'name.max' => __('validation.common.name_max'),
            'name.regex' => __('validation.common.name_regex'),
            'password.required' => __('validation.change_pass.password_required'),
            'password.min' => __('validation.change_pass.password_min'),
            'password.max' => __('validation.change_pass.password_max'),
            'password.regex' => __('validation.change_pass.password_regex'),
            'password_confirmation.required' => __('validation.change_pass.password_confirmation_required'),
            'password_confirmation.same' => __('validation.change_pass.password_confirmation_same'),
        ];
    }
}
