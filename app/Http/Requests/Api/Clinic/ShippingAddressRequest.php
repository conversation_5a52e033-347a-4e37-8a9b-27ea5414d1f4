<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Validation\Rule;
use App\Rules\CustomPostalCodeValidation;

class ShippingAddressRequest extends ValidationRequests
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
            return [
                'address_1' => ['required','string'],
                'address_2' => ['nullable','string'],
                'postal_code' => ['required','string',new CustomPostalCodeValidation($this->city_id)],
                'is_requested' => ['nullable','boolean'],
                'nick_name' => ['required','string','max:50'],
                'country_id' =>  ['required',new EncryptedExists('countries')],
                'state_id' =>  ['required',new EncryptedExists('states')],
                'city_id' =>  ['required',new EncryptedExists('cities')],
            ];

    }

    public function messages()
    {
        return [
            'address_1.required' => __('validation.shipping_address.address_1_required'),
            'address_1.string' => __('validation.shipping_address.address_1_string'),
            'address_2.string' => __('validation.shipping_address.address_2_string'),
            'postal_code.required' => __('validation.shipping_address.postal_code_required'),
            'is_requested.boolean' => __('validation.shipping_address.is_requested_boolean'),
            'country_id.required' => __('validation.shipping_address.country_id_required'),
            'state_id.required' => __('validation.shipping_address.state_id_required'),
            'city_id.required' => __('validation.shipping_address.city_id_required'),
            'nick_name.required' => __('validation.onboarding.nick_name_required'),
        ];
    }
}
