<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;

class AddToCartUpdateRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'products' => 'required|array',
            'products.*.id' => 'required|string',
            'products.*.product_qty' => 'required|integer|min:1',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'products.required' => __('validation.add_to_cart_update.products_required'),
            'products.array' => __('validation.add_to_cart_update.products_array'),
            'products.*.id.required' => __('validation.add_to_cart_update.product_id_required'),
            'products.*.id.string' => __('validation.add_to_cart_update.product_id_string'),
            'products.*.product_qty.required' => __('validation.add_to_cart_update.product_qty_required'),
            'products.*.product_qty.integer' => __('validation.add_to_cart_update.product_qty_integer'),
            'products.*.product_qty.min' => __('validation.add_to_cart_update.product_qty_min'),
        ];
        
        
    }
}