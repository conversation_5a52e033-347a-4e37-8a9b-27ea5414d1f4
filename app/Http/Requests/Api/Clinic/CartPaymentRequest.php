<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;

class CartPaymentRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'suppliers' => 'required|array',
            'suppliers.*.id' => 'required|string', // Example validation rules
            'suppliers.*.payment_type' => 'required|in:pay_now,pay_later,credit_line',
            'suppliers.*.shipping_detail' => 'nullable',
            'suppliers.*.delivery_type' => 'nullable',
            'applied_points' => 'nullable|integer',
            'is_submission' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'suppliers.required' => __('validation.cart_payment.suppliers_required'),
            'suppliers.array' => __('validation.cart_payment.suppliers_array'),
            'suppliers.*.id.required' => __('validation.cart_payment.supplier_id_required'),
            'suppliers.*.id.string' => __('validation.cart_payment.supplier_id_string'),
            'suppliers.*.payment_type.required' => __('validation.cart_payment.payment_type_required'),
            'suppliers.*.payment_type.in' => __('validation.cart_payment.payment_type_invalid'),
            'applied_points.integer' => __('validation.cart_payment.applied_points_integer'),
        ];
    }
}