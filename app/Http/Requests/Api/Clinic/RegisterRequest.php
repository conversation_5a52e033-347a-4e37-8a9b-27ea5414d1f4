<?php

namespace App\Http\Requests\Api\Clinic;

use App\Rules\EncryptedExists;
use App\Http\Requests\Api\ValidationRequests;

class RegisterRequest extends ValidationRequests
{
    protected const REGEX = 'regex:/^[^\s]+(\s*[^\s]+)*$/';

    protected const MAX = 'max:100';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => ['required', 'string', self::MAX, self::REGEX],
            'email' => ['required', 'string', 'email:rfc,dns','unique:users,email'],
            'password' => ['required', 'min:8', 'max:16', 'regex:/^\S*$/u', 'regex:/^(?=.*?[0-9]).{6,}$/'],
            'password_confirmation' => ['required', 'max:16','same:password'],
            "otp" => ['required'],
        ];
    }

    public function messages()
    {
        return [
            'name.required' => __('validation.common.name_required'),
            'name.string' => __('validation.common.name_string'),
            'name.max' => __('validation.common.name_max'),
            'name.regex' => __('validation.common.name_regex'),
            'email.required' => __('validation.otp.email_required'),
            'email.string' => __('validation.otp.email_string'),
            'email.email' => __('validation.otp.email_invalid'),
            'email.exists' => __('validation.forgot_password.email_not_found'),
            'email.unique' => __('validation.otp.email_unique'),
            'password.required' => __('validation.change_pass.password_required'),
            'password.min' => __('validation.change_pass.password_min'),
            'password.max' => __('validation.change_pass.password_max'),
            'password.regex' => __('validation.change_pass.password_regex'),
            'password_confirmation.required' => __('validation.change_pass.password_confirmation_required'),
            'password_confirmation.same' => __('validation.change_pass.password_confirmation_same'),
            'otp.required' => __('validation.otp.otp_required'),
        ];
    }
}
