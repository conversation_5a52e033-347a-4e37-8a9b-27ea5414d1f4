<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;
use App\Rules\RequiredIfBusinessTypeNotOne;


class BasicInfoRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'business_type_id' =>  ['required',new EncryptedExists('business_types')],
           /*  'clinic_name' => ['required','string', 'max:150'], */
            'clinic_number' => ['required','string','max:20','min:2'],
            'mobile_code' => ['nullable','string'],
            'mobile_number' => ['nullable','integer','digits_between:8,12'],
            'landline_code' => ['required','string'],
            'landline_number' => ['nullable','integer','digits_between:7,10'],
            'company_name' => [new RequiredIfBusinessTypeNotOne($this->business_type_id) ? 'nullable' : 'required','string','max:64'],
            'company_number' => [new RequiredIfBusinessTypeNotOne($this->business_type_id) ? 'nullable' : 'required','string','max:20','min:2'],
            'clinic_owner' => ['nullable','string','max:50'],
            'clinic_year' => ['required'],
            'tin_number' => ['required', 'string', 'max:20','min:1'],
            'sst_number' => ['nullable', 'string', 'max:20','min:1'],
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'business_type_id.required' => __('validation.basic_info.business_type_required'),
            'clinic_name.required' => __('validation.basic_info.clinic_name_required'),
            'clinic_name.string' => __('validation.basic_info.clinic_name_string'),
            'clinic_number.required' => __('validation.basic_info.clinic_number_required'),
            'clinic_number.integer' => __('validation.basic_info.clinic_number_integer'),
            'mobile_code.required' => __('validation.basic_info.mobile_code_required'),
            'mobile_code.string' => __('validation.basic_info.mobile_code_string'),
            'mobile_number.required' => __('validation.basic_info.mobile_number_required'),
            'mobile_number.integer' => __('validation.basic_info.mobile_number_integer'),
            'landline_code.required' => __('validation.basic_info.landline_code_required'),
            'landline_code.string' => __('validation.basic_info.landline_code_string'),
            'landline_number.required' => __('validation.basic_info.landline_number_required'),
            'landline_number.integer' => __('validation.basic_info.landline_number_integer'),
            'company_name.required' => __('validation.basic_info.company_name_required'),
            'company_number.required' => __('validation.basic_info.company_number_required'),
            'company_number.integer' => __('validation.basic_info.company_number_integer'),
            'clinic_owner.required' => __('validation.basic_info.clinic_owner_required'),
            'clinic_year.required' => __('validation.basic_info.clinic_year_required'),
            'tin_number.required' => __('validation.basic_info.tin_number_required'),
            'tin_number.max' => __('validation.basic_info.tin_number_max'),
            'sst_number.max' => __('validation.basic_info.sst_number_max'),
        ];
        
    }
}