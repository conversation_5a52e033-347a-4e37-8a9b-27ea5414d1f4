<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;

class paymentHandlerRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'transaction_number' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'session_id.required' => __('validation.payment.session_id_required'),
        ];

    }
}
