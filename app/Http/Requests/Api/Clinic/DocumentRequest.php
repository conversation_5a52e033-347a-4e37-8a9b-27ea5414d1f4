<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;
use App\Rules\EncryptedExists;
use Illuminate\Foundation\Http\FormRequest;

class DocumentRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'clinic_account_type_id' => ['required',new EncryptedExists('clinic_account_types')],
            "borang_certificate" => ['nullable', 'array'],  
            "mmc_certificate" => ['nullable', 'array'],
            "apc_certificate" => ['nullable', 'array'],
            "arc_certificate" => ['nullable', 'array'],
            "poison_license" => ['nullable', 'array'],
            "other_relevant_documents" => ['nullable', 'array'],
            'removed_borang_certificate' => ['nullable', 'array'],
            'removed_mmc_certificate' => ['nullable', 'array'],
            'removed_apc_certificate' => ['nullable', 'array'],
            'removed_arc_certificate' => ['nullable', 'array'],
            'removed_poison_license' => ['nullable', 'array'],
            'removed_other_relevant_documents' => ['nullable', 'array'],
        ];
    }

    public function messages()
    {
        return [
            'clinic_account_type_id.required' => __('validation.clinic.account_type_required'),

            'mmc_certificate.file' => __('validation.certificate.invalid_file_format'),
            'mmc_certificate.mimes' => __('validation.certificate.allowed_formats'),
            'mmc_certificate.max' => __('validation.certificate.file_size_limit'),

            'apc_certificate.file' => __('validation.certificate.invalid_file_format'),
            'apc_certificate.mimes' => __('validation.certificate.allowed_formats'),
            'apc_certificate.max' => __('validation.certificate.file_size_limit'),

            'arc_certificate.file' => __('validation.certificate.invalid_file_format'),
            'arc_certificate.mimes' => __('validation.certificate.allowed_formats'),
            'arc_certificate.max' => __('validation.certificate.file_size_limit'),

            'poison_license.file' => __('validation.certificate.invalid_file_format'),
            'poison_license.mimes' => __('validation.certificate.allowed_formats'),
            'poison_license.max' => __('validation.certificate.file_size_limit'),
        ];
    }
}