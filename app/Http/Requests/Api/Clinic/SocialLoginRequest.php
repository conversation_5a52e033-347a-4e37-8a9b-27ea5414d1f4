<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class SocialLoginRequest extends ValidationRequests {

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            'id_token' => ['required', 'string'],
            "social_type" =>['required','in:google,apple'],
            "first_name" =>['nullable'],
            "last_name" =>['nullable'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array {
        return [
            'id_token.required' => __('validation.social_login.id_token_required'),
            'id_token.string' => __('validation.social_login.id_token_string'),
            'social_type.required' => __('validation.social_login.social_type_required'),
            'social_type.in' => __('validation.social_login.social_type_invalid'),
            'first_name.string' => __('validation.social_login.first_name_string'),
            'first_name.max' => __('validation.social_login.first_name_max'),
            'last_name.string' => __('validation.social_login.last_name_string'),
            'last_name.max' => __('validation.social_login.last_name_max'),
        ];
    }

}
