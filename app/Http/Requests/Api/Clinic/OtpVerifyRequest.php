<?php

namespace App\Http\Requests\Api\Clinic;

use App\Http\Requests\Api\ValidationRequests;

class OtpVerifyRequest extends ValidationRequests
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
public function prepareForValidation(): void
    {
        if ($this->has('email')) {
            $this->merge([
                'email' => strtolower($this->input('email')),
            ]);
        }
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => ['required', 'string', 'email:rfc,dns','exists:users,email'],
            'otp' => ['required']
        ];
    }

    public function messages()
    {
        return [
            'email.required' => __('validation.otp.email_required'),
            'otp.required' => __('validation.otp.otp_required'),
            'email.string' => __('validation.otp.email_string'),
            'email.email' => __('validation.otp.email_invalid'),
            'email.unique' => __('validation.otp.email_unique'),
        ];
    }
}
