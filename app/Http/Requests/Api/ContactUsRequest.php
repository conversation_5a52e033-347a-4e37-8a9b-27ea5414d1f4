<?php

namespace App\Http\Requests\Api;

class ContactUsRequest extends ValidationRequests
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:50|regex:/^[\pL\s\-]+$/',
            'email' => 'required|string|email',
            'code' => 'nullable',
            'landline_number' => 'nullable|integer',
            'subject' => 'nullable|string',
            'description' => 'required|string|max:500',
        ];

    }

    public function messages(): array
{
    return [
        'name.required' => __('validation.contact.name_required'),
        'name.string' => __('validation.contact.name_string'),
        'name.max' => __('validation.contact.name_max'),
        'name.regex' => __('validation.contact.name_regex'),
        'email.required' => __('validation.contact.email_required'),
        'email.string' => __('validation.contact.email_string'),
        'email.email' => __('validation.contact.email_email'),
        'code.nullable' => __('validation.contact.code_nullable'),
        'landline_number.nullable' => __('validation.contact.landline_number_nullable'),
        'landline_number.integer' => __('validation.contact.landline_number_integer'),
        'subject.nullable' => __('validation.contact.subject_nullable'),
        'subject.string' => __('validation.contact.subject_string'),
        'description.required' => __('validation.contact.description_required'),
        'description.string' => __('validation.contact.description_string'),
        'description.max' => __('validation.contact.description_max'),
    ];
}
}
