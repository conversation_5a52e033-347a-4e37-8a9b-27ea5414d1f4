<?php

namespace App\Http\Requests\Api;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\ValidationException;

class ValidationRequests extends FormRequest
{
    protected function failedValidation(Validator $validator): ValidationException
    {
        $errors = $validator->errors()->getMessages();
        if (! empty($errors)) {
            foreach ($errors as $message) {
                $message[0];
                break;
            }
        }

        throw new HttpResponseException(response()->json([
            'success' => false,
            'message' => 'Validation errors',
            'data' => $validator->errors(),
        ], 422));
    }
}
