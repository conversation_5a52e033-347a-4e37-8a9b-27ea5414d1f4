<?php

namespace App\Http\Requests\Api;

class UserProfileRequest extends ValidationRequests
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100|regex:/^[\pL\s\-]+$/',
        ];

    }
    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'name.required' => __('validation.user_profile.name_required'),
            'name.string' => __('validation.user_profile.name_string'),
            'name.max' => __('validation.user_profile.name_max'),
            'name.regex' => __('validation.user_profile.name_regex'),
        ];
    }
}
