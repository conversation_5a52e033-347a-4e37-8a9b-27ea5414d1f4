<?php

namespace App\Http\Requests\Api;

class ChangePasswordRequest extends ValidationRequests
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'old_password' => 'required',
            'password' => [
                'required',
                'min:8',
                'max:16',
                'regex:/[a-z]/', // must contain at least one lowercase letter
                'regex:/[A-Z]/', // must contain at least one uppercase letter
                'regex:/[0-9]/', // must contain at least one digit
                'regex:/[@$!%*#?&]/',
            ],
            'password_confirmation' => 'required|same:password',
        ];
    }

    public function messages(): array
    {
        return [
            'old_password.required' => __('validation.change_pass.old_password_required'),
            'password.required' => __('validation.change_pass.password_required'),
            'password.min' => __('validation.change_pass.password_min'),
            'password.max' => __('validation.change_pass.password_max'),
            'password.regex' => __('validation.change_pass.password_regex'),
            'password_confirmation.required' => __('validation.change_pass.password_confirmation_required'),
            'password_confirmation.same' => __('validation.change_pass.password_confirmation_same'),
        ];
    }
}
