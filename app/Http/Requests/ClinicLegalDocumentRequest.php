<?php

namespace App\Http\Requests;

use App\Http\Requests\Api\ValidationRequests;
use DBTableNames;
use Illuminate\Support\Facades\Auth;

class ClinicLegalDocumentRequest extends ValidationRequests
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'borang_certificate' => 'required',
            'mmc_certificate' => 'required',
            'apc_certificate' => 'required',
            'apc_certificate_expired_date' => 'required',
            'is_declare_info' => 'required',
            'is_term' => 'required',
        ];

    }

}
