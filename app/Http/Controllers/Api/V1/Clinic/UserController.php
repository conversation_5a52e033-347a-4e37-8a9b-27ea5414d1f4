<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Requests\Api\Clinic\SocialAdditionalRequest;
use App\Http\Requests\Api\ChangePasswordRequest;
use App\Http\Requests\Api\UserProfileRequest;
use App\Http\Requests\Api\ContactUsRequest;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\V1\UserResource;
use App\Repositories\Api\UserRepository;
use Illuminate\Support\Facades\Mail;
use App\Http\Controllers\Controller;
use App\Mail\FacilityReferralInfoMail;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\JsonResponse;
use App\Models\ClinicDetail;
use App\Models\ClinicReferredCode;
use App\Models\UserTermCondition;
use App\Models\DpharmaPoint;
use App\Models\Inquiry;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Order;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Jobs\SuperAdminNotificationJob;
use App\Mail\FacilitySelfInquiryMail;

class UserController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    protected $model;

    protected $userRepository;

    public function __construct()
    {
        $this->model = new User;
        $this->userRepository = new UserRepository($this->model);
    }

    /**
     * @desc update current user profile
     *
     * @return type
     */
    public function updateProfile(UserProfileRequest $request): JsonResponse
    {
        try {
            $dataArr = $request->except('email');

            Auth::user()->update($dataArr);

            $response = response()->json([
                'success' => true,
                'message' => __('api.user.profile_update_success'),
            ], Response::HTTP_OK);

        Auth::user()->update($dataArr);

        $response  =  response()->json([
            'success'   => true,
            'message'   => __('api.user.profile_update_success'),
        ], Response::HTTP_OK);

        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    /**
     * @desc update clinic user additional information
     * @param Request $request
     * @return type
     */
    public function socialAdditonalInfo(SocialAdditionalRequest $request): JsonResponse
    {
        try
        {
        $data = $request->validated();
        $accountClinicId = decryptParam($data['account_type_id']);
        $userArray = [];
        $auth = Auth::user();
        $authId = $auth->id;
        $userArray['account_type_id'] = userAccountType('clinic');
        $userArray['name'] =  $data['name'];
        $auth->update($userArray);
        $referralCode = $data['referral_code'] ?? null;

        ClinicDetail::where('user_id', $authId)->update(['clinic_account_type_id' => $accountClinicId,'clinic_name'=> $data['name']]);
        
        if($referralCode){
            $clinicDetail = ClinicDetail::where('referral_code',$referralCode)->first();
            if($clinicDetail) {
                $mailReceiver = $clinicDetail->user;
                ClinicReferredCode::create([
                    'from_user_id' => $clinicDetail->user_id,
                    'to_user_id' => $authId,
                    'code' => $referralCode,
                    'points' => config('constants.api.referral_code.point'),
                    "order_value" => config('constants.api.referral_code.order_value')
                ]);
                Mail::to($mailReceiver->email)->send(new FacilityReferralInfoMail($auth,$mailReceiver->name));
            }
        }

        $response  = response()->json([
            'success'   => true,
            'message'   => __('api.auth.user_details'),
            'data' => [
                'user' =>(new UserResource(Auth::user())),
            ]
        ], Response::HTTP_OK);

        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    /**
     * @desc display user profile
     *
     * @return type
     */
    public function details(): JsonResponse
    {
        try {
            $user = Auth::user();

            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.user_details'),
                'data' => (new UserResource($user)),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }




    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $auth = Auth::user();
        if (! Hash::check($request->old_password, $auth->password)) {

            return response()->json([
                'success' => false,
                'message' => __('api.user.incorrect_password'),
            ], Response::HTTP_BAD_REQUEST);

        }
        $auth->update(['password' => bcrypt($request->password)]);
        $auth->tokens()->delete();
        return response()->json([
            'success' => true,
            'message' => __('api.user.pass_change_success'),
        ], Response::HTTP_OK);
    }

    public function logout(Request $request)
    {
        try {
            $request->user()->tokens()->delete();

            return response()->json([
                'success' => true,
                'message' => __('api.auth.logout_success'),
            ]);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function accountDetail(): JsonResponse
    {
        try {
            $user = Auth::user();
            $userID = $user->id;
            $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
            $currentDate = Carbon::now()->format('Y-m-d');

            $orderQuery = Order::where('user_id', $userID)
            ->whereBetween(DB::raw('DATE(created_at)'), [$startOfMonth, $currentDate]);

            // Clone the query to maintain the original state
            // Clone the original base query separately each time
            $totalOrder = (clone $orderQuery)->count();
            $inTransitOrder = (clone $orderQuery)->where('status', 'in_transit')->count();
            $receivedOrder = (clone $orderQuery)->where('status', 'delivered')->count();
            // $spendOrder = (clone $orderQuery)->whereNot('status', 'cancelled')->sum('amount');
             $spendOrder = Order::where('orders.user_id', $userID)
                ->whereBetween(DB::raw('DATE(orders.created_at)'), [$startOfMonth, $currentDate])
                ->where('orders.status', '!=', 'cancelled')
                ->leftJoin('sub_orders', 'orders.id', '=', 'sub_orders.order_id')
                ->selectRaw("
                    SUM(
                        CASE
                            WHEN sub_orders.payment_type = 'credit_line' THEN sub_orders.total_amount
                            WHEN sub_orders.payment_type != 'credit_line' AND orders.payment_status = 'paid' THEN sub_orders.total_amount
                            ELSE 0
                        END
                    ) as total_spend
                ")
                ->value('total_spend');

            $points  = DpharmaPoint::where('user_id', $userID)
            ->whereBetween(DB::raw('DATE(created_at)'), [$startOfMonth, $currentDate])
            ->selectRaw('SUM(points) as points_sum, SUM(redeem) as redeem_sum')
                ->first();
            $points = $points ? $points->points_sum - $points->redeem_sum : 0;
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.user_details'),
                'data' => [
                    'total_order' => $totalOrder,
                    'in_transit_order' => $inTransitOrder,
                    'received_order' => $receivedOrder,
                    'total_spend' => number_format($spendOrder,2) ?? 0,
                    'start_date' => $startOfMonth,
                    'end_date' => $currentDate,
                    'points' => $points && $points > 0 ? number_format($points , 2)  : 0,
                    'name' => $user->clinicData->clinic_name ?? null,
                    'photo' =>   getImage($user->photo, 'users'), 
                    'email' => $user->email ?? null,
                ],
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function updateTermCondition(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'terms_id' => 'required',
        ]);
    
        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        try {
            UserTermCondition::updateOrCreate([
                'user_id' => Auth::user()->id,
                'term_condition_id' => decryptParam($request->input('terms_id')) ?? null
            ]);
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.update_term_conditions'),
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function contactUs(ContactUsRequest $request): JsonResponse
    {
        try{
            $clinicData = null;
            if(Auth::guard('api')->user()) {
                $auth = Auth::guard('api')->user();
                $authId =$auth->id;
                $clinicData = $auth->clinicData;
            }
            $data = $request->validated();
            $inquiryRes = Inquiry::create([
                "user_id"=> $authId ?? null,
                "name" => $data['name'],
                "email" => $data['email'], 
                "code" => isset($clinicData->mobile_code) ? $clinicData->mobile_code : null,
                "landline_number" => isset($clinicData->mobile_number) ? $clinicData->mobile_number : null,
                "subject" => $data['subject'] ?? null,
                "type" => "b2b",
                "description"=> $data['description'],
            ]);
            $response = response()->json([
                'success' => true,
                'message' => __('api.user.contact_us'),
            ], Response::HTTP_CREATED);

            Mail::to($data['email'])->send(new FacilitySelfInquiryMail($inquiryRes));

            
            SuperAdminNotificationJob::dispatch('inquiry',$inquiryRes->id);   

        }catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }
}
