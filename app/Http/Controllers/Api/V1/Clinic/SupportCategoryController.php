<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\HelpSupportCategoryResource;
use App\Http\Resources\CustomCollection;
use App\Http\Controllers\Controller;
use App\Models\SupportCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SupportCategoryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    protected $helpSupportCategoryRepository;

    /**
     * AccountTypeController constructor.
     *
     * @param  SupportCategory  $model
     */
    public function __construct()
    {
        $this->model = new SupportCategory();
        // $this->helpSupportCategoryRepository = new ProductRepository;
    }

    public function index()
    {
        try {
            $data = SupportCategory::all();
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.category.list'),
                'data' =>  new CustomCollection($data, HelpSupportCategoryResource::class),
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }
}