<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\BannerResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use Illuminate\Http\JsonResponse;
use App\Models\Banner;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Exception;

class BannerController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Banner Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * BannerController constructor.
     * @param Banner $model
     */
    public function __construct()
    {
        $this->model = new Banner();
    }

    public function index(Request $request) : JsonResponse
    {
        try {
            $type = $request->type ? $request->type :'';

            $currentDateTime = Carbon::now('Asia/Kuala_Lumpur')->format('Y-m-d H:i:s');
            $query = $this->model
                ->where('banner_pannel_type','B2B')
                ->where('status',true)
                ->where('start_date', '<=', $currentDateTime)
                ->where('end_date', '>=', $currentDateTime)
                ->orderby('sequence');

            if(is_array($type)) {
                $query->whereIn('banner_type',$type);
            }

            $res = $query->get();

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.banner_list'),
                'data' => new CustomCollection($res, BannerResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}
