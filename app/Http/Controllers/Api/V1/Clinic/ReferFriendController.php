<?php

namespace App\Http\Controllers\Api\V1\Clinic;



use App\Http\Resources\V1\Clinic\ReferFriendResource;
use App\Http\Resources\CustomCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Mail\ReferFriendMail;
use App\Models\ClinicReferredCode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use App\Models\ClinicDetail;
use Illuminate\Support\Facades\Mail;

class ReferFriendController extends Controller
{

    public function list(Request $request) : JsonResponse
    {
        try {
            $perPage = $request->per_page ?? 10;
            $authId = Auth::user()->id;
            $clinicDetail = ClinicDetail::where('user_id', $authId)->first();
            $query = ClinicReferredCode::with('toUser')->where('from_user_id', $authId);
            
            $total = $query->count();
            $res = $perPage == 'all' ? $query->get() : $query->paginate($perPage);

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.refer_friends'),
                'data' => [
                    'extra' => [
                      'code' => $clinicDetail->referral_code,
                    ],
                    "total" => $total,
                    'list'  => new CustomCollection($res, ReferFriendResource::class) 
                ],
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function friendReferral(Request $request): JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $email = $request->input('email');

            $clinicDetail = ClinicDetail::where('user_id', $authId)->orderBy('updated_at', 'desc')->first();
            $code = $clinicDetail->referral_code;

            Mail::to($email)->send(new ReferFriendMail($clinicDetail));
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.referral_code_sent'),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}
