<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\ShippingLabel;
use App\Models\ShippingOrderTracking;
use App\Service\ShippingService;

class ShippingWebhookController extends Controller
{
    /**
     * Webhook to update shipping label and tracking status code.
     * Accepts orderNumber, alternateReferenceNumber, and statusCode as query params.
     */
    public function updateStatus(Request $request): JsonResponse
    {
        $authHeader = $request->header('Authorization');
        if ($authHeader && preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
            $token = $matches[1];
        }
        if(empty($token) || config('app.shipping_webhook_key') != $token){
            return response()->json([
                'success' => false,
                'message' => 'Invalid Access token.'
            ], 400);
        }

        $orderNumber = $request->query('OrderNumber');
        $alternateReferenceNumber = $request->query('AlternateReferenceNumber');
        $statusCode = $request->query('StatusCode');
        $statusUpdateDateTime = $request->query('StatusUpdateDateTime');
        $GEOCode = $request->query('GEOCode');
        $Image = $request->query('Image');
        $packageDetails = $request->query('packageDetails');

        $charge = $request->query('charge');
        if (!$orderNumber || !$alternateReferenceNumber || !$statusCode || !$statusUpdateDateTime) {
            return response()->json([
                'success' => false,
                'message' => 'Missing required query parameters.'
            ], 400);
        }

        $service = new ShippingService();
        $result = $service->updateShippingStatus($orderNumber, $alternateReferenceNumber, $statusCode, $statusUpdateDateTime, $GEOCode, $Image, $packageDetails, $charge);
        $status = $result['success'] ? 200 : ($result['message'] === 'Shipping label not found.' ? 404 : 400);
        return response()->json($result, $status);
    }
}
