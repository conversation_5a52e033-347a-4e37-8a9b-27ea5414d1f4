<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\CategoryResource;
use App\Repositories\Api\Clinic\ProductRepository;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use App\Models\Category;
use App\Models\DosageForm;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Exception;

class CategoryController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    protected $productRepository;

    /**
     * CategoryController constructor.
     * @param Category $model
     */
    public function __construct()
    {
        $this->model = new Category();
        $this->productRepository = new ProductRepository;
    }

    public function index() : JsonResponse
    {
        try {
            $query = $this->productRepository->clinicCategory();
            $res  = $query
                        ->orderBy('serial_number', 'asc')
                        ->orderBy('name', 'asc') 
                        ->get();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.category_list'),
                'data' => new CustomCollection($res, CategoryResource::class)
            ], Response::HTTP_OK);
            
            
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function subCategory($categoryId) : JsonResponse
    {
        try {
            $res = $this->model
                ->where('type','b2b')
                ->where('parent_id','=',decryptParam($categoryId))
                ->where('status',true)
                ->withCount(['subCategoryProducts as active_product_count' => function ($query) {
                    $query->where('status', 'approved');
                }])
                ->get();
            if($res) {
                $response  =  response()->json([
                    'success'   => true,
                    'message'   => __('api.clinic.sub_category_list'),
                    'data' => new CustomCollection($res, CategoryResource::class)
                ], Response::HTTP_OK);
            }else{
                $response  =  response()->json([
                    'success'   => false,
                    'message'   => __('api.clinic.Category_list'),
                    'data' => null
                ], Response::HTTP_OK);
            }
            
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function foams() : JsonResponse
    {
        try {
            $res = DosageForm::where('status',true)
                ->withCount(['products as active_product_count' => function ($query) {
                    $query->where('status', 'approved');
                }])
                ->get();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.foam_list'),
                'data' => new CustomCollection($res, CategoryResource::class)
            ], Response::HTTP_OK);
            
            
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}