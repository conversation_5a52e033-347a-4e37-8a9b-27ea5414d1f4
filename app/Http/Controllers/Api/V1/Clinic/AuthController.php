<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use Carbon\Carbon;
use App\Models\User;
use App\Mail\SendOtpMail;
use Illuminate\Support\Str;
use App\Models\ClinicDetail;
use Illuminate\Http\Response;
use App\Models\OtpVerification;
use Illuminate\Http\JsonResponse;
use App\Models\PasswordResetToken;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use App\Http\Requests\Api\Clinic\OtpRequest;
use App\Http\Resources\V1\UserResource;
use App\Http\Requests\Api\Clinic\LoginRequest;
use App\Http\Requests\Api\Clinic\RegisterRequest;
use App\Http\Requests\Api\Clinic\OtpVerifyRequest;
use App\Http\Requests\Api\Clinic\ResentOtpRequest;
use App\Http\Requests\Api\Clinic\ResetPasswordRequest;
use App\Http\Requests\Api\Clinic\ForgotPasswordRequest;
use App\Http\Requests\Api\Clinic\LoginOtpVerifyRequest;
use App\Http\Requests\Api\Clinic\TermConditionRequest;
use App\Repositories\Api\Clinic\OnboardingRepository;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use App\Models\ClinicAddCart;
use App\Models\TermCondition;
use App\Models\UserAddress;
use App\Mail\WelcomeFacilityMail;
use App\Models\UserTermCondition;
use App\Service\StripeService;
use App\Jobs\SuperAdminNotificationJob;
use Illuminate\Support\Facades\Auth;
use Exception;

class AuthController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    protected $optTime;

    /**
     * UserController constructor.
     *
     * @param  User  $model
     */
    public function __construct(
        protected OnboardingRepository $onboardingRepository,
        protected StripeService $stripeService,)
    {

        $this->model = new User;
        $this->optTime = config('constants.api.otp_time');
        $this->onboardingRepository = $onboardingRepository;
        $this->stripeService = $stripeService;
    }

    public function register(RegisterRequest $request): JsonResponse
    {
        try { 
            DB::beginTransaction();
            $data = $request->validated();

            $otpVerification = $this->otpVerify($data);

            if (! $otpVerification) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.auth.otp_invalid'),
                ], Response::HTTP_BAD_REQUEST);
            }

                $expiresAt = Carbon::parse($otpVerification->expires_at); 
                $isExpired = $expiresAt->isPast(); // Check if expiration time has passed
                
                if ($isExpired) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.auth.otp_expired'),
                ], Response::HTTP_BAD_REQUEST);
            }
            $otpVerification->delete();

            $data['password'] = bcrypt($data['password']);
            $data['remember_token'] = bcrypt($data['password']);
            $data['account_type_id'] = userAccountType('clinic');
            $data['is_otp_verified'] = true;
            $data['status'] = true;
            unset($data['password_confirmation'],$data['otp']);

            $newUser = $this->model->create($data);
            $newUser->assignRole('Clinic');

            ClinicDetail::create([
                'user_id' => $newUser->id,
                'referral_code' => 'REF'.Carbon::now()->format('YmdHis').$newUser->id,
                'clinic_name' =>  $data['name'],
                'signature_type' =>  false,
            ]);

             //Customer create in stripe account
            //$this->stripeService->customerCreate($newUser);
           
            Mail::to($newUser->email)->send(new WelcomeFacilityMail($newUser));
            $response = $this->setUserResponse($newUser, __('api.auth.register_success'), Response::HTTP_CREATED);
            DB::commit();
            SuperAdminNotificationJob::dispatch('new_facility',$newUser->id);   

        } catch (\Exception $e) {
            DB::rollBack();
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function sendSignupOtp(OtpRequest $request)
    {
        try {
            $data = $request->validated();
            $otp = $this->otpCreate($data['email'], $this->optTime,'register');
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.otp_send'),
                'data' => ([
                    'otp' => $otp, // need to remove from response
                ]),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function otpVerify($data)
    {
        return OtpVerification::where('email', $data['email'])
            ->where('otp', $data['otp'])
            ->first();
    }

    public function sendLoginOtp(LoginRequest $request)
    {
        try {
            $data = $request->validated();
            // Find the user by email
            $query = $this->model->where([
                'email' => $data['email'],
            ]);

            $userData = $query->first();
            if ($userData) {
                if(!$userData->status){
                    $response = response()->json([
                        'success' => false,
                        'message' => __('api.auth.inactive_account'),
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }else{
                    // Verify the password and generate the token
                    if (Hash::check($data['password'], $userData->password)) {

                        $termConditionStatus = $this->onboardingRepository->termConditions($userData->id);
                        if(!$termConditionStatus) {
                            $terms = TermCondition::where('status', true)->first();
                            return response()->json([
                                'success' => true,
                                'message' => __('api.auth.term_conditions'),
                                'data' => [
                                    'is_terms_accepted' => false,
                                    'terms_data' => $terms ? $terms->meta_description : null,
                                    'terms_id' => $terms ?  encryptParam($terms->id) : null
                                ],
                            ], Response::HTTP_OK);
                        }

                        $isStayLogin = $userData->is_stay_login;
                        $isStayDate = $userData->last_stay_login_at;
                        if($data['stay_login']) {
                            $this->updateStayLoginData($userData);
                        }
                        $currentDate = Carbon::now()->format('Y-m-d');
                        if(!$isStayLogin || ($isStayLogin && $isStayDate < $currentDate)) {
                            $otp = $this->otpCreate($data['email'], $this->optTime,'login');
                        }else{
                            return $this->setUserResponse($userData);
                        }

                        $response = response()->json([
                            'success' => true,
                            'message' => __('api.auth.otp_send'),
                            'data' => [
                                'is_terms_accepted' => $termConditionStatus ? true : false,
                                'otp' => $otp,
                            ],
                        ], Response::HTTP_OK);
                    } else {
                        $response = response()->json([
                            'success' => false,
                            'message' => __('api.auth.invalid_cred'),
                        ], Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                }
                
            } else {
                $response = response()->json([
                    'success' => false,
                    'message' => __('api.auth.email_verify'),
                ], Response::HTTP_FORBIDDEN);
            }
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function reSentOtp(ResentOtpRequest $request)
    {
        try {
            $data = $request->validated();
            $otp = $this->otpCreate($data['email'], $this->optTime,'re_sent');
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.otp_send'),
                'data' => ([
                    'otp' => $otp,
                ]),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function login(LoginOtpVerifyRequest $request)
    {
        try {
            $data = $request->validated();
            $query = $this->model->where([
                'email' => $data['email'],
                'is_otp_verified' => true,
                'status' => true,
            ]);

            $userData = $query->first();
            if ($userData) {
                $otpVerification = $this->otpVerify($data);

                if (! $otpVerification) {

                    return response()->json([
                        'success' => false,
                        'message' => __('api.auth.otp_invalid'),
                    ], Response::HTTP_BAD_REQUEST);
                }
                $expiresAt = Carbon::parse($otpVerification->expires_at); 
                $isExpired = $expiresAt->isPast(); // Check if expiration time has passed

                if ($isExpired) {
                    return response()->json([
                        'success' => false,
                        'message' => __('api.auth.otp_expired'),
                    ], Response::HTTP_BAD_REQUEST);
                }
                $otpVerification->delete();


                return $this->setUserResponse($userData);
                
            } else {
                $response = response()->json([
                    'success' => false,
                    'message' => __('api.auth.email_verify'),
                ], Response::HTTP_FORBIDDEN);
            }

        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function forgotPassword(ForgotPasswordRequest $request)
    {
        try {
            $data = $request->validated();
            $otp = $this->otpCreate($data['email'], $this->optTime,'forgot_password');
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.otp_send'),
                'data' => ([
                    'otp' => $otp,
                ]),
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function forgotPasswordOtpVerify(OtpVerifyRequest $request)
    {
        try {
            $data = $request->validated();
            $otpVerification = $this->otpVerify($data);

            if (! $otpVerification) {

                return response()->json([
                    'success' => false,
                    'message' => __('api.auth.otp_invalid'),
                ], Response::HTTP_BAD_REQUEST);
            }

            if (Carbon::now()->greaterThan($otpVerification->expires_at)) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.auth.otp_expired'),
                ], Response::HTTP_BAD_REQUEST);
            }
            $otpVerification->delete();

            //Create token for security
            $token = Str::random(60);
            PasswordResetToken::updateOrInsert(
                ['email' => $data['email']],
                [
                    'token' => Hash::make($token),
                    'created_at' => now(),
                ]
            );
            $response = response()->json([
                'success' => true,
                'message' => __('api.auth.otp_success'),
                'data' => ([
                    'token' => $token,
                ]),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {

        try {
            $data = $request->validated();
            DB::beginTransaction();
            $tokenQuery = PasswordResetToken::where('email', $data['email']);
            $tokenExist = $tokenQuery->first();
            if (Hash::check($request->get('token'), $tokenExist->token)) {
                $tokenQuery->delete();

                $user = $this->model->where('email', $data['email'])->first();
                // Reset the password
                if ($user) {
                    $user->password = bcrypt($data['password']);
                    $user->is_otp_verified = true;
                    $user->email_verified_at = Carbon::now();
                    $user->save();
                    $response = response()->json([
                        'success' => true,
                        'message' => __('api.auth.password_sent'),
                    ], Response::HTTP_OK);
                } else {
                    $response = response()->json([
                        'success' => false,
                        'message' => __('api.auth.user_not_found'),
                    ], Response::HTTP_NOT_FOUND);
                }
                DB::commit();
            } else {
                $response = response()->json([
                    'success' => false,
                    'message' => __('api.auth.invalid_token'),
                ], Response::HTTP_NOT_FOUND);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            $response = exceptionResponse($e->getMessage());
        }

        return $response;

    }
    public function updateTermCondition(TermConditionRequest $request): JsonResponse
    {

        try {
            $data = $request->validated();
            
            $query = $this->model->where([
                'email' => $data['email'],
            ]);

            $userData = $query->first();
            if ($userData) {
                if(!$userData->status){
                    $response = response()->json([
                        'success' => false,
                        'message' => __('api.auth.inactive_account'),
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
                }else{
                    if (Hash::check($request->get('password'), $userData->password)) {

                        UserTermCondition::updateOrCreate([
                            'user_id' => $userData->id,
                            'term_condition_id' => decryptParam($data['terms_id'])
                        ]);

                        $isStayLogin = $userData->is_stay_login;
                        $isStayDate = $userData->last_stay_login_at;
                        if($request->stay_login) {
                            $this->updateStayLoginData($userData);
                        }
                        $currentDate = Carbon::now()->format('Y-m-d');

                        if(!$isStayLogin || ($isStayLogin && $isStayDate < $currentDate)) {
                            $otp = $this->otpCreate($data['email'], $this->optTime,'login');
                        }else{
                            return $this->setUserResponse($userData);
                        }

                        $response = response()->json([
                            'success' => true,
                            'message' => __('api.auth.otp_send'),
                            'data' => [
                                'is_terms_accepted' => true,
                                'otp' => $otp,
                            ],
                        ], Response::HTTP_OK);

                    } else {
                        $response = response()->json([
                            'success' => false,
                            'message' => __('api.auth.invalid_cred'),
                        ], Response::HTTP_UNPROCESSABLE_ENTITY);
                    }
                }

            } else {
                $response = response()->json([
                    'success' => false,
                    'message' => __('api.auth.email_verify'),
                ], Response::HTTP_FORBIDDEN);
            }

        } catch (\Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;

    }
    public function updateStayLoginData($userData)
    {
        $days = config('constants.api.stay_login_time');
        $userData->last_stay_login_at = Carbon::now()->addDays($days);
        $userData->is_stay_login = true;
        $userData->save();

        return $userData;

    }

    public function otpCreate($email, $time,$type)
    {
        try {
            $otp = generateOTP();
            //before timing should not be update otp
            $res = OtpVerification::updateOrCreate(['email' => $email], [
                'email' => $email,
                'otp' => $otp,
                'expires_at' => Carbon::now()->addMinutes($time),
            ]);
            
            Mail::to($email)->send(new SendOtpMail($otp,$time,$type));

            return [
                //'otp' => $res['otp'],
                'expires_at' => $time * 60,
            ]; 
        }catch(Exception $e) {
            throw ValidationException::withMessages([
                __('api.auth.mail_issue')
            ]);
        }
        
    }



    public function setUserResponse($userData,$msg=null, $statusCode = Response::HTTP_OK) {

        $userData->update([
            'timezone' => App::make('userTimezone')
        ]);
        $token = $userData->createToken('API Token')->plainTextToken;
        $userData->cart_count = ClinicAddCart::where('user_id', $userData->id)->count();
        $userData->address = UserAddress::where('user_id', $userData->id)->where('is_default', true)->first();
        $userData->unread_message = $this->onboardingRepository->unReadMsgCount($userData->id);

        $userData->clinicData->update(['social_type' => null]);
       
        return response()->json([
            'success' => true,
            'message' => $msg ?? __('api.auth.user_details'),
            'data' => [
                'is_terms_accepted' => $this->onboardingRepository->termConditions($userData->id) ? true : false,
                'token' => $token,
                'user' => (new UserResource($userData)),
            ],
        ], $statusCode);
    }
}
