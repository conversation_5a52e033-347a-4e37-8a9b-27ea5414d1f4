<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\SupportUnreadMessagesListResource;
use App\Http\Resources\V1\Clinic\SupportMessageListResource;
use App\Http\Resources\V1\Clinic\ThreadMessageDetailResource;
use App\Repositories\Api\Clinic\OnboardingRepository;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Http\Resources\CustomCollection;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Clinic\ThreadMessageRequest;
use App\Http\Requests\Api\Clinic\ThreadRequest;
use App\Models\Media;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use App\Models\SubOrder;
use App\Models\Thread;
use App\Models\ThreadMessage;
use App\Notifications\Api\NewMessageNotification;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Database\Eloquent\Builder;
use App\Jobs\SupplierNotificationJob;


class ThreadController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * ThreadController constructor.
     *
     * @param  Thread  $model
     */
    public function __construct(protected OnboardingRepository $onboardingRepository)
    {
        $this->model = new Thread();
        $this->onboardingRepository = $onboardingRepository;
    }


    public function store(ThreadRequest $request): JsonResponse
    {
        try { 
            $auth  = Auth::user();
            $data = $request->all();

            $receiverId = decryptParam($data["receiver_id"]) ?? null; 
            $orderId = decryptParam($data["order_id"]) ?? null; 

            $thread = Thread::where('receiver_id', $receiverId)
                ->where('order_id', $orderId)
                ->first();
            $msg =    __('api.clinic.thread.already_create');
            if (!$thread) {

                $thread = $this->model->create([
                "sender_id"  => $auth->id,
                "receiver_id"  => $receiverId,
                "order_id"  => $orderId,
                "thread_type"  => "b2b",
                ]);
                $msg = __('api.clinic.thread.create');
                
            }
            $receiver =  $thread->receiver;
            $response = response()->json([
                'success' => true,
                'message' =>$msg,
                "data" => [
                    "thread_id" => encryptParam($thread->id),   
                    "order_number" => $thread->order->order_number,   
                    "receiver_name" => $thread->pcDetail->company_name,   
                ],
            ], Response::HTTP_CREATED);

            //$receiver->notify(new SupportTicketNotification($receiver->id,'pc',$thread->id,'thread',$subOrderId));

        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }

    public function messageList(Request $request): JsonResponse
    {
        try {
            $auth  = Auth::user();
            $sort = $request->sort ? ($request->sort == 'newest' ? 'DESC' : 'ASC') : 'DESC';
            //filter remains to be
            $perPage = $request->per_page && is_numeric($request->per_page) ? $request->per_page : 10;
            $latestMessageSubquery = ThreadMessage::select('created_at')
            ->whereColumn('thread_messages.thread_id', 'threads.id')
            ->latest('created_at')
            ->limit(1)
            ->toSql(); // we'll use this for orderByRaw
        
        $query = Thread::query()
            ->withCount('unreadMessages')
            ->with('latestMessage')
            ->addSelect([
                'latest_message_created_at' => ThreadMessage::select('created_at')
                    ->whereColumn('thread_messages.thread_id', 'threads.id')
                    ->latest('created_at')
                    ->limit(1)
            ])
            ->where(function ($q) use ($auth) {
                $q->where('sender_id', $auth->id)
                    ->orWhere('receiver_id', $auth->id);
            }) 
            ->orderByRaw("($latestMessageSubquery) IS NULL, ($latestMessageSubquery) $sort");

            if ($request->filled('search')) {
                $searchKey = $request->search;
            
                $query->where(function ($q) use ($searchKey) {
                    $q->whereHas('order', function ($orderQuery) use ($searchKey) {
                        $orderQuery->where('order_number', 'ILIKE', "%{$searchKey}%");
                    })
                    ->orWhereHas('pcDetail', function ($receiverQuery) use ($searchKey) {
                        $receiverQuery->where(function ($nameQuery) use ($searchKey) {
                            $nameQuery->where('company_name', 'ILIKE', "%{$searchKey}%")
                                      ->orWhere(function ($businessNameQuery) use ($searchKey) {
                                          $businessNameQuery->where(function ($emptyCheck) {
                                              $emptyCheck->whereNull('company_name')
                                                         ->orWhere('company_name', '');
                                          })
                                          ->whereHas('companyType', function ($typeQuery) {
                                              $typeQuery->where('name', 'Sole Proprietary');
                                          })
                                          ->where('business_name', 'ILIKE', "%{$searchKey}%");
                                      });
                        });
                    });
                });
            }

            if ($request->filled('order_number')) {
                $orderNumber = $request->order_number;

                $query->where(function ($q) use ($orderNumber) {
                    $q->whereHas('order', function ($orderQuery) use ($orderNumber) {
                        $orderQuery->where('order_number', $orderNumber);
                    });
                });
            }

            $tickets =$query->paginate($perPage);
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.details'),
                'data' =>  [
                    'total' => $tickets->total(),
                    'list' => new CustomCollection($tickets, SupportMessageListResource::class),
                ]
            ], Response::HTTP_OK);
            
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }
    public function messageDetail(Request $request,$id): JsonResponse
    {
        try {
            $perPage = $request->per_page && is_numeric($request->per_page) ? $request->per_page : 10;

            $id = decryptParam($id);
            $ticket = $this->model->find($id);
            if (!$ticket) {
                return response()->json(['success' => false, 'message' => __('api.clinic.thread.not_found')], 404);
            }
            $messages = $ticket->messages()
                        ->with('media')
                        ->orderBy('created_at', 'desc')
                        ->paginate($perPage);
            
            // **Mark all unread messages in this ticket as read**
            $ticket->messages()->whereNot('from_id',Auth::user()->id)->where('is_read', false)->update(['is_read' => true]);
            $ticket->setAttribute('unread_message', $this->onboardingRepository->unReadMsgCount(Auth::user()->id));

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.thread.message_detail'),
                'data' =>   new ThreadMessageDetailResource($ticket, $messages)
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }
    public function messageSave(ThreadMessageRequest $request): JsonResponse
    {
        try {
            $data = $request->all();
            $threadId = decryptParam($data["thread_id"]);
            $thread = Thread::with('receiver')->find($threadId);
            $receiver = $thread->receiver;

            $subOrderId = SubOrder::where([
                ['order_id', $thread->order_id],
                ['user_id', $receiver->id]
            ])->value('id');
            $message = ThreadMessage::create([
                'thread_id' => $threadId,
                'from_id' => Auth::user()->id,
                'message' => $data['text'] != 'null' ? $data['text'] : '',
            ]);

            if ($request->hasFile('files')) {
                $mediaData = [];
                $folderPath = config('constants.api.media.thread') . $thread->id . '/';
                foreach ($request->file('files') as $key =>  $file) {  

                        $fileName =  uploadFile($file, $folderPath);
                        $mediaData[] = [
                            "model_type"            => 'App\Models\ThreadMessage',
                            "model_id"              => $message->id,
                            "uuid"                  => Str::uuid()->toString(),
                            "collection_name"       => 'thread-chat-images',
                            "name"                  => $fileName,
                            "file_name"             => $fileName,
                            "mime_type"             => $file->getMimeType(),
                            "disk"                  => config('filesystems.default'),
                            "conversions_disk"      => config('filesystems.default'),
                            "size"                  => $file->getSize(),
                            "manipulations"         => json_encode([]),
                            "custom_properties"     => json_encode([]),
                            "generated_conversions" => json_encode([]),
                            "responsive_images"     => json_encode([]),
                            "order_column"          => $key + 1,
                            "created_at"            => now(),
                            "updated_at"            => now(),
                        ];
                }
                Media::insert($mediaData);
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.sent_message'),
            ], Response::HTTP_OK);
            
             //For sub user
            SupplierNotificationJob::dispatch($receiver->id, 'pc', $threadId,'thread',$subOrderId);

            //For main user
            $receiver->notify(new NewMessageNotification($receiver->id,'pc',$threadId,'thread',$subOrderId));
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function unReadMessages(Request $request): JsonResponse
    {
        try{
            $msg = ThreadMessage::whereHas('ticket' ,function ($query) {
                $query->where('sender_id', Auth::id());
            })
            ->where('is_read', false)
            ->whereNot('from_id', Auth::id())
            ->orderBy('created_at','desc')
            ->get();
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.un_read_msg'),
                'data' => new CustomCollection($msg, SupportUnreadMessagesListResource::class)
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }
}