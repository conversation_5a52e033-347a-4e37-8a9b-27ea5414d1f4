<?php

namespace App\Http\Controllers\Api\V1\Clinic;



use App\Http\Resources\V1\Clinic\ReferFriendResource;
use App\Http\Resources\CustomCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Http\Resources\V1\Clinic\PharmaPointResource;
use App\Mail\ReferFriendMail;
use App\Models\ClinicReferredCode;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use App\Models\ClinicDetail;
use App\Models\DpharmaPoint;
use Illuminate\Support\Facades\Mail;

class DpharmaPointController extends Controller
{

    public function list(Request $request) : JsonResponse
    {
        try {
            $perPage = $request->per_page ?? 10;
            $auth = Auth::user();
            $authId = $auth->id;
            $query = DpharmaPoint::where('user_id', $authId);
            $extra = $this->extraData($query);
            $query->orderBy('created_at', 'desc');
            
            $total = $query->count();
            $res = $perPage == 'all' ? $query->get() : $query->paginate($perPage);

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.dpharma_points'),
                'data' => [
                    'extra' => $extra,
                    "total" => $total,
                    'list'  => new CustomCollection($res, PharmaPointResource::class) 
                ],
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function pointsDetail(Request $request) : JsonResponse
    {
        try{
            $auth = Auth::user();
            $authId = $auth->id;
            $query = DpharmaPoint::where('user_id', $authId);
            $extra = $this->extraData($query);
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.dpharma_points'),
                'data' => [
                    "current_tier" => $extra['tier'],
                    "current_point" => $extra['points'],
                    'tier_limit' => config('constants.api.tier_limit'),
                    'tier_per' => config('constants.api.tier_per'),
                ]
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function extraData($query) {
        
        $auth = Auth::user();
        $finalPoints =(clone $query)->latest('created_at')->first();
        return [
            "tier" => $auth->clinicData->tier,
            "points" => number_format($finalPoints->balance , 2) ?? 0 ,
            "is_detail_show"=> config('constants.api.facility_tier.status')
        ];
    }


}
