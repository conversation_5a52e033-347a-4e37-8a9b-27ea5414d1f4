<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\ProductSupplierResource;
use App\Http\Resources\V1\Clinic\ProductDetailResource;
use App\Http\Resources\V1\Clinic\RecentSearchResource;
use App\Repositories\Api\Clinic\ProductRepository;
use App\Http\Resources\V1\Clinic\ProductResource;
use App\Http\Resources\V1\Clinic\GlobalProductSearchResource;
use App\Http\Resources\CustomCollection;
use App\Models\BrowserProductHistory;
use App\Http\Controllers\Controller;
use App\Models\ClinicRecentSearch;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\JsonResponse;
use App\Models\ProductRelation;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Models\DosageForm;
use App\Models\Category;
use App\Models\Distributor;
use App\Models\Product;
use Exception;

class ProductController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    protected $productRepository;

    /**
     * AccountTypeController constructor.
     *
     * @param  Product  $model
     */
    public function __construct()
    {
        $this->model = new Product;
        $this->productRepository = new ProductRepository;
    }

    public function gloabalSearch(Request $request) : JsonResponse
    {
        try {
            $perPage =$request->per_page ?? 10;
            
            if ($request->has('search') && $request->input('search')) {
                $query = $this->productRepository->globalSearchQuery($request);
             
    
                if ($perPage == 'all') {
                    $results = $query->get();
                } else {
                    $results = $query->paginate($perPage);
                }

                // Get all items from the paginator
                $items = collect($results->items());
                
                // Create maps to track each unique entity
                $seenCategories = [];
                $seenSubcategories = [];
                $seenBrands = [];
                $seenGenericNames = [];
                $seenSuppliers = [];
                $seenDistributors = [];
                
                // Filter to get unique entities of each type
                $uniqueItems = $items->filter(function($item) use (
                    &$seenCategories, 
                    &$seenSubcategories, 
                    &$seenBrands, 
                    &$seenGenericNames, 
                    &$seenSuppliers, 
                    &$seenDistributors
                ) {
                    $keepItem = false;
                    
                    // Check if this is a product name match (always include these)
                    if ($item->matched_in_name) {
                        $keepItem = true;
                    }
                    
                    // Check and track category matches
                    if ($item->matched_in_category) {
                        if (!isset($seenCategories[$item->category_id])) {
                            $seenCategories[$item->category_id] = true;
                            $keepItem = true;
                        }
                    }
                    
                    // Check and track subcategory matches
                    if ($item->matched_in_subcategory) {
                        if (!isset($seenSubcategories[$item->sub_category_id])) {
                            $seenSubcategories[$item->sub_category_id] = true;
                            $keepItem = true;
                        }
                    }
                    
                    // Check and track brand matches
                    if ($item->matched_in_brand) {
                        if (!isset($seenBrands[$item->brand_id])) {
                            $seenBrands[$item->brand_id] = true;
                            $keepItem = true;
                        }
                    }
                    
                    // Check and track generic name matches
                    if ($item->matched_in_generic_name) {
                        if (!isset($seenGenericNames[$item->generic_name_id])) {
                            $seenGenericNames[$item->generic_name_id] = true;
                            $keepItem = true;
                        }
                    }
                    
                    // Check and track supplier matches
                    if ($item->matched_in_supplier) {
                        if (!isset($seenSuppliers[$item->user_id])) {
                            $seenSuppliers[$item->user_id] = true;
                            $keepItem = true;
                        }
                    }
                    
                    // Check and track distributor matches
                    if ($item->matched_in_distributor && !empty($item->distributor_name)) {
                        $distributorKey = strtolower($item->distributor_name);
                        if (!isset($seenDistributors[$distributorKey])) {
                            $seenDistributors[$distributorKey] = true;
                            $keepItem = true;
                        }
                    }
                    
                    return $keepItem;
                });
                $total = $uniqueItems->count();
                // Create a new paginator with unique items but maintain original pagination metadata
                $res = new \Illuminate\Pagination\LengthAwarePaginator(
                    $uniqueItems->values(),
                    $total,
                    $perPage == 'all' ? $total : $perPage,
                    $results->currentPage(),
                    ['path' => request()->url(), 'query' => request()->query()]
                );
            }else {
                $total = 0;
                $res = collect([]);
            }

           
            
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_list'),
                'data' => [
                    'total' => $total,
                    'list' => new CustomCollection($res, GlobalProductSearchResource::class),
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }


    public function list(Request $request): JsonResponse
    {
        try {
            $perPage =$request->per_page ?? 10;
            request()->merge(['from_listing' => true]);
            $query = $this->productRepository->products($request);

            $countQuery = clone $query;
            $total = $countQuery->getCountForPagination();
            if($perPage == 'all') {
                $res = $query->get();

            }else{
                $res = $query->paginate($perPage);
            }
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_list'),
                'data' => [
                    'total' => $total,
                    'list' =>new CustomCollection($res, ProductResource::class),
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    public function detail($id): JsonResponse
    {
        try {
            $id = decryptParam($id);
            $authId = Auth::user()->id;
            $product = $this->model->with([
                            'medias', 
                            'productRelations.productRelationPrice',
                            'productRelations.productRelationStock',
                            'distributors'
                        ])
                        ->whereNull('deleted_at')
                        ->whereHas('productRelations', function ($query) {
                            $query->whereHas('productRelationPrice')
                                ->whereHas('productRelationStock');
                        })
                        ->find($id);
            if (!$product) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.product_not_found'),
                ], Response::HTTP_NOT_FOUND);
            }

            //Recent search save with 5 limit
            BrowserProductHistory::updateOrCreate(['user_id'=>$authId,'product_id' => $id]);
            $recentSearchCount = BrowserProductHistory::where('user_id',$authId)->count();
                if($recentSearchCount > 7) {
                    BrowserProductHistory::where('user_id',$authId)->oldest()->take(1)->delete();
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_detail'),
                'data' => new ProductDetailResource($product)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    public function productSupplier(Request $request , $id): JsonResponse
    {
        try {

            $request->validate([
                'value' => [
                    'nullable',
                    'regex:/^\d+$/', // Ensure only digits
                    function ($attribute, $value, $fail) {
                        if ($value < 1) { // Ensure it is at least 1
                            $fail("The $attribute must be at least 1.");
                        }
                    }
                ],
            ]);

            $perPage =$request->per_page ?? 10;

            $query = $this->productRepository->suppliers($request,$id);

            // Pagination and fetching results
            $perPage = $request->get('per_page', 10);
            $total = $query->count();

            $suppliers = ($perPage === 'all') ? $query->get() : $query->paginate($perPage);

            if (!$suppliers) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.product_not_found'),
                ], Response::HTTP_NOT_FOUND);
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_detail'),
                'data' => [
                    'total' => $total,
                    'list' => new CustomCollection($suppliers, ProductSupplierResource::class)
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function recentSearches() : JsonResponse
    {
        try {
            $auth = Auth::user();
            $recentSearches = ClinicRecentSearch::where('user_id', $auth->id)
            ->orderBy('created_at', 'desc')
            ->get();

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.recent_search_list'),
                'data' => [
                    'list' =>new CustomCollection($recentSearches, RecentSearchResource::class)
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function clearRecentSearches() : JsonResponse
    {
        try {
            $auth = Auth::user();
            ClinicRecentSearch::where('user_id', $auth->id)->delete();

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.recent_search_cleared'),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function filterList(Request $request) : JsonResponse
    {
        try{
            $auth = Auth::user();
            $authId = $auth->id;
            $zone = getClinicZone();
            $query = $this->productRepository->filterBaseQuery($request,$zone,$authId); 
            $productIds = $query->pluck('id')->toArray();

            $categories = $this->categories($productIds);
            $filters = [
                    'prices' =>  $this->prices($query),
                    'high_price' => $request->high_price,
                    'suppliers' =>  $this->suppliers($request,$query),
                    'category' =>  $categories,
                    'sub_category' =>  $this->subCategories($request,$query, $categories),
                    'distributors' =>  $this->distributors($request,$query),
                    'foam' =>  $this->foams($request,$query),
            ];
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_list'),
                'data' => $filters,
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }


    public function applyFilters($query, $request, $currentFilter = null) {
        // Clone the query to avoid modifying the original
        $filteredQuery = clone $query;

        // Apply category filter (unless we're in the category method)
        if ($currentFilter !== 'category' && $request->has('categories') && is_array($request->categories)) {
            $categories = $request->categories;
            $filteredQuery->whereIn('main_category.name', $categories);
        }
    
        // Apply subcategory filter (unless we're in the subcategory method)
        if ($currentFilter !== 'subcategory' && $request->has('sub_categories') && is_array($request->sub_categories)) {
            $sub_categories = $request->sub_categories;
            $filteredQuery->whereIn('sub_category.name', $sub_categories);
        }
        
        // Apply foam filter (unless we're in the foam method)
        if ($currentFilter !== 'foam' && $request->has('foams') && is_array($request->foams)) {
            $foams = $request->foams;
            $filteredQuery->whereIn('dosage_foams.name', $foams);
        }

        // Return the updated product IDs
        return $filteredQuery->pluck('id')->toArray();
    }

    public function prices($query)
    {
        $product = $query->get();
        $lowestPrice = 1; //$product->min('min');
        $highestPrice = $product->max('max');

        return [$lowestPrice, $highestPrice ];

    }
    public function categories($productIds) {
        
       $categories = $this->productRepository->clinicCategory()
            ->withCount(['products as product_count' => function ($query) use ($productIds) {
                $query->whereNull('deleted_at')
                    ->where('status', 'approved')
                    ->whereIn('id', $productIds);
            }])
            ->orderBy('product_count', 'desc')->get(['id', 'name']);
        
        $res = $categories->map(function ($category) {
            return [
                'id' => encryptParam($category->id),
                'name' => $category->name,
                'product_count' => $category->product_count ?? 0,
            ];
        })->toArray();

        return  $res;
    }
    public function suppliers($request,$query) {
        $res = [];
        
       
        $filteredIds = $this->applyFilters($query, $request, 'supplier');

        $supplierQry = DB::table('pc_details as pd')
                ->join('users as u', 'u.id', '=', 'pd.user_id')
                ->join('products_relation as pr', 'pr.user_id', '=', 'pd.user_id')
                ->join('products as p', 'p.id', '=', 'pr.product_id')
                ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                ->leftJoin('pc_company_types as pct', 'pct.id', '=', 'pd.company_type_id')
                ->where(function ($query) {
                    $query->where(function ($q) {
                        // For fixed and bonus, east and west prices must be present
                        $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                        ->whereNotNull('prp.east_zone_price')
                        ->where('prp.east_zone_price', '>', 0)
                        ->whereNotNull('prp.west_zone_price')
                        ->where('prp.west_zone_price', '>', 0);
                    })->orWhere(function ($q) {
                        // For tier, all 6 tier prices must be present
                        $q->where('pr.price_type', 'tier')
                        ->whereNotNull('prp.east_tier_1_base_price')
                        ->where('prp.east_tier_1_base_price', '>', 0);
                    });
                })
                ->where(function ($query) {
                    $query->where(function ($q) {
                        // For non-batch stock, check expiry_date in product_relation_stocks
                        $q->where('prs.is_batch_wise_stock', false)
                           ->where(function ($expiry) {
                               $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE')); // Or future expiry date
                           });
                    })
                    ->orWhere(function ($q) {
                        // For batch stock, filtering happens elsewhere
                        $q->where('prs.is_batch_wise_stock', true)->whereExists(function($query) {
                        $currentDate = date('Y-m-d');
                        $query->select(DB::raw(1))
                                ->from('products_batch')
                                ->whereColumn('products_batch.product_id', 'pr.product_id')
                                ->whereColumn('products_batch.user_id', 'pr.user_id')
                                ->where(DB::raw("DATE(products_batch.expiry_date)"), '>=', $currentDate)
                                ->where('products_batch.available_stock', '>', 0);
                    });
                    });
                })
                ->where(function ($query) {
                $query->where('pd.is_restricted', false)
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('pd.is_restricted', true)
                            ->whereExists(function ($existsQuery) {
                                $existsQuery->select(DB::raw(1))
                                    ->from('clinic_pharma_suppliers as cps')
                                    ->whereColumn('cps.pc_id', 'pd.user_id') 
                                    ->where('cps.clinic_id', Auth::user()->id) 
                                    ->where('cps.status', 'approved');
                            });
                    });
                })
                ->where('u.verification_status', 'approved')
                ->whereIn('pr.product_id', $filteredIds)
                ->where('pr.pc_approval', true)
                ->where('pr.admin_approval', true)
                ->whereNull('pr.deleted_at')
                ->whereNull('p.deleted_at')
                ->where('u.is_active', true)
                ->where('p.status', 'approved')

                // Select only what's needed
                ->select(
                'u.id as user_id',
                'pd.company_name',
                'pd.business_name',
                'pct.name as company_type_name',
                DB::raw('COUNT(DISTINCT pr.product_id) as product_count')
                )

                // Group by selected fields
                ->groupBy('u.id', 'pd.company_name', 'pd.business_name', 'pct.name');

                $suppliers = $supplierQry->get();

                $res = [];
                
        foreach ($suppliers as $supplier) {
            // Apply the same logic as in the PcDetail model getter
            $companyName = !empty($supplier->company_name) 
                ? $supplier->company_name 
                : (($supplier->company_type_name === 'Sole Proprietary') 
                    ? ($supplier->business_name ?? null) 
                    : $supplier->company_name);
            \Log::info($supplier->user_id);
            $res[] = [
            'id' => encryptParam($supplier->user_id),
            'name' => $companyName,
            'product_count' => (int) $supplier->product_count,
            ];
        }

        return $res;

    }
    public function distributors($request,$query) {

        $filteredIds = $this->applyFilters($query, $request, 'distributor');


        $distributors = Distributor::withCount([
            'products as product_count' => function ($query) use ($filteredIds) {
                $query->whereNull('products.deleted_at')
                        ->where('products.status', 'approved')
                        ->whereIn('products.id', $filteredIds);
            }
        ])->orderBy('product_count', 'desc')->get(['id', 'name']);
        
        $res = $distributors->map(function ($distributor) {
            return [
                'id' => encryptParam($distributor->id),
                'name' => $distributor->name,
                'product_count' => $distributor->product_count ?? 0,
            ];
        })->toArray();
        
        return $res;
        

    }
    public function subCategories($request,$query, $categories) {

        $onlyEncryptedIds = array_column($categories, 'id');
        $filteredIds = $this->applyFilters($query, $request, 'subcategory');
        // Decrypt each encrypted category ID
        $onlyIds = array_map(function ($encryptedId) {
            return decryptParam($encryptedId);
        }, $onlyEncryptedIds);
        
        $subCategories = Category::where('type', 'b2b')
            ->whereIn('parent_id', $onlyIds)  
            ->where('status', true)
            ->withCount(['subCategoryProducts as product_count' => function ($query) use ($filteredIds) {
                $query->whereNull('deleted_at')
                    ->where('status', 'approved')
                    ->whereIn('id', $filteredIds);
            }])
            ->orderBy('product_count', 'desc')->get(['id', 'name']);
        
        $res = $subCategories->map(function ($category) {
            return [
                'id' => encryptParam($category->id),
                'name' => $category->name,
                'product_count' => $category->product_count ?? 0,
            ];
        })->toArray();
        
        return $res;
    }
    public function foams($request,$query) {
        $res = [];

        $filteredIds = $this->applyFilters($query, $request, 'foam');

        $foams = DosageForm::withCount(['products as product_count' => function ($query) use ($filteredIds) {
                $query->whereNull('deleted_at')
                    ->where('status', 'approved')
                    ->whereIn('id', $filteredIds);
            }])
            ->orderBy('product_count', 'desc')->get(['id', 'name']);
        
        $res = $foams->map(function ($foam) {
            return [
                'id' => encryptParam($foam->id),
                'name' => $foam->name,
                'product_count' => $foam->product_count ?? 0,
            ];
        })->toArray();

        return  $res;
    }

    public function browserProductHistory(Request $request) : JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $zone = getClinicZone();
            $perPage = $request->per_page ?? 10;
            $res = '';
            
            $products = BrowserProductHistory::query()
             ->whereExists(function ($query) use ($authId,$request) {
                $query->select(DB::raw(1))
                    ->from('products_relation as pr')
                    ->join('pc_details as pd', function ($join) {
                            $join->on('pr.user_id', '=', 'pd.user_id')
                                ->where('pd.is_restricted', false)
                                ->orWhereExists(function ($subquery) {
                                    $subquery->select(DB::raw(1))
                                            ->from('clinic_pharma_suppliers as cps')
                                            ->whereColumn('cps.pc_id', 'pd.user_id')
                                            ->where('cps.clinic_id', Auth::user()->id) 
                                            ->where('cps.status', 'approved');
                                });
                        })
                    ->join('users as u', 'u.id', '=', 'pr.user_id')
                    ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                    ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                    ->whereColumn('pr.product_id', 'browser_product_histories.product_id')
                    ->whereNull('pr.deleted_at')

                    ->where(function ($query) {
                        $query->where(function ($q) {
                            // For fixed and bonus, east and west prices must be present
                            $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                            ->whereNotNull('prp.east_zone_price')
                            ->where('prp.east_zone_price', '>', 0)
                            ->whereNotNull('prp.west_zone_price')
                            ->where('prp.west_zone_price', '>', 0);
                        })->orWhere(function ($q) {
                            // For tier, all 6 tier prices must be present
                            $q->where('pr.price_type', 'tier')
                            ->whereNotNull('prp.east_tier_1_base_price')
                            ->where('prp.east_tier_1_base_price', '>', 0);
                        });
                    })
                     ->where(function ($query) {
                        $query->where(function ($q) {
                            $q->where('prs.is_batch_wise_stock', false)
                            ->where(function ($expiry) {
                                $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                            })
                            ->orWhere(function($batchWise) {
                                $batchWise->where('prs.is_batch_wise_stock', true)
                                ->whereExists(function($query) {
                                    $query->select(DB::raw(1))
                                            ->from('products_batch')
                                            ->whereColumn('products_batch.product_id', 'pr.product_id')
                                            ->whereColumn('products_batch.user_id', 'pr.user_id')
                                            ->where(DB::raw("DATE(products_batch.expiry_date)"), '>=', DB::raw('CURRENT_DATE'))
                                            ->where('products_batch.available_stock', '>', 0);
                                });
                            });
                        });
                    })
                    ->where('pr.pc_approval', true)
                    ->where('pr.admin_approval', true);
                
            })
            
            ->where('browser_product_histories.user_id', $authId)
            ->orderBy('browser_product_histories.updated_at', 'desc')
            ->pluck('browser_product_histories.product_id')
            ->toArray();

                                
            if($products)
            { 
                request()->merge(['products' => $products]);

                $query = $this->productRepository->baseQuery($request,$zone,$authId); 
                // Clone the query for count

            $idsString = implode(',', $products); // Convert array to comma-separated string

            $query->orderByRaw("ARRAY_POSITION(STRING_TO_ARRAY(?, ',')::bigint[], products.id)", [$idsString]);


                $countQuery = clone $query;
                $total = $countQuery->getCountForPagination();

                // Pagination and fetching results
                $res = $perPage == 'all' ? $query->get() : $query->paginate($perPage);
            }
            
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_list'),
                'data' => [
                    'total' => $res ? $total : 0,
                    'list' => $res ?  new CustomCollection($res, ProductResource::class) : [],
                ],
            ], Response::HTTP_OK);

        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function topProducts(Request $request) : JsonResponse
    {
        try{
            $userClinicAccountTypeId = Auth::user()->clinicData->clinic_account_type_id ?? null;
            $auth = Auth::user();
            $zone = $auth->clinicDetails->zone;
            $perPage = $request->per_page ?? 10;
            $query =  DB::table('order_products')
                        ->join('products', 'products.id', '=', 'order_products.product_id')
                        ->join('categories as category', 'category.id', '=', 'products.category_id')
                        ->join('brands', 'products.brand_id' , '=', 'brands.id' )
                        ->join('units', 'products.unit_id', '=', 'units.id' )
                        ->join('generic_names', 'products.generic_name_id', '=', 'generic_names.id' )
                        ->leftJoin(DB::raw("LATERAL (
                            SELECT m1.id, m1.file_name
                            FROM media m1
                            WHERE m1.id = products.default_image_id::bigint
                                OR (
                                    products.default_image_id IS NULL 
                                    AND m1.model_id = products.id
                                )
                            ORDER BY m1.id ASC
                            LIMIT 1
                        ) as media"), DB::raw('true'), '=', DB::raw('true'))
                        ->leftJoin('favourites', function ($join) {
                            $join->on('favourites.product_id', '=', 'products.id')
                                ->where('favourites.user_id', '=', Auth::id()); 
                        })
                        ->whereExists(function ($query) use ($request) {
                            $query->select(DB::raw(1))
                                ->from('products_relation as pr')
                                ->join('pc_details as pd', function ($join) {
                                        $join->on('pr.user_id', '=', 'pd.user_id')
                                            ->where('pd.is_restricted', false)
                                            ->orWhereExists(function ($subquery) {
                                                $subquery->select(DB::raw(1))
                                                        ->from('clinic_pharma_suppliers as cps')
                                                        ->whereColumn('cps.pc_id', 'pd.user_id')
                                                        ->where('cps.clinic_id', Auth::user()->id) 
                                                        ->where('cps.status', 'approved');
                                            });
                                    })
                                ->join('users as u', 'u.id', '=', 'pr.user_id')
                                ->join('product_relation_prices as prp', 'pr.id', '=', 'prp.product_relation_id')
                                ->join('product_relation_stocks as prs', 'pr.id', '=', 'prs.product_relation_id')
                                ->whereColumn('pr.product_id', 'products.id')
                                ->whereNull('pr.deleted_at')

                                ->where(function ($query) {
                                    $query->where(function ($q) {
                                        // For fixed and bonus, east and west prices must be present
                                        $q->whereIn('pr.price_type', ['fixed', 'bonus'])
                                        ->whereNotNull('prp.east_zone_price')
                                        ->where('prp.east_zone_price', '>', 0)
                                        ->whereNotNull('prp.west_zone_price')
                                        ->where('prp.west_zone_price', '>', 0);
                                    })->orWhere(function ($q) {
                                        // For tier, all 6 tier prices must be present
                                        $q->where('pr.price_type', 'tier')
                                        ->whereNotNull('prp.east_tier_1_base_price')
                                        ->where('prp.east_tier_1_base_price', '>', 0);
                                    });
                                })
                                ->where(function ($query) {
                                    $query->where(function ($q) {
                                        $q->where('prs.is_batch_wise_stock', false)
                                        ->where(function ($expiry) {
                                            $expiry->where('prs.expiry_date', '>=', DB::raw('CURRENT_DATE'));
                                        })
                                        ->orWhere(function($batchWise) {
                                            $batchWise->where('prs.is_batch_wise_stock', true)
                                            ->whereExists(function($query) {
                                                $currentDate = date('Y-m-d');
                                                $query->select(DB::raw(1))
                                                        ->from('products_batch')
                                                        ->whereColumn('products_batch.product_id', 'pr.product_id')
                                                        ->whereColumn('products_batch.user_id', 'pr.user_id')
                                                        ->where(DB::raw("DATE(products_batch.expiry_date)"), '>', $currentDate)
                                                        ->where('products_batch.available_stock', '>', 0);
                                            });
                                        });
                                    });
                                })
                                ->where('pr.pc_approval', true)
                                ->where('pr.admin_approval', true);
                            
                        })
                        ->whereNotExists(function ($existsQuery) use ($userClinicAccountTypeId) {
                            $existsQuery->select(DB::raw(1))
                                    ->from('category_clinic_account_type')
                                    ->whereColumn('category_clinic_account_type.category_id', 'category.id')
                                    ->where('category_clinic_account_type.clinic_account_type_id', '=', $userClinicAccountTypeId);
                        })
                        ->select('products.*',
                            'units.short_form as unit_name',
                            'brands.name as brand_name',
                            'generic_names.name as generic_name',
                            DB::raw('SUM(order_products.quantity) as total_quantity'),
                            DB::raw('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END AS is_fav'),

                            'media.file_name as media_file_name',
                            'media.id as media_id',
                        )
                        ->whereNull('products.deleted_at') 
                        ->addSelect([
                            'lowest_price' => DB::raw("(
                            SELECT MIN(
                                CASE 
                                    WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                                    WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                                    WHEN pr.price_type = 'tier' THEN LEAST(
                                        COALESCE(prp.{$zone}_tier_1_base_price, 999999), 
                                        COALESCE(prp.{$zone}_tier_2_base_price, 999999), 
                                        COALESCE(prp.{$zone}_tier_3_base_price, 999999)
                                    )
                                END
                            )
                            FROM products_relation pr
                            JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                            WHERE pr.product_id = products.id)"
                            ),

                            'highest_price' => DB::raw("(
                            SELECT MAX(
                                CASE 
                                    WHEN pr.price_type = 'fixed' THEN prp.{$zone}_zone_price
                                    WHEN pr.price_type = 'bonus' THEN prp.{$zone}_zone_price
                                    WHEN pr.price_type = 'tier' THEN GREATEST(
                                        COALESCE(prp.{$zone}_tier_1_base_price, 0), 
                                        COALESCE(prp.{$zone}_tier_2_base_price, 0), 
                                        COALESCE(prp.{$zone}_tier_3_base_price, 0)
                                    )
                                END
                            )
                            FROM products_relation pr
                            JOIN product_relation_prices prp ON prp.product_relation_id = pr.id
                            JOIN product_relation_stocks prs ON prs.product_relation_id = pr.id
                            WHERE pr.product_id = products.id AND pr.deleted_at IS NULL)"
                            )
                
                        ])
                        ->groupBy(
                            'products.id',
                            'units.short_form',
                            'brands.name',
                            'generic_names.name',
                            'favourites.id',
                            'media.file_name',
                            'media.id',
                            )
                        ->orderByRaw('SUM(order_products.quantity) DESC');

            // $total = DB::table('order_products')
            //         ->join('products', 'products.id', '=', 'order_products.product_id')
            //         ->selectRaw('COUNT(DISTINCT products.id) as aggregate')
            //         ->value('aggregate');
                    $total = $query->getCountForPagination();
            $res = $perPage == 'all' ? $query->get() : $query->paginate($perPage);

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.product_list'),
                'data' => [
                    'total' => $total,
                    'list' => new CustomCollection($res, ProductResource::class)
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}
