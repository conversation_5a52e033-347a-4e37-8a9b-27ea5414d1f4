<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\SupportMessageDetailResource;
use App\Notifications\Api\SupportTicketNotification;
use App\Repositories\Api\Clinic\OnboardingRepository;
use App\Http\Requests\Api\Clinic\SupportMessageRequest;
use App\Http\Resources\V1\Clinic\OrderResource;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Http\Requests\Api\Clinic\HelpSupportRequest;
use App\Http\Resources\V1\Clinic\HelpSupportResource;
use App\Notifications\Api\NewMessageNotification;
use App\Http\Resources\CustomCollection;
use App\Http\Controllers\Controller;
use App\Models\Media;
use Illuminate\Support\Facades\Auth;
use App\Models\SupportTicketMessage;
use App\Models\Order;
use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;
use App\Jobs\SupplierNotificationJob;
use App\Mail\FacilitySelfSupportTicketMail;
use App\Mail\SupportTicketMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;


class SupportController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * AccountTypeController constructor.
     *
     * @param  SupportTicket  $model
     */
    public function __construct(protected OnboardingRepository $onboardingRepository)
    {
        $this->model = new SupportTicket();
        $this->onboardingRepository = $onboardingRepository;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $auth  = Auth::user();
            $perPage = $request->per_page && is_numeric($request->per_page) ? $request->per_page : 10;
            $data = $this->model->where('sender_id' , $auth->id)->orderBy('updated_at', 'desc')->paginate($perPage);
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.list'),
                'data' =>  [
                    'total' => $data->total(),
                    'list' => new CustomCollection($data, HelpSupportResource::class),
                ]
                
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function store(HelpSupportRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();
            $auth  = Auth::user();
            $data = $request->all();
            $receiverId = isset($data["receiver_id"]) && !empty($data["receiver_id"]) ?
                        decryptParam($data["receiver_id"]) : null; // need to ad dynamic
                $redirectUrl = 'pc';
            if(!$receiverId){
                $receiverId = Role::where('name', 'Super Admin')->pluck('id')->first();
                $data['is_full_order'] = true;
                $redirectUrl = 'admin';
            }

            $orderId = decryptParam($data["order_id"]);

            $exists = SupportTicket::where('receiver_id', $receiverId)
                ->where('order_id', $orderId)
                ->exists();

            if ($exists) {
                $response = response()->json([
                    'success' => false,
                    'message' => __('api.clinic.support.ticket_exists'),
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }else{
                $ticket = $this->model->create([
                    "sender_id"  => $auth->id,
                    "receiver_id"  => $receiverId,
                    "order_id"  => $orderId,
                    "category_id"  => decryptParam($data["category_id"]),
                    "name"  => $auth->name,
                    "email"  => $auth->email,
                    "subject"  => $data['subject'],
                    "description"  => $data['description'],
                    "is_full_order"  => $data['is_full_order'] ?? false,
                    "status"  => "open",
                ]);
                
                /* image upload */
                if ($request->hasFile('image')) {
                    $files = $request->file('image');
                    $folderPath = config('constants.api.media.support_ticket') . $ticket->id . '/';
                    foreach ($files as $key => $file) {
                         $fileName =  uploadFile($file, $folderPath);
                        Media::insert([
                            "model_type"            => 'App\Models\SupportTicket',
                            "model_id"              => $ticket->id,
                            "uuid"                  => Str::uuid()->toString(),
                            "collection_name"       => 'support-ticket-images',
                            "name"                  => $fileName,
                            "file_name"             => $fileName,
                            "mime_type"             => $file->getMimeType(),
                            "disk"                  => config('filesystems.default'),
                            "conversions_disk"      => config('filesystems.default'),
                            "size"                  => $file->getSize(),
                            "manipulations"         => json_encode([]),
                            "custom_properties"     => json_encode([]),
                            "generated_conversions" => json_encode([]),
                            "responsive_images"     => json_encode([]),
                            "order_column"          =>  1,
                            "created_at"            => now(),
                            "updated_at"            => now(),
                        ]);
                    }
                }
                $response = response()->json([
                    'success' => true,
                    'message' => __('api.clinic.support.create'),
                ], Response::HTTP_CREATED);

                $receiver = User::find($receiverId);

                //Facility received confirmation mail
                Mail::to($auth->email)->send(new FacilitySelfSupportTicketMail($ticket));

                //Main receiver send mail and notification
                Mail::to($receiver->email)->send(new SupportTicketMail($ticket, $redirectUrl, $receiver));
                $receiver->notify(new SupportTicketNotification($receiver->id,$redirectUrl,$ticket->id));
                 
                //For sub user send notification and mail
                SupplierNotificationJob::dispatch($receiverId, $redirectUrl, $ticket->id,null,null,true);

                DB::commit();
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }

    public function messageDetail(Request $request,$id): JsonResponse
    {
        try {
            $perPage = $request->per_page && is_numeric($request->per_page) ? $request->per_page : 10;

            $id = decryptParam($id);
            $ticket = $this->model->find($id);
            if (!$ticket) {
                return response()->json(['success' => false, 'message' => 'Ticket not found'], 404);
            }

            $messages = $ticket->messages()
                        ->with('media')
                        ->orderBy('created_at', 'desc')
                        ->paginate($perPage);
            
            // **Mark all unread messages in this ticket as read**
            $ticket->messages()->where('is_read', false)->update(['is_read' => true]);
           // $ticket->setAttribute('unread_message', $this->onboardingRepository->unReadMsgCount(Auth::user()->id));

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.message_detail'),
                'data' =>  new SupportMessageDetailResource($ticket, $messages)
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }
    public function messageSave(SupportMessageRequest $request): JsonResponse
    {
        try {
            $data = $request->all();

            $supportTicketId = decryptParam($data["support_ticket_id"]);
            $supportTicket = SupportTicket::with('receiver')->find($supportTicketId);
            $receiver = $supportTicket->receiver;

            if($supportTicket->status == 'closed'){
                return response()->json([
                    'success' => true,
                    'message' => __('api.clinic.support.unable_to_send_msg'),
                ], Response::HTTP_OK);
            }
            $message = SupportTicketMessage::create([
                'support_ticket_id' => decryptParam($data["support_ticket_id"]),
                'from_id' => Auth::user()->id,
                'message' => $data['text'] != 'null' ? $data['text'] : '',
                'is_read' => false,
            ]);

            if ($request->hasFile('files')) {
                $folderPath = config('constants.api.media.support_ticket') . $supportTicket->id . '/';
                foreach ($request->file('files') as $key => $file) {

                    $fileName =  uploadFile($file, $folderPath);
                    $mediaData[] = [
                        "model_type"            => 'App\Models\SupportTicketMessage',
                        "model_id"              => $message->id,
                        "uuid"                  => Str::uuid()->toString(),
                        "collection_name"       => 'support-ticket-images',
                        "name"                  => $fileName,
                        "file_name"             => $fileName,
                        "mime_type"             => $file->getMimeType(),
                        "disk"                  => config('filesystems.default'),
                        "conversions_disk"      => config('filesystems.default'),
                        "size"                  => $file->getSize(),
                        "manipulations"         => json_encode([]),
                        "custom_properties"     => json_encode([]),
                        "generated_conversions" => json_encode([]),
                        "responsive_images"     => json_encode([]),
                        "order_column"          => $key + 1,
                        "created_at"            => now(),
                        "updated_at"            => now(),
                    ];
                }
                Media::insert($mediaData);
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.sent_message'),
            ], Response::HTTP_OK);
            
            $redirectUrl = $supportTicket->is_full_order ? 'admin' : 'pc';
           
            //For sub user
            SupplierNotificationJob::dispatch($receiver->id, $redirectUrl, $supportTicketId,null,null);

            //For main user
            $receiver->notify(new NewMessageNotification($receiver->id, $redirectUrl,$supportTicketId));
            
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }


    public function orderList(): JsonResponse
    {
        try {
            $data = Order::whereNot('status','cancelled')->get();
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.details'),
                'data' => new CustomCollection($data, OrderResource::class)
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function closedTicket($id) : JsonResponse
    {
        try {
            $id = decryptParam($id);
            $data = $this->model->find($id);
            $data->update(['status' => 'closed']);
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.support.ticket_closed')
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;

    }
}