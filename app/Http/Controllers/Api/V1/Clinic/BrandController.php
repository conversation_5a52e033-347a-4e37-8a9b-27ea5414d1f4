<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\BrandResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use Illuminate\Http\JsonResponse;
use App\Models\Brand;
use Carbon\Carbon;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Exception;

class BrandController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Brand Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * BrandController constructor.
     * @param Brand $model
     */
    public function __construct()
    {
        $this->model = new Brand();
    }

    public function index() : JsonResponse
    {
        try {
            $res = $this->model
                ->where('status',true)
                ->get();

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.Brand_list'),
                'data' => new CustomCollection($res, BrandResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}