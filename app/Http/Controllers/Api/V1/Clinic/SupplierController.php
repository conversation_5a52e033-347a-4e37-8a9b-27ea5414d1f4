<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\SupplierResource;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Models\PcDetail;
use Exception;

class SupplierController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * AccountTypeController constructor.
     *
     * @param  PcDetail  $model
     */
    public function __construct()
    {
        $this->model = new PcDetail;
    }

    public function index(Request $request): JsonResponse
    {
        try { 
            //check any where use or not so updated it for both
            $perPage = $request->per_page ?? 10;
            $type = $request->type ?? '';
            $query = $this->model->with(['user','fav','companyType']);
            if(Auth::check() && $type == 'potential') {
                $authId = Auth::id();
                $query->with(['clinicPharmaSupplier'])->where('is_restricted', true);  
            }elseif(Auth::check() && $type == 'global'){
                $query->where('is_restricted', false);
            }

            if($request->is_featured){
                $query->where('is_featured', true);
            }
            if ($request->has('search')) {
                $searchTerm = $request->input('search');
                $query->where(function ($q) use ($searchTerm) {
                    // Search in company_name
                    $q->where('company_name', 'ILIKE', '%' . $searchTerm . '%')
                      // Also search in business_name when company_name is blank and company_type is "Sole Proprietary"
                      ->orWhere(function ($subQuery) use ($searchTerm) {
                          $subQuery->where(function ($nameQuery) {
                              $nameQuery->whereNull('company_name')
                                       ->orWhere('company_name', '');
                          })
                          ->whereHas('companyType', function ($typeQuery) {
                              $typeQuery->where('name', 'Sole Proprietary');
                          })
                          ->where('business_name', 'ILIKE', '%' . $searchTerm . '%');
                      });
                });
            }

            $pcCount = $query->count();
            if($perPage == 'all') {
                $res = $query->get();
            }else{
                $res = $query->paginate($perPage);
            }
            if($type == 'global'){
                $res->getCollection()->transform(function ($supplier) {
                    $supplier->is_fav = $supplier->fav !== null;
                    return $supplier;
                });
            }
                

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.supplier_list'),
                'data' => [
                    'total' => $pcCount,
                    'list' => new CustomCollection($res, SupplierResource::class)
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }
}
