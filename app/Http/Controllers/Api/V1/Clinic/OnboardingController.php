<?php

namespace App\Http\Controllers\Api\V1\Clinic;


use App\Http\Requests\Api\Clinic\ShippingAddressRequest;
use App\Http\Requests\Api\Clinic\CertificateRequest;
use App\Repositories\Api\Clinic\OnboardingRepository;
use App\Http\Requests\Api\Clinic\OnboardingRequest;
use App\Http\Resources\V1\Clinic\OnboardingResource;
use App\Http\Requests\Api\Clinic\PersonInChargeRequest;
use App\Http\Requests\Api\Clinic\BasicInfoRequest;
use App\Http\Requests\Api\Clinic\AddressRequest;
use App\Http\Requests\Api\Clinic\DocumentRequest;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Nnjeim\World\Models\State;
use App\Models\AccountType;
use App\Models\ClinicAddCart;
use App\Models\ClinicDetail;
use App\Models\TermCondition;
use App\Models\User;
use Illuminate\Http\Request;
use App\Models\UserAddress;
use Carbon\Carbon;

class OnboardingController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Onboarding Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * OnboardingController constructor.
     *
     * @param  AccountType  $model
     */
    public function __construct(protected OnboardingRepository $onboardingRepository)
    {
        $this->model = new User(); 
        $this->onboardingRepository = $onboardingRepository;
    }

    public function store(OnboardingRequest $request)
    {
        $res = $this->onboardingRepository->save($request);
        
        return response()->json([
            'success'   => $res['success'],
            'message'   => $res['message'],
            'data'      => isset($res['data']) ? $res['data'] : null,
        ], $res['code']);
    }

    public function detail()
    {
        try {
            $authId = Auth::user()->id;

            $onboarding = ClinicDetail::where('user_id', $authId)
                        ->with([
                            'billingAddress',
                            'borangCertificates',
                            'certificates',
                            'mmcCertificates',
                            'apcCertificates',
                            'arcCertificates',
                            'licenseCertificates'
                        ])
                        ->first();
            if (!$onboarding) {

                return  response()->json([
                    'success'   => true,
                    'message'   => __('api.clinic.onboarding_not_found'),
                    'data' => null
                ], Response::HTTP_OK);
            }

            $onboarding->shippingAddress = $this->onboardingRepository->shippingAddress($authId);
            
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.onboarding_details'),
                'data' => new OnboardingResource($onboarding)
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function getClinicDetail() : JsonResponse
    {
        try {
            $auth = Auth::user();
            $authId = $auth->id;
            $data = [];
            $onboarding = ClinicDetail::where('user_id', $authId)
            ->whereHas('clinicAccountType', function ($query) {
                $query->whereNotIn('name', ['Pharmacy','Hospital']);
            })
            ->where('apc_certificate_expired_date', '<', Carbon::now()->year)
            ->exists();
            
            $termConditionStatus = $this->onboardingRepository->termConditions($authId);
            
            if(!$termConditionStatus) {
                $terms = TermCondition::where('status', true)->first();
                if($terms)
                    $data = [
                        'is_apc_certificate_expired' => $onboarding,
                        'is_terms_accepted' => false,
                        'terms_data' => $terms ? $terms->meta_description : null,
                        'terms_id' => $terms ? encryptParam($terms->id) : null
                    ];
            }else{
                $data = [
                    'is_apc_certificate_expired' => $onboarding,
                    'is_terms_accepted' => $termConditionStatus ? true : false,
                ];
            }
            
            $address = UserAddress::where('user_id', $authId)->where('is_default', true)->first();
            if(!$address) {
                $shippingId = $auth->clinicData->shipping_addresses_id;
                $address =  UserAddress::find($shippingId);
            }
            $data['address'] = [
                'id' => $address ? encryptParam($address->id) : null,
                'postal_code' => $address ? $address->postal_code : null,
                'country_name' => $address ? $address->country->name : null,
                'nick_name' => $address ? $address->nick_name : null,
            ];

            $data['unread_message'] = $this->onboardingRepository->unReadMsgCount($authId);
            $data['cart_count'] = ClinicAddCart::where('user_id', $authId)->count();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.acp_certificate_status'),
                'data' => $data,
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }
    
    public function apcCertificateUpdate(CertificateRequest $request): JsonResponse
    {

        $res = $this->onboardingRepository->certificateUpdate($request);
        
        return response()->json([
            'success'   => $res['success'],
            'message'   => $res['message'],
            'data'      => isset($res['data']) ? $res['data'] : null,
        ], $res['code']);
    }


    public function shippingAddresses() : JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            
            $addresses = $this->onboardingRepository->shippingAddress($authId,false);

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.shipping_address'),
                'data' => $addresses,
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }
    public function saveShippingaddress(ShippingAddressRequest $request) : JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $data = $request->validated();

            $values = [
                'address_1' => $data['address_1'],
                'address_2' => $data['address_2'] ?? null,
                'postal_code' => $data['postal_code'],
                'country_id' => decryptParam($data['country_id']),
                'state_id' => decryptParam($data['state_id']),
                'city_id' => decryptParam($data['city_id']),
                'nick_name' => $data['nick_name'] ?? null,
                'address_type'=>'shipping',
                'is_onboarding'=>true,
                'is_default'=>false,
                'user_id'=>$authId,
                'is_requested' => isset($data['is_requested']) ? true : false,
                'is_approved' => isset($data['is_requested']) ? false : true,
                'status' => isset($data['is_requested']) ? 'pending' : "approved"
            ];

            if(isset($data['is_requested'])) {
                $msg = __('api.clinic.request_shipping_address');
            }else{
                $msg = __('api.clinic.save_shipping_address');
            }
            UserAddress::create($values);
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.save_shipping_address'),
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function setDefaultAddress($id) : JsonResponse
    {
        try {
            $id = decryptParam($id);
            $authId = Auth::user()->id;

            $address = UserAddress::where('id',$id)->where('user_id',$authId)->first();
            if(!$address){
                $response  =  response()->json([
                    'success'   => false,
                    'message'   => __('api.clinic.address_not_found'),
                ], Response::HTTP_NOT_FOUND);
            }
            UserAddress::where('user_id', $authId)->where('id', '!=', $id)->update(['is_default' => 0]);
            $address->is_default = 1;// other should be false
            $address->save();
            if($address->state_id) {
                $zone = State::where('id', $address->state_id)->pluck('zone')->first();
            }
            ClinicDetail::where('user_id', $authId)->first()->update([
                'shipping_addresses_id' => $id,
                'zone' =>  $zone ?? null,
            ]);

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.set_default_address'),
            ], Response::HTTP_OK);
            
        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function basicInfoUpdate(BasicInfoRequest $request) : JsonResponse
    {
            $res = $this->onboardingRepository->basicInfo($request,Auth::user()->id);
            return response()->json([
                'success'   => $res['success'],
                'message'   => $res['message'],
            ], $res['code']);

    }
    public function addressUpdate(AddressRequest $request) : JsonResponse
    {
            $authId = Auth::user()->id;
            
            $res = $this->onboardingRepository->addressUpdate($request,$authId);
            
            return response()->json([
                'success'   => $res['success'],
                'message'   => $res['message'],
            ], $res['code']);
    }
    public function personInChargeUpdate(PersonInChargeRequest $request) : JsonResponse
    {
            $res = $this->onboardingRepository->doctorInchargeUpdate($request);
            return response()->json([
                'success'   => $res['success'],
                'dc_signature' => $res['dc_signature'] ?? null,
                'message'   => $res['message'],
            ], $res['code']);
    }
    public function documentsUpdate(DocumentRequest $request) : JsonResponse
    {
        $res = $this->onboardingRepository->documentsUpdate($request);
        return response()->json([
            'success'   => $res['success'],
            'message'   => $res['message'],
        ], $res['code']);
    }

    public function profileUpload(Request $request): JsonResponse
    {
        $user = Auth::user();
        $folderPath = 'users';

        // Validate and get uploaded file
        $file = $request->file('image');
        if (!$file) {
            return response()->json([
                'success' => false,
                'message' => __('validation.user_profile.image_required'),
            ], Response::HTTP_BAD_REQUEST);
        }

        // Delete old image if exists
        if ($user->photo) {
            deleteImage($user->photo, $folderPath);
        }

        // Generate new filename and upload
        $fileName = 'profile_' . now()->timestamp . '_' . $user->id;
        $filePath = uploadFile($file, $folderPath, true, 100, 100, $fileName);

        // Update user profile photo
        $user->update(['photo' => $filePath]);

        return response()->json([
            'success' => true,
            'message' => __('api.user.profile_added'),
            'data'    => ['photo' => $filePath],
        ], Response::HTTP_OK);
    }
}
