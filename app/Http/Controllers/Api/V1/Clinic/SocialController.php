<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Clinic\SocialLoginRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Service\SocialService;
use Google_Client;
use App\Models\User;
use App\Models\ClinicDetail;
use App\Repositories\Api\UserRepository;
use App\Repositories\Api\ClinicRepository;
use App\Http\Resources\V1\UserResource;
use App\Mail\WelcomeFacilityMail;
use App\Service\StripeService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Google_Service_Oauth2;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\App;
use App\Jobs\SuperAdminNotificationJob;
use Illuminate\Support\Facades\Auth;

class SocialController extends Controller
{
    protected $socialService;
    protected $stripeService;
    protected $user;
    protected $clinic;
    

    public function __construct(SocialService $socialService, UserRepository $user, ClinicRepository $clinic,  StripeService $stripeService)
    {
        $this->socialService = $socialService;
        $this->user = $user;
        $this->clinic = $clinic;
        $this->stripeService = $stripeService;
    }



    public function socialLogin(SocialLoginRequest $request)
    {


        try {
            DB::beginTransaction();
            $payload = null;
            $data = $request->validated();
            if($data['social_type'] == 'google') {
                $clientId = env('GOOGLE_CLIENT_ID');

                // Initialize Google Client
                $client = new Google_Client(['client_id' => $clientId]);
                //$payload = $client->verifyIdToken($data['id_token']);
                $client->setAccessToken($data['id_token']);
                $oauthService = new Google_Service_Oauth2($client);
                $payload = $oauthService->userinfo->get();
                $socialId = isset($payload['sub']) ? $payload['sub'] : null;
                $firstName = $payload['given_name'] ?? Null;
                $lastName = $payload['family_name'] ?? Null;
                $name = $firstName .' '. $lastName;
            }
            if($data['social_type'] == 'apple') {
                $payload = $this->socialService->verifyIdToken($request->id_token);
                $socialId = isset($payload['sub']) ? $payload['sub'] : null;

                $firstName = $payload['first_name'] ?? Null;
                $lastName = $payload['last_name'] ?? Null;
                $name = $firstName .' '. $lastName;

            }

            if ($payload) {
                $email = $payload['email'];
                $userData = $this->user->getUserDetailByEmail($email);
                if ($userData) {

                    if(!$userData->hasRole('Clinic')) {
                        return response()->json([
                            'success'   => false,
                            'message'   => __('api.auth.email_exits'),
                        ], Response::HTTP_FORBIDDEN);
                    }
                    DB::commit();
                    return $this->createLoginToken($userData, $request,$data['social_type']);
                } else {
                    $accountId = userAccountType('clinic');
                    $array = [];
                    $array['status'] = true;
                    $array['email'] = $payload['email'];
                    $array['account_type_id'] = $accountId;

                    $array['name'] = $name;
                    $newUser = $this->user->create($array);
                    $newUser->assignRole('Clinic');
                    if($newUser){
                        $clientModel =  [];

                        $clientModel['social_id'] = $socialId;
                        $clientModel['social_type'] = $data['social_type'];
                        $clientModel['user_id'] = $newUser['id'];
                        $clientModel['referral_code'] = 'REF'.Carbon::now()->format('YmdHis').$newUser->id;
                        $clientModel['clinic_name'] = $name;
                        $clientModel['signature_type'] = false;

                        // $clientModel['clinic_account_type_id'] = $accountId;
                        $this->clinic->create($clientModel);
                        $response = $this->createLoginToken($newUser, $request,$data['social_type']);
                        //$this->stripeService->customerCreate($newUser);
                        SuperAdminNotificationJob::dispatch('new_facility',$newUser->id);   
 
                        Mail::to($newUser->email)->send(new WelcomeFacilityMail($newUser));
                    }


                    DB::commit();
                }
            }else{
                    $response  =  response()->json([
                        'success'   => false,
                        'message'   => __('api.auth.invalid_token'),
                    ], Response::HTTP_NOT_FOUND);
            }
            
        } catch (\Exception $e) {
            DB::rollBack();
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }


    public function socialAppleLogin(Request $request){

        $request->validate([
            'id_token' => 'required|string',
        ]);

        try {
            // Verify Apple ID Token
            $decodedToken = $this->socialService->verifyIdToken($request->id_token);
            // Extract user details
            $appleUserId = $decodedToken['sub'];
            $email = $decodedToken['email'] ?? null;
            $userData = $this->user->getUserDetailByEmail($email);

            if ($userData) {
                $token = $userData->createToken('API Token')->plainTextToken;

            } else {
                $array = [];
                $array['email'] = $payload['email'];
                $array['name'] = $payload['given_name'] .' '. $payload['family_name'];
                $userData = $this->user->create($array);
                $userData->assignRole('Clinic');
                if($userData){
                    $clientModel =  [];
                    $clientModel['social_id'] = $request->id_token;
                    $clientModel['social_type'] = 'Apple';
                    $clientModel['user_id'] = $userData['id'];
                    $this->clinic->create($clientModel);
                    $token = $userData->createToken('API Token')->plainTextToken;

                }
            }



            $response  = response()->json([
                'success'   => true,
                'message'   => __('api.auth.user_details'),
                'data' => [
                    'token' => $token,
                    'user' =>(new UserResource($userData)),
                ]
            ], Response::HTTP_OK);
         } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    private function createLoginToken(User $user,$request,$socialType): JsonResponse
    {
        try {
            $user->update([
                'timezone' => App::make('userTimezone')
            ]);
            $token = $user->createToken('API Token')->plainTextToken;
            $user->token = $token;
            $user->isAccountSelected = !empty($user->account_type_id) ? true : false;
            $request['user_id'] = $user['id'];

            $customerOnboarding = null;


            $user->clinicData->update(['social_type' => $socialType]);
            //register device token
            //$this->user->deviceToken()->create($request->all()); // When we use mobile device
            
            $response  = response()->json([
                'success'   => true,
                'message'   => __('api.auth.user_details'),
                'data' => [
                    'token' => $token,
                    'user' =>(new UserResource($user)),
                ]
            ], Response::HTTP_OK);

        } catch (\Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }

        return $response;

    }

    // Redirect user to Google Authentication
    public function redirectToGoogle()
    {
        return redirect()->away($this->googleService->getAuthUrl());
    }

    // Handle Google Callback
    public function handleGoogleCallback(Request $request)
    {
        $code = $request->get('code');

        if ($code) {
            $accessToken = $this->googleService->authenticate($code);

            // Save access token to database or session
            session(['google_access_token' => $accessToken]);

            return response()->json([
                'message' => 'Authentication successful',
                'access_token' => $accessToken,
            ]);
        }

        return response()->json(['error' => 'Authentication failed'], 400);
    }


}
