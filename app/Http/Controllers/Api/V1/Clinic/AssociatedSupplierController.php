<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Resources\V1\Clinic\AssociatedSuppliersResource;
use App\Http\Resources\V1\Clinic\RestrictedSuppliersResource;
use App\Notifications\AssociateSupplierNotification;
use App\Http\Requests\Api\Clinic\SupplierRequest;
use App\Http\Resources\CustomCollection;
use App\Models\ClinicPharmaSupplier;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use App\Mail\AssociateSupplierMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Models\PcDetail;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;

class AssociatedSupplierController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * AssociatedSupplierController constructor.
     * @param ClinicPharmaSupplier $model
     */
    public function __construct()
    {
        $this->model = new ClinicPharmaSupplier();
    }

    public function index(Request $request) : JsonResponse
    {
        try {

            //c2 added new line

            
            $perPage =$request->per_page ?? 10;
            $status =$request->status ?? null;


            $query = $this->model->with(['pcDetail'])
                ->where('clinic_id',Auth::user()->id);

            if ($request->has('search')) {
                $searchTerm = $request->input('search');
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('account_number', 'ILIKE', '%' . $searchTerm . '%')
                    ->orWhereHas('pcInfo', function ($q) use ($searchTerm) {

                        $q->where('company_name', 'ILIKE', '%' . $searchTerm . '%')
                      // Also search in business_name when company_name is blank and company_type is "Sole Proprietary"
                        ->orWhere(function ($subQuery) use ($searchTerm) {
                            $subQuery->where(function ($nameQuery) {
                                $nameQuery->whereNull('company_name')
                                    ->orWhere('company_name', '');
                            })
                            ->whereHas('companyType', function ($typeQuery) {
                                $typeQuery->where('name', 'Sole Proprietary');
                            })
                            ->where('business_name', 'ILIKE', '%' . $searchTerm . '%');
                        });
                    });
                });
            }
            if($status){
                $query->where('status', $status);
            }
            $total = $query->count();
            if($perPage == 'all') {
                $res = $query->get();

            }else{
                $res = $query->paginate($perPage);
            }
            
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.associated_pc_list'),
                'data' => [
                    'total' => $total,
                    'list' => new CustomCollection($res, AssociatedSuppliersResource::class)
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    public function save(SupplierRequest $request) : JsonResponse
    {
        try {
            DB::beginTransaction();
            $auth = Auth::user();
            $authId = $auth->id;

            $pcId = decryptParam($request->pc_id);
            $this->model->create([
                'clinic_id' => $authId,
                'pc_id' => $pcId,
                "status"=>"pending",
                'account_number' => $request->account_number ?? null,
                'is_open_account' => !$request->account_number ? true:false,
            ]);

            //send notification to pc and super admin
            $pcUser = User::find($pcId);
            if (!$request->account_number) {
                if ($pcUser) {
                    $pcUser->notify(new AssociateSupplierNotification($pcId));
                    Mail::to($pcUser->email)->send(new AssociateSupplierMail($pcUser));

                }
                
                $log = ' submitted a request with open account to add credit line supplier "';
            }else{
                $log = ' initiated a request to add credit line supplier "';
            }
            $pcName = pcCompanyName($pcUser->pcDetails);
            DB::commit();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.associated_pc_save'),
            ], Response::HTTP_CREATED);

            //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($this->model)
                ->useLog('Credit line supplier add request')
                ->withProperties([
                    'attributes' => [
                        'supplier_name' => $pcName,
                        'supplier_email' =>$pcUser->email
                    ]
                ])
                ->log($auth->name . $log  .ucfirst($pcName) . '"');
            //Activity Log End


        } catch (Exception $e) {
            DB::rollBack();
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    public function delete($id) : JsonResponse
    {
        try {
            $id = decryptParam($id);
            $pc = $this->model->find($id);
            $pc->delete();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.associated_pc_delete'),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function restrictedSuppliers(Request $request)  : JsonResponse
    {
        try {
            $authId = Auth::id();
            $state_id = decryptParam($request->state_id) ?? null;
            $pcs = PcDetail::with(['user'])
                            // ->where('is_restricted', true)
                             ->where('is_credit_line', true)
                            ->when($state_id, function ($q) use ($state_id) {
                                $q->whereHas('userAddress', function ($q) use ($state_id) { // Added $q->
                                    $q->where('state_id', $state_id);
                                });
                            })
                            ->whereDoesntHave('clinicPharmaSuppliers', function ($query) use ($authId) {
                                $query->where('clinic_id', $authId);
                            })
                            ->get();

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.restricted_pc_list'),
                'data' => new CustomCollection($pcs, RestrictedSuppliersResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

}