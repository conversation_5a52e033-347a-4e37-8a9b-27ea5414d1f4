<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use App\Http\Resources\V1\Clinic\AccountTypeResource;
use App\Models\BusinessType;
use App\Models\ClinicAccountType;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

class AccountTypeController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | AccountType Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;

    /**
     * AccountTypeController constructor.
     *
     * @param  ClinicAccountType  $model
     */
    public function __construct()
    {
        $this->model = new ClinicAccountType;
    }

    public function index(): JsonResponse
    {
        try {
            //c1 added new line 
            //c1.1 added new line
            
            $res = $this->model->where('status', 1)->get();

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.account_list'),
                'data' => new CustomCollection($res, AccountTypeResource::class),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }

    public function businessType()
    {
        try {
            $res = BusinessType::where('status', 1)->get();

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.business_type_list'),
                'data' => new CustomCollection($res, AccountTypeResource::class),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }

        return $response;
    }
}
