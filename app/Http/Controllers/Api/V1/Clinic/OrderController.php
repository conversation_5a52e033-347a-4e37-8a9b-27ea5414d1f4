<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Requests\Api\Clinic\ProductCheckoutRequest;
use App\Http\Requests\Api\Clinic\paymentHandlerRequest;
use App\Http\Resources\V1\Clinic\OrderDetailResource;
use App\Notifications\Api\OrderCancelPcNotification;
use App\Repositories\Api\Clinic\OrderRepository;
use App\Http\Resources\V1\Clinic\OrderResource;
use App\Http\Resources\CustomCollection;
use App\Http\Controllers\Controller;
use App\Jobs\OrderCancelPcMailJob;
use App\Jobs\PaymentFailMailJob;
use App\Mail\OrderCancelMail;
use Illuminate\Support\Facades\Mail;
use App\Models\ClinicAddCart;
use App\Models\ClinicCreditHistory;
use App\Models\DpharmaPoint;
use App\Models\Transaction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\Models\ProductRelation;
use App\Models\OrderProduct;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\SubOrder;
use App\Models\PcDetail;
use App\Models\Order;
use App\Models\Payout;
use Illuminate\Support\Facades\Log;
use App\Jobs\PaymentSuccessfulMailJob;
use App\Jobs\PaymentPendingMailJob;
use App\Service\ShippingService;
use App\Service\EcommerceService;
use App\Service\PayoutService;
use Carbon\Carbon;


class OrderController extends Controller
{
    private $model;
    protected $orderRepository;
    protected $ecommerceService;
    protected $payoutService;
    protected $shippingService;
   
    public function __construct()
    {
        $this->model = new Order();
        $this->ecommerceService = new EcommerceService();
        $this->shippingService = new ShippingService();
        $this->payoutService = new PayoutService();
        $this->orderRepository = new OrderRepository($this->model);
    }

    /**
     * Method index: Get all orders list with filters
     *
     * @param Request $request [explicit description]
     *
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $sort = $request->sort_by ?? 'id';
            $order = $request->order_by ?? 'desc';
            $perPage = $request->per_page ?? 'all';
            $query = $this->orderRepository->getOrderList($request);
            $total = $query->count();
           $query->orderBy($sort, $order);


           $orders = $perPage == 'all' ? $query->get() : $query->paginate($perPage);
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.list'),
                'data' =>  [
                    'total' => $total,
                    'list' => new CustomCollection($orders, OrderResource::class)
                ]
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function show($id): JsonResponse
    {
        try {
            $id = decryptParam($id);
            $order =  $this->model->where("id", $id)
                ->with([
                    "billingCity",
                    "billingState",
                    "billingCountry",
                    "shippingCity",
                    "shippingState",
                    "shippingCountry",
                    "subOrder.user",
                    "subOrder.orderProducts"])
                ->withCount(['subOrder', "orderProducts"])
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.order.order_not_found')
                ], Response::HTTP_NOT_FOUND);
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.detail'),
                'data' =>  new OrderDetailResource($order)
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function productCheckout(ProductCheckoutRequest $request) : JsonResponse
    {
        try {
            $data = $request->validated();
            $auth = Auth::user();
            $authId = $auth->id;
            $clinicData = $auth->clinicData;
            $billingAddress = $clinicData->billingAddress;
            $clinicPoints = DpharmaPoint::where('user_id',$authId)->latest()->pluck('balance')->first() ?? 0;
            $productData = $this->orderRepository->getSupplierProductPrice($data);
            if($productData['grandWithPayNowTotal'] == 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong'
                ]);
            }
            DB::beginTransaction();
            $shippingAddress = UserAddress::find(decryptParam($data['shipping_address_id']));

            $orderNumber = getOrderNumber();
            $zonePrefix = getClinicZone();
            $grandTotal = 0;
            $payAmount = 0;
            $shippingFee = 0;
            $appliedPoints = 0;
            $paymentDeliveryType = null;
            $now = now();
            $order =  Order::create([
                "order_number" => $orderNumber ,
                'user_id' => $authId,
                "amount" => 0,
                'status' => 'pending',
                'shipping_address_1' => $shippingAddress->address_1,
                'shipping_address_2' => $shippingAddress->address_2,
                'shipping_first_name' => $shippingAddress->first_name,
                'shipping_last_name' => $shippingAddress->last_name,
                'shipping_country_id' => $shippingAddress->country_id,
                'shipping_state_id' => $shippingAddress->state_id,
                'shipping_city_id' => $shippingAddress->city_id,
                'shipping_postal_code' => $shippingAddress->postal_code,
                'shipping_phone_code' => $shippingAddress->phone_code,
                'shipping_phone_number' => $shippingAddress->phone_number,

                'billing_address_1' => $billingAddress->address_1,
                'billing_address_2' => $billingAddress->address_2,
                'billing_first_name' => $billingAddress->first_name,
                'billing_last_name' => $billingAddress->last_name,
                'billing_country_id' => $billingAddress->country_id,
                'billing_state_id' => $billingAddress->state_id,
                'billing_city_id' => $billingAddress->city_id,
                'billing_postal_code' => $billingAddress->postal_code,
                'billing_phone_code' => $billingAddress->phone_code,
                'billing_phone_number' => $billingAddress->phone_number,
                'transaction_id' =>  null,
                'user_zone' => getClinicZone(),
                //'payment_status' => "pending",
                'zone' => $clinicData->zone
            ]);
            $activityLog = [];
            $totalSupEarnPoints = 0;
            foreach ($data['suppliers'] as $supKey =>  $supplier) {
                $supplierId = decryptParam($supplier['id']);
                $products = [];
                $paymentType = $supplier['payment_type'];
                $supplierData = PcDetail::where('user_id', $supplierId)->first();
                
                $subOrder =  SubOrder::create([
                    'order_id' => $order->id,
                    'user_id' => $supplierId,
                    'user_type' => 'pc',
                    'status' => 'pending',
                    'credit_line_status' => 'pending',
                    'payment_type' => $paymentType,
                    'payout_type' => $supplierData->commission_payout_option ?? null,
                    'cycle_type' => $supplierData->cycle_type ?? null,
                    'warehouse_type' => $supplierData->user->warehouseInfo->warehouse_type ?? null,
                    'delivery_days' =>  getClinicZone() == 'west' ? $supplierData->delivery_days_west : $supplierData->delivery_days,
                ]);
              
                $suppliersQuery = ClinicAddCart::where('user_id', Auth::id())
                                ->where('supplier_id', $supplierId);
                $supProds = $suppliersQuery->get();
                $supplierSubOrderValue = 0;
                $totalSupQty= 0;
                $productActivityLog = [];
                foreach ($supProds as $key => $supProd) {
                    $tierNumber = $supProd->tier_number;
                    
                    $supplierProd = $supProd->product->pcInfo
                                            ->where('user_id', $supplierId)
                                            ->where('product_id', $supProd->product_id)
                                            ->first();
                    $basePrice = $supplierProd->productRelationPrice;
                    $productPurchaseQty = (int) $supProd->quantity;

                    $pricePerQty = $supplierProd->price_type == 'fixed' || $supplierProd->price_type == 'bonus' ? $basePrice->{$zonePrefix.'_zone_price'} :
                                    $basePrice->{$zonePrefix.'_'.$tierNumber.'_base_price'};
                    $totalAmt = $pricePerQty * $productPurchaseQty;
                    $supplierSubOrderValue += $totalAmt;

                    //supplier product array
                    $products[] = $this->orderProducts($subOrder,$supProd,$supplierProd,$productPurchaseQty,$basePrice,$zonePrefix,$tierNumber,$totalAmt,$order,$now,$pricePerQty);
                    $cartMetaData = json_decode($supProd->meta_data);
                    $paymentDeliveryType = $cartMetaData->delivery_type;
                    $appliedPoints = $cartMetaData->applied_points ?? 0;
                    $shippingFee = $cartMetaData->shipping_fee ?? 0;
                    $totalSupQty +=$productPurchaseQty;

                    $productActivityLog[$key] = [
                        'Name' => $supProd->product->name,
                        'Quantity'=> $productPurchaseQty,
                        'Amount' => $totalAmt
                    ];
                }
                OrderProduct::insert($products);

                $supplierAmount = $supplierSubOrderValue + $shippingFee ;

                //Supplier point distribution
                $subAppliedPoints = $this->supplierPoints($appliedPoints,$supplierAmount,$productData['grandWithPayNowTotal'],$supplierSubOrderValue);
                $totalAmt = $supplierSubOrderValue + $shippingFee -  $subAppliedPoints;
                
                $supEarnPoints = 0;
                if($paymentType == 'pay_now' || $paymentType == 'pay_later') {
                   
                    $payAmount += $totalAmt;
                }elseif($paymentType == 'credit_line'){
                    try {
                        $creditAmount = ClinicCreditHistory::where('facility_id', $authId)
                                        ->where('supplier_id',$supplierId)
                                        ->latest()->first();
                        
                        if (!$creditAmount) {
                            throw new \Exception('Credit line not found for supplier: ' . $supplierId);
                        }
                        
                        // Validate credit availability
                        if ($creditAmount->remaining_amount < $totalAmt) {
                            throw new \Exception('Insufficient credit line balance for supplier: ' . $supplierId);
                        }
                        
                        ClinicCreditHistory::create([
                            "facility_id" => $authId,
                            "supplier_id" => $supplierId,
                            "debit_amount" => $totalAmt,
                            "remaining_amount" => $creditAmount->remaining_amount - $totalAmt,
                            "edit_credit" => 0,
                            "order_credit_used" => $creditAmount->order_credit_used + $totalAmt,
                            "total_credit_amount" => $creditAmount->total_credit_amount,
                            "reference_id" => $subOrder->id,
                            "reference_value" => 'SubOrder',
                            "action" =>'Order Place',
                        ]);
                        
                    } catch (\Exception $e) {
                        Log::error('Credit line processing failed', [
                            'order_id' => $order->id,
                            'sub_order_id' => $subOrder->id,
                            'supplier_id' => $supplierId,
                            'facility_id' => $authId,
                            'total_amount' => $totalAmt,
                            'error' => $e->getMessage()
                        ]);
                        
                        // Credit line failure should rollback the entire transaction
                        throw new \Exception('Credit line processing failed: ' . $e->getMessage());
                    }
                }
                $grandTotal += $totalAmt;

                $activityLog[$supKey] = [
                    'Name'          => pcCompanyName($supplierData),
                    'Payment Type'   => ucwords(str_replace('_', ' ', $paymentType)),
                    'Product Details' => $productActivityLog,
                    'Shipping Fee'   => $shippingFee,
                    'Applied Points' => $subAppliedPoints,
                    'Actual Price'   => $supplierSubOrderValue,
                    'Total Amount'      => round($totalAmt,2),
                ];

                $supEarnPoints = ($totalAmt * (getTierPercentage() / 100));
                $totalSupEarnPoints += $supEarnPoints;
                $subOrder->update([
                    'total_sub_order_value' => number_format($supplierSubOrderValue, 2, '.', ''),
                    'total_amount' => number_format($totalAmt, 2, '.', ''),
                    'total_dpharma_points_used' => number_format($subAppliedPoints, 2, '.', ''),
                    'earn_points' => number_format($supEarnPoints, 2, '.', ''),
                    'total_shipping_amount' => number_format($shippingFee, 2, '.', ''),
                    'payment_delivery_type' => $paymentDeliveryType,
                ]);
                $suppliersQuery->delete();
            }
            //$earningPoints =  round($grandTotal * (getTierPercentage() / 100));
            $order->update([
                'amount' => $grandTotal,
                'points' => number_format($totalSupEarnPoints,2, '.', '')
            ]);
            
            $hasPayNow = count(array_filter($data['suppliers'], function ($supplierPayType) {
                return $supplierPayType['payment_type'] === "pay_now" || $supplierPayType['payment_type'] === "pay_later";
            })) > 0;

            if($appliedPoints > 0){
                try {
                    DpharmaPoint::create([
                        'user_id'=> $authId ,
                        'description'=>'Redeem for order # '.$orderNumber,
                        'redeem'=> number_format($appliedPoints,2, '.', ''),
                        'reference_id' => $order->id,
                        'reference_value' => 'order',
                        'balance'=> number_format(($clinicPoints - $appliedPoints),2, '.', '')
                    ]);
                } catch (\Exception $e) {
                    Log::error('Point redemption failed during order creation', [
                        'order_id' => $order->id,
                        'user_id' => $authId,
                        'applied_points' => $appliedPoints,
                        'error' => $e->getMessage()
                    ]);
                    // This is critical - rollback the entire transaction
                    throw new \Exception('Point redemption failed. Please try again.');
                }
            }
            
            if(!$hasPayNow) {
                PaymentSuccessfulMailJob::dispatch($order, null, 'credit_line');
            }
            DB::commit();
            
            //e-commerce redirect url create
            $session = null;
            if($hasPayNow){
                if ($payAmount > 0) {
                    try {
                        $order->update([
                            'payment_status' => "init"
                        ]);
                        // Try to get payment session from ecommerce service
                        $session = $this->ecommerceService->store($request, $payAmount);
                    
                        if (isset($session['transactionNumber'])) {
                            $order->update(['ecommerce_tran_id' => $session['transactionNumber']]);
                        } else {
                            // If still no session, throw error
                            throw new \Exception('Failed to get payment session from payment gateway.');
                        }
                    } catch (\Exception $e) {
                        Log::error('Payment gateway error during checkout: ' . $e->getMessage(), [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'pay_amount' => $payAmount
                        ]);

                        // Rollback the order if payment gateway fails
                        DB::rollBack();

                        return response()->json([
                            'success' => false,
                            'message' => 'Payment processing failed. Please try again.',
                            'error' => $e->getMessage()
                        ], Response::HTTP_INTERNAL_SERVER_ERROR);
                    }
                }else{
                    $order->update([
                        'payment_status' => "paid"
                    ]);
                }
            }
          
            //mail send when payment type credit_line
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.placed'),
                "data" => [
                    'is_payment_request' => $payAmount > 0 ? true : false,
                    'order_id' => encryptParam($order->id),
                    'redirect_url' => $session['redirectUrl'] ?? null,
                    'transaction_number' => $session['transactionNumber'] ?? null,
                ]
            ], Response::HTTP_OK);

            //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($order)
                ->useLog('order_create')
                ->withProperties([
                    'attributes' => [
                        'amount' => $order->amount,
                        'order_number' => $order->order_number,
                        'points' => $order->points ?? 0,
                        'suppliers' => $activityLog
                    ]
                ])
                ->log('Order #'.$order->order_number.' Placed');
            //Activity Log End


        } catch (\Throwable $th) {
            DB::rollBack();
            
            Log::error('Order checkout failed: ' . $th->getMessage(), [
                'user_id' => $authId ?? null,
                'request_data' => $request->all(),
                'stack_trace' => $th->getTraceAsString()
            ]);
            
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function orderProducts($subOrder,$supProd,$supplierProd,$productPurchaseQty,$basePrice,$zonePrefix,$tierNumber, $totalAmt,$order,$now,$pricePerQty) {

        $commissionData = getCommissions($supplierProd,$totalAmt,$zonePrefix,$supplierProd->price_type,$tierNumber);
     
        return [
            "sub_order_id" => $subOrder->id,
            "product_id" => $supProd->product_id,
            "status" => 'pending',
            "quantity" =>  $productPurchaseQty,
            "price_per_qty" => $pricePerQty,
            "total_price" => $totalAmt,
            "tier_min_quantity" => $supplierProd->price_type == 'tier' ? $basePrice->{$zonePrefix.'_'.$tierNumber.'_min_quantity'} : null,
            "tier_max_quantity" => $supplierProd->price_type == 'tier' ? $basePrice->{$zonePrefix.'_'.$tierNumber.'_max_quantity'} : null,
            "tier_price" =>        $supplierProd->price_type == 'tier' ? $pricePerQty : null,

            "commission_type" => $commissionData['commission_type'] ?? null,
            "commission_value" => $commissionData['commission_value'] ?? 0,
            "total_commission" => $commissionData['total_commission'] ?? 0,

            "bonus_quantity_value" => $supplierProd->price_type == 'bonus' && !empty($tierNumber) ? $basePrice->{$zonePrefix.'_'.$tierNumber.'_quantity'}  : null,
            "bonus_quantity_free" => $supplierProd->price_type == 'bonus' && !empty($tierNumber) ? $basePrice->{$zonePrefix.'_'.$tierNumber.'_quantity_value'} : null,
            "bonus_final_qty" => $supplierProd->price_type == 'bonus' && !empty($tierNumber) ?
            floor($productPurchaseQty / $basePrice->{$zonePrefix.'_'.$tierNumber.'_quantity'}) * $basePrice->{$zonePrefix.'_'.$tierNumber.'_quantity_value'} : null,

            'order_id' => $order->id,
            'price_type' => $supplierProd->price_type,
            'price_type_value' => $supplierProd->price_type == 'fixed' ? null : $tierNumber,
            'created_at' => $now,
            'updated_at' => $now,
            'wholesale_pack_size' => $supplierProd->productRelationStock->stock_type == 'unit' ? 1 :   ($supplierProd->productRelationStock->wholesale_pack_size),
            'stock_type' => $supplierProd->productRelationStock->stock_type,
        ];
    }

    public function supplierPoints($appliedPoints,$supplierAmount,$grandTotal,$supplierSubOrderValue)
    {
         
        $subAppliedPoints = 0;
        if($appliedPoints > 0){
            $supplierPer = round(($supplierAmount / $grandTotal) * 100);
            $subAppliedPoints = ($appliedPoints * $supplierPer / 100);
        }
        if($subAppliedPoints > round($supplierSubOrderValue)) {
            throw new \Exception(trans('api.clinic.cart.applied_points_error'));
        }
        return $subAppliedPoints;
    }

    public function paymentHandler(paymentHandlerRequest $request)
    {
        try {
            $data = $this->ecommerceService->webhook($request);

            if (!$data['result']) {
                Log::error('Invalid payment response - no result data', ['data' => $data]);
                return response()->json([
                    'success' => false,
                    'order_id' => null,
                    'message' => __('api.clinic.order.payment_failed')
                ]);
            }

            $transactionResult = $data['result'];
            $order =  Order::where('ecommerce_tran_id', $transactionResult['transactionNumber'])->first();

            if (!$order) {
                Log::error('Order not found for transaction', [
                    'transaction_number' => $transactionResult['transactionNumber']
                ]);
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.order.order_not_found'),
                    'order_id' => null,
                ], Response::HTTP_NOT_FOUND);
            }

            $paymentHandling = $this->orderRepository->handlePaymentStatus($order, $transactionResult);
            if($order->payment_status != 'paid') {
                $this->orderRepository->createTransaction(
                    $order,
                    $transactionResult,
                    $paymentHandling['status'],
                    $paymentHandling['failureMessage'] ?? null
                );
            }

            $order->update(['payment_status' => $paymentHandling['status']]);

            

            return $paymentHandling['response'];
            
        } catch (\Throwable $th) {
            Log::error('Payment handler error: ' . $th->getMessage(), [
                'request_data' => $request->all(),
                'stack_trace' => $th->getTraceAsString()
            ]);
            return exceptionResponse($th->getMessage());
        }
    }

    public function productReOrder($orderId) : JsonResponse
    {
        DB::beginTransaction();
        try {
            $auth = Auth::user();
            $authId = $auth->id;
            $order = Order::with(['subOrder','subOrder.orderProducts'])
                            ->where('id', decryptParam($orderId))
                            ->where('user_id', $authId)
                            ->first();

            // Delete previous cart data
            ClinicAddCart::where('user_id', $authId)->delete();

            $cartData = [];
            // Fetch all product relations in bulk
            foreach($order['subOrder'] as $subOrder) {
                foreach($subOrder['orderProducts'] as $orderProduct) {
                    $priceType = $orderProduct->price_type;
                    $tierNumber = null;
                    $currentProduct = ProductRelation::where('user_id', $subOrder['user_id'])
                                                        ->where('product_id', $orderProduct['product_id'])
                                                        ->first();


                    $currentPriceType = $currentProduct->price_type;
                    $quantity = $priceType == 'bonus' ? $orderProduct->quantity - $orderProduct->bonus_quantity_free : $orderProduct->quantity;
                    if($quantity && $currentPriceType != 'fixed') {
                        $tierNumber = $this->orderRepository->getTierBasedQuantity($orderProduct['product_id'],$subOrder['user_id'],$auth->clinicDetails->zone,$quantity);
                    }
                  
                    $productId = $orderProduct['product_id'];
                    $cartData[] =  [
                        "user_id" => $authId,
                        "user_account_type_id" => userAccountType('facilities'),
                        'product_id' => $productId,
                        'supplier_id' => $subOrder['user_id'],
                        'quantity' => $quantity ,
                        'tier_number' => $tierNumber ?? null,
                        "created_at" => now(),
                        "updated_at" => now(),
                    ];
                }
            }

            // Bulk insert cart data
            if (!empty($cartData)) {
                ClinicAddCart::insert($cartData);
            }
            DB::commit();

            //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($order)
                ->useLog('re_order')
                ->withProperties([
                    'attributes' => [
                        'amount' => $order->amount,
                        'order_number' => $order->order_number
                    ]
                ])
                ->log('Order #'.$order->order_number.' re-order');
            //Activity Log End


            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.re_order'),
                'cart_count' => count($cartData) ?? 0
            ], Response::HTTP_OK);


        } catch (\Throwable $th) {
            DB::rollBack();
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function cancelOrder(Request $request , $orderId): JsonResponse
    {
        DB::beginTransaction();
        try {
            $auth = Auth::user();
            $authId = $auth->id;
            $returnPoints = 0;
            $orderId = decryptParam($orderId);
            $order = Order::where('id', $orderId)
                            ->where('user_id', $authId)
                            ->where('status', 'pending')
                            ->first();

            if(!$order) {
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.order.order_not_found'),
                ], Response::HTTP_NOT_FOUND);
            }

            $cancellationErrors = [];
            
            foreach($order->subOrder as $subOrder) {
                $returnPoints += $subOrder->total_dpharma_points_used;

                if($subOrder->payment_type == 'credit_line'){
                    try {
                        $creditAmount = ClinicCreditHistory::where('facility_id', $authId)
                                                ->where('supplier_id',$subOrder->user_id)
                                                ->latest()
                                                ->first();
                        
                        if(!$creditAmount){
                            throw new \Exception('Credit history not found for supplier: ' . $subOrder->user_id);
                        }

                        $orderAmt = $subOrder->total_amount; 
                        if($orderAmt > 0){
                            $remaining_amount = $creditAmount->remaining_amount;

                            ClinicCreditHistory::create([
                                "facility_id" => $authId,
                                "supplier_id" => $subOrder->user_id,
                                "credit_amount" => $subOrder->total_sub_order_value,
                                "debit_amount" => 0,
                                "remaining_amount" => $creditAmount->order_credit_used >= $creditAmount->total_credit_amount && 
                                $orderAmt == $creditAmount->order_credit_used
                                ? $creditAmount->total_credit_amount : ($creditAmount->order_credit_used <= $creditAmount->total_credit_amount ? $orderAmt+$remaining_amount : 0),
                                "edit_credit" => 0,
                                "order_credit_used" => $creditAmount->order_credit_used - $subOrder->total_sub_order_value,
                                "total_credit_amount" => $creditAmount->total_credit_amount,
                                "reference_id" => $subOrder->id,
                                "reference_value" => 'SubOrder',
                                "action" =>'Order cancel',
                            ]);
                        }
                        
                        
                    } catch (\Exception $e) {
                        $cancellationErrors[] = "Credit line reversal failed for supplier {$subOrder->user_id}: " . $e->getMessage();
                        Log::error('Credit line reversal failed during order cancellation', [
                            'order_id' => $order->id,
                            'sub_order_id' => $subOrder->id,
                            'supplier_id' => $subOrder->user_id,
                            'error' => $e->getMessage()
                        ]);
                    }
                } 

                // Send mail to PC with error handling
                try {
                    OrderCancelPcMailJob::dispatch($subOrder);
                    $subOrder->user->notify(new OrderCancelPcNotification($subOrder->user_id,$subOrder->id));
                } catch (\Exception $e) {
                    $cancellationErrors[] = "Failed to send cancellation notification to supplier {$subOrder->user_id}";
                    Log::error('Failed to send order cancellation notification', [
                        'order_id' => $order->id,
                        'sub_order_id' => $subOrder->id,
                        'supplier_id' => $subOrder->user_id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

           if($returnPoints > 0) {
                try {
                    $balancePoints = DpharmaPoint::where('user_id',$authId)->latest()->pluck('balance')->first() ?? 0;
                    
                    DpharmaPoint::create([
                        'user_id'=> $authId ,
                        'description'=>'order cancel - # '.$order->order_number,
                        'points'=> number_format($returnPoints,2, '.', ''),
                        'reference_id' => $order->id,
                        'reference_value' => 'order',
                        'balance'=> number_format(($balancePoints + $returnPoints),2, '.', '')
                    ]);
                } catch (\Exception $e) {
                    $cancellationErrors[] = "Failed to restore points: " . $e->getMessage();
                    Log::error('Point restoration failed during order cancellation', [
                        'order_id' => $order->id,
                        'user_id' => $authId,
                        'return_points' => $returnPoints,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            
            // Update order status regardless of individual failures
            OrderProduct::where('order_id', $orderId)->update(['status' => 'cancelled']);
            SubOrder::where('order_id', $orderId)->update([
                        // 'refund_at' =>  $refundAt,
                        // 'is_refund' => $isRefund,
                        'status' => 'cancelled'
                        ]);
            $order->update([
               'status' => 'cancelled',
                'cancelled_at' => now(),
               ]);

            // Send cancellation email to customer
            try {
                Mail::to($order->user->email)->send(new OrderCancelMail($order));

                //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($order)
                ->useLog('order_cancel')
                ->withProperties([
                    'old' => [
                        'amount' => $order->amount,
                        'order_number' => $order->order_number
                    ],
                ])
                ->log('Order #'.$order->order_number.' cancelled');
                //Activity Log End


            } catch (\Exception $e) {
                $cancellationErrors[] = "Failed to send cancellation email to customer";
                Log::error('Failed to send order cancellation email to customer', [
                    'order_id' => $order->id,
                    'user_email' => $order->user->email,
                    'error' => $e->getMessage()
                ]);
            }

            DB::commit();
            
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.cancel'),
                'warnings' => !empty($cancellationErrors) ? $cancellationErrors : null
            ], Response::HTTP_OK);

        } catch (\Throwable $th) {
            DB::rollBack();
            
            Log::error('Order cancellation failed: ' . $th->getMessage(), [
                'order_id' => $orderId, 
                'stack_trace' => $th->getTraceAsString()
            ]);
            
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }

    public function webhook(Request $request) 
    {
        Log::info('webhook call => '.now());
        try {
            $callbackUrl = config('app.url')."/api/v1/webhook/payment-handler";
            $signature = $this->ecommerceService->generateSignature($request->all(),$callbackUrl);
            $requestSignature = $request->header('cap-signature');
            if($signature['signature'] != $requestSignature){

                Log::info('webhook call signature not match => ', [
                    'requestSignature' => $requestSignature,
                    'webhookSignature' => $signature['signature'],
                ]);
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.order.invalid_signature'),
                ], Response::HTTP_UNAUTHORIZED);
            }

            $data = $request->all();
            Log::info('webhook call with this request and response => ', [
                'request' => $data,
            ]);

            DB::table('transaction_webhooks')->insert([
                'meta_data' => json_encode($data), 
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $transactionNumber = $data['transactionNumber'];
            $order =  Order::where('ecommerce_tran_id', $transactionNumber)->first();
            $ecommerceStatus = !in_array($data['status'],["0","1","9"]) ? $this->orderRepository->getFailureMessage($data['status']) : null;
            if ($order) {
                if($order->payment_status != 'paid' && $data['status'] == 1){
                    $order->update(['payment_status' => 'paid']);
                    PaymentSuccessfulMailJob::dispatch($order, 'success', 'pay_now');
                    $this->orderRepository->createTransaction(
                        $order,
                        $data,
                        'paid',
                        null
                    );
                    
                    //Activity Log Start
                        activity()
                        ->causedBy(Auth::user())
                        ->performedOn($order)
                        ->useLog('order_payment_done')
                        ->withProperties([
                            'attributes' => [
                                'amount' => $data['amount'] / 100,
                                'order_number' => $order->order_number,
                                'status' =>'paid'
                            ]
                        ])
                        ->log('Order #'.$order->order_number.' payment completed successfully.');
                        //Activity Log End
                }else{
                    $paymentStatus = $data['status'] == 9 ?  'authorized' :
                                     ($data['status'] == 0 ? 'pending' : 'failed');
                    if($order->payment_status != 'paid'){
                       
                        $order->update(['payment_status' => $paymentStatus]);
                    }
                    $trasRecord = Transaction::where('transaction_id', $transactionNumber)
                    ->where('ecommerce_status', $data['status'])->latest() 
                    ->first();
                   if(!$trasRecord){
                    
                        $this->orderRepository->createTransaction(
                        $order,
                        $data,
                        $paymentStatus,
                        $ecommerceStatus
                        );
                        $data['ecommerce_status'] = $ecommerceStatus;
                        $data['status'] == 0 ? PaymentPendingMailJob::dispatch($order, $data) : PaymentFailMailJob::dispatch($order, $data);
                    
                    }
                    
                    //Activity Log Start
                        activity()
                        ->causedBy(Auth::user())
                        ->performedOn($order)
                        ->useLog('order_webhook_response')
                        ->withProperties([
                            'attributes' => [
                                'amount' => $data['amount'] / 100,
                                'order_number' => $order->order_number,
                                'ecommerce_status' => $ecommerceStatus,
                            ]
                        ])
                        ->log('Order #'.$order->order_number.' new response arrived');
                }
            }else {
                
                $payout =  Payout::where('ecommerce_tran_id', $transactionNumber)->first();
                if($payout) {
                    $isStatusChanged = $payout->ecommerce_status != $data['status'];
                    if($payout->ecommerce_status != 1 && $data['status'] == 1){

                        $status = 'success';
                        $log = 'Payout completed for supplier ';
                        $this->payoutService->sendMail($payout);
                        $this->payoutService->transactionCreate($data,$payout,$status ,$ecommerceStatus);

                    }elseif ($isStatusChanged) {
                        // Failed or updated status case
                        $status = 'failed';
                        $log = 'Payout fail this supplier ';
                        $failureMessage = $ecommerceStatus;

                        $payout->update([
                            'payout_failed_reason' => $ecommerceStatus,
                            'ecommerce_status' => $data['status'],
                        ]);
                        $this->payoutService->transactionCreate($data, $payout, $status, $ecommerceStatus);
                    } else {
                       return response()->json([
                            'success' => true,
                        ], Response::HTTP_OK);
                    }
                    activity()
                        ->causedBy($payout->user)
                        ->performedOn($payout->user)
                        ->useLog('Payout Status')
                        ->withProperties([
                            'attributes' => $data
                        ])
                    ->log($log . $payout->user->email);
                }else{
                    // add webhook response for merchant admin fee response
                    $payout =  Payout::where('merchant_ecommerce_tran_id', $transactionNumber)->first();
                    if($payout) {
                        $isStatusChanged = $payout->merchant_ecommerce_status != $data['status'];
                        if($payout->merchant_ecommerce_status != 1 && $data['status'] == 1){

                            $status = 'success';
                            $log = 'Payout merchant completed from supplier ';
                            $this->payoutService->sendMerchantMail($payout,$data);
                            $this->payoutService->transactionCreate($data,$payout,$status ,null,'Payout Merchant');

                        }elseif ($isStatusChanged) {
                            // Failed or updated status case
                            $status = 'failed';
                            $log = 'Payout merchant fail this supplier ';
                            $failureMessage = $ecommerceStatus;

                            $payout->update([
                                'payout_merchant_failed_reason' => $ecommerceStatus,
                                'merchant_ecommerce_status' => $data['status'],
                            ]);
                            $this->payoutService->transactionCreate($data, $payout, $status, $ecommerceStatus,'Payout Merchant');
                        } else {
                        return response()->json([
                                'success' => true,
                            ], Response::HTTP_OK);
                        }
                        activity()
                            ->causedBy($payout->user)
                            ->performedOn($payout->user)
                            ->useLog('Payout Status')
                            ->withProperties([
                                'attributes' => $data
                            ])
                        ->log($log . $payout->user->email);
                    }

                }
            }

            return response()->json([
                'success' => true,
            ], Response::HTTP_OK);

        }catch (\Throwable $th) {
            Log::error('webhook Payment handler error: ' . $th->getMessage(), [
                'request_data' => $request->all(),
                'stack_trace' => $th->getTraceAsString()
            ]);

            DB::table('transaction_webhooks')->insert([
                'meta_data' => json_encode($th->getMessage()), 
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return exceptionResponse($th->getMessage());
        }

    }

    public function reTryPayment(Request $request,$orderId): JsonResponse
    {
        try {
            $data = $request->all();
            $auth = Auth::user();
            $authId = $auth->id;
            $order = Order::with(['subOrder','subOrder.orderProducts'])
                            ->where('id', decryptParam($orderId))
                            ->where('user_id', $authId)
                            ->first();

             if (!$order) {
                    return response()->json([
                        'success' => false,
                        'message' => __('api.clinic.order.order_not_found'),
                        'order_id' => null,
                    ], Response::HTTP_NOT_FOUND);
            }
            $createdAt = Carbon::parse($order->created_at); 
            $hoursDiff = $createdAt->diffInHours(Carbon::now());
            $paymentReason = $hoursDiff > 24 ? true : false;
            if($paymentReason) {
                return response()->json([
                        'success' => false,
                        'message' => __('api.clinic.order.order_payment_expired'),
                        'order_id' => null,
                    ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($order)
                ->useLog('order_retry_payment')
                ->withProperties([
                     'attributes' => [
                        'amount' => $order->amount,
                        'order_number' => $order->order_number
                    ]
                ])
                ->log('Order #'.$order->order_number.' payment process retried');
            //Activity Log End


            $payAmount = 0;
            // Fetch all product relations in bulk
            foreach($order['subOrder'] as $subOrder) {
                if($subOrder->payment_type != 'credit_line'){
                    $payAmount+= $subOrder->total_amount;
                }
            }
            $session =  $this->ecommerceService->store($request,$payAmount);

            if (isset($session['transactionNumber'])) {
                $order->update(['ecommerce_tran_id' => $session['transactionNumber']]);
            } else {
                // If still no session, throw error
                throw new \Exception('Failed to get payment session from payment gateway.');
            }

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.order.placed'),
                "data" => [
                    'is_payment_request' => $payAmount > 0 ? true : false,
                    'order_id' => encryptParam($order->id),
                    'redirect_url' => $session['redirectUrl'] ?? null,
                    'transaction_number' => $session['transactionNumber'] ?? null,
                ]
            ], Response::HTTP_OK);

        }catch (\Throwable $th) {
            Log::error('webhook Payment handler error: ' . $th->getMessage(), [
                'request_data' => $request->all(),
                'stack_trace' => $th->getTraceAsString()
            ]);
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }
}
