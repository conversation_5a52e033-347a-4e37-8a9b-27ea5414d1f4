<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Requests\Api\Clinic\AddToCartUpdateRequest;
use App\Http\Requests\Api\Clinic\CartPaymentRequest;
use App\Http\Resources\V1\Clinic\CartPaymentResource;
use App\Http\Requests\Api\Clinic\AddToCartRequest;
use App\Repositories\Api\Clinic\OrderRepository;
use App\Http\Resources\V1\UserAddressResource;
use App\Http\Resources\V1\Clinic\CartResource;
use App\Http\Resources\V1\Clinic\CartReviewResource;
use App\Http\Resources\CustomCollection;
use App\Services\CartService;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Models\ClinicAddCart;
use App\Models\DpharmaPoint;
use App\Models\Order;
use App\Models\PcDetail;
use App\Models\ProductRelation;
use App\Models\UserAddress;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;


class CartController extends Controller
{
    private $model;
    private $order;
    protected $orderRepository;
    protected $cartService;


    public function __construct(CartService $cartService)
    {
        $this->model = new ClinicAddCart();
        $this->order = new Order();
        $this->orderRepository = new OrderRepository($this->order);
        $this->cartService = $cartService;
    }

    /**
     * Method index: Get all cart list  data with filters
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        try {
            $query = ClinicAddCart::with(['supplier','product','product.unit','product.foam','product.fav'])
                                    ->where('user_id', Auth::id())
                                    ->orderBy('created_at', 'desc')
                                    ->get();
            $total = $query->count();
            $cartData = $query->groupBy('supplier_id');
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.cart.cart_list'),
                'data' =>  [
                    'total' => $total,
                    'suppliers' => new CustomCollection($cartData, CartResource::class)
                ],
        ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }

    /**
     * Method Add To Cart: Add product in to cart.
     *
     * @param Request $request
     *
     * @return JsonResponse
     */

    public function addCart(AddToCartRequest $request) : JsonResponse
    {
        try {
            $body = $request->validated();
            $auth = Auth::user();
            $zone  = getClinicZone();
            $body["user_id"] = $auth->id;
            $body["user_account_type_id"] = userAccountType('facilities');
            $body["product_id"] = decryptParam($body["product_id"]);
            $body["supplier_id"] = decryptParam($body["supplier_id"]);
            $body["tier_number"] = $body["tier_number"] ?? null;
            
            $stock  = $this->getProductStk($body["product_id"], $body["supplier_id"]);

            $existing = $this->model->where('user_id', $body["user_id"])
                    ->where('product_id', $body["product_id"])
                    ->where('supplier_id', $body["supplier_id"])
                    ->first();
            $totalQty = $existing ? $existing->quantity + $body['quantity'] : $body['quantity']; 

            if($stock < $totalQty) { 
                
                
                $qtyDifference = floor($stock) - $existing->quantity;
                $msg = __('api.clinic.cart.qty_limit_exceed');

                if($qtyDifference > 0 ) {
                    $qtyText = $qtyDifference == 1 ?  ' quantity' : ' quantities';;
                    $msg .= __('api.clinic.cart.add_qty', ['Qty' => $qtyDifference . $qtyText]);
                }
                return response()->json([
                    "success" => false,
                    'message' => $msg,
                ], Response::HTTP_UNPROCESSABLE_ENTITY);
            }
            $body["tier_number"] = $this->orderRepository->getTierBasedQuantity($body["product_id"],$body["supplier_id"],$zone,$totalQty);
           
            /* check product if exists ot not based on the product_id and supplier id */
            if ($existing) {
                $oldQty = $existing->quantity;
                $existing->update([
                    'quantity' => $totalQty,
                    'tier_number' => $body["tier_number"], 
                ]);
                $model = $existing;
                $activityPro = [
                    'old' => [
                        'supplier' => $model->supplier->company_name,
                        'product'  => $model->product->name,
                        'quantity' => $oldQty,
                    ]
                ];

            } else {
               $model = $this->model->create($body);

            }
            $productName = $model->product->name;
            $activityPro['attributes'] = [
                'supplier' => $model->supplier->company_name,
                'product'  => $model->product->name,
                'quantity' => $model->quantity,
            ];

            $response = response()->json([
                "success" => true,
                'cart_count' => $this->totalCartProducts(),
                'message' => __('api.clinic.cart.cart_add'),
            ]);

  
            //Activity Log Start
                activity()
                ->causedBy($auth)
                ->performedOn($this->model)
                ->useLog('cart')
                ->withProperties($activityPro)
                ->log('Product "' . $productName . '" added to cart');
            //Activity Log End

        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }

    public function getProductStk($product_id, $supplier_id) {

        $productStk = ProductRelation::where('product_id',$product_id)
            ->where('user_id',$supplier_id)
            ->with(['productRelationStock'])
            ->first();

            if ($productStk) {
                $productRelationStock = $productStk->productRelationStock;
            
                if ($productRelationStock->is_batch_wise_stock) {
                    // Summing available stock from batches with valid expiry dates
                    $stock = $productRelationStock->productsBatch
                                ->filter(function ($batch) {
                                    return Carbon::parse($batch->expiry_date)->gte(Carbon::today());
                                })
                                ->sum('available_stock');
                } else {
                    // Directly using the stock value if not batch-wise
                    $stock = $productRelationStock->stock;
                }

                // if($productRelationStock->stock_type == 'wps') {
                //     $stock = $stock / $productRelationStock->wholesale_pack_size;
                // }
            } else {
                // Handle case where no product relation is found
                $stock = 0; 
            }

        return $stock;
    }
    public function update(AddToCartUpdateRequest $request) : JsonResponse
    {
        try {
            $zone  = getClinicZone();
            $validatedData = $request->validated();
            foreach ($validatedData['products'] as $product) {
                $cart = $this->model->find(decryptParam($product["id"]));

                $productExpired = DB::select("
                    SELECT 
                        CASE 
                            WHEN prs.is_batch_wise_stock = false AND prs.expiry_date IS NOT NULL AND prs.expiry_date < CURRENT_DATE THEN true
                            WHEN prs.is_batch_wise_stock = true AND NOT EXISTS (
                                SELECT 1 FROM products_batch pb 
                                WHERE pb.product_id = ? AND pb.user_id = ? 
                                AND pb.expiry_date >= CURRENT_DATE AND pb.available_stock > 0
                            ) THEN true
                            ELSE false
                        END as is_expired
                    FROM products_relation pr
                    JOIN product_relation_stocks prs ON prs.product_relation_id = pr.id
                    WHERE pr.product_id = ? AND pr.user_id = ?
                    LIMIT 1
                ", [$cart->product_id, $cart->supplier_id, $cart->product_id, $cart->supplier_id]);
                
                if (!empty($productExpired) && $productExpired[0]->is_expired) {
                    throw ValidationException::withMessages([
                        __('api.clinic.product_expired', ['product' => $cart->product->name])
                    ]);
                }

                $userInfo = PcDetail::where('user_id',$cart->supplier_id)
                            ->where('is_restricted', false)
                            ->orWhere(function ($q2) {
                                $q2->where('is_restricted', true)
                                ->whereHas('clinicPharmaSuppliers', function ($q3) {
                                    $q3->where('status', 'approved')
                                     ->where('clinic_id', Auth::user()->id);
                                });
                        })->pluck('id')->first();
                if(!$userInfo){
                    throw ValidationException::withMessages([
                        __('api.clinic.restricted_pc',['pc' => $cart->supplier->company_name])
                    ]);
                }
                $quantity = $product["product_qty"];
                $supPro = $cart->product->productDataForPc($cart->supplier_id);
                $productRelationStock = $supPro->productRelationStock;

                if ($productRelationStock->is_batch_wise_stock) {
                    $stock =  $supPro->batches()->sum('available_stock');
                }else{
                    $stock =  $productRelationStock->stock;
                }
                // if($productRelationStock->stock_type == 'wps') {
                //     $stock = $stock / $productRelationStock->wholesale_pack_size;
                // }

                if($stock < $quantity) { 
                   throw ValidationException::withMessages([
                        __('api.clinic.cart.qty_limit_exceed_sup',['pc' => $cart->supplier->company_name,'product' => $cart->product->name])
                    ]);
                }

                $tier = $this->orderRepository->getTierBasedQuantity($cart->product_id,$cart->supplier_id,$zone,$quantity);

                $tier = $tier ? $tier : null;
                    $cart->update(["quantity" => $quantity , "tier_number" => $tier]);
                }

                
            $response = response()->json([
                "success" => true,
                'message' => __('api.clinic.cart.cart_updated'),
            ]);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }

    /**
     * Method Delete: Delete product from the cart.
     *
     * @param Request $request
     *
     * @return JsonResponse
     */

    public function delete($id): JsonResponse
    {
        try {
            $id = decryptParam($id);
            $cart = $this->model->find($id);
            if (is_null($cart)) {
                $response = response()->json([
                    "success" => false,
                    'message' => __('api.clinic.cart.cart_not_found'),
                    'data' => [],
                ], Response::HTTP_NOT_FOUND);
            } else {
                $this->model->destroy($id);
                $response = response()->json([
                    "success" => true,
                    'cart_count' => $this->totalCartProducts(),
                    'message' => __('api.clinic.cart.cart_delete')
                ], Response::HTTP_OK);
            }

             //Activity Log Start
                activity()
                ->causedBy(Auth::user())
                ->performedOn($this->model)
                ->useLog('cart remove')
                ->withProperties([
                    'old' => [
                                    'supplier' => $cart->supplier->company_name,
                                    'product' => $cart->product->name,
                                    'quantity' => $cart->quantity,
                                ],
                ])
                ->log('Removed product  "'.$cart->product->name.'"  from the cart.');
            //Activity Log End

        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }
    public function clear(): JsonResponse
    {
        try {
            $auth = Auth::user();
            $authId = $auth->id;

            $cartItems = $this->model->where('user_id', $authId)->get();
            if (is_null($cartItems)) {
                $response = response()->json([
                    "success" => false,
                    'message' => __('api.clinic.cart.cart_not_found'),
                    'data' => [],
                ], Response::HTTP_NOT_FOUND);
            } else {
                foreach ($cartItems as $key => $cart) {
                    
                    $oldData[$key] = [
                        'supplier' => $cart->supplier->company_name,
                        'product' => $cart->product->name,
                        'quantity' => $cart->quantity,
                    ];
                }
                //Activity Log Start
                    activity()
                    ->causedBy($auth)
                    ->performedOn($this->model)
                    ->useLog('cart remove')
                    ->withProperties([
                        'old' => $oldData
                    ])
                    ->log('All products removed from cart');
                //Activity Log End


                $this->model->where('user_id', $authId)->delete();
                $response = response()->json([
                    "success" => true,
                    'cart_count' => $this->totalCartProducts(),
                    'message' => __('api.clinic.cart.cart_clear')
                ], Response::HTTP_OK);
            }
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }
    public function detail(): JsonResponse
    {
        try {
                $auth = Auth::user();
                $authId = $auth->id;
                $address = UserAddress::where('is_default', 1)->where('user_id',$authId)->first();
                if(!$address) {
                    $shippingId = $auth->clinicData->shipping_addresses_id;
                    $address =  UserAddress::find($shippingId);
                }
                $response  =  response()->json([
                    'success'   => true,
                    'message'   => __('api.clinic.clinic_default_address'),

                    'data' => [
                        'address' => $address ? new UserAddressResource($address) : null,
                        "points" => round(DpharmaPoint::where('user_id',$authId)->latest()->pluck('balance')->first() , 2) ?? 0,
                        'earn_points' => getTierPercentage()  // This is percentage
                    ],
                ], Response::HTTP_OK);
        
            
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;
    }
    public function cartPaymentDetail(CartPaymentRequest $request): JsonResponse
    {
        try {
            $auth = Auth::user();
            $validatedData = $request->validated();
            $clinicPoints = DpharmaPoint::where('user_id',$auth->id)->latest()->pluck('balance')->first() ?? 0;
            $toTier = null;
            if(isset($validatedData['applied_points']) && ($validatedData['applied_points'] > $clinicPoints)){
                return response()->json([
                    'success' => false,
                    'message' => __('api.clinic.cart.points_not_enough'),
                ], Response::HTTP_NOT_FOUND);
            }else{
                $updatedPoints =  $clinicPoints-$validatedData['applied_points'];
                // Check if any supplier has "payment_type" as "pay_now"
                $hasPayNow = count(array_filter($request['suppliers'], function ($supplier) {
                    return $supplier['payment_type'] === "pay_now";
                })) > 0;
                if(/*$hasPayNow && */ config('constants.api.facility_tier.status')){
                    $toTier = getTierBaseOnPoint($updatedPoints);
                    $extra =  [
                        'from' => ucfirst($auth->clinicDetails->tier) ?? null,
                        'to' => $toTier,
                    ];
                }else{
                    $extra =  [
                        'from' =>  null,
                        'to' => null,
                    ];
                }
            }
            
            $supplierData = collect($validatedData['suppliers']);
           
            $supplierIds = $supplierData->pluck('id')->toArray();

            // Create a mapping of supplier_id to payment_type
            $paymentTypeMapping = $supplierData->mapWithKeys(function ($supplier) {
                $supplierId = decryptParam($supplier['id']);
                return [$supplierId => $supplier['payment_type']];
            })->toArray();
            
            $query = ClinicAddCart::where('user_id', $auth->id)
                                    ->whereIn('supplier_id', decryptParam($supplierIds))
                                    ->orderBy('created_at', 'desc')
                                    ->get();

            $total = $query->count();
            $cartData = $query->groupBy('supplier_id');
            $grandTotal = $this->cartService->calculateCartGrandTotal($cartData,$paymentTypeMapping,$validatedData);

            foreach($validatedData['suppliers'] as $supplier) {
                $isSubmission = $validatedData['is_submission'] ?? false;
                $supplierId = decryptParam($supplier['id']);

                if($isSubmission){
                    ClinicAddCart::where('user_id', $auth->id)
                        ->where('supplier_id', $supplierId)
                        ->update(['meta_data' => json_encode([
                            "payment_type" => $supplier['payment_type'],                                      
                            //"shipping_detail" => $supplier['shipping_detail'],                                      
                            "delivery_type" => $supplier['delivery_type'],                                      
                            "applied_points" => $validatedData['applied_points'],                                      
                            "grand_total" => $grandTotal,                                      
                            ])]);
                    
                    }

            }
            // Combine the grand total and payment type mapping into the request data
            $request->merge([
                'grandTotal' => $grandTotal,
                'paymentTypeMapping' => $paymentTypeMapping,
            ]);

            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.cart.cart_payment_list'),
                'data' =>  [
                    'total' => $total,
                    'suppliers' => CartPaymentResource::collection($cartData),
                    'extra' => $extra
                ],
            ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }
        return $response;
    }
    public function totalCartProducts(){
        return ClinicAddCart::where('user_id', Auth::id())->count();
    }

    public function cartReview() {

        try {
            $query = ClinicAddCart::with(['supplier','product','product.unit','product.foam','product.fav'])
                                    ->where('user_id', Auth::id())
                                    ->orderBy('created_at', 'desc')
                                    ->get();
            $total = $query->count();
            $cartData = $query->groupBy('supplier_id');
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.cart.cart_review'),
                'data' =>  [
                    'total' => $total,
                    'suppliers' => new CustomCollection($cartData, CartReviewResource::class)
                ],
        ], Response::HTTP_OK);
        } catch (\Throwable $th) {
            $response = exceptionResponse($th->getMessage());
        }

        return $response;

    }
}