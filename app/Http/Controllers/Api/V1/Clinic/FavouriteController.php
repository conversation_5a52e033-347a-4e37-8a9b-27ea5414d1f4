<?php

namespace App\Http\Controllers\Api\V1\Clinic;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Clinic\FavouriteRequest;
use App\Http\Resources\CustomCollection;
use App\Http\Resources\V1\Clinic\ProductResource;
use App\Http\Resources\V1\Clinic\SupplierResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Models\Favourite;
use App\Models\PcDetail;
use App\Models\Product;
use App\Repositories\Api\Clinic\ProductRepository;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FavouriteController extends Controller
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    private $model;
    protected $productRepository;

    /**
     * AccountTypeController constructor.
     *
     * @param  Favourite  $model
     */
    public function __construct()
    {
        $this->model = new Favourite;
        $this->productRepository = new ProductRepository;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $perPage =$request->per_page ?? 10;
            $type = $request->type;
            
            if($type == 'supplier') {
                $query = PcDetail::with(['fav', 'user'])->where(function ($subQuery) use ($authId) {
                    $subQuery->where('is_restricted', false)
                        ->orWhere(function ($subQueryInner) use ($authId) {
                            $subQueryInner->where('is_restricted', true)
                                ->whereHas('clinicPharmaSuppliers', function ($q) use ($authId) {
                                    $q->where('status', 'approved')->where('clinic_id', $authId);
                                });
                        });
                    })->whereHas('fav', function ($q)  use ($authId) {
                        $q->where('type', 'supplier')
                        ->where('user_id', $authId);
                    });
            }else{ 
                $query =$this->productRepository->products($request);

                $query->join('favourites as fav2', function ($q)  use ($authId) {
                    $q->on('fav2.product_id', '=', 'products.id')
                        ->where('fav2.type', 'product')
                        ->where('fav2.user_id', $authId);
                });
                
            }

            $total = $query->count();
            if($perPage === 'all') {
                $res = $query->get();

            }else{
                $res = $query->paginate($perPage);
            }
            if($type == 'supplier') {
                $res->getCollection()->transform(function ($supplier) {
                    $supplier->is_fav = $supplier->fav !== null;
                    return $supplier;
                });
                $res = new CustomCollection($res, SupplierResource::class);
            }else{
                $countQuery = clone $query;
                $total = $countQuery->getCountForPagination();
                $res = new CustomCollection($res, ProductResource::class);
            }
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.supplier_list'),
                'data' => [
                    'total' => $total,
                    'list' => $res
                ],
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function save(FavouriteRequest $request): JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $data = $request->validated();
            $saveData = [
                'type' => $data['type'],
                'user_id' => $authId,
            ];

            if($data['type'] == 'supplier') {
                $saveData['supplier_id'] = decryptParam($data['supplier_id']);
                $log = 'Supplier';
            }else{
                $saveData['product_id'] = decryptParam($data['product_id']);
                $log = 'Product';
            }
            $res = $this->model->updateOrCreate($saveData);
               //Activity Log Start
                    activity()
                    ->causedBy(Auth::user())
                    ->performedOn($this->model)
                    ->useLog('Fav '.$data['type'] . 'add')
                    ->withProperties([
                        'old' => [
                            'status' => 'un-favourite',
                        ],
                        'attributes' => [
                            'status' => 'favourite',
                        ],
                    ])
                    ->log($log . '" '.strtoupper($res->{$data['type']}->name) . '" added to favourite.');
                //Activity Log End


            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.favourite_list'),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function remove(FavouriteRequest $request): JsonResponse
    {
        try {
            $authId = Auth::user()->id;
            $data = $request->validated();

            if($data['type'] == 'supplier') {
                $condition = decryptParam($data['supplier_id']);
                $log = 'Supplier';
            }else{
                $condition =  decryptParam($data['product_id']);

                $log = 'Product';
            }
            $res = $this->model->where('user_id',$authId)
                        ->where('type', $data['type'])
                        ->where($data['type'].'_id' , $condition)->first();
            
            //Activity Log Start
                    activity()
                    ->causedBy(Auth::user())
                    ->performedOn($this->model)
                    ->useLog('Fav '.$data['type'] . 'remove')
                    ->withProperties([
                        'old' => [
                                'status' => 'favourite',
                            ],
                            'attributes' => [
                                'status' => 'un-favourite',
                            ],
                    ])
                    ->log($log .'" '.strtoupper($res->{$data['type']}->name) . '" removed from favourite.');
            //Activity Log End
            // $res->each(function ($item) {
            //     $item->delete();
            // });

            $res->delete();
            $response = response()->json([
                'success' => true,
                'message' => __('api.clinic.favourite_remove'),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response = exceptionResponse($e->getMessage());
        }
        return $response;
    }
}
