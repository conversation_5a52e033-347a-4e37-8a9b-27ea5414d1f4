<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Resources\V1\Clinic\CategoryResource;
use App\Http\Resources\V1\StaticPageResource;
use Indianic\Settings\Models\GlobalSettings;
use Indianic\CmsPages\Models\CmsPage;
use App\Http\Controllers\Controller;
use App\Http\Resources\CustomCollection;
use Illuminate\Support\Facades\Auth;
use App\Models\Category;
use App\Models\TermCondition;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Exception;

class StaticPageController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try{
            $slug = $request->slug;
            if($slug == 'term-and-conditions'){ 
                $page = TermCondition::where('status',true)->first();
                if($page)
                {
                    $page->title = 'Term and Conditions';
                    $page->sub_title = 'Term and Conditions';
                    $page->slug = 'term_and_conditions';
                    $page->body = $page->meta_description;
                }
            }else {
                $page = CMSPage::where('slug', $slug)->first();
            }

            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.static_page_detail'),
                'data' => $page ? new StaticPageResource($page) : null
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    public function footer(): JsonResponse
    {
        try{
            $userClinicAccountTypeId = Auth::guard('api')->user()->clinicData->clinic_account_type_id ?? null;
            $socialMedia = GlobalSettings::all()->keyBy('name');

            $socialMedias  = [ 
                'facebook_url' => $socialMedia['facebook_url']['value'] ?? null,
                'linkedin_url' => $socialMedia['linkedin_url']['value'] ?? null,
                'twitter_url' => $socialMedia['twitter_url']['value'] ?? null,
                'instagram_url' => $socialMedia['instagram_url']['value'] ?? null,
                'youtube_url' => $socialMedia['youtube_url']['value'] ?? null,
                'contact_email' => $socialMedia['contact_email']['value'] ?? null,
                'contact_number' => $socialMedia['contact_number']['value'] ?? null,
            ];

            $categories = Category::where('type','b2b')
            ->where('parent_id',null)
            ->where('status',true)
            ->whereDoesntHave('categoryAccountTypes', function ($query) use ($userClinicAccountTypeId) {
                $query->where('clinic_account_type_id', $userClinicAccountTypeId);
            })
            ->get();


            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.clinic.footer_detail'),
                'data' => [
                    'social_media' => $socialMedias,
                    'categories' => new CustomCollection($categories, CategoryResource::class)
                ]
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
}