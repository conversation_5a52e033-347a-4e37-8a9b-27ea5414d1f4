<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Resources\V1\CountryResource;
use App\Http\Resources\V1\ZipCodeResource;
use App\Http\Resources\CustomCollection;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Nnjeim\World\Models\Country;
use Nnjeim\World\Models\State;
use App\Models\ZipCode;
use Nnjeim\World\Models\City;
use Illuminate\Http\Request;
use Exception;

class CountryCityController extends Controller
{
    public function countries()
    {
        try{
            $countries = Country::all();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.user.countries_list'),
                'data' => new CustomCollection($countries, CountryResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;

    }
    public function states($countryId)
    {
        try{
            if ($countryId == 0) {
                //gwet country id from Config Malaysia
                $countries = State::where('country_id',config('constants.api.default_country_id'))->get();
            }else{
                $countries = State::where('country_id',decryptParam($countryId))->get();
            }
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.user.states_list'),
                'data' => new CustomCollection($countries, CountryResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;

    }
    public function cities($stateId)
    {
        try{
            $countries = City::where('state_id',decryptParam($stateId))->get();
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.user.cities_list'),
                'data' => new CustomCollection($countries, CountryResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
    
    public function getLandlineCode(Request $request)
    {
        try{
            $cityId = decryptParam($request->city_id) ?? null;
            $codes = $cityId 
                        ? City::where('id', $cityId)->get() 
                        : City::selectRaw('MIN(id) as id, MIN(name) as name, landline_code')
                            ->whereNotNull('landline_code')
                            ->groupBy('landline_code')
                            ->orderByRaw('CAST(landline_code AS INTEGER)')
                            ->get();
                            
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.user.landline_code_list'),
                'data' => new CustomCollection($codes, CountryResource::class)
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }

    public function getPostalcode(Request $request)
    {
        try{
            $cityId = decryptParam($request->city_id) ?? null;
            $codes = ZipCode::where('city_id', $cityId)->get();
                        
            $response  =  response()->json([
                'success'   => true,
                'message'   => __('api.user.zip_code_code_list'),
                'data' => ZipCodeResource::collection($codes),
            ], Response::HTTP_OK);
        } catch (Exception $e) {
            $response  = exceptionResponse($e->getMessage());
        }
        return $response;
    }
}