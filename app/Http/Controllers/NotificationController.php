<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Filament\Notifications\Notification as FilamentNotification;
use Livewire\Livewire;

class NotificationController extends Controller
{

    public function redirect(Request $request, $notification)
    {
        $notification = Notification::findOrFail($notification);
        $data = json_decode($notification->data) ?? null;
        $targetUrl = $data->target_url ?? '/';
        if ($notification->type ==  'App\Notifications\Api\NewMessageNotification') {
            Notification::where('notifiable_id', $notification->notifiable_id)
                ->where('type', 'App\Notifications\Api\NewMessageNotification')
                ->whereRaw("data->>'target_url' = ?", [$targetUrl])
                ->update(['read_at' => now()]);
        } else {
            $notification->update(['read_at' => now()]);
        }

        return redirect($targetUrl);
    }
}
