<?php

namespace App\Http\Controllers;

use App\Filament\Admin\Pages\EditProfile;
use App\Http\Controllers\Controller;
use App\Models\PcDetail;
use Stripe\Stripe;
use Stripe\Account;
use App\Models\User;
use Stripe\AccountLink;
use Illuminate\Support\Facades\Crypt;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use App\Http\Requests\Api\Clinic\paymentHandlerRequest;
use App\Models\Order;
use App\Models\Payout;
use App\Service\ShippingService;
use App\Service\EcommerceService;
use App\Service\PayoutService;
use App\Repositories\Api\Clinic\OrderRepository;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;

class EcommerceController extends Controller
{

    private $model;
    protected $orderRepository;
    protected $ecommerceService;
    protected $payoutService;
    protected $shippingService;

    public function __construct()
    {
        $this->model = new Order();
        $this->ecommerceService = new EcommerceService();
        $this->shippingService = new ShippingService();
        $this->payoutService = new PayoutService();
        $this->orderRepository = new OrderRepository($this->model);
    }

    public function supplierPaymentHandler(Request $request) {
        $transactionData = $request->all();

         Log::error('payout status');
         Log::error($transactionData);

    }


    public function checkPayoutPaymentStatus(Request $request){
        $request->merge([
            'transaction_number' => $request->get('TransactionNumber')
        ]);
        $request->offsetUnset('TransactionNumber');

        $data = $this->ecommerceService->webhook($request);

        if (!$data['result']) {
            Log::error('Invalid payment response - no result data', ['data' => $data]);
            return response()->json([
                'success' => false,
                'order_id' => null,
                'message' => __('api.clinic.order.payment_failed')
            ]);
        }

        $transactionResult = $data['result'];
// dd(!in_array($transactionResult['status'], ['1','9']));
        $ecommerceStatus = !in_array($transactionResult['status'], ['0','1','9']) ? $this->orderRepository->getFailureMessage($transactionResult['status']) : null; //$this->orderRepository->getFailureMessage($transactionResult['status']);
        // dd($ecommerceStatus);
        // add webhook response for merchant admin fee response
        $payout =  Payout::where('merchant_ecommerce_tran_id', $transactionResult['transactionNumber'])->first();
// dd($payout);
        if($payout) {
            $isStatusChanged = $payout->merchant_ecommerce_status != $transactionResult['status'];

            if($payout->merchant_ecommerce_status != 1 && $transactionResult['status'] == 1){

                $status = 'success';
                $log = 'Payout merchant completed from supplier ';
                $this->payoutService->sendMerchantMail($payout,$transactionResult);

                $res = $this->payoutService->transactionCreate($transactionResult,$payout,$status ,$ecommerceStatus,'Payout Merchant');
                $paymentStatus = 'Your payout payment has been successfully processed.';
                return view('payout-status', [ 'paymentStatus' =>$paymentStatus]);

            }

        }
        // return response()->json([
        //     'success' => false,
        //     'order_id' => null,
        //     'message' => __('api.clinic.order.invalid_payout')
        // ]);
        $paymentStatus = __('api.clinic.order.invalid_payout');
        return view('payout-status', [ 'paymentStatus' =>$paymentStatus]);

    }
}
