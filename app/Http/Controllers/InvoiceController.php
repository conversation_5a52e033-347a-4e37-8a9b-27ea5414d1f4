<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\SubOrder;
use App\Models\User;
use Carbon\Carbon;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Filament\Notifications\Notification as FilamentNotification;
use Livewire\Livewire;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class InvoiceController extends Controller
{
    public function download($id)
    {
        $record = SubOrder::findOrFail($id); // Replace with actual model


        $media = $record->getMedia('invoices')->last();

        //activityLog Start
        activity()
            ->causedBy(auth()->user())
            ->useLog('invoice_download')
            ->performedOn($record)
            ->log("Invoice downloaded for Order #{$record->order->order_number}");
        //activityLog end

        $url = $media->getTemporaryUrl(now()->addMinutes(5));
        $fileName = $media->file_name;


        return response()->streamDownload(function () use ($url) {
            echo file_get_contents($url);
        }, $fileName);
    }
}
