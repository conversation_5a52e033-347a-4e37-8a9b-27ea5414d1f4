<?php

namespace App\Http\Controllers;

use App\Filament\Admin\Pages\EditProfile;
use App\Http\Controllers\Controller;
use App\Models\PcDetail;
use Stripe\Stripe;
use Stripe\Account;
use App\Models\User;
use Stripe\AccountLink;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Request;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;

class StripeController extends Controller
{
    public function onBoard()
    {
        // dd('test');
        $user = User::where(['email' => '<EMAIL>'])->first();

        /** @var User $user */
        // if (empty($user->stripe_on_boarding_completed_at)) {
        Stripe::setApiKey('sk_test_51PvYVRJTmCrnb1sIxjObAcyBGY3LeJBawaGOFCLfDDelgo75OqP2LbXMnRXDiCH379J7onZ0rf98YLX0yj86EXvY00eSsw4YgM');

        // if (empty($user->stripe_connect_id)) {
        /** @var Account $account */
        $account = Account::create([
            'type'         => 'express',
            'email'        => $user->email,
            'country'      => 'US',
            'capabilities' => [
                'card_payments' => ['requested' => true],
                'transfers'     => ['requested' => true],
            ],
            'settings'     => [
                'payouts' => [
                    'schedule' => [
                        'interval' => 'manual',
                    ],
                ],
            ],
        ]);
        // dd($account->id);
        //                 $user->stripe_connect_id = $account->id;
        //                 $user->save();
        // }

        $user->fresh();

        $onBoardLink = AccountLink::create([
            'account'     => $account->id,
            'refresh_url' => route('pc.home'),
            'return_url'  => route('stripe.onboard-result', Crypt::encrypt($account->id)),
            'type'        => 'account_onboarding',
        ]);

        return redirect($onBoardLink->url);
        // }


        $loginLink = $this->stripeClient->accounts->createLoginLink($account->id, []);

        return redirect($loginLink->url);
    }

    public function onBoardResult($encodedToken)
    {
        /** @var User $user */
        $user = PcDetail::whereStripeConnectId(Crypt::decrypt($encodedToken))->firstOrFail();

        $user->stripe_on_boarding_status = 'completed';
        $user->save();
        Notification::make()->title('Stripe onboarding information updated Successfully')->success()->send();
        return redirect()->route('filament.pc.pages.onboarding');
    }

    public function downloadFile($filePath)
    {
        if (!Storage::disk('s3')->exists($filePath)) {
            abort(404);
        }

        $headers = [
            'Content-Type' => Storage::disk('s3')->mimeType($filePath),
            'Content-Disposition' => 'attachment; filename="' . basename($filePath) . '"',
        ];

        return response(Storage::disk('s3')->get($filePath), 200, $headers);
    }

    public function toggle(Request $request, $id)
    {


        $user = User::findOrFail($id);

        $user->is_active = request()->input('active') ? 1 : 0;


        // Optional: Fire Filament Notification if this is a Livewire or Inertia page
        if ($user->save()) {
            Notification::make()
                ->title('User status updated successfully.')
                ->success()
                ->send();
            return response()->json([
                'success' => true,
                'message' => 'User status updated successfully.',
            ]);
        }


        return back();
    }
}
