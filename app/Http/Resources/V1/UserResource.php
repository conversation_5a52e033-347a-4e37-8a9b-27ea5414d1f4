<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource {

    public function toArray($request) {
        return [
            'id' => encryptParam($this->id),
            "name" => ucfirst($this->name),
            "clinic_name" => ucfirst($this->clinicData->clinic_name) ?? ucfirst($this->name),
            "email" => $this->email,
            "created_at" => $this->created_at->format('d-m-Y'),
            "is_admin_verified" => $this->is_admin_verified ? true : false, 
            "step" => $this->clinicData ? $this->clinicData->completed_step : 0, 
            "social_type" => $this->clinicData->social_type ?? null, 
            'isAccountSelected' => (!empty($this->clinicData) && $this->clinicData->clinic_account_type_id) ? true : false,
            'country_id' => getDefaultCountry(),
            'cart_count' => $this->cart_count,
            'unread_message' => $this->unread_message ?? 0,
            'address' => [
                'id' => $this->address ? encryptParam($this->address->id) : null,
                'postal_code' => $this->address ? $this->address->postal_code : null,
                'country_name' => $this->address ? $this->address->country->name : null
            ]
        ];
    }
}
