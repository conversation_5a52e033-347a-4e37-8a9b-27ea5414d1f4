<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Resources\Json\JsonResource;

class StaticPageResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "title" => $this->title,
            "sub_title" => $this->sub_title,
            "slug" => $this->slug,
            "body" => $this->body,
            "meta_keywords" => $this->meta_keywords ?? null,
            "meta_description" => $this->meta_description ?? null,
        ];
    }
}
