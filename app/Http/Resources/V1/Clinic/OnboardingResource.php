<?php

namespace App\Http\Resources\V1\Clinic;

use App\Http\Resources\V1\UserAddressResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class OnboardingResource extends JsonResource {

    public function toArray($request): array
    {
        $folderPath = config('constants.api.media.clinic_medias').$this->id.'/';

       // Extract certificates dynamically
        $mapCertificates = function ($certificates) use ($folderPath) {
            return $certificates->map(fn($cert) => [
                'id'   => encryptParam($cert->id),
                'name' => $cert->name,
                'size' => $cert->size,
                'url'  => $cert->name ? Storage::url($folderPath . $cert->name) : null,
                'type' => $cert->name ? pathinfo($cert->name, PATHINFO_EXTENSION) : null,
            ]);
        };

        return [
            'id' => encryptParam($this->id),
            "completed_step" => (!empty($this->user->created_by) && !$this->user->is_admin_verified && !$this->is_declare_info) ? $this->completed_step - 1 :$this->completed_step,
            "clinic_name" => $this->clinic_name,
            'photo' =>   getImage($this->user->photo, 'users'), // default-image
            "clinic_number" => $this->clinic_number,
            "mobile_code" => $this->mobile_code,
            "mobile_number" => $this->mobile_number,
            "landline_code" => $this->landline_code,
            "landline_number" => $this->landline_number,
            "clinic_account_type" => $this->clinic_account_type_id ? new AccountTypeResource($this->clinicAccountType) : null,
            "business_type_id" => $this->business_type_id ? encryptParam($this->business_type_id) : null,
            "default_country_id" => encryptParam(config('constants.api.default_country_id')) , 
            "business_name" => $this->business_type_id ? $this->businessName->name : null,
            "company_name" => $this->company_name,
            "company_number" => $this->company_number,
            "clinic_owner" => $this->clinic_owner,
            "clinic_year" => $this->clinic_year,
            "tin_number" => $this->tin_number ?? null,
            "sst_number" => $this->sst_number ?? null,

            "shipping_address" =>$this->shippingAddress ? $this->shippingAddress : null,
            "billing_address" => $this->billing_addresses_id ?  new UserAddressResource($this->billingAddress) : null,
            "is_same_as_billing" => $this->is_billing_address_same,

            "dc_name" => $this->dc_name,
            "dc_nric" => $this->dc_nric ?? null,
            "dc_mmc_number" => $this->dc_mmc_number,
            "dc_apc_number" => $this->dc_apc_number,
            "dc_phone_code" => $this->dc_phone_code,
            "dc_phone_number" => $this->dc_phone_number,
            "dc_landline_code" => $this->dc_landline_code,
            "dc_landline_number" => $this->dc_landline_number,
            "dc_signature" => $this->dc_signature ? Storage::url($folderPath.$this->dc_signature) : null,
            "is_admin_in_charge" => $this->is_admin_in_charge,
            "ac_name" => $this->ac_name,
            "ac_nric" => $this->ac_nric ?? null,
            "ac_phone_code" => $this->ac_phone_code,
            "ac_phone_number" => $this->ac_phone_number,
            "ac_landline_code" => $this->ac_landline_code,
            "ac_landline_number" => $this->ac_landline_number,
            "signature_type" => $this->signature_type,


            'mmc_certificate' => $mapCertificates($this->mmcCertificates),
            'apc_certificate' => $mapCertificates($this->apcCertificates),
            'arc_certificate' => $mapCertificates($this->arcCertificates),
            'poison_license' => $mapCertificates($this->licenseCertificates),
            'other_relevant_documents' => $mapCertificates($this->otherRelevantDocuments),
            'borang_certificate' => $mapCertificates($this->borangCertificates),

            "apc_certificate_expired_date" => $this->apc_certificate_expired_date  ?? '',

            "is_admin_verified" => $this->user->is_admin_verified ? true : false,
            "reject_reason" => $this->user->verification_status == 'rejected' ? $this->user->rejection_reason : null,
            "social_type" => $this->social_type,

        ];
    }
}
