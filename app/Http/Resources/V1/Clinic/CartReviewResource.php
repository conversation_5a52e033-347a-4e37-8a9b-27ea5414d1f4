<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\ProductRelation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Service\ShippingService;
use App\Services\CartService;
use Illuminate\Support\Str;


class CartReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        
        $zonePrefix = getClinicZone();
        $supplier = $this->first()->supplier;
        $wareHouseType = $supplier->wareHouse ? $supplier->wareHouse->warehouse_type : 'own';
        $postalCode = $shippingCost = $deliveryType = $paymentType  = null;
        if($wareHouseType == 'dpharma') { 
            $postalCode = $supplier->wareHouse->postal_code;
        } 
        $products = $findService =  [];
        $shippingCostDetail = [];
        $productWeight = $supplierTotalQty=  $productTotalAmount = $reqAppliedPoints = $grandTotal= $appliedPoints = 0;

        $products = $this->map(function ($cartItem) use($zonePrefix,$wareHouseType,&$productTotalAmount,&$deliveryType,&$reqAppliedPoints,&$paymentType, &$productWeight, &$supplierTotalQty,&$grandTotal) {
            $supPro = ProductRelation::where('product_id',$cartItem->product_id)
                                    ->with(['productRelationStock','productRelationPrice'])
                                    ->where('user_id',$cartItem->supplier_id)
                                    ->first();
                $cartMetaData = $cartItem['meta_data'] ? json_decode($cartItem['meta_data']) : null;

                $cartService = new CartService();
                $cartService->updateTier($cartItem,$supPro,$zonePrefix);

                $priceBase = $supPro->productRelationPrice;
                $singleProductPrice = $supPro->price_type == 'fixed' || $supPro->price_type == 'bonus' ? $priceBase->{$zonePrefix .'_zone_price'} : 
                                    $priceBase->{$zonePrefix .'_'.$cartItem->tier_number.'_base_price'}  ?? 0;
                $productTotalAmount += $singleProductPrice * $cartItem->quantity;

                $deliveryType = $cartMetaData->delivery_type ?? null;
                $reqAppliedPoints = $cartMetaData->applied_points ?? 0;
                $grandTotal = $cartMetaData->grand_total ?? 0;
                $paymentType = $cartMetaData->payment_type ?? 'pay_now';

                $getFreeQty = 0;
                if($supPro->price_type == 'bonus') {
                    $getFreeQty = $this->getFreeQty($supPro, $zonePrefix, $cartItem->quantity, $priceBase,$cartItem->tier_number);
                }
                $defaultImage = $cartItem->product->getResolvedDefaultImageAttribute();
                if($wareHouseType == 'dpharma'){
                   $productWeight += $supPro->products->weight;
                   $supplierTotalQty += $cartItem->quantity;
                }
            return [
                "id"=> encryptParam($cartItem->id),
                "supplier_id"=> encryptParam($cartItem->supplier_id),
                "product_id"=> encryptParam($cartItem->product_id),
                "product_name"=> ucfirst($cartItem->product->name),
                "unit_name"=> ucfirst($cartItem->product->unit->name) ?? null,
                "foams_name"=> ucfirst($cartItem->product->foam->name),
                "is_fav"=> $cartItem->product->fav ? true : false,
                "product_qty"=> $cartItem->quantity,
                "medias" => $defaultImage ? getImage($defaultImage->file_name, $defaultImage->id) : asset('images/default-image.png'), 
                
                "price"=> $supPro->price_type == 'tier' ? $priceBase->{$zonePrefix .'_'.$cartItem->tier_number.'_base_price'}
                            : $priceBase->{$zonePrefix .'_zone_price'},
                "wholesale_pack_size" => $this->getWholesaleSize($supPro,$cartItem),
                'get_free_qty' => $getFreeQty,
               
                                // "bonus_3_quantity_value" =>  $priceBase->{$zonePrefix .'_bonus_3_quantity_value'},
                "is_out_stock" => $this->getStock($supPro) > 0 ? false  : true,
            ];
            
        });

        if($wareHouseType == 'dpharma'){
            $shippingService = new ShippingService();
            $shippingCostDetail = $shippingService->getOrderShipCharge($productWeight,$supplierTotalQty,$postalCode);
        }
            if($shippingCostDetail) {
                $shippingDetail = $shippingCostDetail['result'];
                $shippingDetailArr = array_values(array_filter($shippingDetail, fn($item) => $item['serviceType'] === $deliveryType));
                if (!empty($shippingDetailArr)) {
                    $findService = $shippingDetailArr[0]; 
                    
                }
            }
            $shippingFee = $findService && $findService ?  (float) str_replace(',', '', $findService['charge']): 0;
            $supplierAmount = $productTotalAmount  + $shippingFee;
        

            if($reqAppliedPoints > 0){
                
                $supplierPer = round(($supplierAmount / $grandTotal) * 100);
                $appliedPoints = ($reqAppliedPoints * $supplierPer / 100);

            }
            if($appliedPoints > round($supplierAmount)) {
                throw new \Exception(trans('api.clinic.cart.applied_points_error'));
            }

        return [
            'supplier_id' => encryptParam($supplier->user_id),
            'id' => encryptParam($supplier->user_id),
            "company_name" => pcCompanyName($supplier) ,
            'is_fav' => $supplier->fav ? true: false,
            "delivery_days" => $supplier->delivery_days,
            "min_order" => (int) $supplier->min_order_value,
            "credit_line_limit" => (float) (isset($supplier->ClinicCreditLimit->remaining_amount) &&
                $supplier->ClinicCreditLimit->remaining_amount > 0 ?
                $supplier->ClinicCreditLimit->remaining_amount : 0),
            "supplier_amount"=>round($supplierAmount),
            "shipping_detail" => $shippingCostDetail ?? [],
            "delivery_type"=> $deliveryType,
            "payment_type"=> $paymentType,
            "shipping_fee" => $shippingFee,
            "req_applied_points" => $reqAppliedPoints,
            "applied_points"=> $appliedPoints,
            "products" => $products,

        ];
    }


    private function getStock($supPro)
    {
        $productRelationStock = $supPro->productRelationStock;

        if ($productRelationStock->is_batch_wise_stock) {
            $stock =  $supPro->batches()->sum('available_stock');
        }else{
            $stock =  $productRelationStock->stock;
        }
        if($productRelationStock->stock_type == 'unit') {
          // $stock = $stock / $productRelationStock->wholesale_pack_size;
           $stock = $stock / 1;
        }

       return $stock;
    }

    private function getWholesaleSize($supPro,$cartItem)
    {
        $packSize = $supPro->productRelationStock->wholesale_pack_size;

        return $packSize && $supPro->productRelationStock->stock_type == 'wps'? trans('api.clinic.cart.wolesale_size') . $packSize. ' ' . Str::plural($cartItem->product->container->name) . ' ('. ($packSize * $cartItem->product->quantity_per_unit) . ' ' . Str::plural($cartItem->product->foam->name).')' : null;

    }
    private function getFreeQty($supPro,$zonePrefix,$quantity,$priceBase,$tierNumber) 
    {
        return  $supPro->price_type == 'bonus' && !empty($tierNumber) ?
        floor($quantity / $priceBase->{$zonePrefix.'_'.$tierNumber.'_quantity'}) * $priceBase->{$zonePrefix.'_'.$tierNumber.'_quantity_value'} : 0;
    }
}