<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportMessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
       
        $fromUserName = getMessageFromName($this->user);
       
        $folderPath = config('constants.api.media.support_ticket') . $this->support_ticket_id;
        return [
            "id" => encryptParam($this->id),
            "from_id" => encryptParam($this->from_id),
            "from_name" => ucfirst($fromUserName),
            "message" => $this->message,
            "date" => convertToUserTimezone($this->created_at, 'd/m/Y'),
            "time" => convertToUserTimezone($this->created_at, 'H:i'),
            "files" => $this->getMediaUrls($folderPath),
            "timestamp" => strtotime($this->created_at),
        ];
    }

    private function getMediaUrls($folderPath)
    {
        return $this->getMedia('support-ticket-images')->map(function ($media) use($folderPath) {
            return [
                'id' => encryptParam($media->id),
                'url' => getImage($media->file_name, $folderPath),
                'file_name' => $media->file_name,
                'type' => str_starts_with($media->mime_type, 'image/') ? 'image' : 
                        ($media->mime_type == 'application/pdf' ? 'pdf' : $media->mime_type),
            ];
        });
    }
}