<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;

class ReferFriendResource extends JsonResource {

    public function toArray($request)
    {
        $orderValue = $this->order_value ?? config('constants.api.referral_code.order_value');
        return [
            'id' => encryptParam($this->id),
            'date' => convertToUserTimezone($this->updated_at, 'd-m-Y, H:i'),
            'redeem' => $this->status == 'pending' ? 'Pending' : ( $this->status == 'approved' ? 'Successful' : 'Rejected'),
            'points' => $this->points ? $this->points . ' points' : '-',
            'user_name' => 'When '.ucfirst($this->toUser->name) . ' makes a purchase order exceeding '. $orderValue. ', you will receive these points.'
        ];
    }
}
