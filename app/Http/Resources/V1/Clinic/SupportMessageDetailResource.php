<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportMessageDetailResource extends JsonResource
{

    protected $messages;

    // Constructor to accept messages separately
    public function __construct($resource, $messages)
    {
        parent::__construct($resource);
        $this->messages = $messages;
    }

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

         // Group messages by date
         $groupedMessages = $this->messages->groupBy(function ($msg) {
            return convertToUserTimezone($msg->created_at, 'Y-m-d');
        })->map(function ($group) {
            return SupportMessageResource::collection($group);
        }); 
        $folderPath = config('constants.api.media.support_ticket') . $this->id;
     
        return [
            "id" => encryptParam($this->id),
            "ticket_number" => 'TKT-'.$this->id,
            "status" => ucfirst($this->status) ?? null,
            "subject" => $this->subject ?? null,
            "sender_name" => getMessageFromName($this->receiver) ?? null,
            "unread_message" => $this->unread_message ?? 0,
            "attachments" => $this->getMediaUrls($folderPath),
            "messages" => [
                "data" => $groupedMessages,
                "pagination" => [
                    "total" => $this->messages->total(),
                    "per_page" => $this->messages->perPage(),
                    "current_page" => $this->messages->currentPage(),
                    "last_page" => $this->messages->lastPage(),
                ],
            ],  
            
        ];
    }

     private function getMediaUrls($folderPath)
    {
        return $this->getMedia('support-ticket-images')->where('model_type', 'App\Models\SupportTicket')->map(function ($media) use($folderPath) {
            return [
                'id' => encryptParam($media->id),
                'url' => getImage($media->file_name, $folderPath),
                'file_name' => $media->file_name,
                'type' => $media->mime_type,
            ];
        });
    }
}