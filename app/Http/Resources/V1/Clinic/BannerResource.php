<?php

namespace App\Http\Resources\V1\Clinic;

use App\Models\Brand;
use App\Models\Category;
use App\Models\PcDetail;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class BannerResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "type" => $this->banner_type,
            "image" => $this->image? getImage($this->image,null,false,'banner-default-image.png') : null,
            "redirect_to" => $this->redirect_to && $this->redirectName() ?  $this->redirect_to : null,
            "redirect_to_id" => $this->redirect_to  && $this->redirectName() ?  encryptParam($this->redirect_to_id) : null,
            "redirect_to_name" => $this->redirect_to ? $this->redirectName() : null,
        ];
    }

    private function redirectName()
    {
        $map = [
            'seller' => function ($id) {
                $pcDetail = PcDetail::with('user','clinicPharmaSuppliers')
                            ->where('id', $id)
                            ->first();

                        if (!$pcDetail) {
                            return null;
                        }

                        if (!$pcDetail->is_restricted) {
                            return $pcDetail->company_name;
                        }

                        $approvedSupplier = $pcDetail->clinicPharmaSuppliers
                            ->where('status', 'approved')
                            ->first();

                        return $approvedSupplier ? $pcDetail?->user?->name : null;
            },
            'product' => function ($id) {

                return Product:: with([
                            'productRelations.productRelationPrice',
                            'productRelations.productRelationStock',
                        ])
                        ->whereHas('productRelations', function ($query) {
                            $query->whereHas('productRelationPrice')
                                ->whereHas('productRelationStock');
                        })->where('id', $id)
                    ->whereNull('deleted_at')
                    ->value('name');
            },
            'category' => function ($id) {
                return Category::where('id', $id)
                    ->whereNull('deleted_at')
                    ->value('name');
            },
            'brand' => function ($id) {
                return Brand::where('id', $id)
                    ->whereNull('deleted_at')
                    ->value('name');
            },
        ];

        $type = $this->redirect_to;
        $id = $this->redirect_to_id;
        
        if (isset($map[$type])) {
            return $map[$type]($id);
        }

        return null;
    }

    

}
