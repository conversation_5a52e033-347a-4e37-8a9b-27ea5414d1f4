<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class RestrictedSuppliersResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            'user_id' => encryptParam($this->user_id),
            'user_name' => ucfirst($this->user->name),
            'name' => pcCompanyName($this),
            'photo' => getImage($this->user->photo, 'users'),

        ];
    }
}
