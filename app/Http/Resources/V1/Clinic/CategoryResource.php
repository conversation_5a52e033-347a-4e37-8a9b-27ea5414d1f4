<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class CategoryResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "name" => $this->name,
            "serial_number" => $this->serial_number,
            "sub_categories" => $this->whenLoaded('children', function () {
                return $this->children->map(function ($sub) {
                    return [
                        'name' => $sub->name,
                        'serial_number' => $sub->serial_number,
                        'id' => encryptParam($sub->id) // Corrected to use $sub->id
                    ];
                });
            }),
        ];
    }
}
