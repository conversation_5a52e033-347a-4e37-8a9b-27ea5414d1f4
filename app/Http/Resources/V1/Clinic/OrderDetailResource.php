<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use App\Models\DpharmaPoint;
use Carbon\Carbon;

class OrderDetailResource extends JsonResource
{
    public function toArray($request): array
    {

        $subOrderIds = $this->subOrder->whereNotIn('status', ['rejected', 'canceled'])->pluck('id')->toArray();
        $subOrderPoints = $this->subOrder->whereNotIn('status', ['rejected', 'canceled'])
                                    ->pluck('earn_points')->sum();
        
        $dpharmaPoint = DpharmaPoint::where('user_id',$this->user_id)
            ->whereIn('reference_id', $subOrderIds)
            ->where('reference_value', 'suborder')
            ->pluck('points')->sum();

        $hasPayNowOrLater = $this->subOrders->contains(function ($subOrder) {
            return in_array($subOrder->payment_type, ['pay_now', 'pay_later']);
        });

        $createdAt = Carbon::parse($this->created_at); 
        $hoursDiff = $createdAt->diffInHours(Carbon::now());

        $isRetryPayment = ($this->payment_status == 'failed' || $this->payment_status == 'init')  ? true : (($this->payment_status == 'pending' && $hasPayNowOrLater &&
            !($this->transaction) ? true : false));

        $isRetryPayment = ($hoursDiff <= 24 && $isRetryPayment) ? true : false;

        $invoicePath = config('constants.api.order_invoices.main');
        return [
            "id" => encryptParam($this->id),
            "order_number" => '#'.$this->order_number,
            "order_placed" => convertToUserTimezone($this->created_at, 'd M Y  H:i'),
            "order_status" => ucfirst($this->status),
            "total_amount" => $this->amount,
            "total_items" => $this->order_products_count,
            "total_suppliers" => $this->sub_order_count,

            "billing_address_1" => $this->billing_address_1,
            "billing_address_2" => $this->billing_address_2,
            "billing_city_id" => $this->billing_city_id,
            "billing_city_name" => $this->billingCity->name,
            "billing_state_id" => $this->billing_state_id,
            "billing_state_name" => $this->billingState->name,
            "billing_postal_code" => $this->billing_postal_code,
            "billing_country_id" => $this->billing_country_id,
            "billing_country_name" => $this->billingCountry->name,

            "shipping_address_1" => $this->shipping_address_1,
            "shipping_address_2" => $this->shipping_address_2,
            "shipping_city_id" => $this->shipping_city_id,
            "shipping_city_name" => $this->shippingCity->name,
            "shipping_state_id" => $this->shipping_state_id,
            "shipping_state_name" => $this->shippingState->name,
            "shipping_country_id" => $this->shipping_country_id,
            "shipping_postal_code" => $this->shipping_postal_code,
            "shipping_country_name" => $this->shippingCountry->name,
            'order_invoice_path' =>  $this->pdf_path ? Storage::url($invoicePath .$this->pdf_path) : null, 
            "earn_points" => round($dpharmaPoint,2),
            "will_earn_points" =>round(($subOrderPoints - $dpharmaPoint),2),
            "payment_status" => $hasPayNowOrLater ?  ($this->payment_status == 'init' ? 'Payment Initiate' :  $this->payment_status) : 'Credit Line',
            'is_retry_payment' => $isRetryPayment,
             "payment_reason" => $this->payment_status == 'init'  || ($this->payment_status =='pending' && $hasPayNowOrLater && !($this->transaction)) ? 
                                trans('api.clinic.order.retry_payment_status')  : 
                                ($this->payment_status == 'pending' && $hasPayNowOrLater && $this->ecommerce_tran_id != null  && $this->transaction  ?  trans('api.clinic.order.pending_payment_status') :
                                     ( $this->transaction && $this->payment_status != 'paid'   ? $this->transaction->failure_reason : null)) ,
           
            "sub_order_details" => SubOrderDetailResource::collection($this->subOrder),
        ];
    }
}
