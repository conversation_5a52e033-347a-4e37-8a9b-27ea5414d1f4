<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Services\CartService;
use Illuminate\Http\Request;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $cartService = new CartService();
        $isShipping = ($request->has('is_shipping')) && $request->input('is_shipping') ? true : false;
        
        $processedData = $cartService->processCartListingData($this, $isShipping);
        
        return [
            'supplier_id' => $processedData['supplier']['supplier_id'],
            'id' => $processedData['supplier']['id'],
            "business_name" => $processedData['supplier']['business_name'],
            "company_name" => $processedData['supplier']['company_name'],
            "user_name" => $processedData['supplier']['user_name'],
            "payment_type" => $processedData['payment_type'] ?? 'pay_now',
            'is_fav' => $processedData['supplier']['is_fav'],
            'dpharma_logistic' => $processedData['supplier']['dpharma_logistic'],
            "delivery_days" => $processedData['supplier']['delivery_days'],
            "min_order" => $processedData['supplier']['min_order'],
            "credit_line_limit" => $processedData['supplier']['credit_line_limit'],
            "shipping_detail" => $processedData['shipping_detail'],
            "req_applied_points" => $processedData['req_applied_points'],
            "delivery_type" => $processedData['delivery_type'],
            "shipping_fee" => $processedData['shipping_fee'],
            "products" => $processedData['products'],
        ];
    }
}