<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;
class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        $hasPayNowOrLater = $this->subOrders->contains(function ($subOrder) {
            return in_array($subOrder->payment_type, ['pay_now', 'pay_later']);
        });

        $createdAt = Carbon::parse($this->created_at); 
        $hoursDiff = $createdAt->diffInHours(Carbon::now());
        $paymentReason = $hoursDiff <= 24 ? true : false;

        $invoicePath = config('constants.api.order_invoices.main');

        return [
            "id" => encryptParam($this->id),
            "order_number" => '#'.$this->order_number,
            "order_placed" => convertToUserTimezone($this->created_at, 'd M Y  H:i'),
            "amount" => $this->amount,
            "total_items" => $this->order_products_count,
            "total_suppliers" => $this->sub_order_count,
            "order_status" => $this->status,
            "payment_status" => $hasPayNowOrLater ?  ($this->payment_status == 'init' ? 'Payment Initiate' :  $this->payment_status) : 'Credit Line',
            "transaction_status" => $this->transaction->status ?? 'pending',
            "payment_reason" => $hoursDiff <= 24 ? $this->payment_status == 'init' || ($this->payment_status =='pending' && $hasPayNowOrLater && !($this->transaction)) ? 
                                trans('api.clinic.order.retry_payment_status') : 
                                ($this->payment_status == 'pending' && $hasPayNowOrLater && $this->ecommerce_tran_id != null  && $this->transaction  ? trans('api.clinic.order.pending_payment_status')  :
                                     ( $this->transaction && $this->payment_status != 'paid' ? $this->transaction->failure_reason : null)) : null ,
            'order_invoice_path' =>  $this->pdf_path ? Storage::url($invoicePath .$this->pdf_path) : null, 

        ];
    }
}
