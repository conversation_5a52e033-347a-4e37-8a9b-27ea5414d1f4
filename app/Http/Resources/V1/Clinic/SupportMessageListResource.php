<?php

namespace App\Http\Resources\V1\Clinic;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SupportMessageListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $message = $this->latestMessage;
        return [
            "id" => encryptParam($this->id),
            "sender_id" => encryptParam($this->sender_id),
            "receiver_name" => getMessageFromName($this->receiver) ?? null,
            "receiver_photo" => getImage($this->receiver->photo, 'users'), 
            "order_number" => $this->order->order_number ?? null,
            "status" => $this->status,
            "message" => $message?->message ?? null,
            "latest_message_time" => $this->formatMessageTime($message?->created_at),
            "unread_messages_count" => $this->unread_messages_count,
        ];
    }

    private function formatMessageTime($timestamp)
    {
        if (!$timestamp) {
            return null;
        }

        $date = Carbon::parse($timestamp);
        $now = Carbon::now();

        if ($date->isToday()) {
            return "Today";
        } elseif ($date->isYesterday()) {
            return "Yesterday";
        } elseif ($date->greaterThan($now->subDays(7))) {
            return $date->format('l'); // Show "Monday", "Tuesday", etc.
        } else {
            return $date->format('d/m/y'); // Show full date like 24/02/20
        }
    }
}