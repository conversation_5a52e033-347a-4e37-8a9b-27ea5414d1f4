<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class HelpSupportResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            "id" => encryptParam($this->id),
            "issue_type" => $this->supportCategory->name,
            "issue_type_id" => encryptParam($this->category_id),
            "subject" => $this->subject,
            "receiver_name" => $this->receiver_id == 1 ? 'DPharma' :  pcCompanyName($this->pcDetail),
            "ticket_number" => 'TKT-'.$this->id,
            "order_number" =>  'ORD-'.$this->order->order_number,
            "order_id" =>  encryptParam($this->order->id),
            "description" => $this->description,
            "unread_msg_count" => $this->unreadMessages->count(), 
            //"image" => $this->getMedia('images')->first()?->getUrl(),
            "status" => ucfirst($this->status),
            "date_raised" => convertToUserTimezone($this->created_at, 'd-m-Y H:i'),
            "last_updated" => convertToUserTimezone($this->updated_at, 'd-m-Y H:i'),
            "color" => $this->receiver_id == 1 ?'text-secondary-500':'text-info-500',
        ];
    }
}