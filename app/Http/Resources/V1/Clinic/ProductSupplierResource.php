<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ProductSupplierResource extends JsonResource {

    public function toArray($request)
    {
        $zonePrefix = getClinicZone();

        $minQuantity = $this->price_type == 'tier' ? $this->{$zonePrefix . '_tier_1_min_quantity'} : 1;
        $maxQuantity = $zonePrefix === 'east'
            ? $this->getMaxQuantity('east')
            : $this->getMaxQuantity('west');
        return [
            'id' => encryptParam($this->user_id),
            'product_id' => encryptParam($this->product_id),
            "user_name" => ucfirst($this->user_name),
            "photo" => getImage($this->photo, 'users'), //
            "business_name" => ucfirst($this->business_name),
            "company_name" => $this->company_name == null ? $this->business_name : $this->company_name,
            "is_bonus_pricing" => $this->is_bonus_pricing,
            "is_tier_pricing" => $this->is_tier_pricing,
            "quantity_per_unit" => $this->quantity_per_unit,

            "bonus_1_quantity" =>   $this->{$zonePrefix .'_bonus_1_quantity'},
            "bonus_1_quantity_value" =>  $this->{$zonePrefix .'_bonus_1_quantity_value'},

            "bonus_2_quantity" =>  $this->{$zonePrefix .'_bonus_2_quantity'},
            "bonus_2_quantity_value" =>  $this->{$zonePrefix .'_bonus_2_quantity_value'},

            "bonus_3_quantity" =>  $this->{$zonePrefix .'_bonus_3_quantity'},
            "bonus_3_quantity_value" =>  $this->{$zonePrefix .'_bonus_3_quantity_value'},
        
            "tier_1_min_quantity" => $minQuantity,
            "tier_1_max_quantity" =>  $this->{$zonePrefix .'_tier_1_max_quantity'},
            "tier_1_base_price" =>  $this->{$zonePrefix .'_tier_1_base_price'},
            "tier_2_min_quantity" =>  $this->{$zonePrefix .'_tier_2_min_quantity'},
            "tier_2_max_quantity" =>  $this->{$zonePrefix .'_tier_2_max_quantity'},
            "tier_2_base_price" =>  $this->{$zonePrefix .'_tier_2_base_price'},
            "tier_3_min_quantity" =>  $this->{$zonePrefix .'_tier_3_min_quantity'},
            "tier_3_max_quantity" =>  $this->{$zonePrefix .'_tier_3_max_quantity'},
            "tier_3_base_price" =>  $this->{$zonePrefix .'_tier_3_base_price'},

            'min_quantity' =>  $minQuantity,
            'max_quantity' => $this->price_type == 'tier' ?
            ($maxQuantity === true ? null : $maxQuantity) : $this->stock ,
            "stock" => $this->stock_type == 'unit' ? floor($this->stock / 1): $this->stock,
            "wholesale_pack_size" => $this->wholesale_pack_size && $this->stock_type == 'wps'? trans('api.clinic.cart.wolesale_size') . $this->wholesale_pack_size . ' ' . Str::plural($this->container_name) . ' ('. ($this->wholesale_pack_size  * $this->quantity_per_unit) . ' ' . Str::plural($this->dosage_foams_name).')' : null,
            "expires_on_after" => $this->expires_on_after,
            "zone_price" => $this->{$zonePrefix .'_zone_price'},
            "is_admin_verified" => $this->is_admin_verified,
            "is_fav" => $this->is_fav ? true:false,
            
            "delivery_days" => $zonePrefix == 'east' ? $this->delivery_days : ((int)$this->delivery_days_west ?? null),
            "min_order" => $this->min_order_value ? (int)$this->min_order_value:  null,
            "supplier_rating" => null,
            "supplier_rating_count" => 0,
            "cart_quantity" =>  1 , //$this->quantity ?? $minQuantity,
            'price_type' => $this->price_type
        ];
    }

    private function getMaxQuantity($zone)
    {
        $tier3 = $this->{$zone . '_tier_3_max_quantity'} != null ? $this->{$zone . '_tier_3_max_quantity'} : true;
        $tier2 = $this->{$zone . '_tier_2_max_quantity'} != null ? $this->{$zone . '_tier_2_max_quantity'} : true ;
        $tier1 = $this->{$zone . '_tier_1_max_quantity'};
        return $tier3 ?? $tier2 ?? $tier1;
    }
}
