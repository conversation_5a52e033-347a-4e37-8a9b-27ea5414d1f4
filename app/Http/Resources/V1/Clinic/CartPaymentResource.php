<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\ProductRelation;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Service\ShippingService;


class CartPaymentResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $zonePrefix = getClinicZone();
        $supplier = $this->first()->supplier;
        $wareHouseType = $supplier->wareHouse ? $supplier->wareHouse->warehouse_type : 'own';
        $postalCode = $shippingCost = null;
        if($wareHouseType == 'dpharma') { 
            $postalCode = $supplier->wareHouse->postal_code;
        } 
        $products = [];
        $productTotalAmount = 0;
        $shippingCostDetail = [];
        $products = $this->map(function ($cartItem) use($zonePrefix,&$productTotalAmount,$wareHouseType) {
            $supPro = ProductRelation::with('productRelationPrice')->where('product_id',$cartItem->product_id)
                                    ->where('user_id',$cartItem->supplier_id)
                                    ->first();
            $basePrice = $supPro->productRelationPrice;
            $singleProductPrice = $supPro->price_type == 'fixed' || $supPro->price_type == 'bonus' ? $basePrice->{$zonePrefix .'_zone_price'} : 
                                    $basePrice->{$zonePrefix .'_'.$cartItem->tier_number.'_base_price'}  ?? 0;
            $productTotalAmount += $singleProductPrice * $cartItem->quantity;
           
            return [
                "id"=> encryptParam($cartItem->id),
                "supplier_id"=> encryptParam($cartItem->supplier_id),
                "product_id"=> encryptParam($cartItem->product_id),
                "product_qty"=> $cartItem->quantity,
                "price"=> $singleProductPrice,
            ];
    
        });

         $supplierId = $supplier->user_id;

        if($wareHouseType == 'dpharma'){
            $requestSup = request('suppliers');
            $shippingCostDetail = $this->supShippingDetail($requestSup,encryptParam($supplierId));
        }
        $shippingFee = $shippingCostDetail && $shippingCostDetail['findService'] ? ((float) str_replace(',', '', $shippingCostDetail['findService']['charge'])): 0;
        $supplierAmount = $productTotalAmount + $shippingFee;
       
        $paymentType = $request->paymentTypeMapping[$supplierId];
        $appliedPoints = 0;
        if(($request->has('applied_points')) && $request->input('applied_points') > 0){
            $points = $request->input('applied_points');
           
             $supplierPer = round(($supplierAmount / $request->grandTotal) * 100);
             $appliedPoints = ($points * $supplierPer / 100);

        } 
        if($appliedPoints > round($supplierAmount)) {
            throw new \Exception(trans('api.clinic.cart.applied_points_error'));
        }
        return [
            'supplier_id' => encryptParam($supplierId),
            "business_name" => $supplier->business_name,
            "company_name" => $supplier->company_name,
            'payment_type' => $paymentType,
            "delivery_days" => $supplier->delivery_days,
            "min_order" => $supplier->min_order_value,
            "credit_line_limit" =>  (float)(isset($supplier->ClinicCreditLimit->remaining_amount) &&
            $supplier->ClinicCreditLimit->remaining_amount > 0 ?
             $supplier->ClinicCreditLimit->remaining_amount : 0),
            "delivery_type"=> $shippingCostDetail && $shippingCostDetail['findService'] ?  $shippingCostDetail['findService']['serviceType']: null,
            "shipping_fee" =>  $shippingFee,
            "shipping_detail" => $shippingCostDetail ? $shippingCostDetail['shippingDetail']['shipping_detail'] : [],
            "applied_points"=> $appliedPoints,
            "supplier_amount"=>round($supplierAmount),
            'dpharma_logistic' => $wareHouseType == 'dpharma' ? true : false,
            "products" => $products

        ];
    }




    public function supShippingDetail($requestSup,$supIdToFind) {

        $shippingCostDetail = [];
        $supShippingDetail = [];
        $findService = [];
        $filteredSupArray = array_values(array_filter($requestSup, function($requestItem) use ($supIdToFind) {
            return $requestItem['id'] === $supIdToFind;
        }));
            
        if (!empty($filteredSupArray)) {
            
            $supShippingDetail = $filteredSupArray[0];
            
            $targetServiceType = $supShippingDetail['delivery_type'];
            $shippingDetail = $supShippingDetail['shipping_detail']['result'];
            $shippingDetailArr = array_values(array_filter($shippingDetail, fn($item) => $item['serviceType'] === $targetServiceType));
            if (!empty($shippingDetailArr)) {
                $findService = $shippingDetailArr[0]; 
            }
        }
        $shippingCostDetail = [
            'findService' => $findService,
            'shippingDetail' => $supShippingDetail,
        ];
      
        return $shippingCostDetail;
    }
}