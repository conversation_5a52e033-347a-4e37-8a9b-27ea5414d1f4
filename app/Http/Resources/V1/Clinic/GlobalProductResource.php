<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class GlobalProductResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "name" => ucfirst($this->name),
            "generic_name" => ucfirst($this->generic_name),
            "category_name" => ucfirst($this->category->name),
            "sub_category_name" => ucfirst($this->subcategory->name),
            "brand_name" => ucfirst($this->brand->name),
            "seller_name" => $this->pcInfo,

        ];
    }
}
