<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ProductResource extends JsonResource {

    public function toArray($request)
    {

        return [
            'id' => encryptParam($this->id),
            "name" => strtoupper($this->name),
            "generic_name" => ucfirst($this->generic_name),
            "unit_name" => ucfirst($this->unit_name),
           // "medias" => $media,
            "is_fav" => $this->is_fav,
            "lowest_price" => $this->min,
            "highest_price" => $this->max,
            "brand_name" => ucfirst($this->brand_name),
            "default_image_id" => getImage($this->media_file_name, $this->media_id),
        ];
    }
}