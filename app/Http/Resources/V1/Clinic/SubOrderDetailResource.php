<?php

namespace App\Http\Resources\V1\Clinic;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use App\Models\DpharmaPoint;

class SubOrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $invoicePath = config('constants.api.order_invoices.supplier_invoice');
        $pointText = null;
        if($this->earn_points && $this->status != 'rejected' && $this->status != 'canceled') {
            $dpharmaPoint = DpharmaPoint::where('reference_value','suborder')
            ->where('reference_id',$this->id)
            ->pluck('points')->first();
            
            $pointText = $dpharmaPoint > 0 ? __('api.clinic.earning_point', ['POINT' => ($dpharmaPoint)])  : __('api.clinic.will_earn_point', ['POINT' => ($this->earn_points)]);
        }
        return [
            "id" => encryptParam($this->id),
            "supplier_id" => encryptParam($this->user_id),
            "supplier_name" => pcCompanyName($this->pcDetail) ?? " ",
            "supplier_photo" => getImage($this->user->photo, 'users'),
            "invoice_total_item_amount" => $this->invoice_total_item_amount,
            "total_sub_order" => $this->total_sub_order_value,
            "status" => $this->status,
            "rejected_note" => $this->rejected_note ?? null,
            "total_amount" => $this->total_amount,
            "updated_at" => convertToUserTimezone($this->updated_at, 'd M Y H:i'),
            "dpharma_points_used" => $this->total_dpharma_points_used,
            "shipping_amount" => $this->total_shipping_amount,
            "payment_type" => $this->payment_type,
            'invoice_path' =>  $this->getMedia('invoices')->last()?->getUrl(),
            'point' => $pointText,
            'products' => ProductOrderDetailResource::collection($this->orderProducts),
            'ship_status' => [
                'order_placed' =>  convertToUserTimezone($this->created_at, 'd M Y H:i') ?? null, 

                'order_confirmed' => $this->approved_at 
                                    ? convertToUserTimezone($this->approved_at, 'd M Y H:i') 
                                    : null,

                'order_ready_for_pickup' => $this->ready_for_pickup_at 
                                            ? convertToUserTimezone($this->ready_for_pickup_at, 'd M Y H:i') 
                                            : null,

                'order_shipped' =>  $this->in_transit_at 
                                    ? convertToUserTimezone($this->in_transit_at, 'd M Y H:i') 
                                    : null,
                
                'order_cancelled' => $this->order->cancelled_at 
                                        ? convertToUserTimezone($this->order->cancelled_at, 'd M Y H:i') 
                                        : null,

                'order_rejected' => $this->rejected_at 
                                        ? convertToUserTimezone($this->rejected_at, 'd M Y H:i') 
                                        : null,

                'order_delivered' =>  $this->deliver_at 
                                    ? convertToUserTimezone($this->deliver_at, 'd M Y H:i') 
                                    : null,
            ]
        ];
    }
}
