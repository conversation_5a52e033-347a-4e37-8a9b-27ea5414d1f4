<?php

namespace App\Http\Resources\V1\Clinic;

use App\Http\Resources\CustomCollection;
use App\Models\ProductRelation;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class GlobalProductSearchResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' =>encryptParam($this->id),
            'matched_in_name' => $this->matched_in_name,
            'matched_in_generic_name' => $this->matched_in_generic_name,
            'matched_in_category' => $this->matched_in_category,
            'matched_in_subcategory' => $this->matched_in_subcategory,
            'matched_in_brand' => $this->matched_in_brand,
            'matched_in_supplier' => $this->matched_in_supplier,
            'matched_in_distributor' => $this->matched_in_distributor,
            'name' => strtoupper($this->name),
            'generic' => $this->matched_in_generic_name == 1 ? [
                'id' => $this->generic_name_id,
                'name' => $this->generic_name,
            ] : null,
            
            'category' => $this->matched_in_category ? [
                'id' => $this->category_id,
                'name' => $this->category_name,
            ] : null,
            'subcategory' => $this->matched_in_subcategory ? [
                'id' => $this->sub_category_id,
                'category' => $this->category_name ?? null,
                'name' => $this->sub_category_name,
            ] : null,
            'brand' => $this->matched_in_brand ? [
                'id' => $this->brand_id,
                'name' => $this->brand_name,
            ] : null,
            'distributor' => $this->matched_in_distributor ? $this->distributors($request) : null,
            'pc_info'=> $this->matched_in_supplier ? $this->suppliers() : null,
        ];

    }

    public function suppliers(){

        $authId = Auth::user()->id;
        
        $suppliers = DB::table('products_relation')
        ->select([
            'pc_details.user_id',
            'pc_details.company_name',
            'pc_details.business_name', 
            'pc_company_types.name as company_type_name',
            'users.name as user_name'
        ])
        ->join('pc_details', function ($join) use ($authId) {
            $join->on('pc_details.user_id', '=', 'products_relation.user_id')
                ->where(function ($query) use ($authId) {
                    $query->where('pc_details.is_restricted', false)
                        ->orWhereExists(function ($subquery) use ($authId) {
                            $subquery->select(DB::raw(1))
                                ->from('clinic_pharma_suppliers as cps')
                                ->whereColumn('cps.pc_id', 'pc_details.user_id')
                                ->where('cps.clinic_id', $authId)
                                ->where('cps.status', 'approved');
                        });
                });
        })
        ->leftJoin('pc_company_types', 'pc_company_types.id', '=', 'pc_details.company_type_id')
        ->leftJoin('users', 'users.id', '=', 'pc_details.user_id')
        ->where('products_relation.product_id', $this->id)
        ->where('products_relation.pc_approval', true)
        ->where('products_relation.admin_approval', true)
        ->where('products_relation.user_id', $this->user_id)
        ->get();
        
        return new CustomCollection($suppliers, PCInfoResource::class);
    }

    public function distributors($request){
        
        $keyword = $request->input('search', '');
        return DB::table('distributor_product')
                    ->where('product_id', $this->id)
                    ->Where('distributors.name', 'ILIKE', "%{$keyword}%")
                    ->select('distributors.name')
                ->join('distributors', 'distributors.id', '=', 'distributor_product.distributor_id')->get();
    }

}
