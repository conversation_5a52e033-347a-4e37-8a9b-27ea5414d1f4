<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class ProductOrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $defaultImage = $this->product->getResolvedDefaultImageAttribute();
        return [
            "id" => encryptParam($this->id),
            "product_id" => encryptParam($this->product_id),
            "product_name" => ucfirst($this->product->name),
            "product_deleted_at" => $this->product->deleted_at ? true : false,
            "status" => $this->status,
            "image" =>  $defaultImage ? getImage($defaultImage->file_name, $defaultImage->id) : asset('images/default-image.png'), 
            "quantity" => $this->quantity,
            "total_price" => $this->total_price,
            "free_qty" => $this->bonus_final_qty ?? 0,
        ];
    }
}
