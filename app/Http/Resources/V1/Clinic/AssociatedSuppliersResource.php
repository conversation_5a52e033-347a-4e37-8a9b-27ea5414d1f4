<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;

class AssociatedSuppliersResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            'clinic_id' => encryptParam($this->clinic_id),
            'pc_id' => encryptParam($this->pc_id),
            'status' => $this->status ?? 'pending',
            'pc_name' => $this->pcDetail->name,
            'business_name' => $this->pcInfo->business_name,
            'company_name' => pcCompanyName($this->pcInfo),  
            'account_number' => $this->account_number,
            'logo' => getImage($this->pcDetail->photo, 'users')
        ];
    }
}
