<?php

namespace App\Http\Resources\V1\Clinic;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\App;

class SupportUnreadMessagesListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $timezone = App::bound('userTimezone') ? App::make('userTimezone') : config('app.timezone');

        $createdAt = Carbon::parse($this->created_at);
        // Convert to Malaysia Time (UTC+8)
        $createdAtMalaysia = $createdAt->setTimezone($timezone);

        // Then apply your formatting logic
        if ($createdAtMalaysia->diffInSeconds(now($timezone)) < 1) {
            $formatted = 'less than a second ago';
        } elseif ($createdAtMalaysia->diffInSeconds(now($timezone)) < 60) {
             $formatted =  'less than a seconds ago';
        } elseif ($createdAtMalaysia->isToday()) {
            $formatted = 'Today at ' . $createdAtMalaysia->format('h:i A');
        } elseif ($createdAtMalaysia->isYesterday()) {
            $formatted = 'Yesterday at ' . $createdAtMalaysia->format('h:i A');
        } else {
            $formatted = $createdAtMalaysia->format('F d, Y \a\t h:i A');
        }

        return [
            "id" => encryptParam($this->id),
            "thread_id" => encryptParam($this->thread_id),
            "sender_name" => ucfirst(getMessageFromName($this->user)) ?? null, 
            "order_number" => $this->ticket->order->order_number,
            "ticket_receiver" => getMessageFromName($this->ticket->receiver) ?? null,
            "created_at" => $formatted ?? null,
            "message" => $this->message
        ];
    }
}