<?php

namespace App\Http\Resources\V1\Clinic;

use App\Models\SubOrder;
use Illuminate\Http\Resources\Json\JsonResource;

class PharmaPointResource extends JsonResource {

    public function toArray($request)
    {
        $associated_reference_id = null;

        if ($this->reference_value === 'order') {
            $associated_reference_id = $this->reference_id;
        } elseif ($this->reference_value === 'suborder') {
            $subOrder = SubOrder::find($this->reference_id);
            $associated_reference_id = $subOrder?->order_id;
        }

        return [
            'id' => encryptParam($this->id),
            'date' => convertToUserTimezone($this->updated_at, 'd-m-Y, H:i'),
            'description' => $this->description,
            'points' => $this->points ?? '-',
            'redeem' => $this->redeem ?? '-',
            'balance' => number_format($this->balance ,2) . ' points',
            'associated_reference_id' => $associated_reference_id ? encryptParam($associated_reference_id) : null,
            'associated_reference_value' => in_array($this->reference_value, ['order', 'suborder']) ? 'order' : $this->reference_value,
        ];
    }
}
