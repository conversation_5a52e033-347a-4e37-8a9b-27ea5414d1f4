<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use App\Http\Resources\CustomCollection;

class ProductDetailResource extends JsonResource {

    public function toArray($request)
    {
        $defaultImage = $this->getResolvedDefaultImageAttribute();
        
        return [
            'id' => encryptParam($this->id),
            "name" => ucfirst($this->name),
            "description_active_benefits" => $this->description_active_benefits,
            "description_strength" => $this->description_strength,
            "category_name" => ucfirst($this->category->name),
            "sub_category_name" => ucfirst($this->subcategory->name) ?? null,
            "foam_name" => ucfirst($this->foam->name),
            "description_indications" => $this->description_indications,
            "description_contradictions" => $this->description_contradictions,
            "description_how_to_use" => $this->description_how_to_use,
            "description_storage_instructions" => $this->description_storage_instructions,
            "description_side_effects" => $this->description_side_effects,
            "description_faq" => $this->description_faq,
            "product_description" => $this->product_description,
            "description_dosage" => $this->description_dosage,
            "description_ingredients" => $this->description_ingredients,
            "description_safety_information" => $this->description_safety_information,
            "weight" => $this->weight ?? null,
            "brand_name" => ucfirst($this->brand->name) ?? null,
            "unit_name" => ucfirst($this->unit->name) ?? null,
            "unit_short_form" => $this->unit->short_form ?? null,
            "generic_name" => $this->generic->name ?? null,
            "volumn" => $this->quantity_per_unit ?? null,
            "is_fav" => $this->fav ? true : false,
            'distributors' => DistributorResource::collection($this->distributors),
            "default_image_id" => $defaultImage ? getImage($defaultImage->file_name, $defaultImage->id) : asset('images/default-image.png'),
            "medias" =>  $this->medias ? $this->medias->map(function ($media) {
                return [
                    'file_name' => $media->file_name,
                    'url' => getImage($media->file_name, $media->id)
                    ];
            }) : [],
        
        ];
    }
}
