<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class SupplierResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->user_id),
            'business_name' => ucfirst($this->business_name),
            'company_name' => pcCompanyName($this),
            'is_featured' => $this->is_featured ?? false,
            'user_name' => ucfirst($this->user->name),
            'photo' =>   getImage($this->user->photo, 'users'), // default-image
            'is_fav' => $this->is_fav ?? false,
            'associate_status' => (Auth::user() && $this->clinicPharmaSupplier && $this->is_restricted) ? $this->clinicPharmaSupplier->status :  null
        ];
    }
}
