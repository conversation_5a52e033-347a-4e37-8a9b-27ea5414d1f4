<?php

namespace App\Http\Resources\V1\Clinic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

class BrandResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "name" => ucfirst($this->name),
            "logo" => Storage::url($this->logo), 
            "status" => $this->status,
        ];
    }
}
