<?php

namespace App\Http\Resources\V1;

use Illuminate\Http\Resources\Json\JsonResource;

class UserAddressResource extends JsonResource {

    public function toArray($request)
    {
        return [
            'id' => encryptParam($this->id),
            "address_1" => $this->address_1,
            "address_2" => $this->address_2,
            "city_id" => encryptParam($this->city_id),
            "city_name" => $this->city->name,
            "state_id" => encryptParam($this->state_id),
            "state_name" => $this->state->name,
            "country_id" => encryptParam($this->country_id),
            "country_name" => $this->country->name,
            "is_default" => $this->is_default ? true : false,
            "postal_code" => $this->postal_code,
            "is_same_as_billing" => $this->is_same_as_billing ?? false,
            "nick_name" => $this->nick_name ?? null,
        ];
    }
}
