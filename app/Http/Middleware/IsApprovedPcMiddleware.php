<?php

namespace App\Http\Middleware;

use Closure;
use App\Models\User;
use Illuminate\Http\Request;
use Filament\Pages\Dashboard;
use Filament\Facades\Filament;
use App\Filament\Pc\Pages\EditProfile;
use App\Filament\Pc\Pages\ReviewProfile;
use App\Filament\Pc\Pages\ApprovalProfile;
use App\Filament\Pc\Pages\RejectedProfile;
use Symfony\Component\HttpFoundation\Response;
use App\Filament\Pc\Pages\UnderVerificationProfile;
use Illuminate\Support\Facades\Route;

class IsApprovedPcMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Filament::auth()->user();
        // dd($user);
        if (!empty($user->parent_id)) {
            $user = User::find($user->parent_id);
        }
        if (!empty($user) && $user->is_admin_verified == true && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == true && (
            $request->route()->getName() === UnderVerificationProfile::getRouteName() || $request->route()->getName() === Dashboard::getRouteName()
        ) && $user->pcDetails?->is_first_login == true) {

            Filament::getCurrentPanel()->sidebarWidth('25rem');
            return to_route(ApprovalProfile::getRouteName());
        }
        if (!empty($user) && $user->is_admin_verified == true && empty($user->rejection_reason)) {
            Filament::getCurrentPanel()->sidebarWidth('25rem');
            return $next($request);
        }
        if (
            $request->route()->getName() === EditProfile::getRouteName() || $request->route()->getName() === ReviewProfile::getRouteName()
            || $request->route()->getName() === UnderVerificationProfile::getRouteName() || $request->route()->getName() === RejectedProfile::getRouteName() || $request->route()->getName() === ApprovalProfile::getRouteName() || $request->route()->getName() === 'filament.pc.pages.sf-filament-2fa.2fa'
        ) {
            if (! empty($user) && $user->is_admin_verified == true) {
                if ($request->route()->getName() === RejectedProfile::getRouteName() || $request->route()->getName() === UnderVerificationProfile::getRouteName()) {
                    if (empty($user->admin_verified_on)) {
                        return to_route(ApprovalProfile::getRouteName());
                    } elseif ($user->pcDetails?->is_submitted == false) {
                        return to_route(Dashboard::getRouteName());
                    }
                } elseif ($request->route()->getName() === ApprovalProfile::getRouteName()) {
                    return $next($request);
                } else {
                    Filament::getCurrentPanel()->sidebarWidth('25rem');
                    return $next($request);
                }
            } elseif (! empty($user) && $user->is_admin_verified == false && $user->pcDetails?->is_submitted == false && $user->pcDetails?->step == 5) {
                return $next($request);
            } elseif (! empty($user) && $user->is_admin_verified == false && !empty($user->rejection_reason && $request->route()->getName() === UnderVerificationProfile::getRouteName())) {
                return to_route(RejectedProfile::getRouteName());
            }

            return $next($request);
        }
        if (empty($user)) {
            return $next($request);
        }


        if (! empty($user) && $user->is_admin_verified == true && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == true) {
            Filament::getCurrentPanel()->sidebarWidth('25rem');
            if (Route::currentRouteName() != UnderVerificationProfile::getRouteName()) {
                return to_route(UnderVerificationProfile::getRouteName());
            }
        } elseif ($user->is_admin_verified == false && empty($user->rejection_reason) && $user->pcDetails?->step < 5) {
            return to_route(EditProfile::getRouteName());
        } elseif ($user->is_admin_verified == false && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == true) {

            return to_route(UnderVerificationProfile::getRouteName());
        } elseif ($user->is_admin_verified == true && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == true) {
            return to_route(ApprovalProfile::getRouteName());
        } elseif ($user->is_admin_verified == false && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == false) {
            return to_route(ReviewProfile::getRouteName());
        } elseif ($user->is_admin_verified == false && !empty($user->rejection_reason)) {
            return to_route(RejectedProfile::getRouteName());
        } else {
            return $next($request);
        }
    }
}
