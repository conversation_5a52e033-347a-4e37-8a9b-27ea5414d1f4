<?php

namespace App\Http\Middleware;

use App\Filament\Pc\Pages\EditProfile;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class IsPcProfileCompleted
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $user = auth()->user();
        if (! empty($user) && $user?->is_approved_by_admin == true) {
            return $next($request);
        } elseif (! empty($user) && $user?->is_approved_by_admin == false) {
            return redirect()->route(EditProfile::getRouteName());
        } else{
            return redirect()->route(EditProfile::getRouteName());
        }
    }
}
