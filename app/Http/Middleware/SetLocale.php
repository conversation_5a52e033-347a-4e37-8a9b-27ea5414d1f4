<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        $locale = $request->header('Accept-Language'); // Assuming frontend sends 'Accept-Language' header

        if ($locale && in_array($locale, ['en', 'ms','hi'])) {
            app()->setLocale($locale);
            
        } else {
            app()->setLocale('en'); // Fallback to English if no or invalid header
        }

        return $next($request);
    }
}
