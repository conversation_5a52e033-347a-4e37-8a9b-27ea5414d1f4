<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Filament\Http\Middleware\AuthenticateSession as BaseAuthenticateSession;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\URL;

class ForcePasswordReset
{
    public function handle(Request $request, Closure $next): Response
    {
        // Only check if user is authenticated
        // if (
        //     auth()->check() &&
        //     auth()->user()->is_temp_password &&
        //     !$request->routeIs('set-password') && !$request->is('two-factor-auth') // Avoid redirect loop
        // ) {
        //     return redirect()->route('set-password',['id'=>encryptParam(auth()->user()->id)]);
        // }

        // if (
        //     auth()->check() &&
        //     auth()->user()->is_temp_password &&
        //     !$request->routeIs('filament.pc.pages.set-password') // Ensure the full route name is used
        // ) {
        //     return redirect()->route('filament.pc.pages.set-password', [
        //         'id' => encryptParam(auth()->user()->id),
        //     ]);
        // }

        return $next($request);
    }

    // protected function redirectTo($request): ?string
    // {
    //     $previousUrl = URL::previous();
    //     $previousRequest = Request::create($previousUrl);
    //     $previousRoute = app('router')->getRoutes()->match($previousRequest);
    //     $previousRouteName = $previousRoute->getName();
    //     // Avoid redirect (logout) if user is on set-password page
    //     if ($previousRouteName == 'set-password') {
    //         return null; // Prevent logout
    //     }

    //     return Filament::getLoginUrl();
    // }
}
