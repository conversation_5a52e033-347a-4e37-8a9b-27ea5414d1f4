<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;


class TimezoneMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $headerTz = $request->header('time-zone');
        $normalizedTz = $this->normalizeTimezone($headerTz);
    
        $timezone = in_array($normalizedTz, \DateTimeZone::listIdentifiers())
            ? $normalizedTz
            : config('app.timezone');
    
        App::instance('userTimezone', $timezone);
    
        return $next($request);
    }

    protected function normalizeTimezone($tz)
    {
        $map = [
            'Asia/Calcutta' => 'Asia/Kolkata',
            // add other aliases if needed
        ];

        return $map[$tz] ?? $tz;
    }
}
