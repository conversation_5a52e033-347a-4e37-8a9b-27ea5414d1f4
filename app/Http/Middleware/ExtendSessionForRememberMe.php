<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use App\Services\ExtendedSessionService;
use Symfony\Component\HttpFoundation\Response;

class ExtendSessionForRememberMe
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        
        $firstTimeLogin = cache()->get('remember_me_first_time_login_'.Auth::id());
        // dd($firstTimeLogin);
        if ($firstTimeLogin === true) {
            Auth::user()->send2FAEmail();
            cache()->forget('remember_me_first_time_login_'.Auth::id());
            if(Filament::getCurrentPanel()->getId() == 'admin') {
                return redirect()->route('filament.admin.pages.sf-filament-2fa.2fa');
            } else {
                return redirect()->route('filament.pc.pages.sf-filament-2fa.2fa');
            }
        }
        if (ExtendedSessionService::hasRememberMe()) {
            \Log::info('User has remember me - bypassing 2FA', [
                'user_id' => auth()->id(),
                'url' => $request->url()
            ]);
            ExtendedSessionService::extendSessionForRememberMe();
            return $next($request);
        }

        // If no remember me, check if this is 2FA process
        if ($this->is2FAProcess($request)) {
            \Log::info('2FA process detected', [
                'url' => $request->url(),
                'path' => $request->path()
            ]);
            return $next($request);
        }

        \Log::info('Setting standard session lifetime', [
            'user_id' => auth()->id() ?? 'guest',
            'url' => $request->url()
        ]);
        // Set standard session lifetime for users without remember me
        ExtendedSessionService::setStandardSessionLifetime();

        return $next($request);
    }

    /**
     * Check if current request is part of 2FA/OTP process
     */
    private function is2FAProcess(Request $request): bool
    {
        $url = $request->url();
        $path = $request->path();
        
        // Check for 2FA related URLs and routes
        return str_contains($url, '2fa') || 
               str_contains($url, 'otp') || 
               str_contains($path, 'two-factor') ||
               str_contains($path, 'verify') ||
               $request->routeIs('*.auth.2fa.*') ||
               $request->has('otp_code') ||
               session()->has('2fa_user_id') ||
               session()->has('login.id');
    }
}