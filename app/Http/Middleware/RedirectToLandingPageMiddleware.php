<?php

namespace App\Http\Middleware;

use Closure;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\URL;

class RedirectToLandingPageMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next)
    {
        $referrer = $request->headers->get('referer');
        if($request->routeIs('filament.pc.auth.login') && $request->get('is_redirected') == true)
        {
            return $next($request);
        }

        if($request->routeIs('filament.pc.auth.register'))
        {
            return $next($request);
        }
        
        if (str_contains($referrer, 'password-reset')) {
            return $next($request);
        }

        if (str_contains($referrer, 'create-password')) {
            return $next($request);
        }

        if (str_contains($referrer, 'two-factor-auth')) {
            return $next($request);
        }

        if (str_contains($referrer, 'register')) {
            return $next($request);
        }
        if ($request->route()->getName() == 'filament.pc.auth.password-reset.request') {
            return $next($request);
        }

        if ($request->route()->getName() == 'filament.pc.auth.register') {
            return $next($request);
        }

        if (auth()->check()) {
            return $next($request);
        }
        $currentHost = $request->getHost();
        $currentPanelId = Filament::getCurrentPanel()->getId();

        $previousUrl = URL::previous();
        $previousRequest = Request::create($previousUrl);
        $previousRoute = app('router')->getRoutes()->match($previousRequest);
        $previousRouteName = $previousRoute->getName();
        if ($previousRouteName == 'set-password' || $previousRouteName == 'pc.passwor-reset.otp' || $previousRouteName == 'pc.create-password') {
            return $next($request);
        } else {
            if (!Filament::auth()->check() && $currentPanelId == 'pc' && !str_contains($referrer, 'home')) {
                return redirect()->to('/home');
            }
        }

        return $next($request);
    }
}
