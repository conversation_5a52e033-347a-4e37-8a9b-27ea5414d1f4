<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class CheckAdminVerification
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if ($user && (!$user->is_admin_verified || !$user->status)) { 
            $user->tokens()->delete();

            return response()->json([
                'success' => false,
                'message' => 'Unauthorized.',
            ], Response::HTTP_UNAUTHORIZED);
        }


        return $next($request);
    }
}
