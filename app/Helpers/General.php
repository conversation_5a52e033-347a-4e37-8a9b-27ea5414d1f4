<?php

use App\Jobs\SendDynamicNotification;
use Carbon\Carbon;
use App\Models\Role;
use App\Models\User;
use App\Models\Order;
use App\Models\PcDetail;
use App\Models\AccountType;
use App\Mail\PCRegisterMail;
use Illuminate\Http\Response;
use App\Mail\SendPCPasswordMail;
use App\Models\ClinicAccountType;
use Nnjeim\World\Models\Country;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Intervention\Image\Facades\Image;
use Illuminate\Support\Facades\Storage;
use Indianic\Settings\Models\GlobalSettings;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Indianic\EmailTemplate\Models\EmailTemplate;

define("THUMB", "/thumb/");
define("FILESYSTEM_DEFAULT", "filesystems.default");

if (!function_exists('exceptionResponse')) {

    function exceptionResponse($message)
    {

        return response()->json([
            'success' => false,
            'message' => __($message),
        ], Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}

if (!function_exists('getUser')) {
    function getUser($user)
    {
        if (!empty($user->parent_id)) {
            return User::find($user->parent_id);
        }
        return $user;
    }
}

if (!function_exists('getMessageFromName')) {
    function getMessageFromName($user)
    {
        
        // If the user is not fully loaded, fetch it
        if (!$user->hasRole('Pharmaceutical Company') 
            && !$user->hasRole('Clinic') 
            && !$user->hasRole('Super Admin')) {
            $user = getUser($user);
        }

        return match (true) {
            $user->hasRole('Pharmaceutical Company') => pcCompanyName($user->pcDetails),
            $user->hasRole('Clinic') => clinicCompanyName($user->clinicData),
            $user->hasRole('Super Admin') => $user->name,
            default => $user->name,
        };
    }
}

if (!function_exists('getSubUser')) {
    function getSubUser($parentId)
    {
        if (!empty($parentId)) {
            return User::where('parent_id', $parentId)->where('is_active', true)->get();
        }
        return User::where('id', $parentId)->get();
    }
}

if (! function_exists('generateOTP')) {
    function generateOTP($length = 4)
    {
        return str_pad(mt_rand(0, pow(10, $length) - 1), $length, '0', STR_PAD_LEFT);
    }
}

if (! function_exists('encryptParam')) {
    /**
     * @return array|mixed|string
     */
    function encryptParam($param)
    {
        if (is_numeric($param)) {
            $cipherKey = config('app.cipher_key');
            $cipher = config('app.cipher', 'AES-256-CBC');
            $iv = substr(hash('sha256', $cipherKey), 0, 16);
            $encryptedValue = openssl_encrypt($param, $cipher, $cipherKey, 0, $iv);

            return base64_encode($iv . '::' . $encryptedValue);
        }
        if (is_array($param)) {
            return collect($param)
                ->map(function ($item) {
                    return encryptParam($item);
                })->toArray();
        }

        return $param;
    }
}

if (! function_exists('decryptParam')) {
    /**
     * @return array|mixed|string
     */
    function decryptParam($param)
    {
        try {
            if (is_string($param)) {
                $encryptedValue = base64_decode($param);
                [$iv, $encryptedData] = explode('::', $encryptedValue, 2);

                return openssl_decrypt($encryptedData, config('app.cipher', 'AES-256-CBC'), config('app.cipher_key'), 0, $iv);
            }
            if (is_array($param)) {
                return collect($param)
                    ->map(function ($item) {
                        return decryptParam($item);
                    })->toArray();
            }

            return $param;
        } catch (\Exception $e) {

            throw_if(!$param, Exception::class, 'Invalid Id.');
        }
    }
}

if (! function_exists('userAccountType')) {
    /**
     * @param  $param
     * @return array|mixed|string
     */
    function userAccountType($name)
    {
        $res = AccountType::where('name', $name)->first();

        return $res ? $res->id : null;
    }
}


if (!function_exists('getStorageDisk')) {
    function getStorageDisk()
    {
        return Storage::disk(config(FILESYSTEM_DEFAULT));
    }
}


if (!function_exists('uploadFile')) {
    /**
     * upload file/image storage folder and their thumb image also
     * @param $file
     * @param $path
     * @param $filesystem
     * @param $width
     * @param $height
     * @param $thumb
     * @return string
     */
    function uploadFile($file, $path, $thumb = false, $width = 100, $height = 100, $originalName = null)
    {
        $extension = $file->getClientOriginalExtension();
        if ($extension && $originalName) {
            $originalName = $originalName . '.' . $extension;
        }
        $fileName = $originalName ? $originalName
            : hash('sha256', $file->getFilename() . '_' . now()) . '.' . $extension;

        getStorageDisk()->put($path . '/' . $fileName, file_get_contents($file));

        if ($thumb === true) {
            $thumb_image = Image::make($file)->resize($width, $height);
            $thumb_image = $thumb_image->stream();
            getStorageDisk()->put($path . THUMB . $fileName, $thumb_image->__toString());
        }
        return $fileName;
    }
}

if (!function_exists('deleteImage')) {
    /**
     * Delete uploaded file from server or cloud server
     *
     * @param $image
     * @param $path
     * @param $filesystem
     * @return boolean
     */
    function deleteImage($image, $path)
    {
        if (!empty($image) && getStorageDisk()->exists($path . $image)) {
            getStorageDisk()->delete($path . $image);
        }
        if (!empty($image) && getStorageDisk()->exists($path . THUMB . $image)) {
            getStorageDisk()->delete($path . THUMB . $image);
        }

        return true;
    }
}

if (!function_exists('getClinicZone')) {
    function getClinicZone()
    {
        $zone = Auth::user()->clinicData->zone ?? null;
        throw_if(!$zone, Exception::class, 'Zone not found for the facility.');

        return $zone;
    }
}

if (!function_exists('getDefaultCountry')) {
    function getDefaultCountry()
    {
        $id = Country::where('name', 'Malaysia')->pluck('id')->first();

        return $id ? encryptParam($id) : null;
    }
}

if (!function_exists('getTierPercentage')) {
    function getTierPercentage()
    {
        try {
            $auth = Auth::user();
            $tier = ucfirst($auth->clinicDetails->tier) ?? null;
            $per = config('constants.api.tier_per');
            return $per[$tier] ?? 0;
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}

if (!function_exists('getCommissions')) {
    function getCommissions($supplierProd, $amount, $zone, $priceType, $tierNumber)
    {
        try {
            $commissionType = null;
            $commissionValue = null;
            if ($supplierProd->productCommission) {
                $data = $supplierProd->productCommission;
                $keyType = $zone . '_' . ($priceType == 'fixed' ? $priceType : $tierNumber) . '_cummission_type';
                $keyValue = $zone . '_' . ($priceType == 'fixed' ? $priceType : $tierNumber) . '_cummission_value';
                $commissionType  = $data->$keyType ?? null;
                $commissionValue = $data->$keyValue ?? null;
            }
            if ($commissionType === null && $supplierProd->pcDetails) {
                $commissionType  = $supplierProd->pcDetails->commission_type ?? null;
                $commissionValue = $supplierProd->pcDetails->commission_percentage ?? null;
            }
            // Fetch global settings only if needed
            if ($commissionType === null) {
                $settings = GlobalSettings::whereIn('name', ['commission_type', 'commission'])->pluck('value', 'name');
                $commissionType  = $settings['commission_type'] ?? null;
                $commissionValue = $settings['commission'] ?? 0;
            }

            // Calculate total commission
            $totalCommission = ($commissionType === 'percentage') ? ($amount * $commissionValue / 100) : $commissionValue;
            Log::info("commission return: { $totalCommission}");
            return [
                "commission_type"  => $commissionType ?? null,
                "commission_value" => $commissionValue ?? 0,
                "total_commission" => $totalCommission,
            ];
        } catch (\Exception $e) {
            throw new \Exception($e->getMessage());
        }
    }
}

if (!function_exists('getTierBaseOnPoint')) {
    function getTierBaseOnPoint($updatedPoints)
    {
        $toTier = null;
        $tierLimits = config('constants.api.tier_limit');
        foreach ($tierLimits as $points => $tierName) {
            if ($updatedPoints >= $points) {
                $toTier = $tierName;
                break;
            }
        }
        return $toTier;
    }
}


if (!function_exists('getOrderNumber')) {
    function getOrderNumber()
    {
        $lastOrder = Order::latest()->first();
        return $lastOrder ? $lastOrder->order_number + 1 : 1000;
    }
}

if (!function_exists('getFormatNumber')) {
    function getFormatNumber($number, int $decimals = 2): string
    {
        // if (!is_numeric($number)) {
        //     return '0' . ($decimals > 0 ? '.' . str_repeat('0', $decimals) : '');
        // }

        // return number_format((float)$number, $decimals, '.', ',');

        if (!is_numeric($number)) {
            return '0' . ($decimals > 0 ? '.' . str_repeat('0', $decimals) : '');
        }

        $number = (float)$number;
        $formatted = number_format($number, $decimals, '.', '');

        // Handle decimal part separately
        $parts = explode('.', $formatted);
        $wholeNumber = $parts[0];
        $decimalPart = isset($parts[1]) ? '.' . $parts[1] : '';

        // Indian numbering system formatting
        $lastThree = substr($wholeNumber, -3);
        $otherNumbers = substr($wholeNumber, 0, -3);

        if ($otherNumbers != '') {
            $lastThree = ',' . $lastThree;
        }

        $formattedWhole = preg_replace("/\B(?=(\d{2})+(?!\d))/", ",", $otherNumbers) . $lastThree;

        return $formattedWhole . $decimalPart;
    }
}

if (!function_exists('generateStrongPassword')) {
    function generateStrongPassword(): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $digits = '0123456789';
        $specialChars = '@$!%*?&';

        $password = [
            $lowercase[rand(0, strlen($lowercase) - 1)],
            $uppercase[rand(0, strlen($uppercase) - 1)],
            $digits[rand(0, strlen($digits) - 1)],
            $specialChars[rand(0, strlen($specialChars) - 1)],
        ];

        // Fill the rest of the password with random characters from all sets
        $allChars = $lowercase . $uppercase . $digits . $specialChars;
        for ($i = count($password); $i < 8; $i++) {
            $password[] = $allChars[rand(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize the order
        shuffle($password);

        // Convert the array to a string
        return implode('', $password);
    }
}

if (!function_exists('getAdminData')) {
    function getAdminData()
    {
        $role = Role::findById(1);
        return $role->users;
    }
}

// Universal date formatting function
if (!function_exists('getFormatedDate')) {
    function getFormatedDate($date, $format = 'M d, Y')
    {
        return Carbon::parse($date)->format($format);
    }
}

if (!function_exists('getFormatedDateForPc')) {
    function getFormatedDateForPc()
    {
        $user = getUser(auth()->user());
        $format = PcDetail::where('user_id', $user->id)->value('date_format') ?? 'M d, Y';
        // dd($format);
        return (string)$format;
    }
}

if (!function_exists('getDateTimeFormatForPc')) {
    function getDateTimeFormatForPc($authUser)
    {
        $dateFormat = $authUser->pcDetails->date_format ?? 'd/m/Y';
        $timeLabel = $authUser->pcDetails->time_format ?? '12-hour';
        $timeFormat = match ($timeLabel) {
            '12-hour' => 'h:i A',
            '24-hour' => 'H:i',
            default   => 'h:i A',
        };
        return $dateFormat . ' ' . $timeFormat;
    }
}

if (!function_exists('isPharmaceuticalCompany')) {
    function isPharmaceuticalCompany()
    {
        $parentUser = getUser(auth()->user());
        $user = auth()->user();
        if ($user->id == $parentUser->id) {
            return true;
        }
        return false;
    }
}

if (!function_exists('pcCompanyName')) {
    function pcCompanyName($pc)
    {
        return !empty($pc->company_name)
            ? $pc?->company_name
            : (($pc?->companyType->name === 'Sole Proprietary' && !empty($pc?->business_name))
                ? $pc->business_name
                : null);
    }
}

if (!function_exists('clinicCompanyName')) {
    function clinicCompanyName($clinic)
    {
        return !empty($clinic->company_name)
            ? $clinic->company_name
            : (($clinic->businessName->name === 'Sole Proprietary' && !empty($clinic->clinic_name))
                ? $clinic->clinic_name
                : null);
    }
}

if (!function_exists('isAdmin')) {
    function isAdmin()
    {
        $parentUser = getUser(auth()->user());
        $user = auth()->user();

        if ($user->id == $parentUser->id) {
            return true;
        }
        return false;
    }
}

if (!function_exists('sendCredential')) {
    function sendCredential(array $data)
    {
        $userEmail = $data['email'];
        $plainPassword = generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['user_id'])->update(['password' => $hashedPassword]);
        Mail::to($userEmail)->send(new SendPCPasswordMail($plainPassword, $userEmail));
    }
}

if (!function_exists('generateStrongPassword')) {
    function generateStrongPassword()
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $digits = '0123456789';
        $specialChars = '@$!%*?&';

        $password = [
            $lowercase[rand(0, strlen($lowercase) - 1)],
            $uppercase[rand(0, strlen($uppercase) - 1)],
            $digits[rand(0, strlen($digits) - 1)],
            $specialChars[rand(0, strlen($specialChars) - 1)],
        ];

        // Fill the rest of the password with random characters from all sets
        $allChars = $lowercase . $uppercase . $digits . $specialChars;
        for ($i = count($password); $i < 8; $i++) {
            $password[] = $allChars[rand(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize the order
        shuffle($password);

        // Convert the array to a string
        return implode('', $password);
    }
}
if (!function_exists('getUnMaskNumber')) {
    function getUnMaskNumber($number)
    {
        return preg_replace('/\D/', '', $number);
    }
}

if (!function_exists('sendEmailToAdmin')) {
    function sendEmailToAdmin($data)
    {
        $adminData = getAdminData();
        $currentUserId = \Auth::user()->id;
        if ($adminData->isNotEmpty()) {
            $adminEmail = $adminData->first()->email;
            $loginUrl = config('app.admin_url');
            $link = '<a style="color:blue;" href="' . $loginUrl . '/users/' . $currentUserId . '?activeTab=Pending">View</a>';

            Mail::to($adminEmail)->send(new PCRegisterMail($data, $link));
        }
    }
}



//check file exist or not to path
if (!function_exists('getImage')) {
    function getImage($filename, $path, $thumb = false, $default = 'default-image.png')
    {

        if (!empty($filename) && getStorageDisk()->exists($path . '/' . $filename)) {
            if ($thumb) {
                return getStorageDisk()->url($path . THUMB . $filename);
            }
            return getStorageDisk()->url($path . '/' . $filename);
        }
        return asset('images/' . $default);
    }
}

if (!function_exists('uploadUserImage')) {
    function uploadUserImage($data)
    {

        $uploadedFiles = $data;
        if (is_array($uploadedFiles)) {
            $uploadedFile = reset($uploadedFiles);

            if ($uploadedFile instanceof TemporaryUploadedFile) {
                $path = $uploadedFile->store('users/', 's3');
                $url = Storage::disk('s3')->url($path);
                User::where('id', $data['user_id'])->update([
                    'photo' => basename($path)
                ]);
            }
        }
    }
}


if (!function_exists('getFormattedFileSize')) {
    function getFormattedFileSize(string $filePath): ?string
    {
        try {
            $size = Storage::size($filePath);
            return match (true) {
                $size >= ********** => number_format($size / **********, 2) . ' GB',
                $size >= 1048576    => number_format($size / 1048576, 2) . ' MB',
                $size >= 1024       => number_format($size / 1024, 2) . ' KB',
                $size > 1           => $size . ' bytes',
                $size === 1         => '1 byte',
                default             => '0 bytes',
            };
        } catch (\Exception) {
            return null;
        }
    }
}


if (!function_exists('getFileUrl')) {
    function getFileUrl(array $licensePermitFiles, $record): ?array
    {
        if (empty($licensePermitFiles)) {
            return null;
        }

        return array_map(function ($file) use ($record) {
            return config('constants.api.media.pc_medias') . $record->pcDetails->id . '/' . $file;
        }, $licensePermitFiles);
    }
}

if (!function_exists('convertToUserTimezone')) {
    function convertToUserTimezone($datetime, $format = 'Y-m-d H:i:s')
    {
        $timezone = App::bound('userTimezone') ? App::make('userTimezone') : config('app.timezone');

        return Carbon::parse($datetime)->setTimezone($timezone)->format($format);
    }
}

if (!function_exists('convertToUserMailTimezone')) {
    function convertToUserMailTimezone($timezone, $datetime, $format = 'Y-m-d H:i:s')
    {
        $timezone = $timezone ?? 'utc';

        return Carbon::parse($datetime)->setTimezone($timezone)->format($format);
    }
}

if (!function_exists('isClinicAccountType')) {
    function isClinicAccountType($record, $accountTypeKeys)
    {
        if (!$record || !$record->clinicData || empty($accountTypeKeys)) {
            return false;
        }

        // Single database query to check all types at once
        return ClinicAccountType::whereIn('key', $accountTypeKeys)
            ->where('id', $record->clinicData->clinic_account_type_id)
            ->exists();
    }
}

if (!function_exists('adminSettingDetail')) {
    function adminSettingDetail()
    {
        return GlobalSettings::whereIn('name', ['contact_email', 'contact_number'])->pluck('value', 'name');
    }
}

if (!function_exists('getTimeBasedGreeting')) {
    function getTimeBasedGreeting()
    {
        $timezone = auth()->user()->timezone ?? config('app.timezone', 'UTC');
        $hour = now()->setTimezone($timezone)->hour;

        return match (true) {
            $hour < 12 => 'Good Morning',
            $hour < 17 => 'Good Afternoon',
            default    => 'Good Evening',
        };
    }
}

if (!function_exists('getBankNames')) {
    function getBankNames(?string $search = null)
    {
        try {
            $response = Http::timeout(90)->get('https://staging-payments.commerce.asia/api/services/app/Channel/GetProviderChannels', [
                'ChannelId' => 15,
                'DisplayName' => $search, // Pass search term to API (if supported)
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $banks = collect($data['result'] ?? [])
                    ->where('status', 1)
                    ->when($search, function ($collection) use ($search) {
                        // Local filtering if API doesn't support search
                        return $collection->filter(function ($item) use ($search) {
                            return stripos($item['displayName'], $search) !== false;
                        });
                    })
                    ->pluck('displayName', 'displayName')
                    ->toArray();
                return $banks;
            }

            Log::error('Bank API request failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
        } catch (\Exception $e) {
            Log::error('Bank API connection failed: ' . $e->getMessage());
            return [];
        }

        return [];
    }
}


if (! function_exists('encryptTextParam')) {
    /**
     * @return array|mixed|string
     */
    function encryptTextParam($param)
    {
        if (is_string($param) || is_numeric($param)) {
            $cipherKey = config('app.cipher_key');
            $cipher = config('app.cipher', 'AES-256-CBC');
            $iv = substr(hash('sha256', $cipherKey), 0, 16);
            $encryptedValue = openssl_encrypt($param, $cipher, $cipherKey, 0, $iv);

            return base64_encode($iv . '::' . $encryptedValue);
        }
        if (is_array($param)) {
            return collect($param)
                ->map(function ($item) {
                    return encryptTextParam($item);
                })->toArray();
        }

        return $param;
    }
}


if (!function_exists('sendMailNotification')) {
    function sendMailNotification($sendEmailUserData, $emailTemplateKey, $emailContentData = null)
    {
        $emailTemplateData = EmailTemplate::where('key', $emailTemplateKey)->first()?->toArray();
        if ($emailTemplateData) {
            // Log::info("Sending Push Notification to user ID: " . ($sendUserNotificationData["id"] ?? 'N/A'));
            $job = new SendDynamicNotification(
                $sendEmailUserData,
                $emailTemplateData,
                $emailContentData,
            );
            dispatch($job);
        } else {

            Log::info("Email template not found for key: " . $emailTemplateKey);
        }
    }
}
