<?php

namespace App\Service;

use App\Models\PcCertificateFile;
use App\Models\PcDetail;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\WareHouse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class StoreProfileService
{
    public function storeCompanyInformations(array $data, int $step)
    {
        $data['user_id'] = auth()->user()->id;
        $data['step'] = $step;
        User::where('id', $data['user_id'])->update(['name' => $data['company_name']]);
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    public function storeContactDetails(array $data)
    {

        DB::transaction(function () use ($data) {
            $data['user_id'] = auth()->user()->id;
            $phone = $data['phone_number'];
            PcDetail::updateOrCreate(['user_id' => $data['user_id']], ['phone_number' => $phone, 'step' => 2, 'profile_email' => $data['profile_email'], 'postal_code' => $data['postal_code'], 'web_url' => $data['web_url'], 'region' => $data['region'], "phone_number_code" => $data['phone_code']]);
            // unset($data['phone_number']);
            unset($data['profile_email']);
            // unset($data['postal_code']);
            unset($data['web_url']);
            // unset($data['region']);
            $data['is_onboarding'] = true;
            $data['country_id'] = 132; // 132 is country id for malasia
            $data['region'] = $data['region'];
            UserAddress::updateOrCreate(['user_id' => $data['user_id']], $data);
        });
    }

    public function storeWareHouse(array $data)
    {
        $user = auth()->user();
        $data['user_id'] = $user->id;
        if (! empty($data['ware_id'])) {
            if($data['warehouse_type'] == 'owned') {
                $data = [
                    "phone_number" => null,
                    "address_1" => null,
                    "address_2" => null,
                    "district" => null,
                    "state_id" => null,
                    "postal_code" => null,
                    "city_id" => null,
                    "warehouse_type" => $data['warehouse_type'],
                    "ware_region" => null,
                    'ware_id' => $data['ware_id'],
                    "user_id" => $data['user_id'],
                ];
            }
            if ($data['warehouse_type'] == 'dpharma') {
                PcDetail::where('user_id', $data['user_id'])->update([
                    'delivery_days' => null,
                    'delivery_days_west' => null,
                ]);
            }
            WareHouse::where('id', $data['ware_id'])->update(Arr::except($data, 'ware_id'));
        } else {
            WareHouse::where('user_id', $data['user_id'])->delete();
            unset($data['ware_id']);
            PcDetail::updateOrCreate(['user_id' => $data['user_id']], ['step' => 3]);
           
            WareHouse::create($data);
        }
    }

    public function storePersonInCharge(array $data, $step)
    {
        $data['user_id'] = auth()->user()->id;
        $data['step'] = $step;
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data, );
    }

    public function storeDocuments(array $data)
    {
        $data['user_id'] = auth()->user()->id;
        PcDetail::where('user_id', $data['user_id'])->update([
            // 'company_registration_certificate' => $data['company_registration_certificate'],
            // 'license_permit' => $data['license_permit'],
            'step' => 5 // Update the step to 5
        ]);

        $this->processFileUploads($data['user_id'], $data, 'company_registration_certificate', 'company_registration_certificate');
        $this->processFileUploads($data['user_id'], $data, 'license_permit', 'license_permit');
    }


    protected function processFileUploads(int $userId, array $data, string $inputKey, string $fileType): void
    {
        if (empty($data[$inputKey])) {
            return;
        }

        $filesToInsert = array_map(function ($filePath) use ($userId, $fileType) {
            return [
                'user_id' => $userId,
                'type' => $fileType,
                'name' => basename($filePath),
                'status' => 'active',
                'size' => $this->getFormattedFileSize($filePath),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, (array)$data[$inputKey]);
        if (!empty($filesToInsert)) {
            PcCertificateFile::insert($filesToInsert);
        }
    }

    /**
     * Get formatted file size with error handling
     */
    protected function getFormattedFileSize(string $filePath): ?string
    {
        try {
            $size = Storage::size($filePath);
            return match (true) {
                $size >= ********** => number_format($size / **********, 2) . ' GB',
                $size >= 1048576    => number_format($size / 1048576, 2) . ' MB',
                $size >= 1024       => number_format($size / 1024, 2) . ' KB',
                $size > 1           => $size . ' bytes',
                $size === 1         => '1 byte',
                default             => '0 bytes',
            };
        } catch (\Exception) {
            return null;
        }
    }

    public function storeBankDetails(array $data)
    {
        $data['user_id'] = auth()->user()->id;
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data, );
    }
}
