<?php 
namespace App\Service;

use Google\Client;
use Firebase\JWT\JWT;
use Firebase\JWT\JWK;
use Exception;

class SocialService
{
    protected $client;
    private $applePublicKeysUrl = 'https://appleid.apple.com/auth/keys';
    
    public function __construct()
    {
        
    }


    public function verifyIdToken($idToken)
    {
        try {
            // Fetch Apple's public keys
            $applePublicKeys = json_decode(file_get_contents($this->applePublicKeysUrl), true);

            // Decode the ID token header to get the key ID (kid)
            $tokenParts = explode('.', $idToken);
            $header = json_decode(base64_decode($tokenParts[0]), true);
            $kid = $header['kid'] ?? null;

            if (!$kid) {
                throw new Exception('Invalid token header.');
            }

            // Find the correct public key from Apple's JWKs
            $publicKeys = JWK::parseKeySet($applePublicKeys);
            $publicKey = $publicKeys[$kid] ?? null;

            if (!$publicKey) {
                throw new Exception('No matching public key found.');
            }

            // Manually decode the payload (no signature verification yet)
            $payload = base64_decode($tokenParts[1]);

            // You can verify the token's signature here manually using PHP's OpenSSL functions, or you can continue with Firebase's verify
            // After decoding, validate claims
            $decodedToken = json_decode($payload);
            $this->validateClaims($decodedToken);

            return (array) $decodedToken;
        } catch (Exception $e) {
            throw new Exception('Apple ID Token verification failed: ' . $e->getMessage());
        }
    }

    private function validateClaims($token)
    {
        // Validate audience (client_id/service ID) and issuer
        $validIssuer = 'https://appleid.apple.com';
        $validAudience = env('APPLE_CLIENT_ID'); // Replace with your Apple Service ID

        if ($token->iss !== $validIssuer) {
            throw new Exception('Invalid user.');
        }

        if ($token->aud !== $validAudience) {
            throw new Exception('Invalid audience.');
        }

        // Check token expiration
        if ($token->exp < time()) {
            throw new Exception('Token has expired.');
        }
    }



}
