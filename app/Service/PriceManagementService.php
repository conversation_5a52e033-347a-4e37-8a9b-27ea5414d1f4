<?php

namespace App\Service;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ProductRelationPrice;

class PriceManagementService
{
    public function store($data, $record = null, $relationId = null, $user = null)
    {

        if ($data['price_type'] == 'tier') {
            $data = self::handleTierPrice($data, $record);
        }
        if ($data['price_type'] == 'bonus') {
            $data['east_zone_price'] = $data['east_zone_price_bonus'] ?? null;
            $data['west_zone_price'] = $data['west_zone_price_bonus'] ?? null;
        }
        if ($data['price_type'] == 'fixed') {
            $data['east_zone_price'] = $data['east_zone_price_1'] ?? null;
            $data['west_zone_price'] = $data['west_zone_price_1'] ?? null;
        }
        $data['user_id'] = $user->id;
        try {
            DB::transaction(function () use ($data, $record, $relationId, $user) {

                $userId = $user->id;
                self::handleOtherPriceTypes($data['price_type'], $record, $relationId);
                $data['requested_by'] = auth()->id();
                unset($data['east_zone_price_1']);
                unset($data['west_zone_price_1']);
                unset($data['east_zone_price_bonus']);
                unset($data['west_zone_price_bonus']);
                $record->productDataForPc($userId)->update(['price_type' => $data['price_type']]);
                unset($data['price_type']);
                unset($data['user_id']);
                unset($data['requested_by']);

                ProductRelationPrice::updateOrCreate(['product_relation_id' => $record->productDataForPc($userId)->id], $data);
            });
        } catch (\Exception $e) {
            info($e->getMessage());
            throw $e; // Re-throw the exception so it's not silently swallowed
        }
    }

    public static function handleTierPrice($data, $record)
    {
        $eastTierPriceInfo = $data['pcInfo_east'] ?? [];
        $westTierPriceInfo = $data['pcInfo_west'] ?? [];
        
        $eastTierPrice = [];
        $westTierPrice = [];
        
        // Process east tier prices with proper array key handling
        foreach ($eastTierPriceInfo as $key => $value) {
            $count = is_numeric($key) ? $key + 1 : $key + 1;
            $eastTierPrice["east_tier_{$count}_min_quantity"] = $value['min_quantity'] ?? null;
            // Handle max_quantity: convert null to 0 for database storage
            $eastTierPrice["east_tier_{$count}_max_quantity"] = $value['max_quantity'] ?? 0;
            $eastTierPrice["east_tier_{$count}_base_price"] = $value['price'] ?? null;
        }
        
        // Process west tier prices with proper array key handling
        foreach ($westTierPriceInfo as $key => $value) {
            $count = is_numeric($key) ? $key + 1 : $key + 1;
            $westTierPrice["west_tier_{$count}_min_quantity"] = $value['min_quantity'] ?? null;
            // Handle max_quantity: convert null to 0 for database storage
            $westTierPrice["west_tier_{$count}_max_quantity"] = $value['max_quantity'] ?? 0;
            $westTierPrice["west_tier_{$count}_base_price"] = $value['price'] ?? null;
        }
        
        // Note: JSON tier price fields are not stored in the database schema
        // Only the individual tier fields (east_tier_1_min_quantity, etc.) are used
        
        $productData = array_merge($data, $eastTierPrice, $westTierPrice);
        unset($productData['pcInfo_east']);
        unset($productData['pcInfo_west']);
        return $productData;
    }

    public static function handleOtherPriceTypes($priceType, $record, $relationId = null)
    {
        $nullBonusArr = [
            'east_bonus_1_quantity' => null,
            'east_bonus_1_quantity_value' => null,
            'east_bonus_2_quantity' => null,
            'east_bonus_2_quantity_value' => null,
            'east_bonus_3_quantity' => null,
            'east_bonus_3_quantity_value' => null,

            'west_bonus_1_quantity' => null,
            'west_bonus_1_quantity_value' => null,
            'west_bonus_2_quantity' => null,
            'west_bonus_2_quantity_value' => null,
            'west_bonus_3_quantity' => null,
            'west_bonus_3_quantity_value' => null,
        ];

        $nullTierArr = [
            'east_tier_1_min_quantity' => null,
            'east_tier_1_max_quantity' => null,
            'east_tier_1_base_price' => null,
            'east_tier_2_min_quantity' => null,
            'east_tier_2_max_quantity' => null,
            'east_tier_2_base_price' => null,
            'east_tier_3_min_quantity' => null,
            'east_tier_3_max_quantity' => null,
            'east_tier_3_base_price' => null,
            'west_tier_1_min_quantity' => null,
            'west_tier_1_max_quantity' => null,
            'west_tier_1_base_price' => null,
            'west_tier_2_min_quantity' => null,
            'west_tier_2_max_quantity' => null,
            'west_tier_2_base_price' => null,
            'west_tier_3_min_quantity' => null,
            'west_tier_3_max_quantity' => null,
            'west_tier_3_base_price' => null,
        ];

        $nullFixedArray = [
            'east_zone_price' => null,
            'west_zone_price' => null,
        ];
        $nullArr = array_merge($nullBonusArr, $nullTierArr, $nullFixedArray);
        ProductRelationPrice::where('product_relation_id', $relationId)?->update($nullArr);
    }
}
