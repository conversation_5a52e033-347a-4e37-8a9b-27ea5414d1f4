<?php

namespace App\Service;

use App\Models\ShippingLabel;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;


class ShippingService
{
    public function getToken()
    {

        try {
            $response = Http::withHeaders([
                'request_id' => config('app.shipping_request_id'),
                'business_unit' =>  config('app.shipping_business_unit'),
                'x_api_key' =>  config('app.shipping_x_api_key'),
                'Content-Type' => 'application/json',
            ])->post(config('app.shipping_base_auth_url') . 'Auth/GetToken', [
                'clientUsername' => config('app.shipping_clinet_user_name'),
                'clientSecret' => config('app.shipping_clinet_secret'),
                'appToAccess' => [config('app.shipping_application_id')],
                'TokenType' => 'Bearer',
                'contractNumber' => config('app.shipping_contract_number'),
                'costCenterNumber' => config('app.shipping_clinet_cost_center_nuber'),
            ]);

            Cache::remember('shipping_access_token', now()->addMinutes(50), function () use ($response) {
                return $response->json()["result"]['accessToken'];
            });

            // Handle the response
            return $responseBody = $response->json();
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function getOrderShipCharge($productWeight, $supplierTotalQty, $pcPostalCode)
    {
        $datePlusOne = $this->getDeliveryDate();
        $accessToken = Cache::get('shipping_access_token');

        if (!$accessToken) {
            $token = $this->getToken();
            $accessToken = $token['result'] ? $token['result']['accessToken'] : null;
        }
        if ($accessToken) {
            $dimention = $this->dimention($productWeight, $supplierTotalQty);

            $response = Http::withHeaders([
                'request_id' => config('app.shipping_request_id'),
                'business_unit' =>  config('app.shipping_business_unit'),
                'x_api_key' =>  config('app.shipping_x_api_key'),
                'Content-Type' => 'application/json',
                'application_id' => config('app.shipping_application_id'),
                'Authorization' => 'Bearer ' . $accessToken,
            ])
                ->post(config('app.shipping_base_order_url') . 'Order/GDM/ChargeEnquiry', [
                    'contractNumber' => config('app.shipping_contract_number'),
                    'costCenterNumber' => config('app.shipping_clinet_cost_center_nuber'),
                    //'fromPostalCode' => $pcPostalCode,
                    'fromPostalCode' => '53000',
                    'toPostalCode' => '53100',
                    //'toPostalCode' => Auth::user()->defaultShipAddress->postal_code,
                    'packageDetails' => [
                        [
                            'quantity' => $supplierTotalQty,
                            'length' => $dimention['baseLength'],
                            'width' => $dimention['baseWidth'],
                            'height' => $dimention['baseHeight'],
                            'weight' => $productWeight,
                        ]
                    ],
                    'readyToPickUpDateTime' => $datePlusOne . 'T09:30:00',
                    'isShowChargeBreakDown' => false,
                ]);

            if ($response->status() === 200) {
                $data = $response->json();
                return [
                    'isSuccess' => true,
                    'result' => $data['result']
                ];
            } else if ($response->status() === 400) {
                $data = $response->json();
                return [
                    'isSuccess' => false,
                    'title' => $data['title']
                ];
            }
        } else {
            return [
                'isSuccess' => false,
                'title' => 'Facing issue for token generate, Error : ' . $accessToken
            ];
        }
    }

    public function dimention($weight, $qty)
    {

        $baseLength = 10;
        $baseWidth = 25;
        $baseHeight = 10;
        $baseWeight = 500;

        $ceilWeight =  ceil($weight / $baseWeight);
        $dimention = [
            'baseLength' => $baseLength * $ceilWeight,
            'baseWidth' => $baseWidth * $ceilWeight,
            'baseHeight' => $baseHeight * $ceilWeight,
        ];
        return $dimention;
    }

    public function getDeliveryDate()
    {
        $date = Carbon::now();

        $datePlusOne = $date->copy()->addDay();

        // If the next day is Saturday or Sunday, jump to next Monday
        if ($datePlusOne->isSaturday()) {
            $datePlusOne->addDays(2); // Skip to Monday
        } elseif ($datePlusOne->isSunday()) {
            $datePlusOne->addDay(); // Skip to Monday
        }

        return $datePlusOne->format('Y-m-d');
    }

    public function createOrderRequest($subOrderInfo)
    {
        $datePlusOne = Carbon::now()->addDay()->format('Y-m-d');
        $token = $this->getToken();
        $accessToken = $token['result'] ? $token['result']['accessToken'] : null;

        if ($accessToken) {

            $response = Http::withHeaders([
                'request_id' => config('app.shipping_request_id'),
                'business_unit' =>  config('app.shipping_business_unit'),
                'x_api_key' =>  config('app.shipping_x_api_key'),
                'Content-Type' => 'application/json',
                'application_id' => config('app.shipping_application_id'),
                'Authorization' => 'Bearer ' . $accessToken,
            ])
                ->post(config('app.shipping_base_order_url') . '/Order/GDM/AddOrder', [
                    "orderCreationList" => [
                        [
                            "alternateReferenceNumber" => (string)$subOrderInfo->order->order_number,
                            "contractNumber" => "KLG0539",
                            "costCenterNumber" => "DGP001",
                            "scheduledPickUpDateTime" => '2025-07-23T07:30:00',
                            "pickupAddress" => [
                                "contactName" => $subOrderInfo->user->pcDetails->company_name,
                                "isAddToAddressBook" => false,
                                "address1" => $subOrderInfo->user->warehouseInfo->address_1,
                                "address2" => $subOrderInfo->user->warehouseInfo->address_1,
                                "address3" => $subOrderInfo->user->warehouseInfo->address_1,
                                "address4" => "",
                                "postalCode" => "53000",
                                "contactPerson" => $subOrderInfo->user->name,
                                "email" => $subOrderInfo->user->email,
                                "contactNumber" => "82916620", //$subOrderInfo->user->warehouseInfo->phone_number,
                                "phone1" => $subOrderInfo->user->warehouseInfo->phone_number,
                                "phoneExtension1" => "",
                                "phone2" => "",
                                "phoneExtension2" => "",
                                "specialInstruction" => "Please press 2 at the door",
                                "otpEmail1" => null,
                                "otpEmail2" => null,
                                "otpsmS1" => null,
                                "otpsmS2" => null,
                            ],
                            "isPickSMSNotification" => false,
                            "pickSMSNotification" => null,
                            "isPickEmailNotification" => false,
                            "pickEmailNotification" => null,
                            "scheduleDeliveryDateTime" => '2025-07-24T08:25:00',
                            "deliveryAddress" => [
                                "contactName" => $subOrderInfo->order->clinicDetail->company_name,
                                "isAddToAddressBook" => false,
                                "address1" => $subOrderInfo->order->shipping_address_1,
                                "address2" => $subOrderInfo->order->shipping_address_1,
                                "address3" => "",
                                "address4" => "",
                                "postalCode" => "53100",
                                "contactPerson" => $subOrderInfo->order->user->name,
                                "email" => $subOrderInfo->order->user->email,
                                "contactNumber" => $subOrderInfo->order->shipping_phone_number,
                                "phone1" => "",
                                "phoneExtension1" => "",
                                "phone2" => "",
                                "phoneExtension2" => "",
                                "specialInstruction" => "Please deliver to only the mentioned receipient",
                                "otpEmail1" => null,
                                "otpEmail2" => null,
                                "otpsmS1" => null,
                                "otpsmS2" => null,
                            ],
                            "isDeliverySMSNotification" => false,
                            "deliverySMSNotification" => null,
                            "isDeliveryEmailNotification" => false,
                            "deliveryEmailNotification" => null,
                            "packageDetails" => [
                                [
                                    "quantity" => 1,
                                    "length" => 23.00,
                                    "width" => 23.00,
                                    "height" => 30.00,
                                    "weight" => 0.50
                                ]
                            ],
                            "specialInstruction" => "To collect sealed documents",
                            "serviceType" => "Next Day DL",
                            "isReturnTrip" => false,
                            "returnServiceType" => null,
                            "returnScheduledPickUpDateTime" => null,
                            "returnScheduledDeliveryDateTime" => null
                        ]
                    ]
                ]);

            // Handle response
            if ($response->successful()) {
                $data = $response->json();
                ShippingLabel::create([
                    'order_id'      => $subOrderInfo->order->id,
                    'suborder_id'   => $subOrderInfo->id,
                    'supplier_id'   => $subOrderInfo->user_id,
                    'user_id'       => $subOrderInfo->order->user_id,
                    'order_number'  => $data['result'][0]['orderNumber'],
                    'alternate_reference_number'  => $subOrderInfo->order->order_number,
                ]);
                return $data;
                // Do something with the response
            } else {
                // Log or handle the error
                return true;
                //dd($response->status(), $response->body());
            }
        }
    }

    /**
     * Update shipping label status and insert tracking history.
     *
     * @param string $orderNumber
     * @param string $alternateReferenceNumber
     * @param string $statusCode
     * @return array
     */
    public function updateShippingStatus($orderNumber, $alternateReferenceNumber, $statusCode, $statusUpdateDateTime, $GEOCode, $Image, $packageDetails, $totalCharge)
    {
        $label = \App\Models\ShippingLabel::where('order_number', $orderNumber)
            ->where('alternate_reference_number', $alternateReferenceNumber)
            ->first();
        if (!$label) {
            return [
                'success' => false,
                'message' => 'Shipping label not found.'
            ];
        }
        $label->status_code = $statusCode;
        $label->save();
        // Insert new tracking record
        \App\Models\ShippingOrderTracking::create([
            'shipping_label_id' => $label->id,
            'status' => $statusCode,
            'status_code' => $statusCode,
            'updated_at' => $statusUpdateDateTime,
            'geo_code' => $GEOCode,
            'image' => $Image,
            'total_charge' => $totalCharge,
            'package_detail_json' => json_encode($packageDetails)
        ]);
        return [
            'success' => true,
            'message' => 'Status code updated and tracking inserted.'
        ];
    }
}
