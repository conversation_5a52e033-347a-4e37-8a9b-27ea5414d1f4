<?php

namespace App\Service;

use App\Models\ShippingLabel;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Auth;
use App\Mail\PayoutProcessedMail;
use App\Models\Payout;
use App\Models\Transaction;
use Illuminate\Console\Command;
use Spatie\Browsershot\Browsershot;
use Illuminate\Support\Facades\Storage;
use App\Mail\PayoutCommissionProcessedMail;
use App\Mail\PayoutCommissionMerchantMail;
use App\Mail\PcPayoutCommissionConfirmMail;
use Illuminate\Support\Facades\Mail;
use App\Service\EcommerceService;


class PayoutService
{

    protected $ecommerceService;

    public function __construct()
    {
        $this->ecommerceService = new EcommerceService();
    }

    public function sendMail($payout) {

        $fileName = 'payout_' . $payout->id . '.pdf';
        $s3Path = config('constants.api.payout_pdf.main_path')  . $fileName;
        $payout_cycle_date = $this->invoiceDateFormat($payout);
        $grid =  $this->payoutOrderGrid($payout->payoutSubOrders, $payout,$payout_cycle_date);
        $merchantPaymentLink = null;
         $data  = [
            "returnUrl" => url("/supplier-payment-handler"), // add pc web url
            "callbackUrl" => config('app.url')."/api/v1/webhook/payment-handler",
        ];

        $mainHtml = view('pdf.payout.payout-main-pdf', [
            'payout' => $payout,
            'payout_cycle_date' => $payout_cycle_date,
            'grid' => $grid,
            'adminSettingDetail' => adminSettingDetail()
        ])->render();

        $this->fileGenerate($mainHtml, $s3Path);

        if($grid['returnAmount'] > 0) {
            $paymentLinks  = [
                "returnUrl" => config("app.pc_url") ."/check-payout-payment-status",
                "callbackUrl" => config('app.url')."/api/v1/webhook/payment-handler",
            ];
            $session = $this->ecommerceService->store(null, $grid['returnAmount'],'merchant_payout',$paymentLinks);
            
            if (isset($session['transactionNumber'])) {
                $merchantTransId =  $session['transactionNumber'];
                $merchantPaymentLink = ($payout->mechant_payment_type == 'online' ? $session['redirectUrl'] : null);
            }
        }
        Mail::to($payout->user->email)->send(new PayoutProcessedMail($payout, $s3Path,$payout_cycle_date,$grid['totalPayableAmt']));

        if(($payout->payout_type == 'full') || ($payout->payout_type == 'schedule' && $grid['returnAmount'] > 0)) {

            $commissionFileName = $payout->payout_type.'_commission_payout_' . $payout->id . '.pdf';
            $commissionS3Path = config('constants.api.payout_pdf.full_commission_path')  .$commissionFileName;

            
            $commissionHtml = view('pdf.payout.full-commission-pdf', [
                'payout' => $payout,
                'payout_cycle_date' => $payout_cycle_date,
                'grid' => $grid,
                'adminSettingDetail' => adminSettingDetail()
            ])->render();

            $this->fileGenerate($commissionHtml, $commissionS3Path);
            
            Log::info("PayoutProcessedMail call for main payout and send mail to " . $payout->user->email);
            Log::info("PayoutProcessedMail link " . json_encode($session));
            Mail::to($payout->user->email)->send(new PayoutCommissionProcessedMail($payout, $commissionS3Path,$payout_cycle_date,$grid['totalAdminFee'],$merchantPaymentLink));
        }
                
        $payout->update([
            'payment_type' => 'online',
            'ecommerce_status' => 1,
            'is_payout' =>true,
            'payout_status' => 'paid',
            'payout_on' => now(),
            'main_path' => $fileName,
            'full_commission_path' => $commissionFileName ?? null,
            'merchant_ecommerce_tran_id' => $merchantTransId ?? null,
        ]);
        return $grid;
    }

    public function fileGenerate($html, $s3Path) {
        $pdfBinary = Browsershot::html($html)
        ->setChromePath('/usr/bin/google-chrome')
        ->format('A4')
        ->noSandbox()
        ->pdf();
        Storage::disk(config(FILESYSTEM_DEFAULT))->put($s3Path, $pdfBinary);
        return $pdfBinary;
    }

    public function transactionCreate($transactionResult,$payout,$status ,$failureMessage = null,$type="Payout")
    {
         Transaction::create([
                "transaction_id" => $transactionResult['transactionNumber'],
                "payout_id" => $payout->id,
                "type" => $type,
                "payment_method" => 'CREDIT',
                "amount" => $transactionResult['amount'] / 100,
                "status" => $status,
                "ecommerce_status" => (int) $transactionResult['status'],
                "meta_data" => json_encode($transactionResult),
                "provider_transaction_id" => $transactionResult['providerTransactionNumber'] ?? null,
                "failure_reason" => $failureMessage,
            ]);
    }

    public function payoutOrderGrid($payoutSubOrders, $payout,$payout_cycle_date) {
        $html = '';
        $totalAdminFee  = $totalPayableAmt = 0;
        foreach ($payoutSubOrders as $payoutSubOrder) {

            $subOrder = $payoutSubOrder->subOrder;

            $orderFileName = 'payout_' . $payout->id . '_sub_order_' . $subOrder->id . '.pdf';
            $s3Path = config('constants.api.payout_pdf.order_payout_path')  .$orderFileName;

            $commission = $subOrder->orderProducts->sum('total_commission');

            if($subOrder->payment_type != 'credit_line') {
                if ($subOrder->payout_type == 'schedule') {
                    $finalAmt = ($subOrder->total_sub_order_value - $commission);
                } else {
                    $finalAmt = $subOrder->total_sub_order_value;
                }
            }else{
                if ($subOrder->payout_type == 'schedule') {
                    $finalAmt = ($subOrder->total_dpharma_points_used - $commission);
                
                    $finalAmt = $finalAmt > 0? $finalAmt : 0;
                } else {
                    $finalAmt = $subOrder->total_dpharma_points_used;
                }
            }
     
            $orderHtml = view('pdf.payout.payout-sub-order-pdf', [
                    'payout' => $payout,
                    'subOrder' =>$subOrder,
                    'payableAmount' => 'RM ' . number_format($finalAmt, 2),
                    'payout_cycle_date' => $payout_cycle_date,
                    'adminFee' => 'RM ' . number_format($commission, 2),
                    'adminSettingDetail' => adminSettingDetail()
                ])->render();
                    
        
                    
            $this->fileGenerate($orderHtml, $s3Path);
            
            $subOrder->update(['payout_path' => $orderFileName]);

            $adminFee = 'RM ' . number_format($commission, 2); 
            $totalAdminFee += $commission;
            $payableAmount = 'RM ' . number_format($finalAmt, 2);
            $totalPayableAmt += $finalAmt;

            $html .= '<tr>';
            $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; "> #'.$payoutSubOrder->order->order_number.'</td>';
                $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; ">
                '.$payoutSubOrder->order->created_at->format('M d, Y').'
                </td>';
                $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; ">
                '.Carbon::parse($subOrder->approved_at)->format('M d, Y').'
                </td>';
                $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; ">
                RM '.$payoutSubOrder->subOrder->total_sub_order_value.'
                </td>';
                $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; ">
                    '.$adminFee .'</td>';
            $html .= '<td style="font-weight: 600; text-align: left; font-size: 12px; color: #1A1C21; padding: 10px 5px; ">
                '.$payableAmount . '</td>';
            $html .= '</tr>';
        }
        return [
            'html' => $html,
            'totalAdminFee' => 'RM ' . number_format($totalAdminFee, 2),
            'totalPayableAmt' => 'RM ' . number_format($totalPayableAmt, 2),
            'totalPayout' => round($totalPayableAmt, 2),
            'returnAmount' => round($totalAdminFee, 2),
            'email' => $subOrder->user->email,
        ];
    }

    public function invoiceDateFormat($payout) {
        if (!$payout->start_date || !$payout->end_date) {
            return '—'; // Or return null to leave it blank
        }
        $start = Carbon::parse($payout->start_date);
        $end = Carbon::parse($payout->end_date);

        $monthYear = $start->format('M Y');
        $startFormatted = $start->format('M jS');
        $endFormatted = $end->format('M jS');

        $payout_cycle_date = "{$monthYear} ({$startFormatted} to {$endFormatted})";
        return $payout_cycle_date;
    }

    public function sendMerchantMail($payout,$transactionResult) {
        $payout_cycle_date = $this->invoiceDateFormat($payout);

        $superAdmins = getAdminData();

        foreach ($superAdmins as $superAdmin) {
            Mail::to($superAdmin->email)->send(new PayoutCommissionMerchantMail($payout, $transactionResult,$payout_cycle_date,$superAdmin));
        }

        Mail::to($payout->user->email)->send(new PcPayoutCommissionConfirmMail($payout, $transactionResult,$payout_cycle_date));

        $payout->update([
            'received_on' =>true,
            'outstanding_commission_status' => 'received',
            'received_on' => now(),
            'merchant_ecommerce_status' => 1,
        ]);

        return $payout_cycle_date;

    }

}