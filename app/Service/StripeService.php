<?php

namespace App\Service;

use App\Models\ClinicDetail;
use Stripe\Exception\ApiErrorException;
use Stripe\Customer;
use Stripe\Stripe;


class StripeService
{
    /**
     * StripeService constructor.
     * @throws ConfigurationException
     */
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function customerCreate($user)
    {
        try { 
            // Create a new customer in Stripe
            $stripeCustomer = Customer::create([
                'email' => $user->email,
                'name' => $user->name,
                'description' => 'Customer for ' . $user->email,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);
            // Save the Stripe customer ID in your database
            ClinicDetail::where('user_id', $user->id)->update(['stripe_customer_id' => $stripeCustomer->id]);
            

            return $stripeCustomer;
        } catch (ApiErrorException $e) {
            // Handle the error
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function updateCustomerEmail($user)
    {
        try {

            $customer = Customer::update($user->stripe_customer_id, [
                'email' => $user->email
            ]);
        } catch (ApiErrorException $e) {
            // Handle the error
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
