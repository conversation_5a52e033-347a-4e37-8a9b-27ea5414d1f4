<?php

namespace App\Service;


use Illuminate\Support\Str;
use Filament\Support\Enums\MaxWidth;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\MarkdownEditor;

class PriceAndVarientSection
{
    public static function getSection()
    {
        return
            Section::make('Price & Variants')
            ->headerActions([
                Action::make('create')
                    ->slideOver()
                    ->modalWidth(MaxWidth::ScreenTwoExtraLarge)
                    ->steps([
                        Step::make('Attributs & Values')
                            ->schema([
                                TextInput::make('name'),
                                TextInput::make('slug')
                            ])
                            ->columns(2),
                        Step::make('Images')
                            ->schema([
                                MarkdownEditor::make('description'),
                            ]),
                        Step::make('Price')
                            ->schema([
                                Toggle::make('is_visible')
                                    ->label('Visible to customers.')
                                    ->default(true),
                            ]),
                        Step::make('Quantity')
                            ->schema([
                                TextInput::make('quantity')
                                    ->numeric()
                                    ->required(),
                            ]),
                        Step::make('Summary')
                            ->schema([])
                    ])
            ]);
    }
}
