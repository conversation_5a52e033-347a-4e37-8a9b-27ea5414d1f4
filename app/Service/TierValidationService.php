<?php

namespace App\Service;

class TierValidationService
{
    public static function validateTierCompletion($tiers)
    {
        if (empty($tiers)) {
            return true; // Empty tiers array is valid - allow adding first tier
        }

        // Check the last tier to make sure it's complete
        $lastTier = end($tiers);

        // A tier is complete if it has max_quantity and price values
        if (
            empty($lastTier['max_quantity']) ||
            empty($lastTier['price']) ||
            $lastTier['max_quantity'] === null ||
            $lastTier['price'] === null
        ) {
            return false;
        }

        return true;
    }
}
