<?php

namespace App\Service;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EcommerceService
{

    public const CONTENT_TYPE = "application/json";



    public function getTenantChannel()
    {
        return $response = Http::withHeader('Abp-TenantId', config("app.commerce_merchant_id"))->get(config('app.commerce_base_url') . '/services/app/Channel/GetTenantChannelsByCountry?countryCode=MY');
    }

    //public function getProviderChannelId($id)
     public function getProviderChannelId()
    {
        $id = 45;
        $response = Http::withHeaders(['Abp-TenantId' => config("app.commerce_merchant_id")])->get(config('app.commerce_base_url') . '/services/app/Channel/GetProviderChannels?ChannelId=' . $id);

        return $response->json()["result"];
    }

    public function store($request,$payAmount,$from = 'facility',$reqData = null)
    {
        if($from == 'payout' || $from == 'merchant_payout'){
            $callbackUrl = $reqData['callbackUrl'];
            $returnUrl = $reqData['returnUrl'];
        }else{
            $callbackUrl = config('app.url')."/api/v1/webhook/payment-handler";
            //$callbackUrl = "http://admin-dpharma-qa.devpress.net/api/v1/webhook/payment-handler-1";
            $returnUrl = config('app.clinic_url')."/callback-handler";
        }
        $baseUrl = config('app.commerce_base_url') . "/services/app/paymentgateway/requestpayment";

        $data = $request ? $request->all() : [];

        unset($data["suppliers"]);
        unset($data["applied_points"]);
        unset($data["shipping_address_id"]);

        $data['returnUrl'] = $returnUrl;
        $data['callbackUrl'] = $callbackUrl;
        $data["amount"] = (int) round($payAmount * 100);
        $data["referenceCode"] = Str::random(16);
        $data["timestamp"] = Carbon::now()->timestamp;
        //$data["channelId"] = 45;
        $data["ipAddress"] = request()->ip();
        $data["userAgent"] = request()->userAgent();
        $data["currencyCode"] = "MYR";
        $data["localCitizen"] = true;

        if($from == 'payout'){
            $data["channelId"] = (int)$reqData["channelId"];
            $data["providerChannelId"] = $reqData["providerChannelId"];
            $data["description"] = $reqData["description"];
            $data["customer"] = $reqData["customer"];
        }

        try {
            $signature = $this->generateSignature($data, $baseUrl);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . Cache::get('access_token'),
                'cap-signature' => $signature["signature"],
            ])->post($baseUrl, $data);
            if($response->status() === 200) {
                $responseData = $response->json();

                if (!isset($responseData["result"])) {
                    //Log::error('Invalid payment gateway response structure', $responseData);
                    throw new \Exception('Invalid payment gateway response');
                }
                return $responseData["result"];
            }elseif($response->status() === 401) {
                $this->generateAccessToken();
                return $this->store($request,$payAmount);
            }else{
                $errorRes = json_decode($response->body());
                $message =  $errorRes->error->message;
                // Log::error('Payment gateway API error', [
                //     'status' => $response->status(),
                //     'response' => $response->body(),
                //     'data' => $data
                // ]);
                $log ='Payment gateway returned error: ' . $response->status(). ' with this details => ' . $message;
                throw new \Exception($log);
            }

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('Payment gateway connection error: ' . $e->getMessage());
            throw new \Exception('Unable to connect to payment gateway. Please try again.');
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('Payment gateway request error: ' . $e->getMessage());
            throw new \Exception('Payment gateway request failed. Please try again.');
        } catch (\Exception $e) {
            Log::error('Payment gateway general error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function generateAccessToken()
    {
        $response = Http::withHeader('Abp-TenantId', config("app.commerce_merchant_id"))->post(config('app.commerce_base_url') .'/TokenAuth/Authenticate', [
            "userNameOrEmailAddress" => "<EMAIL>",
            "password" => "D1234567"
        ]);

        Cache::remember('access_token', now()->addMinutes(50), function () use ($response) {
            return $response->json()["result"]['accessToken'];
        });


        return $response->json()["result"];
    }

    public function generateSignature($data, $url)
    {
        $signatureBodyData = [];
        foreach ($data as $key => $value) {
            $lowerKey = is_string($key) ? strtolower($key) : $key;

            if (is_string($value)) {
                $signatureBodyData[$lowerKey] = strtolower($value);
            } elseif (is_array($value)) {
                $lowerArray = [];

                // Check if associative array
                $isAssoc = array_keys($value) !== range(0, count($value) - 1);

                foreach ($value as $k => $v) {
                    $lk = is_string($k) ? strtolower($k) : $k;
                    $lv = is_string($v) ? strtolower($v) : $v;
                    $lowerArray[$lk] = $lv;
                }

                $signatureBodyData[$lowerKey] = $lowerArray;
            } else {
                $signatureBodyData[$lowerKey] = $value;
            }
        }
        ksort($signatureBodyData);
        $originalSignature = json_encode($signatureBodyData, JSON_UNESCAPED_SLASHES);
        $signatureBody = $url . $originalSignature;
        $signature = hash_hmac('sha256', $signatureBody, "f64301144aae5a27b807593b9272849e464546bf7bf59bea87991c2290f263b7");

        return [
            "data" => $signatureBodyData,
            "signature" => $signature
        ];
    }

    public function webhook($request)
    {
        try {
            $this->generateAccessToken();
            $data = $request->all();

            if (!isset($data['transaction_number'])) {
                Log::error('Invalid webhook data - missing transaction_number', $data);
                throw new \Exception('Invalid transaction number');
            }

            $data["transactionNumber"] = $data["transaction_number"];
            $data["timestamp"] = Carbon::now()->timestamp;
            unset($data["ReferenceCode"]);
            unset($data["transaction_number"]);

            $baseUrl = config('app.commerce_base_url')  . "/services/app/paymentgateway/query";
            $signature = $this->generateSignature($data, $baseUrl);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . Cache::get('access_token'),
                'cap-signature' => $signature["signature"],
                'accept' => self::CONTENT_TYPE,
            ])->get($baseUrl, [
                'timestamp' => $data["timestamp"],
                'transactionNumber' => $data["transactionNumber"]
            ]);
            if ($response->status() === 200) {
                $responseData = $response->json();
                $result = $responseData["result"];

                // Validate response structure
                if (!isset($responseData["result"])) {
                    Log::error('Invalid webhook response structure', $responseData);
                    throw new \Exception('Invalid webhook response');
                }

                return $responseData;

            } elseif ($response->status() === 400) {
                Log::error('Webhook bad request error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'data' => $data
                ]);
                return $response->json();

            } elseif ($response->status() === 401) {
                $this->generateAccessToken();
                $this->webhook($request);
            }else{
                Log::error('Webhook API error', [
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'data' => $data
                ]);
                throw new \Exception('Webhook API returned error: ' . $response->status());
            }
        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('Webhook connection error: ' . $e->getMessage());
            throw new \Exception('Unable to connect to payment gateway webhook. Please try again.');
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('Webhook request error: ' . $e->getMessage());
            throw new \Exception('Webhook request failed. Please try again.');
        } catch (\Exception $e) {
            Log::error('Webhook general error: ' . $e->getMessage());
            throw $e;
        }
    }

    public function refundPayment($transactionId)
    {
        $baseUrl = config('app.commerce_base_url') . "/services/app/paymentgateway/refundpayment";
        $data["transactionNumber"] = $transactionId;
        $data["reason"] = 'Product was damanged';
        // $data["amount"] = (int) round($payAmount * 100);
        $data["timestamp"] = Carbon::now()->timestamp;
         try {
            $signature = $this->generateSignature($data, $baseUrl);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . Cache::get('access_token'),
                'cap-signature' => $signature["signature"],
            ])->post($baseUrl, $data);

            if($response->status() === 200) {
                $responseData = $response->json();

                if (!isset($responseData["result"])) {
                    throw new \Exception('Invalid refund response');
                }
                return $responseData;
            }elseif($response->status() === 401) {
                $this->generateAccessToken();
                return $this->refundPayment($transactionId);
            }else{
                $errorRes = json_decode($response->body());
                $message =  $errorRes->error->message;
                if($message == '[Transaction not found]') {
                    return 'Payment already refund';
                }
                //$log ='refund response returned error: ' . $response->status(). ' with this details => ' . json_encode($errorRes);
                throw new \Exception($message);
            }

        } catch (\Illuminate\Http\Client\ConnectionException $e) {
            Log::error('refund process connection error: ' . $e->getMessage());
            throw new \Exception('Unable to connect to refund process. Please try again.');
        } catch (\Illuminate\Http\Client\RequestException $e) {
            Log::error('refund process request error: ' . $e->getMessage());
            throw new \Exception('refund process request failed. Please try again.');
        } catch (\Exception $e) {
            Log::error('refund process general error: ' . $e->getMessage());
            throw $e;
        }

    }

}