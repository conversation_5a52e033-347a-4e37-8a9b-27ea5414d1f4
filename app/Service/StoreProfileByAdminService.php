<?php

namespace App\Service;

use App\Mail\SendPCPasswordMail;
use App\Models\PcCertificateFile;
use App\Models\PcDetail;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\WareHouse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class StoreProfileByAdminService
{
    public function storeCompanyInformations(array $data, int $step)
    {
        $data['step'] = $step;
        User::where('id', $data['user_id'])->update(['name' => $data['company_name']]);
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    public function storeContactDetails(array $data)
    {

        DB::transaction(function () use ($data) {
            $phone = $data['phone_number'];
            $phoneNumberCode = $data['phone_code'];
            PcDetail::updateOrCreate(['user_id' => $data['user_id']], ['phone_number' => $phone, 'step' => 2, 'profile_email' => $data['profile_email'],  'web_url' => $data['web_url'], "phone_number_code" => $phoneNumberCode,'region' => $data['region']]);
            unset($data['phone_number']);
            unset($data['profile_email']);
            // unset($data['postal_code']);
            unset($data['web_url']);
            $data['is_onboarding'] = true;
            $data['country_id'] = 132; // 132 is country id for malasia
            $data['is_company_address'] = 1;            
            UserAddress::updateOrCreate(['user_id' => $data['user_id']], $data);
        });
       
    }

    public function storeWareHouse(array $data)
    {
        
        if (! empty($data['ware_id'])) {
            if($data['warehouse_type'] == 'owned') {
                $data = [
                    "phone_number" => null,
                    "address_1" => null,
                    "address_2" => null,
                    "district" => null,
                    "state_id" => null,
                    "postal_code" => null,
                    "city_id" => null,
                    "warehouse_type" => $data['warehouse_type'],
                    "ware_region" => null,
                    'ware_id' => $data['ware_id'],
                    "user_id" => $data['user_id'],
                ];
            }
            if ($data['warehouse_type'] == 'dpharma') {
                PcDetail::where('user_id', $data['user_id'])->update([
                    'delivery_days' => null,
                    'delivery_days_west' => null,
                ]);
            }
            WareHouse::where('id', $data['ware_id'])->update(Arr::except($data, 'ware_id'));
        } else {
            WareHouse::where('user_id', $data['user_id'])->delete();
            unset($data['ware_id']);
            PcDetail::updateOrCreate(['user_id' => $data['user_id']], ['step' => 3]);
            $data = [
                "phone_number" => null,
                "address_1" => $data['address_1'],
                "address_2" => $data['address_2'],
                "district" => null,
                "state_id" => $data['state_id'],
                "postal_code" => $data['postal_code'],
                "city_id" => $data['city_id'],
                "warehouse_type" => $data['warehouse_type'],
                "ware_region" => $data['ware_region'],
                "user_id" => $data['user_id'],
            ];
            WareHouse::create($data);
        }

    }

    public function storePersonInCharge(array $data, $step)
    {
        $data['step'] = $step;
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data);
    }

    public function storeDocuments(array $data, $recordId)
    {
        $data['user_id'] = $recordId;
        PcDetail::where('user_id', $data['user_id'])->update([
            // 'company_registration_certificate' => $data['company_registration_certificate'],
            // 'license_permit' => $data['license_permit'],
            'step' => 5
        ]);
        $this->processFileUploads($data['user_id'], $data, 'company_registration_certificate', 'company_registration_certificate');
        $this->processFileUploads($data['user_id'], $data, 'license_permit', 'license_permit');
    }

    public function sendCredential(array $data)
    {
        $userEmail = $data['email'];
        $plainPassword = $this->generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['user_id'])->update(['password' => $hashedPassword]);
        $loginUrl = config('app.pc_url').'/home';
        Mail::to($userEmail)->send(new SendPCPasswordMail($plainPassword, $userEmail, $loginUrl));
    }

    protected function generateStrongPassword(): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $digits = '**********';
        $specialChars = '@$!%*?&';

        $password = [
            $lowercase[rand(0, strlen($lowercase) - 1)],
            $uppercase[rand(0, strlen($uppercase) - 1)],
            $digits[rand(0, strlen($digits) - 1)],
            $specialChars[rand(0, strlen($specialChars) - 1)],
        ];

        // Fill the rest of the password with random characters from all sets
        $allChars = $lowercase . $uppercase . $digits . $specialChars;
        for ($i = count($password); $i < 8; $i++) {
            $password[] = $allChars[rand(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize the order
        shuffle($password);

        // Convert the array to a string
        return implode('', $password);
    }

    protected function processFileUploads(int $userId, array $data, string $inputKey, string $fileType): void
    {
        if (empty($data[$inputKey])) {
            return;
        }

        PcCertificateFile::where('user_id', $userId)
        ->where('type', $fileType)
        ->where('status', 'active')
        ->update(['status' => 'inactive']);

        $filesToInsert = array_map(function ($filePath) use ($userId, $fileType) {
            return [
                'user_id' => $userId,
                'type' => $fileType,
                'name' => basename($filePath), // Use the basename of the file path as the name$filePath,
                'status' => 'active',
                'size' => getFormattedFileSize($filePath),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }, (array)$data[$inputKey]);
        if (!empty($filesToInsert)) {
            PcCertificateFile::insert($filesToInsert);
        }
    }

    public function storeBankDetails(array $data)
    {
        PcDetail::updateOrCreate(['user_id' => $data['user_id']], $data, );
    }
    
}
