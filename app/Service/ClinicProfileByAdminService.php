<?php

namespace App\Service;

use App\Mail\SendClinicPasswordMail;
use App\Models\ClinicCertificateFile;
use App\Models\ClinicDetail;
use App\Models\User;
use App\Models\ClinicPharmaSupplier;
use App\Models\UserAddress;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Nnjeim\World\Models\State;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ClinicProfileByAdminService
{
    public function storeFacelityInformations(array $data, int $step, $email)
    {
        $data['completed_step'] = $step;
        $user['email'] = $email;

        $userData = User::where('email', $user['email'])->first();
        if(empty($userData)) {
            $userData = User::create(['email' => $email, 'name' => $data['clinic_name'], 'verification_status' => 'pending', 'created_by' => auth()->user()->id]);
            $userData->assignRole('Clinic');
        }else{
            $userData->name = $data['clinic_name'];
            $userData->save();
        }


        $clinic = ClinicDetail::where('user_id', $userData['id'])->first();
        if($clinic) {
            $clinic->clinic_name = $data['clinic_name'];
            $clinic->clinic_account_type_id = $data['clinic_account_type_id'];
            $clinic->clinic_number = $data['clinic_number'];
            $clinic->mobile_number = $data['mobile_number'];
            $clinic->landline_number = $data['landline_number'];
            $clinic->landline_code = $data['landline_code'];
            $clinic->business_type_id = $data['business_type_id'];
            $clinic->company_name = $data['company_name'];
            $clinic->company_number = $data['company_number'];
            // $clinic->clinic_owner = $data['clinic_owner'];
            $clinic->clinic_year = $data['clinic_year'];
            $clinic->tin_number = $data['tin_number'];
            $clinic->sst_number = $data['sst_number'];
            $clinic->update();
            $result = $clinic;
        } else {
            $data['user_id'] = $userData['id'];
            $result = ClinicDetail::create($data);
            // $data['email'] = $user['email'] ;
            // $this->sendClinicCredential($data);
        }
        return $userData;
    }

    // public function storeAddressDetails(array $data)
    // {
    //     UserAddress::where('user_id', $data['user_id'])->delete();
    //     DB::transaction(function () use ($data) {
    //         $userId = $data['user_id'];
    //         $data['country_id'] = 132; // 132 is country id for malasia
    //         $shippingStateInfo = [];
    //         if(count($data['shipping_addresses']) > 0) {
    //             foreach($data['shipping_addresses'] as $address) {
    //                 $shippingAddress = [];
    //                 $shippingAddress['address_type'] = 'shipping';
    //                 $shippingAddress['user_id'] = $userId;
    //                 $shippingAddress['address_1'] = $address['shipping_address_1'];
    //                 $shippingAddress['address_2'] = $address['shipping_address_2'];
    //                 $shippingAddress['city_id'] = $address['shipping_city_id'];
    //                 $shippingAddress['state_id'] = $address['shipping_state_id'];
    //                 $shippingAddress['country_id'] = $data['country_id'];
    //                 $shippingAddress['is_onboarding'] = true;
    //                 $shippingAddress['postal_code'] = $address['shipping_postal_code'];
    //                 $shippingAddress['is_default'] = $address['is_default'];
    //                 $shippingAddressData = UserAddress::create($shippingAddress);

    //             }

    //         }

    //         $defaultShippingAddress = [];
    //         $defaultShippingAddress['address_type'] = 'shipping';
    //         $defaultShippingAddress['user_id'] = $userId;
    //         $defaultShippingAddress['is_onboarding'] = true;
    //         $defaultShippingAddress['address_1'] = $data['shipping_address_1'];
    //         $defaultShippingAddress['address_2'] = $data['shipping_address_2'];
    //         $defaultShippingAddress['city_id'] = $data['shipping_city_id'];
    //         $defaultShippingAddress['state_id'] = $data['shipping_state_id'];
    //         $defaultShippingAddress['country_id'] = $data['country_id'];
    //         $defaultShippingAddress['postal_code'] = $data['shipping_postal_code'];
    //         $defaultShippingAddress['is_company_address'] = 1;
    //         $defaultShippingAddress['is_default'] = $data['is_default'];
    //         $defaultShippingAddress = UserAddress::create($defaultShippingAddress);
    //         $shippingStateInfo = State::where(['id' => $data['shipping_state_id']])->first();

    //         $shippingStateInfo = State::where(['id' => $data['shipping_state_id']])->first();
    //         $billingAddress = [];
    //         $billingAddress['address_type'] = 'billing';
    //         $billingAddress['user_id'] = $userId;
    //         $billingAddress['is_onboarding'] = true;
    //         $billingAddress['address_1'] = $data['billing_address_1'];
    //         $billingAddress['address_2'] = $data['billing_address_2'];
    //         $billingAddress['city_id'] = $data['billing_city_id'];
    //         $billingAddress['state_id'] = $data['billing_state_id'];
    //         $billingAddress['country_id'] = $data['country_id'];
    //         $billingAddress['postal_code'] = $data['billing_postal_code'];
    //         $billingAddress['is_company_address'] = 1;
    //         $billingAddressData = UserAddress::create($billingAddress);



    //         ClinicDetail::updateOrCreate(
    //             ['user_id' => $userId],
    //             ['completed_step' => 2, 'zone' => !empty($shippingStateInfo) ? $shippingStateInfo->zone : '', 'shipping_addresses_id' => !empty($defaultShippingAddress) ? $defaultShippingAddress['id'] : null, 'billing_addresses_id' => $billingAddressData['id'], 'is_billing_address_same' => $data['is_billing_address_same']]
    //         );

    //     });
    // }

    // public function storeAddressDetails(array $data)
    // {
    //     UserAddress::where('user_id', $data['user_id'])->delete();
    //     DB::transaction(function () use ($data) {
    //         $userId = $data['user_id'];
    //         $data['country_id'] = 132; // 132 is country id for malaysia

    //         // First, handle the billing address
    //         $billingAddress = [
    //             'address_type' => 'billing',
    //             'user_id' => $userId,
    //             'is_onboarding' => true,
    //             'address_1' => $data['billing_address_1'],
    //             'address_2' => $data['billing_address_2'],
    //             'city_id' => $data['billing_city_id'],
    //             'state_id' => $data['billing_state_id'],
    //             'country_id' => $data['country_id'],
    //             'postal_code' => $data['billing_postal_code'],
    //             'is_company_address' => 1,
    //             'is_default' => false, // Billing address is never default for shipping
    //         ];
    //         // dd($billingAddress);
    //         $billingAddressData = UserAddress::create($billingAddress);

    //         // Handle shipping addresses from repeater
    //         $defaultShippingAddressId = null;
    //         if (count($data['shipping_addresses']) > 0) {
    //             foreach ($data['shipping_addresses'] as $address) {
    //                 $shippingAddress = [
    //                     'address_type' => 'shipping',
    //                     'user_id' => $userId,
    //                     'address_1' => $address['shipping_address_1'],
    //                     'address_2' => $address['shipping_address_2'],
    //                     'city_id' => $address['shipping_city_id'],
    //                     'state_id' => $address['shipping_state_id'],
    //                     'country_id' => $data['country_id'],
    //                     'is_onboarding' => true,
    //                     'postal_code' => $address['shipping_postal_code'],
    //                     'is_default' => $address['is_default'] ?? false,
    //                 ];

    //                 $shippingAddressData = UserAddress::create($shippingAddress);

    //                 // Track the default shipping address
    //                 if ($shippingAddress['is_default']) {
    //                     $defaultShippingAddressId = $shippingAddressData->id;
    //                 }
    //             }
    //         }

    //         // If no default was set in repeater, use the first shipping address as default
    //         if (empty($defaultShippingAddressId) && isset($shippingAddressData)) {
    //             $defaultShippingAddressId = $shippingAddressData->id;
    //             UserAddress::where('id', $defaultShippingAddressId)->update(['is_default' => true]);
    //         }

    //         // Get state info for zone
    //         $shippingStateInfo = State::where(['id' => $data['shipping_state_id']])->first();

    //         // Update clinic details
    //         ClinicDetail::updateOrCreate(
    //             ['user_id' => $userId],
    //             [
    //                 'completed_step' => 2,
    //                 // 'zone' => !empty($shippingStateInfo) ? $shippingStateInfo->zone : '',
    //                 'shipping_addresses_id' => $defaultShippingAddressId,
    //                 'billing_addresses_id' => $billingAddressData->id,
    //                 // 'is_billing_address_same' => $data['is_billing_address_same']
    //             ]
    //         );
    //     });
    // }

    public function storeAddressDetails(array $data)
    {
        UserAddress::where('user_id', $data['user_id'])->delete();
        DB::transaction(function () use ($data) {
            $userId = $data['user_id'];
            $data['country_id'] = 132; // 132 is country id for malaysia

            // First, handle the billing address
            $billingAddress = [
                'address_type' => 'billing',
                'user_id' => $userId,
                'is_onboarding' => true,
                'address_1' => $data['billing_address_1'],
                'address_2' => $data['billing_address_2'],
                'city_id' => $data['billing_city_id'],
                'state_id' => $data['billing_state_id'],
                'country_id' => $data['country_id'],
                'postal_code' => $data['billing_postal_code'],
                'is_company_address' => 1,
                'nick_name' => $data['billing_nick_name'],
                'is_default' => false, // Billing address is never default for shipping
            ];

            $billingAddressData = UserAddress::create($billingAddress);

            // Handle shipping addresses from repeater
            $defaultShippingAddressId = null;
            $shippingAddressesToSave = [];

            if (count($data['shipping_addresses']) > 0) {
                foreach ($data['shipping_addresses'] as $address) {
                    // Check if shipping address matches billing address
                    $isSameAsBilling =
                        $address['shipping_address_1'] === $data['billing_address_1'] &&
                        $address['shipping_address_2'] === $data['billing_address_2'];

                    // Only save if different from billing address
                    // if (!$isSameAsBilling) {
                    $shippingAddress = [
                        'address_type' => 'shipping',
                        'user_id' => $userId,
                        'address_1' => $address['shipping_address_1'],
                        'address_2' => $address['shipping_address_2'],
                        'city_id' => $address['shipping_city_id'],
                        'state_id' => $address['shipping_state_id'],
                        'country_id' => $data['country_id'],
                        'is_onboarding' => true,
                        'postal_code' => $address['shipping_postal_code'],
                        'is_default' => $address['is_default'] ?? false,
                        'is_same_as_billing' => $address['is_same_as_billing'] ?? false,
                        'nick_name' => $address['shipping_nick_name'],
                    ];

                    $shippingAddressData = UserAddress::create($shippingAddress);
                    $shippingAddressesToSave[] = $shippingAddressData;

                    // Track the default shipping address
                    if ($shippingAddress['is_default']) {
                        $defaultShippingAddressId = $shippingAddressData->id;
                    }
                    // }
                }
            }

            // If no default was set in repeater, use the first shipping address as default
            if (empty($defaultShippingAddressId) && !empty($shippingAddressesToSave)) {
                $defaultShippingAddressId = $shippingAddressesToSave[0]->id;
                UserAddress::where('id', $defaultShippingAddressId)->update(['is_default' => true]);
            }

            // Get state info for zone
            $shippingStateInfo = State::where(['id' => $data['shipping_state_id']])->first();

            // Update clinic details
            ClinicDetail::updateOrCreate(
                ['user_id' => $userId],
                [
                    'completed_step' => 2,
                    'shipping_addresses_id' => $defaultShippingAddressId,
                    'billing_addresses_id' => $billingAddressData->id,
                ]
            );
        });
    }

    public function storeDoctorInCharge(array $data, $step, $signature)
    {
        $userId = $data['user_id'];

        $data['completed_step'] = 4;
        // dd(array_values($data['dc_signature']));
        if (!empty($data['dc_signature'])) {

            // Check if it's an uploaded file (array) or existing filename (string)
            if (is_array($data['dc_signature'])) {
                // Handle new file upload
                $uploadedFile = reset($data['dc_signature']);

                if ($uploadedFile instanceof \Livewire\Features\SupportFileUploads\TemporaryUploadedFile) {
                    $extension = $uploadedFile->getClientOriginalExtension();
                    $filename = 'signature_'.time().'.'.$extension;
                    $storagePath = config('constants.api.media.clinic_medias').$data['id'].'/';

                    $uploadedFile->storeAs($storagePath, $filename, 's3');
                    $data['dc_signature'] = $filename;
                }
            } else {
                $base64String = $data['dc_signature'];

                try {
                    // Validate base64 string
                    if (!preg_match('/^data:image\/(\w+);base64,/', $base64String, $matches)) {
                        throw new \Exception('Invalid base64 image format');
                    }


                    $imageType = $matches[1]; // png, jpeg, etc.
                    $imageData = base64_decode(preg_replace('/^data:image\/\w+;base64,/', '', $base64String));

                    if (!$imageData) {
                        throw new \Exception('Failed to decode base64 image');
                    }

                    // Generate filename and path
                    $filename = 'signature_'.time().'.'.$imageType;
                    $storagePath = config('constants.api.media.clinic_medias').$data['id'].'/';

                    // Ensure directory exists
                    //    $res =  Storage::disk('s3')->makeDirectory($storagePath);
                    //     dd('test');
                    // Save file
                    $fullPath = $storagePath.$filename;

                    Storage::disk('s3')->put($fullPath, $imageData);

                    // Verify file was saved
                    if (!Storage::disk('s3')->exists($fullPath)) {
                        throw new \Exception('File save verification failed');
                    }
                    // dd($filename);
                    $data['dc_signature'] = $filename;

                } catch (\Exception $e) {
                    Log::error('Signature upload failed: '.$e->getMessage());
                    // Handle error (set default image, return error, etc.)
                    $data['dc_signature'] = $data['dc_signature'];
                }
            }
            // If it's a string, it's already a filename - no processing needed
        }
        // dd($data);
        ClinicDetail::updateOrCreate(['user_id' => $userId], $data);
        $data['user_id'] = $userId;
        $userEmail = User::where(['id' => $userId])->pluck('email')->first();
        if (!$userEmail) {
            $data['email'] = $userEmail;
            $this->sendClinicCredential($data);
        }
    }

    private static function storeBase64Image(string $base64Image, $userId): string
    {
        // Decode the Base64 string
        @list($type, $data) = explode(';', $base64Image);
        @list(, $data) = explode(',', $data);

        $data = base64_decode($data);
        if(empty($data)) {
            return $base64Image;
        }
        // Generate a unique file name
        $fileName =  uniqid() . '.png';

        // Store the file in the public disk
        Storage::disk('s3')->put(config('constants.api.media.clinic_medias').$userId.'/'.$fileName, $data);

        return $fileName;
    }

    public function storeUpdateClinicStepInfo(array $data)
    {
        $clinicId = $data['clinic_id'];
        $data['completed_step'] = 3;
        ClinicDetail::updateOrCreate(['id' => $clinicId], ['completed_step' => 3]);
        return true;
    }

    public function storeClinicPharmaSuplier(array $data, $step)
    {
        $clinicId = $data['clinic_id'];
        $data['completed_step'] = 3;
        ClinicDetail::updateOrCreate(['id' => $clinicId], ['completed_step' => 3]);

        // if(count($data['pc_suplier']) > 0){
        ClinicPharmaSupplier::where(['clinic_id' => $data['user_id'], 'pc_id' => $data['pc_suplier']])->delete();
        // foreach($data['pc_suplier'] as $suplier){
        $new = new ClinicPharmaSupplier();
        $new->clinic_id = $data['user_id'];
        $new->pc_id = $data['pc_suplier'];
        $new->account_number = $data['account_number'];
        $new->status = ClinicPharmaSupplier::STATUS_APPROVED;
        $new->save();
        // }

        // }


    }

    // public function storeLegalDocuments(array $data, $recordId): bool
    // {
    //     $data['user_id'] = $recordId;
    //     $UserId = ClinicDetail::where('user_id', $data['user_id'])->pluck('id')->first();
    //     $updateDocuments = [];

    //     $certificateFields = [
    //         'dc_signature',
    //         'borang_certificate',
    //         'mmc_certificate',
    //         'apc_certificate',
    //         'arc_certificate',
    //         'poison_license'
    //     ];
    //     $updateDocuments['apc_certificate_expired_date'] = $data['apc_certificate_expired_date'];

    //     foreach ($certificateFields as $field) {
    //         if (!array_key_exists($field, $data) || empty($data[$field])) {
    //             continue;
    //         }

    //         // Get the uploaded file instance
    //         $uploadedFile = $data[$field];

    //         // 1. Deactivate old files
    //         ClinicCertificateFile::where([
    //             'user_id' => $data['user_id'],
    //             'type' => $field,
    //             'status' => 'active'
    //         ])->update(['status' => 'inactive']);

    //         // 2. Handle file upload
    //         $filename = $this->storeUploadedFile($uploadedFile, $UserId, $field);

    //         if ($filename) {
    //             // 3. Create new record
    //             ClinicCertificateFile::create([
    //                 'user_id' => $data['user_id'],
    //                 'type' => $field,
    //                 'name' => $filename,
    //                 'status' => 'active'
    //             ]);
    //         }
    //     }

    //     return true;
    // }

    // public function storeLegalDocuments(array $data, $recordId): bool
    // {
    //     $data['user_id'] = $recordId;

    //     $updateDocuments = [];

    //     $certificateFields = [
    //         'dc_signature',
    //         'borang_certificate',
    //         'mmc_certificate',
    //         'apc_certificate',
    //         'arc_certificate',
    //         'poison_license'
    //     ];
    //     $updateDocuments['apc_certificate_expired_date'] = $data['apc_certificate_expired_date'];

    //     foreach ($certificateFields as $field) {
    //         if (!array_key_exists($field, $data)) {
    //             continue;
    //         }

    //         if (!empty($data[$field])) {
    //             $image = collect((array)$data[$field])->first();

    //             if ($image) {
    //                 $filename = basename($image);
    //                 ClinicCertificateFile::where([
    //                     'user_id' => $data['user_id'],
    //                     'type' => $field,
    //                     'status' => 'active'
    //                 ])->update(['status' => 'inactive']);

    //                 ClinicCertificateFile::create([
    //                     'user_id' => $data['user_id'],
    //                     'type' => $field,
    //                     'name' => $filename,
    //                     'status' => 'active'
    //                 ]);
    //             }
    //         }
    //     }

    //     return true;
    // }

    public function storeLegalDocuments(array $data, $recordId): bool
    {
        $data['user_id'] = $recordId;
        $updateDocuments = [];

        $certificateFields = [
            'dc_signature',
            'borang_certificate', // can be single or multiple
            'mmc_certificate',
            'apc_certificate',
            'arc_certificate',
            'poison_license',
            'other_relevant_documents'
        ];
        // dd($data);
        $updateDocuments['apc_certificate_expired_date'] = $data['apc_certificate_expired_date'] ?? null;

        foreach ($certificateFields as $field) {
            if (!array_key_exists($field, $data)) {
                continue;
            }

            if (!empty($data[$field])) {
                // First, deactivate all existing files of this type
                ClinicCertificateFile::where([
                    'user_id' => $data['user_id'],
                    'type' => $field,
                    'status' => 'active'
                ])->update(['status' => 'inactive']);

                // Handle file(s)
                $files = (array)$data[$field]; // Convert to array to handle both single and multiple
// dd($files);
                foreach ($files as $file) {
                    if ($file) {
                        $filename = basename($file);
                        ClinicCertificateFile::create([
                            'user_id' => $data['user_id'],
                            'type' => $field,
                            'name' => $filename,
                            'status' => 'active'
                        ]);
                    }
                }
            }
        }
        $clinicData = ClinicDetail::where(['user_id' => $data['user_id']])->first();
        $userEmail = User::where(['id' => $data['user_id']])->pluck('email')->first();

        if (!empty($userEmail) && $clinicData->is_submitted == false) {
            $data['email'] = $userEmail;
            // $this->sendClinicCredential($data);
        }
        ClinicDetail::updateOrCreate(
            ['user_id' => $data['user_id']],
            [
                'completed_step' => 5,
                // 'is_submitted' => true
            ]
        );
        return true;
    }
    // public function storeLegalDocuments(array $data, $recordId): bool
    // {
    //     // Assign user_id to $data
    //     $data['user_id'] = $recordId;

    //     // Initialize updateDocuments array
    //     $updateDocuments = [];

    //     // List of certificate fields to process
    //     $certificateFields = [
    //         'dc_signature',
    //         'borang_certificate',
    //         'mmc_certificate',
    //         'apc_certificate',
    //         'arc_certificate',
    //         'poison_license'
    //     ];
    //     // $certificateFields['dc_signature'] = $data['final_dc_signature'];
    //     $updateDocuments['apc_certificate_expired_date'] = $data['apc_certificate_expired_date'];
    //     foreach ($certificateFields as $field) {
    //         if (!empty($data[$field]) && gettype($data[$field]) != 'string') {
    //             $updateDocuments[$field] = collect((array)$data[$field])->first();
    //         } else {
    //             if (!empty($data[$field])) {
    //                 $updateDocuments[$field] = $data[$field];
    //             }

    //         }
    //     }
    //     // dd($data);
    //     // Debug to check $updateDocuments (optional)

    //     // Update the database
    //     try {
    //         ClinicDetail::where('user_id', $data['user_id'])->update($updateDocuments);
    //         return true;
    //     } catch (\Exception $e) {
    //         info('Error in storeLegalDocuments: ' . $e->getMessage());
    //         throw $e;
    //     }
    // }
    public function sendClinicCredential(array $data)
    {
        $userEmail = $data['email'];
        $plainPassword = $this->generateStrongPassword();
        $hashedPassword = Hash::make($plainPassword);
        User::where('id', $data['user_id'])->update(['password' => $hashedPassword,'email_verified_at' => now(),'status' => true,'is_otp_verified' => 1]);
        Mail::to($userEmail)->send(new SendClinicPasswordMail($plainPassword, $userEmail));
    }

    protected function generateStrongPassword(): string
    {
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $digits = '0123456789';
        $specialChars = '@$!%*?&';

        $password = [
            $lowercase[rand(0, strlen($lowercase) - 1)],
            $uppercase[rand(0, strlen($uppercase) - 1)],
            $digits[rand(0, strlen($digits) - 1)],
            $specialChars[rand(0, strlen($specialChars) - 1)],
        ];

        // Fill the rest of the password with random characters from all sets
        $allChars = $lowercase . $uppercase . $digits . $specialChars;
        for ($i = count($password); $i < 8; $i++) {
            $password[] = $allChars[rand(0, strlen($allChars) - 1)];
        }

        // Shuffle the password to randomize the order
        shuffle($password);

        // Convert the array to a string
        return implode('', $password);
    }

    protected function storeUploadedFile($uploadedFile, $userId, $fieldName)
    {
        try {
            // Configure storage path
            $basePath = config('constants.api.media.clinic_medias');
            $userFolder = "{$basePath}/{$userId}";

            // Ensure directory exists
            if (!Storage::exists($userFolder)) {
                Storage::makeDirectory($userFolder);
            }

            // Generate unique filename
            $extension = $uploadedFile->getClientOriginalExtension();
            $filename = "{$fieldName}_" . time() . ".{$extension}";

            // Store the file
            $path = $uploadedFile->storeAs(
                "clinic_medias/{$userId}",
                $filename,
                's3' // Using public disk
            );

            return $filename;
        } catch (\Exception $e) {
            \Log::error("File upload failed: " . $e->getMessage());
            return null;
        }
    }

}
