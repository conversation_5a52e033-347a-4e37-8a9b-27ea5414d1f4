<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use Filament\Panel;
use Filament\Facades\Filament;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Solutionforest\FilamentEmail2fa\Trait\HasTwoFALogin;
use Solutionforest\FilamentEmail2fa\Interfaces\RequireTwoFALogin;
use Spatie\Activitylog\Traits\LogsActivity;
use App\Traits\LogsActivityTrait;
use Illuminate\Support\Facades\Storage;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Str;


class User extends Authenticatable implements FilamentUser, HasMedia, RequireTwoFALogin, HasName
{
    use HasApiTokens;
    use Notifiable;

    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use Notifiable;
    use HasRoles;
    use HasTwoFALogin;
    use InteractsWithMedia;
    use SoftDeletes;
    use LogsActivity;
    use LogsActivityTrait;
   

    public function canAccessPanel(Panel $panel): bool
    {
        if (Filament::getCurrentPanel()->getId() === 'admin') {
            return $this->hasRole('Super Admin') || auth()->user()->parent_id != null;
        } elseif (Filament::getCurrentPanel()->getId() === 'pc') {
            return $this->hasRole('Pharmaceutical Company') || auth()->user()->parent_id != null;
        } elseif (Filament::getCurrentPanel()->getId() === 'clinic') {
            return $this->hasRole('Clinic');
        }

        return false;
    }

    public function getFilamentName(): string
    {
        return "{$this->name}" ?? '';
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    // protected $fillable = [
    //     'name',
    //     'email',
    //     'password',
    // ];
    public $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function warehouses(): HasMany
    {
        return $this->hasMany(WareHouse::class);
    }

    public function warehouseInfo(): HasOne
    {
        return $this->hasOne(WareHouse::class);
    }

    public function pcDetails(): HasOne
    {
        return $this->hasOne(PcDetail::class);
    }

    public function clinicData(): HasOne
    {
        return $this->hasOne(ClinicDetail::class);
    }

    public function addresses(): HasMany
    {
        return $this->hasMany(UserAddress::class, 'user_id', 'id');
    }

    public function address()
    {
        return $this->hasOne(UserAddress::class, 'user_id', 'id')->where('is_company_address', 1)->latest();
    }
    public function defaultShipAddress()
    {
        return $this->hasOne(UserAddress::class, 'user_id', 'id')->where('address_type', 'shipping')->where('is_default', 1)->latest();
    }

    public function accounttype(): HasOne
    {
        return $this->hasOne(AccountType::class, 'id', 'account_type_id');
    }

    public function pharmaSuppliers()
    {
        return $this->hasMany(ClinicPharmaSupplier::class, 'clinic_id');
    }

    public function clinicDetails(): HasOne
    {
        return $this->HasOne(ClinicDetail::class);
    }

    public function productRelations()
    {
        return $this->hasMany(ProductRelation::class, 'user_id');
    }

    public function carts()
    {
        return $this->hasMany(ClinicAddCart::class, 'supplier_id');
    }

    public function dpharmaPoints(): HasMany
    {
        return $this->hasMany(DpharmaPoint::class);
    }

    public function userAddresses()
    {
        return $this->hasMany(UserAddress::class);
    }

    public function shippingAddresses()
    {
        return $this->hasMany(UserAddress::class)->where('address_type', 'shipping')->where('status','approved')->where('is_requested',false);
    }
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'sender_id');
    }
    public function parent()
    {
        return $this->belongsTo(User::class, 'parent_id');
    }
    // public function roles()
    // {
    //     return $this->belongsToMany(Role::class, 'model_has_roles', 'model_id', 'role_id');
    // }

    public function clinicCertificateFiles()
    {
        return $this->hasMany(ClinicCertificateFile::class);
    }

    public function billingAddress()
    {
        return $this->hasOne(UserAddress::class, 'user_id', 'id')->where('address_type', 'billing')->latest();
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function subOrders()
    {
        return $this->hasMany(\App\Models\SubOrder::class);
    }


    public function approvals()
    {
        return $this->hasMany(Approval::class, 'approvalable_id');
    }

    public function payouts()
    {
        return $this->hasMany(Payout::class);
    }

    public function getImageUrlAttribute(): string
    {
        if ($this->photo) {
            if (Str::startsWith($this->photo, 'users/')) {
                return Storage::disk('s3')->url($this->photo);
            }

            return Storage::disk('s3')->url('users/' . $this->photo);
        }

        return asset('/images/user-avatar.png');
    }


    public function setEmailAttribute($value)
    {
        $this->attributes['email'] = strtolower($value);
    }

    public function clinicCreditHistory()
    {
        return $this->hasOne(ClinicCreditHistory::class, 'facility_id');
    }


    public function certificateFiles()
    {
        return $this->hasMany(PcCertificateFile::class);
    }

    // In User.php (assuming created_by FK)
    public function createdRoles()
    {
        return $this->hasMany(Role::class, 'created_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function getNameAttribute($value)
    {
        if ($this->hasRole('Super Admin')) {
            return 'DPharma';
        }

        return $value;
    }

    public function inquiries()
    {
        return $this->hasMany(Inquiry::class);
    }
}
