<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClinicCreditHistory extends Model
{
    public function facility()
    {
        return $this->belongsTo(User::class, 'facility_id');
    }

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    public function clinicDetail()
    {
        return $this->belongsTo(ClinicDetail::class, 'facility_id');
    }

    public function suborder()
    {
        return $this->belongsTo(Suborder::class, 'reference_id')->where('reference_value','suborder');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'reference_id')->where('reference_value','user');
    }
}
