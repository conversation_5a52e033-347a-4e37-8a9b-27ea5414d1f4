<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductAttribute extends Model
{
    protected $fillable = [
        'product_id',
        'attribute_id', 
        'attribute_value_id'
    ];

    protected $casts = [
        'product_id' => 'integer',
        'attribute_id' => 'integer',
        'attribute_value_id' => 'integer'
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class);
    }

    public function attributeValue(): BelongsTo
    {
        return $this->belongsTo(AttributeValue::class);
    }

    /**
     * Get a formatted display for this product-attribute combination
     */
    public function getDisplayAttribute(): string
    {
        return $this->attribute->name . ': ' . $this->attributeValue->name;
    }

    /**
     * Scope to get attributes for a specific product
     */
    public function scopeForProduct($query, int $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to get specific attribute for a product
     */
    public function scopeForProductAttribute($query, int $productId, int $attributeId)
    {
        return $query->where('product_id', $productId)
                    ->where('attribute_id', $attributeId);
    }
} 