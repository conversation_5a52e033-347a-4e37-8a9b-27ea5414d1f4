<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductBatch extends Model
{
    use HasFactory;

    // Define the table associated with the model (if it's not the default name)
    protected $table = 'products_batch';

    // Define the fillable properties to allow mass assignment
    protected $fillable = [
        'product_id',
        'user_id',
        'products_relation_id',
        'batch_name',
        'available_stock',
        'expiry_date',
    ];

    // Cast expiry_date to datetime
    protected $casts = [
        'expiry_date' => 'datetime',
    ];

    /**
     * Mutator for expiry_date to set time to end of day (23:59:59)
     */
    protected function expiryDate(): Attribute
    {
        return Attribute::make(
            set: fn($value) => $value ? Carbon::parse($value)->setTime(23, 59, 0) : null,
        );
    }

    // Relationship with Product model
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Relationship with User model
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relationship with ProductRelation model
    public function productRelation()
    {
        return $this->belongsTo(ProductRelation::class, 'products_relation_id');
    }
    
    public function productBatch()
    {
        return $this->hasOne(ProductBatch::class);
    }
}
