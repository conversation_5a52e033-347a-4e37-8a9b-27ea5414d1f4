<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\Country;
use Nnjeim\World\Models\State;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Order extends Model implements HasMedia
{
    use InteractsWithMedia;
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoices')
            ->useDisk('public');
        // ->storingConversionsOnDisk('public');
    }
    public function user()
    {
        return $this->belongsTo(User::class);
    }
    public function clinicDetail()
    {
        return $this->belongsTo(ClinicDetail::class, 'user_id','user_id');
    }
    public function orderProducts()
    {
        return $this->hasMany(OrderProduct::class)->with('product');
    }
    public function orderProductsGrouped()
    {
        return $this->hasMany(OrderProduct::class);
    }
    public function subOrder()
    {
        return $this->hasMany(SubOrder::class)->orderby('id', 'desc');
    }

    public function subOrderTypeCreditLine()
    {
        return $this->subOrder()->where('payment_type', 'credit_line');
    }


    public function shippingCity()
    {
        return $this->belongsTo(City::class, 'shipping_city_id');
    }
    public function shippingState()
    {
        return $this->belongsTo(State::class, 'shipping_state_id');
    }
    public function shippingCountry()
    {
        return $this->belongsTo(Country::class, 'shipping_country_id');
    }

    public function billingCity()
    {
        return $this->belongsTo(City::class, 'billing_city_id');
    }
    public function billingState()
    {
        return $this->belongsTo(State::class, 'billing_state_id');
    }
    public function billingCountry()
    {
        return $this->belongsTo(Country::class, 'billing_country_id');
    }
    public function trancationDetail()
    {
        return $this->belongsTo(Transaction::class, 'id', 'order_id');
    }
    public function getFilteredOrderProductsCountAttribute()
    {
        return $this->orderProducts()
            ->whereHas('subOrder', function ($query) {
                $query->where('payment_type', 'credit_line');
            })
            ->count();
    }
    public function subOrders(): HasMany
    {
        return $this->hasMany(SubOrder::class, 'order_id', 'id');
    }

    public function payoutOrder(): HasOne
    {
        return $this->HasOne(PayoutSubOrder::class, 'order_id');
    }

    public function transaction()
    {
        return $this->HasOne(Transaction::class, 'order_id','id');
    }
}
