<?php

namespace App\Models;

use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role as ModelsRole;
use Illuminate\Database\Eloquent\Relations\BelongsTo;


class Role extends ModelsRole
{
    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo<User, Role>
     */
    use LogsActivityTrait;
   

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
