<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class OrderProductBatch extends Model
{
    use HasFactory;

    protected $table = 'order_products_batch';

    public $timestamps = false;

    protected $fillable = [
        'product_id',
        'order_id',
        'suborder_id',
        'product_batch_id',
        'available_batch_stock',
        'assign_stock',
    ];

    // Define the relationships if needed
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function suborder()
    {
        return $this->belongsTo(Suborder::class);
    }

    public function productBatch()
    {
        return $this->belongsTo(ProductBatch::class);
    }

    public function productBatchInfo()
    {
        return $this->hasOne(ProductBatch::class, 'id', 'product_batch_id');
    }
}
