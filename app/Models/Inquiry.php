<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Inquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'user_id',
        'code',
        'landline_number',
        'email',
        'type',
        'description',
        'subject',
        'read_at'
    ];

    protected $hidden = ['code', 'landline_number'];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
