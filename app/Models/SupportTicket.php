<?php

namespace App\Models;

use App\Mail\SupportTicketCreated;
use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;

class SupportTicket extends Model implements HasMedia
{
    use InteractsWithMedia;
    use HasFactory;
    use LogsActivityTrait;
    use Notifiable;

    public function registerMediaCollections(): void
    {

        $this->addMediaCollection('support-ticket-images')
            ->useDisk(config('filesystems.default'));
    }

    public function messages()
    {
        return $this->hasMany(SupportTicketMessage::class, 'support_ticket_id')->orderBy('created_at', 'desc');
    }
    public function messagesChat()
    {
        return $this->hasMany(SupportTicketMessage::class, 'support_ticket_id')->orderBy('id', 'asc');
    }


    public function latestMessage()
    {
        return $this->hasOne(SupportTicketMessage::class, 'support_ticket_id')->latest();
    }

    public function unreadMessages()
    {
        return $this->hasMany(SupportTicketMessage::class, 'support_ticket_id')->where('is_read', false)->whereNot('from_id', Auth::user()->id);
    }

    public function category()
    {
        return $this->belongsTo(SupportCategory::class, 'category_id');
    }
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function pcDetail()
    {
        return $this->hasOne(PcDetail::class,'user_id', 'receiver_id');
    }

    public function supportCategory()
    {
        return $this->belongsTo(SupportCategory::class, 'category_id');
    }

    public function getUnreadMessages()
    {
        return $this->belongsTo(SupportCategory::class, 'category_id');
    }

    public function getLatestMessage()
    {
        return $this->hasOne(SupportTicketMessage::class)->latest();
    }

    public function getMessages()
    {
        return $this->hasMany(SupportTicketMessage::class);
    }

    public function accountType(): BelongsTo
    {
        return $this->belongsTo(AccountType::class, 'account_type_id');
    }

    public function roles()
    {
        return $this->belongsTo(Role::class, 'model_has_roles', 'model_id', 'role_id');
    }

    public function subOrder()
    {
        return $this->belongsTo(SubOrder::class, 'order_id', 'id');
    }

    public function medias()
    {
        return $this->hasMany(Media::class, 'model_id')->where('model_type','App\Models\SupportTicket');
    }

    // In SupportTicket.php

    public function scopeWithUnreadCount($query)
    {
        return $query->withCount([
            'messages as unread_messages_count' => function ($q) {
                $q->where('is_read', false);
            },
        ]);
    }
}
