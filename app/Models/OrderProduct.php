<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Order;

class OrderProduct extends Model
{
    const STATUSES = [
        'pending' => '1',
        'accepted' => '2',
        'rejected' => '3',
        'delivered' => '4',
        'cancelled' => '5',
    ];
    const STATUS_DETAIL = [
        'pending' => 'Pending',
        'accepted' => 'Accepted',
        'rejected' => 'Rejected',
        'delivered' => 'Delivered',
        'cancelled' => 'Cancelled',
        'in_transit' => 'In-Transit',
        'ready_for_pickup' => 'Ready For Pickup'

    ];
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
    public function product()
    {
        return $this->hasOne(Product::class, 'id', 'product_id')->withTrashed();
    }

    public function subOrder()
    {
        return $this->belongsTo(SubOrder::class, 'sub_order_id');
    }
    public function orderProductBatch()
    {
        return $this->hasOne(OrderProductBatch::class, 'order_product_id', 'id');
    }

    public function orderProductBatches()
    {
        return $this->hasMany(OrderProductBatch::class, 'order_product_id', 'id');
    }
}
