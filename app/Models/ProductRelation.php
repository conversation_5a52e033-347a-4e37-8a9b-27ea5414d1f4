<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Auth;

class ProductRelation extends Model
{
    use SoftDeletes;
    public $table = 'products_relation';

    protected $fillable = [
        'product_id',
        'price_type',
        'user_id',
        'admin_approval',
        'requested_by',
        'quantity_per_unit',
        'unit_id',
        'dosage_foams_id',
        'sku',
        'pc_approval',
    ];

    public function products(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function wareHouse()
    {
        return $this->hasOne(WareHouse::class, 'user_id', 'user_id');
    }

    public function productCommission(): BelongsTo
    {
        return $this->belongsTo(ProductCommission::class, 'id', 'product_relation_id');
    }

    public function userDetails(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    public function pcDetails(): BelongsTo
    {
        return $this->belongsTo(PcDetail::class, 'user_id', 'user_id');
    }
    public function fav(): HasOne
    {
        return $this->hasOne(Favourite::class, 'supplier_id', 'user_id')
            ->where('user_id', Auth::id());
    }

    public function batches()
    {
        return $this->hasMany(ProductBatch::class, 'product_id', 'product_id')
            ->where('user_id', $this->user_id)
            ->where('expiry_date', '>=', now());
    }

    public function foam(): HasOne
    {
        return $this->hasOne(DosageForm::class, 'id', 'dosage_foams_id');
    }

    public function unit(): HasOne
    {
        return $this->hasOne(Unit::class, 'id', 'unit_id');
    }
    public function productRelationPrice(): HasOne
    {
        return $this->HasOne(ProductRelationPrice::class);
    }
    public function productRelationStock(): HasOne
    {
        return $this->HasOne(ProductRelationStock::class);
    }
}
