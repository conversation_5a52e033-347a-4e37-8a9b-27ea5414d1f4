<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ClinicDetail extends Model
{
    public function getMobileNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['mobile_number'] ?? null);
    }

    public function getLandlineNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['landline_number'] ?? null);
    }

    public function getDcMobileNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['dc_mobile_number'] ?? null);
    }

    public function getDcLandlineNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['dc_landline_number'] ?? null);
    }

    public function getAcMobileNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['ac_mobile_number'] ?? null);
    }

    public function getAcLandlineNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['ac_landline_number'] ?? null);
    }

    // public function getCompanyNameAttribute($value)
    // {
    //     // If company_name is not blank, return it as is
    //     if (!empty($value)) {
    //         return $value;
    //     }

    //     // If company_name is blank and company type is "Sole Proprietary", return clinic_name
    //     if ($this->businessName && $this->businessName->name === 'Sole Proprietary') {
    //         return $this->attributes['clinic_name'] ?? null;
    //     }

    //     // Otherwise return the original value (null/empty)
    //     return $value;
    // }

    public function shippingAddress()
    {
        return $this->belongsTo(UserAddress::class, 'shipping_addresses_id');
    }
    public function billingAddress()
    {
        return $this->belongsTo(UserAddress::class, 'billing_addresses_id');
    }
    public function clinicAccountType()
    {
        return $this->belongsTo(ClinicAccountType::class);
    }
    public function businessName()
    {
        return $this->belongsTo(BusinessType::class, 'business_type_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function borangCertificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'borang_certificate')
        ->where('status', 'active');
    }

    public function certificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->whereNot('type', 'borang_certificate')
        ->where('status', 'active');
    }

    public function clinicCredit()
    {
        return $this->hasOne(ClinicCredit::class, 'facility_id');
    }

    public function userAddress()
    {
        return $this->belongsTo(UserAddress::class, 'user_id');
    }

    public function shippingAddresses()
    {
        return $this->hasMany(UserAddress::class, 'user_id', 'user_id')->where('address_type', 'shipping');
    }

    public function mmcCertificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'mmc_certificate')
        ->where('status', 'active');
    }

    public function apcCertificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'apc_certificate')
        ->where('status', 'active');
    }

    public function arcCertificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'arc_certificate')
        ->where('status', 'active');
    }

    public function licenseCertificates(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'poison_license')
        ->where('status', 'active');
    }
    public function otherRelevantDocuments(): HasMany
    {
        return $this->hasMany(ClinicCertificateFile::class, 'user_id', 'user_id')
        ->where('type', 'other_relevant_documents')
        ->where('status', 'active');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

}
