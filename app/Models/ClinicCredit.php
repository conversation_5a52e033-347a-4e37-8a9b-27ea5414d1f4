<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
class ClinicCredit extends Model
{
    use HasFactory;

    protected $table = 'clinic_credit';

    protected $fillable = [
        'facility_id',
        'supplier_id',
        'credit_amount',
    ];

    public function facility()
    {
        return $this->belongsTo(User::class, 'facility_id');
    }

    public function supplier()
    {
        return $this->belongsTo(User::class, 'supplier_id');
    }

    public function clinicDetail()
    {
        return $this->belongsTo(ClinicDetail::class, 'facility_id');
    }

    public function suborder()
    {
        return $this->belongsTo(Suborder::class, 'suborder_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
