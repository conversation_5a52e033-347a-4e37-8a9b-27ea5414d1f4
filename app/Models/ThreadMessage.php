<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;

class ThreadMessage extends Model implements HasMedia
{
    use InteractsWithMedia;
    use HasFactory;


    public function user()
    {
        return $this->belongsTo(User::class, 'from_id', 'id');
    }

    public function ticket()
    {
        return $this->belongsTo(Thread::class, 'thread_id', 'id');
    }
    public function thread()
    {
        return $this->belongsTo(Thread::class);
    }
    public function sender()
    {
        return $this->belongsTo(User::class, 'from_id');
    }
    public function broadcastWith()
    {
        return [
            'id' => $this->id,
            'message' => $this->message,
            'from_id' => $this->from_id,
            'created_at' => $this->created_at,
        ];
    }

    public function registerMediaCollections(): void
    {

        $this->addMediaCollection('thread-chat-images')
            ->useDisk(config('filesystems.default'));
    }



    public function broadcastAs()
    {
        return 'new-message';
    }
}
