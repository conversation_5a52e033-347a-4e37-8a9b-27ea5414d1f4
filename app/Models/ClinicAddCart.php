<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ClinicAddCart extends Model
{
    protected $table = 'clinic_add_carts';

    public function supplier()
    {
        return $this->belongsTo(PcDetail::class, 'supplier_id','user_id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function clinicDetail()
    {
        return $this->belongsTo(ClinicDetail::class, 'user_id','user_id');
    }
}
