<?php

namespace App\Models;

use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class DosageForm extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivityTrait;
    protected $table = "dosage_foams";


    public function products()
    {
        return $this->hasMany(Product::class, 'dosage_foams_id');
    }
}
