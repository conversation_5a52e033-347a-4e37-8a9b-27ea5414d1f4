<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class SubOrder extends Model implements HasMedia
{
    use InteractsWithMedia;
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('invoices')
            ->useDisk('public');
        // ->storingConversionsOnDisk('public');
    }
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }
    public function orderProducts()
    {
        return $this->hasMany(OrderProduct::class, 'sub_order_id', 'id');
    }

    public function pcDetail(): BelongsTo
    {
        return $this->BelongsTo(PcDetail::class, 'user_id', 'user_id');
    }

    public function payoutSubOrder(): HasOne
    {
        return $this->HasOne(PayoutSubOrder::class, 'sub_order_id');
    }
    public function clinicCredit(): BelongsTo
    {
        return $this->belongsTo(ClinicCredit::class);
    }
}
