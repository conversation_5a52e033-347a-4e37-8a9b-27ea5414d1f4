<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class ProductVariant extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'product_id',
        'sku'
    ];

    protected $casts = [
        'product_id' => 'integer'
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'product_variant_attributes')
                    ->withPivot('attribute_value_id')
                    ->withTimestamps();
    }

    public function attributeValues(): BelongsToMany
    {
        return $this->belongsToMany(AttributeValue::class, 'product_variant_attributes')
                    ->withTimestamps();
    }

    public function prices(): HasMany
    {
        return $this->hasMany(ProductRelationPrice::class, 'product_variant_id');
    }

    public function stocks(): HasMany
    {
        return $this->hasMany(ProductRelationStock::class, 'product_variant_id');
    }

    /**
     * Get the full attribute combination for this variant
     */
    public function getAttributeCombinationAttribute(): array
    {
        return $this->attributeValues()
                    ->with('attribute')
                    ->get()
                    ->map(function ($value) {
                        return [
                            'attribute' => $value->attribute->name,
                            'value' => $value->name
                        ];
                    })
                    ->toArray();
    }

    /**
     * Generate a display name for this variant
     */
    public function getDisplayNameAttribute(): string
    {
        $combinations = $this->getAttributeCombinationAttribute();
        
        if (empty($combinations)) {
            return $this->product->name;
        }

        $variantParts = collect($combinations)->map(function ($combo) {
            return $combo['attribute'] . ': ' . $combo['value'];
        });

        return $this->product->name . ' (' . $variantParts->implode(', ') . ')';
    }
} 