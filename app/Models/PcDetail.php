<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;

class PcDetail extends Model
{
    public $table = 'pc_details';

    public function getPhoneNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['phone_number'] ?? null);
    }

    public function getPersonInChargePhoneAttribute()
    {
        return getUnMaskNumber($this->attributes['person_in_charge_phone'] ?? null);
    }


    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function clinicPharmaSuppliers()
    {
        return $this->hasMany(ClinicPharmaSupplier::class, 'pc_id', 'user_id');
    }

    public function userAddress()
    {
        return $this->hasOne(UserAddress::class, 'user_id', 'user_id');
    }    
    public function wareHouse()
    {
        return $this->hasOne(WareHouse::class, 'user_id', 'user_id');
    }

    public function products()
    {
        return $this->hasMany(ProductRelation::class, 'user_id', 'user_id');
    }
    public function favourites()
    {
        return $this->hasMany(Favourite::class, 'supplier_id');
    }
    public function fav(): HasOne
    {
        return $this->hasOne(Favourite::class, 'supplier_id', 'user_id')
            ->where('user_id', Auth::id());
    }

    public function companyType()
    {
        return $this->belongsTo(PcCompanyType::class, 'company_type_id');
    }

    public function ClinicCreditLimit()
    {
        return $this->hasOne(ClinicCreditHistory::class, 'supplier_id', 'user_id')->where('facility_id', Auth::user()->id)->latest();
    }

    public function clinicPharmaSupplier()
    {
        return $this->hasOne(ClinicPharmaSupplier::class, 'pc_id', 'user_id')->where('clinic_id', Auth::user()->id);
    }

    
}
