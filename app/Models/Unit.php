<?php

namespace App\Models;

use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;


class Unit extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivityTrait;
   
    public function products(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
