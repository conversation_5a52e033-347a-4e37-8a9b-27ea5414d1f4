<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\HasMedia;

class SupportTicketMessage extends Model implements HasMedia
{
    use InteractsWithMedia;
    use HasFactory;
    use Notifiable;

    protected $fillable = ['from_id', 'to_id', 'message', 'support_ticket_id', 'file_path'];


    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('support-ticket-images')
            ->useDisk(config('filesystems.default'));
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'from_id', 'id');
    }

    public function ticket()
    {
        return $this->belongsTo(SupportTicket::class, 'support_ticket_id', 'id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'from_id');
    }
}
