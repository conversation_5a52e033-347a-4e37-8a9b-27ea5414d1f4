<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Models\Order;
use App\Models\SubOrder;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'suborder_id',
        'file_name',
        'file_path',
        'mime_type',
        'file_size',
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function subOrder()
    {
        return $this->belongsTo(SubOrder::class, 'suborder_id');
    }
}
