<?php

namespace App\Models;

use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use SoftDeletes;

    /**
     * Cache for relations to avoid duplicate queries
     */
    protected $_cached_relations = [];

    protected function name(): Attribute
    {
        return Attribute::make(
            get: fn($value) => Str::upper($value),
            set: fn($value) => strtolower($value),
        );
    }
    protected $casts = [
        'is_batchh_wise_stock' => 'boolean',
    ];

    // public $with = ['productData', 'category', 'subcategory', 'brand', 'unit', 'generic', 'foam', 'productRelations', 'batches', 'pcInfo', 'fav'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product-images')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif'])
            ->useDisk('public');
    }

    public function relationPrices()
    {
        return $this->hasManyThrough(
            ProductRelationPrice::class,
            ProductRelation::class,
            'product_id',
            'product_relation_id',
            'id',
            'id'
        );
    }

    public function relationStocks()
    {
        return $this->hasManyThrough(
            ProductRelationStock::class,
            ProductRelation::class,
            'product_id',
            'product_relation_id',
            'id',
            'id'
        );
    }

    public function scopePendingApprovals($query)
    {
        $userId = getUser(auth()->user())->id;
        $data = $query
            ->where('status', 'pending')
            ->whereHas('productData', function ($q) use ($userId) {
                $q->where('admin_approval', false)
                    ->where('is_rejected', false)
                    ->where('user_id', $userId);
            });

        return $data;
    }

    public function scopeAllProducts($query)
    {
        $userId = getUser(auth()->user())->id;
        $data = $query
            ->where('status', '!=', 'approved')
            ->whereHas('productData', function ($q) use ($userId) {
                $q->where('admin_approval', true)
                    ->where('user_id', $userId);
            });
        return $data;
    }

    public function scopePendingApprovalsForAdmin($query)
    {
        $data = $query
            ->where('status', '!=', 'approved')
            ->where('status', '!=', 'rejected');
        return $data;
    }

    public function scopeWithoutRejectedForAdmin($query)
    {
        $data = $query->where('status', '!=', 'rejected')->where('status', '!=', 'pending');
        return $data;
    }

    public function scopeApproved($query)
    {
        $user_id = getUser(auth()->user())->id;
        $data = $query
            ->where('status', 'approved')
            ->whereHas('productData', function ($q) use ($user_id) {
                $q->where('admin_approval', true)
                    ->where('is_rejected', false)
                    ->where('user_id', $user_id);
            });

        return $data;
    }

    public function scopeNotAdded($query, $userId)
    {
        return $query->whereNotExists(function ($subquery) use ($userId) {
            $subquery->select(DB::raw(1))
                ->from('products_relation')
                ->whereRaw('products_relation.product_id = products.id')
                ->where('products_relation.user_id', $userId)
                ->whereNull('products_relation.deleted_at'); // Exclude soft-deleted relations
        });
    }

    public function distributors(): BelongsToMany
    {
        return $this->belongsToMany(Distributor::class, 'distributor_product');
    }


    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function categoryForFilter()
    {
        return $this->belongsTo(Category::class, 'category_id')->where('parent_id', '=', null);
    }

    public function subcategory()
    {
        return $this->belongsTo(Category::class, 'sub_category_id');
    }

    public function subcategoryForFilter()
    {
        return $this->belongsTo(Category::class, 'sub_category_id')->whereNotNull('parent_id');
    }
    public function brand(): HasOne
    {
        return $this->hasOne(Brand::class, 'id', 'brand_id');
    }

    public function unit(): HasOne
    {
        return $this->hasOne(Unit::class, 'id', 'unit_id');
    }
    public function generic(): HasOne
    {
        return $this->hasOne(GenericName::class, 'id', 'generic_name_id');
    }

    public function productData(): HasOne
    {
        return $this->hasOne(ProductRelation::class, 'product_id', 'id');
    }

    public function productRelations(): HasMany
    {
        return $this->hasMany(ProductRelation::class, 'product_id', 'id');
    }

    public function container(): HasOne
    {
        return $this->hasOne(Container::class, 'id', localKey: 'container_id');
    }

    public function productDataForPc(int $userId)
    {
        return $this->hasOne(ProductRelation::class, 'product_id', 'id')
        ->where('user_id', $userId)->first();

    }

    public function batches(): HasMany
    {
        return $this->hasMany(ProductBatch::class, 'product_id', 'id');
    }

    public function pcInfo(): HasMany
    {
        return $this->hasMany(ProductRelation::class, 'product_id', 'id');
    }

    public function fav(): HasOne
    {
        return $this->hasOne(Favourite::class, 'product_id', 'id')
            ->where('user_id', auth()->id());
    }

    public function foam(): HasOne
    {
        return $this->hasOne(DosageForm::class, 'id', 'dosage_foams_id');
    }

    public function cart()
    {
        return $this->hasMany(ClinicAddCart::class, 'product_id');
    }

    public function scopeBatchWiseStock($query, $ids)
    {
        $data = $query->whereIn('id', $ids)
            ->whereHas('batches', function ($query) {
                return $query->whereColumn('available_stock', '<', 'low_stock_trigger_value');
            })->with('batches');


        return $data;
    }

    // Define a search scope
    public function scopeSearch(Builder $query, $keyword)
    {
        return $query->where('name', 'ILIKE', "%$keyword%")
            ->orWhereHas('generic', function (Builder $query) use ($keyword) {
                $query->where('name', 'ILIKE', "%$keyword%");
            })
            ->orWhereHas('category', function (Builder $query) use ($keyword) {
                $query->where('name', 'ILIKE', "%$keyword%");
            })
            ->orWhereHas('subcategory', function (Builder $query) use ($keyword) {
                $query->where('name', 'ILIKE', "%$keyword%");
            })
            ->orWhereHas('brand', function (Builder $query) use ($keyword) {
                $query->where('name', 'ILIKE', "%$keyword%");
            })
            ->orWhereHas('pcInfo', function (Builder $query) use ($keyword) {
                $query->whereHas('userDetails.pcDetails', function ($q2) use ($keyword) {
                    $q2->where('business_name', 'ILIKE', "%$keyword%")
                        ->whereNull('deleted_at');
                });
            });
    }

    // Attribute and Variant relationships
    public function productAttributes(): HasMany
    {
        return $this->hasMany(ProductAttribute::class);
    }

    public function productVariants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    public function attributes(): BelongsToMany
    {
        return $this->belongsToMany(Attribute::class, 'product_attributes')
                    ->withPivot('attribute_value_id')
                    ->withTimestamps();
    }

    public function attributeValues(): BelongsToMany
    {
        return $this->belongsToMany(AttributeValue::class, 'product_attributes')
                    ->withTimestamps();
    }

    public function medias()
    {
        return $this->hasMany(Media::class, 'model_id');
    }

    public function defaultImage()
    {
        return $this->belongsTo(Media::class, 'default_image_id');
    }

    public function getResolvedDefaultImageAttribute()
    {
        if ($this->defaultImage) {
            return $this->defaultImage;
        }

        return Media::where('model_id', $this->id)->orderBy('id')->first();
    }
}
