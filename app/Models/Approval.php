<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Approval extends Model
{
    public $guared = [];


    public function scopePending($query)
    {
        return $query->whereNull(['approved_by', 'approved_at', 'rejected_by', 'rejected_at']);
    }

    public function scopePendingForSteps($query, $step)
    {
        return $this->pending()
            ->where('steps', $step);
    }

    public function reject($userId, $steps)
    {
        $this->where('approvalable_id', $this->approvalable_id)->where('steps', $steps)
        ->update([
            'rejected_at' => now(),
            'rejected_by' => $userId,
        ]);

    }

    public function approve($userId, $steps)
    {
        $this->where('approvalable_id', $this->approvalable_id)->where('steps', $steps)
            ->update([
                'approved_at' => now(),
                'approved_by' => $userId,
            ]);

    }
}
