<?php

namespace App\Models;

use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Category extends Model
{
    use HasFactory;
    use SoftDeletes;
    use LogsActivityTrait;

    public $timestamps = true;
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id')
                    ->where('status', true) 
                    ->orderBy('serial_number', 'asc')
                    ->orderBy('name', 'asc') ;
    }

    public function categoryAccountTypes()
    {
        return $this->belongsToMany(ClinicAccountType::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class, 'category_id');
    }
    public function subCategoryProducts()
    {
        return $this->hasMany(Product::class, 'sub_category_id');
    }
}
