<?php

namespace App\Models;

use App\Notifications\NewMessageNotification;
use App\Traits\LogsActivityTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Notifications\Notifiable;

class Thread extends Model
{
    use HasFactory;
    use Notifiable;
    use LogsActivityTrait;
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }


    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }

    public function pcDetail()
    {
        return $this->hasOne(PcDetail::class,'user_id', 'receiver_id');
    }

    public function latestMessage()
    {
    return $this->hasOne(ThreadMessage::class, 'thread_id')->latest('created_at');
    }   

    public function getMessages()
    {
        return $this->hasMany(ThreadMessage::class, 'thread_id')->orderBy('created_at', 'desc');
    }

    public function getLatestMessage()
    {
        return $this->hasOne(ThreadMessage::class)->latest();
    }

    public function unreadMessages()
    {
        return $this->hasMany(ThreadMessage::class, 'thread_id')
            ->where('is_read', false)
            ->whereNot('from_id', Auth::user()->id);
    }

    public function messages()
    {
        return $this->hasMany(ThreadMessage::class, 'thread_id')->orderBy('created_at', 'desc');
    }

    public function subOrder()
    {
        return $this->belongsTo(SubOrder::class, 'order_id', 'id');
    }
}
