<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductRelationPrice extends Model
{
    use HasFactory;
    public $table = 'product_relation_prices';

    protected $fillable = [
        'product_relation_id',
        'product_attribute_id',
        'product_variant_id',
        'east_zone_price',
        'west_zone_price',
        // Tier pricing fields
        'east_tier_1_base_price',
        'west_tier_1_base_price',
        'east_tier_2_base_price',
        'west_tier_2_base_price',
        'east_tier_3_base_price',
        'west_tier_3_base_price',
        'east_tier_1_min_quantity',
        'west_tier_1_min_quantity',
        'east_tier_2_min_quantity',
        'west_tier_2_min_quantity',
        'east_tier_3_min_quantity',
        'west_tier_3_min_quantity',
        'east_tier_1_max_quantity',
        'west_tier_1_max_quantity',
        'east_tier_2_max_quantity',
        'west_tier_2_max_quantity',
        'east_tier_3_max_quantity',
        'west_tier_3_max_quantity',
        // Bonus pricing fields
        'east_bonus_1_quantity',
        'east_bonus_1_quantity_value',
        'east_bonus_2_quantity',
        'east_bonus_2_quantity_value',
        'east_bonus_3_quantity',
        'east_bonus_3_quantity_value',
        'west_bonus_1_quantity',
        'west_bonus_1_quantity_value',
        'west_bonus_2_quantity',
        'west_bonus_2_quantity_value',
        'west_bonus_3_quantity',
        'west_bonus_3_quantity_value',
        'east_bonus_1_base_price',
        'east_bonus_2_base_price',
        'east_bonus_3_base_price',
        'west_bonus_1_base_price',
        'west_bonus_2_base_price',
        'west_bonus_3_base_price',
    ];

    protected $casts = [
        'product_relation_id' => 'integer',
        'product_attribute_id' => 'integer',
        'product_variant_id' => 'integer',
        'east_zone_price' => 'decimal:2',
        'west_zone_price' => 'decimal:2',
    ];

    public function productRelation(): BelongsTo
    {
        return $this->belongsTo(ProductRelation::class, 'product_relation_id');
    }

    public function productAttribute(): BelongsTo
    {
        return $this->belongsTo(ProductAttribute::class, 'product_attribute_id');
    }

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id');
    }
}
