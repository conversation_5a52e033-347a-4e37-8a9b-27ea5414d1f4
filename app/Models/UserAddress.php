<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Nnjeim\World\Models\City;
use Nnjeim\World\Models\Country;
use Nnjeim\World\Models\State;

class UserAddress extends Model
{
   
    public function getPhoneNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['phone_number'] ?? null);
    }

    public function getLandlineNumberAttribute()
    {
        return getUnMaskNumber($this->attributes['landline_number'] ?? null);
    }
    public function country()
    {
        return $this->belongsTo(Country::class);
    }
    public function state()
    {
        return $this->belongsTo(State::class);
    }
    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function clinicDetail()
    {
        return $this->hasOne(ClinicDetail::class, 'user_id', 'user_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function clinicDetails()
    {
        return $this->hasMany(ClinicDetail::class, 'billing_addresses_id');
    }

}
