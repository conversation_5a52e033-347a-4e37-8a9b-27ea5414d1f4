<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ClinicPharmaSupplier extends Model
{

    const STATUS_APPROVED = 'approved';
    const STATUS_PENDING = 'pending';
    protected $fillable = [
        'pc_id',
        'clinic_id',
        'account_number',
        'status',
        'approved_by',
        'reject_by',
        'is_open_account',
        'reject_reason'
    ];

    public function pcDetail(): BelongsTo
    {
        return $this->belongsTo(User::class, 'pc_id', 'id');
    }
    public function pcInfo(): BelongsTo
    {
        return $this->belongsTo(PcDetail::class, 'pc_id', 'user_id');
    }

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function clinicDetail(): BelongsTo
    {
        return $this->belongsTo(User::class, 'clinic_id', 'id');
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(PcDetail::class, 'approved_by', 'user_id');
    }
}
