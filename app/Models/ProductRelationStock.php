<?php

declare(strict_types=1);

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;

class ProductRelationStock extends Model
{
    use HasFactory;
    public $table = 'product_relation_stocks';

    protected $fillable = [
        'product_relation_id',
        'product_attribute_id',
        'product_variant_id',
        'is_batch_wise_stock',
        'stock',
        'total_stock',
        'low_stock',
        'weight',
        'expiry_date',
        'wholesale_pack_size',
        'stock_type',
    ];

    protected $casts = [
        'product_relation_id' => 'integer',
        'product_attribute_id' => 'integer',
        'product_variant_id' => 'integer',
        'is_batch_wise_stock' => 'boolean',
        'stock' => 'integer',
        'total_stock' => 'integer',
        'low_stock' => 'integer',
        'weight' => 'integer',
        'expiry_date' => 'datetime',
    ];

    protected function expiryDate(): Attribute
    {
        return Attribute::make(
            set: fn($value) => $value ? Carbon::parse($value)->setTime(23, 59, 0) : null,
        );
    }

    public function productRelation(): BelongsTo
    {
        return $this->belongsTo(ProductRelation::class, 'product_relation_id');
    }

    public function productAttribute(): BelongsTo
    {
        return $this->belongsTo(ProductAttribute::class, 'product_attribute_id');
    }

    public function productVariant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'product_variant_id');
    }

    public function productsBatch(): HasMany
    {
        return $this->hasMany(ProductBatch::class, 'products_relation_id', 'product_relation_id');
    }
}
