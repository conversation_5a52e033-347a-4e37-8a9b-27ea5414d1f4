<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Payout extends Model
{
    use HasFactory;
    protected $appends = ['calculated_amount'];
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    public function payoutSubOrders(): HasMany
    {
        return $this->HasMany(PayoutSubOrder::class);
    }
    public function pcDetail(): BelongsTo
    {
        return $this->BelongsTo(PcDetail::class, 'user_id', 'user_id');
    }

    // In your model
    public function getCalculatedAmountAttribute()
    {
        return $this->payoutSubOrders->sum(function ($payoutSubOrder) {
            $subOrder = $payoutSubOrder->subOrder;
            if ($subOrder->payment_type != 'credit_line') {
                if ($subOrder->payout_type === 'schedule') {
                    $commision = $subOrder->orderProducts->sum('total_commission');
                    return ($subOrder->total_sub_order_value ?? 0) - ($commision ?? 0);
                } else if ($subOrder->payout_type === 'full') {
                    return $subOrder->total_sub_order_value ?? 0;
                }
            }
            return 0;
        });
    }

    // In Payout model
    public function orders()
    {
        return $this->hasManyThrough(
            Order::class,
            PayoutSubOrder::class,
            'payout_id', // Foreign key on payout_sub_orders table
            'id',        // Foreign key on orders table (assuming)
            'id',        // Local key on payouts
            'order_id'   // Local key on payout_sub_orders
        );
    }

}
