<?php

namespace App\Enums;

enum LabelEnum: string
{
    case PRODUCT_NAME = 'Product Name';
    case PRODUCT_ID = 'Product Id';
    case CATEGORY_NAME = 'Category';
    case SUB_CATEGORY_NAME = 'Sub Category';
    case STATUS = 'Status';
    case ADDED_FROM = 'Added From';
    case GENERIC_NAME = 'Generic Name';
    case SELLERS = 'Sellers';
    case TABLE_ACTION_LABEL = 'Action';
    case EMAIL = 'Email';
    case PASSWORD = 'Password';
    case CONFIRM_PASSWORD = 'Confirm Password';
    case OLD_PASSWORD = 'Old Password';
    case NEW_PASSWORD = 'New Password';
    case CONFIRM_NEW_PASSWORD = 'Confirm New Password';
    case ROLE = 'Role';
    case CREATED_AT = 'Created At';
    case CREATED_AT_ON = 'Created On';
    case CREATED_FROM = 'From';
    case CREATED_UNTIL = 'Until';
    case EDIT = 'Edit';
    case VIEW = 'View';
    case DELETE = 'Delete';
    case APPROVE = 'Approve';
    case REJECT = 'Reject';
    case REASON = 'Reason';
}
