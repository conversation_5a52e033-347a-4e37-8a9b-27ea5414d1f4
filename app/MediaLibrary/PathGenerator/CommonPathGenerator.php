<?php

namespace App\MediaLibrary\PathGenerator;

use App\Models\SupportTicket;
use <PERSON><PERSON>\MediaLibrary\Support\PathGenerator\PathGenerator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CommonPathGenerator implements PathGenerator
{
    public function getPath(Media $media): string
    {
        if ($media->model_type === SupportTicket::class) {
            return config('constants.api.media.support_ticket') . $media->model_id . '/';
        }

        // fallback for other models
        return '/' . $media->id . '/';
    }

    public function getPathForConversions(Media $media): string
    {
        return $this->getPath($media) . 'conversions/';
    }

    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getPath($media) . 'responsive/';
    }
}
