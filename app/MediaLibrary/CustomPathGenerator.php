<?php
namespace App\MediaLibrary;

use <PERSON><PERSON>\MediaLibrary\Support\PathGenerator\PathGenerator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CustomPathGenerator implements PathGenerator
{
    public function getPath(Media $media): string
    {
        $model = $media->model;

        if ($model instanceof \App\Models\ThreadMessage) {
            // Custom path for thread messages
            return config('constants.api.media.thread') . $media->model->id . '/';
        }

        if ($model instanceof \App\Models\SupportTicketMessage || $model instanceof \App\Models\SupportTicket) {
            // Custom path for another module
            return config('constants.api.media.support_ticket') . $media->model->id . '/';
        }
    }

    public function getPathForConversions(Media $media): string
    {
        return $this->getPath($media) . 'conversions/';
    }

    public function getPathForResponsiveImages(Media $media): string
    {
        return $this->getPath($media) . 'responsive/';
    }
}
