<?php

use App\Http\Middleware\SetLocale;
use Illuminate\Foundation\Application;
use App\Http\Middleware\TimezoneMiddleware;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Session\TokenMismatchException;
use App\Http\Middleware\CheckAdminVerification;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Log;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        api: __DIR__ . '/../routes/api.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->trustProxies(at: '*');
        $middleware->validateCsrfTokens(except: [
            'livewire/upload-file',
        ]);
        $middleware->alias([
            'is_admin_verified' => CheckAdminVerification::class,
            'set.locale' => SetLocale::class,
            'set.timezone' => TimezoneMiddleware::class,
        ]);
    })
    ->withSchedule(function (Schedule $schedule) {
        $schedule->command('import-manager')->everyMinute();
        $schedule->command('payment-retry-mail')->everyFiveMinutes();
        // $schedule->command('update:order-stripe-webhook')->everyMinute();
        $schedule->command('orderInvoice:generate')->hourly();
        $schedule->command('refund-order-process')->dailyAt('00:00');
        $schedule->command('activitylogs:clean')->daily();
        $schedule->command('payout:generate')
            ->daily()
            ->when(function () {
                $day = now()->day;
                return in_array($day, [1, 15]);
            });
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // $exceptions->reportable(function (Throwable $e) {
        //     throw new \Symfony\Component\HttpKernel\Exception\HttpException(500, $e->getMessage());
        // });
        // $exceptions->renderable(function (\Exception $e, $request) {
        //     if ($e instanceof \Symfony\Component\HttpKernel\Exception\HttpException && $e->getStatusCode() === 500) {
        //         return response()->view('errors.500', [], 500);
        //     }
        // });
        $exceptions->renderable(function (\Illuminate\Session\TokenMismatchException $e, $request) {
            \Illuminate\Support\Facades\Log::warning('CSRF token mismatch', [
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'message' => $e->getMessage(),
                'user_id' => optional($request->user())->id,
                'user_agent' => $request->userAgent(),
            ]);
        });
    })->create();
