<?php

declare(strict_types=1);

try {
    require_once __DIR__ . '/../vendor/autoload.php';
    require_once __DIR__ . '/app.php';

    // Preload Filament files
    $filamentPath = __DIR__ . '/../vendor/filament';
    if (is_dir($filamentPath)) {
        $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($filamentPath));
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                try {
                    opcache_compile_file($file->getRealPath());
                } catch (Throwable $e) {
                    // Ignore compilation errors
                }
            }
        }
    }
} catch (Throwable $e) {
    // Log error if needed
    error_log('OPcache preload error: ' . $e->getMessage());
}
