<?php

namespace Solutionforest\FilamentEmail2fa\Middlewares;

use Closure;
use Exception;
use Illuminate\Http\Request;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Solutionforest\FilamentEmail2fa\Pages\TwoFactorAuth;
use Solutionforest\FilamentEmail2fa\Interfaces\RequireTwoFALogin;

class IsTwoFAVerified
{
    public function handle(Request $request, Closure $next)
    {
        $user = Filament::auth()->user();
        
        try {
            $routeName = $request->route()->getName();
        } catch (Exception $e) {
            $routeName = null;
        }
        
        // Check if user has remember me enabled and should bypass 2FA
        if ($user && cache()->get('remember_me_' . $user->id) && $user->remember_me == true) {
            return $next($request);
        }

        if ($user == null || $routeName == TwoFactorAuth::getRouteName() || $routeName == Filament::getCurrentPanel()->generateRouteName('auth.logout')) {
            return $next($request);
        }

        if ($user instanceof RequireTwoFALogin && $user->isTwoFaVerfied($request->session()->getId())) {
            
            return $next($request);

        }

        return redirect(route(TwoFactorAuth::getRouteName()));
    }
}
