<?php

namespace Solutionforest\FilamentEmail2fa\Responses;

use App\Models\User;
use Filament\Facades\Filament;
use Illuminate\Support\Facades\Auth;
use App\Filament\Pc\Pages\EditProfile;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Redirect;
use App\Filament\Pc\Pages\UnderVerificationProfile;
use Solutionforest\FilamentEmail2fa\Pages\LoginSuccessPage;
use Filament\Http\Responses\Auth\Contracts\LoginResponse as LoginResponseContract;

class LoginSuccessResponse implements LoginResponseContract
{
    /**
      * Create an HTTP response that represents the object.
      *
      * @param  \Illuminate\Http\Request  $request
      * @return \Symfony\Component\HttpFoundation\Response
      */
    public function toResponse($request)
    {
        // return whatever you want as url
        $user = Filament::auth()->user();
        if (!empty($user->parent_id)) {
            $user = User::find($user->parent_id);
        }
        cache()->forget('remember_me_first_time_login_' . $user->id);
        $currentPanelId = Filament::getCurrentPanel()->getId();
        if ($currentPanelId == 'pc' && $user->hasRole('Pharmaceutical Company')) {
            if ($user->is_admin_verified == true && $user->pcDetails->step == 5) {
                Notification::make()
                ->title(getTimeBasedGreeting() . ", " . $user->name . "!")
                ->body("Glad to have you on Dpharma. Let’s make your health a priority.")
                ->success()
                ->send();
                return redirect()->intended(route('filament.pc.pages.dashboard'));
               
            } elseif ($user->is_admin_verified == false && empty($user->rejection_reason) && $user->pcDetails?->step < 5) {
                return to_route(EditProfile::getRouteName());
            } elseif ($user->is_admin_verified == false && empty($user->rejection_reason) && $user->pcDetails?->is_submitted == true) {
                return to_route(UnderVerificationProfile::getRouteName());
            } else {
                Notification::make()
                ->title(getTimeBasedGreeting() . ", " . $user->name . "!")
                ->body("Glad to have you on Dpharma. Let’s make your health a priority.")
                ->success()
                ->send();
                return redirect()->intended(route('filament.pc.pages.dashboard'));
            }
        }

        if ($currentPanelId == 'admin' && $user->hasRole('Super Admin')) {
            $intendedUrl = cache()->get('url.intended_user_id_' . Auth::id());
            if($intendedUrl) {
                cache()->forget('url.intended_user_id_' . Auth::id());
                return redirect()->to($intendedUrl);
            }
            return redirect()->intended(route('filament.admin.pages.dashboard'));
        }
        Filament::auth()->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        Notification::make()->title("You are not authorized to access this $currentPanelId panel !")->danger()->send();

        return Redirect::to(Filament::getCurrentPanel()->getLoginUrl());
    }
}
