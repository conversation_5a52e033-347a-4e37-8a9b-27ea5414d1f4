<?php

namespace Solutionforest\FilamentEmail2fa\Pages;

use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Facades\Auth;
use Filament\Support\Enums\Alignment;
use Filament\Forms\Contracts\HasForms;
use Illuminate\Support\Facades\Session;
use App\Services\ExtendedSessionService;
use Filament\Notifications\Notification;
use Illuminate\Contracts\Support\Htmlable;
use Filament\Forms\Concerns\InteractsWithForms;
use Hasan<PERSON><PERSON>\FilamentOtpInput\Components\OtpInput;
use Solutionforest\FilamentEmail2fa\Interfaces\RequireTwoFALogin;
use Solutionforest\FilamentEmail2fa\Responses\LoginSuccessResponse;
use Solutionforest\FilamentEmail2fa\Exceptions\InvalidTwoFACodeException;

/**
 * @property Form $form
 */
class TwoFactorAuth extends Page implements HasForms
{
    use InteractsWithForms;

    protected static bool $shouldRegisterNavigation = false;

    protected static string $layout = 'filament-email-2fa::simple-layout';

    protected static string $view = 'filament-email-2fa::email-sent';

    public ?array $data = [
        'fa_code' => '',
    ];

    public string $email;

    // public string $fa_code;

    public static function getLabel(): string
    {
        return config('app.name');
    }

    public static function getRelativeRouteName(): string
    {
        return 'sf-filament-2fa.2fa';
    }

    public function mount()
    {
        if (! Filament::auth()->user() instanceof RequireTwoFALogin) {
            return redirect(Filament::getUrl());
        }
        $this->email = Filament::auth()->user()->email;
    }


    public function resAction()
    {
        return Action::make('resend')
            ->link()
            ->color('gray-200')
            ->label('Resend OTP')
            ->action('resend')
            ->extraAttributes([
                'class' => 'btn-d-abled cl_text',
                'id' => 'resend_otp',
            ])
            ->keyBindings(['mod+s']);
    }

    public function resend()
    {
        try {
            $this->resetErrorBag();
            $this->data['fa_code'] = '';
            $this->form->fill(['fa_code' => '']);

            // Get the user and send the email
            $user = $this->getUser();
            if (!$user) {
                Notification::make()
                    ->title('Error')
                    ->body('Could not find user to send OTP.')
                    ->danger()
                    ->send();
                return null;
            }

            // Send the email
            $user->send2FAEmail();

            // Set session flash message
            // session()->flash('resent-success', __('filament-email-2fa::filament-email-2fa.resend_success'));

            // Calculate new expiry time
            $expiryMinutes = config('filament-email-2fa.expiry_time_by_mins', 5);
            $expiryTime = now()->addMinutes($expiryMinutes);
            session(['otp_expiry_time' => $expiryTime]);

            // Dispatch events for different Livewire versions
            $this->dispatch('otp-resent');

            if (method_exists($this, 'dispatchBrowserEvent')) {
                $this->dispatchBrowserEvent('otp-resent');
            }

            if (method_exists($this, 'dispatchTo')) {
                $this->dispatchTo('*', 'otp-resent');
            }

            // Dispatch timer reset event
            $this->dispatch('reset-otp-timer', [
                'expiryTime' => $expiryTime->timestamp * 1000 // Convert to milliseconds for JavaScript
            ]);

            // Show success notification
            Notification::make()
                ->title('OTP Sent')
                ->body('A new OTP has been sent to your email.')
                ->success()
                ->send();
        } catch (\Exception $e) {
            // Log any errors
            /** @phpstan-ignore-next-line */
            info('Error in resend OTP process: ' . $e->getMessage());

            // Show error notification
            Notification::make()
                ->title('Error')
                ->body('Failed to send OTP. Please try again.')
                ->danger()
                ->send();
        }

        return null;
    }
    public function logout()
    {
        Filament::auth()->logout();

        session()->invalidate();
        session()->regenerateToken();

        return redirect()->to(
            Filament::hasLogin() ? Filament::getLoginUrl() : Filament::getUrl(),
        );
    }

    // public function resAction()
    // {
    //     return Action::make('resend')
    //         ->link()
    //         ->color('gray-200')
    //         ->label('Resend OTP')
    //         ->action('resend')
    //         ->extraAttributes([
    //             'class' => 'btn-d-abled cl_text',
    //             'id' => 'resend_otp',
    //         ])
    //         ->keyBindings(['mod+s']);
    // }
    public function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Verify')
                ->action('save')
                ->extraAttributes([
                    'class' => 'w-full'
                ])
                ->keyBindings(['mod+s']),
            Action::make('resend_otp')
                ->label('Resend OTP')
                ->action(function () {
                    // Reset error bag
                    $this->resetErrorBag();

                    // Reset the OTP input field
                    $this->data['fa_code'] = '';
                    $this->form->fill(['fa_code' => '']);

                    if ($user = $this->getUser()) {
                        $user->send2FAEmail();
                        session()->flash('resent-success', __('filament-email-2fa::filament-email-2fa.resend_success'));

                        // Reset any expiry time in the session
                        $expiryMinutes = config('filament-email-2fa.expiry_time_by_mins', 5);
                        $expiryTime = now()->addMinutes($expiryMinutes);
                        session(['otp_expiry_time' => $expiryTime]);

                        // Also dispatch a JavaScript event to reset the timer
                        $this->dispatch('reset-otp-timer', [
                            'expiryTime' => $expiryTime->timestamp * 1000 // Convert to milliseconds for JavaScript
                        ]);

                        // Dispatch events
                        $this->dispatch('otp-resent');

                        // Show notification
                        Notification::make()
                            ->title('OTP Sent')
                            ->body('A new OTP has been sent to your email.')
                            ->success()
                            ->send();
                    }
                })
                ->color('secondary')
                ->extraAttributes([
                    'id' => 'resend_otp',
                    'class' => 'hidden'
                ]),
        ];
    }

    public function loginAction()
    {
        return Action::make('login')
            ->label('Login')
            ->extraAttributes(['id' => 'back_to_login'])
            ->link()
            ->action(function () {
                Auth::logout();
                Session::flush();
                return redirect()->to(Filament::getCurrentPanel()->getLoginUrl());
            });
    }

    public function save()
    {
        $this->form->getState();
        $code = $this->data['fa_code'] ?? null;
        // dd($code);
        // if(app()->environment('local') || app()->environment('develop'))
        // {
        //     $code = '0000';
        // }

        try {
            if ($user = $this->getUser()) {
                $user->verify2FACode($code ?? '');
                $user->twoFaVerifis()->create([
                    'session_id' => request()->session()->getId(),
                ]);

                // Set remember me cache after successful 2FA verification
                if ($user->remember_me) {
                    cache()->put('remember_me_' . $user->id, true, ExtendedSessionService::daysToMinutes(15));
                    // Clear first-time login flag so future logins can bypass 2FA
                    cache()->forget('remember_me_first_time_login_' . $user->id);
                }

                if ($user->is_temp_password ) {
                    return redirect()->route('set-password',['id'=>encryptParam(auth()->user()->id)]);
                }

                return app(LoginSuccessResponse::class);
            } else {
                throw new InvalidTwoFACodeException;
            }
        } catch (InvalidTwoFACodeException) {
            // $this->addError('data.fa_code', $e->getMessage());
            // Notification::make()
            //     ->title('Invalid Code')
            //     ->body('Invalid code. Please try again.')
            //     ->danger()
            //     ->send();
            return;
        }
    }

    public function getUser()
    {
        $guard = $this->getCurrentGuard();
        $model = config("auth.providers.{$guard}.model");
        $user = $model::where('email', $this->email)->first();

        return $user;
    }

    public function getCurrentGuard()
    {
        return Filament::getCurrentPanel()->getAuthGuard();
    }

    public function form(Form $form): Form
    {
        return $form;
    }

    /**
     * @return array<int | string, string | Form>
     */
    protected function getForms(): array
    {
        return [
            'form' => $this->form(
                $this->makeForm()
                    ->schema([
                        OtpInput::make('fa_code')
                            ->label('')
                            ->numberInput(4)
                            ->rules(['required', 'min:4', 'max:4'])

                            ->validationMessages([
                                'required' => 'OTP is required/OTP must be of 4 characters',
                                'min' => 'OTP must be 4 characters',
                                'max' => 'OTP must be 4 characters',
                            ])
                            ->extraAttributes([
                                'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 4);",
                                'onkeydown' => "if (['e', 'E', '+', '-'].includes(event.key)) event.preventDefault();",
                                'class' => 'otp-input'
                            ])
                    ])
                    ->statePath('data'),
            ),
        ];
    }

    public function hasFullWidthFormActions(): bool
    {
        return false;
    }

    public function getFormActionsAlignment(): string|Alignment
    {
        return Alignment::End;
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('<div>Verify Your Account</div>');
    }

    public function hasLogo(): bool
    {
        return true;
    }
}
