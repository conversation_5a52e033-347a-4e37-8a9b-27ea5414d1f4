<?php

namespace Solutionforest\FilamentEmail2fa\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Support\Facades\DB;
use Illuminate\Queue\SerializesModels;

class TwoFAEmail extends Mailable implements \Illuminate\Contracts\Queue\ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public ?string $name = '';

    public string $code;

    public string $htmlBody;

    public string $emailSubject;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(?string $name = null, string $code)
    {
        $this->name = $name;
        $this->code = $code;
        $template = DB::table('email_templates')->where('key', 'TWO_FA_CODE_LOGIN')->first();

        $this->emailSubject = $template->subject ?? '2 FA Code';

        $this->htmlBody = $this->parseTemplate($template->email_content, [
            'name' => $this->name,
            'code' => $this->code,
        ]);
    }

    private function parseTemplate(string $template, array $data): string
    {
        foreach ($data as $key => $value) {
            $template = str_replace('{{ ' . $key . ' }}', $value, $template);
        }
        return $template;
    }

    /**
     * Build the message.
     *
     * @return $this
     */

     public function build()
     {
        return $this->subject($this->emailSubject)
        ->view('emails.common_mail')
        ->with([
            'html' => $this->htmlBody,
            'logo' => asset('images/logo.png')
        ]);
     }
    // public function build()
    // {
    //     return $this->subject($this->emailSubject)
    //         ->html($this->htmlBody);
    // }
}
