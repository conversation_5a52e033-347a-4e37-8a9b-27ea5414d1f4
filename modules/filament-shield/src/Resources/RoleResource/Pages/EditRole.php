<?php

namespace Indianic\FilamentShield\Resources\RoleResource\Pages;

use Indianic\FilamentShield\Resources\RoleResource;
use Indianic\FilamentShield\Support\Utils;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class EditRole extends EditRecord
{
    protected static string $resource = RoleResource::class;

    public Collection $permissions;


    protected function getActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url('/shield/roles')
                ->color('gray'),
        ];
    }


    public function getBreadcrumbs(): array
    {
        return [
            // Dashboard::getUrl() => "Master",
            $this->getResource()::getUrl('index') => 'Roles',
            //Rolename view 

            2 => "Edit",
        ];
    }
    public function getTitle(): string
    {
        return 'Role: ' . preg_replace('/^\d+-/', '', $this->record->name);
    }

    protected function getRedirectUrl(): string
    {
        return static::getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', Utils::getTenantModelForeignKey()]);
            })
            ->values()
            ->flatten()
            ->unique();
        if (Arr::has($data, Utils::getTenantModelForeignKey())) {
            return Arr::only($data, ['name', 'guard_name', Utils::getTenantModelForeignKey()]);
        }

        return Arr::only($data, ['name', 'guard_name', 'display_name']);
    }

    protected function afterSave(): void
    {
        $permissionModels = collect();

        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title(__('Role Updated'))
            ->body(__('Role has been successfully updated'));
    }
}
