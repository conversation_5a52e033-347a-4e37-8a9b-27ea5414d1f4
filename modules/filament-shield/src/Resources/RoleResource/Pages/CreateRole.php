<?php

namespace Indianic\FilamentShield\Resources\RoleResource\Pages;

use Filament\Actions\Action;
use Illuminate\Support\Arr;
use Filament\Facades\Filament;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Filament\Resources\Pages\CreateRecord;
use Indianic\FilamentShield\Support\Utils;
use Illuminate\Validation\ValidationException;
use Indianic\FilamentShield\Resources\RoleResource;

class CreateRole extends CreateRecord
{
    protected static string $resource = RoleResource::class;

    public Collection $permissions;
    public function getHeaderActions(): array
    {
        return [
            Action::make('back')
                ->label('Back')
                ->url('/shield/roles')
                ->color('gray'),
        ];
    }

    protected function getFormActions(): array
    {
        return [
            $this->getCreateFormAction()->label('Add Role'),
            // ...(static::canCreateAnother() ? [$this->getCreateAnotherFormAction()] : []),
            $this->getCancelFormAction(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return static::getResource()::getUrl('index');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // dd($data);                                                                               
        $selectedPermissions = collect($data)
            ->filter(fn($value, $key) => str_starts_with($key, 'permissions_') && !empty($value))
            ->flatten();
        if (count($selectedPermissions) < 1) {
            Notification::make()
                ->title('At least one permission must be selected.')
                ->danger()
                ->send();
            throw ValidationException::withMessages([
                'permissions' => 'At least one permission must be selected.',
            ]);
        }
        $this->permissions = collect($data)
            ->filter(function ($permission, $key) {
                return ! in_array($key, ['name', 'guard_name', 'select_all', Utils::getTenantModelForeignKey()]);
            })
            ->values()
            ->flatten()
            ->unique();

        if (Arr::has($data, Utils::getTenantModelForeignKey())) {
            return Arr::only($data, ['name', 'guard_name', Utils::getTenantModelForeignKey()]);
        }

        $data['created_by'] = null;
        $data['panel'] = Filament::getCurrentPanel()->getId();
        $data['created_by'] = Auth::user()->id;
        if (Filament::getCurrentPanel()->getId() != 'admin') {

            $data['panel'] = Filament::getCurrentPanel()->getId();
            $data['name'] = getUser(auth()->user())->id . "-{$data['display_name']}";
            // $data['display_name'] = $data['display_name'];
        } else {
            $data['name'] = $data['display_name'];
        }
        return Arr::only($data, ['name', 'guard_name', 'created_by', 'panel', 'display_name']);
    }

    protected function afterCreate(): void
    {
        $permissionModels = collect();
        $this->permissions->each(function ($permission) use ($permissionModels) {
            $permissionModels->push(Utils::getPermissionModel()::firstOrCreate([
                /** @phpstan-ignore-next-line */
                'name' => $permission,
                'guard_name' => $this->data['guard_name'],
            ]));
        });

        $this->record->syncPermissions($permissionModels);
    }

    protected function getCreatedNotification(): ?Notification
    {
        return Notification::make()
            ->success()
            ->title('Role Created')
            ->body('The role has been created successfully.');
    }
}
