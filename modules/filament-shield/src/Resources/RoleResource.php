<?php

namespace Indianic\FilamentShield\Resources;

use Filament\Forms;
use App\Models\Role;
use Filament\Tables;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Filament\Facades\Filament;
use Illuminate\Validation\Rule;
use Filament\Resources\Resource;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use App\Services\PermissionService;
use Filament\Forms\Components\Card;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Group;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Tables\Filters\SelectFilter;
use Indianic\FilamentShield\Support\Utils;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Tables\Columns\ViewColumn;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Facades\Log;
use Indianic\FilamentShield\Forms\ShieldSelectAllToggle;
use Indianic\FilamentShield\Resources\RoleResource\Pages;
use Indianic\FilamentShield\Contracts\HasShieldPermissions;
use Indianic\FilamentShield\Traits\HasShieldFormComponents;

class RoleResource extends Resource implements HasShieldPermissions
{
    use HasShieldFormComponents;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $model = Role::class;

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function canAccess(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_view')
            || $user->can('roles_create') || $user->can('roles_update') || $user->can('roles_delete')
            || $user->can('roles_change status');
    }

    public static function canCreate(): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_create');
    }

    public static function canEdit($record): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_update');
    }

    public static function canDelete($record): bool
    {
        $user = auth()->user();
        $isPharmaceuticalCompany = isPharmaceuticalCompany();
        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_delete');
    }

    public static function form(Form $form): Form
    {
        $permissionsData = self::getPermissionList();
        $permissionGroups = $permissionsData['permissions'];
        $assignedPermissions = $permissionsData['assigned'];
        $permissionComponents = [];

        foreach ($permissionGroups as $groupName => $options) {
            $groupAssignedPermissions = array_intersect(array_keys($options), $assignedPermissions);

            if (!empty($options)) {
                $permissionComponents[] = Grid::make()
                    ->schema([
                        Section::make("{$groupName} Permissions")
                            ->heading(fn() => $groupName === 'credit-line-orders'
                                ? 'Credit Line Permissions'
                                : Str::headline($groupName) . ' Permissions')
                            ->schema([
                                CheckboxList::make("permissions_{$groupName}")
                                    ->label('')
                                    ->options($options)
                                    ->live()
                                    ->columns(2)
                                    ->bulkToggleable()
                                    ->extraAttributes([
                                        'class' => 'fix-checkbox-label-click',
                                    ])
                                    ->default($groupAssignedPermissions)
                                    ->afterStateUpdated(function (Get $get, Set $set, $state, $old, CheckboxList $component) use ($groupName) {
                                        $dependentPermissions = [
                                            'credit-line-orders_assign credit',
                                            'credit-line-orders_edit assign credit',
                                        ];

                                        $facilityKey = 'permissions_facility';
                                        $facilityPermissions = collect($get($facilityKey) ?? []);

                                        $sessionKey = "permissions_{$groupName}_state_info";
                                        $stateInfo = session()->get($sessionKey, [
                                            'facility_view_previously_checked' => false,
                                            'facility_auto_added' => false,
                                            'all_orders_chat_auto_added' => false,
                                            'previous_state' => []
                                        ]);

                                        $facilityViewPreviouslyChecked = $stateInfo['facility_view_previously_checked'] ?? false;
                                        $facilityWasAutoInserted = $stateInfo['facility_auto_added'] ?? false;
                                        $chatWasAutoInserted = $stateInfo['all_orders_chat_auto_added'] ?? false;

                                        $newState = collect($state ?? []);
                                        $oldState = collect($old ?? []);
                                        $dependentSelected = $newState->intersect($dependentPermissions)->isNotEmpty();

                                        $changed = false;

                                        // Facility view auto-insert logic
                                        if ($dependentSelected) {
                                            if (!$facilityPermissions->contains('facility_view')) {
                                                $facilityPermissions->push('facility_view');
                                                $changed = true;
                                                $stateInfo['facility_auto_added'] = true;
                                            }
                                        } else {
                                            if (
                                                $facilityWasAutoInserted &&
                                                !$facilityViewPreviouslyChecked &&
                                                $facilityPermissions->contains('facility_view')
                                            ) {
                                                $facilityPermissions = $facilityPermissions->reject(fn($p) => $p === 'facility_view');
                                                $changed = true;
                                                $stateInfo['facility_auto_added'] = false;
                                            }
                                        }

                                        if ($changed) {
                                            $set($facilityKey, $facilityPermissions->values()->toArray());
                                        }

                                        // ✅ Sync credit-line-orders_chat ↔ all-orders_chat ONLY IF credit-line-orders_chat was toggled
                                        $chatPermission = 'credit-line-orders_chat';
                                        $chatWasChecked = !$oldState->contains($chatPermission) && $newState->contains($chatPermission);
                                        $chatWasUnchecked = $oldState->contains($chatPermission) && !$newState->contains($chatPermission);

                                        if ($chatWasChecked || $chatWasUnchecked) {
                                            $allOrdersKey = 'permissions_all-orders';
                                            $allOrdersPermissions = collect($get($allOrdersKey) ?: []);
                                            $allOrdersChanged = false;

                                            if ($chatWasChecked) {
                                                if (!$allOrdersPermissions->contains('all-orders_chat')) {
                                                    $allOrdersPermissions->push('all-orders_chat');
                                                    $stateInfo['all_orders_chat_auto_added'] = true;
                                                    $allOrdersChanged = true;
                                                }
                                            } elseif ($chatWasUnchecked) {
                                                if (
                                                    $chatWasAutoInserted &&
                                                    $allOrdersPermissions->contains('all-orders_chat')
                                                ) {
                                                    $allOrdersPermissions = $allOrdersPermissions->reject(fn($p) => $p === 'all-orders_chat');
                                                    $stateInfo['all_orders_chat_auto_added'] = false;
                                                    $allOrdersChanged = true;
                                                }
                                            }

                                            if ($allOrdersChanged) {
                                                $set($allOrdersKey, $allOrdersPermissions->values()->toArray());
                                            }
                                        }

                                        // Save the new state
                                        $stateInfo['previous_state'] = $state;
                                        session()->put($sessionKey, $stateInfo);
                                    })

                                    ->afterStateHydrated(function (CheckboxList $component, $state, Get $get) use ($groupAssignedPermissions, $groupName) {
                                        if (empty($state) || $state === null) {
                                            $component->state(array_values($groupAssignedPermissions));
                                        }

                                        // Initialize state tracking on hydration
                                        $sessionKey = "permissions_{$groupName}_state_info";
                                        $facilityKey = 'permissions_facility';
                                        $facilityPermissions = collect($get($facilityKey) ?? []);

                                        session()->put($sessionKey, [
                                            'facility_view_previously_checked' => $facilityPermissions->contains('facility_view'),
                                            'auto_added' => false,
                                            'previous_state' => $state ?? $groupAssignedPermissions
                                        ]);
                                    })
                                    ->dehydrated(fn() => true)
                            ])

                            ->columnSpan(2)
                            ->collapsible(),
                    ]);
            }
        }

        return $form
            ->schema([
                Forms\Components\Grid::make()
                    ->schema([
                        Forms\Components\Section::make()
                            ->schema([

                                TextInput::make('display_name')
                                    ->formatStateUsing(fn($state, $record) => $state ?? $record->name ?? null)
                                    ->label(new HtmlString('Role <span class="text-red-500">*</span>'))
                                    ->validationAttribute('Role')
                                    ->placeholder('Role')
                                    ->rules(function (Get $get, ?Model $record) {
                                        $userId = getUser(auth()->user())->id;
                                        $generatedName = "{$userId}-{$get('display_name')}";

                                        return [
                                            'required',
                                            'regex:/^[a-zA-Z0-9 ]+$/',
                                            'max:20',
                                            function ($attribute, $value, $fail) use ($record) {
                                                $userId = getUser(auth()->user())->id;
                                                $value = trim($value);
                                                $generatedName = "{$userId}-{$value}";
                                                $generatedName = trim(strtolower(trim($generatedName)));
                                                $query = Role::whereRaw('LOWER(TRIM(name)) = ?', [$generatedName]);
                                                if ($record) {
                                                    $query->where('id', '!=', $record->id);
                                                }

                                                if ($query->exists()) {
                                                    $fail("The role '{$value}' is already taken.");
                                                }
                                            }
                                        ];
                                    })
                                    ->validationMessages([
                                        'required' => 'The Role field is required.',
                                        'regex' => 'Only letters, numbers, and spaces are allowed.',
                                    ])
                                    ->afterStateUpdated(function (Get $get, Set $set) {
                                        $userId = auth()->id();
                                        $set('name', "{$userId}-{$get('display_name')}");
                                    })
                                    ->live(onBlur: true)
                                    // ->columns(3)
                                    ->maxLength(255),
                                TextInput::make('name')
                                    ->label('Name')
                                    ->hidden()
                                    ->unique(ignoreRecord: true)
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('guard_name')
                                    ->label(__('filament-shield::filament-shield.field.guard_name'))
                                    ->readOnly()
                                    ->dehydrated(true)
                                    ->default(Utils::getFilamentAuthGuard())
                                    ->hidden()
                                    ->nullable()
                                    ->maxLength(255),

                                Forms\Components\Select::make(config('permission.column_names.team_foreign_key'))
                                    ->label(__('filament-shield::filament-shield.field.team'))
                                    ->placeholder(__('filament-shield::filament-shield.field.team.placeholder'))
                                    /** @phpstan-ignore-next-line */
                                    ->default([Filament::getTenant()?->id])
                                    ->options(function () {
                                        return Role::pluck('name', 'id');
                                    })
                                    //->options(fn(): Arrayable => Utils::getTenantModel() ? Utils::getTenantModel()::pluck('name', 'id') : collect())
                                    ->hidden(fn(): bool => ! (static::shield()->isCentralApp() && Utils::isTenancyEnabled())),
                                ShieldSelectAllToggle::make('select_all')
                                    ->onIcon('heroicon-s-shield-check')
                                    ->offIcon('heroicon-s-shield-exclamation')
                                    ->label(__('filament-shield::filament-shield.field.select_all.name'))
                                    ->helperText(fn(): HtmlString => new HtmlString(__('filament-shield::filament-shield.field.select_all.message')))
                                    ->dehydrated(fn(bool $state): bool => $state),
                            ])
                            ->columns([
                                'sm' => 2,
                                'lg' => 2,
                            ]),
                    ]),
                Group::make()
                    ->columnSpan(2)
                    ->schema([
                        Section::make('User Permissions')
                            ->description('Modify permissions assigned to this user')
                            ->schema([
                                Grid::make(2)
                                    ->columnSpan(2)
                                    ->schema($permissionComponents)
                            ])
                            ->columnSpan(2)
                            ->visible(fn() => count($permissionComponents) > 0),
                    ])
            ]);
    }

    public static function getPermissionList(): array
    {
        $routeId = request()->route('record');
        return app(PermissionService::class)->getPermissionsForRole($routeId);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->actionsColumnLabel('Actions')
            ->query(function () {

                $user = Filament::auth()->user();

                if ($user->hasRole('Super Admin')) {
                    // Super Admin: See roles created by them, excluding key roles
                    $roles = Role::query()
                        ->where('created_by', $user->id)
                        ->whereNotIn('name', ['Super Admin', 'Pharmaceutical Company', 'Clinic'])
                        ->withCount('users')
                        ->with('permissions');
                } elseif ($user->hasRole('Pharmaceutical Company')) {
                    // Pharmaceutical Company: Custom logic here
                    $roles = Role::query()
                        ->where('created_by', $user->id)
                        ->where('name', '!=', 'Pharmaceutical Company')
                        ->withCount('users')
                        ->with('permissions');
                } elseif ($user->hasRole('Clinic')) {
                    // Clinic: Custom logic here
                    $roles = Role::query()
                        ->where('created_by', $user->id)
                        ->where('name', '!=', 'Clinic')
                        ->withCount('users')
                        ->with('permissions');
                } else {
                    // Default logic for other users
                    $userId = getUser($user)->id;
                    $loggedInRoleId = $user->roles->first()->id;

                    $roles = Role::query()
                        ->where('created_by', $user->id)
                        ->where('id', '!=', $loggedInRoleId)
                        ->withCount('users')
                        ->with('permissions');
                }

                return $roles;
            })
            ->columns([
                Tables\Columns\TextColumn::make(name: 'name')
                    ->sortable()
                    ->label('Name')
                    ->formatStateUsing(function ($state, $record) {
                        return $record->display_name ?? $record->name;
                    })
                    ->weight('font-medium')
                    ->toggleable()
                    ->label('Role')
                    ->searchable(),
                ViewColumn::make('users_count')
                    ->label('No. of Users')
                    ->sortable()
                    ->alignment('center')
                    ->toggleable()
                    ->view('tables.columns.users-count-modal'),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Status')
                    ->toggleable()
                    ->alignment('center')
                    ->sortable()
                    ->disabled(fn($record) => $record->users_count > 0)
                    ->visible(function () {
                        $user = auth()->user();
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_change status');
                    })
                    ->beforeStateUpdated(function ($record, $state) {
                        if ($record->users_count > 0) {
                            Notification::make()
                                ->danger()
                                // ->title(__('role_permission.cannot_change_status_title'))
                                ->body(__('message.role_permission.cannot_change_status_body'))
                                ->send();
                            return false; // Prevent state change
                        }
                        return true;
                    })
                    ->afterStateUpdated(function () {
                        Notification::make()
                            ->success()
                            ->body(__('message.role_permission.status_updated'))
                            ->send();
                    })
                    ->tooltip(fn($record) => $record->users_count > 0
                        ? __('message.role_permission.cannot_change_status_tooltip')
                        : null)
                    ->extraAttributes(function ($record) {
                        return $record->users_count > 0 ? [
                            'class' => 'cursor-not-allowed disabled-opacity',
                        ] : [];
                    })

            ])
            ->filters([
                SelectFilter::make('name')
                    ->multiple()
                    ->searchable()
                    ->options(function () {
                        $query = Role::query();

                        if (Filament::auth()->user()->hasRole('Super Admin')) {
                            $query->whereNotIn('name', ['Super Admin', 'Pharmaceutical Company', 'Clinic']);
                        } else {
                            $userId = getUser(auth()->user())->id;
                            $query->where('created_by', $userId);
                        }

                        return $query->orderBy('name', 'asc')
                            ->get()
                            ->mapWithKeys(function ($role) {
                                // Remove prefix like "2-" or "14-" from the name using regex
                                $cleanName = preg_replace('/^\d+-\s*/', '', $role->name);
                                return [$role->name => $cleanName];
                            })->sort()
                            ->toArray();
                    }),
                SelectFilter::make('is_active')
                    ->label('Status')
                    ->options([
                        true => 'Active',
                        false => 'Inactive',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 border-success rounded-lg text-blue-900', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->tooltip(function (Model $record) {
                        return  "Edit";
                    }),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);',])
                    ->tooltip(function (Model $record) {
                        return  "View";
                    }),
                Tables\Actions\DeleteAction::make()
                    ->visible(
                        function ($record) {
                            return !in_array($record->name, ['Super Admin', 'Pharmaceutical Company', 'Clinic']) && $record->users_count === 0;
                        }
                    )->icon('heroicon-o-trash')->size('sm')->iconButton()
                    ->tooltip(function (Model $record) {
                        return  "Delete";
                    })
                    ->successNotification(
                        Notification::make()
                            ->success()
                            // ->title('Role Deleted')
                            ->title('The role has been deleted successfully.'),
                    )
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);']),
            ])
            ->bulkActions([
                Tables\Actions\DeleteBulkAction::make()
                    ->visible(function () {
                        $user = auth()->user();
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_delete');
                    })

                    ->action(function (Collection $records) {
                        $protectedRoles = ['Super Admin', 'Pharmaceutical Company', 'Clinic'];

                        $rolesWithUsers = [];
                        $undeletableRoles = [];

                        $records = $records->loadMissing('users');

                        $deletableRecords = $records->reject(function ($role) use ($protectedRoles, &$rolesWithUsers, &$undeletableRoles) {
                            if (in_array($role->name, $protectedRoles)) {
                                if ($role->users->count() > 0) {
                                    $rolesWithUsers[] = $role->name;
                                }
                                $undeletableRoles[] = $role->name;
                                return true;
                            }
                            if ($role->users->count() > 0) {
                                $rolesWithUsers[] = $role->name;
                                $undeletableRoles[] = $role->name;
                                return true;
                            }
                            return false;
                        });
                        $deletableRecords->each->delete();

                        $notification = Notification::make()->success();

                        if ($deletableRecords->isNotEmpty()) {
                            $notification->title('Roles Deleted')
                                ->body('The selected roles have been deleted.');
                        }

                        if (!empty($rolesWithUsers)) {
                            $notification->danger()
                                // ->title('Some Roles Not Deleted')
                                ->body('The following roles have users assigned and were not deleted: ' . implode(', ', $rolesWithUsers) . '.');
                        } elseif (!empty($undeletableRoles)) {
                            $notification->danger()
                                // ->title('Protected Roles Not Deleted')
                                ->body('The following protected roles were not deleted: ' . implode(', ', $undeletableRoles) . '.');
                        }

                        $notification->send();
                    }),

                Tables\Actions\BulkAction::make('active')
                    ->color('success')
                    ->visible(function () {
                        $user = auth()->user();
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_change status');
                    })
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $rolesWithUsers = $records->filter(fn($record) => $record->users_count > 0)->pluck('name')->toArray();
                        $processableRecords = $records->filter(fn($record) => $record->users_count === 0);

                        // if (!empty($rolesWithUsers)) {
                        //     Notification::make()
                        //         ->danger()
                        //         // ->title(__('message.role_permission.cannot_change_status_title'))
                        //         ->title(__('message.role_permission.cannot_change_status_body_bulk', ['roles' => implode(', ', $rolesWithUsers)]))
                        //         ->send();
                        // }

                        if ($processableRecords->isNotEmpty()) {
                            $processableRecords->each(function ($record) {
                                $record->update(['is_active' => true]);
                            });
                            Notification::make()
                                ->success()
                                ->title(__('message.role_permission.roles_activated_body'))
                                ->send();
                        }
                    }),
                Tables\Actions\BulkAction::make('inactive')
                    ->color('warning')
                    ->visible(function () {
                        $user = auth()->user();
                        $isPharmaceuticalCompany = isPharmaceuticalCompany();
                        return $user->hasRole('Super Admin') || $isPharmaceuticalCompany || $user->can('roles_change status');
                    })
                    ->requiresConfirmation()
                    ->action(function (Collection $records) {
                        $rolesWithUsers = $records->filter(fn($record) => $record->users_count > 0)
                            ->map(function ($record) {
                                $userNames = $record->users->pluck('name')->toArray();
                                return [
                                    'role' => $record->name,
                                    'users' => $userNames,
                                ];
                            })->toArray();

                        $processableRecords = $records->filter(fn($record) => $record->users_count === 0);

                        if (!empty($rolesWithUsers)) {
                            $message = collect($rolesWithUsers)->map(function ($item) {
                                return __('message.role_permission.cannot_change_status_body_bulk_role', [
                                    'role' => $item['role'],
                                    'users' => implode(', ', $item['users']),
                                ]);
                            })->implode('; ');

                            Notification::make()
                                ->warning()
                                ->body($message)
                                ->send();
                        }

                        if ($processableRecords->isNotEmpty()) {
                            $processableRecords->each(function ($record) {
                                $record->update(['is_active' => false]);
                            });
                            Notification::make()
                                ->success()
                                ->body(__('message.role_permission.roles_inactivated_body'))
                                ->send();
                        }
                    }),

            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRoles::route('/'),
            'create' => Pages\CreateRole::route('/create'),
            'view' => Pages\ViewRole::route('/{record}'),
            'edit' => Pages\EditRole::route('/{record}/edit'),
        ];
    }

    public static function getCluster(): ?string
    {
        return Utils::getResourceCluster() ?? static::$cluster;
    }

    public static function getModelLabel(): string
    {
        return __('filament-shield::filament-shield.resource.label.role');
    }

    public static function getPluralModelLabel(): string
    {
        return __('filament-shield::filament-shield.resource.label.roles');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return Utils::isResourceNavigationRegistered();
    }

    public static function getNavigationGroup(): ?string
    {
        return Utils::isResourceNavigationGroupEnabled()
            ? __('filament-shield::filament-shield.nav.group')
            : '';
    }

    public static function getNavigationLabel(): string
    {
        return __('filament-shield::filament-shield.nav.role.label');
    }

    public static function getNavigationIcon(): string
    {
        return __('filament-shield::filament-shield.nav.role.icon');
    }

    public static function getNavigationSort(): ?int
    {
        return Utils::getResourceNavigationSort();
    }

    public static function getSlug(): string
    {
        return Utils::getResourceSlug();
    }

    public static function getNavigationBadge(): ?string
    {
        if (Auth::user()->hasRole('Super Admin')) {
            return Role::count();
        } elseif (Auth::user()->hasRole('Pharmaceutical Company')) {
            return Role::where('created_by', Auth::id())->count();
        }

        return null;
    }

    public static function isScopedToTenant(): bool
    {
        return Utils::isScopedToTenant();
    }

    public static function canGloballySearch(): bool
    {
        return Utils::isResourceGloballySearchable() && count(static::getGloballySearchableAttributes()) && static::canViewAny();
    }
}
