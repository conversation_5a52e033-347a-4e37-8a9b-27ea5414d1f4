<?php

namespace Indianic\CmsPages\Policies;

use Indianic\CmsPages\Models\CmsPage;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class CmsPagePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {

        return $user->hasRole('Super Admin') || $user->can('static-pages_view');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, CmsPage $cmsPage): bool
    {
        return $user->hasRole('Super Admin') || $user->can('static-pages_view');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasRole('Super Admin') || $user->can('static-pages_create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, CmsPage $cmsPage): bool
    {
        return $user->hasRole('Super Admin') || $user->can('static-pages_update');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, CmsPage $cmsPage): bool
    {
        return $user->hasRole('Super Admin') || $user->can('static-pages_delete');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, CmsPage $cmsPage): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, CmsPage $cmsPage): bool
    {
        return false;
    }
}
