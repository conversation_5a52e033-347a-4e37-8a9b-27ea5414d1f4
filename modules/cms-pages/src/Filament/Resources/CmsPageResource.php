<?php

namespace Indianic\CmsPages\Filament\Resources;

use AmidEsfahani\FilamentTinyEditor\TinyEditor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Indianic\CmsPages\Filament\Resources\CmsPageResource\Pages;
use Indianic\CmsPages\Models\CmsPage;

class CmsPageResource extends Resource
{
    protected static ?string $model = CmsPage::class;

    protected static ?string $navigationIcon = 'heroicon-o-code-bracket-square';

    protected static ?string $navigationLabel = 'Static Pages';

    // protected static ?string $navigationGroup = 'General';

    public static function form(Form $form): Form
    {
        $id = $form->getRecord()?->getKey();
        $context = request()->route()->getName();
        return $form
            ->schema([

                Forms\Components\Section::make()
                    ->schema([

                        // Forms\Components\Select::make('parent_id')
                        //     ->options(function () use ($id): array {
                        //         $parentPages = CmsPage::whereNull('parent_id')
                        //             ->when($id, function ($q) use ($id) {
                        //                 $q->where('id', '!=', $id);
                        //             })
                        //             ->orderBy('title', 'ASC')
                        //             ->pluck('title', 'id')
                        //             ->toArray();

                        //         return [0 => 'Root'] + $parentPages;
                        //     })
                        //     ->placeholder('Please select Parent')
                        //     ->selectablePlaceholder(false),

                        Forms\Components\TextInput::make('title')
                            ->label(new HtmlString("Title <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->maxLength(255)
                            ->rules(['required'])
                            ->unique(CmsPage::class, 'title', ignoreRecord: true)
                            ->validationMessages([
                                'required' => "The title field is required.",
                                'unique' => "The title has already been taken."
                            ])
                            ->live(onBlur: false)
                            ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                if ($operation == 'edit') {
                                    return;
                                }

                                return $set('slug', Str::slug($state));
                            }),
                        Forms\Components\TextInput::make('slug')
                            ->label(new HtmlString("Slug <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                            ->disabled()
                            ->dehydrated()
                            ->rules(['required'])
                            ->unique(CmsPage::class, 'slug', ignoreRecord: true)
                            ->validationMessages([
                                'required' => "The slug field is required.",
                                'unique' => "The slug has already been taken."
                            ]),

                        Forms\Components\TextInput::make('updated_at')
                            ->label('Last Updated on')
                            ->disabled()
                            ->visible(fn() => in_array(request()->route()->getName(), [
                                'filament.admin.resources.cms-pages.view',
                            ]))
                            ->formatStateUsing(function ($state) {
                                return date('M j, Y | h:i A', strtotime($state));
                            }),

                        Forms\Components\TextInput::make('sub_title')
                            ->placeholder('Please add Sub Title')
                            ->label('Sub Title')
                            ->maxLength(255),
                        // TinyEditor::make('body')
                        //     ->label(new HtmlString("Content <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        //     ->rules(['required'])
                        //     ->fileAttachmentsDisk('s3')
                        //     ->fileAttachmentsDirectory('cms-pages')
                        //     ->fileAttachmentsVisibility('private')
                        //     ->getUploadedAttachmentUrlUsing(fn(string $file)
                        //     => config()
                        //         ->string('filesystems.disks.s3.url') . '/dpharma/' . Str::trim($file))
                        //     ->validationMessages([
                        //         'required' => "The content field is required.",
                        //     ])
                        //     ->columnSpanFull(),
                        // Forms\Components\RichEditor::make('body')
                        //     ->fileAttachmentsDisk('s3')
                        //     ->fileAttachmentsDirectory('cms-pages')
                        //     ->label(new HtmlString("Content <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        //     ->rules(['required'])
                        //     ->validationMessages([
                        //         'required' => "The content field is required.",
                        //     ])
                        //     ->fileAttachmentsVisibility('private')
                        //     ->getUploadedAttachmentUrlUsing(fn(string $file)
                        //     => config()
                        //         ->string('filesystems.disks.s3.url') . '/dpharma/' . Str::trim($file))
                        //     ->columnSpanFull(),

                        // Forms\Components\TextInput::make('meta_keywords')
                        //     ->label('Meta Keywords')
                        //     ->placeholder('Please add Meta Keywords'),

                        // Forms\Components\Toggle::make('status')
                        //     ->default(true),

                        // Forms\Components\Textarea::make('meta_description')
                        //     ->placeholder('Please add Meta Description')
                        //     ->label('Meta Description')
                        //     ->columnSpanFull(),

                    ])
                    ->columns($context === 'filament.admin.resources.cms-pages.view' ? 3 : 2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->label('Page Name')
                    //->searchable(isIndividual: true)
                    ->sortable()
                    ->searchable()
                    ->toggleable(),
                // ->weight('medium')
                // ->alignLeft(),

                // Tables\Columns\ToggleColumn::make('status')->onColor('success')
                //     ->onColor('primary')
                //     ->offColor('gray')
                //     ->toggleable()
                //     ->afterStateUpdated(function () {
                //         Notification::make()
                //             ->success()
                //             ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                //             ->body('Status has been updated successfully.')
                //             ->send();
                //     }),
                Tables\Columns\TextColumn::make('updated_at')  // Assuming you have an 'updated_at' field in your database
                    ->label('Last Updated')
                    ->dateTime('M j, Y | h:i A')
                    ->toggleable()
                    ->searchable(),

            ])
            ->filters([
                //
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);'])
                    ->visible(function (CmsPage $record) {
                        $user = auth()->user();
                        if ($user->hasRole('super-admin') || $user->can('static-pages_update')) {
                            return true;
                        }
                        return false;
                    })
                    ->tooltip(function (CmsPage $record) {
                        return "Edit";
                    }),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);'])
                    ->visible(function (CmsPage $record) {
                        $user = auth()->user();
                        if ($user->hasRole('super-admin') || $user->can('static-pages_view')) {
                            return true;
                        }
                        return false;
                    })
                    ->tooltip(function (CmsPage $record) {
                        return "View";
                    }),
                // Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                //     ->successNotification(
                //         Notification::make()
                //             ->success()
                //             ->title('Cms Page Deleted')
                //             ->body('The cms page has been deleted successfully.'),
                //     ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([]),
                // Tables\Actions\DeleteBulkAction::make(),
                // Tables\Actions\BulkAction::make('Active')
                //     ->action(function (Collection $records, array $data): void {
                //         foreach ($records as $record) {
                //             $record['status'] = 'Active';
                //             $record->save();
                //         }
                //     })
                //     ->color('success')
                //     ->requiresConfirmation()
                //     ->modalHeading('Update status')
                //     ->modalSubheading('Are you sure you\'d like to Active these Cms Pages?')
                //     ->modalButton('Yes, update them'),
                // Tables\Actions\BulkAction::make('Inactive')
                //     ->action(function (Collection $records, array $data): void {
                //         foreach ($records as $record) {
                //             $record['status'] = 'Inactive';
                //             $record->save();
                //         }
                //     })
                //     ->requiresConfirmation()
                //     ->modalHeading('Update status')
                //     ->modalSubheading('Are you sure you\'d like to Inactive these Cms Pages?')
                //     ->modalButton('Yes, update them'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCmsPages::route('/'),
            'create' => Pages\CreateCmsPage::route('/create'),
            'view' => Pages\ViewCmsPage::route('/{record}'),
            'edit' => Pages\EditCmsPage::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['title', 'slug'];
    }
}
