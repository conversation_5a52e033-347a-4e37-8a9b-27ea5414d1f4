<?php

namespace Indianic\CmsPages\Filament\Resources\CmsPageResource\Pages;

use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Indianic\CmsPages\Filament\Resources\CmsPageResource;

class ViewCmsPage extends ViewRecord
{
    protected static string $resource = CmsPageResource::class;

    public function getTitle(): string
    {
        return $this->record->title ?? 'Static Page Details';
    }

    public function getBreadcrumbs(): array
    {
        return [
            $this->getResource()::getUrl('index') => "Static Pages",
            2 => "Static Page Details",
        ];
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('edit')
                ->label('Edit')
                ->url(fn() => $this->getResource()::getUrl('edit', ['record' => $this->record->getKey()]))
                ->button()
                ->visible(function () {


                    return auth()->user()->hasRole('Super Admin') || auth()->user()->can('static-pages_update');
                })
                ->color('primary')->icon('heroicon-m-pencil'),

            Action::make('back')
                ->label('Back')
                ->visible(fn() => auth()->user()->hasRole('Super Admin') || auth()->user()->can('static-pages_view'))
                ->color('gray')
                ->url(CmsPageResource::getUrl('index')),
        ];
    }
}
