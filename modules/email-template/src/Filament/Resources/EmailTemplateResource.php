<?php

namespace Indianic\EmailTemplate\Filament\Resources;

use App\Models\User;
use Carbon\Carbon;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\BulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Indianic\EmailTemplate\Filament\Resources\EmailTemplateResource\Pages;
use Indianic\EmailTemplate\Models\EmailTemplate;

class EmailTemplateResource extends Resource
{
    protected static ?string $model = EmailTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    // protected static ?string $navigationLabel = 'Static Pages';

    // protected static ?string $navigationGroup = 'General';


    public static function canView(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('email-templates_view');
    }

    public static function canCreate(): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('email-templates_create');
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('email-templates_update');
    }
    public static function canDelete(Model $record): bool
    {
        return auth()->user()->hasRole('Super Admin') || auth()->user()->can('email-templates_delete');
    }
    public static function form(Form $form): Form
    {
        $context = request()->route()->getName();
        return $form
            ->schema([
                Section::make()->schema([
                    TextInput::make('key')
                        ->label(new HtmlString("Email Key <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->placeholder("Enter email key")
                        ->disabled()
                        ->rules(['required'])
                        ->validationMessages([
                            'required' => "The email key field is required.",
                        ]),
                    TextInput::make('title')
                        ->label(new HtmlString("Template Name <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->placeholder("Enter template name")
                        ->live(onBlur: false)
                        ->afterStateUpdated(function (Get $get, Set $set) {
                            $set('key', str_replace(' ', '_', strtoupper($get('title'))));
                        })
                        ->rules([
                            'required',
                            // 'regex:/^[a-zA-Z0-9\s]{1,20}$/'
                            'max:100',
                        ])
                        ->unique(EmailTemplate::class, 'title', ignoreRecord: true)
                        ->validationMessages([
                            'required' => "The template name field is required.",
                            // 'regex' => "The template name should be alphanumeric, special character and spaces are allowed. Maximum 20 character.",
                            'max' => 'The template name must not exceed 100 characters.',
                            'unique' => "The template name has already been taken."
                        ]),
                    TextInput::make('date')
                        ->label('Last Updated on')
                        ->disabled()
                        ->visible(fn() => in_array(request()->route()->getName(), [
                            'filament.admin.resources.email-templates.view', // Show only on the view page
                        ]))
                        ->formatStateUsing(function ($state) {
                            return date('M j, Y | h:i A', strtotime($state));
                        }),
                    TextInput::make('subject')
                        ->label(new HtmlString("Subject <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->placeholder("Enter subject")
                        ->rules([
                            'required',
                            'max:100',
                            // 'regex:/^[a-zA-Z0-9\s]{1,20}$/'
                        ])
                        ->validationMessages([
                            'required' => "The subject field is required.",
                            'max' => 'The subject must not exceed 100 characters.',
                            // 'regex' => "The subject should be alphanumeric, special character and spaces are allowed. Maximum 20 character.",
                        ])
                        ->columnSpan($context === 'filament.admin.resources.email-templates.view' ? 1 : 2),
                    // Checkbox::make('status'),
                    Checkbox::make('is_email')
                        ->default(true)
                        ->hidden()
                        ->live()
                        ->required(),
                    // Checkbox::make('is_sms')->live()->required(),
                    RichEditor::make('email_content')
                        ->label(new HtmlString("Email Content <span class='font-medium text-danger-600 dark:text-danger-400'>*</span>"))
                        ->placeholder("Enter body text")
                        ->visible(function (Get $get) {
                            if ($get('is_email')) {
                                return true;
                            }
                            return false;
                        })
                        ->rules(['required'])
                        ->validationMessages([
                            'required' => "The email content field is required.",
                        ])
                        ->columnSpanFull(),
                    // RichEditor::make('sms_content')->visible(function (Get $get) {
                    //     if ($get('is_sms')) {
                    //         return true;
                    //     }
                    //     return false;
                    // })->required(),
                ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('updatedBy.name')
                    ->label("Modified By")
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('updated_at')  // Assuming you have an 'updated_at' field in your database
                    ->label('Last Updated')
                    ->dateTime('M j, Y | h:i A')  // Format: Feb 8, 2024 | 02:27 PM
                    ->toggleable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email_to')
                    ->label('Email To')
                    ->toggleable()
                    ->searchable()
                    ->sortable(),

                // TextColumn::make('subject')
                //     ->searchable()
                //     ->sortable()
                //     ->toggleable(),
                // ToggleColumn::make('status')
                //     ->searchable()
                //     ->toggleable()
                //     ->afterStateUpdated(function () {
                //         Notification::make()
                //             ->success()
                //             ->title(__('filament-panels::resources/pages/edit-record.notifications.saved.title'))
                //             ->body('Status has been updated successfully.')
                //             ->send();
                //     })->extraAttributes([
                //         'wire:loading.class' => 'opacity-50 cursor-wait',
                //     ]),
                // TextColumn::make('key')->searchable()->sortable()->copyable()->tooltip('Click To Copy')->toggleable(),

            ])
            ->filters([
                // Tables\Filters\SelectFilter::make('status')
                //     ->label('Status')
                //     ->options([
                //         true => 'Active',
                //         false => 'Inactive',
                //     ]),
                SelectFilter::make('updated_by')
                    ->label('Modified By')
                    ->searchable()
                    ->options(function () {
                        return User::whereIn('id', EmailTemplate::pluck('updated_by')->unique()->filter())->pluck('name', 'id')->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['value']) {
                            $query->where('updated_by', $data['value']);
                        }
                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['value']) {
                            return null;
                        }
                        $user = User::find($data['value']);
                        return 'Modified By: ' . ($user ? $user->name : 'Unknown');
                    }),

                Filter::make('updated_at')
                    ->label('Date')
                    ->form([
                        DatePicker::make('date')->label('Select Date')->format('Y-m-d'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if ($data['date']) {
                            $query->whereDate('updated_at', $data['date']);
                        }
                        return $query;
                    })->indicateUsing(function (array $data): ?string {
                        if (! $data['date']) {
                            return null;
                        }
                        return 'Date: ' . Carbon::parse($data['date'])->toFormattedDateString();
                    }),
            ])
            ->actionsColumnLabel('Actions')
            ->actions([
                Tables\Actions\EditAction::make()->icon('heroicon-o-pencil-square')->size('sm')->iconButton()->tooltip('Edit')
                    ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(0, 70, 104);']),
                Tables\Actions\ViewAction::make()->icon('heroicon-o-eye')->size('sm')->iconButton()->tooltip('View')
                    ->extraAttributes(['class' => 'border-2 rounded-lg text-gray-400', 'style' => 'margin-left: inherit; border-color:rgb(213, 213, 214);']),

                // Tables\Actions\DeleteAction::make()->icon('heroicon-o-trash')->size('sm')->iconButton()
                //     ->extraAttributes(['class' => 'border-2 rounded-lg', 'style' => 'margin-left: inherit; border-color:rgb(239, 68, 68);'])
                //     // ->visible(fn() => env('DEVELOPER_ACCESS', false))
                //     ->successNotification(
                //         Notification::make()
                //             ->success()
                //             ->title('Template Deleted')
                //             ->body('The template has been deleted successfully.'),
                //     ),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
                // Tables\Actions\DeleteBulkAction::make()
                //     ->visible(fn() => env('DEVELOPER_ACCESS', false))
                //     ->deselectRecordsAfterCompletion()
                //     ->before(function () {
                //         fn($record) => ($record?->id) != 1;
                //     }),
                // Tables\Actions\BulkAction::make('Active')
                //     ->action(function (Collection $records, array $data): void {
                //         foreach ($records as $record) {
                //             $record['status'] = 1;
                //             $record->save();
                //         }
                //     })
                //     ->color('success')
                //     ->requiresConfirmation()
                //     ->modalHeading('Update status')
                //     ->modalSubheading('Are you sure you\'d like to Active these Email Templates?')
                //     ->modalButton('Yes, update them'),
                // Tables\Actions\BulkAction::make('Inactive')
                //     ->action(function (Collection $records, array $data): void {
                //         foreach ($records as $record) {
                //             $record['status'] = '0';
                //             $record->save();
                //         }
                //     })
                //     ->requiresConfirmation()
                //     ->modalHeading('Update status')
                //     ->modalSubheading('Are you sure you\'d like to Inactive these Email Templates?')
                //     ->modalButton('Yes, update them'),

                // BulkAction::make('status')
                // ->form([
                //     Toggle::make('status')
                // ])
                // ->action(function (Collection $records){
                //     $records->each(function ($record) {
                //         if(!$record->status)
                //         {
                //             $record->status = 1;
                //         }
                //         else
                //         {
                //             $record->status = 0;
                //         }
                //         $record->save();
                //     });
                // })

            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailTemplates::route('/'),
            'create' => Pages\CreateEmailTemplate::route('/create'),
            'view' => Pages\ViewEmailTemplate::route('/{record}'),
            'edit' => Pages\EditEmailTemplate::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['title', 'subject'];
    }
}
