[unix_http_server]
file=/dev/shm/supervisor.sock
chmod=0700

[supervisord]
user=root
nodaemon=true
logfile=/dev/null
logfile_maxbytes=0
pidfile=/run/supervisord.pid

[rpcinterface:supervisor]
supervisor.rpcinterface_factory=supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///dev/shm/supervisor.sock

[program:php]
command=php-fpm -F
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=3

[program:nginx]
command=nginx -g 'daemon off;'
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
autorestart=true
startretries=3

[program:optimize]
command=php artisan optimize

directory=/var/www
user=www-data
environment=HOME="/var/www",USER="www-data"
autostart=true
startsecs=0
autorestart=false
stdout_logfile=/var/log/supervisor/optimize.log
stderr_logfile=/var/log/supervisor/optimize_err.log

[program:migrate]

directory=/var/www
user=www-data
environment=HOME="/var/www",USER="www-data"
autostart=true
startsecs=0
autorestart=false
command=php artisan migrate
stdout_logfile=/var/log/supervisor/migrate.log
stderr_logfile=/var/log/supervisor/migrate_err.log
