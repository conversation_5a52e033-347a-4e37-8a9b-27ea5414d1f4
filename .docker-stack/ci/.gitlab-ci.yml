stages:
  - sonar_stage
  - build_base_image
  - build_admin_dev
  - deploy_admin_dev
  - build_pc_dev
  - deploy_pc_dev
  - build_admin_qa
  - deploy_admin_qa
  - build_pc_qa
  - deploy_pc_qa
  - build_admin_uat
  - deploy_admin_uat
  - build_pc_uat
  - deploy_pc_uat
  #- build_admin_prod
  #- deploy_admin_prod
  #- build_pc_prod
  #- deploy_pc_prod

############################################################################################################

sonar_stage:
  stage: sonar_stage
  tags:                                                                             
    - docker-exec 
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner -X -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.login=$SONAR_LOGIN -Dsonar.password=$SONAR_PASSWORD -Dsonar.projectKey=dpharma-admin
  allow_failure: true
  only:
    - dev

#############################################################################################################

build_base_image:
  stage: build_base_image
  tags:
    - docker-exec
  script:
    - echo "$BASE_ENV" >> .env
    - docker build -t harbor.indianic.com/dpharma/dpharma-admin-dev-base:latest -f .docker-stack/docker-files/base.dockerfile .
    - docker push harbor.indianic.com/dpharma/dpharma-admin-dev-base:latest
  when: manual
  only:
    - dev

###############################################################################################################

build_admin_dev:
  stage: build_admin_dev
  tags:
    - docker-exec
  script:
    - echo "$DEV_ADMIN_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$DEV_ADMIN_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$DEV_ADMIN_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$DEV_ADMIN_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$DEV_ADMIN_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t harbor.indianic.com/dpharma/dpharma-admin-dev:latest -f .docker-stack/docker-files/Dockerfile .
    - docker push harbor.indianic.com/dpharma/dpharma-admin-dev:latest
    - docker rmi  harbor.indianic.com/dpharma/dpharma-admin-dev:latest
  only:
    - dev

deploy_admin_dev:
  stage: deploy_admin_dev
  tags:
    - kube-prod-exec
  script:
    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$DEV_ADMIN_DEPLOYMENT" > .docker-stack/deployment-files/admin-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-admin-dev -n dpharma

    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$DEV_ADMIN_SCHEDULER_CRON_DEPLOYMENT" > .docker-stack/deployment-files/admin-scheduler-cron-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-scheduler-cron-deployment.yaml -n dpharma
    
    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$DEV_ADMIN_IMPORT_SCHEDULER_CRON_DEPLOYMENT" > .docker-stack/deployment-files/admin-import-scheduler-cron-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-import-scheduler-cron-deployment.yaml -n dpharma

    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$DEV_ADMIN_SUPERVISOR_DEPLOYMENT" > .docker-stack/deployment-files/admin-supervisor-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-supervisor-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-admin-worker-dev -n dpharma

  only:
    - dev

###############################################################################################################

build_pc_dev:
  stage: build_pc_dev
  tags:
    - docker-exec
  script:
    - echo "$DEV_PC_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$DEV_PC_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$DEV_PC_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$DEV_PC_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$DEV_PC_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t harbor.indianic.com/dpharma/dpharma-pc-dev:latest -f .docker-stack/docker-files/Dockerfile .
    - docker push harbor.indianic.com/dpharma/dpharma-pc-dev:latest
    - docker rmi  harbor.indianic.com/dpharma/dpharma-pc-dev:latest
  only:
    - dev

deploy_pc_dev:
  stage: deploy_pc_dev
  tags:
    - kube-prod-exec
  script:
    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$DEV_PC_DEPLOYMENT" > .docker-stack/deployment-files/pc-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/pc-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-pc-dev -n dpharma

    ##- echo -e "$DEV_PC_SCHEDULER_CRON_DEPLOYMENT" > .docker-stack/deployment-files/pc-scheduler-cron-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/pc-scheduler-cron-deployment.yaml -n dpharma

    ##- echo -e "$DEV_PC_SUPERVISOR_DEPLOYMENT" > .docker-stack/deployment-files/pc-supervisor-deployment.yaml
    ##- kubectl apply -f .docker-stack/deployment-files/pc-supervisor-deployment.yaml -n dpharma
    #- kubectl rollout restart deployment dpharma-supervisor-pc-dev -n dpharma
  only:
    - dev

###############################################################################################################

build_admin_qa:
  stage: build_admin_qa
  tags:
    - docker-exec
  script:
    - echo "$QA_ADMIN_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$QA_ADMIN_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$QA_ADMIN_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$QA_ADMIN_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$QA_ADMIN_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t harbor.indianic.com/dpharma/dpharma-admin-qa:latest -f .docker-stack/docker-files/Dockerfile .
    - docker push harbor.indianic.com/dpharma/dpharma-admin-qa:latest
    - docker rmi  harbor.indianic.com/dpharma/dpharma-admin-qa:latest
  only:
    - qa

deploy_admin_qa:
  stage: deploy_admin_qa
  tags:
    - kube-prod-exec
  script:
    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$QA_ADMIN_DEPLOYMENT" > .docker-stack/deployment-files/admin-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-admin-qa -n dpharma

    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$QA_ADMIN_SCHEDULER_CRON_DEPLOYMENT" > .docker-stack/deployment-files/admin-scheduler-cron-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-scheduler-cron-deployment.yaml -n dpharma

    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$QA_ADMIN_SUPERVISOR_DEPLOYMENT" > .docker-stack/deployment-files/admin-supervisor-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/admin-supervisor-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-admin-worker-qa -n dpharma

  only:
    - qa

################################################################################################################

build_pc_qa:
  stage: build_pc_qa
  tags:
    - docker-exec
  script:
    - echo "$QA_PC_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$QA_PC_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$QA_PC_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$QA_PC_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$QA_PC_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t harbor.indianic.com/dpharma/dpharma-pc-qa:latest -f .docker-stack/docker-files/Dockerfile .
    - docker push harbor.indianic.com/dpharma/dpharma-pc-qa:latest
    - docker rmi  harbor.indianic.com/dpharma/dpharma-pc-qa:latest
  only:
    - qa

deploy_pc_qa:
  stage: deploy_pc_qa
  tags:
    - kube-prod-exec
  script:
    #- mkdir -p .docker-stack/deployment-files
    #- echo -e "$QA_PC_DEPLOYMENT" > .docker-stack/deployment-files/pc-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/pc-deployment.yaml -n dpharma
    - kubectl rollout restart deployment dpharma-pc-qa -n dpharma

    ##- echo -e "$QA_PC_SCHEDULER_CRON_DEPLOYMENT" > .docker-stack/deployment-files/pc-scheduler-cron-deployment.yaml
    #- kubectl apply -f .docker-stack/deployment-files/pc-scheduler-cron-deployment.yaml -n dpharma

    ##- echo -e "$QA_PC_SUPERVISOR_DEPLOYMENT" > .docker-stack/deployment-files/pc-supervisor-deployment.yaml
    ##- kubectl apply -f .docker-stack/deployment-files/pc-supervisor-deployment.yaml -n dpharma
    #- kubectl rollout restart deployment dpharma-supervisor-pc-qa -n dpharma
  only:
    - qa

################################################################

build_admin_uat:
  stage: build_admin_uat
  tags:
    - admin-uat
  script:
    - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com > /dev/null 2>&1  
    - echo "$HARBOR_PASSWORD" | docker login harbor.indianic.com -u "$HARBOR_USERNAME" --password-stdin
    - echo "$UAT_ADMIN_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$UAT_ADMIN_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$UAT_ADMIN_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$UAT_ADMIN_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$UAT_ADMIN_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-admin:v1 -f .docker-stack/docker-files/Dockerfile .
    - docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-admin:v1
    - docker rmi  ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-admin:v1
  only:
    - uat

deploy_admin_uat:
  stage: deploy_admin_uat
  tags:
    - admin-uat
  script:
    - aws elasticbeanstalk update-environment --application-name dpharma --environment-name dpharma-admin --version-label admin-v1 --output json    
  only:
    - uat

################################################################

build_pc_uat:
  stage: build_pc_uat
  tags:
    - admin-uat
  script:
    - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ************.dkr.ecr.ap-southeast-1.amazonaws.com > /dev/null 2>&1
    - echo "$HARBOR_PASSWORD" | docker login harbor.indianic.com -u "$HARBOR_USERNAME" --password-stdin
    - echo "$UAT_PC_ENV" >> .env
    - mkdir -p .docker-stack/config-files
    - echo -e "$UAT_PC_UPLOADS_INI" > .docker-stack/config-files/uploads.ini
    - echo -e "$UAT_PC_OPCACHE_INI" > .docker-stack/config-files/opcache.ini
    - echo -e "$UAT_PC_WWW_CONF" > .docker-stack/config-files/www.conf
    - echo -e "$UAT_PC_NGINX_CONF" > .docker-stack/config-files/nginx.conf
    - docker build -t ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-pc:v1 -f .docker-stack/docker-files/Dockerfile .
    - docker push ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-pc:v1
    - docker rmi  ************.dkr.ecr.ap-southeast-1.amazonaws.com/dpharma-uat-pc:v1    
  only:
    - uat

deploy_pc_uat:
  stage: deploy_pc_uat
  tags:
    - admin-uat
  script:
    - aws elasticbeanstalk update-environment --application-name dpharma --environment-name dpharma-pc --version-label pc-v1 --output json    
  only:
    - uat    
