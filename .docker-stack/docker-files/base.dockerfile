FROM php:8.4-fpm

# setup user as root
USER root

# Work Directory
WORKDIR /var/www

# Install dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential wget openssl htop telnet mariadb-client ffmpeg net-tools \
        nginx nano cron imagemagick supervisor libfreetype6-dev libjpeg-dev \
        libpng-dev libwebp-dev zlib1g-dev libzip-dev gcc g++ make vim unzip curl git \
        jpegoptim optipng pngquant gifsicle locales libonig-dev libgmp-dev libpq-dev \
        libicu-dev libmagickwand-dev \
    && docker-php-ext-configure gd --with-jpeg \
    && docker-php-ext-install gd gmp pdo_mysql mbstring pgsql pdo_pgsql pdo opcache exif sockets pcntl bcmath zip intl \
    && pecl install imagick redis \
    && docker-php-ext-enable imagick redis \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/pear/

# Install Node.js and npm, then clean up
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - && \
    apt-get install -y nodejs && \
    node -v && npm -v && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Puppeteer globally
RUN npm install -g puppeteer npx --force

# Install Chromium using Puppeteer's browser installer
RUN npx puppeteer browsers install chrome

# Add Google's signing key and repo using secure method
RUN wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /usr/share/keyrings/google-linux-signing-keyring.gpg && \
    echo "deb [arch=amd64 signed-by=/usr/share/keyrings/google-linux-signing-keyring.gpg] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list && \
    apt-get update && \
    apt-get install -y google-chrome-stable && \
    rm -rf /var/lib/apt/lists/*

# Copy Nginx and PHP Supervisor Configuration Files
COPY .docker-stack/services/supervisord.conf /etc/supervisor/supervisord.conf

# Copy Source Code and setup permissions
COPY --chown=www-data . /var/www
RUN mkdir -p storage/framework/sessions storage/framework/views storage/framework/cache/data storage/api-docs/ storage/logs storage/quiz && \
    chown -R www-data:www-data storage
    
# # setup composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
RUN composer install --working-dir="/var/www"
RUN composer dump-autoload --working-dir="/var/www"

# Run Laravel artisan commands
RUN php artisan key:generate --ansi && \
    php artisan storage:link --ansi && \
    php artisan vendor:publish --force --tag=livewire:assets --ansi && \
    php artisan migrate --force --ansi
    #php artisan optimize --ansi

RUN chown -R www-data:www-data /var/www/storage && echo "Storage folder permissions changed successfully"

# Install Node dependencies and build frontend
RUN npm install && echo "Node packages installed successfully"

############################################################################