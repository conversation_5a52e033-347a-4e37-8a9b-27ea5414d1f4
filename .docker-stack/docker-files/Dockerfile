FROM harbor.indianic.com/dpharma/dpharma-admin-dev-base:latest

# Copy Source Code and setup permission
COPY --chown=www-data . /var/www

RUN rm /usr/local/etc/php-fpm.d/zz-docker.conf
RUN mkdir -p /run/php && chown www-data:www-data /run/php

#Copy Nginx and PHP Configration File
COPY .docker-stack/config-files/uploads.ini /usr/local/etc/php/conf.d/uploads.ini
COPY .docker-stack/config-files/opcache.ini /usr/local/etc/php/conf.d/docker-php-ext-opcache.ini
COPY .docker-stack/config-files/www.conf /usr/local/etc/php-fpm.d/www.conf
COPY .docker-stack/config-files/nginx.conf /etc/nginx/nginx.conf

# NPM Build
RUN npm run build

# Expose Nginx port
EXPOSE 80

# Start Nginx and PHP-FPM from supervisor
<PERSON><PERSON> ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]